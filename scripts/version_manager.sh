#!/bin/bash
# ========================================================================
# 版本管理脚本 - Linux/macOS
# ========================================================================
# 用于读取、更新和管理项目版本号的统一工具

set -e  # Exit on error

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
VERSION_FILE="$PROJECT_ROOT/VERSION"
CONFIG_FILE="$PROJECT_ROOT/version.config.yaml"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Version Manager - ItForce WAN

Usage: $0 <command>

Commands:
    update-all    Update all component versions
    help          Show this help

EOF
}

# 检查版本文件是否存在
check_version_file() {
    if [ ! -f "$VERSION_FILE" ]; then
        log_error "版本文件不存在: $VERSION_FILE"
        log_info "请先创建版本文件或运行初始化命令"
        exit 1
    fi
}

# 读取当前版本
get_version() {
    check_version_file
    cat "$VERSION_FILE" | tr -d '\n\r'
}

# 验证版本格式
validate_version() {
    local version="$1"
    if [[ ! "$version" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        log_error "无效的版本格式: $version"
        log_info "版本格式应为: MAJOR.MINOR.PATCH (例如: 1.0.0)"
        return 1
    fi
    return 0
}

# 设置版本
set_version() {
    local new_version="$1"
    
    if [ -z "$new_version" ]; then
        log_error "请提供版本号"
        exit 1
    fi
    
    if ! validate_version "$new_version"; then
        exit 1
    fi
    
    local current_version=$(get_version 2>/dev/null || echo "unknown")
    
    if [ "$DRY_RUN" = "true" ]; then
        log_info "DRY RUN: 将版本从 $current_version 更新为 $new_version"
        return 0
    fi
    
    echo "$new_version" > "$VERSION_FILE"
    log_success "版本已更新: $current_version -> $new_version"
}

# 递增版本号
bump_version() {
    local bump_type="$1"
    local current_version=$(get_version)
    
    # 解析当前版本
    IFS='.' read -r major minor patch <<< "$current_version"
    
    case "$bump_type" in
        "major")
            major=$((major + 1))
            minor=0
            patch=0
            ;;
        "minor")
            minor=$((minor + 1))
            patch=0
            ;;
        "patch")
            patch=$((patch + 1))
            ;;
        *)
            log_error "无效的递增类型: $bump_type"
            log_info "支持的类型: major, minor, patch"
            exit 1
            ;;
    esac
    
    local new_version="$major.$minor.$patch"
    set_version "$new_version"
}

# 生成构建版本号
generate_build_version() {
    local version=$(get_version)
    local timestamp=$(date +%Y%m%d%H%M%S)
    local git_commit=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    
    local build_version="$version-$git_commit-$timestamp"
    echo "$build_version"
}

# 更新 Go 后端版本
update_go_version() {
    local version="$1"
    local main_file="$PROJECT_ROOT/cmd/vpn-service/main.go"
    
    if [ ! -f "$main_file" ]; then
        log_warning "Go 主文件不存在: $main_file"
        return 1
    fi
    
    if [ "$DRY_RUN" = "true" ]; then
        log_info "DRY RUN: 将更新 Go 版本变量为 $version"
    else
        # 使用 sed 更新版本变量
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s/version[[:space:]]*=[[:space:]]*\"[^\"]*\"/version = \"$version\"/" "$main_file"
        else
            # Linux
            sed -i "s/version[[:space:]]*=[[:space:]]*\"[^\"]*\"/version = \"$version\"/" "$main_file"
        fi

        log_success "已更新 Go 后端版本: $version"
    fi
}

# 更新 Flutter 版本
update_flutter_version() {
    local version="$1"
    local pubspec_file="$PROJECT_ROOT/ui/flutter/pubspec.yaml"
    local constants_file="$PROJECT_ROOT/ui/flutter/lib/utils/constants.dart"

    if [ ! -f "$pubspec_file" ]; then
        log_warning "Flutter pubspec.yaml 不存在: $pubspec_file"
        return 1
    fi

    if [ "$DRY_RUN" = "true" ]; then
        log_info "DRY RUN: 将更新 Flutter 版本为 $version"
    else
        # 更新 pubspec.yaml 中的版本
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s/^version:[[:space:]]*[^[:space:]]*/version: $version+1/" "$pubspec_file"
            sed -i '' "s/msix_version:[[:space:]]*[^[:space:]]*/msix_version: $version.0/" "$pubspec_file"
        else
            # Linux
            sed -i "s/^version:[[:space:]]*[^[:space:]]*/version: $version+1/" "$pubspec_file"
            sed -i "s/msix_version:[[:space:]]*[^[:space:]]*/msix_version: $version.0/" "$pubspec_file"
        fi

        # 更新 constants.dart 中的版本号
        if [ -f "$constants_file" ]; then
            if [[ "$OSTYPE" == "darwin"* ]]; then
                # macOS
                sed -i '' "s/const String kAppVersion = '[^']*'/const String kAppVersion = '$version'/" "$constants_file"
            else
                # Linux
                sed -i "s/const String kAppVersion = '[^']*'/const String kAppVersion = '$version'/" "$constants_file"
            fi
            log_success "已更新 constants.dart 版本: $version"
        fi

        log_success "已更新 Flutter 版本: $version"
    fi
}

# 更新安装程序版本
update_installer_version() {
    local version="$1"
    local setup_file="$PROJECT_ROOT/installer/setup.iss"
    
    if [ ! -f "$setup_file" ]; then
        log_warning "安装程序脚本不存在: $setup_file"
        return 1
    fi
    
    if [ "$DRY_RUN" = "true" ]; then
        log_info "DRY RUN: 将更新安装程序版本为 $version"
    else
        # 更新 setup.iss 中的版本定义
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s/#define MyAppVersion \"[^\"]*\"/#define MyAppVersion \"$version\"/" "$setup_file"
        else
            # Linux
            sed -i "s/#define MyAppVersion \"[^\"]*\"/#define MyAppVersion \"$version\"/" "$setup_file"
        fi

        log_success "已更新安装程序版本: $version"
    fi
}

# 更新所有组件版本
update_all_components() {
    local version=$(get_version)
    
    log_info "开始更新所有组件版本为: $version"
    
    update_go_version "$version"
    update_flutter_version "$version"
    update_installer_version "$version"
    
    if [ "$NO_GIT" != "true" ]; then
        # 创建 Git 标签
        if git rev-parse --git-dir > /dev/null 2>&1; then
            local tag="v$version"
            if [ "$DRY_RUN" = "true" ]; then
                log_info "DRY RUN: 将创建 Git 标签: $tag"
            else
                if ! git tag -l | grep -q "^$tag$"; then
                    git tag "$tag"
                    log_success "已创建 Git 标签: $tag"
                else
                    log_warning "Git 标签已存在: $tag"
                fi
            fi
        fi
    fi
    
    if [ "$DRY_RUN" = "true" ]; then
        log_success "DRY RUN: 所有组件版本更新预览完成"
    else
        log_success "所有组件版本更新完成"
    fi
}

# 解析命令行参数
DRY_RUN=false
VERBOSE=false
NO_GIT=false

# 保存原始参数
ORIGINAL_ARGS=("$@")

# 解析选项参数
ARGS=()
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --no-git)
            NO_GIT=true
            shift
            ;;
        *)
            ARGS+=("$1")
            shift
            ;;
    esac
done

# 恢复非选项参数
set -- "${ARGS[@]}"

# 主命令处理
case "${1:-help}" in
    "update-all")
        update_all_components
        ;;
    "help"|*)
        show_help
        ;;
esac
