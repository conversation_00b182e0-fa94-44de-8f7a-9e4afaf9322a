#!/bin/bash
# ========================================================================
# VPN Client Backend Build Script for Linux/macOS
# ========================================================================

set -e  # Exit on error

echo "[INFO] Starting VPN Client backend build process..."

# 设置版本信息
VERSION_FILE="$PROJECT_ROOT/VERSION"
if [ -f "$VERSION_FILE" ]; then
    VERSION=$(cat "$VERSION_FILE" | tr -d '\n\r')
    echo "[INFO] Reading version from file: $VERSION"
else
    VERSION="1.0.2"
    echo "[WARNING] Version file not found, using default: $VERSION"
fi

TIMESTAMP=$(date +%Y%m%d%H%M%S)

# 获取Git提交信息
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 生成最终版本号
BUILD_ID="$VERSION-$GIT_COMMIT-$TIMESTAMP"

# 设置目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
OUTPUT_DIR="$PROJECT_ROOT/build/backend"
CONFIG_DIR="$PROJECT_ROOT/configs"
WINTUN_DIR="$PROJECT_ROOT/wintun"

# 检测操作系统
OS=$(uname)
if [ "$OS" = "Darwin" ]; then
    echo "[INFO] Detected macOS platform"
    EXE_EXT=""
elif [ "$OS" = "Linux" ]; then
    echo "[INFO] Detected Linux platform"
    EXE_EXT=""
else
    echo "[INFO] Detected Windows platform"
    EXE_EXT=".exe"
fi

# 检查是否为调试模式
DEBUG_MODE=0
for arg in "$@"; do
    if [ "$arg" = "--debug" ]; then
        DEBUG_MODE=1
    fi
done

# 设置环境变量
if [ "$OS" != "Darwin" ] && [ "$OS" != "Linux" ]; then
    export GOOS=windows
    export GOARCH=amd64
    export CGO_ENABLED=1
fi

# 清理并创建输出目录
echo "[INFO] Cleaning build directory..."
if [ -d "$OUTPUT_DIR" ]; then
    echo "[INFO] Removing existing build files..."
    rm -rf "$OUTPUT_DIR"
fi

echo "[INFO] Creating output directory..."
mkdir -p "$OUTPUT_DIR"

# 编译主程序
echo "[INFO] Compiling backend executable..."
cd "$PROJECT_ROOT"

# 检查是否在非Windows平台上编译Windows程序
if [ "$OS" != "Windows" ]; then
    echo "[INFO] Cross-compiling for Windows on $OS"
    export GOOS=windows
    export GOARCH=amd64
    export CGO_ENABLED=0
fi

echo "[INFO] Checking for import cycles..."
go mod tidy -e 2>/dev/null || true

if [ $DEBUG_MODE -eq 1 ]; then
    echo "[INFO] Building in DEBUG mode..."
    go build -tags windows -o "$OUTPUT_DIR/itforce-service$EXE_EXT" -ldflags "-X main.version=$BUILD_ID" ./cmd/vpn-service
else
    echo "[INFO] Building in RELEASE mode..."
    if [ "$GOOS" = "windows" ] || [ "$OS" = "Windows" ]; then
        go build -tags windows -o "$OUTPUT_DIR/itforce-service$EXE_EXT" -ldflags "-H=windowsgui -s -w -X main.version=$BUILD_ID" ./cmd/vpn-service
    else
        go build -tags windows -o "$OUTPUT_DIR/itforce-service$EXE_EXT" -ldflags "-s -w -X main.version=$BUILD_ID" ./cmd/vpn-service
    fi
fi

# 复制 WinTun 驱动 (仅 Windows)
if [ "$OS" != "Darwin" ] && [ "$OS" != "Linux" ]; then
    echo "[INFO] Copying WinTun driver..."
    if [ -f "$WINTUN_DIR/amd64/wintun.dll" ]; then
        cp "$WINTUN_DIR/amd64/wintun.dll" "$OUTPUT_DIR/"
        echo "[INFO] WinTun driver copied successfully"
    else
        echo "[WARNING] WinTun driver not found at $WINTUN_DIR/amd64/wintun.dll"
        echo "[WARNING] Please download WinTun driver from https://www.wintun.net/ and place it in $WINTUN_DIR/amd64/"
        echo "[WARNING] Creating placeholder file for now..."
        mkdir -p "$WINTUN_DIR/amd64"
        touch "$OUTPUT_DIR/wintun.dll"
    fi
fi

# 复制配置文件
echo "[INFO] Copying configuration files..."
if [ -f "$CONFIG_DIR/config.yaml" ]; then
    cp "$CONFIG_DIR/config.yaml" "$OUTPUT_DIR/"
    echo "[INFO] Configuration file copied successfully"
else
    echo "[WARNING] No configuration file found in $CONFIG_DIR"
    echo "[WARNING] Creating default configuration file..."
    cat > "$OUTPUT_DIR/config.yaml" << EOF
# Default VPN Client Configuration
server:
  http:
    host: 127.0.0.1
    port: 8080
  server_list_url: https://api.example.com/servers
vpn:
  interface:
    name: unisase
    mtu: 1300
logging:
  level: info
  format: json
  outputs:
    - type: file
      file: vpn-service.log
      max_size: 10
      max_backups: 3
      max_age: 28
    - type: console
EOF
fi

# 复制服务器列表文件
echo "[INFO] Copying servers list file..."
if [ -f "$CONFIG_DIR/servers.json" ]; then
    cp "$CONFIG_DIR/servers.json" "$OUTPUT_DIR/"
    echo "[INFO] Servers list file copied successfully"
fi

# 创建版本文件
echo "[INFO] Creating version file..."
echo "$BUILD_ID" > "$OUTPUT_DIR/version.txt"

echo "[SUCCESS] Backend build completed successfully!"
echo "[INFO] Output directory: $OUTPUT_DIR"
echo "[INFO] Version: $BUILD_ID"
