@echo off
setlocal enabledelayedexpansion

REM ========================================================================
REM VPN Client Backend Build Script for Windows
REM ========================================================================

echo [INFO] Starting VPN Client backend build process...

REM 设置目录
set PROJECT_ROOT=%~dp0..

REM 设置版本信息
set VERSION_FILE=%PROJECT_ROOT%\VERSION
if exist "%VERSION_FILE%" (
    set /p VERSION=<"%VERSION_FILE%"
    echo [INFO] Reading version from file: !VERSION!
) else (
    set VERSION=1.1.0
    echo [WARNING] Version file not found, using default: !VERSION!
)

REM 使用PowerShell获取标准格式的日期时间 (年月日时分秒)
for /f "delims=" %%a in ('powershell -Command "Get-Date -Format 'yyyyMMddHHmmss'"') do set TIMESTAMP=%%a

REM 获取Git提交信息
for /f "tokens=*" %%a in ('git rev-parse --short HEAD 2^>nul') do set GIT_COMMIT=%%a
if "%GIT_COMMIT%"=="" set GIT_COMMIT=unknown

REM 生成最终版本号
set BUILD_ID=!VERSION!-%GIT_COMMIT%-%TIMESTAMP%
set OUTPUT_DIR=%PROJECT_ROOT%\build\backend
set CONFIG_DIR=%PROJECT_ROOT%\configs
set WINTUN_DIR=%PROJECT_ROOT%\wintun

REM 设置环境变量
set GOOS=windows
set GOARCH=amd64
set CGO_ENABLED=1

REM 清理并创建输出目录
echo [INFO] Cleaning build directory...
if exist "%OUTPUT_DIR%" (
    echo [INFO] Removing existing build files...
    rd /s /q "%OUTPUT_DIR%"
)

echo [INFO] Creating output directory...
mkdir "%OUTPUT_DIR%"

REM 检查是否为调试模式
set DEBUG_MODE=0
for %%a in (%*) do (
    if "%%a"=="--debug" set DEBUG_MODE=1
)

REM 编译主程序
echo [INFO] Compiling backend executable...
cd "%PROJECT_ROOT%"

echo [INFO] Checking for import cycles...
go mod tidy -e 2>nul

if %DEBUG_MODE%==1 (
    echo [INFO] Building in DEBUG mode...
    go build -tags windows -o "%OUTPUT_DIR%\panabit-service.exe" -ldflags "-X main.version=%BUILD_ID%" .\cmd\vpn-service
) else (
    echo [INFO] Building in RELEASE mode...
    go build -tags windows -o "%OUTPUT_DIR%\panabit-service.exe" -ldflags "-H=windowsgui -s -w -X main.version=%BUILD_ID%" .\cmd\vpn-service
)

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Failed to build backend executable
    exit /b 1
)

REM 复制 WinTun 驱动
echo [INFO] Copying WinTun driver...
if exist "%WINTUN_DIR%\amd64\wintun.dll" (
    copy "%WINTUN_DIR%\amd64\wintun.dll" "%OUTPUT_DIR%\"
    echo [INFO] WinTun driver copied successfully
) else (
    echo [WARNING] WinTun driver not found at %WINTUN_DIR%\amd64\wintun.dll
    echo [WARNING] Please download WinTun driver from https://www.wintun.net/ and place it in %WINTUN_DIR%\amd64\
    echo [WARNING] Creating placeholder file for now...
    if not exist "%WINTUN_DIR%\amd64" mkdir "%WINTUN_DIR%\amd64"
    type nul > "%OUTPUT_DIR%\wintun.dll"
)

REM 复制配置文件
echo [INFO] Copying configuration files...
if exist "%CONFIG_DIR%\config.yaml" (
    copy "%CONFIG_DIR%\config.yaml" "%OUTPUT_DIR%\"
    echo [INFO] Configuration file copied successfully
) else (
    echo [WARNING] No configuration file found in %CONFIG_DIR%
    echo [WARNING] Creating default configuration file...
    (
        echo # Default VPN Client Configuration
        echo server:
        echo   http:
        echo     host: 127.0.0.1
        echo     port: 8080
        echo   server_list_url: https://api.example.com/servers
        echo vpn:
        echo   interface:
        echo     name: unisase
        echo     mtu: 1300
        echo logging:
        echo   level: info
        echo   format: json
        echo   outputs:
        echo     - type: file
        echo       file: vpn-service.log
        echo       max_size: 10
        echo       max_backups: 3
        echo       max_age: 28
        echo     - type: console
    ) > "%OUTPUT_DIR%\config.yaml"
)

REM 复制服务器列表文件
echo [INFO] Copying servers list file...
if exist "%CONFIG_DIR%\servers.json" (
    copy "%CONFIG_DIR%\servers.json" "%OUTPUT_DIR%\"
    echo [INFO] Servers list file copied successfully
)

REM 创建版本文件
echo [INFO] Creating version file...
echo %BUILD_ID% > "%OUTPUT_DIR%\version.txt"

echo [SUCCESS] Backend build completed successfully!
echo [INFO] Output directory: %OUTPUT_DIR%
echo [INFO] Version: %BUILD_ID%

endlocal
