@echo off
echo Verifying build files for Panabit Client...

echo.
echo === Checking Flutter CMakeLists.txt ===
findstr "BINARY_NAME" ui\flutter\windows\CMakeLists.txt

echo.
echo === Checking Flutter build output (if exists) ===
if exist ui\flutter\build\windows\x64\runner\Release (
    echo Flutter x64 Release directory exists:
    dir ui\flutter\build\windows\x64\runner\Release\*.exe
) else (
    echo Flutter x64 Release directory not found
)

if exist ui\flutter\build\windows\runner\Release (
    echo Flutter Release directory exists:
    dir ui\flutter\build\windows\runner\Release\*.exe
) else (
    echo Flutter Release directory not found
)

echo.
echo === Checking build output directories ===
if exist build\frontend (
    echo Frontend build directory exists:
    dir build\frontend\*.exe
) else (
    echo Frontend build directory not found
)

if exist build\backend (
    echo Backend build directory exists:
    dir build\backend\*.exe
) else (
    echo Backend build directory not found
)

echo.
echo === Checking installer build directory ===
if exist installer\build (
    echo Installer build directory exists:
    dir installer\build\*.exe
) else (
    echo Installer build directory not found
)

echo.
echo Verification complete.
