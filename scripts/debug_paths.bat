@echo off
echo Debugging Panabit Client paths...

echo.
echo === Checking current user AppData paths ===
echo APPDATA: %APPDATA%
echo LOCALAPPDATA: %LOCALAPPDATA%

echo.
echo === Checking for existing Panabit directories ===
if exist "%APPDATA%\Panabit" (
    echo Found: %APPDATA%\Panabit
    dir "%APPDATA%\Panabit" /B
) else (
    echo Not found: %APPDATA%\Panabit
)

if exist "%APPDATA%\Panabit Client" (
    echo Found: %APPDATA%\Panabit Client
    dir "%APPDATA%\Panabit Client" /B
) else (
    echo Not found: %APPDATA%\Panabit Client
)

echo.
echo === Checking for old ItForce directories ===
if exist "%APPDATA%\ItForce" (
    echo Found old: %APPDATA%\ItForce
    dir "%APPDATA%\ItForce" /B
) else (
    echo Not found: %APPDATA%\ItForce
)

if exist "%APPDATA%\ItForce WAN" (
    echo Found old: %APPDATA%\ItForce WAN
    dir "%APPDATA%\ItForce WAN" /B
) else (
    echo Not found: %APPDATA%\ItForce WAN
)

echo.
echo === Checking Program Files installations ===
if exist "C:\Program Files\Panabit" (
    echo Found: C:\Program Files\Panabit
    dir "C:\Program Files\Panabit" /B
) else (
    echo Not found: C:\Program Files\Panabit
)

if exist "C:\Program Files (x86)\Panabit" (
    echo Found: C:\Program Files (x86)\Panabit
    dir "C:\Program Files (x86)\Panabit" /B
) else (
    echo Not found: C:\Program Files (x86)\Panabit
)

if exist "C:\Program Files\ItForce" (
    echo Found old: C:\Program Files\ItForce
    dir "C:\Program Files\ItForce" /B
) else (
    echo Not found: C:\Program Files\ItForce
)

if exist "C:\Program Files (x86)\ItForce" (
    echo Found old: C:\Program Files (x86)\ItForce
    dir "C:\Program Files (x86)\ItForce" /B
) else (
    echo Not found: C:\Program Files (x86)\ItForce
)

echo.
echo === Checking Windows Registry for auto-start entries ===
reg query "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Run" /v "Panabit Client" 2>nul
if %ERRORLEVEL% equ 0 (
    echo Found Panabit Client auto-start entry
) else (
    echo No Panabit Client auto-start entry found
)

reg query "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Run" /v "ItForce WAN" 2>nul
if %ERRORLEVEL% equ 0 (
    echo Found old ItForce WAN auto-start entry
) else (
    echo No old ItForce WAN auto-start entry found
)

echo.
echo === Checking for running processes ===
tasklist /FI "IMAGENAME eq panabit_client.exe" 2>nul | find /I "panabit_client.exe" >nul
if %ERRORLEVEL% equ 0 (
    echo panabit_client.exe is running
) else (
    echo panabit_client.exe is not running
)

tasklist /FI "IMAGENAME eq panabit-service.exe" 2>nul | find /I "panabit-service.exe" >nul
if %ERRORLEVEL% equ 0 (
    echo panabit-service.exe is running
) else (
    echo panabit-service.exe is not running
)

tasklist /FI "IMAGENAME eq itforce-service.exe" 2>nul | find /I "itforce-service.exe" >nul
if %ERRORLEVEL% equ 0 (
    echo Old itforce-service.exe is still running
) else (
    echo Old itforce-service.exe is not running
)

echo.
echo Debug complete.
