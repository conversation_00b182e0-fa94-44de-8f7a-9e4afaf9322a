PODS:
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

EXTERNAL SOURCES:
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: f4810288d0049be11f0c708d71c4d9e3eb6a7605

COCOAPODS: 1.16.2
