/**
 * FILE: PacketTunnelProvider.swift
 *
 * DESCRIPTION:
 *     iOS-specific PacketTunnelProvider implementation for Panabit Client VPN.
 *     Uses simplified VPNPacketTunnelProvider from ItForceCore.
 *     Provides iOS-specific NetworkExtension integration.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 simplified architecture - direct use of VPNPacketTunnelProvider
 */

import NetworkExtension
import ItForceCore

/**
 * NAME: PacketTunnelProvider
 *
 * DESCRIPTION:
 *     iOS-specific NetworkExtension provider for Panabit Client.
 *     Uses simplified VPNPacketTunnelProvider from ItForceCore.
 *     No platform-specific customization needed with simplified architecture.
 */
class PacketTunnelProvider: VPNPacketTunnelProvider {
    // No platform-specific customization needed with simplified architecture
    // VPNPacketTunnelProvider handles all functionality directly
}
