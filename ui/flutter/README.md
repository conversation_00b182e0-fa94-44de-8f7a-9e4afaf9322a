# ITforce WAN Flutter 客户端

ITforce WAN 的跨平台 Flutter 用户界面，提供统一的用户体验和现代化的界面设计。

## 项目概述

本项目是 ITforce WAN 的前端界面实现，基于 Flutter 框架开发，支持 Windows、macOS、iOS 和 Android 平台。通过 Platform Channel 与各平台的原生后端服务通信，提供完整的 VPN 连接管理功能。

## 核心特性

- **跨平台支持**：统一的 UI 代码，支持桌面和移动平台
- **现代化设计**：响应式布局，适配不同屏幕尺寸
- **实时状态更新**：通过 Event Channel 实现状态实时同步
- **多语言支持**：支持中文和英文界面
- **主题系统**：统一的设计系统和颜色方案
- **原生集成**：深度集成各平台特有功能

## 技术架构

### 整体架构
```
Flutter UI Layer
├── Screens (界面层)
│   ├── LoginScreen - 登录界面
│   ├── MainScreen - 主界面容器
│   ├── ConnectionScreen - 连接管理
│   ├── UserScreen - 用户信息
│   ├── StatisticsScreen - 流量统计
│   ├── SettingsScreen - 应用设置
│   ├── LogsScreen - 日志查看
│   └── AboutScreen - 关于信息
├── Services (服务层)
│   ├── BackendService - 后端通信
│   ├── ConnectionManager - 连接管理
│   ├── AuthService - 认证服务
│   ├── NotificationService - 通知服务
│   └── Platform Services - 平台特定服务
├── Models (数据模型)
│   ├── ConnectionStatus - 连接状态
│   ├── Server - 服务器信息
│   ├── UserInfo - 用户信息
│   └── TrafficStats - 流量统计
└── Utils (工具类)
    ├── Constants - 常量定义
    ├── Formatters - 格式化工具
    └── Design System - 设计系统
```

### 平台通信

**Windows/Linux**: HTTP API + WebSocket
```dart
// HTTP API 调用
final response = await ApiService.post('/api/connect', data);

// WebSocket 事件监听
WebSocketService.onStatusUpdate((status) => updateUI(status));
```

**iOS/macOS**: Platform Channel + Event Channel
```dart
// Method Channel 调用
await platform.invokeMethod('connect', {'serverId': serverId});

// Event Channel 监听
eventChannel.receiveBroadcastStream().listen((event) => handleEvent(event));
```

**Android**: Platform Channel + Event Channel (规划中)
```dart
// 与 iOS/macOS 相同的 Platform Channel 模式
await platform.invokeMethod('connect', {'serverId': serverId});
```

## 开发环境

### 环境要求
- **Flutter SDK**: 3.24.0+
- **Dart SDK**: 3.5.0+
- **IDE**: VS Code 或 Android Studio
- **平台工具**:
  - Windows: Visual Studio 2022
  - macOS: Xcode 15.0+
  - iOS: Xcode 15.0+
  - Android: Android Studio

### 依赖管理
```yaml
dependencies:
  flutter: sdk: flutter
  provider: ^6.1.2          # 状态管理
  shared_preferences: ^2.2.3 # 本地存储
  http: ^1.2.1              # HTTP 请求
  web_socket_channel: ^2.4.5 # WebSocket 通信
  flutter_localizations: sdk: flutter # 国际化
  # 平台特定依赖
  window_manager: ^0.3.9    # 窗口管理 (桌面)
  package_info_plus: ^8.0.0 # 应用信息
```

## 快速开始

### 1. 环境搭建
```bash
# 检查 Flutter 环境
flutter doctor

# 获取依赖
flutter pub get

# 生成本地化文件
flutter gen-l10n
```

### 2. 运行应用

**Windows 桌面**:
```bash
flutter run -d windows
```

**macOS 桌面**:
```bash
flutter run -d macos
```

**iOS 模拟器**:
```bash
flutter run -d ios
```

**Android 模拟器**:
```bash
flutter run -d android
```

### 3. 构建发布版本

**Windows**:
```bash
flutter build windows --release
```

**macOS**:
```bash
flutter build macos --release
```

**iOS**:
```bash
flutter build ios --release
```

**Android**:
```bash
flutter build apk --release
```

## 项目结构

```
lib/
├── main.dart                 # 应用入口
├── core/                     # 核心功能
│   ├── app_state.dart        # 全局状态
│   ├── dependency_injection.dart # 依赖注入
│   └── error_handler.dart    # 错误处理
├── screens/                  # 界面层
│   ├── login_screen.dart     # 登录界面
│   ├── main_screen.dart      # 主界面
│   ├── connection_screen.dart # 连接界面
│   ├── user_screen.dart      # 用户界面
│   ├── statistics_screen.dart # 统计界面
│   ├── settings_screen.dart  # 设置界面
│   ├── logs_screen.dart      # 日志界面
│   └── about_screen.dart     # 关于界面
├── services/                 # 服务层
│   ├── backend_service.dart  # 后端通信
│   ├── connection_manager.dart # 连接管理
│   ├── auth_service.dart     # 认证服务
│   ├── notification_service.dart # 通知服务
│   └── platform/             # 平台特定服务
├── models/                   # 数据模型
│   ├── connection_status.dart # 连接状态
│   ├── server.dart           # 服务器信息
│   ├── user_info.dart        # 用户信息
│   └── traffic_stats.dart    # 流量统计
├── widgets/                  # 通用组件
│   ├── app_builder.dart      # 应用构建器
│   ├── notification_manager.dart # 通知管理
│   └── responsive_layout.dart # 响应式布局
├── utils/                    # 工具类
│   ├── constants.dart        # 常量定义
│   ├── formatters.dart       # 格式化工具
│   └── design_system.dart    # 设计系统
└── l10n/                     # 国际化
    ├── app_en.arb           # 英文资源
    └── app_zh.arb           # 中文资源
```

## 平台集成

### iOS/macOS 集成
- **Swift Package**: `ItForceCore` 本地包
- **NetworkExtension**: VPN 扩展集成
- **Platform Channel**: 双向通信桥梁

### Windows 集成
- **Go 后端**: HTTP API 服务
- **WebSocket**: 实时状态更新
- **系统集成**: 窗口管理和系统托盘

### Android 集成 (规划中)
- **Kotlin 实现**: 原生 Android 后端
- **VpnService**: Android VPN 框架
- **Platform Channel**: 与 iOS/macOS 一致的通信方式

## 开发指南

### 代码规范
- 遵循 Dart 官方代码规范
- 使用 `flutter analyze` 进行静态分析
- 所有公共方法需要文档注释
- 使用 Provider 进行状态管理

### 测试
```bash
# 运行单元测试
flutter test

# 运行集成测试
flutter test integration_test/
```

### 调试
```bash
# 开启调试模式
flutter run --debug

# 查看日志
flutter logs
```

## 相关文档

- **[项目主文档](../../README.md)** - 完整项目概述
- **[架构设计](../../docs/architecture_design.md)** - 系统架构设计
- **[iOS/macOS 文档](../../docs/ios-macos-docs-index.md)** - iOS/macOS 平台文档
- **[Android 文档](../../docs/android/README.md)** - Android 平台文档
- **[API 规范](../../docs/api_specification.md)** - 接口文档

## 许可证

本项目采用 MIT License 开源许可证。
