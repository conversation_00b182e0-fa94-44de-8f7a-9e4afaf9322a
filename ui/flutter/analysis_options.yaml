include: package:flutter_lints/flutter.yaml

linter:
  rules:
    - avoid_print
    - avoid_empty_else
    - avoid_relative_lib_imports
    - avoid_type_to_string
    - cancel_subscriptions
    - close_sinks
    - control_flow_in_finally
    - empty_statements
    - hash_and_equals
    - no_adjacent_strings_in_list
    - no_duplicate_case_values
    - prefer_void_to_null
    - unnecessary_statements
    - unrelated_type_equality_checks
    - use_key_in_widget_constructors
    - valid_regexps

analyzer:
  errors:
    missing_required_param: error
    missing_return: error
    must_be_immutable: error
    sort_unnamed_constructors_first: ignore
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
