#!/usr/bin/env swift

/**
 * FILE: test_heartbeat_timestamp.swift
 *
 * DESCRIPTION:
 *     Simple test to verify heartbeat packet timestamp implementation.
 *     Tests that buildEchoPacket correctly includes timestamp in microseconds.
 *
 * AUTHOR: wei
 * HISTORY: 05/07/2025 create heartbeat timestamp test
 */

import Foundation

// MARK: - Basic Structures for Testing

enum PacketType: UInt8 {
    case echoRequest = 0x15
}

enum EncryptionMethod: UInt8 {
    case none = 0x00
    case xor = 0x01
    case aes = 0x02
}

struct PacketHeader {
    let type: PacketType
    let encrypt: EncryptionMethod
    let sessionID: UInt16
    let token: UInt32
    
    func toData() -> Data {
        var data = Data()
        data.append(type.rawValue)
        data.append(encrypt.rawValue)
        data.append(contentsOf: withUnsafeBytes(of: sessionID.bigEndian) { Data($0) })
        data.append(contentsOf: withUnsafeBytes(of: token.bigEndian) { Data($0) })
        return data
    }
}

// MARK: - Simplified PacketBuilder for Testing

class TestPacketBuilder {
    func buildEchoPacket(
        header: PacketHeader,
        currentDelay: UInt32,
        minDelay: UInt32,
        maxDelay: UInt32
    ) -> Data {
        // Calculate signature (header + "mw")
        let headerData = header.toData()
        let salt = Data([109, 119]) // ASCII values for 'm' and 'w'
        let _ = headerData + salt  // Not used in this test

        // Simple MD5 simulation (for testing purposes)
        let signature = Data(repeating: 0xAB, count: 16)
        
        // Build echo data payload
        var echoData = Data()
        
        // Add MD5 signature (16 bytes)
        echoData.append(signature)
        
        // Add timestamp in microseconds (8 bytes) - matching Go backend format
        let timestamp = UInt64(Date().timeIntervalSince1970 * 1_000_000)
        echoData.append(contentsOf: withUnsafeBytes(of: timestamp.bigEndian) { Data($0) })
        
        // Add delay information (12 bytes total)
        echoData.append(contentsOf: withUnsafeBytes(of: currentDelay.bigEndian) { Data($0) })  // 4 bytes
        echoData.append(contentsOf: withUnsafeBytes(of: minDelay.bigEndian) { Data($0) })      // 4 bytes
        echoData.append(contentsOf: withUnsafeBytes(of: maxDelay.bigEndian) { Data($0) })      // 4 bytes
        
        // Add SDRT tag (4 bytes)
        let sdrtTag = Data([0x53, 0x44, 0x52, 0x54]) // "SDRT"
        echoData.append(sdrtTag)
        
        // Combine header and payload
        let completePacket = headerData + echoData
        
        return completePacket
    }
}

// MARK: - Test Functions

func testTimestampInclusion() {
    print("🔍 Testing Heartbeat Timestamp Inclusion")
    print(String(repeating: "=", count: 50))
    
    let builder = TestPacketBuilder()
    
    let header = PacketHeader(
        type: .echoRequest,
        encrypt: .xor,
        sessionID: 1234,
        token: 0x56789ABC
    )
    
    let beforeTime = Date().timeIntervalSince1970 * 1_000_000
    let packet = builder.buildEchoPacket(
        header: header,
        currentDelay: 100,
        minDelay: 50,
        maxDelay: 200
    )
    let afterTime = Date().timeIntervalSince1970 * 1_000_000
    
    // Verify packet structure
    print("📋 Packet Analysis:")
    print("  Total size: \(packet.count) bytes (expected: 48)")
    print("  Header size: 8 bytes")
    print("  Signature size: 16 bytes")
    print("  Timestamp size: 8 bytes")
    print("  Delays size: 12 bytes")
    print("  SDRT size: 4 bytes")
    
    // Extract timestamp from packet (bytes 24-31)
    let timestampStart = 8 + 16  // Header + Signature
    let timestampData = packet.subdata(in: timestampStart..<(timestampStart + 8))
    let extractedTimestamp = timestampData.withUnsafeBytes { $0.load(as: UInt64.self).bigEndian }
    
    print("\n⏰ Timestamp Verification:")
    print("  Before build: \(UInt64(beforeTime))")
    print("  Extracted:    \(extractedTimestamp)")
    print("  After build:  \(UInt64(afterTime))")
    
    // Verify timestamp is within reasonable range
    let isValidTimestamp = extractedTimestamp >= UInt64(beforeTime) && extractedTimestamp <= UInt64(afterTime)
    print("  Valid range:  \(isValidTimestamp ? "✅ YES" : "❌ NO")")
    
    // Verify SDRT tag
    let sdrtStart = timestampStart + 8 + 12  // After timestamp and delays
    let sdrtData = packet.subdata(in: sdrtStart..<(sdrtStart + 4))
    let sdrtString = String(data: sdrtData, encoding: .ascii) ?? ""
    print("\n🏷️  SDRT Tag Verification:")
    print("  Expected: SDRT")
    print("  Found:    \(sdrtString)")
    print("  Valid:    \(sdrtString == "SDRT" ? "✅ YES" : "❌ NO")")
    
    // Print hex dump for verification
    let hexString = packet.map { String(format: "%02x", $0) }.joined(separator: " ")
    print("\n📋 Hex dump: \(hexString)")
    
    print("\n🎯 Test Result: \(isValidTimestamp && sdrtString == "SDRT" ? "✅ PASSED" : "❌ FAILED")")
}

func testGoBackendCompatibility() {
    print("\n🔍 Testing Go Backend Compatibility")
    print(String(repeating: "=", count: 50))
    
    // Simulate Go backend timestamp format
    let goTimestamp = UInt64(Date().timeIntervalSince1970 * 1_000_000)
    print("Go backend timestamp format: \(goTimestamp) microseconds")
    
    // Convert back to seconds for verification
    let secondsFromMicros = Double(goTimestamp) / 1_000_000.0
    let originalSeconds = Date().timeIntervalSince1970
    let difference = abs(secondsFromMicros - originalSeconds)
    
    print("Original seconds: \(originalSeconds)")
    print("Converted back:   \(secondsFromMicros)")
    print("Difference:       \(difference) seconds")
    print("Compatible:       \(difference < 1.0 ? "✅ YES" : "❌ NO")")
}

// MARK: - Main Execution

print("🚀 Heartbeat Timestamp Test Suite")
print(String(repeating: "=", count: 60))

testTimestampInclusion()
testGoBackendCompatibility()

print("\n✅ All tests completed!")
