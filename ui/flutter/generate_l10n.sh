#!/bin/bash

echo "Generating localization files..."

# 检查Flutter是否可用
if ! command -v flutter &> /dev/null; then
    echo "Flutter command not found. Please ensure Flutter is installed and in PATH."
    exit 1
fi

# 生成本地化文件
echo "Running flutter gen-l10n..."
flutter gen-l10n

if [ $? -eq 0 ]; then
    echo "Localization files generated successfully!"
else
    echo "Failed to generate localization files."
    exit 1
fi

echo ""
echo "Generated files:"
echo "- lib/generated/l10n/app_localizations.dart"
echo "- lib/generated/l10n/app_localizations_en.dart"
echo "- lib/generated/l10n/app_localizations_zh.dart"
