/**
 * FILE: AppDelegate.swift
 *
 * DESCRIPTION:
 *     macOS AppDelegate with Platform Channel integration.
 *     Initializes universal Platform Channel handler from ItForceCore.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 integrate universal Platform Channel handler
 */

import Cocoa
import FlutterMacOS
import ItForceCore

@main
class AppDelegate: FlutterAppDelegate {

    private var platformChannelHandler: PlatformChannelHandler?

    override func applicationDidFinishLaunching(_ notification: Notification) {
        // Initialize Platform Channel handlers
        setupPlatformChannelHandlers()

        super.applicationDidFinishLaunching(notification)
    }

    override func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return true
    }

    override func applicationSupportsSecureRestorableState(_ app: NSApplication) -> Bool {
        return true
    }

    /**
     * NAME: setupPlatformChannelHandlers
     *
     * DESCRIPTION:
     *     Sets up Platform Channel handler for macOS.
     *     Registers VPN methods/events channels.
     */
    private func setupPlatformChannelHandlers() {
        guard let mainFlutterWindow = NSApplication.shared.windows.first(where: { $0 is NSWindow }),
              let contentViewController = mainFlutterWindow.contentViewController as? FlutterViewController else {
            print("Failed to get Flutter view controller")
            return
        }

        let binaryMessenger = contentViewController.engine.binaryMessenger

        // Initialize VPN Platform Channel handler (methods/events)
        platformChannelHandler = PlatformChannelHandler()
        if let handler = platformChannelHandler {
            handler.configureFlutter(binaryMessenger: binaryMessenger)
        }

        print("macOS Platform Channel handler initialized successfully")
        print("- VPN methods/events channel: \(PlatformChannelHandler.methodChannelName)")
        print("- VPN events channel: \(PlatformChannelHandler.eventChannelName)")
    }
}
