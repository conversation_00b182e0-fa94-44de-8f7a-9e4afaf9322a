/**
 * FILE: PacketTunnelProvider.swift
 *
 * DESCRIPTION:
 *     macOS-specific PacketTunnelProvider implementation for ItForce VPN.
 *     Uses simplified VPNPacketTunnelProvider from ItForceCore.
 *     Provides macOS-specific NetworkExtension integration.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 simplified architecture - direct use of VPNPacketTunnelProvider
 */

import NetworkExtension
import ItForceCore

/**
 * NAME: PacketTunnelProvider
 *
 * DESCRIPTION:
 *     macOS-specific NetworkExtension provider for ItForce VPN.
 *     Uses simplified VPNPacketTunnelProvider from ItForceCore.
 *     No platform-specific customization needed with simplified architecture.
 */
class PacketTunnelProvider: VPNPacketTunnelProvider {

    // No platform-specific customization needed with simplified architecture
    // VPNPacketTunnelProvider handles all functionality directly
}


