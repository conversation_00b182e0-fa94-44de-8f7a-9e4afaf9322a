#!/bin/bash

# FILE: add_network_extension.sh
#
# DESCRIPTION:
#     Script to add NetworkExtension target to macOS project
#
# AUTHOR: wei
# HISTORY: 23/06/2025 create

set -e

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_FILE="$PROJECT_DIR/Runner.xcodeproj/project.pbxproj"
EXTENSION_NAME="ItForceVPNExtension"
BUNDLE_ID="com.itforce.vpn.extension"

echo "🚀 Adding NetworkExtension target to macOS project..."

# Check if Xcode project exists
if [ ! -f "$PROJECT_FILE" ]; then
    echo "❌ Error: Xcode project not found at $PROJECT_FILE"
    exit 1
fi

# Create extension directory structure
echo "📁 Creating extension directory structure..."
mkdir -p "$PROJECT_DIR/$EXTENSION_NAME"

# Create PacketTunnelProvider.swift for macOS
cat > "$PROJECT_DIR/$EXTENSION_NAME/PacketTunnelProvider.swift" << 'EOF'
/**
 * FILE: PacketTunnelProvider.swift
 *
 * DESCRIPTION:
 *     NetworkExtension PacketTunnelProvider implementation for ItForce VPN (macOS)
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import NetworkExtension
import os.log

class PacketTunnelProvider: NEPacketTunnelProvider {
    
    private let logger = Logger(subsystem: "com.itforce.vpn.extension", category: "PacketTunnel")
    
    override func startTunnel(options: [String : NSObject]?, completionHandler: @escaping (Error?) -> Void) {
        logger.info("Starting VPN tunnel on macOS...")
        
        // TODO: Implement SDWAN ZZVPN protocol connection
        // This is a placeholder implementation
        
        let settings = NEPacketTunnelNetworkSettings(tunnelRemoteAddress: "127.0.0.1")
        
        // Configure IPv4 settings
        let ipv4Settings = NEIPv4Settings(addresses: ["********"], subnetMasks: ["*************"])
        ipv4Settings.includedRoutes = [NEIPv4Route.default()]
        
        // macOS specific: exclude local networks
        ipv4Settings.excludedRoutes = [
            NEIPv4Route(destinationAddress: "***********", subnetMask: "***********"),
            NEIPv4Route(destinationAddress: "10.0.0.0", subnetMask: "*********"),
            NEIPv4Route(destinationAddress: "**********", subnetMask: "***********")
        ]
        
        settings.ipv4Settings = ipv4Settings
        
        // Configure DNS settings
        let dnsSettings = NEDNSSettings(servers: ["*******", "*******"])
        settings.dnsSettings = dnsSettings
        
        // macOS specific: Configure MTU
        settings.mtu = 1400
        
        setTunnelNetworkSettings(settings) { error in
            if let error = error {
                self.logger.error("Failed to set tunnel network settings: \(error.localizedDescription)")
                completionHandler(error)
            } else {
                self.logger.info("VPN tunnel started successfully on macOS")
                completionHandler(nil)
            }
        }
    }
    
    override func stopTunnel(with reason: NEProviderStopReason, completionHandler: @escaping () -> Void) {
        logger.info("Stopping VPN tunnel with reason: \(reason)")
        
        // TODO: Implement proper cleanup
        
        completionHandler()
    }
    
    override func handleAppMessage(_ messageData: Data, completionHandler: ((Data?) -> Void)?) {
        logger.debug("Received app message on macOS")
        
        // TODO: Implement communication with main app
        
        completionHandler?(nil)
    }
}
EOF

# Create Info.plist for macOS extension
cat > "$PROJECT_DIR/$EXTENSION_NAME/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>\$(DEVELOPMENT_LANGUAGE)</string>
    <key>CFBundleDisplayName</key>
    <string>$EXTENSION_NAME</string>
    <key>CFBundleExecutable</key>
    <string>\$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>\$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>\$(PRODUCT_NAME)</string>
    <key>CFBundlePackageType</key>
    <string>\$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundleVersion</key>
    <string>1</string>
    <key>LSMinimumSystemVersion</key>
    <string>11.0</string>
    <key>NSExtension</key>
    <dict>
        <key>NSExtensionPointIdentifier</key>
        <string>com.apple.networkextension.packet-tunnel</string>
        <key>NSExtensionPrincipalClass</key>
        <string>\$(PRODUCT_MODULE_NAME).PacketTunnelProvider</string>
    </dict>
</dict>
</plist>
EOF

# Create entitlements file for macOS
cat > "$PROJECT_DIR/$EXTENSION_NAME/$EXTENSION_NAME.entitlements" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>com.apple.security.app-sandbox</key>
    <true/>
    <key>com.apple.security.network.client</key>
    <true/>
    <key>com.apple.security.application-groups</key>
    <array>
        <string>group.com.itforce.vpn.shared</string>
    </array>
    <key>com.apple.developer.networking.networkextension</key>
    <array>
        <string>packet-tunnel-provider</string>
    </array>
</dict>
</plist>
EOF

echo "✅ macOS NetworkExtension files created successfully!"
echo ""
echo "📋 Next steps for macOS:"
echo "1. Open Runner.xcworkspace in Xcode"
echo "2. Add the $EXTENSION_NAME folder to your project"
echo "3. Create a new target using the created files"
echo "4. Configure build settings and code signing"
echo "5. Set macOS Deployment Target to 11.0"
echo ""
echo "💡 Tip: macOS configuration is similar to iOS but with different entitlements"
