<svg width="131" height="132" viewBox="0 0 131 132" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="65.0641" cy="65.9999" r="65.0641" fill="white"/>
<g filter="url(#filter0_i_914_454)">
<circle cx="65.0645" cy="65.8945" r="62" fill="#FF7A00"/>
</g>
<path d="M73.0449 36.6147C72.1366 36.2149 71.076 36.6271 70.6761 37.5355C70.2763 38.4439 70.6887 39.5044 71.597 39.9042C77.5244 42.5131 81.3545 48.3823 81.3545 54.8568C81.3545 59.2181 79.6561 63.3185 76.5721 66.4024C73.4881 69.4863 69.3879 71.1848 65.0265 71.1848C60.6652 71.1848 56.5648 69.4863 53.4809 66.4024C50.3969 63.3185 48.6986 59.2181 48.6986 54.8568C48.6986 48.3452 52.5591 42.4626 58.5338 39.8702C59.4443 39.4751 59.862 38.4169 59.467 37.5064C59.072 36.5959 58.0135 36.1781 57.1032 36.5732C49.8142 39.7358 45.1045 46.9126 45.1045 54.8567C45.1045 57.5454 45.6315 60.1546 46.6708 62.6118C47.6743 64.9843 49.1105 67.1147 50.9395 68.9437C52.7685 70.7727 54.8988 72.2089 57.2714 73.2124C59.7286 74.2517 62.3379 74.7787 65.0265 74.7787C67.7151 74.7787 70.3244 74.2517 72.7816 73.2124C75.1542 72.2089 77.2845 70.7727 79.1135 68.9437C80.9425 67.1147 82.3787 64.9843 83.3822 62.6118C84.4215 60.1546 84.9485 57.5454 84.9485 54.8567C84.9485 46.9578 80.276 39.7973 73.0449 36.6147Z" fill="white"/>
<path d="M65.0646 53.0546C66.0571 53.0546 66.8617 52.25 66.8617 51.2576V33.1574C66.8617 32.1649 66.0571 31.3604 65.0646 31.3604C64.0721 31.3604 63.2676 32.1649 63.2676 33.1574V51.2576C63.2676 52.25 64.0722 53.0546 65.0646 53.0546Z" fill="white"/>
<path d="M31.4491 91.2465C32.7931 91.2465 33.9131 91.5985 34.7771 92.3025C35.6091 92.9745 36.1051 93.9025 36.2811 95.0545H34.4571C34.2651 94.3025 33.9131 93.7585 33.3851 93.4065C32.8731 93.0545 32.2171 92.8945 31.4171 92.8945C30.2171 92.8945 29.3211 93.2945 28.7131 94.1265C28.1531 94.8625 27.8811 95.8865 27.8811 97.1985C27.8811 98.5425 28.1531 99.5825 28.6971 100.303C29.2891 101.071 30.2171 101.471 31.4651 101.471C32.2811 101.471 32.9531 101.263 33.4651 100.879C34.0091 100.447 34.3931 99.7905 34.6171 98.9265H36.4411C36.1851 100.271 35.5931 101.311 34.6491 102.063C33.7691 102.767 32.7131 103.119 31.4811 103.119C29.5771 103.119 28.1531 102.511 27.2251 101.327C26.4091 100.303 26.0091 98.9265 26.0091 97.1985C26.0091 95.5025 26.4251 94.1105 27.2731 93.0545C28.2331 91.8385 29.6251 91.2465 31.4491 91.2465ZM43.2799 91.2465C45.0239 91.2465 46.3999 91.8065 47.4079 92.9425C48.3679 94.0145 48.8479 95.4385 48.8479 97.1985C48.8479 98.9585 48.3679 100.367 47.4079 101.439C46.3999 102.559 45.0239 103.119 43.2799 103.119C41.5199 103.119 40.1439 102.543 39.1519 101.423C38.1919 100.335 37.7279 98.9265 37.7279 97.1985C37.7279 95.4545 38.1919 94.0465 39.1519 92.9585C40.1439 91.8065 41.5199 91.2465 43.2799 91.2465ZM43.2799 92.8945C42.0959 92.8945 41.1839 93.2945 40.5279 94.0945C39.9039 94.8625 39.5999 95.8865 39.5999 97.1985C39.5999 98.4945 39.9039 99.5185 40.5279 100.287C41.1679 101.071 42.0959 101.471 43.2799 101.471C44.4639 101.471 45.3759 101.087 46.0159 100.335C46.6399 99.5825 46.9599 98.5425 46.9599 97.1985C46.9599 95.8545 46.6399 94.7985 46.0159 94.0305C45.3759 93.2625 44.4639 92.8945 43.2799 92.8945ZM50.6141 91.4705H52.4861L58.1341 99.7105H58.1981V91.4705H60.0861V102.895H58.2621L52.5501 94.5425H52.4861V102.895H50.6141V91.4705ZM62.3329 91.4705H64.2049L69.8529 99.7105H69.9169V91.4705H71.8049V102.895H69.9809L64.2689 94.5425H64.2049V102.895H62.3329V91.4705ZM74.0516 91.4705H82.2916V93.0705H75.9236V96.2385H81.9076V97.8385H75.9236V101.295H82.5636V102.895H74.0516V91.4705ZM89.2773 91.2465C90.6213 91.2465 91.7413 91.5985 92.6053 92.3025C93.4373 92.9745 93.9333 93.9025 94.1093 95.0545H92.2853C92.0933 94.3025 91.7413 93.7585 91.2133 93.4065C90.7013 93.0545 90.0453 92.8945 89.2453 92.8945C88.0453 92.8945 87.1493 93.2945 86.5413 94.1265C85.9813 94.8625 85.7093 95.8865 85.7093 97.1985C85.7093 98.5425 85.9813 99.5825 86.5253 100.303C87.1173 101.071 88.0453 101.471 89.2933 101.471C90.1093 101.471 90.7813 101.263 91.2933 100.879C91.8373 100.447 92.2213 99.7905 92.4453 98.9265H94.2693C94.0133 100.271 93.4213 101.311 92.4773 102.063C91.5973 102.767 90.5413 103.119 89.3093 103.119C87.4053 103.119 85.9813 102.511 85.0533 101.327C84.2373 100.303 83.8373 98.9265 83.8373 97.1985C83.8373 95.5025 84.2533 94.1105 85.1013 93.0545C86.0613 91.8385 87.4533 91.2465 89.2773 91.2465ZM95.156 91.4705H104.516V93.0705H100.772V102.895H98.9V93.0705H95.156V91.4705Z" fill="white"/>
<defs>
<filter id="filter0_i_914_454" x="3.06445" y="3.89453" width="124" height="134" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="7"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_914_454"/>
</filter>
</defs>
</svg>
