<svg width="132" height="132" viewBox="0 0 132 132" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="65.9996" cy="66" r="65.0641" fill="white"/>
<g filter="url(#filter0_i_914_445)">
<circle cx="66" cy="65.8945" r="62" fill="#33DB87"/>
</g>
<path d="M73.9804 36.6146C73.0721 36.2148 72.0115 36.627 71.6117 37.5354C71.2119 38.4438 71.6242 39.5043 72.5326 39.9041C78.4599 42.5129 82.29 48.3822 82.29 54.8567C82.29 59.218 80.5916 63.3183 77.5076 66.4023C74.4237 69.4862 70.3234 71.1846 65.9621 71.1846C61.6007 71.1846 57.5004 69.4862 54.4165 66.4023C51.3325 63.3183 49.6341 59.218 49.6341 54.8567C49.6341 48.3451 53.4947 42.4625 59.4693 39.8701C60.3798 39.475 60.7976 38.4167 60.4026 37.5063C60.0075 36.5958 58.9491 36.178 58.0387 36.573C50.7498 39.7357 46.04 46.9124 46.04 54.8566C46.04 57.5453 46.567 60.1545 47.6063 62.6117C48.6098 64.9842 50.046 67.1146 51.875 68.9436C53.7041 70.7726 55.8344 72.2088 58.207 73.2123C60.6642 74.2516 63.2734 74.7785 65.9621 74.7785C68.6507 74.7785 71.26 74.2516 73.7171 73.2123C76.0897 72.2088 78.22 70.7726 80.049 68.9436C81.878 67.1146 83.3142 64.9842 84.3177 62.6117C85.3571 60.1545 85.884 57.5453 85.884 54.8566C85.8841 46.9576 81.2115 39.7972 73.9804 36.6146Z" fill="white"/>
<path d="M66.0002 53.0545C66.9926 53.0545 67.7972 52.2499 67.7972 51.2575V33.1573C67.7972 32.1648 66.9927 31.3602 66.0002 31.3602C65.0076 31.3602 64.2031 32.1648 64.2031 33.1573V51.2575C64.2031 52.2499 65.0077 53.0545 66.0002 53.0545Z" fill="white"/>
<path d="M17.6262 91.1844H21.5412C23.2812 91.1844 24.5862 91.6644 25.4862 92.6244C26.3412 93.5244 26.7762 94.8294 26.7762 96.5394C26.7762 98.2344 26.3412 99.5394 25.4862 100.454C24.5862 101.414 23.2812 101.894 21.5412 101.894H17.6262V91.1844ZM19.3812 92.6844V100.394H21.2112C22.5462 100.394 23.5212 100.079 24.1362 99.4644C24.7362 98.8344 25.0362 97.8594 25.0362 96.5394C25.0362 95.1894 24.7362 94.1994 24.1362 93.5994C23.5212 92.9844 22.5462 92.6844 21.2112 92.6844H19.3812ZM28.4221 91.1844H30.1771V101.894H28.4221V91.1844ZM36.0546 90.9744C37.3146 90.9744 38.3046 91.2294 39.0096 91.7544C39.7596 92.3094 40.1946 93.1794 40.2996 94.3494H38.5596C38.4096 93.6894 38.1246 93.2094 37.7346 92.9244C37.3446 92.6244 36.7446 92.4894 35.9646 92.4894C35.2896 92.4894 34.7796 92.5794 34.4196 92.7744C33.9696 92.9994 33.7596 93.3744 33.7596 93.8844C33.7596 94.3344 33.9996 94.7094 34.5096 94.9794C34.7346 95.0994 35.3496 95.3244 36.3396 95.6394C37.7946 96.0894 38.7246 96.4494 39.1446 96.6894C40.0746 97.2444 40.5396 98.0094 40.5396 98.9994C40.5396 99.9594 40.1646 100.724 39.4146 101.279C38.6646 101.819 37.5996 102.104 36.2346 102.104C34.9146 102.104 33.8796 101.849 33.1446 101.339C32.2446 100.709 31.7496 99.7194 31.6746 98.3544H33.4146C33.5346 99.1644 33.8196 99.7494 34.2846 100.094C34.7046 100.394 35.3496 100.559 36.2346 100.559C37.0146 100.559 37.6446 100.424 38.0946 100.169C38.5446 99.8994 38.7846 99.5544 38.7846 99.1044C38.7846 98.5344 38.4396 98.0844 37.7796 97.7544C37.5696 97.6494 36.8796 97.4094 35.6946 97.0494C34.3746 96.6294 33.5496 96.3294 33.2346 96.1494C32.4096 95.6544 32.0046 94.9344 32.0046 93.9894C32.0046 93.0444 32.3946 92.2944 33.2046 91.7544C33.9546 91.2294 34.8996 90.9744 36.0546 90.9744ZM46.6951 90.9744C47.9551 90.9744 49.0051 91.3044 49.8151 91.9644C50.5951 92.5944 51.0601 93.4644 51.2251 94.5444H49.5151C49.3351 93.8394 49.0051 93.3294 48.5101 92.9994C48.0301 92.6694 47.4151 92.5194 46.6651 92.5194C45.5401 92.5194 44.7001 92.8944 44.1301 93.6744C43.6051 94.3644 43.3501 95.3244 43.3501 96.5544C43.3501 97.8144 43.6051 98.7894 44.1151 99.4644C44.6701 100.184 45.5401 100.559 46.7101 100.559C47.4751 100.559 48.1051 100.364 48.5851 100.004C49.0951 99.5994 49.4551 98.9844 49.6651 98.1744H51.3751C51.1351 99.4344 50.5801 100.409 49.6951 101.114C48.8701 101.774 47.8801 102.104 46.7251 102.104C44.9401 102.104 43.6051 101.534 42.7351 100.424C41.9701 99.4644 41.5951 98.1744 41.5951 96.5544C41.5951 94.9644 41.9851 93.6594 42.7801 92.6694C43.6801 91.5294 44.9851 90.9744 46.6951 90.9744ZM57.7864 90.9744C59.4214 90.9744 60.7114 91.4994 61.6564 92.5644C62.5564 93.5694 63.0064 94.9044 63.0064 96.5544C63.0064 98.2044 62.5564 99.5244 61.6564 100.529C60.7114 101.579 59.4214 102.104 57.7864 102.104C56.1364 102.104 54.8464 101.564 53.9164 100.514C53.0164 99.4944 52.5814 98.1744 52.5814 96.5544C52.5814 94.9194 53.0164 93.5994 53.9164 92.5794C54.8464 91.4994 56.1364 90.9744 57.7864 90.9744ZM57.7864 92.5194C56.6764 92.5194 55.8214 92.8944 55.2064 93.6444C54.6214 94.3644 54.3364 95.3244 54.3364 96.5544C54.3364 97.7694 54.6214 98.7294 55.2064 99.4494C55.8064 100.184 56.6764 100.559 57.7864 100.559C58.8964 100.559 59.7514 100.199 60.3514 99.4944C60.9364 98.7894 61.2364 97.8144 61.2364 96.5544C61.2364 95.2944 60.9364 94.3044 60.3514 93.5844C59.7514 92.8644 58.8964 92.5194 57.7864 92.5194ZM64.6623 91.1844H66.4173L71.7123 98.9094H71.7723V91.1844H73.5423V101.894H71.8323L66.4773 94.0644H66.4173V101.894H64.6623V91.1844ZM75.6486 91.1844H77.4036L82.6986 98.9094H82.7586V91.1844H84.5286V101.894H82.8186L77.4636 94.0644H77.4036V101.894H75.6486V91.1844ZM86.635 91.1844H94.36V92.6844H88.39V95.6544H94V97.1544H88.39V100.394H94.615V101.894H86.635V91.1844ZM100.909 90.9744C102.169 90.9744 103.219 91.3044 104.029 91.9644C104.809 92.5944 105.274 93.4644 105.439 94.5444H103.729C103.549 93.8394 103.219 93.3294 102.724 92.9994C102.244 92.6694 101.629 92.5194 100.879 92.5194C99.754 92.5194 98.914 92.8944 98.344 93.6744C97.819 94.3644 97.564 95.3244 97.564 96.5544C97.564 97.8144 97.819 98.7894 98.329 99.4644C98.884 100.184 99.754 100.559 100.924 100.559C101.689 100.559 102.319 100.364 102.799 100.004C103.309 99.5994 103.669 98.9844 103.879 98.1744H105.589C105.349 99.4344 104.794 100.409 103.909 101.114C103.084 101.774 102.094 102.104 100.939 102.104C99.154 102.104 97.819 101.534 96.949 100.424C96.184 99.4644 95.809 98.1744 95.809 96.5544C95.809 94.9644 96.199 93.6594 96.994 92.6694C97.894 91.5294 99.199 90.9744 100.909 90.9744ZM106.42 91.1844H115.195V92.6844H111.685V101.894H109.93V92.6844H106.42V91.1844Z" fill="white"/>
<defs>
<filter id="filter0_i_914_445" x="4" y="3.89453" width="124" height="134" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="7"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_914_445"/>
</filter>
</defs>
</svg>
