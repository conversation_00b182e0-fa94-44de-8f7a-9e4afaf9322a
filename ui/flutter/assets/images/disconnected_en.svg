<svg width="132" height="131" viewBox="0 0 132 131" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="66" cy="65.2421" r="65.0641" fill="white"/>
<g filter="url(#filter0_i_926_2154)">
<circle cx="66" cy="65.1367" r="62" fill="#C8C8C8"/>
</g>
<path d="M73.981 35.8569C73.0727 35.4571 72.0121 35.8693 71.6123 36.7777C71.2125 37.6861 71.6248 38.7466 72.5332 39.1464C78.4606 41.7552 82.2906 47.6245 82.2906 54.099C82.2906 58.4603 80.5922 62.5606 77.5083 65.6446C74.4243 68.7285 70.324 70.427 65.9627 70.427C61.6014 70.427 57.501 68.7285 54.4171 65.6446C51.3331 62.5606 49.6347 58.4603 49.6347 54.099C49.6347 47.5874 53.4953 41.7048 59.4699 39.1124C60.3804 38.7173 60.7982 37.6591 60.4032 36.7486C60.0081 35.8381 58.9497 35.4203 58.0393 35.8153C50.7504 38.978 46.0406 46.1547 46.0406 54.0989C46.0406 56.7876 46.5676 59.3968 47.6069 61.854C48.6104 64.2265 50.0466 66.3569 51.8757 68.1859C53.7047 70.0149 55.835 71.4511 58.2076 72.4546C60.6648 73.4939 63.274 74.0209 65.9627 74.0209C68.6513 74.0209 71.2606 73.4939 73.7177 72.4546C76.0903 71.4511 78.2206 70.0149 80.0497 68.1859C81.8786 66.3569 83.3148 64.2265 84.3183 61.854C85.3577 59.3968 85.8846 56.7876 85.8846 54.0989C85.8847 46.1999 81.2122 39.0395 73.981 35.8569Z" fill="white"/>
<path d="M66.0004 52.2969C66.9929 52.2969 67.7974 51.4923 67.7974 50.4998V32.3996C67.7974 31.4072 66.9929 30.6026 66.0004 30.6026C65.0079 30.6026 64.2034 31.4072 64.2034 32.3996V50.4998C64.2034 51.4923 65.0079 52.2969 66.0004 52.2969Z" fill="white"/>
<path d="M35.8285 96.1211L37.3441 96.5039C37.0264 97.7487 36.4535 98.6992 35.6254 99.3555C34.8024 100.007 33.7946 100.332 32.6019 100.332C31.3676 100.332 30.3623 100.082 29.5863 99.582C28.8155 99.0768 28.2269 98.3477 27.8207 97.3945C27.4196 96.4414 27.2191 95.418 27.2191 94.3242C27.2191 93.1315 27.4457 92.0924 27.8988 91.207C28.3571 90.3164 29.0056 89.6419 29.8441 89.1836C30.6879 88.7201 31.6149 88.4883 32.6254 88.4883C33.7712 88.4883 34.7347 88.7799 35.516 89.3633C36.2972 89.9466 36.8415 90.7669 37.1488 91.8242L35.6566 92.1758C35.391 91.3424 35.0056 90.7357 34.5004 90.3555C33.9952 89.9753 33.3597 89.7852 32.5941 89.7852C31.7139 89.7852 30.9769 89.9961 30.3832 90.418C29.7946 90.8398 29.3806 91.4076 29.141 92.1211C28.9014 92.8294 28.7816 93.5612 28.7816 94.3164C28.7816 95.2904 28.9222 96.1419 29.2035 96.8711C29.4899 97.5951 29.9327 98.1367 30.5316 98.4961C31.1306 98.8555 31.779 99.0352 32.4769 99.0352C33.3259 99.0352 34.0446 98.7904 34.6332 98.3008C35.2217 97.8112 35.6202 97.0846 35.8285 96.1211ZM38.7582 94.5586C38.7582 92.6576 39.2686 91.1706 40.2894 90.0977C41.3103 89.0195 42.628 88.4805 44.2426 88.4805C45.2998 88.4805 46.253 88.7331 47.1019 89.2383C47.9509 89.7435 48.5967 90.4492 49.0394 91.3555C49.4873 92.2565 49.7113 93.2799 49.7113 94.4258C49.7113 95.5872 49.4769 96.6263 49.0082 97.543C48.5394 98.4596 47.8754 99.1549 47.016 99.6289C46.1566 100.098 45.2295 100.332 44.2347 100.332C43.1566 100.332 42.1931 100.072 41.3441 99.5508C40.4952 99.0299 39.8519 98.319 39.4144 97.418C38.9769 96.5169 38.7582 95.5638 38.7582 94.5586ZM40.3207 94.582C40.3207 95.9622 40.6905 97.0508 41.4301 97.8477C42.1748 98.6393 43.1071 99.0352 44.2269 99.0352C45.3676 99.0352 46.3051 98.6341 47.0394 97.832C47.779 97.0299 48.1488 95.8919 48.1488 94.418C48.1488 93.4857 47.9899 92.6732 47.6722 91.9805C47.3597 91.2826 46.8988 90.7435 46.2894 90.3633C45.6853 89.9779 45.0056 89.7852 44.2504 89.7852C43.1774 89.7852 42.253 90.1549 41.4769 90.8945C40.7061 91.6289 40.3207 92.8581 40.3207 94.582ZM51.6566 100.137V88.6836H53.2113L59.2269 97.6758V88.6836H60.6801V100.137H59.1254L53.1097 91.1367V100.137H51.6566ZM63.2191 100.137V88.6836H64.7738L70.7894 97.6758V88.6836H72.2426V100.137H70.6879L64.6722 91.1367V100.137H63.2191ZM74.8285 100.137V88.6836H83.1097V90.0352H76.3441V93.543H82.6801V94.8867H76.3441V98.7852H83.3754V100.137H74.8285ZM93.641 96.1211L95.1566 96.5039C94.8389 97.7487 94.266 98.6992 93.4379 99.3555C92.6149 100.007 91.6071 100.332 90.4144 100.332C89.1801 100.332 88.1748 100.082 87.3988 99.582C86.628 99.0768 86.0394 98.3477 85.6332 97.3945C85.2321 96.4414 85.0316 95.418 85.0316 94.3242C85.0316 93.1315 85.2582 92.0924 85.7113 91.207C86.1696 90.3164 86.8181 89.6419 87.6566 89.1836C88.5004 88.7201 89.4274 88.4883 90.4379 88.4883C91.5837 88.4883 92.5472 88.7799 93.3285 89.3633C94.1097 89.9466 94.654 90.7669 94.9613 91.8242L93.4691 92.1758C93.2035 91.3424 92.8181 90.7357 92.3129 90.3555C91.8077 89.9753 91.1722 89.7852 90.4066 89.7852C89.5264 89.7852 88.7894 89.9961 88.1957 90.418C87.6071 90.8398 87.1931 91.4076 86.9535 92.1211C86.7139 92.8294 86.5941 93.5612 86.5941 94.3164C86.5941 95.2904 86.7347 96.1419 87.016 96.8711C87.3024 97.5951 87.7452 98.1367 88.3441 98.4961C88.9431 98.8555 89.5915 99.0352 90.2894 99.0352C91.1384 99.0352 91.8571 98.7904 92.4457 98.3008C93.0342 97.8112 93.4327 97.0846 93.641 96.1211ZM99.9457 100.137V90.0352H96.1722V88.6836H105.25V90.0352H101.461V100.137H99.9457Z" fill="white"/>
<defs>
<filter id="filter0_i_926_2154" x="4" y="3.13672" width="124" height="134" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="7"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_926_2154"/>
</filter>
</defs>
</svg>
