#!/usr/bin/env dart

/// 验证日志优化效果的脚本
/// 
/// 用途：
/// 1. 检查Flutter项目的日志配置
/// 2. 验证debug日志是否已被正确注释
/// 3. 确认关键日志仍然保留
/// 
/// 运行方式：
/// dart verify_log_optimization.dart

import 'dart:io';

void main() async {
  stdout.writeln('🔍 开始验证日志优化效果...\n');

  // 1. 检查Flutter日志配置
  await checkFlutterLogConfig();

  // 2. 检查debug日志注释情况
  await checkDebugLogComments();

  // 3. 检查关键日志保留情况
  await checkCriticalLogsRetained();

  stdout.writeln('\n✅ 日志优化验证完成！');
}

/// 检查Flutter日志配置
Future<void> checkFlutterLogConfig() async {
  stdout.writeln('📋 检查Flutter日志配置...');

  // 检查PlatformLogService的默认日志级别
  final platformLogFile = File('lib/services/platform/platform_log_service.dart');
  if (await platformLogFile.exists()) {
    final content = await platformLogFile.readAsString();
    if (content.contains('LogLevel _currentLogLevel = LogLevel.info')) {
      stdout.writeln('  ✅ Flutter默认日志级别已设置为info');
    } else {
      stdout.writeln('  ❌ Flutter默认日志级别配置可能有问题');
    }
  }

  stdout.writeln('');
}

/// 检查debug日志注释情况
Future<void> checkDebugLogComments() async {
  stdout.writeln('🔇 检查debug日志注释情况...');
  
  final filesToCheck = [
    'lib/main.dart',
    'lib/services/auth_service.dart',
    'lib/services/connection_manager.dart',
    'lib/services/backend_service.dart',
    'lib/services/data_manager.dart',
  ];
  
  int totalDebugLogs = 0;
  int commentedDebugLogs = 0;
  
  for (final filePath in filesToCheck) {
    final file = File(filePath);
    if (await file.exists()) {
      final content = await file.readAsString();
      final lines = content.split('\n');
      
      for (int i = 0; i < lines.length; i++) {
        final line = lines[i];
        
        // 检查debug日志
        if (line.contains('debugPrint(') || 
            line.contains('logService.debug(') ||
            line.contains('.debug(') ||
            line.contains('print(\'🚨')) {
          totalDebugLogs++;
          
          // 检查是否被注释
          if (line.trim().startsWith('//')) {
            commentedDebugLogs++;
          } else {
            stdout.writeln('  ⚠️  未注释的debug日志: $filePath:${i+1}');
            stdout.writeln('     ${line.trim()}');
          }
        }
      }
    }
  }
  
  stdout.writeln('  📊 Debug日志统计:');
  stdout.writeln('     总计: $totalDebugLogs');
  stdout.writeln('     已注释: $commentedDebugLogs');
  stdout.writeln('     注释率: ${totalDebugLogs > 0 ? (commentedDebugLogs / totalDebugLogs * 100).toStringAsFixed(1) : 0}%');
  stdout.writeln('');
}

/// 检查关键日志保留情况
Future<void> checkCriticalLogsRetained() async {
  stdout.writeln('🔍 检查关键日志保留情况...');
  
  final filesToCheck = [
    'lib/services/auth_service.dart',
    'lib/services/connection_manager.dart',
    'lib/services/backend_service.dart',
  ];
  
  final criticalLogPatterns = [
    'logService.error(',
    'logService.warning(',
    'logService.info(',
    '.error(',
    '.warning(',
    '.info(',
  ];
  
  int totalCriticalLogs = 0;
  
  for (final filePath in filesToCheck) {
    final file = File(filePath);
    if (await file.exists()) {
      final content = await file.readAsString();
      final lines = content.split('\n');
      
      int fileLogCount = 0;
      for (final line in lines) {
        for (final pattern in criticalLogPatterns) {
          if (line.contains(pattern) && !line.trim().startsWith('//')) {
            fileLogCount++;
            totalCriticalLogs++;
            break;
          }
        }
      }
      
      if (fileLogCount > 0) {
        stdout.writeln('  ✅ $filePath: $fileLogCount 个关键日志');
      }
    }
  }

  stdout.writeln('  📊 关键日志总计: $totalCriticalLogs');
  stdout.writeln('');
}
