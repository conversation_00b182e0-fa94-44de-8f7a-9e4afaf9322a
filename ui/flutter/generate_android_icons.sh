#!/bin/bash

# Android 图标生成脚本
# 为 Panabit Client 生成所有必要的 Android 图标尺寸

echo "开始生成 Android 图标..."

# 检查源图标文件是否存在
if [ ! -f "assets/icons/panabit_icon.png" ]; then
    echo "错误：源图标文件 assets/icons/panabit_icon.png 不存在"
    exit 1
fi

# 确保 flutter_launcher_icons 插件已安装
echo "安装依赖..."
flutter pub get

# 运行图标生成器
echo "生成 Android 图标..."
dart run flutter_launcher_icons:main

# 验证生成的图标
echo "验证生成的图标文件..."
ICON_DIRS=("mipmap-mdpi" "mipmap-hdpi" "mipmap-xhdpi" "mipmap-xxhdpi" "mipmap-xxxhdpi")
MISSING_ICONS=()

for dir in "${ICON_DIRS[@]}"; do
    icon_path="android/app/src/main/res/$dir/ic_launcher.png"
    if [ ! -f "$icon_path" ]; then
        MISSING_ICONS+=("$icon_path")
    else
        echo "✓ $icon_path 存在"
    fi
done

# 验证图标尺寸
echo "验证图标尺寸..."
EXPECTED_SIZES=("48x48" "72x72" "96x96" "144x144" "192x192")
for i in "${!ICON_DIRS[@]}"; do
    dir="${ICON_DIRS[$i]}"
    expected_size="${EXPECTED_SIZES[$i]}"
    icon_path="android/app/src/main/res/$dir/ic_launcher.png"

    if [ -f "$icon_path" ]; then
        actual_size=$(file "$icon_path" | grep -o '[0-9]\+ x [0-9]\+' | head -1 | tr -d ' ')
        expected_size_normalized=$(echo "$expected_size" | tr -d ' ')
        if [ "$actual_size" = "$expected_size_normalized" ]; then
            echo "✓ $dir/ic_launcher.png 尺寸正确: $actual_size"
        else
            echo "❌ $dir/ic_launcher.png 尺寸错误: 期望 $expected_size_normalized, 实际 $actual_size"
        fi
    fi
done

# 验证 Adaptive Icon 前景图标
echo "验证 Adaptive Icon 前景图标..."
FOREGROUND_DIRS=("drawable-mdpi" "drawable-hdpi" "drawable-xhdpi" "drawable-xxhdpi" "drawable-xxxhdpi")
for dir in "${FOREGROUND_DIRS[@]}"; do
    foreground_path="android/app/src/main/res/$dir/ic_launcher_foreground.png"
    if [ -f "$foreground_path" ]; then
        echo "✓ $foreground_path 存在"
    else
        echo "❌ $foreground_path 缺失"
    fi
done

if [ ${#MISSING_ICONS[@]} -eq 0 ]; then
    echo "✅ 所有 Android 图标已成功生成！"
else
    echo "❌ 以下图标文件缺失："
    for missing in "${MISSING_ICONS[@]}"; do
        echo "  - $missing"
    done
fi

# 检查 Adaptive Icon 配置
echo "检查 Adaptive Icon 配置..."
if [ -f "android/app/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" ]; then
    echo "✓ Adaptive Icon 配置存在"
else
    echo "❌ Adaptive Icon 配置缺失"
fi

# 检查背景颜色配置
echo "检查背景颜色配置..."
if [ -f "android/app/src/main/res/values/colors.xml" ]; then
    if grep -q "#FFFFFF" "android/app/src/main/res/values/colors.xml"; then
        echo "✓ 白色背景颜色配置正确"
    else
        echo "❌ 背景颜色配置不正确"
    fi
else
    echo "❌ colors.xml 文件缺失"
fi

echo "Android 图标配置完成！"
