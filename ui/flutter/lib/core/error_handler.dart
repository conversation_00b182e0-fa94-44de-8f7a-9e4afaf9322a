/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      error_handler.dart
///
/// DESCRIPTION :    统一错误处理机制，提供API错误、网络错误、认证错误等
///                  各种错误的统一处理和用户友好的错误提示
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'package:flutter/material.dart';
import '../utils/api_exception.dart';
import '../services/log_service.dart';
import '../services/notification_service.dart';

import '../generated/l10n/app_localizations.dart';
import '../utils/constants.dart';
import 'dependency_injection.dart';

/// _ErrorInfo
///
/// PURPOSE:
///     错误信息数据类，用于封装用户消息和日志消息
///
/// FEATURES:
///     - 用户消息：面向用户的友好错误提示
///     - 日志消息：面向开发者的详细错误信息
///
/// USAGE:
///     final errorInfo = _ErrorInfo(
///       userMessage: 'Network connection failed',
///       logMessage: 'HTTP 500: Internal Server Error'
///     );
class _ErrorInfo {
  /// 用户友好的错误消息
  final String userMessage;

  /// 详细的日志错误消息
  final String logMessage;

  /// 构造函数
  ///
  /// DESCRIPTION:
  ///     创建错误信息实例
  ///
  /// PARAMETERS:
  ///     userMessage - 用户友好的错误消息
  ///     logMessage - 详细的日志错误消息
  const _ErrorInfo({
    required this.userMessage,
    required this.logMessage,
  });
}

/// ErrorHandler
///
/// PURPOSE:
///     统一错误处理器，提供应用程序中所有类型错误的统一处理机制，
///     包括API错误、网络错误、认证错误等，并提供用户友好的错误提示
///
/// FEATURES:
///     - 统一错误处理：支持多种错误类型的统一处理
///     - 本地化支持：根据用户语言设置显示相应的错误消息
///     - 用户友好提示：将技术错误转换为用户可理解的消息
///     - 日志记录：详细记录错误信息用于调试和分析
///     - 通知集成：与通知服务集成，统一错误显示方式
///     - 对话框支持：提供确认对话框等交互功能
///
/// USAGE:
///     // 处理API错误
///     ErrorHandler.handleApiError(error, context: 'LoginService');
///
///     // 处理网络错误
///     ErrorHandler.handleNetworkError(error);
///
///     // 显示成功消息
///     ErrorHandler.showSuccess('Operation completed successfully');
///
///     // 显示确认对话框
///     final confirmed = await ErrorHandler.showConfirmDialog(context,
///       title: 'Confirm', content: 'Are you sure?');
class ErrorHandler {
  // 私有构造函数，防止实例化
  ErrorHandler._();

  /// _getLogService
  ///
  /// DESCRIPTION:
  ///     安全获取日志服务实例，处理服务获取失败的情况
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     LogService? - 日志服务实例，获取失败时返回null
  static LogService? _getLogService() {
    try {
      return serviceLocator<LogService>();
    } catch (e) {
      return null;
    }
  }

  /// _getNotificationService
  ///
  /// DESCRIPTION:
  ///     安全获取通知服务实例，处理服务获取失败的情况
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     NotificationService? - 通知服务实例，获取失败时返回null
  static NotificationService? _getNotificationService() {
    try {
      return serviceLocator<NotificationService>();
    } catch (e) {
      return null;
    }
  }

  /// _getLocalizations
  ///
  /// DESCRIPTION:
  ///     获取本地化文本的辅助方法，安全处理获取失败的情况
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     AppLocalizations? - 本地化实例，获取失败时返回null
  static AppLocalizations? _getLocalizations() {
    try {
      final context = serviceLocator<GlobalKey<NavigatorState>>().currentContext;
      if (context != null) {
        return AppLocalizations.of(context);
      }
    } catch (e) {
      final logService = _getLogService();
      logService?.debug(kLogModuleErrorHandler, 'Failed to get localizations: $e');
    }
    return null;
  }

  /// handleApiError
  ///
  /// DESCRIPTION:
  ///     处理API错误，将技术错误转换为用户友好的消息并记录日志
  ///
  /// PARAMETERS:
  ///     error - 错误对象，可以是ApiException或其他类型
  ///     context - 错误发生的上下文，用于日志记录
  ///
  /// RETURNS:
  ///     void
  static void handleApiError(dynamic error, {String? context}) {
    final errorInfo = _processApiError(error);

    // 记录日志
    final logService = _getLogService();
    logService?.error(context ?? kLogModuleErrorHandler, errorInfo.logMessage);

    // 显示用户友好的错误信息
    final notificationService = _getNotificationService();
    notificationService?.showErrorNotification(errorInfo.userMessage);
  }

  /// _processApiError
  ///
  /// DESCRIPTION:
  ///     处理API错误，提取用户消息和日志消息
  ///
  /// PARAMETERS:
  ///     error - 错误对象
  ///
  /// RETURNS:
  ///     _ErrorInfo - 包含用户消息和日志消息的错误信息
  static _ErrorInfo _processApiError(dynamic error) {
    if (error is ApiException) {
      return _ErrorInfo(
        userMessage: _getApiErrorMessage(error),
        logMessage: 'API Error [${error.code}]: ${error.message}',
      );
    } else {
      final l10n = _getLocalizations();
      return _ErrorInfo(
        userMessage: l10n?.connectionFailedGeneric ?? 'Operation failed, please try again later',
        logMessage: 'Unknown error: ${error.toString()}',
      );
    }
  }

  /// handleNetworkError
  ///
  /// DESCRIPTION:
  ///     处理网络错误，提供网络连接相关的用户友好提示
  ///
  /// PARAMETERS:
  ///     error - 网络错误对象
  ///     context - 错误发生的上下文，用于日志记录
  ///
  /// RETURNS:
  ///     void
  static void handleNetworkError(dynamic error, {String? context}) {
    final l10n = _getLocalizations();
    final userMessage = l10n?.networkConnectionFailed ?? 'Network connection failed, please check network settings';
    final logMessage = 'Network error: ${error.toString()}';

    _logError(context, logMessage);
    _showErrorNotification(userMessage);
  }

  /// handleWebSocketError
  ///
  /// DESCRIPTION:
  ///     处理WebSocket错误，提供实时连接相关的用户友好提示
  ///
  /// PARAMETERS:
  ///     error - WebSocket错误对象
  ///     context - 错误发生的上下文，用于日志记录
  ///
  /// RETURNS:
  ///     void
  static void handleWebSocketError(dynamic error, {String? context}) {
    final l10n = _getLocalizations();
    final userMessage = l10n?.realtimeConnectionInterrupted ?? 'Real-time connection interrupted, attempting to reconnect...';
    final logMessage = 'WebSocket error: ${error.toString()}';

    _logWarning(context, logMessage);
    _showWarningNotification(userMessage);
  }

  /// handleAuthError
  ///
  /// DESCRIPTION:
  ///     处理认证错误，提供认证相关的用户友好提示
  ///
  /// PARAMETERS:
  ///     error - 认证错误对象
  ///     context - 错误发生的上下文，用于日志记录
  ///
  /// RETURNS:
  ///     void
  static void handleAuthError(dynamic error, {String? context}) {
    final l10n = _getLocalizations();
    final userMessage = l10n?.authenticationFailed ?? 'Authentication failed, please login again';
    final logMessage = 'Authentication error: ${error.toString()}';

    _logError(context, logMessage);
    _showErrorNotification(userMessage);
  }

  /// handleGenericError
  ///
  /// DESCRIPTION:
  ///     处理一般错误，提供通用的错误处理机制
  ///
  /// PARAMETERS:
  ///     error - 错误对象
  ///     context - 错误发生的上下文，用于日志记录
  ///     userMessage - 自定义用户消息，如果不提供则使用默认消息
  ///
  /// RETURNS:
  ///     void
  static void handleGenericError(dynamic error, {String? context, String? userMessage}) {
    final l10n = _getLocalizations();
    final message = userMessage ?? l10n?.operationFailedRetry ?? 'Operation failed, please try again later';
    final logMessage = 'Generic error: ${error.toString()}';

    _logError(context, logMessage);
    _showErrorNotification(message);
  }

  // ==========================================================================
  // 私有辅助方法
  // ==========================================================================

  /// _logError
  ///
  /// DESCRIPTION:
  ///     安全记录错误级别日志
  ///
  /// PARAMETERS:
  ///     context - 日志上下文
  ///     message - 日志消息
  ///
  /// RETURNS:
  ///     void
  static void _logError(String? context, String message) {
    final logService = _getLogService();
    logService?.error(context ?? kLogModuleErrorHandler, message);
  }

  /// _logWarning
  ///
  /// DESCRIPTION:
  ///     安全记录警告级别日志
  ///
  /// PARAMETERS:
  ///     context - 日志上下文
  ///     message - 日志消息
  ///
  /// RETURNS:
  ///     void
  static void _logWarning(String? context, String message) {
    final logService = _getLogService();
    logService?.warning(context ?? kLogModuleErrorHandler, message);
  }

  /// _logInfo
  ///
  /// DESCRIPTION:
  ///     安全记录信息级别日志
  ///
  /// PARAMETERS:
  ///     context - 日志上下文
  ///     message - 日志消息
  ///
  /// RETURNS:
  ///     void
  static void _logInfo(String? context, String message) {
    final logService = _getLogService();
    logService?.info(context ?? kLogModuleErrorHandler, message);
  }

  /// _showErrorNotification
  ///
  /// DESCRIPTION:
  ///     安全显示错误通知
  ///
  /// PARAMETERS:
  ///     message - 错误消息
  ///
  /// RETURNS:
  ///     void
  static void _showErrorNotification(String message) {
    final notificationService = _getNotificationService();
    notificationService?.showErrorNotification(message);
  }

  /// _showWarningNotification
  ///
  /// DESCRIPTION:
  ///     安全显示警告通知
  ///
  /// PARAMETERS:
  ///     message - 警告消息
  ///
  /// RETURNS:
  ///     void
  static void _showWarningNotification(String message) {
    final notificationService = _getNotificationService();
    notificationService?.showWarningNotification(message);
  }

  /// _showSuccessNotification
  ///
  /// DESCRIPTION:
  ///     安全显示成功通知
  ///
  /// PARAMETERS:
  ///     message - 成功消息
  ///
  /// RETURNS:
  ///     void
  static void _showSuccessNotification(String message) {
    final notificationService = _getNotificationService();
    notificationService?.showSuccessNotification(message);
  }

  /// _showInfoNotification
  ///
  /// DESCRIPTION:
  ///     安全显示信息通知
  ///
  /// PARAMETERS:
  ///     message - 信息消息
  ///
  /// RETURNS:
  ///     void
  static void _showInfoNotification(String message) {
    final notificationService = _getNotificationService();
    notificationService?.showInfoNotification(message);
  }

  /// showSuccess
  ///
  /// DESCRIPTION:
  ///     显示成功消息，同时记录日志和显示通知
  ///
  /// PARAMETERS:
  ///     message - 成功消息
  ///     context - 日志上下文，可选
  ///
  /// RETURNS:
  ///     void
  static void showSuccess(String message, {String? context}) {
    if (context != null) {
      _logInfo(context, message);
    }
    _showSuccessNotification(message);
  }

  /// showInfo
  ///
  /// DESCRIPTION:
  ///     显示信息消息，同时记录日志和显示通知
  ///
  /// PARAMETERS:
  ///     message - 信息消息
  ///     context - 日志上下文，可选
  ///
  /// RETURNS:
  ///     void
  static void showInfo(String message, {String? context}) {
    if (context != null) {
      _logInfo(context, message);
    }
    _showInfoNotification(message);
  }

  /// showWarning
  ///
  /// DESCRIPTION:
  ///     显示警告消息，同时记录日志和显示通知
  ///
  /// PARAMETERS:
  ///     message - 警告消息
  ///     context - 日志上下文，可选
  ///
  /// RETURNS:
  ///     void
  static void showWarning(String message, {String? context}) {
    if (context != null) {
      _logWarning(context, message);
    }
    _showWarningNotification(message);
  }

  /// _getApiErrorMessage
  ///
  /// DESCRIPTION:
  ///     获取API错误的用户友好消息，根据错误类型返回相应的本地化消息
  ///
  /// PARAMETERS:
  ///     error - API异常对象
  ///
  /// RETURNS:
  ///     String - 用户友好的错误消息
  static String _getApiErrorMessage(ApiException error) {
    final l10n = _getLocalizations();

    switch (error.type) {
      case 'timeout':
        return l10n?.connectionTimeoutDetailed ?? 'Request timeout, please check network connection';
      case 'network':
        return l10n?.connectionFailedGeneric ?? 'Network connection failed, please check network settings';
      case 'auth':
        return l10n?.connectionFailedGeneric ?? 'Authentication failed, please login again';
      case 'permission':
        return l10n?.connectionFailedGeneric ?? 'Insufficient permissions, please contact administrator';
      case 'validation':
        return l10n?.connectionFailedGeneric ?? 'Input information is incorrect, please check and try again';
      case 'server':
        return l10n?.connectionFailedGeneric ?? 'Server error, please try again later';
      default:
        return error.message.isNotEmpty ? error.message : (l10n?.connectionFailedGeneric ?? 'Operation failed, please try again later');
    }
  }

  /// showConfirmDialog
  ///
  /// DESCRIPTION:
  ///     显示确认对话框，用于需要用户确认的操作
  ///
  /// PARAMETERS:
  ///     context - 构建上下文
  ///     title - 对话框标题
  ///     content - 对话框内容
  ///     confirmText - 确认按钮文本，默认为'确认'
  ///     cancelText - 取消按钮文本，默认为'取消'
  ///
  /// RETURNS:
  ///     Future<bool> - 用户选择结果，true表示确认，false表示取消
  static Future<bool> showConfirmDialog(
    BuildContext context, {
    required String title,
    required String content,
    String? confirmText,
    String? cancelText,
  }) async {
    final l10n = _getLocalizations();
    final effectiveConfirmText = confirmText ?? l10n?.confirm ?? 'Confirm';
    final effectiveCancelText = cancelText ?? l10n?.cancel ?? 'Cancel';

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(effectiveCancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(effectiveConfirmText),
          ),
        ],
      ),
    );
    return result ?? false;
  }
}
