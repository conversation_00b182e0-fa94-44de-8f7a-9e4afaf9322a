/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      dependency_injection.dart
///
/// DESCRIPTION :    依赖注入容器配置，使用GetIt管理应用中所有服务的
///                  生命周期和依赖关系
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create


import 'package:get_it/get_it.dart';
import 'package:flutter/material.dart';

import '../services/api_service.dart';
import '../services/websocket_service.dart';
import '../services/auth_service.dart';
import '../services/backend_service.dart';
import '../services/log_service.dart';
import '../services/notification_service.dart';
import '../services/language_service.dart';
import '../services/app_lifecycle_service.dart';
import '../services/connection_manager.dart';
import '../services/data_manager.dart';
import '../services/auto_start_service.dart';
import '../services/routing_settings_service.dart';
import '../services/reconnect_service.dart';
import '../services/font_service.dart';
import '../services/window_service.dart';
import '../services/platform/platform_service_factory.dart';
import '../services/platform/cross_platform_api_service.dart';
import '../services/platform/cross_platform_storage_service.dart';
import '../services/platform/cross_platform_log_service.dart';
import '../services/platform/cross_platform_backend_service.dart';
import '../services/tray_service.dart';
import '../core/app_state.dart';
import '../models/routing_settings.dart';
import '../utils/constants.dart';

/// 全局服务定位器实例
///
/// DESCRIPTION:
///     GetIt依赖注入容器的全局实例，用于注册和获取应用中的所有服务
final GetIt serviceLocator = GetIt.instance;

/// DependencyInjection
///
/// PURPOSE:
///     依赖注入容器配置类，负责管理应用中所有服务的注册、生命周期和依赖关系
///
/// FEATURES:
///     - 服务注册管理：按类别组织服务注册
///     - 依赖关系管理：确保服务间依赖关系正确
///     - 生命周期管理：管理单例和瞬态服务
///     - 错误处理：提供服务注册和获取的错误处理
///     - 资源清理：提供容器重置和资源清理功能
///
/// USAGE:
///     // 初始化依赖注入容器
///     await DependencyInjection.init();
///
///     // 获取服务实例
///     final logService = serviceLocator<LogService>();
///
///     // 清理容器
///     DependencyInjection.dispose();
class DependencyInjection {
  /// init
  ///
  /// DESCRIPTION:
  ///     初始化依赖注入容器，按顺序注册所有应用服务
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 服务注册失败时抛出异常
  static Future<void> init() async {
    try {
      // 按依赖顺序注册服务
      await _registerCoreServices();
      await _registerInfrastructureServices();
      await _registerBusinessServices();
      await _registerStateManagementServices();
    } catch (e) {
      throw Exception('Failed to initialize dependency injection: $e');
    }
  }

  /// _registerCoreServices
  ///
  /// DESCRIPTION:
  ///     注册核心基础服务，这些服务被其他服务依赖
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _registerCoreServices() async {
    // 跨平台日志服务 - 最基础的服务，其他服务都依赖它（立即初始化）
    final crossPlatformLogService = PlatformServiceFactory.createLogService();
    try {
      await crossPlatformLogService.initialize();
    } catch (e) {
      // 日志服务初始化失败不应该阻止应用启动
    }
    serviceLocator.registerSingleton<CrossPlatformLogService>(crossPlatformLogService);

    // 传统日志服务 - 为了向后兼容，使用已初始化的跨平台日志服务
    final logService = LogService.production();
    logService.platformLogService = crossPlatformLogService;
    serviceLocator.registerSingleton<LogService>(logService);

    // 跨平台存储服务 - 立即初始化，确保在其他服务使用前可用
    final storageService = PlatformServiceFactory.createStorageService();
    try {
      // 初始化存储服务
      await storageService.initialize();
      final logService = serviceLocator<LogService>();
      logService.info('DependencyInjection', 'Cross-platform storage service initialized successfully');
    } catch (e) {
      try {
        final logService = serviceLocator<LogService>();
        logService.error('DependencyInjection', 'Failed to initialize cross-platform storage service: $e');
      } catch (_) {
        // 如果日志服务不可用，静默处理
      }
      // 存储服务初始化失败不应该阻止应用启动
    }
    serviceLocator.registerSingleton<CrossPlatformStorageService>(storageService);

    // 跨平台API服务 - 网络通信基础服务
    serviceLocator.registerLazySingleton<CrossPlatformApiService>(
      () => PlatformServiceFactory.createApiService(),
    );

    // 跨平台后端服务 - 后端管理基础服务
    serviceLocator.registerLazySingleton<CrossPlatformBackendService>(
      () => PlatformServiceFactory.createBackendService(),
    );

    // 语言服务 - 需要异步初始化
    final languageService = LanguageService(logService: serviceLocator<LogService>());
    await languageService.initialize();
    serviceLocator.registerSingleton<LanguageService>(languageService);

    // 字体服务 - UI相关的基础服务
    serviceLocator.registerLazySingleton<FontService>(
      () => FontService(),
    );
  }

  /// _registerInfrastructureServices
  ///
  /// DESCRIPTION:
  ///     注册基础设施服务，包括网络、通知、窗口等，使用跨平台服务架构
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _registerInfrastructureServices() async {
    // API服务 - 网络通信基础服务（向后兼容包装器）
    serviceLocator.registerLazySingleton<ApiService>(
      () => ApiService(baseUrl: kApiBaseUrl)..logService = serviceLocator<LogService>(),
    );

    // 通知服务 - 用户界面通知
    serviceLocator.registerLazySingleton<NotificationService>(
      () => NotificationService(),
    );

    // 平台特定服务注册（仅在需要时注册）
    if (PlatformServiceFactory.isDesktopPlatform) {
      // 窗口服务 - 仅桌面平台需要
      serviceLocator.registerLazySingleton<WindowService>(
        () => WindowService(logService: serviceLocator<LogService>()),
      );

      // 传统后端服务 - 仅桌面平台需要（向后兼容）
      serviceLocator.registerLazySingleton<BackendService>(
        () => BackendService(logService: serviceLocator<LogService>()),
      );

      // 系统托盘服务 - 仅Windows平台需要
      serviceLocator.registerLazySingleton<TrayService>(
        () => TrayService(logService: serviceLocator<LogService>()),
      );
    }

    // WebSocket服务 - 实时通信
    serviceLocator.registerLazySingleton<WebSocketService>(
      () => WebSocketService(
        url: kWebSocketUrl,
        apiService: serviceLocator<ApiService>(),
        notificationService: serviceLocator<NotificationService>(),
      ),
    );
  }

  /// _registerBusinessServices
  ///
  /// DESCRIPTION:
  ///     注册业务逻辑服务，包括认证、路由设置等
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _registerBusinessServices() async {
    // 认证服务 - 用户认证和授权
    serviceLocator.registerLazySingleton<AuthService>(
      () => AuthService(apiService: serviceLocator<ApiService>()),
    );

    // 路由设置服务 - 路由配置管理
    serviceLocator.registerLazySingleton<RoutingSettingsService>(
      () => RoutingSettingsService(),
    );

    // 重连服务 - 网络重连管理
    serviceLocator.registerLazySingleton<ReconnectService>(
      () => ReconnectService(
        connectionManager: serviceLocator<ConnectionManager>(),
        logService: serviceLocator<LogService>(),
        appState: serviceLocator<AppState>(),
      ),
    );

    // 自动启动服务 - 应用自动启动管理
    serviceLocator.registerLazySingleton<AutoStartService>(
      () => AutoStartService(
        logService: serviceLocator<LogService>(),
        authService: serviceLocator<AuthService>(),
      ),
    );

    // 应用生命周期服务 - 应用生命周期管理
    serviceLocator.registerLazySingleton<AppLifecycleService>(
      () => AppLifecycleService(
        apiService: serviceLocator<ApiService>(),
        logService: serviceLocator<LogService>(),
      ),
    );
  }

  /// _registerStateManagementServices
  ///
  /// DESCRIPTION:
  ///     注册状态管理服务，包括应用状态、连接管理等
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _registerStateManagementServices() async {
    // 应用状态管理 - 全局应用状态（需要异步初始化存储服务）
    final appState = AppState();
    await appState.initializeStorage(serviceLocator<CrossPlatformStorageService>());
    serviceLocator.registerSingleton<AppState>(appState);

    // 路由设置模型 - 路由配置状态管理（单例，确保全局状态一致）
    serviceLocator.registerLazySingleton<RoutingSettingsModel>(() => RoutingSettingsModel());

    // 连接管理器 - VPN连接管理
    serviceLocator.registerLazySingleton<ConnectionManager>(() => ConnectionManager());

    // 数据管理器 - 数据缓存和管理
    serviceLocator.registerLazySingleton<DataManager>(() => DataManager());
  }

  /// registerNavigatorKey
  ///
  /// DESCRIPTION:
  ///     注册全局导航键到依赖注入容器，用于全局导航操作
  ///
  /// PARAMETERS:
  ///     navigatorKey - 全局导航键实例
  ///
  /// RETURNS:
  ///     void
  static void registerNavigatorKey(GlobalKey<NavigatorState> navigatorKey) {
    try {
      // 检查是否已经注册，避免重复注册
      if (!serviceLocator.isRegistered<GlobalKey<NavigatorState>>()) {
        serviceLocator.registerSingleton<GlobalKey<NavigatorState>>(navigatorKey);
      }
    } catch (e) {
      throw Exception('Failed to register navigator key: $e');
    }
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     清理依赖注入容器，重置所有注册的服务实例
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  static void dispose() {
    try {
      serviceLocator.reset();
    } catch (e) {
      // 记录错误但不抛出异常，确保应用能够正常关闭
      // 静默处理容器清理错误
    }
  }
}
