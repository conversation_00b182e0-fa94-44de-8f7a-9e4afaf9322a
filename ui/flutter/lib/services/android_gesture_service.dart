/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// ******************************************************************************
/// FILE NAME :      android_gesture_service.dart
///
/// DESCRIPTION :    Android平台特定的手势管理服务，负责处理侧边栏滑出、
///                  关闭和应用最小化等手势功能，确保与iOS版本保持一致的用户体验
///
/// AUTHOR :         wei
///
/// HISTORY :        17/07/2025 create

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../core/dependency_injection.dart';
import '../services/log_service.dart';


/// AndroidGestureService
///
/// PURPOSE:
///     Android平台特定的手势管理服务，提供侧边栏和应用最小化手势功能
///
/// FEATURES:
///     - 侧边栏手势：向右滑动打开侧边栏，向左滑动关闭侧边栏
///     - 应用最小化：当没有侧边栏时，滑动手势最小化应用到后台
///     - 系统手势覆盖：禁用Android默认的滑动返回手势
///     - 平台检测：只在Android平台生效，不影响其他平台
///
/// USAGE:
///     final gestureService = AndroidGestureService();
///     gestureService.initialize(scaffoldKey, onDrawerToggle);
class AndroidGestureService {
  static const String _logModule = 'AndroidGestureService';
  
  /// 日志服务实例
  late final LogService _logService;
  
  /// 是否已初始化
  bool _isInitialized = false;
  
  /// Scaffold键，用于控制侧边栏
  GlobalKey<ScaffoldState>? _scaffoldKey;
  
  /// 侧边栏切换回调
  VoidCallback? _onDrawerToggle;
  
  /// 手势识别阈值配置
  static const double _verticalSwipeThreshold = 100.0;  // 垂直滑动最大允许距离
  static const double _velocityThreshold = 500.0;       // 滑动速度阈值
  static const double _edgeSwipeZone = 50.0;            // 边缘滑动区域宽度
  
  /// 构造函数
  AndroidGestureService() {
    _logService = serviceLocator<LogService>();
  }
  
  /// initialize
  ///
  /// DESCRIPTION:
  ///     初始化Android手势服务
  ///
  /// PARAMETERS:
  ///     scaffoldKey - Scaffold键，用于控制侧边栏
  ///     onDrawerToggle - 侧边栏切换回调（可选）
  ///
  /// RETURNS:
  ///     void
  void initialize({
    required GlobalKey<ScaffoldState> scaffoldKey,
    VoidCallback? onDrawerToggle,
  }) {
    if (!Platform.isAndroid) {
      _logService.debug(_logModule, 'Not Android platform, skipping gesture service initialization');
      return;
    }
    
    _scaffoldKey = scaffoldKey;
    _onDrawerToggle = onDrawerToggle;
    _isInitialized = true;
    
    _logService.info(_logModule, 'Android gesture service initialized successfully');
  }
  
  /// handlePanUpdate
  ///
  /// DESCRIPTION:
  ///     处理滑动更新事件，识别手势方向和意图
  ///
  /// PARAMETERS:
  ///     details - 滑动手势详情
  ///     screenSize - 屏幕尺寸
  ///
  /// RETURNS:
  ///     bool - 是否处理了手势事件
  bool handlePanUpdate(DragUpdateDetails details, Size screenSize) {
    if (!_isInitialized || !Platform.isAndroid) {
      return false;
    }
    
    final delta = details.delta;
    final globalPosition = details.globalPosition;
    
    // 检查是否为有效的水平滑动手势
    if (!_isValidHorizontalSwipe(delta)) {
      return false;
    }
    
    // 向右滑动 - 尝试打开侧边栏
    if (delta.dx > 0) {
      return _handleRightSwipe(globalPosition, screenSize);
    }
    
    // 向左滑动 - 尝试关闭侧边栏或最小化应用
    if (delta.dx < 0) {
      return _handleLeftSwipe(globalPosition, screenSize);
    }
    
    return false;
  }
  
  /// handlePanEnd
  ///
  /// DESCRIPTION:
  ///     处理滑动结束事件，根据滑动速度执行相应操作
  ///
  /// PARAMETERS:
  ///     details - 滑动结束详情
  ///     screenSize - 屏幕尺寸
  ///
  /// RETURNS:
  ///     bool - 是否处理了手势事件
  bool handlePanEnd(DragEndDetails details, Size screenSize) {
    if (!_isInitialized || !Platform.isAndroid) {
      return false;
    }
    
    final velocity = details.velocity.pixelsPerSecond;
    
    // 检查是否为快速水平滑动
    if (velocity.dx.abs() > _velocityThreshold && 
        velocity.dy.abs() < _velocityThreshold) {
      
      // 快速向右滑动 - 打开侧边栏或最小化应用
      if (velocity.dx > 0) {
        return _handleRightSwipeAction();
      }
      
      // 快速向左滑动 - 关闭侧边栏或最小化应用
      if (velocity.dx < 0) {
        return _handleLeftSwipeAction();
      }
    }
    
    return false;
  }
  
  /// 检查是否为有效的水平滑动手势
  bool _isValidHorizontalSwipe(Offset delta) {
    return delta.dx.abs() > delta.dy.abs() && // 水平滑动为主
           delta.dx.abs() > 2.0 &&             // 最小滑动距离
           delta.dy.abs() < _verticalSwipeThreshold; // 垂直偏移不能太大
  }
  
  /// 处理向右滑动
  bool _handleRightSwipe(Offset globalPosition, Size screenSize) {
    // 只在屏幕左边缘区域响应向右滑动
    if (globalPosition.dx <= _edgeSwipeZone) {
      // 尝试打开侧边栏
      if (_openDrawer()) {
        return true;
      }
      // 如果没有侧边栏或打开失败，最小化应用
      return _minimizeApp();
    }
    return false;
  }
  
  /// 处理向左滑动
  bool _handleLeftSwipe(Offset globalPosition, Size screenSize) {
    // 如果侧边栏已打开，关闭它
    if (_isDrawerOpen()) {
      return _closeDrawer();
    }
    
    // 如果没有侧边栏，在屏幕右边缘区域响应向左滑动进行应用最小化
    if (globalPosition.dx >= screenSize.width - _edgeSwipeZone) {
      return _minimizeApp();
    }
    
    return false;
  }
  
  /// 处理向右滑动动作
  bool _handleRightSwipeAction() {
    // 尝试打开侧边栏
    if (_openDrawer()) {
      return true;
    }
    // 如果没有侧边栏或打开失败，最小化应用
    return _minimizeApp();
  }

  /// 处理向左滑动动作
  bool _handleLeftSwipeAction() {
    if (_isDrawerOpen()) {
      return _closeDrawer();
    } else {
      return _minimizeApp();
    }
  }
  
  /// 打开侧边栏
  bool _openDrawer() {
    try {
      if (_scaffoldKey?.currentState?.hasDrawer == true) {
        _scaffoldKey!.currentState!.openDrawer();
        _onDrawerToggle?.call();
        _logService.debug(_logModule, 'Drawer opened via gesture');
        return true;
      }
    } catch (e) {
      _logService.error(_logModule, 'Failed to open drawer: $e');
    }
    return false;
  }
  
  /// 关闭侧边栏
  bool _closeDrawer() {
    try {
      if (_isDrawerOpen()) {
        Navigator.of(_scaffoldKey!.currentContext!).pop();
        _onDrawerToggle?.call();
        _logService.debug(_logModule, 'Drawer closed via gesture');
        return true;
      }
    } catch (e) {
      _logService.error(_logModule, 'Failed to close drawer: $e');
    }
    return false;
  }
  
  /// 检查侧边栏是否已打开
  bool _isDrawerOpen() {
    return _scaffoldKey?.currentState?.isDrawerOpen == true;
  }
  
  /// 最小化应用到后台
  bool _minimizeApp() {
    try {
      // 在Android上，使用SystemNavigator.pop()会退出应用
      // 要将应用最小化到后台，我们需要使用平台特定的方法
      if (Platform.isAndroid) {
        // 使用MethodChannel调用Android原生方法将应用移到后台
        _minimizeAppToBackground();
        _logService.debug(_logModule, 'App minimized to background via gesture');
        return true;
      } else {
        // 非Android平台使用默认行为
        SystemNavigator.pop();
        _logService.debug(_logModule, 'App closed via gesture (non-Android platform)');
        return true;
      }
    } catch (e) {
      _logService.error(_logModule, 'Failed to minimize app: $e');
      return false;
    }
  }

  /// 将Android应用最小化到后台
  void _minimizeAppToBackground() {
    // 使用MethodChannel调用Android原生方法
    const platform = MethodChannel('panabit_client/system');
    platform.invokeMethod('minimizeApp').catchError((error) {
      _logService.error(_logModule, 'Failed to call native minimize method: $error');
      // 如果原生方法调用失败，记录错误但不退出应用
    });
  }
  
  /// 清理资源
  void dispose() {
    _scaffoldKey = null;
    _onDrawerToggle = null;
    _isInitialized = false;
    _logService.debug(_logModule, 'Android gesture service disposed');
  }
}
