// /**
//  * AUTHOR: wei
//  * HISTORY: 27/06/2025 - Initial implementation of cross-platform log service interface
//  */

import 'dart:async';

/// LogLevel
///
/// PURPOSE:
///     Enumeration of log levels for filtering and categorization.
enum LogLevel {
  debug,
  info,
  warning,
  error,
  critical
}

/// CrossPlatformLogService
///
/// PURPOSE:
///     Abstract interface for cross-platform logging operations.
///     Provides unified logging API for both file-based (Windows/Linux) and 
///     platform-native (iOS/macOS OSLog) implementations.
///
/// FEATURES:
///     - Multiple log levels with filtering
///     - Structured logging with JSON format
///     - Module-based categorization
///     - Automatic log rotation and cleanup
///     - Platform-specific optimizations
///
/// USAGE:
///     Implemented by platform-specific services:
///     - FileLogService for Windows/Linux
///     - PlatformLogService for iOS/macOS (OSLog integration)
abstract class CrossPlatformLogService {
  
  // ============================================================================
  // INITIALIZATION AND LIFECYCLE
  // ============================================================================
  
  /// initialize
  ///
  /// DESCRIPTION:
  ///     Initialize logging service and create log directories/subsystems.
  ///
  /// RETURNS:
  ///     Future<void> - completion of initialization
  Future<void> initialize();
  
  /// dispose
  ///
  /// DESCRIPTION:
  ///     Clean up logging resources and flush pending logs.
  ///
  /// RETURNS:
  ///     Future<void> - completion of cleanup
  Future<void> dispose();
  
  // ============================================================================
  // BASIC LOGGING OPERATIONS
  // ============================================================================
  
  /// debug
  ///
  /// DESCRIPTION:
  ///     Log debug message for development and troubleshooting.
  ///
  /// PARAMETERS:
  ///     module - source module name
  ///     message - debug message content
  ///     error - optional error object
  ///
  /// RETURNS:
  ///     void
  void debug(String module, String message, [dynamic error]);
  
  /// info
  ///
  /// DESCRIPTION:
  ///     Log informational message for general events.
  ///
  /// PARAMETERS:
  ///     module - source module name
  ///     message - informational message content
  ///     error - optional error object
  ///
  /// RETURNS:
  ///     void
  void info(String module, String message, [dynamic error]);
  
  /// warning
  ///
  /// DESCRIPTION:
  ///     Log warning message for potential issues.
  ///
  /// PARAMETERS:
  ///     module - source module name
  ///     message - warning message content
  ///     error - optional error object
  ///
  /// RETURNS:
  ///     void
  void warning(String module, String message, [dynamic error]);
  
  /// error
  ///
  /// DESCRIPTION:
  ///     Log error message for failures and exceptions.
  ///
  /// PARAMETERS:
  ///     module - source module name
  ///     message - error message content
  ///     error - optional error object
  ///
  /// RETURNS:
  ///     void
  void error(String module, String message, [dynamic error]);
  
  /// critical
  ///
  /// DESCRIPTION:
  ///     Log critical message for severe system failures.
  ///
  /// PARAMETERS:
  ///     module - source module name
  ///     message - critical message content
  ///     error - optional error object
  ///
  /// RETURNS:
  ///     void
  void critical(String module, String message, [dynamic error]);
  
  // ============================================================================
  // STRUCTURED LOGGING
  // ============================================================================
  
  /// logStructured
  ///
  /// DESCRIPTION:
  ///     Log structured data with additional context fields.
  ///
  /// PARAMETERS:
  ///     level - log level
  ///     module - source module name
  ///     message - primary message content
  ///     data - additional structured data
  ///     error - optional error object
  ///
  /// RETURNS:
  ///     void
  void logStructured(
    LogLevel level,
    String module,
    String message, {
    Map<String, dynamic>? data,
    dynamic error,
  });
  
  /// logEvent
  ///
  /// DESCRIPTION:
  ///     Log application event with event type and metadata.
  ///
  /// PARAMETERS:
  ///     eventType - type of event (connection, authentication, etc.)
  ///     module - source module name
  ///     message - event description
  ///     metadata - event-specific metadata
  ///
  /// RETURNS:
  ///     void
  void logEvent(
    String eventType,
    String module,
    String message, {
    Map<String, dynamic>? metadata,
  });
  
  // ============================================================================
  // LOG CONFIGURATION
  // ============================================================================
  
  /// setLogLevel
  ///
  /// DESCRIPTION:
  ///     Set minimum log level for filtering.
  ///
  /// PARAMETERS:
  ///     level - minimum log level to record
  ///
  /// RETURNS:
  ///     void
  void setLogLevel(LogLevel level);
  
  /// getLogLevel
  ///
  /// DESCRIPTION:
  ///     Get current minimum log level.
  ///
  /// RETURNS:
  ///     LogLevel - current minimum log level
  LogLevel getLogLevel();
  
  /// enableModule
  ///
  /// DESCRIPTION:
  ///     Enable logging for specific module.
  ///
  /// PARAMETERS:
  ///     module - module name to enable
  ///
  /// RETURNS:
  ///     void
  void enableModule(String module);
  
  /// disableModule
  ///
  /// DESCRIPTION:
  ///     Disable logging for specific module.
  ///
  /// PARAMETERS:
  ///     module - module name to disable
  ///
  /// RETURNS:
  ///     void
  void disableModule(String module);
  
  /// isModuleEnabled
  ///
  /// DESCRIPTION:
  ///     Check if logging is enabled for specific module.
  ///
  /// PARAMETERS:
  ///     module - module name to check
  ///
  /// RETURNS:
  ///     bool - true if module logging is enabled
  bool isModuleEnabled(String module);
  
  // ============================================================================
  // LOG FILE MANAGEMENT
  // ============================================================================
  
  /// getLogFiles
  ///
  /// DESCRIPTION:
  ///     Get list of available log files.
  ///
  /// RETURNS:
  ///     Future<List<String>> - list of log file paths
  Future<List<String>> getLogFiles();
  
  /// getLogContent
  ///
  /// DESCRIPTION:
  ///     Get content of specific log file.
  ///
  /// PARAMETERS:
  ///     filePath - path to log file
  ///     maxLines - maximum number of lines to return (optional)
  ///
  /// RETURNS:
  ///     Future<String> - log file content
  Future<String> getLogContent(String filePath, {int? maxLines});
  
  /// clearLogs
  ///
  /// DESCRIPTION:
  ///     Clear all log files and reset logging.
  ///
  /// RETURNS:
  ///     Future<void> - completion of log clearing
  Future<void> clearLogs();
  
  /// rotateLogs
  ///
  /// DESCRIPTION:
  ///     Manually trigger log rotation.
  ///
  /// RETURNS:
  ///     Future<void> - completion of log rotation
  Future<void> rotateLogs();
  
  // ============================================================================
  // PLATFORM-SPECIFIC FEATURES
  // ============================================================================
  
  /// exportLogs
  ///
  /// DESCRIPTION:
  ///     Export logs for sharing or analysis.
  ///     Returns platform-appropriate format (file path, share intent, etc.)
  ///
  /// PARAMETERS:
  ///     format - export format ('json', 'text', 'archive')
  ///
  /// RETURNS:
  ///     Future<String> - export result (file path or share identifier)
  Future<String> exportLogs({String format = 'text'});
  
  /// getLogStatistics
  ///
  /// DESCRIPTION:
  ///     Get logging statistics and metrics.
  ///
  /// RETURNS:
  ///     Future<Map<String, dynamic>> - logging statistics
  Future<Map<String, dynamic>> getLogStatistics();
}
