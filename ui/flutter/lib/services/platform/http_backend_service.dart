/// AUTHOR: wei
/// HISTORY: 27/06/2025 - Initial implementation of HTTP backend service

import 'package:flutter/foundation.dart';
import '../backend_service.dart';
import '../log_service.dart';
import 'cross_platform_backend_service.dart';

/// HttpBackendService
///
/// PURPOSE:
///     Windows/Linux backend service implementation using HTTP communication.
///     Wraps the existing BackendService to provide cross-platform interface.
///
/// FEATURES:
///     - HTTP API communication with Go backend
///     - Process management for Go backend
///     - Health checking via HTTP endpoints
///     - Admin privileges support on Windows
///
/// USAGE:
///     final backendService = HttpBackendService(logService);
///     final success = await backendService.initialize();
class HttpBackendService implements CrossPlatformBackendService {
  
  // ============================================================================
  // DEPENDENCIES
  // ============================================================================
  
  final BackendService _backendService;
  
  // ============================================================================
  // CONSTRUCTOR
  // ============================================================================
  
  /// HttpBackendService constructor
  ///
  /// DESCRIPTION:
  ///     Create HTTP backend service wrapper around existing BackendService.
  ///
  /// PARAMETERS:
  ///     logService - log service instance for event logging
  HttpBackendService({required LogService logService})
      : _backendService = BackendService(logService: logService);
  
  // ============================================================================
  // LIFECYCLE MANAGEMENT
  // ============================================================================
  
  @override
  Future<bool> initialize() async {
    try {
      return await _backendService.start();
    } catch (e) {
      debugPrint('HTTP backend initialization failed: $e');
      return false;
    }
  }
  
  @override
  Future<void> shutdown() async {
    try {
      await _backendService.stop();
    } catch (e) {
      debugPrint('HTTP backend shutdown failed: $e');
    }
  }
  
  @override
  Future<void> dispose() async {
    await shutdown();
  }
  
  // ============================================================================
  // HEALTH MONITORING
  // ============================================================================
  
  @override
  Future<bool> checkHealth() async {
    // Use the existing backend service's running status
    // Could be enhanced with actual HTTP health check
    return _backendService.isRunning;
  }
  
  @override
  bool get isRunning => _backendService.isRunning;
  
  // ============================================================================
  // STATUS INFORMATION
  // ============================================================================
  
  @override
  Future<Map<String, dynamic>> getStatus() async {
    return {
      'status': _backendService.isRunning ? 'running' : 'stopped',
      'platform': 'windows_linux',
      'type': 'http_api',
      'process_managed': true,
      'backend_type': 'go_process',
    };
  }
  
  @override
  Map<String, dynamic> getPlatformInfo() {
    return {
      'platform': 'windows_linux',
      'type': 'http_api',
      'communication': 'http_websocket',
      'process_management': 'external_process',
      'backend_type': 'go_executable',
    };
  }
}
