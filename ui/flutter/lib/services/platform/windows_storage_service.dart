// /**
//  * AUTHOR: wei
//  * HISTORY: 27/06/2025 - Windows storage service using only SharedPreferences
//  */

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'cross_platform_storage_service.dart';

/// WindowsStorageService
///
/// PURPOSE:
///     Windows implementation of CrossPlatformStorageService using only SharedPreferences.
///     Avoids creating custom directories that might require admin permissions.
///
/// FEATURES:
///     - SharedPreferences for all key-value storage
///     - No custom directory creation
///     - No admin permissions required
///     - Simple and reliable storage
///
/// USAGE:
///     Created by PlatformServiceFactory for Windows platform.
///     Uses only standard Flutter SharedPreferences mechanism.
class WindowsStorageService implements CrossPlatformStorageService {
  
  bool _isInitialized = false;
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 简单的初始化，不创建任何目录
      _isInitialized = true;
      debugPrint('✓ Windows storage service initialized successfully (SharedPreferences only)');
    } catch (e) {
      debugPrint('✗ Failed to initialize Windows storage: $e');
      throw Exception('Failed to initialize Windows storage: $e');
    }
  }
  
  @override
  Future<void> dispose() async {
    _isInitialized = false;
  }
  
  @override
  Future<void> setString(String key, String value) async {
    await _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, value);
  }

  @override
  Future<String?> getString(String key) async {
    await _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }

  @override
  Future<void> setBool(String key, bool value) async {
    await _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, value);
  }

  @override
  Future<bool?> getBool(String key) async {
    await _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key);
  }

  @override
  Future<void> setInt(String key, int value) async {
    await _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(key, value);
  }

  @override
  Future<int?> getInt(String key) async {
    await _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(key);
  }

  @override
  Future<void> remove(String key) async {
    await _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }

  @override
  Future<void> clear() async {
    await _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }
  
  @override
  Future<void> setSecureString(String key, String value) async {
    await _ensureInitialized();
    // 在Windows上，将安全数据也直接存储在SharedPreferences中，保持与mobile_old一致
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, value);
  }

  @override
  Future<String?> getSecureString(String key) async {
    await _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }

  @override
  Future<void> removeSecureString(String key) async {
    await _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }

  @override
  Future<void> setSharedData(String key, Map<String, dynamic> data) async {
    await _ensureInitialized();
    // 将共享数据直接存储在SharedPreferences中，保持与mobile_old一致
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, json.encode(data));
  }

  @override
  Future<Map<String, dynamic>?> getSharedData(String key) async {
    await _ensureInitialized();
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonStr = prefs.getString(key);
      if (jsonStr == null) return null;

      return Map<String, dynamic>.from(json.decode(jsonStr));
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> removeSharedData(String key) async {
    await _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }
  
  @override
  Future<void> setBatch(Map<String, dynamic> data) async {
    await _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();

    for (final entry in data.entries) {
      final value = entry.value;
      if (value is String) {
        await prefs.setString(entry.key, value);
      } else if (value is bool) {
        await prefs.setBool(entry.key, value);
      } else if (value is int) {
        await prefs.setInt(entry.key, value);
      } else if (value is double) {
        await prefs.setDouble(entry.key, value);
      } else {
        await prefs.setString(entry.key, json.encode(value));
      }
    }
  }

  @override
  Future<Map<String, dynamic>> getBatch(List<String> keys) async {
    await _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    final result = <String, dynamic>{};

    for (final key in keys) {
      final value = prefs.get(key);
      if (value != null) {
        result[key] = value;
      }
    }

    return result;
  }

  @override
  Future<Map<String, String>> getStoragePaths() async {
    await _ensureInitialized();

    // 返回标准路径，但不创建目录
    return {
      'config': 'SharedPreferences',
      'logs': 'SharedPreferences',
      'userdata': 'SharedPreferences',
      'cache': 'SharedPreferences',
      'secure': 'SharedPreferences',
      'shared': 'SharedPreferences',
    };
  }
  
  @override
  Future<String?> getAppGroupPath() async {
    // App groups not available on Windows
    return null;
  }
  
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }
}
