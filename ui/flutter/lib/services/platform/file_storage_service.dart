// /**
//  * AUTHOR: wei
//  * HISTORY: 27/06/2025 - Initial implementation of Windows/Linux file storage service
//  */

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'cross_platform_storage_service.dart';

/// FileStorageService
///
/// PURPOSE:
///     Windows/Linux implementation of CrossPlatformStorageService using file-based storage.
///     Provides SharedPreferences and encrypted file storage for desktop platforms.
///
/// FEATURES:
///     - SharedPreferences for basic key-value storage
///     - Encrypted file storage for secure data
///     - File-based batch operations
///     - Directory path management
///
/// USAGE:
///     Created by PlatformServiceFactory for Windows/Linux platforms.
///     Uses standard Flutter storage mechanisms with file encryption.
class FileStorageService implements CrossPlatformStorageService {
  
  bool _isInitialized = false;
  late String _secureStorageDir;
  late String _sharedDataDir;
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 获取正确的AppData路径
      String baseDir;
      if (Platform.isWindows) {
        // Windows: 直接使用AppData目录，不添加额外的应用名
        final appDataDir = await getApplicationSupportDirectory();
        // getApplicationSupportDirectory() 在Windows上可能已经包含应用名，直接使用
        baseDir = appDataDir.path;
      } else if (Platform.isIOS || Platform.isMacOS) {
        // iOS/macOS: 使用应用支持目录
        final appDataDir = await getApplicationSupportDirectory();
        baseDir = appDataDir.path;
      } else if (Platform.isAndroid) {
        // Android: 使用应用支持目录
        final appDataDir = await getApplicationSupportDirectory();
        baseDir = appDataDir.path;
      } else {
        // Linux: 使用当前目录
        baseDir = Directory.current.path;
      }

      _secureStorageDir = '$baseDir${Platform.pathSeparator}secure_storage';
      _sharedDataDir = '$baseDir${Platform.pathSeparator}shared_data';

      // 创建基础目录
      final baseDirObj = Directory(baseDir);
      await baseDirObj.create(recursive: true);

      // 创建安全存储目录
      final secureDir = Directory(_secureStorageDir);
      await secureDir.create(recursive: true);

      // 创建共享数据目录
      final sharedDir = Directory(_sharedDataDir);
      await sharedDir.create(recursive: true);

      // 验证目录是否创建成功
      if (!await secureDir.exists()) {
        throw Exception('Failed to create secure storage directory: $_secureStorageDir');
      }

      if (!await sharedDir.exists()) {
        throw Exception('Failed to create shared data directory: $_sharedDataDir');
      }

      _isInitialized = true;

      // 记录初始化成功信息（使用debugPrint避免循环依赖）
      debugPrint('✓ File storage service initialized successfully');
      debugPrint('✓ Base directory: $baseDir');
      debugPrint('✓ Secure storage directory: $_secureStorageDir');
      debugPrint('✓ Shared data directory: $_sharedDataDir');
    } catch (e) {
      // 使用debugPrint确保错误信息能够输出
      debugPrint('✗ Failed to initialize file storage: $e');
      throw Exception('Failed to initialize file storage: $e');
    }
  }
  
  @override
  Future<void> dispose() async {
    _isInitialized = false;
  }
  
  @override
  Future<void> setString(String key, String value) async {
    _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, value);
  }
  
  @override
  Future<String?> getString(String key) async {
    _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }
  
  @override
  Future<void> setBool(String key, bool value) async {
    _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, value);
  }
  
  @override
  Future<bool?> getBool(String key) async {
    _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key);
  }
  
  @override
  Future<void> setInt(String key, int value) async {
    _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(key, value);
  }
  
  @override
  Future<int?> getInt(String key) async {
    _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(key);
  }
  
  @override
  Future<void> remove(String key) async {
    _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }
  
  @override
  Future<void> clear() async {
    _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }
  
  @override
  Future<void> setSecureString(String key, String value) async {
    _ensureInitialized();
    final encrypted = _encryptValue(value);
    final file = File('$_secureStorageDir/$key.enc');
    await file.writeAsString(encrypted);
  }
  
  @override
  Future<String?> getSecureString(String key) async {
    _ensureInitialized();
    try {
      final file = File('$_secureStorageDir/$key.enc');
      if (!await file.exists()) return null;
      
      final encrypted = await file.readAsString();
      return _decryptValue(encrypted);
    } catch (e) {
      return null;
    }
  }
  
  @override
  Future<void> removeSecureString(String key) async {
    _ensureInitialized();
    final file = File('$_secureStorageDir/$key.enc');
    if (await file.exists()) {
      await file.delete();
    }
  }
  
  @override
  Future<void> setSharedData(String key, Map<String, dynamic> data) async {
    _ensureInitialized();
    final file = File('$_sharedDataDir/$key.json');
    await file.writeAsString(json.encode(data));
  }
  
  @override
  Future<Map<String, dynamic>?> getSharedData(String key) async {
    _ensureInitialized();
    try {
      final file = File('$_sharedDataDir/$key.json');
      if (!await file.exists()) return null;
      
      final content = await file.readAsString();
      return Map<String, dynamic>.from(json.decode(content));
    } catch (e) {
      return null;
    }
  }
  
  @override
  Future<void> removeSharedData(String key) async {
    _ensureInitialized();
    final file = File('$_sharedDataDir/$key.json');
    if (await file.exists()) {
      await file.delete();
    }
  }
  
  @override
  Future<void> setBatch(Map<String, dynamic> data) async {
    _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    
    for (final entry in data.entries) {
      final value = entry.value;
      if (value is String) {
        await prefs.setString(entry.key, value);
      } else if (value is bool) {
        await prefs.setBool(entry.key, value);
      } else if (value is int) {
        await prefs.setInt(entry.key, value);
      } else if (value is double) {
        await prefs.setDouble(entry.key, value);
      } else {
        await prefs.setString(entry.key, json.encode(value));
      }
    }
  }
  
  @override
  Future<Map<String, dynamic>> getBatch(List<String> keys) async {
    _ensureInitialized();
    final prefs = await SharedPreferences.getInstance();
    final result = <String, dynamic>{};
    
    for (final key in keys) {
      final value = prefs.get(key);
      if (value != null) {
        result[key] = value;
      }
    }
    
    return result;
  }
  
  @override
  Future<Map<String, String>> getStoragePaths() async {
    _ensureInitialized();

    // 获取正确的AppData路径
    String baseDir;
    if (Platform.isWindows) {
      final appDataDir = await getApplicationSupportDirectory();
      // getApplicationSupportDirectory() 在Windows上可能已经包含应用名，直接使用
      baseDir = appDataDir.path;
    } else if (Platform.isIOS || Platform.isMacOS) {
      // iOS/macOS: 使用应用支持目录
      final appDataDir = await getApplicationSupportDirectory();
      baseDir = appDataDir.path;
    } else if (Platform.isAndroid) {
      // Android: 使用应用支持目录
      final appDataDir = await getApplicationSupportDirectory();
      baseDir = appDataDir.path;
    } else {
      // Linux: 使用当前目录
      baseDir = Directory.current.path;
    }

    return {
      'config': '$baseDir${Platform.pathSeparator}config',
      'logs': '$baseDir${Platform.pathSeparator}logs',
      'userdata': '$baseDir${Platform.pathSeparator}userdata',
      'cache': '$baseDir${Platform.pathSeparator}cache',
      'secure': _secureStorageDir,
      'shared': _sharedDataDir,
    };
  }
  
  @override
  Future<String?> getAppGroupPath() async {
    // App groups not available on Windows/Linux
    return null;
  }
  
  String _encryptValue(String value) {
    // Simple base64 encoding for demonstration
    // In production, use proper encryption
    return base64Encode(utf8.encode(value));
  }
  
  String _decryptValue(String encrypted) {
    // Simple base64 decoding for demonstration
    // In production, use proper decryption
    return utf8.decode(base64Decode(encrypted));
  }
  
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw Exception('File storage service not initialized');
    }
  }
}
