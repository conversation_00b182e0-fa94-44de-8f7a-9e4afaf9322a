///**
/// * AUTHOR: wei
/// * HISTORY: 27/06/2025 - Initial implementation of iOS/macOS platform storage service stub
/// */

import 'dart:async';
import 'package:flutter/services.dart';
import 'cross_platform_storage_service.dart';

/// PlatformStorageService
///
/// PURPOSE:
///     iOS/macOS implementation of CrossPlatformStorageService using Platform Channels.
///     Provides UserDefaults, Keychain, and App Group storage through Swift backend.
///
/// FEATURES:
///     - UserDefaults for basic key-value storage
///     - Keychain for secure credential storage
///     - App Group shared storage for NetworkExtension
///     - Platform Channel communication with Swift
///
/// USAGE:
///     Created by PlatformServiceFactory for iOS/macOS platforms.
///     Automatically handles platform-specific storage mechanisms.
class PlatformStorageService implements CrossPlatformStorageService {
  
  static const MethodChannel _storageChannel = MethodChannel('panabit_client/storage');
  
  bool _isInitialized = false;
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _storageChannel.invokeMethod('initializeStorage');
      _isInitialized = true;
    } catch (e) {
      throw Exception('Failed to initialize platform storage: $e');
    }
  }
  
  @override
  Future<void> dispose() async {
    _isInitialized = false;
  }
  
  @override
  Future<void> setString(String key, String value) async {
    _ensureInitialized();
    await _storageChannel.invokeMethod('setString', {'key': key, 'value': value});
  }
  
  @override
  Future<String?> getString(String key) async {
    _ensureInitialized();
    return await _storageChannel.invokeMethod('getString', {'key': key});
  }
  
  @override
  Future<void> setBool(String key, bool value) async {
    _ensureInitialized();
    await _storageChannel.invokeMethod('setBool', {'key': key, 'value': value});
  }
  
  @override
  Future<bool?> getBool(String key) async {
    _ensureInitialized();
    return await _storageChannel.invokeMethod('getBool', {'key': key});
  }
  
  @override
  Future<void> setInt(String key, int value) async {
    _ensureInitialized();
    await _storageChannel.invokeMethod('setInt', {'key': key, 'value': value});
  }
  
  @override
  Future<int?> getInt(String key) async {
    _ensureInitialized();
    return await _storageChannel.invokeMethod('getInt', {'key': key});
  }
  
  @override
  Future<void> remove(String key) async {
    _ensureInitialized();
    await _storageChannel.invokeMethod('remove', {'key': key});
  }
  
  @override
  Future<void> clear() async {
    _ensureInitialized();
    await _storageChannel.invokeMethod('clear');
  }
  
  @override
  Future<void> setSecureString(String key, String value) async {
    _ensureInitialized();
    await _storageChannel.invokeMethod('setKeychainValue', {'key': key, 'value': value});
  }
  
  @override
  Future<String?> getSecureString(String key) async {
    _ensureInitialized();
    try {
      return await _storageChannel.invokeMethod('getKeychainValue', {'key': key});
    } catch (e) {
      return null;
    }
  }
  
  @override
  Future<void> removeSecureString(String key) async {
    _ensureInitialized();
    await _storageChannel.invokeMethod('removeKeychainValue', {'key': key});
  }
  
  @override
  Future<void> setSharedData(String key, Map<String, dynamic> data) async {
    _ensureInitialized();
    await _storageChannel.invokeMethod('setAppGroupData', {'key': key, 'data': data});
  }
  
  @override
  Future<Map<String, dynamic>?> getSharedData(String key) async {
    _ensureInitialized();
    try {
      final result = await _storageChannel.invokeMethod('getAppGroupData', {'key': key});
      return result != null ? Map<String, dynamic>.from(result) : null;
    } catch (e) {
      return null;
    }
  }
  
  @override
  Future<void> removeSharedData(String key) async {
    _ensureInitialized();
    await _storageChannel.invokeMethod('removeAppGroupData', {'key': key});
  }
  
  @override
  Future<void> setBatch(Map<String, dynamic> data) async {
    _ensureInitialized();
    await _storageChannel.invokeMethod('setBatch', {'data': data});
  }
  
  @override
  Future<Map<String, dynamic>> getBatch(List<String> keys) async {
    _ensureInitialized();
    final result = await _storageChannel.invokeMethod('getBatch', {'keys': keys});
    return Map<String, dynamic>.from(result ?? {});
  }
  
  @override
  Future<Map<String, String>> getStoragePaths() async {
    _ensureInitialized();
    final result = await _storageChannel.invokeMethod('getStoragePaths');
    return Map<String, String>.from(result ?? {});
  }
  
  @override
  Future<String?> getAppGroupPath() async {
    _ensureInitialized();
    return await _storageChannel.invokeMethod('getAppGroupPath');
  }
  
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw Exception('Platform storage service not initialized');
    }
  }
}
