// /**
//  * AUTHOR: wei
//  * HISTORY: 27/06/2025 - Initial implementation of Windows/Linux file log service
//  */

import 'dart:async';
import 'dart:convert';
import 'dart:io';
// import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'cross_platform_log_service.dart';

/// FileLogService
///
/// PURPOSE:
///     Windows/Linux implementation of CrossPlatformLogService using file-based logging.
///     Provides structured logging with automatic rotation and JSON format.
///
/// FEATURES:
///     - File-based logging with rotation
///     - J<PERSON>N structured log format
///     - Multiple log levels and filtering
///     - Module-based categorization
///
/// USAGE:
///     Created by PlatformServiceFactory for Windows/Linux platforms.
///     Logs are written to local files with automatic management.
class FileLogService implements CrossPlatformLogService {
  
  bool _isInitialized = false;
  LogLevel _currentLogLevel = LogLevel.info;
  final Set<String> _enabledModules = <String>{};
  late String _logDirectory;
  late File _currentLogFile;
  static const int _maxLogFileSize = 10 * 1024 * 1024; // 10MB
  static const int _maxLogFiles = 5;
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 获取正确的AppData路径
      if (Platform.isWindows) {
        // Windows: 使用AppData\Roaming\Panabit\logs
        final appDataDir = await getApplicationSupportDirectory();
        // getApplicationSupportDirectory() 在Windows上可能已经包含应用名，直接使用
        _logDirectory = '${appDataDir.path}${Platform.pathSeparator}logs';
      } else if (Platform.isIOS || Platform.isMacOS) {
        // iOS/macOS: 使用应用沙盒目录
        final appDataDir = await getApplicationSupportDirectory();
        _logDirectory = '${appDataDir.path}${Platform.pathSeparator}logs';
      } else if (Platform.isAndroid) {
        // Android: 使用应用内部存储目录
        final appDataDir = await getApplicationSupportDirectory();
        _logDirectory = '${appDataDir.path}${Platform.pathSeparator}logs';
      } else {
        // Linux: 使用当前目录
        _logDirectory = '${Directory.current.path}${Platform.pathSeparator}logs';
      }

      // 创建日志目录
      final logDir = Directory(_logDirectory);
      await logDir.create(recursive: true);

      // 验证目录是否创建成功
      if (!await logDir.exists()) {
        throw Exception('Failed to create log directory: $_logDirectory');
      }

      // 创建日志文件
      _currentLogFile = await _createNewLogFile();

      // 验证日志文件是否创建成功
      if (!await _currentLogFile.exists()) {
        throw Exception('Failed to create log file: ${_currentLogFile.path}');
      }

      _isInitialized = true;

      // 记录初始化成功信息
      // debugPrint('✓ File log service initialized successfully');
      // debugPrint('✓ Log directory: $_logDirectory');
      // debugPrint('✓ Current log file: ${_currentLogFile.path}');
    } catch (e) {
      // 使用debugPrint确保错误信息能够输出
      // debugPrint('✗ Failed to initialize file logging: $e');
      throw Exception('Failed to initialize file logging: $e');
    }
  }
  
  @override
  Future<void> dispose() async {
    _isInitialized = false;
  }
  
  @override
  void debug(String module, String message, [dynamic error]) {
    if (_shouldLog(LogLevel.debug, module)) {
      _writeLog(LogLevel.debug, module, message, error);
    }
  }
  
  @override
  void info(String module, String message, [dynamic error]) {
    if (_shouldLog(LogLevel.info, module)) {
      _writeLog(LogLevel.info, module, message, error);
    }
  }
  
  @override
  void warning(String module, String message, [dynamic error]) {
    if (_shouldLog(LogLevel.warning, module)) {
      _writeLog(LogLevel.warning, module, message, error);
    }
  }
  
  @override
  void error(String module, String message, [dynamic error]) {
    if (_shouldLog(LogLevel.error, module)) {
      _writeLog(LogLevel.error, module, message, error);
    }
  }
  
  @override
  void critical(String module, String message, [dynamic error]) {
    if (_shouldLog(LogLevel.critical, module)) {
      _writeLog(LogLevel.critical, module, message, error);
    }
  }
  
  @override
  void logStructured(
    LogLevel level,
    String module,
    String message, {
    Map<String, dynamic>? data,
    dynamic error,
  }) {
    if (_shouldLog(level, module)) {
      _writeLog(level, module, message, error, data);
    }
  }
  
  @override
  void logEvent(
    String eventType,
    String module,
    String message, {
    Map<String, dynamic>? metadata,
  }) {
    if (_shouldLog(LogLevel.info, module)) {
      final data = <String, dynamic>{
        'eventType': eventType,
        ...?metadata,
      };
      _writeLog(LogLevel.info, module, message, null, data);
    }
  }
  
  @override
  void setLogLevel(LogLevel level) {
    _currentLogLevel = level;
  }
  
  @override
  LogLevel getLogLevel() {
    return _currentLogLevel;
  }
  
  @override
  void enableModule(String module) {
    _enabledModules.add(module);
  }
  
  @override
  void disableModule(String module) {
    _enabledModules.remove(module);
  }
  
  @override
  bool isModuleEnabled(String module) {
    return _enabledModules.isEmpty || _enabledModules.contains(module);
  }
  
  @override
  Future<List<String>> getLogFiles() async {
    _ensureInitialized();
    try {
      final dir = Directory(_logDirectory);
      final files = await dir.list().where((entity) => 
        entity is File && entity.path.endsWith('.log')).toList();
      return files.map((file) => file.path).toList();
    } catch (e) {
      return [];
    }
  }
  
  @override
  Future<String> getLogContent(String filePath, {int? maxLines}) async {
    _ensureInitialized();
    try {
      final file = File(filePath);
      if (!await file.exists()) return '';
      
      final lines = await file.readAsLines();
      if (maxLines != null && lines.length > maxLines) {
        return lines.skip(lines.length - maxLines).join('\n');
      }
      return lines.join('\n');
    } catch (e) {
      return '';
    }
  }
  
  @override
  Future<void> clearLogs() async {
    _ensureInitialized();
    try {
      final dir = Directory(_logDirectory);
      await for (final entity in dir.list()) {
        if (entity is File && entity.path.endsWith('.log')) {
          await entity.delete();
        }
      }
      _currentLogFile = await _createNewLogFile();
    } catch (e) {
      // Ignore errors
    }
  }
  
  @override
  Future<void> rotateLogs() async {
    _ensureInitialized();
    await _rotateLogFile();
  }
  
  @override
  Future<String> exportLogs({String format = 'text'}) async {
    _ensureInitialized();

    // Android平台使用平台通道调用原生日志导出功能
    if (Platform.isAndroid) {
      try {
        const platform = MethodChannel('panabit_client/method');
        final result = await platform.invokeMethod('exportLogs', {
          'format': format,
        });
        return result as String? ?? '';
      } catch (e) {
        // 如果平台通道调用失败，回退到文件系统操作
        // 这确保了向后兼容性
      }
    }

    // iOS/macOS/Windows/Linux平台使用直接文件系统操作
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final exportFile = File('$_logDirectory/export_$timestamp.$format');

      if (format == 'json') {
        // 对于文本格式的日志，创建JSON结构
        final allLogs = <Map<String, dynamic>>[];
        final logFiles = await getLogFiles();

        for (final filePath in logFiles) {
          final content = await File(filePath).readAsString();
          final lines = content.split('\n').where((line) => line.isNotEmpty);
          for (final line in lines) {
            // 解析文本格式的日志行: [时间] [级别] [模块] 消息
            final logEntry = _parseLogLine(line);
            if (logEntry != null) {
              allLogs.add(logEntry);
            }
          }
        }

        await exportFile.writeAsString(json.encode(allLogs));
      } else {
        final buffer = StringBuffer();
        final logFiles = await getLogFiles();

        for (final filePath in logFiles) {
          buffer.writeln('=== $filePath ===');
          buffer.writeln(await getLogContent(filePath));
          buffer.writeln();
        }

        await exportFile.writeAsString(buffer.toString());
      }

      return exportFile.path;
    } catch (e) {
      return '';
    }
  }
  
  @override
  Future<Map<String, dynamic>> getLogStatistics() async {
    _ensureInitialized();
    try {
      final logFiles = await getLogFiles();
      int totalLines = 0;
      int totalSize = 0;
      
      for (final filePath in logFiles) {
        final file = File(filePath);
        final stat = await file.stat();
        totalSize += stat.size;
        
        final lines = await file.readAsLines();
        totalLines += lines.length;
      }
      
      return {
        'totalFiles': logFiles.length,
        'totalLines': totalLines,
        'totalSize': totalSize,
        'currentLogLevel': _levelToString(_currentLogLevel),
        'enabledModules': _enabledModules.toList(),
      };
    } catch (e) {
      return {};
    }
  }
  
  void _writeLog(
    LogLevel level,
    String module,
    String message,
    dynamic error, [
    Map<String, dynamic>? data,
  ]) {
    if (!_isInitialized) return;
    
    try {
      final timestamp = DateTime.now().toString();
      final levelStr = _levelToString(level).toUpperCase();

      // 构建正常格式的日志行: [时间] [级别] [模块] 消息
      String logLine = '[$timestamp] [$levelStr] [$module] $message';

      // 如果有错误信息，添加到日志行
      if (error != null) {
        logLine += ' - Error: $error';
      }

      // 如果有额外数据，添加到日志行
      if (data != null) {
        logLine += ' - Data: $data';
      }

      _currentLogFile.writeAsStringSync('$logLine\n', mode: FileMode.append);
      
      // Check if rotation is needed
      _checkLogRotation();
    } catch (e) {
      // Ignore logging errors to prevent infinite loops
    }
  }
  
  Future<void> _checkLogRotation() async {
    try {
      final stat = await _currentLogFile.stat();
      if (stat.size > _maxLogFileSize) {
        await _rotateLogFile();
      }
    } catch (e) {
      // Ignore rotation errors
    }
  }
  
  Future<void> _rotateLogFile() async {
    try {
      _currentLogFile = await _createNewLogFile();

      // Clean up old log files
      await _cleanupOldLogFiles();
    } catch (e) {
      // Ignore rotation errors
    }
  }
  
  Future<File> _createNewLogFile() async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final fileName = 'panabit_$timestamp.log';
    final file = File('$_logDirectory${Platform.pathSeparator}$fileName');

    // 实际创建文件
    await file.create(recursive: true);

    return file;
  }
  
  Future<void> _cleanupOldLogFiles() async {
    try {
      final logFiles = await getLogFiles();
      if (logFiles.length > _maxLogFiles) {
        logFiles.sort();
        final filesToDelete = logFiles.take(logFiles.length - _maxLogFiles);
        for (final filePath in filesToDelete) {
          await File(filePath).delete();
        }
      }
    } catch (e) {
      // Ignore cleanup errors
    }
  }
  
  bool _shouldLog(LogLevel level, String module) {
    if (!_isInitialized) return false;
    if (level.index < _currentLogLevel.index) return false;
    return isModuleEnabled(module);
  }
  
  String _levelToString(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return 'debug';
      case LogLevel.info:
        return 'info';
      case LogLevel.warning:
        return 'warning';
      case LogLevel.error:
        return 'error';
      case LogLevel.critical:
        return 'critical';
    }
  }
  
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw Exception('File log service not initialized');
    }
  }

  /// _parseLogLine
  ///
  /// DESCRIPTION:
  ///     解析文本格式的日志行为JSON格式
  ///
  /// PARAMETERS:
  ///     line - 日志行文本
  ///
  /// RETURNS:
  ///     Map<String, dynamic>? - 解析后的日志条目，如果解析失败返回null
  Map<String, dynamic>? _parseLogLine(String line) {
    try {
      // 解析格式: [时间] [级别] [模块] 消息
      final regex = RegExp(r'^\[([^\]]+)\] \[([^\]]+)\] \[([^\]]+)\] (.+)$');
      final match = regex.firstMatch(line);

      if (match != null) {
        final timestamp = match.group(1)!;
        final level = match.group(2)!.toLowerCase();
        final module = match.group(3)!;
        final message = match.group(4)!;

        // 检查是否有错误信息
        String? error;
        String actualMessage = message;
        if (message.contains(' - Error: ')) {
          final parts = message.split(' - Error: ');
          actualMessage = parts[0];
          error = parts[1];
        }

        return {
          'timestamp': timestamp,
          'level': level,
          'module': module,
          'message': actualMessage,
          if (error != null) 'error': error,
        };
      }
    } catch (e) {
      // 解析失败，返回null
    }
    return null;
  }
}
