// /**
//  * AUTHOR: wei
//  * HISTORY: 27/06/2025 - Initial implementation of cross-platform backend service
//  */

/// CrossPlatformBackendService
///
/// PURPOSE:
///     Abstract interface for backend service management across different platforms.
///     Provides unified API for backend initialization, health checking, and lifecycle management.
///
/// FEATURES:
///     - Platform-agnostic backend management
///     - Unified initialization and health checking
///     - Async lifecycle management
///     - Error handling and status reporting
///
/// USAGE:
///     final backendService = PlatformServiceFactory.createBackendService();
///     final success = await backendService.initialize();
///     final isHealthy = await backendService.checkHealth();
abstract class CrossPlatformBackendService {
  
  // ============================================================================
  // LIFECYCLE MANAGEMENT
  // ============================================================================
  
  /// initialize
  ///
  /// DESCRIPTION:
  ///     Initialize the backend service for the current platform.
  ///     iOS/macOS: Initialize Swift backend via Platform Channel
  ///     Windows/Linux: Start Go backend process
  ///
  /// RETURNS:
  ///     Future<bool> - true if initialization successful, false otherwise
  Future<bool> initialize();
  
  /// shutdown
  ///
  /// DESCRIPTION:
  ///     Gracefully shutdown the backend service.
  ///
  /// RETURNS:
  ///     Future<void> - completion of shutdown operation
  Future<void> shutdown();
  
  /// dispose
  ///
  /// DESCRIPTION:
  ///     Clean up resources and dispose the service.
  ///
  /// RETURNS:
  ///     Future<void> - completion of disposal operation
  Future<void> dispose();
  
  // ============================================================================
  // HEALTH MONITORING
  // ============================================================================
  
  /// checkHealth
  ///
  /// DESCRIPTION:
  ///     Check if the backend service is healthy and responsive.
  ///     iOS/macOS: Check Platform Channel connectivity
  ///     Windows/Linux: HTTP health check
  ///
  /// RETURNS:
  ///     Future<bool> - true if backend is healthy, false otherwise
  Future<bool> checkHealth();
  
  /// isRunning
  ///
  /// DESCRIPTION:
  ///     Check if the backend service is currently running.
  ///
  /// RETURNS:
  ///     bool - true if running, false otherwise
  bool get isRunning;
  
  // ============================================================================
  // STATUS INFORMATION
  // ============================================================================
  
  /// getStatus
  ///
  /// DESCRIPTION:
  ///     Get detailed status information about the backend service.
  ///
  /// RETURNS:
  ///     Future<Map<String, dynamic>> - status information map
  Future<Map<String, dynamic>> getStatus();
  
  /// getPlatformInfo
  ///
  /// DESCRIPTION:
  ///     Get platform-specific information about the backend.
  ///
  /// RETURNS:
  ///     Map<String, dynamic> - platform information map
  Map<String, dynamic> getPlatformInfo();
}
