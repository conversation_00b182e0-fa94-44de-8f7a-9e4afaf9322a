// /**
//  * AUTHOR: wei
//  * HISTORY: 27/06/2025 - Initial implementation of cross-platform storage service interface
//  */

import 'dart:async';

/// CrossPlatformStorageService
///
/// PURPOSE:
///     Abstract interface for cross-platform storage operations.
///     Provides unified storage API for both file-based (Windows/Linux) and 
///     platform-native (iOS/macOS) implementations.
///
/// FEATURES:
///     - Basic key-value storage (UserDefaults/Registry/File)
///     - Secure storage for sensitive data (Keychain/Encrypted files)
///     - App group shared storage (iOS/macOS NetworkExtension sharing)
///     - Batch operations for performance
///     - Storage path management
///
/// USAGE:
///     Implemented by platform-specific services:
///     - FileStorageService for Windows/Linux
///     - PlatformStorageService for iOS/macOS
abstract class CrossPlatformStorageService {
  
  // ============================================================================
  // INITIALIZATION AND LIFECYCLE
  // ============================================================================
  
  /// initialize
  ///
  /// DESCRIPTION:
  ///     Initialize storage service and create necessary directories/containers.
  ///
  /// RETURNS:
  ///     Future<void> - completion of initialization
  Future<void> initialize();
  
  /// dispose
  ///
  /// DESCRIPTION:
  ///     Clean up storage resources and close connections.
  ///
  /// RETURNS:
  ///     Future<void> - completion of cleanup
  Future<void> dispose();
  
  // ============================================================================
  // BASIC STORAGE OPERATIONS
  // ============================================================================
  
  /// setString
  ///
  /// DESCRIPTION:
  ///     Store string value with specified key.
  ///
  /// PARAMETERS:
  ///     key - storage key identifier
  ///     value - string value to store
  ///
  /// RETURNS:
  ///     Future<void> - completion of storage operation
  Future<void> setString(String key, String value);
  
  /// getString
  ///
  /// DESCRIPTION:
  ///     Retrieve string value by key.
  ///
  /// PARAMETERS:
  ///     key - storage key identifier
  ///
  /// RETURNS:
  ///     Future<String?> - stored value or null if not found
  Future<String?> getString(String key);
  
  /// setBool
  ///
  /// DESCRIPTION:
  ///     Store boolean value with specified key.
  ///
  /// PARAMETERS:
  ///     key - storage key identifier
  ///     value - boolean value to store
  ///
  /// RETURNS:
  ///     Future<void> - completion of storage operation
  Future<void> setBool(String key, bool value);
  
  /// getBool
  ///
  /// DESCRIPTION:
  ///     Retrieve boolean value by key.
  ///
  /// PARAMETERS:
  ///     key - storage key identifier
  ///
  /// RETURNS:
  ///     Future<bool?> - stored value or null if not found
  Future<bool?> getBool(String key);
  
  /// setInt
  ///
  /// DESCRIPTION:
  ///     Store integer value with specified key.
  ///
  /// PARAMETERS:
  ///     key - storage key identifier
  ///     value - integer value to store
  ///
  /// RETURNS:
  ///     Future<void> - completion of storage operation
  Future<void> setInt(String key, int value);
  
  /// getInt
  ///
  /// DESCRIPTION:
  ///     Retrieve integer value by key.
  ///
  /// PARAMETERS:
  ///     key - storage key identifier
  ///
  /// RETURNS:
  ///     Future<int?> - stored value or null if not found
  Future<int?> getInt(String key);
  
  /// remove
  ///
  /// DESCRIPTION:
  ///     Remove value by key.
  ///
  /// PARAMETERS:
  ///     key - storage key identifier
  ///
  /// RETURNS:
  ///     Future<void> - completion of removal operation
  Future<void> remove(String key);
  
  /// clear
  ///
  /// DESCRIPTION:
  ///     Clear all stored values.
  ///
  /// RETURNS:
  ///     Future<void> - completion of clear operation
  Future<void> clear();
  
  // ============================================================================
  // SECURE STORAGE OPERATIONS
  // ============================================================================
  
  /// setSecureString
  ///
  /// DESCRIPTION:
  ///     Store sensitive string data securely.
  ///     iOS/macOS: Keychain storage
  ///     Windows/Linux: Encrypted file storage
  ///
  /// PARAMETERS:
  ///     key - secure storage key identifier
  ///     value - sensitive string value to store
  ///
  /// RETURNS:
  ///     Future<void> - completion of secure storage operation
  Future<void> setSecureString(String key, String value);
  
  /// getSecureString
  ///
  /// DESCRIPTION:
  ///     Retrieve sensitive string data from secure storage.
  ///
  /// PARAMETERS:
  ///     key - secure storage key identifier
  ///
  /// RETURNS:
  ///     Future<String?> - stored secure value or null if not found
  Future<String?> getSecureString(String key);
  
  /// removeSecureString
  ///
  /// DESCRIPTION:
  ///     Remove sensitive data from secure storage.
  ///
  /// PARAMETERS:
  ///     key - secure storage key identifier
  ///
  /// RETURNS:
  ///     Future<void> - completion of secure removal operation
  Future<void> removeSecureString(String key);
  
  // ============================================================================
  // APP GROUP SHARED STORAGE (iOS/macOS)
  // ============================================================================
  
  /// setSharedData
  ///
  /// DESCRIPTION:
  ///     Store data in app group shared container.
  ///     Used for sharing data between main app and NetworkExtension.
  ///     Only available on iOS/macOS platforms.
  ///
  /// PARAMETERS:
  ///     key - shared storage key identifier
  ///     data - data to store in shared container
  ///
  /// RETURNS:
  ///     Future<void> - completion of shared storage operation
  Future<void> setSharedData(String key, Map<String, dynamic> data);
  
  /// getSharedData
  ///
  /// DESCRIPTION:
  ///     Retrieve data from app group shared container.
  ///
  /// PARAMETERS:
  ///     key - shared storage key identifier
  ///
  /// RETURNS:
  ///     Future<Map<String, dynamic>?> - stored shared data or null if not found
  Future<Map<String, dynamic>?> getSharedData(String key);
  
  /// removeSharedData
  ///
  /// DESCRIPTION:
  ///     Remove data from app group shared container.
  ///
  /// PARAMETERS:
  ///     key - shared storage key identifier
  ///
  /// RETURNS:
  ///     Future<void> - completion of shared removal operation
  Future<void> removeSharedData(String key);
  
  // ============================================================================
  // BATCH OPERATIONS
  // ============================================================================
  
  /// setBatch
  ///
  /// DESCRIPTION:
  ///     Store multiple key-value pairs in a single operation.
  ///
  /// PARAMETERS:
  ///     data - map of key-value pairs to store
  ///
  /// RETURNS:
  ///     Future<void> - completion of batch storage operation
  Future<void> setBatch(Map<String, dynamic> data);
  
  /// getBatch
  ///
  /// DESCRIPTION:
  ///     Retrieve multiple values by keys in a single operation.
  ///
  /// PARAMETERS:
  ///     keys - list of keys to retrieve
  ///
  /// RETURNS:
  ///     Future<Map<String, dynamic>> - map of found key-value pairs
  Future<Map<String, dynamic>> getBatch(List<String> keys);
  
  // ============================================================================
  // STORAGE PATH MANAGEMENT
  // ============================================================================
  
  /// getStoragePaths
  ///
  /// DESCRIPTION:
  ///     Get platform-specific storage directory paths.
  ///
  /// RETURNS:
  ///     Future<Map<String, String>> - map of storage type to directory path
  Future<Map<String, String>> getStoragePaths();
  
  /// getAppGroupPath
  ///
  /// DESCRIPTION:
  ///     Get app group shared directory path (iOS/macOS only).
  ///
  /// RETURNS:
  ///     Future<String?> - app group directory path or null if not available
  Future<String?> getAppGroupPath();
}
