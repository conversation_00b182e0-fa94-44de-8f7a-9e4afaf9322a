// /**
//  * AUTHOR: wei
//  * HISTORY: 27/06/2025 - Initial implementation of iOS/macOS platform log service
//  */

import 'dart:async';
import 'package:flutter/services.dart';
import 'cross_platform_log_service.dart';

/// PlatformLogService
///
/// PURPOSE:
///     iOS/macOS implementation of CrossPlatformLogService using OSLog.
///     Provides native logging through Platform Channels to Swift OSLog subsystem.
///
/// FEATURES:
///     - OSLog integration for native iOS/macOS logging
///     - Structured logging with categories and levels
///     - Platform Channel communication with Swift
///     - Automatic log rotation and system integration
///
/// USAGE:
///     Created by PlatformServiceFactory for iOS/macOS platforms.
///     Logs are automatically integrated with system logging.
class PlatformLogService implements CrossPlatformLogService {
  
  static const MethodChannel _logChannel = MethodChannel('panabit_client/logging');
  
  bool _isInitialized = false;
  LogLevel _currentLogLevel = LogLevel.info;
  final Set<String> _enabledModules = <String>{};
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _logChannel.invokeMethod('initializeLogging');
      _isInitialized = true;
    } catch (e) {
      throw Exception('Failed to initialize platform logging: $e');
    }
  }
  
  @override
  Future<void> dispose() async {
    _isInitialized = false;
  }
  
  @override
  void debug(String module, String message, [dynamic error]) {
    if (_shouldLog(LogLevel.debug, module)) {
      _logToChannel('debug', module, message, error);
    }
  }
  
  @override
  void info(String module, String message, [dynamic error]) {
    if (_shouldLog(LogLevel.info, module)) {
      _logToChannel('info', module, message, error);
    }
  }
  
  @override
  void warning(String module, String message, [dynamic error]) {
    if (_shouldLog(LogLevel.warning, module)) {
      _logToChannel('warning', module, message, error);
    }
  }
  
  @override
  void error(String module, String message, [dynamic error]) {
    if (_shouldLog(LogLevel.error, module)) {
      _logToChannel('error', module, message, error);
    }
  }
  
  @override
  void critical(String module, String message, [dynamic error]) {
    if (_shouldLog(LogLevel.critical, module)) {
      _logToChannel('critical', module, message, error);
    }
  }
  
  @override
  void logStructured(
    LogLevel level,
    String module,
    String message, {
    Map<String, dynamic>? data,
    dynamic error,
  }) {
    if (_shouldLog(level, module)) {
      _logToChannel(_levelToString(level), module, message, error, data);
    }
  }
  
  @override
  void logEvent(
    String eventType,
    String module,
    String message, {
    Map<String, dynamic>? metadata,
  }) {
    if (_shouldLog(LogLevel.info, module)) {
      final data = <String, dynamic>{
        'eventType': eventType,
        ...?metadata,
      };
      _logToChannel('info', module, message, null, data);
    }
  }
  
  @override
  void setLogLevel(LogLevel level) {
    _currentLogLevel = level;
  }
  
  @override
  LogLevel getLogLevel() {
    return _currentLogLevel;
  }
  
  @override
  void enableModule(String module) {
    _enabledModules.add(module);
  }
  
  @override
  void disableModule(String module) {
    _enabledModules.remove(module);
  }
  
  @override
  bool isModuleEnabled(String module) {
    return _enabledModules.isEmpty || _enabledModules.contains(module);
  }
  
  @override
  Future<List<String>> getLogFiles() async {
    _ensureInitialized();
    try {
      final result = await _logChannel.invokeMethod('getLogFiles');
      return List<String>.from(result ?? []);
    } catch (e) {
      return [];
    }
  }
  
  @override
  Future<String> getLogContent(String filePath, {int? maxLines}) async {
    _ensureInitialized();
    try {
      final result = await _logChannel.invokeMethod('getLogContent', {
        'filePath': filePath,
        'maxLines': maxLines,
      });
      return result ?? '';
    } catch (e) {
      return '';
    }
  }
  
  @override
  Future<void> clearLogs() async {
    _ensureInitialized();
    await _logChannel.invokeMethod('clearLogs');
  }
  
  @override
  Future<void> rotateLogs() async {
    _ensureInitialized();
    await _logChannel.invokeMethod('rotateLogs');
  }
  
  @override
  Future<String> exportLogs({String format = 'text'}) async {
    _ensureInitialized();
    try {
      final result = await _logChannel.invokeMethod('exportLogs', {
        'format': format,
      });
      return result ?? '';
    } catch (e) {
      return '';
    }
  }
  
  @override
  Future<Map<String, dynamic>> getLogStatistics() async {
    _ensureInitialized();
    try {
      final result = await _logChannel.invokeMethod('getLogStatistics');
      return Map<String, dynamic>.from(result ?? {});
    } catch (e) {
      return {};
    }
  }
  
  void _logToChannel(
    String level,
    String module,
    String message,
    dynamic error, [
    Map<String, dynamic>? data,
  ]) {
    if (!_isInitialized) return;
    
    try {
      _logChannel.invokeMethod('writeLog', {
        'level': level,
        'module': module,
        'message': message,
        'error': error?.toString(),
        'data': data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      // Ignore logging errors to prevent infinite loops
    }
  }
  
  bool _shouldLog(LogLevel level, String module) {
    if (!_isInitialized) return false;
    if (level.index < _currentLogLevel.index) return false;
    return isModuleEnabled(module);
  }
  
  String _levelToString(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return 'debug';
      case LogLevel.info:
        return 'info';
      case LogLevel.warning:
        return 'warning';
      case LogLevel.error:
        return 'error';
      case LogLevel.critical:
        return 'critical';
    }
  }
  
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw Exception('Platform log service not initialized');
    }
  }
}
