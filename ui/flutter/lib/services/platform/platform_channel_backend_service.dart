/// AUTHOR: wei
/// HISTORY: 27/06/2025 - Initial implementation of Platform Channel backend service

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'cross_platform_backend_service.dart';
import 'platform_service_factory.dart';

/// PlatformChannelBackendService
///
/// PURPOSE:
///     iOS/macOS backend service implementation using Platform Channel.
///     Communicates with Swift backend through method calls instead of starting separate process.
///
/// FEATURES:
///     - Platform Channel communication with Swift backend
///     - Synchronous backend initialization at app startup
///     - Native iOS/macOS integration
///     - No separate process management needed
///
/// USAGE:
///     final backendService = PlatformChannelBackendService();
///     final success = await backendService.initialize();
class PlatformChannelBackendService implements CrossPlatformBackendService {
  
  // ============================================================================
  // CONSTANTS
  // ============================================================================
  
  static const MethodChannel _initChannel = MethodChannel('panabit_client/init');
  
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================
  
  bool _isInitialized = false;
  bool _isRunning = false;
  
  // ============================================================================
  // LIFECYCLE MANAGEMENT
  // ============================================================================
  
  @override
  Future<bool> initialize() async {
    if (_isInitialized) {
      return _isRunning;
    }

    try {
      // ✅ 在登录时创建PlatformChannelHandler
      // 通过Platform Channel调用AppDelegate的setupPlatformChannelHandler方法
      debugPrint('PlatformChannelBackendService: Creating PlatformChannelHandler on login...');

      // 调用initializeBackend方法，这会触发PlatformChannelHandler的创建和PanabitCoreManager的初始化
      final result = await _initChannel.invokeMethod('initializeBackend');

      if (result == true) {
        _isInitialized = true;
        _isRunning = true;
        debugPrint('PlatformChannelBackendService: Backend initialized successfully on login');

        // ✅ EventChannel监听将由PlatformChannelApiService在需要时自动启动
        // 避免在这里强制启动，防止循环依赖
        debugPrint('PlatformChannelBackendService: Backend ready, EventChannel will be available');

        return true;
      } else {
        debugPrint('PlatformChannelBackendService: Backend initialization failed');
        return false;
      }
    } catch (e) {
      debugPrint('Platform Channel backend initialization failed: $e');
      return false;
    }
  }
  
  @override
  Future<void> shutdown() async {
    if (!_isRunning) {
      return;
    }

    // ✅ 在新架构中，shutdown由PlatformChannelHandler处理
    // 这里只需要更新状态
    _isRunning = false;
    debugPrint('PlatformChannelBackendService: Backend marked as shutdown');
  }
  
  @override
  Future<void> dispose() async {
    await shutdown();
    _isInitialized = false;
  }
  
  // ============================================================================
  // HEALTH MONITORING
  // ============================================================================
  
  @override
  Future<bool> checkHealth() async {
    if (!_isInitialized) {
      return false;
    }

    // ✅ 在新架构中，健康检查由PlatformChannelHandler处理
    // 这里简单返回运行状态
    return _isRunning;
  }
  
  @override
  bool get isRunning => _isRunning;
  
  // ============================================================================
  // STATUS INFORMATION
  // ============================================================================
  
  @override
  Future<Map<String, dynamic>> getStatus() async {
    // ✅ 在新架构中，状态获取由PlatformChannelHandler处理
    // 这里返回基本的后端状态
    return {
      'status': _isRunning ? 'running' : 'stopped',
      'platform': PlatformServiceFactory.platformName.toLowerCase(),
      'type': 'platform_channel',
      'initialized': _isInitialized,
      'message': _isRunning ? 'Backend service is running' : 'Backend service is stopped'
    };
  }



  @override
  Map<String, dynamic> getPlatformInfo() {
    final platformName = PlatformServiceFactory.platformName.toLowerCase();
    String backendType;

    if (PlatformServiceFactory.isApplePlatform) {
      backendType = 'swift_native';
    } else if (Platform.isAndroid) {
      backendType = 'kotlin_native';
    } else {
      backendType = 'unknown';
    }

    return {
      'platform': platformName,
      'type': 'platform_channel',
      'communication': 'method_channel',
      'process_management': 'none',
      'backend_type': backendType,
    };
  }
}
