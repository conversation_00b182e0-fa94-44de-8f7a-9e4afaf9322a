// /**
//  * AUTHOR: wei
//  * HISTORY: 27/06/2025 - Initial implementation of cross-platform API service interface
//  */

import 'dart:async';
import '../../models/connection_status.dart';
import '../../models/server.dart';
import '../../models/user_info.dart';
import '../../models/routing_settings.dart';
import '../../models/interface_info.dart';

/// CrossPlatformApiService
///
/// PURPOSE:
///     Abstract interface for cross-platform API communication.
///     Provides unified API for both HTTP-based (Windows/Linux) and 
///     Platform Channel-based (iOS/macOS) implementations.
///
/// FEATURES:
///     - Unified authentication interface
///     - Server management operations
///     - VPN connection control
///     - Status monitoring and health checks
///     - Event streaming for real-time updates
///     - Platform-agnostic error handling
///
/// USAGE:
///     Implemented by platform-specific services:
///     - HttpApiService for Windows/Linux (HTTP + WebSocket)
///     - PlatformChannelApiService for iOS/macOS (Platform Channel + Event Channel)
abstract class CrossPlatformApiService {
  
  // ============================================================================
  // INITIALIZATION AND LIFECYCLE
  // ============================================================================
  
  /// initialize
  ///
  /// DESCRIPTION:
  ///     Initialize the API service and establish communication channel.
  ///     For HTTP implementation: verify backend service availability.
  ///     For Platform Channel: establish Swift backend connection.
  ///
  /// RETURNS:
  ///     Future<bool> - true if initialization successful, false otherwise
  Future<bool> initialize();
  
  /// dispose
  ///
  /// DESCRIPTION:
  ///     Clean up resources and close communication channels.
  ///     Should be called when the service is no longer needed.
  ///
  /// RETURNS:
  ///     Future<void> - completion of cleanup operations
  Future<void> dispose();
  
  // ============================================================================
  // AUTHENTICATION
  // ============================================================================
  
  /// login
  ///
  /// DESCRIPTION:
  ///     Authenticate user with username and password.
  ///
  /// PARAMETERS:
  ///     username - User login name
  ///     password - User password
  ///
  /// RETURNS:
  ///     Future<UserInfo> - authenticated user information
  ///
  /// THROWS:
  ///     ApiException - authentication failure or network error
  Future<UserInfo> login(String username, String password);
  
  /// logout
  ///
  /// DESCRIPTION:
  ///     Log out current user and clear session.
  ///
  /// RETURNS:
  ///     Future<void> - completion of logout operation
  Future<void> logout();
  
  // ============================================================================
  // SERVER MANAGEMENT
  // ============================================================================
  
  /// getServers
  ///
  /// DESCRIPTION:
  ///     Retrieve list of available VPN servers.
  ///
  /// RETURNS:
  ///     Future<List<Server>> - list of available servers
  Future<List<Server>> getServers();
  
  /// pingServer
  ///
  /// DESCRIPTION:
  ///     Test latency to specific server.
  ///
  /// PARAMETERS:
  ///     serverId - target server identifier
  ///
  /// RETURNS:
  ///     Future<int> - latency in milliseconds, -1 if unreachable
  Future<int> pingServer(String serverId);

  /// pingServers
  ///
  /// DESCRIPTION:
  ///     Test latency to all servers and trigger event notifications.
  ///     For HTTP implementation: POST to /servers/ping endpoint.
  ///     For Platform Channel: call Swift pingAllServers method.
  ///
  /// RETURNS:
  ///     Future<void> - completes when ping operation starts
  Future<void> pingServers();
  
  // ============================================================================
  // VPN CONNECTION CONTROL
  // ============================================================================
  
  /// connect
  ///
  /// DESCRIPTION:
  ///     Establish VPN connection to specified server with user credentials.
  ///     Returns connection result data including interface information.
  ///
  /// PARAMETERS:
  ///     serverId - target server identifier
  ///     username - user login name
  ///     password - user password (encrypted for Windows, plain text for iOS/Android)
  ///
  /// RETURNS:
  ///     Future<Map<String, dynamic>?> - connection result data including interface info
  ///
  /// THROWS:
  ///     ApiException - connection failure or invalid server
  Future<Map<String, dynamic>?> connect(String serverId, String username, String password);
  
  /// disconnect
  ///
  /// DESCRIPTION:
  ///     Disconnect current VPN connection.
  ///
  /// RETURNS:
  ///     Future<void> - completion of disconnection
  Future<void> disconnect();
  
  /// reconnect
  ///
  /// DESCRIPTION:
  ///     Reconnect to current or last connected server.
  ///
  /// RETURNS:
  ///     Future<void> - completion of reconnection attempt
  Future<void> reconnect();
  
  // ============================================================================
  // STATUS AND MONITORING
  // ============================================================================
  
  /// getConnectionStatus
  ///
  /// DESCRIPTION:
  ///     Get current VPN connection status and statistics.
  ///
  /// RETURNS:
  ///     Future<ConnectionStatus> - current connection state and metrics
  Future<ConnectionStatus> getConnectionStatus();
  
  /// getInterfaceInfo
  ///
  /// DESCRIPTION:
  ///     Get network interface information and configuration.
  ///
  /// RETURNS:
  ///     Future<InterfaceInfo> - network interface details
  Future<InterfaceInfo> getInterfaceInfo();
  
  /// healthCheck
  ///
  /// DESCRIPTION:
  ///     Verify API service health and availability.
  ///
  /// RETURNS:
  ///     Future<bool> - true if service is healthy, false otherwise
  Future<bool> healthCheck();
  
  // ============================================================================
  // ROUTING CONFIGURATION
  // ============================================================================
  
  /// getRoutingSettings
  ///
  /// DESCRIPTION:
  ///     Get current routing configuration.
  ///
  /// RETURNS:
  ///     Future<RoutingSettingsModel> - current routing settings
  Future<RoutingSettingsModel> getRoutingSettings();

  /// updateRoutingSettings
  ///
  /// DESCRIPTION:
  ///     Update routing configuration.
  ///
  /// PARAMETERS:
  ///     settings - new routing configuration
  ///
  /// RETURNS:
  ///     Future<void> - completion of settings update
  Future<void> updateRoutingSettings(RoutingSettingsModel settings);

  /// setServerProviderUrl
  ///
  /// DESCRIPTION:
  ///     Set server list provider URL for dynamic server list fetching.
  ///     For HTTP implementation: POST to /servers/provider endpoint.
  ///     For Platform Channel: call Swift setServerProviderUrl method.
  ///
  /// PARAMETERS:
  ///     url - server list provider URL
  ///
  /// RETURNS:
  ///     Future<void> - completion of URL setting operation
  Future<void> setServerProviderUrl(String url);

  // ============================================================================
  // EVENT STREAMING
  // ============================================================================
  
  /// eventStream
  ///
  /// DESCRIPTION:
  ///     Stream of real-time events from the VPN service.
  ///     Events include connection status changes, traffic updates,
  ///     server availability changes, and error notifications.
  ///
  /// RETURNS:
  ///     Stream<Map<String, dynamic>> - stream of event data
  Stream<Map<String, dynamic>> get eventStream;
  
  // ============================================================================
  // UTILITY METHODS
  // ============================================================================
  
  /// shutdown
  ///
  /// DESCRIPTION:
  ///     Request graceful shutdown of backend service.
  ///     Only applicable to HTTP implementation with separate backend process.
  ///
  /// RETURNS:
  ///     Future<void> - completion of shutdown request
  Future<void> shutdown();
}
