/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      auth_service.dart
///
/// DESCRIPTION :    用户认证服务，负责用户登录、注销、凭据管理和认证状态维护，
///                  提供安全的密码加密存储和设备ID管理功能
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:io';

import 'package:flutter/foundation.dart';


import '../models/server.dart';
import '../core/app_state.dart';
import '../core/dependency_injection.dart';
import 'platform/platform_service_factory.dart';
import 'platform/cross_platform_storage_service.dart';
import '../utils/constants.dart';
import '../utils/simple_encryptor.dart';
import '../utils/device_info.dart';
import 'log_service.dart';
import '../utils/async_operation_manager.dart';
import 'api_service.dart';

/// AuthService
///
/// PURPOSE:
///     用户认证服务，负责用户登录、注销、凭据管理和认证状态维护
///
/// FEATURES:
///     - 用户认证：登录验证和会话管理
///     - 凭据管理：安全的密码加密存储和自动填充
///     - 设备管理：设备ID生成和缓存
///     - 状态管理：认证状态的持久化和通知
///     - 安全存储：使用加密技术保护敏感信息
///     - 域管理：客户域的配置和存储
///     - 服务器信息：最佳服务器的获取和缓存
///
/// USAGE:
///     通过依赖注入获取实例，监听认证状态变化，调用登录注销方法
class AuthService extends ChangeNotifier {
  final ApiService apiService;
  late final CrossPlatformStorageService _storageService;
  bool _isAuthenticated = false;
  String _username = '';
  String _password = '';
  String _domain = '';
  bool _rememberCredentials = false;
  Server? _bestServer;
  bool _shouldAutoConnect = false; // 标记是否需要在进入连接界面时自动连接

  // 缓存的设备ID
  String? _cachedDeviceId;

  /// isAuthenticated getter
  ///
  /// DESCRIPTION:
  ///     获取当前用户认证状态
  ///
  /// RETURNS:
  ///     bool - 是否已认证
  bool get isAuthenticated => _isAuthenticated;

  /// username getter
  ///
  /// DESCRIPTION:
  ///     获取当前登录用户名
  ///
  /// RETURNS:
  ///     String - 用户名
  String get username => _username;

  /// password getter
  ///
  /// DESCRIPTION:
  ///     获取当前用户密码（仅内部使用）
  ///
  /// RETURNS:
  ///     String - 密码
  String get password => _password;

  /// domain getter
  ///
  /// DESCRIPTION:
  ///     获取客户域配置
  ///
  /// RETURNS:
  ///     String - 客户域
  String get domain => _domain;

  /// domain setter
  ///
  /// DESCRIPTION:
  ///     设置客户域并保存到本地存储
  ///
  /// PARAMETERS:
  ///     value - 新的客户域值
  set domain(String value) {
    _domain = value;
    _saveDomain();
    notifyListeners();
  }

  /// rememberCredentials getter
  ///
  /// DESCRIPTION:
  ///     获取是否记住凭据的设置
  ///
  /// RETURNS:
  ///     bool - 是否记住凭据
  bool get rememberCredentials => _rememberCredentials;

  /// bestServer getter
  ///
  /// DESCRIPTION:
  ///     获取登录时返回的最佳服务器
  ///
  /// RETURNS:
  ///     Server? - 最佳服务器对象，可能为null
  Server? get bestServer => _bestServer;

  /// shouldAutoConnect getter
  ///
  /// DESCRIPTION:
  ///     获取是否需要在进入连接界面时自动连接
  ///
  /// RETURNS:
  ///     bool - 是否需要自动连接
  bool get shouldAutoConnect => _shouldAutoConnect;

  /// clearAutoConnectFlag
  ///
  /// DESCRIPTION:
  ///     清除自动连接标记，通常在连接完成后调用
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void clearAutoConnectFlag() {
    _shouldAutoConnect = false;
  }

  /// deviceId getter
  ///
  /// DESCRIPTION:
  ///     获取设备ID（同步方法，返回缓存的设备ID）
  ///
  /// RETURNS:
  ///     String - 设备ID，如果未初始化则返回空字符串
  String get deviceId => _cachedDeviceId ?? '';

  /// getDeviceId
  ///
  /// DESCRIPTION:
  ///     异步获取设备ID，如果未缓存则重新获取
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<String> - 设备ID
  Future<String> getDeviceId() async {
    if (_cachedDeviceId != null) {
      return _cachedDeviceId!;
    }

    try {
      final deviceInfo = DeviceInfo();
      _cachedDeviceId = await deviceInfo.getDeviceId();
      return _cachedDeviceId!;
    } catch (e) {
      // 如果获取失败，返回一个基于时间戳的备用ID
      _cachedDeviceId = 'device_${DateTime.now().millisecondsSinceEpoch}';
      return _cachedDeviceId!;
    }
  }

  /// AuthService构造函数
  ///
  /// DESCRIPTION:
  ///     创建认证服务实例并初始化设备ID
  ///
  /// PARAMETERS:
  ///     apiService - API服务实例
  AuthService({required this.apiService}) {
    // 初始化跨平台存储服务
    _storageService = PlatformServiceFactory.createStorageService();
    // 异步初始化设备ID
    _initializeDeviceId();
  }

  /// _initializeDeviceId
  ///
  /// DESCRIPTION:
  ///     初始化设备ID并缓存
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _initializeDeviceId() async {
    try {
      await getDeviceId();
      notifyListeners(); // 通知监听器设备ID已更新
    } catch (e) {
      // 初始化失败时静默处理，getDeviceId方法会提供备用方案
    }
  }

  /// checkAuthentication
  ///
  /// DESCRIPTION:
  ///     检查本地认证状态，加载保存的凭据（不调用API验证）
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 是否有有效的本地认证信息
  Future<bool> checkAuthentication() async {
    if (_isAuthenticated) return true;

    // 加载保存的域
    final savedDomain = await _storageService.getString(StorageKeys.domain);
    if (savedDomain != null && savedDomain.isNotEmpty) {
      _domain = savedDomain;
    }

    // 检查是否有保存的凭据（但不尝试登录）
    if (await _storageService.getBool(StorageKeys.rememberCredentials) == true) {
      final savedUsername = await _storageService.getString(StorageKeys.username);
      final encryptedPassword = await _storageService.getSecureString(StorageKeys.password);

      if (savedUsername != null && encryptedPassword != null &&
          savedUsername.isNotEmpty && encryptedPassword.isNotEmpty) {
        try {
          // 解密密码验证凭据完整性
          final encryptor = SimpleEncryptor();
          final decryptedPassword = await encryptor.decrypt(encryptedPassword);

          if (decryptedPassword.isNotEmpty) {
            // 设置本地认证状态，但不调用后端API
            // 注意：这里不设置 _isAuthenticated = true，因为这只是加载保存的凭据
            // 真正的认证状态应该在成功登录后设置
            _username = savedUsername;
            _password = decryptedPassword;
            _rememberCredentials = true;
            // _isAuthenticated = false; // 保持为false，直到真正登录成功

            notifyListeners();
            return true;
          }
        } catch (e) {
          // 解密失败，清除保存的凭据
          await _clearAuthData();
          return false;
        }
      }
    }

    return false;
  }

  /// validateAuthentication
  ///
  /// DESCRIPTION:
  ///     验证当前认证状态，调用后端API进行验证
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 认证是否有效
  Future<bool> validateAuthentication() async {
    if (!_isAuthenticated || _username.isEmpty || _password.isEmpty) {
      return false;
    }

    try {
      // 使用保存的凭据尝试登录验证
      await login(_username, _password, _rememberCredentials);
      return true;
    } catch (e) {
      // 验证失败，清除认证状态
      _isAuthenticated = false;
      await _clearAuthData();
      notifyListeners();
      return false;
    }
  }

  /// login
  ///
  /// DESCRIPTION:
  ///     用户登录，验证凭据并保存认证状态
  ///
  /// PARAMETERS:
  ///     username - 用户名
  ///     password - 密码
  ///     rememberCredentials - 是否记住凭据
  ///     domain - 可选的客户域
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     ApiException - 登录失败时抛出异常
  Future<void> login(String username, String password, bool rememberCredentials, {String? domain}) async {
    final logService = serviceLocator<LogService>();

    try {
      // logService.debug('AuthService', 'Starting login process');
      // logService.debug('AuthService', 'Login parameters - username: $username, domain: $domain, remember: $rememberCredentials');

      // 如果提供了域，则更新域
      if (domain != null && domain.isNotEmpty) {
        // logService.debug('AuthService', 'Updating domain to: $domain');
        _domain = domain;
        await _saveDomain();
        // logService.debug('AuthService', 'Domain saved successfully');
      }

      // 根据平台决定密码格式
      String loginPassword;
      if (Platform.isIOS || Platform.isMacOS || Platform.isAndroid) {
        // iOS/macOS/Android: 传递明文密码，让Native层处理协议加密
        loginPassword = password;
        // logService.debug('AuthService', 'Using plaintext password for iOS/macOS/Android platform');
      } else {
        // Windows等其他平台: 传递加密密码保持兼容性
        final encryptor = SimpleEncryptor();
        loginPassword = await encryptor.encrypt(password);
        // logService.debug('AuthService', 'Using encrypted password for Windows platform');
      }

      // 使用适当格式的密码进行登录
      // logService.debug('AuthService', 'Calling API service login...');
      final response = await apiService.login(username, loginPassword);
      // logService.debug('AuthService', 'API service login completed successfully');

      _username = username;
      _password = password; // 本地存储原始密码，方便后续使用
      _rememberCredentials = rememberCredentials;
      _isAuthenticated = true;

      // 保存认证数据
      await _saveAuthData();

      // 获取最佳服务器并设置到AppState
      // logService.debug('AuthService', 'Extracting best_server from login response...');
      _bestServer = apiService.getBestServerFromLoginResponse(response);
      // logService.debug('AuthService', 'Best server extracted: ${_bestServer?.name ?? "null"} (ping: ${_bestServer?.ping ?? -1}ms)');

      if (_bestServer != null) {
        // 将登录时选择的最佳服务器设置到AppState
        final appState = serviceLocator<AppState>();
        // logService.debug('AuthService', 'Setting best server to AppState...');
        appState.selectServer(_bestServer!, source: ServerSelectionSource.backendPush);
        // 保留关键成功信息用于生产环境监控
        logService.info('AuthService', 'Successfully set best server: ${_bestServer!.name} (ping: ${_bestServer!.ping}ms)');
      } else {
        // 保留关键警告信息
        logService.warning('AuthService', 'No best_server found in login response');
      }

      // 设置自动连接标记，表示需要在进入连接界面时自动连接
      _shouldAutoConnect = true;
      logService.info('AuthService', 'Auto-connect flag set - will connect automatically when entering connection screen');

      notifyListeners();
    } catch (e) {
      _isAuthenticated = false;
      logService.error('AuthService', 'Login failed', e);
      rethrow;
    }
  }

  /// _saveDomain
  ///
  /// DESCRIPTION:
  ///     保存客户域信息到本地存储
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _saveDomain() async {
    await _storageService.setString(StorageKeys.domain, _domain);
  }

  /// logout
  ///
  /// DESCRIPTION:
  ///     用户注销，清除认证状态和敏感数据
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> logout() async {
    // 首先清除内存中的敏感数据
    _isAuthenticated = false;
    _username = '';
    _password = '';
    _bestServer = null;
    _shouldAutoConnect = false; // 清除自动连接标记

    // 然后清除持久化的认证数据（包括加密的密码）
    await _clearAuthData();

    notifyListeners();
  }

  /// _saveAuthData
  ///
  /// DESCRIPTION:
  ///     保存认证数据到本地存储，包括用户名和加密密码
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _saveAuthData() async {
    // 使用异步操作管理器执行，避免阻塞UI
    await AsyncOperationManager.executeNonBlocking<void>(
      operation: () async {
        final encryptor = SimpleEncryptor();

        // 无论是否记住凭据，都保存用户名以便下次自动填充
        await _storageService.setString(StorageKeys.username, _username);

        // 只有在用户选择记住凭据时才保存加密后的密码
        if (_rememberCredentials) {
          // 加密密码后保存到安全存储
          final encryptedPassword = await encryptor.encrypt(_password);
          await _storageService.setSecureString(StorageKeys.password, encryptedPassword);
        } else {
          await _storageService.removeSecureString(StorageKeys.password);
        }

        // 保存用户对记住凭据的选择
        await _storageService.setBool(StorageKeys.rememberCredentials, _rememberCredentials);
      },
      operationId: 'save_auth_data_$_username',
      timeout: const Duration(seconds: 5),
    );
  }

  /// _clearAuthData
  ///
  /// DESCRIPTION:
  ///     清除认证数据，但保留用户名以便下次自动填充
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _clearAuthData() async {
    // 使用异步操作管理器执行，避免阻塞UI
    await AsyncOperationManager.executeNonBlocking<void>(
      operation: () async {
        // 不删除用户名，以便下次登录时自动填充
        // await _storageService.remove(StorageKeys.username);

        // 清除加密的密码数据
        await _storageService.removeSecureString(StorageKeys.password);

        // 清除记住凭据的设置
        await _storageService.remove(StorageKeys.rememberCredentials);
      },
      operationId: 'clear_auth_data',
      timeout: const Duration(seconds: 5),
    );
  }

  /// clearAllAuthData
  ///
  /// DESCRIPTION:
  ///     强制清除所有认证数据（包括用户名），用于完全重置登录状态
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> clearAllAuthData() async {
    // 清除内存中的数据
    _isAuthenticated = false;
    _username = '';
    _password = '';
    _rememberCredentials = false;
    _bestServer = null;

    // 清除所有持久化的认证数据
    await AsyncOperationManager.executeNonBlocking<void>(
      operation: () async {
        await _storageService.remove(StorageKeys.username);
        await _storageService.removeSecureString(StorageKeys.password);
        await _storageService.remove(StorageKeys.rememberCredentials);
      },
      operationId: 'clear_all_auth_data',
      timeout: const Duration(seconds: 5),
    );

    notifyListeners();
  }

  /// updateRememberCredentials
  ///
  /// DESCRIPTION:
  ///     更新记住凭据设置并保存到本地存储
  ///
  /// PARAMETERS:
  ///     value - 新的记住凭据设置值
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> updateRememberCredentials(bool value) async {
    _rememberCredentials = value;
    await _saveAuthData();
    notifyListeners();
  }
}
