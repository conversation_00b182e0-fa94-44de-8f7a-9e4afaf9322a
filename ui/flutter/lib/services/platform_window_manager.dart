/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// ******************************************************************************
/// FILE NAME :      platform_window_manager.dart
///
/// DESCRIPTION :    Platform-specific window management abstraction layer
///
/// AUTHOR :         wei
///
/// HISTORY :        27/06/2025 create

import 'dart:io';

import 'package:flutter/services.dart';
import 'package:window_manager/window_manager.dart';
import 'window_service.dart';
import 'platform/platform_service_factory.dart';
import 'log_service.dart';
import '../core/dependency_injection.dart';

/// PlatformWindowManager
///
/// PURPOSE:
///     A platform-adaptive window management service that provides
///     unified window control across iOS, macOS, and Windows platforms.
///     Handles platform-specific window behaviors and lifecycle management.
///
/// FEATURES:
///     - iOS: Native app lifecycle management
///     - macOS: System window management integration
///     - Windows: Custom window control via WindowService
///     - Unified API for cross-platform window operations
///
/// USAGE:
///     await PlatformWindowManager.initialize();
///     await PlatformWindowManager.handleAppClose();
class PlatformWindowManager {
  /// Window service instance for desktop platforms
  static WindowService? _windowService;

  /// Whether the manager has been initialized
  static bool _isInitialized = false;



  /// Initialize platform-specific window management
  ///
  /// DESCRIPTION:
  ///     Initializes window management based on the current platform.
  ///     Sets up appropriate window controls and configurations.
  ///
  /// RETURNS:
  ///     Future<void> - Completes when initialization is finished
  static Future<void> initialize() async {
    if (_isInitialized) return;

    // Check if this is iOS App running on macOS
    final isIOSAppOnMac = await PlatformServiceFactory.isIOSAppOnMacOS();

    if (Platform.isWindows || Platform.isLinux) {
      await _initializeDesktopWindow();
    } else if (Platform.isMacOS && !isIOSAppOnMac) {
      // Native macOS App
      await _initializeMacOSWindow();
    } else if (Platform.isIOS || isIOSAppOnMac) {
      // iOS App (native or on macOS)
      await _initializeIOSApp();
    } else if (Platform.isAndroid) {
      // Android App
      await _initializeAndroidApp();
    }

    _isInitialized = true;
  }

  /// Initialize desktop window (Windows/Linux)
  static Future<void> _initializeDesktopWindow() async {
    try {
      final logService = serviceLocator<LogService>();
      _windowService = WindowService(logService: logService);
      await _windowService!.initializeWindow();
      logService.info('PlatformWindowManager', 'Desktop window initialized successfully');
    } catch (e) {
      try {
        final logService = serviceLocator<LogService>();
        logService.error('PlatformWindowManager', 'Failed to initialize desktop window', e);
      } catch (_) {
        // Fallback if log service is not available
        rethrow;
      }
    }
  }

  /// Initialize macOS window
  static Future<void> _initializeMacOSWindow() async {
    try {
      final logService = serviceLocator<LogService>();
      _windowService = WindowService(logService: logService);
      await _windowService!.initializeWindow();
      logService.info('PlatformWindowManager', 'macOS window initialized successfully');
    } catch (e) {
      try {
        final logService = serviceLocator<LogService>();
        logService.error('PlatformWindowManager', 'Failed to initialize macOS window', e);
      } catch (_) {
        // Fallback if log service is not available
        rethrow;
      }
    }
  }

  /// Initialize iOS app
  static Future<void> _initializeIOSApp() async {
    try {
      // iOS apps don't need window initialization
      // System handles app lifecycle automatically
      try {
        final logService = serviceLocator<LogService>();
        logService.info('PlatformWindowManager', 'iOS app lifecycle managed by system');
      } catch (_) {
        // Log service may not be available during early initialization
      }
    } catch (e) {
      try {
        final logService = serviceLocator<LogService>();
        logService.error('PlatformWindowManager', 'Failed to initialize iOS app', e);
      } catch (_) {
        // Fallback if log service is not available
        rethrow;
      }
    }
  }

  /// Initialize Android app
  static Future<void> _initializeAndroidApp() async {
    try {
      // Android apps don't need window initialization
      // System handles app lifecycle automatically
      try {
        final logService = serviceLocator<LogService>();
        logService.info('PlatformWindowManager', 'Android app lifecycle managed by system');
      } catch (_) {
        // Log service may not be available during early initialization
      }
    } catch (e) {
      try {
        final logService = serviceLocator<LogService>();
        logService.error('PlatformWindowManager', 'Failed to initialize Android app', e);
      } catch (_) {
        // Fallback if log service is not available
        rethrow;
      }
    }
  }

  /// Handle application close request
  ///
  /// DESCRIPTION:
  ///     Handles application close requests in a platform-appropriate manner.
  ///     Ensures proper cleanup and graceful shutdown.
  ///
  /// RETURNS:
  ///     Future<void> - Completes when close handling is finished
  static Future<void> handleAppClose() async {
    if (Platform.isIOS || Platform.isMacOS) {
      await _handleAppleAppClose();
    } else if (Platform.isWindows || Platform.isLinux) {
      await _handleDesktopAppClose();
    }
  }

  /// Handle Apple platform app close (iOS/macOS)
  static Future<void> _handleAppleAppClose() async {
    try {
      // Use platform-native app exit mechanism
      await SystemNavigator.pop();
    } catch (e) {
      try {
        final logService = serviceLocator<LogService>();
        logService.error('PlatformWindowManager', 'Failed to close Apple app', e);
      } catch (_) {
        // Log service may not be available during shutdown
      }
      // Force exit as fallback
      exit(0);
    }
  }

  /// Handle desktop platform app close (Windows/Linux)
  static Future<void> _handleDesktopAppClose() async {
    try {
      // Use window_manager directly for close operations
      await windowManager.close();
    } catch (e) {
      try {
        final logService = serviceLocator<LogService>();
        logService.error('PlatformWindowManager', 'Failed to close desktop app', e);
      } catch (_) {
        // Log service may not be available during shutdown
      }
      // Force exit as fallback
      exit(0);
    }
  }

  /// Minimize application window
  ///
  /// DESCRIPTION:
  ///     Minimizes the application window on supported platforms.
  ///     iOS ignores this operation as it doesn't support window minimization.
  ///
  /// RETURNS:
  ///     Future<void> - Completes when minimize operation is finished
  static Future<void> minimizeWindow() async {
    if (Platform.isIOS) {
      // iOS doesn't support window minimization
      return;
    }

    try {
      await windowManager.minimize();
    } catch (e) {
      try {
        final logService = serviceLocator<LogService>();
        logService.error('PlatformWindowManager', 'Failed to minimize window', e);
      } catch (_) {
        // Log service may not be available
      }
    }
  }

  /// Maximize application window
  ///
  /// DESCRIPTION:
  ///     Maximizes the application window on supported platforms.
  ///     iOS ignores this operation as it doesn't support window maximization.
  ///
  /// RETURNS:
  ///     Future<void> - Completes when maximize operation is finished
  static Future<void> maximizeWindow() async {
    if (Platform.isIOS) {
      // iOS doesn't support window maximization
      return;
    }

    try {
      await windowManager.maximize();
    } catch (e) {
      try {
        final logService = serviceLocator<LogService>();
        logService.error('PlatformWindowManager', 'Failed to maximize window', e);
      } catch (_) {
        // Log service may not be available
      }
    }
  }

  /// Show application window
  ///
  /// DESCRIPTION:
  ///     Shows the application window on supported platforms.
  ///     iOS brings the app to foreground.
  ///
  /// RETURNS:
  ///     Future<void> - Completes when show operation is finished
  static Future<void> showWindow() async {
    try {
      if (Platform.isIOS || Platform.isMacOS) {
        // On Apple platforms, bring app to foreground
        await SystemChannels.platform.invokeMethod('SystemNavigator.routeUpdated');
      } else {
        await windowManager.show();
      }
    } catch (e) {
      try {
        final logService = serviceLocator<LogService>();
        logService.error('PlatformWindowManager', 'Failed to show window', e);
      } catch (_) {
        // Log service may not be available
      }
    }
  }

  /// Hide application window
  ///
  /// DESCRIPTION:
  ///     Hides the application window on supported platforms.
  ///     iOS moves the app to background.
  ///
  /// RETURNS:
  ///     Future<void> - Completes when hide operation is finished
  static Future<void> hideWindow() async {
    try {
      if (Platform.isIOS || Platform.isMacOS) {
        // On Apple platforms, move app to background
        await SystemNavigator.pop();
      } else {
        await windowManager.hide();
      }
    } catch (e) {
      try {
        final logService = serviceLocator<LogService>();
        logService.error('PlatformWindowManager', 'Failed to hide window', e);
      } catch (_) {
        // Log service may not be available
      }
    }
  }

  /// Check if window operations are supported
  ///
  /// DESCRIPTION:
  ///     Checks whether window operations (minimize, maximize, etc.)
  ///     are supported on the current platform.
  ///
  /// RETURNS:
  ///     bool - True if window operations are supported
  static bool get supportsWindowOperations {
    return Platform.isWindows || Platform.isLinux || Platform.isMacOS;
  }

  /// Check if the platform supports custom window frames
  ///
  /// DESCRIPTION:
  ///     Checks whether custom window frames are supported
  ///     on the current platform.
  ///
  /// RETURNS:
  ///     bool - True if custom window frames are supported
  static bool get supportsCustomWindowFrame {
    return Platform.isWindows || Platform.isLinux;
  }

  /// Get platform-specific window configuration
  ///
  /// DESCRIPTION:
  ///     Returns platform-specific window configuration settings.
  ///     iOS App on macOS is treated as desktop for window operations.
  ///
  /// RETURNS:
  ///     Map<String, dynamic> - Platform-specific configuration
  static Map<String, dynamic> get platformConfig {
    final shouldUseDesktop = PlatformServiceFactory.shouldUseDesktopLayoutSync();

    if ((Platform.isIOS && !shouldUseDesktop) || Platform.isAndroid) {
      // Native iOS and Android - no window operations
      return {
        'supportsWindowOperations': false,
        'supportsCustomFrame': false,
        'supportsMinimize': false,
        'supportsMaximize': false,
        'supportsResize': false,
      };
    } else if (Platform.isMacOS || shouldUseDesktop) {
      // macOS or iOS App on macOS - system window frame
      return {
        'supportsWindowOperations': true,
        'supportsCustomFrame': false,
        'supportsMinimize': true,
        'supportsMaximize': true,
        'supportsResize': true,
      };
    } else {
      // Windows/Linux - custom window frame
      return {
        'supportsWindowOperations': true,
        'supportsCustomFrame': true,
        'supportsMinimize': true,
        'supportsMaximize': true,
        'supportsResize': true,
      };
    }
  }

  /// Dispose platform window manager
  ///
  /// DESCRIPTION:
  ///     Cleans up platform window manager resources.
  ///
  /// RETURNS:
  ///     Future<void> - Completes when disposal is finished
  static Future<void> dispose() async {
    try {
      _windowService = null;
      _isInitialized = false;
    } catch (e) {
      try {
        final logService = serviceLocator<LogService>();
        logService.error('PlatformWindowManager', 'Failed to dispose platform window manager', e);
      } catch (_) {
        // Log service may not be available during disposal
      }
    }
  }
}
