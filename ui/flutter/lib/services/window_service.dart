// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      window_service.dart
//  *
//  * DESCRIPTION :    窗口管理服务，负责应用窗口的初始化、配置和样式设置，
//  *                  提供跨平台的窗口管理功能
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  ******************************************************************************/

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:window_manager/window_manager.dart';
import '../utils/constants.dart';
import 'log_service.dart';

/// WindowService
///
/// PURPOSE:
///     窗口管理服务，负责应用窗口的初始化、配置和样式设置
///
/// FEATURES:
///     - 跨平台窗口配置：支持Windows、Linux、macOS平台
///     - 窗口样式设置：无边框、固定大小、透明背景等
///     - 窗口行为控制：居中显示、防止关闭、可移动等
///     - 平台特定优化：针对不同平台的窗口特性优化
///
/// USAGE:
///     在应用启动时调用initializeWindow()方法初始化窗口配置
class WindowService {
  final LogService _logService;

  /// WindowService构造函数
  ///
  /// DESCRIPTION:
  ///     创建窗口服务实例
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  WindowService({required LogService logService}) : _logService = logService;

  /// initializeWindow
  ///
  /// DESCRIPTION:
  ///     初始化应用窗口配置，设置窗口大小、样式和行为
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 窗口初始化失败时抛出异常
  Future<void> initializeWindow() async {
    // 只在桌面平台初始化窗口管理器
    if (!_isDesktopPlatform()) {
      _logService.info(kLogModuleWindow, 'Skipping window initialization on non-desktop platform');
      return;
    }

    try {
      await windowManager.ensureInitialized();
      _logService.info(kLogModuleWindow, 'Window manager initialized');

      // 配置窗口选项
      final windowOptions = WindowOptions(
        size: const Size(kWindowWidth, kWindowHeight),
        minimumSize: const Size(kWindowWidth, kWindowHeight),
        maximumSize: const Size(kWindowWidth, kWindowHeight),
        center: true,
        backgroundColor: Colors.transparent,
        skipTaskbar: false,
        // 只在Windows/Linux隐藏标题栏，macOS使用系统标题栏
        titleBarStyle: (Platform.isWindows || Platform.isLinux)
            ? TitleBarStyle.hidden
            : TitleBarStyle.normal,
        windowButtonVisibility: !(Platform.isWindows || Platform.isLinux),
        title: kAppTitle,
      );

      // 设置窗口关闭事件处理
      windowManager.setPreventClose(true);

      await windowManager.waitUntilReadyToShow(windowOptions, () async {
        await _showAndConfigureWindow();
      });

      _logService.info(kLogModuleWindow, 'Window configuration completed');
    } catch (e) {
      _logService.error(kLogModuleWindow, 'Failed to initialize window: $e', e);
      rethrow;
    }
  }

  /// _showAndConfigureWindow
  ///
  /// DESCRIPTION:
  ///     显示窗口并进行平台特定的配置
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _showAndConfigureWindow() async {
    await windowManager.show();
    await windowManager.focus();
    
    _logService.info(kLogModuleWindow, 'Window shown and focused');

    // Windows平台特定配置
    if (Platform.isWindows) {
      await _configureWindowsSpecific();
    }
  }

  /// _configureWindowsSpecific
  ///
  /// DESCRIPTION:
  ///     Windows平台特定的窗口配置
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _configureWindowsSpecific() async {
    try {
      // 只在Windows平台隐藏标题栏
      if (Platform.isWindows) {
        await windowManager.setTitleBarStyle(
          TitleBarStyle.hidden,
          windowButtonVisibility: false,
        );
      }
      await windowManager.setBackgroundColor(Colors.transparent);
      await windowManager.setHasShadow(false);
      await windowManager.setResizable(false);
      await windowManager.setFullScreen(false);
      await windowManager.setAlwaysOnTop(false);
      await windowManager.setSkipTaskbar(false);
      // 移除setMovable调用，因为当前版本的window_manager不支持此方法
      // await windowManager.setMovable(true);

      _logService.info(kLogModuleWindow, 'Windows-specific window configuration applied');
    } catch (e) {
      _logService.warning(kLogModuleWindow, 'Failed to apply Windows-specific configuration: $e');
    }
  }

  /// _isDesktopPlatform
  ///
  /// DESCRIPTION:
  ///     检查当前平台是否为桌面平台
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     bool - 如果是桌面平台返回true，否则返回false
  bool _isDesktopPlatform() {
    return Platform.isWindows || Platform.isLinux || Platform.isMacOS;
  }
}
