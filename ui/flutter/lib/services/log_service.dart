/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      log_service.dart
///
/// DESCRIPTION :    日志服务适配器，提供向后兼容的日志接口，
///                  内部使用跨平台日志服务实现，支持iOS/macOS和Windows/Linux
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create
///                  27/06/2025 adapt to use cross-platform log service

import 'dart:async';
import 'dart:io';

import '../models/log_entry.dart';
import '../utils/constants.dart';
import 'platform/platform_service_factory.dart';
import 'platform/cross_platform_log_service.dart' as platform;

/// LogService
///
/// PURPOSE:
///     日志服务适配器，提供向后兼容的日志接口，内部使用跨平台日志服务
///
/// FEATURES:
///     - 向后兼容：保持现有API不变
///     - 跨平台支持：iOS/macOS使用OSLog，Windows/Linux使用文件日志
///     - 自动平台检测：根据运行平台选择最优实现
///     - 内存管理：限制内存中日志条数，避免内存溢出
///     - 实时流：提供日志流，支持实时监听日志事件
///     - 日志筛选：支持按级别、模块、关键词筛选日志
///     - 日志导出：支持导出日志到JSON文件
///
/// USAGE:
///     // 创建日志服务实例
///     final logService = LogService.production();
///
///     // 记录不同级别的日志
///     logService.debug('Module', 'Debug message');
///     logService.info('Module', 'Info message');
///     logService.warning('Module', 'Warning message');
///     logService.error('Module', 'Error message', error);
///
///     // 监听日志流
///     logService.logStream.listen((entry) => print(entry.message));
class LogService {
  // 跨平台日志服务实例
  late platform.CrossPlatformLogService _platformLogService;

  // 向后兼容的内存日志存储
  final List<LogEntry> _logs = [];
  final int _maxLogSize = 1000; // 内存中保存的最大日志条数
  final StreamController<LogEntry> _logStreamController = StreamController<LogEntry>.broadcast();



  /// _convertLogLevel
  ///
  /// DESCRIPTION:
  ///     转换旧的LogLevel到新的platform.LogLevel
  ///
  /// PARAMETERS:
  ///     level - 旧的LogLevel枚举值
  ///
  /// RETURNS:
  ///     platform.LogLevel - 新的平台LogLevel枚举值
  static platform.LogLevel _convertLogLevel(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return platform.LogLevel.debug;
      case LogLevel.info:
        return platform.LogLevel.info;
      case LogLevel.warning:
        return platform.LogLevel.warning;
      case LogLevel.error:
        return platform.LogLevel.error;
    }
  }

  /// _convertFromPlatformLogLevel
  ///
  /// DESCRIPTION:
  ///     转换platform.LogLevel到旧的LogLevel
  ///
  /// PARAMETERS:
  ///     level - platform.LogLevel枚举值
  ///
  /// RETURNS:
  ///     LogLevel - 旧的LogLevel枚举值
  static LogLevel _convertFromPlatformLogLevel(platform.LogLevel level) {
    switch (level) {
      case platform.LogLevel.debug:
        return LogLevel.debug;
      case platform.LogLevel.info:
        return LogLevel.info;
      case platform.LogLevel.warning:
        return LogLevel.warning;
      case platform.LogLevel.error:
        return LogLevel.error;
      case platform.LogLevel.critical:
        return LogLevel.error; // 映射critical到error
    }
  }

  /// logStream getter
  ///
  /// DESCRIPTION:
  ///     获取日志流，用于实时监听日志事件
  ///
  /// RETURNS:
  ///     Stream<LogEntry> - 日志事件流
  Stream<LogEntry> get logStream => _logStreamController.stream;

  /// logs getter
  ///
  /// DESCRIPTION:
  ///     获取所有内存中的日志条目（只读）
  ///
  /// RETURNS:
  ///     List<LogEntry> - 不可修改的日志列表
  List<LogEntry> get logs => List.unmodifiable(_logs);

  /// currentLogLevel getter
  ///
  /// DESCRIPTION:
  ///     获取当前日志级别
  ///
  /// RETURNS:
  ///     LogLevel - 当前日志级别
  LogLevel get currentLogLevel => _convertFromPlatformLogLevel(_platformLogService.getLogLevel());

  /// setPlatformLogService
  ///
  /// DESCRIPTION:
  ///     设置跨平台日志服务实例（用于依赖注入）
  ///
  /// PARAMETERS:
  ///     service - 跨平台日志服务实例
  ///
  /// RETURNS:
  ///     void
  set platformLogService(platform.CrossPlatformLogService service) {
    _platformLogService = service;
  }

  /// setLogLevel
  ///
  /// DESCRIPTION:
  ///     设置日志级别，只有达到或超过此级别的日志才会被记录
  ///
  /// PARAMETERS:
  ///     level - 要设置的日志级别
  ///
  /// RETURNS:
  ///     void
  void setLogLevel(LogLevel level) {
    _platformLogService.setLogLevel(_convertLogLevel(level));
    info('LogService', 'Log level set to: ${level.toString().split('.').last.toUpperCase()}');
  }

  /// LogService构造函数
  ///
  /// DESCRIPTION:
  ///     创建日志服务实例，使用跨平台日志服务
  ///
  /// PARAMETERS:
  ///     initialLogLevel - 可选的初始日志级别，默认为info
  LogService({LogLevel? initialLogLevel}) {
    _platformLogService = PlatformServiceFactory.createLogService();
    if (initialLogLevel != null) {
      _platformLogService.setLogLevel(_convertLogLevel(initialLogLevel));
    }
    _initializeAsync();
  }

  /// _initializeAsync
  ///
  /// DESCRIPTION:
  ///     异步初始化跨平台日志服务
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _initializeAsync() {
    _platformLogService.initialize().catchError((error) {
      // 静默处理初始化错误，避免循环依赖
    });
  }

  /// LogService.withLevel工厂方法
  ///
  /// DESCRIPTION:
  ///     创建带有指定日志级别的LogService实例
  ///
  /// PARAMETERS:
  ///     level - 指定的日志级别
  ///
  /// RETURNS:
  ///     LogService - 日志服务实例
  factory LogService.withLevel(LogLevel level) {
    return LogService(initialLogLevel: level);
  }

  /// LogService.production工厂方法
  ///
  /// DESCRIPTION:
  ///     创建生产环境LogService实例（默认info级别）
  ///
  /// RETURNS:
  ///     LogService - 生产环境日志服务实例
  factory LogService.production() {
    return LogService(initialLogLevel: LogLevel.info);
  }

  /// LogService.development工厂方法
  ///
  /// DESCRIPTION:
  ///     创建开发环境LogService实例（debug级别）
  ///
  /// RETURNS:
  ///     LogService - 开发环境日志服务实例
  factory LogService.development() {
    return LogService(initialLogLevel: LogLevel.debug);
  }



  /// debug
  ///
  /// DESCRIPTION:
  ///     记录调试级别日志，用于详细的调试信息
  ///
  /// PARAMETERS:
  ///     module - 日志模块名称
  ///     message - 日志消息内容
  ///
  /// RETURNS:
  ///     void
  void debug(String module, String message) {
    _platformLogService.debug(module, message);
    _addToMemoryLog(LogLevel.debug, module, message);
  }

  /// info
  ///
  /// DESCRIPTION:
  ///     记录信息级别日志，用于一般的信息记录
  ///
  /// PARAMETERS:
  ///     module - 日志模块名称
  ///     message - 日志消息内容
  ///
  /// RETURNS:
  ///     void
  void info(String module, String message) {
    _platformLogService.info(module, message);
    _addToMemoryLog(LogLevel.info, module, message);
  }

  /// warning
  ///
  /// DESCRIPTION:
  ///     记录警告级别日志，用于警告信息
  ///
  /// PARAMETERS:
  ///     module - 日志模块名称
  ///     message - 日志消息内容
  ///
  /// RETURNS:
  ///     void
  void warning(String module, String message) {
    _platformLogService.warning(module, message);
    _addToMemoryLog(LogLevel.warning, module, message);
  }

  /// error
  ///
  /// DESCRIPTION:
  ///     记录错误级别日志，用于错误信息和异常
  ///
  /// PARAMETERS:
  ///     module - 日志模块名称
  ///     message - 日志消息内容
  ///     error - 可选的错误对象或异常信息
  ///
  /// RETURNS:
  ///     void
  void error(String module, String message, [dynamic error]) {
    _platformLogService.error(module, message, error);
    final errorMsg = error != null ? '$message: $error' : message;
    _addToMemoryLog(LogLevel.error, module, errorMsg);
  }

  /// _addToMemoryLog
  ///
  /// DESCRIPTION:
  ///     添加日志条目到内存存储，用于向后兼容
  ///
  /// PARAMETERS:
  ///     level - 日志级别
  ///     module - 日志模块名称
  ///     message - 日志消息内容
  ///
  /// RETURNS:
  ///     void
  void _addToMemoryLog(LogLevel level, String module, String message) {
    final entry = LogEntry(
      level: level,
      message: message,
      module: module,
    );

    _logs.add(entry);

    // 限制内存中的日志大小
    if (_logs.length > _maxLogSize) {
      _logs.removeAt(0);
    }

    // 发送到日志流
    _logStreamController.add(entry);
  }



  /// getLogFilePath
  ///
  /// DESCRIPTION:
  ///     获取当前日志文件的完整路径
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<String> - 日志文件路径
  Future<String> getLogFilePath() async {
    try {
      final logFiles = await _platformLogService.getLogFiles();
      return logFiles.isNotEmpty ? logFiles.first : '';
    } catch (e) {
      return '';
    }
  }

  /// getLogFiles
  ///
  /// DESCRIPTION:
  ///     获取所有日志文件列表
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<List<File>> - 日志文件列表
  Future<List<File>> getLogFiles() async {
    try {
      final logFilePaths = await _platformLogService.getLogFiles();
      return logFilePaths.map((path) => File(path)).toList();
    } catch (e) {
      return [];
    }
  }

  /// clearLogs
  ///
  /// DESCRIPTION:
  ///     清除内存中的所有日志条目
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void clearLogs() {
    _logs.clear();
    // 异步清除平台日志，但不等待结果
    _platformLogService.initialize().then((_) {
      _platformLogService.clearLogs();
    }).catchError((e) {
      // 忽略错误
    });
    info('LogService', 'Logs cleared');
  }

  /// filterByLevel
  ///
  /// DESCRIPTION:
  ///     按日志级别筛选日志条目
  ///
  /// PARAMETERS:
  ///     level - 要筛选的日志级别
  ///
  /// RETURNS:
  ///     List<LogEntry> - 筛选后的日志列表
  List<LogEntry> filterByLevel(LogLevel level) {
    return _logs.where((log) => log.level == level).toList();
  }

  /// filterByModule
  ///
  /// DESCRIPTION:
  ///     按模块名称筛选日志条目
  ///
  /// PARAMETERS:
  ///     module - 要筛选的模块名称
  ///
  /// RETURNS:
  ///     List<LogEntry> - 筛选后的日志列表
  List<LogEntry> filterByModule(String module) {
    return _logs.where((log) => log.module == module).toList();
  }

  /// search
  ///
  /// DESCRIPTION:
  ///     按关键词搜索日志条目（搜索消息和模块名称）
  ///
  /// PARAMETERS:
  ///     keyword - 搜索关键词
  ///
  /// RETURNS:
  ///     List<LogEntry> - 搜索结果日志列表
  List<LogEntry> search(String keyword) {
    final lowerKeyword = keyword.toLowerCase();
    return _logs.where((log) =>
      log.message.toLowerCase().contains(lowerKeyword) ||
      log.module.toLowerCase().contains(lowerKeyword)
    ).toList();
  }

  /// exportLogs
  ///
  /// DESCRIPTION:
  ///     导出所有日志到JSON文件
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<String> - 导出文件的路径
  ///
  /// THROWS:
  ///     Exception - 导出过程中发生错误时抛出异常
  Future<String> exportLogs() async {
    try {
      return await _platformLogService.exportLogs(format: 'json');
    } catch (e) {
      error('LogService', 'Failed to export logs', e);
      rethrow;
    }
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     释放日志服务资源，关闭流控制器和跨平台日志服务
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void dispose() async {
    await _logStreamController.close();
    await _platformLogService.dispose();
  }
}
