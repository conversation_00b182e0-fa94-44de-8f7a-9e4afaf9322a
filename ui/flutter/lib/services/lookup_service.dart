// Copyright (c) 2024 Panabit Inc. All rights reserved.

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

import '../utils/api_exception.dart';
import '../services/log_service.dart';
import '../core/dependency_injection.dart';

/// LookupError
///
/// PURPOSE:
///     Lookup服务错误响应数据模型，包含错误码、消息和详细信息
///
/// FEATURES:
///     - 解析lookup服务返回的错误响应
///     - 提供结构化的错误信息访问
///     - 支持错误码、消息、详情和字段信息
class LookupError {
  final int code;
  final String message;
  final String detail;
  final String field;

  LookupError({
    required this.code,
    required this.message,
    required this.detail,
    required this.field,
  });

  /// 从JSON创建LookupError实例
  factory LookupError.fromJson(Map<String, dynamic> json) {
    return LookupError(
      code: json['code'] as int? ?? 0,
      message: json['message'] as String? ?? '',
      detail: json['detail'] as String? ?? '',
      field: json['field'] as String? ?? '',
    );
  }

  /// 转换为字符串表示
  @override
  String toString() {
    return 'LookupError(code: $code, message: $message, detail: $detail, field: $field)';
  }
}

/// LookupResponse
///
/// PURPOSE:
///     Lookup服务响应数据模型，包含服务器地址和类型信息，支持成功和错误响应
///
/// FEATURES:
///     - 解析lookup服务返回的JSON响应（成功和错误格式）
///     - 验证响应数据的有效性
///     - 提供类型安全的数据访问
///     - 支持错误码和错误详情的解析
class LookupResponse {
  final bool success;
  final String type;
  final String address;
  final String domain;
  final String serverlistaddress;  // 新增：服务器拼接好的serverlist地址
  final String timestamp;
  final int latency;

  // 错误响应字段
  final LookupError? error;

  LookupResponse({
    required this.success,
    required this.type,
    required this.address,
    required this.domain,
    required this.serverlistaddress,
    required this.timestamp,
    required this.latency,
    this.error,
  });

  /// 从JSON创建LookupResponse实例
  factory LookupResponse.fromJson(Map<String, dynamic> json) {
    final success = json['success'] as bool? ?? false;

    if (success) {
      // 成功响应格式
      final data = json['data'] as Map<String, dynamic>?;
      if (data == null) {
        throw ApiException('Invalid lookup response: missing data field', 1003, 'invalid_response');
      }

      return LookupResponse(
        success: success,
        type: data['type'] as String? ?? '',
        address: data['address'] as String? ?? '',
        domain: data['domain'] as String? ?? '',
        serverlistaddress: data['serverlistaddress'] as String? ?? '',
        timestamp: data['timestamp'] as String? ?? '',
        latency: data['latency'] as int? ?? 0,
      );
    } else {
      // 错误响应格式
      final errorData = json['error'] as Map<String, dynamic>?;
      if (errorData == null) {
        throw ApiException('Invalid error response: missing error field', 1003, 'invalid_response');
      }

      final error = LookupError.fromJson(errorData);
      return LookupResponse(
        success: success,
        type: '',
        address: '',
        domain: '',
        serverlistaddress: '',
        timestamp: '',
        latency: 0,
        error: error,
      );
    }
  }

  /// 验证响应是否有效
  bool isValid() {
    return success && 
           type.isNotEmpty && 
           address.isNotEmpty && 
           domain.isNotEmpty;
  }

  /// 是否为serverlist类型
  bool isServerListType() {
    return type.toLowerCase() == 'serverlist';
  }

  /// 是否为controller类型
  bool isControllerType() {
    return type.toLowerCase() == 'controller';
  }
}

/// LookupResult
///
/// PURPOSE:
///     Lookup查询结果，包含服务器列表URL和完整域名信息
///
/// FEATURES:
///     - 包含拼接好的服务器列表URL
///     - 包含lookup返回的完整域名（用于域名替换）
///     - 包含原始用户输入域名（用于比较）
class LookupResult {
  /// 拼接好的服务器列表URL
  final String serverListUrl;

  /// lookup返回的完整域名
  final String completeDomain;

  /// 用户原始输入的域名
  final String originalDomain;

  /// 是否为模糊匹配（需要替换域名）
  final bool isFuzzyMatch;

  const LookupResult({
    required this.serverListUrl,
    required this.completeDomain,
    required this.originalDomain,
    required this.isFuzzyMatch,
  });

  @override
  String toString() {
    return 'LookupResult(serverListUrl: $serverListUrl, completeDomain: $completeDomain, originalDomain: $originalDomain, isFuzzyMatch: $isFuzzyMatch)';
  }
}

/// LookupService
///
/// PURPOSE:
///     Lookup服务类，负责向lookup服务器查询客户域对应的服务器地址
///
/// FEATURES:
///     - 向固定的lookup服务发起HTTP请求
///     - 直接使用lookup服务器返回的拼接好的serverlist地址
///     - 统一的错误处理和超时管理
///     - 支持网络错误重试机制
///     - 支持模糊匹配和域名自动替换
///
/// USAGE:
///     final lookupService = LookupService();
///     final result = await lookupService.getServerListUrl('research');
///     if (result.isFuzzyMatch) {
///       // 需要替换用户输入的域名为完整域名
///       domainController.text = result.completeDomain;
///     }
class LookupService {
  
  // ============================================================================
  // CONFIGURATION
  // ============================================================================
  
  /// Lookup服务的固定地址 (临时使用HTTP测试)
  static const String _lookupBaseUrl = 'https://uctest.unisase.cn:9000';
  
  /// 请求超时时间
  static const Duration _requestTimeout = Duration(seconds: 5);
  
  /// 重试次数
  static const int _maxRetries = 2;
  
  // ============================================================================
  // DEPENDENCIES
  // ============================================================================
  
  LogService get _logService => serviceLocator<LogService>();
  
  // ============================================================================
  // PUBLIC METHODS
  // ============================================================================
  
  /// getServerListUrl
  ///
  /// DESCRIPTION:
  ///     根据客户域名查询并拼接最终的服务器列表URL，支持模糊匹配和域名替换
  ///
  /// PARAMETERS:
  ///     clientDomain - 客户域名（如：research.staff.unisase 或 research）
  ///
  /// RETURNS:
  ///     Future<LookupResult> - 包含服务器列表URL和域名信息的查询结果
  ///
  /// THROWS:
  ///     ApiException - 查询失败或响应无效时抛出异常
  Future<LookupResult> getServerListUrl(String clientDomain) async {
    // 添加直接的debug输出，确保能在flutter run中看到
    print('🔍 LookupService: getServerListUrl called with domain: $clientDomain');

    if (clientDomain.trim().isEmpty) {
      print('❌ LookupService: Client domain is empty');
      _logService.error('LookupService', 'Client domain cannot be empty');
      throw ApiException('Domain parameter is required', 1103, 'domain_required');
    }

    final startTime = DateTime.now();
    final trimmedDomain = clientDomain.trim();
    print('🔍 LookupService: Starting lookup for domain: $trimmedDomain');
    print('🔍 LookupService: URL: $_lookupBaseUrl, timeout: ${_requestTimeout.inSeconds}s, retries: $_maxRetries');

    _logService.info('LookupService', 'Starting lookup for client domain: $trimmedDomain');
    _logService.debug('LookupService', 'Lookup service URL: $_lookupBaseUrl, timeout: ${_requestTimeout.inSeconds}s, max_retries: $_maxRetries');

    try {
      // 1. 向lookup服务查询
      _logService.debug('LookupService', 'Calling _queryLookupService for domain: $trimmedDomain');
      final queryStartTime = DateTime.now();
      final lookupResponse = await _queryLookupService(trimmedDomain);
      final queryDuration = DateTime.now().difference(queryStartTime).inMilliseconds;

      _logService.info('LookupService', 'Lookup query completed in ${queryDuration}ms');
      _logService.debug('LookupService', 'Lookup response: type=${lookupResponse.type}, address=${lookupResponse.address}, domain=${lookupResponse.domain}');

      // 2. 检查响应是否为错误
      _logService.debug('LookupService', 'Checking lookup response status');
      if (!lookupResponse.success && lookupResponse.error != null) {
        final error = lookupResponse.error!;
        _logService.error('LookupService', 'Lookup service returned error: ${error.toString()}');

        // 将lookup API的错误码映射到我们的错误码体系
        int mappedErrorCode;
        String errorType;

        switch (error.code) {
          case 1001:
            // 域名查找失败（域名不存在）
            mappedErrorCode = 1101;
            errorType = 'domain_not_found';
            break;
          case 1002:
            // 无效域名（域名参数为空或格式错误）
            mappedErrorCode = 1102;
            errorType = 'domain_invalid';
            break;
          default:
            // 其他lookup错误
            mappedErrorCode = 1101;
            errorType = 'domain_lookup_failed';
            break;
        }

        throw ApiException(
          '${error.message}: ${error.detail}',
          mappedErrorCode,
          errorType
        );
      }

      // 3. 验证成功响应
      _logService.debug('LookupService', 'Validating lookup response');
      if (!lookupResponse.isValid()) {
        _logService.error('LookupService', 'Invalid lookup response received: ${lookupResponse.toString()}');
        throw ApiException('Invalid lookup response', 1003, 'invalid_response');
      }
      
      // 3. 根据类型处理响应
      _logService.debug('LookupService', 'Processing lookup response type: ${lookupResponse.type}');
      if (lookupResponse.isServerListType()) {
        // serverlist类型：直接使用lookup返回的serverlistaddress作为serverlist URL
        _logService.debug('LookupService', 'Using server list URL from lookup response');

        // 判断是否为模糊匹配（用户输入域名与lookup返回域名不同）
        final isFuzzyMatch = trimmedDomain != lookupResponse.domain;

        final totalDuration = DateTime.now().difference(startTime).inMilliseconds;
        _logService.info('LookupService', 'Successfully got server list URL in ${totalDuration}ms: ${lookupResponse.serverlistaddress}');
        _logService.info('LookupService', 'Domain match result - original: $trimmedDomain, complete: ${lookupResponse.domain}, fuzzy: $isFuzzyMatch');

        return LookupResult(
          serverListUrl: lookupResponse.serverlistaddress,  // 使用lookup返回的serverlistaddress
          completeDomain: lookupResponse.domain,
          originalDomain: trimmedDomain,
          isFuzzyMatch: isFuzzyMatch,
        );
      } else {
        // 非serverlist类型：当作域名无效处理（错误码1102）
        _logService.error('LookupService', 'Invalid service type received: ${lookupResponse.type}, expected: serverlist');
        throw ApiException('Invalid domain: unsupported service type ${lookupResponse.type}', 1102, 'domain_invalid');
      }

    } catch (e) {
      final totalDuration = DateTime.now().difference(startTime).inMilliseconds;
      _logService.error('LookupService', 'Failed to get server list URL for domain: $trimmedDomain after ${totalDuration}ms', e);

      // 添加详细的错误信息用于调试
      if (e is ApiException) {
        _logService.error('LookupService', 'ApiException details: code=${e.code}, type=${e.type}, message=${e.message}');
      }

      rethrow;
    }
  }
  
  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================
  
  /// _queryLookupService
  ///
  /// DESCRIPTION:
  ///     向lookup服务发起HTTP请求查询域名信息
  ///
  /// PARAMETERS:
  ///     domain - 客户域名
  ///
  /// RETURNS:
  ///     Future<LookupResponse> - lookup服务响应
  ///
  /// THROWS:
  ///     ApiException - 网络错误或响应解析失败时抛出异常
  Future<LookupResponse> _queryLookupService(String domain) async {
    final startTime = DateTime.now();
    final url = Uri.parse('$_lookupBaseUrl/lookup').replace(
      queryParameters: {'domain': domain},
    );

    _logService.info('LookupService', 'Sending lookup request to: $url for domain: $domain');

    ApiException? lastException;

    // 重试机制
    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      try {
        _logService.debug('LookupService', 'Attempt $attempt/$_maxRetries - Making HTTP GET request');
        print('🌐 LookupService: Attempt $attempt/$_maxRetries - Making HTTP GET request to: $url');
        final requestStartTime = DateTime.now();

        // 临时去掉自定义headers，测试是否是headers导致的问题
        final response = await http.get(url).timeout(_requestTimeout);

        print('✅ LookupService: HTTP request completed with status: ${response.statusCode}');

        final requestDuration = DateTime.now().difference(requestStartTime).inMilliseconds;
        _logService.info('LookupService', 'HTTP request completed in ${requestDuration}ms with status: ${response.statusCode}');

        _logService.debug('LookupService', 'Parsing JSON response body');
        final parseStartTime = DateTime.now();
        final responseData = json.decode(response.body) as Map<String, dynamic>;
        final parseDuration = DateTime.now().difference(parseStartTime).inMilliseconds;

        final totalDuration = DateTime.now().difference(startTime).inMilliseconds;
        _logService.info('LookupService', 'Response parsed successfully in ${parseDuration}ms (total: ${totalDuration}ms)');

        // 解析响应，无论HTTP状态码如何，都尝试解析JSON
        final lookupResponse = LookupResponse.fromJson(responseData);

        // 如果是错误响应，直接返回（会在上层处理错误）
        if (!lookupResponse.success) {
          _logService.warning('LookupService', 'Lookup service returned error response: ${lookupResponse.error?.toString()}');
        }

        return lookupResponse;
        
      } on TimeoutException {
        final attemptDuration = DateTime.now().difference(startTime).inMilliseconds;
        lastException = ApiException('Domain lookup timeout', 1104, 'domain_lookup_timeout');
        print('⏰ LookupService: TIMEOUT after ${attemptDuration}ms (attempt $attempt/$_maxRetries) - URL: $url');
        _logService.warning('LookupService', 'Lookup request timeout after ${attemptDuration}ms (attempt $attempt/$_maxRetries) - URL: $url');
      } on SocketException catch (e) {
        final attemptDuration = DateTime.now().difference(startTime).inMilliseconds;
        lastException = ApiException('Network error during domain lookup: ${e.message}', 1105, 'domain_lookup_network_error');
        print('🌐 LookupService: NETWORK ERROR after ${attemptDuration}ms (attempt $attempt/$_maxRetries) - ${e.message}');
        _logService.warning('LookupService', 'Network error after ${attemptDuration}ms (attempt $attempt/$_maxRetries) - URL: $url, Error: ${e.message}');
      } on FormatException catch (e) {
        final attemptDuration = DateTime.now().difference(startTime).inMilliseconds;
        lastException = ApiException('Invalid JSON response: ${e.message}', 1003, 'invalid_response');
        _logService.error('LookupService', 'JSON parsing error after ${attemptDuration}ms: ${e.message}');
        break; // JSON错误不需要重试
      } catch (e) {
        final attemptDuration = DateTime.now().difference(startTime).inMilliseconds;
        lastException = ApiException('Lookup service error: ${e.toString()}', 1003, 'unknown');
        print('🔥 LookupService: UNEXPECTED ERROR after ${attemptDuration}ms (attempt $attempt/$_maxRetries) - Type: ${e.runtimeType}, Error: $e');
        _logService.error('LookupService', 'Unexpected error after ${attemptDuration}ms (attempt $attempt/$_maxRetries) - URL: $url', e);
      }

      // 如果不是最后一次尝试，等待一段时间再重试
      if (attempt < _maxRetries) {
        final retryDelay = 500 * attempt;
        _logService.debug('LookupService', 'Waiting ${retryDelay}ms before retry');
        await Future.delayed(Duration(milliseconds: retryDelay));
      }
    }

    // 所有重试都失败了，抛出最后一个异常
    final totalDuration = DateTime.now().difference(startTime).inMilliseconds;
    print('❌ LookupService: ALL ATTEMPTS FAILED after ${totalDuration}ms - Final error: ${lastException?.message}');
    _logService.error('LookupService', 'All lookup attempts failed after ${totalDuration}ms');
    throw lastException!;
  }


}
