/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      reconnect_service.dart
///
/// DESCRIPTION :    重连管理服务，负责处理网络连接重连流程，包括断开连接、
///                  路由设置和重新连接的完整流程管理
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:async';

import 'log_service.dart';
import '../models/connection_status.dart';
import 'connection_manager.dart';
import '../core/app_state.dart';
import '../models/server.dart';

/// ReconnectService
///
/// PURPOSE:
///     重连管理服务，负责处理网络连接重连流程，复用现有的ConnectionManager逻辑
///
/// FEATURES:
///     - 统一重连流程：disconnect -> 路由设置 -> connect
///     - 重连状态管理：跟踪重连进度和状态
///     - 错误处理：处理重连过程中的各种错误情况
///     - 日志记录：详细记录重连过程的每个步骤
///     - 复用现有逻辑：使用ConnectionManager的connect/disconnect方法
///
/// USAGE:
///     通过依赖注入获取实例，调用handleReconnectRequired处理重连需求
class ReconnectService {
  /// 连接管理器实例，用于执行连接操作
  final ConnectionManager connectionManager;

  /// 日志服务实例，用于记录重连过程
  final LogService logService;

  /// 应用状态实例，用于获取当前连接状态
  final AppState appState;

  /// 重连状态标志
  bool _isReconnecting = false;

  /// 当前重连的服务器信息
  Server? _lastConnectedServer;

  /// 重连尝试次数
  int _reconnectAttempts = 0;

  /// 最大重连尝试次数
  static const int _maxReconnectAttempts = 3;

  /// ReconnectService构造函数
  ///
  /// DESCRIPTION:
  ///     创建重连服务实例
  ///
  /// PARAMETERS:
  ///     connectionManager - 连接管理器实例
  ///     logService - 日志服务实例
  ///     appState - 应用状态实例
  ReconnectService({
    required this.connectionManager,
    required this.logService,
    required this.appState,
  });

  /// handleReconnectRequired
  ///
  /// DESCRIPTION:
  ///     处理重连需求，执行完整的重连流程，支持最多3次重连尝试
  ///
  /// PARAMETERS:
  ///     reason - 重连原因
  ///     message - 详细消息
  ///     serverInfo - 可选的服务器信息，如果为null则使用最后连接的服务器
  ///
  /// RETURNS:
  ///     Future<bool> - 重连是否成功
  Future<bool> handleReconnectRequired(String reason, String message, {Server? serverInfo}) async {
    if (_isReconnecting) {
      logService.warning('ReconnectService', 'Reconnection already in progress, ignoring new request');
      return false;
    }

    _isReconnecting = true;

    // 如果这是一个新的重连请求（不是重试），重置计数器
    if (!message.contains('Retry after failed attempt')) {
      _reconnectAttempts = 0;
    }

    // 在开始重连流程前，保存当前选中的服务器信息
    if (serverInfo == null && appState.selectedServer != null) {
      _lastConnectedServer = appState.selectedServer;
      logService.info('ReconnectService', 'Saved current selected server for reconnection: ${_lastConnectedServer!.name}');
    }

    try {
      // 执行最多3次重连尝试
      for (int attempt = 1; attempt <= _maxReconnectAttempts; attempt++) {
        _reconnectAttempts = attempt;

        logService.info('ReconnectService', '=== STARTING RECONNECTION PROCESS ===');
        logService.info('ReconnectService', 'Reason: $reason');
        logService.info('ReconnectService', 'Message: $message');
        logService.info('ReconnectService', 'Attempt: $attempt/$_maxReconnectAttempts');
        logService.info('ReconnectService', '=====================================');

        // 步骤1: 断开当前连接（仅在第一次尝试时执行）
        if (attempt == 1) {
          final disconnectSuccess = await _performDisconnect();
          if (!disconnectSuccess) {
            logService.warning('ReconnectService', 'Disconnect failed, but continuing with reconnection');
          }
        }

        // 步骤2: 等待网络稳定
        await _waitForNetworkStability();

        // 步骤3: 设置路由（如果需要）
        await _setupRouting();

        // 步骤4: 重新连接
        final reconnectSuccess = await _performReconnect(serverInfo);

        if (reconnectSuccess) {
          logService.info('ReconnectService', 'Reconnection completed successfully on attempt $attempt');
          _reconnectAttempts = 0; // 重置重连计数
          return true;
        } else {
          logService.error('ReconnectService', 'Reconnection attempt $attempt failed');

          // 如果还有重连机会，等待短暂时间后继续下一次尝试
          if (attempt < _maxReconnectAttempts) {
            logService.info('ReconnectService', 'Waiting before next retry attempt...');
            await Future.delayed(const Duration(seconds: 2));
          } else {
            logService.error('ReconnectService', 'Maximum reconnection attempts reached, giving up');
            _reconnectAttempts = 0; // 重置计数器
            return false;
          }
        }
      }

      // 如果所有尝试都失败了
      logService.error('ReconnectService', 'All reconnection attempts failed');
      _reconnectAttempts = 0;
      return false;

    } catch (e) {
      logService.error('ReconnectService', 'Unexpected error during reconnection: $e');
      _reconnectAttempts = 0;
      return false;
    } finally {
      _isReconnecting = false;
    }
  }

  /// _performDisconnect
  ///
  /// DESCRIPTION:
  ///     执行断开连接操作
  ///
  /// RETURNS:
  ///     Future<bool> - 断开是否成功
  Future<bool> _performDisconnect() async {
    try {
      logService.info('ReconnectService', 'Step 1: Disconnecting current connection...');
      final success = await connectionManager.disconnect();
      if (success) {
        logService.info('ReconnectService', 'Step 1 completed: Disconnect successful');
        return true;
      } else {
        logService.warning('ReconnectService', 'Step 1 warning: Disconnect returned false');
        return false;
      }
    } catch (e) {
      logService.error('ReconnectService', 'Step 1 failed: Disconnect error - $e');
      return false;
    }
  }

  /// _waitForNetworkStability
  ///
  /// DESCRIPTION:
  ///     等待网络稳定，给网络接口变更一些时间完成
  ///
  /// RETURNS:
  ///     Future<void>
  Future<void> _waitForNetworkStability() async {
    logService.info('ReconnectService', 'Step 2: Waiting for network stability...');
    await Future.delayed(const Duration(seconds: 3));
    logService.info('ReconnectService', 'Step 2 completed: Network stability wait finished');
  }

  /// _setupRouting
  ///
  /// DESCRIPTION:
  ///     设置路由配置（如果需要）
  ///
  /// RETURNS:
  ///     Future<void>
  Future<void> _setupRouting() async {
    logService.info('ReconnectService', 'Step 3: Setting up routing...');
    // 这里可以添加具体的路由设置逻辑
    // 目前只是占位符，实际实现可能需要根据具体需求调整
    logService.info('ReconnectService', 'Step 3 completed: Routing setup finished');
  }

  /// _performReconnect
  ///
  /// DESCRIPTION:
  ///     执行重新连接操作，等待实际连接结果而不是仅依赖connect方法返回值
  ///
  /// PARAMETERS:
  ///     serverInfo - 可选的服务器信息
  ///
  /// RETURNS:
  ///     Future<bool> - 重连是否成功
  Future<bool> _performReconnect(Server? serverInfo) async {
    try {
      logService.info('ReconnectService', 'Step 4: Attempting to reconnect...');

      // 使用提供的服务器信息、最后连接的服务器或当前选中的服务器
      final targetServer = serverInfo ?? _lastConnectedServer ?? appState.selectedServer;

      if (targetServer == null) {
        logService.error('ReconnectService', 'Step 4 failed: No server information available for reconnection');
        logService.error('ReconnectService', 'Debug: serverInfo=$serverInfo, _lastConnectedServer=$_lastConnectedServer, appState.selectedServer=${appState.selectedServer}');
        return false;
      }

      // 确保目标服务器被选中
      appState.selectServer(targetServer, source: ServerSelectionSource.systemDefault);
      logService.info('ReconnectService', 'Selected server for reconnection: ${targetServer.name}');

      // 执行连接请求
      final connectRequestSuccess = await connectionManager.connect();

      if (!connectRequestSuccess) {
        logService.error('ReconnectService', 'Step 4 failed: Connection request failed');
        return false;
      }

      logService.info('ReconnectService', 'Connection request sent successfully, waiting for actual connection result...');

      // 等待实际连接结果（最多等待30秒）
      final connectionResult = await _waitForConnectionResult(targetServer, const Duration(seconds: 30));

      if (connectionResult) {
        logService.info('ReconnectService', 'Step 4 completed: Reconnection successful to server ${targetServer.name}');
        // 保存成功连接的服务器信息，用于下次重连
        _lastConnectedServer = targetServer;
        return true;
      } else {
        logService.error('ReconnectService', 'Step 4 failed: Connection authentication failed or timed out');
        return false;
      }

    } catch (e) {
      logService.error('ReconnectService', 'Step 4 failed: Reconnection error - $e');
      return false;
    }
  }

  /// _waitForConnectionResult
  ///
  /// DESCRIPTION:
  ///     等待实际的连接结果，监听状态变化直到连接成功或失败
  ///
  /// PARAMETERS:
  ///     targetServer - 目标服务器
  ///     timeout - 超时时间
  ///
  /// RETURNS:
  ///     Future<bool> - 连接是否成功
  Future<bool> _waitForConnectionResult(Server targetServer, Duration timeout) async {
    final startTime = DateTime.now();
    final timeoutTime = startTime.add(timeout);

    logService.info('ReconnectService', 'Waiting for connection result, timeout: ${timeout.inSeconds}s');

    while (DateTime.now().isBefore(timeoutTime)) {
      final currentStatus = appState.connectionStatus;

      // 检查连接是否成功
      if (currentStatus == ConnectionStatus.connected) {
        logService.info('ReconnectService', 'Connection successful detected');
        return true;
      }

      // 检查连接是否失败
      if (currentStatus == ConnectionStatus.error) {
        final errorMessage = appState.connectionMessage;
        logService.error('ReconnectService', 'Connection failed detected: $errorMessage');
        return false;
      }

      // 检查是否意外断开
      if (currentStatus == ConnectionStatus.disconnected) {
        logService.error('ReconnectService', 'Unexpected disconnection detected during connection wait');
        return false;
      }

      // 等待短暂时间后再次检查
      await Future.delayed(const Duration(milliseconds: 500));
    }

    // 超时
    logService.error('ReconnectService', 'Connection result wait timed out after ${timeout.inSeconds}s');
    return false;
  }

  /// setLastConnectedServer
  ///
  /// DESCRIPTION:
  ///     设置最后连接的服务器信息，用于重连时使用
  ///
  /// PARAMETERS:
  ///     server - 服务器信息
  ///
  /// RETURNS:
  ///     void
  void setLastConnectedServer(Server server) {
    _lastConnectedServer = server;
    logService.debug('ReconnectService', 'Last connected server updated: ${server.name}');
  }

  /// isReconnecting
  ///
  /// DESCRIPTION:
  ///     获取当前是否正在重连
  ///
  /// RETURNS:
  ///     bool - 是否正在重连
  bool get isReconnecting => _isReconnecting;

  /// reconnectAttempts
  ///
  /// DESCRIPTION:
  ///     获取当前重连尝试次数
  ///
  /// RETURNS:
  ///     int - 重连尝试次数
  int get reconnectAttempts => _reconnectAttempts;
}
