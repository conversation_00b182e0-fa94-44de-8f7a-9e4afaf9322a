/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      auto_start_service.dart
///
/// DESCRIPTION :    自动启动服务，负责管理应用程序的自动启动设置和相关功能，
///                  支持Windows、macOS和Linux平台的系统级自动启动配置
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';

import 'log_service.dart';
import 'auth_service.dart';
import '../utils/constants.dart';
import '../core/dependency_injection.dart';
import 'platform/cross_platform_storage_service.dart';
import 'platform/platform_service_factory.dart';

/// AutoStartService
///
/// PURPOSE:
///     自动启动服务，负责管理应用程序的自动启动设置和相关功能
///
/// FEATURES:
///     - 跨平台自动启动：支持Windows、macOS和Linux平台
///     - 系统级配置：直接操作系统注册表或配置文件
///     - 设置同步：本地设置与系统设置的一致性检查
///     - 自动登录：基于自动启动和保存凭据的自动登录判断
///     - 状态验证：自动启动设置的验证和错误处理
///
/// USAGE:
///     通过依赖注入获取实例，调用相关方法管理自动启动设置
class AutoStartService {
  /// 日志服务实例，用于记录自动启动相关事件
  final LogService logService;

  /// 认证服务实例，用于检查保存的凭据
  final AuthService authService;

  /// SharedPreferences中存储自动启动设置的键名
  static const String _autoStartKey = 'auto_start_enabled';

  /// AutoStartService构造函数
  ///
  /// DESCRIPTION:
  ///     创建自动启动服务实例
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例，用于记录事件
  ///     authService - 认证服务实例，用于检查凭据
  AutoStartService({
    required this.logService,
    required this.authService,
  });

  /// getAutoStartSetting
  ///
  /// DESCRIPTION:
  ///     获取自动启动设置状态，检查系统注册表和本地设置的一致性
  ///     移动平台（iOS、Android、iOS应用在macOS上运行）默认返回true
  ///     桌面平台（Windows、原生macOS、Linux）使用用户设置
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 自动启动启用返回true，否则返回false
  ///
  /// THROWS:
  ///     Exception - 读取设置过程中发生错误时记录日志并返回false
  Future<bool> getAutoStartSetting() async {
    try {
      // 移动平台默认开机自启动，无需用户设置
      if (await _isMobilePlatform()) {
        logService.info('AutoStart', 'Mobile platform detected, auto start is always enabled');
        return true;
      }

      // 桌面平台使用用户设置
      // 获取本地保存的设置
      final prefs = await SharedPreferences.getInstance();
      final localSetting = prefs.getBool(_autoStartKey) ?? false;

      // 检查系统级别的自动启动状态
      bool systemAutoStart = localSetting; // 默认使用本地设置

      if (Platform.isWindows) {
        // Windows平台检查注册表
        systemAutoStart = await _checkWindowsAutoStart();

        // 如果系统设置和本地设置不一致，以系统设置为准并更新本地设置
        if (systemAutoStart != localSetting) {
          await prefs.setBool(_autoStartKey, systemAutoStart);
          logService.info('AutoStart', 'Synced local setting with system setting: $systemAutoStart');
        }
      } else {
        // macOS和Linux平台暂时只使用本地设置
        // TODO: 实现macOS和Linux的系统级检查
        logService.info('AutoStart', 'Using local setting for non-Windows platform: $localSetting');
      }

      logService.info('AutoStart', 'Auto start setting loaded: $systemAutoStart');
      return systemAutoStart;
    } catch (e) {
      logService.error('AutoStart', 'Failed to load auto start setting', e);
      // 移动平台出错时也返回true，确保自动登录功能可用
      if (await _isMobilePlatform()) {
        return true;
      }
      return false;
    }
  }

  /// setAutoStartSetting
  ///
  /// DESCRIPTION:
  ///     设置自动启动状态，同时更新本地设置和系统级配置
  ///
  /// PARAMETERS:
  ///     enabled - 是否启用自动启动
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 设置过程中发生错误时抛出异常
  Future<void> setAutoStartSetting(bool enabled) async {
    try {
      // 保存设置到SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_autoStartKey, enabled);

      // 根据平台实现系统级自动启动
      if (enabled) {
        await _enableSystemAutoStart();
      } else {
        await _disableSystemAutoStart();
      }

      logService.info('AutoStart', 'Auto start setting saved: $enabled');
    } catch (e) {
      logService.error('AutoStart', 'Failed to save auto start setting', e);
      throw Exception('Failed to set auto start: $e');
    }
  }

  /// shouldAutoLogin
  ///
  /// DESCRIPTION:
  ///     检查是否应该自动登录，当自动启动开启且有保存的凭据时返回true
  ///     Windows平台保留SharedPreferences使用，其他平台使用跨平台存储服务
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 应该自动登录返回true，否则返回false
  ///
  /// THROWS:
  ///     Exception - 检查过程中发生错误时记录日志并返回false
  Future<bool> shouldAutoLogin() async {
    try {
      final isAutoStartEnabled = await getAutoStartSetting();
      if (!isAutoStartEnabled) {
        logService.debug('AutoStart', 'Auto start is disabled, skipping auto login');
        return false;
      }

      // Windows平台保留SharedPreferences使用，其他平台使用跨平台存储服务
      bool rememberCredentials = false;
      String? savedUsername;
      String? savedPassword;

      if (PlatformServiceFactory.isDesktopPlatform) {
        // Windows/Linux: 使用SharedPreferences (保持原有行为)
        try {
          final prefs = await SharedPreferences.getInstance();
          rememberCredentials = prefs.getBool(StorageKeys.rememberCredentials) ?? false;
          savedUsername = prefs.getString(StorageKeys.username);
          savedPassword = prefs.getString(StorageKeys.password);
          logService.debug('AutoStart', 'Using SharedPreferences for Windows/Linux platform');
        } catch (e) {
          logService.warning('AutoStart', 'Failed to read from SharedPreferences, trying cross-platform storage: $e');
          // 如果SharedPreferences失败，尝试跨平台存储服务
          final result = await _readFromCrossPlatformStorage();
          rememberCredentials = result['rememberCredentials'] ?? false;
          savedUsername = result['username'];
          savedPassword = result['password'];
        }
      } else {
        // iOS/macOS: 使用跨平台存储服务
        final result = await _readFromCrossPlatformStorage();
        rememberCredentials = result['rememberCredentials'] ?? false;
        savedUsername = result['username'];
        savedPassword = result['password'];
        logService.debug('AutoStart', 'Using cross-platform storage for Apple platforms');
      }

      final hasCredentials = rememberCredentials &&
                           savedUsername != null &&
                           savedPassword != null &&
                           savedUsername.isNotEmpty &&
                           savedPassword.isNotEmpty;

      logService.info('AutoStart', 'Should auto login: $hasCredentials (auto start: $isAutoStartEnabled, remember: $rememberCredentials, has username: ${savedUsername?.isNotEmpty ?? false}, has password: ${savedPassword?.isNotEmpty ?? false})');
      return hasCredentials;
    } catch (e) {
      logService.error('AutoStart', 'Failed to check auto login condition', e);
      return false;
    }
  }

  /// _readFromCrossPlatformStorage
  ///
  /// DESCRIPTION:
  ///     从跨平台存储服务读取凭据信息
  ///
  /// RETURNS:
  ///     Future<Map<String, dynamic>> - 包含凭据信息的Map
  Future<Map<String, dynamic>> _readFromCrossPlatformStorage() async {
    try {
      final storageService = serviceLocator<CrossPlatformStorageService>();

      final rememberCredentials = await storageService.getBool(StorageKeys.rememberCredentials) ?? false;
      final savedUsername = await storageService.getString(StorageKeys.username);
      final savedPassword = await storageService.getSecureString(StorageKeys.password);

      return {
        'rememberCredentials': rememberCredentials,
        'username': savedUsername,
        'password': savedPassword,
      };
    } catch (e) {
      logService.warning('AutoStart', 'Failed to read from cross-platform storage: $e');
      return {
        'rememberCredentials': false,
        'username': null,
        'password': null,
      };
    }
  }

  /// _enableSystemAutoStart
  ///
  /// DESCRIPTION:
  ///     启用系统级自动启动，根据平台调用相应的实现方法
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 启用过程中发生错误时记录日志但不抛出异常
  Future<void> _enableSystemAutoStart() async {
    try {
      if (Platform.isWindows) {
        await _enableWindowsAutoStart();
      } else if (Platform.isMacOS) {
        await _enableMacOSAutoStart();
      } else if (Platform.isLinux) {
        await _enableLinuxAutoStart();
      }

      logService.info('AutoStart', 'System auto start enabled');
    } catch (e) {
      logService.error('AutoStart', 'Failed to enable system auto start', e);
      // 不抛出异常，因为SharedPreferences设置已经成功
    }
  }

  /// _disableSystemAutoStart
  ///
  /// DESCRIPTION:
  ///     禁用系统级自动启动，根据平台调用相应的实现方法
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 禁用过程中发生错误时记录日志但不抛出异常
  Future<void> _disableSystemAutoStart() async {
    try {
      if (Platform.isWindows) {
        await _disableWindowsAutoStart();
      } else if (Platform.isMacOS) {
        await _disableMacOSAutoStart();
      } else if (Platform.isLinux) {
        await _disableLinuxAutoStart();
      }

      logService.info('AutoStart', 'System auto start disabled');
    } catch (e) {
      logService.error('AutoStart', 'Failed to disable system auto start', e);
      // 不抛出异常，因为SharedPreferences设置已经成功
    }
  }

  /// _checkWindowsAutoStart
  ///
  /// DESCRIPTION:
  ///     检查Windows平台的自动启动状态，通过查询注册表验证
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 自动启动已启用返回true，否则返回false
  ///
  /// THROWS:
  ///     Exception - 查询过程中发生错误时记录日志并返回false
  Future<bool> _checkWindowsAutoStart() async {
    try {
      const appName = 'Panabit Client';

      // 查询注册表中的启动项
      final result = await Process.run('reg', [
        'query',
        'HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run',
        '/v',
        appName,
      ], runInShell: false); // 防止显示cmd窗口

      // 如果退出码为0，说明注册表项存在
      if (result.exitCode == 0) {
        final output = result.stdout.toString();
        final executablePath = Platform.resolvedExecutable;

        // 检查注册表中的路径是否与当前可执行文件路径匹配
        if (output.contains(executablePath) || output.contains('"$executablePath"')) {
          logService.info('AutoStart', 'Windows auto start is enabled');
          return true;
        } else {
          logService.info('AutoStart', 'Windows auto start entry exists but path mismatch');
          // 路径不匹配，需要更新注册表项
          await _enableWindowsAutoStart();
          return true;
        }
      } else {
        logService.info('AutoStart', 'Windows auto start is disabled');
        return false;
      }
    } catch (e) {
      logService.error('AutoStart', 'Failed to check Windows auto start status', e);
      return false;
    }
  }

  /// _enableWindowsAutoStart
  ///
  /// DESCRIPTION:
  ///     Windows平台启用自动启动，通过注册表添加启动项
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 注册表操作失败时抛出异常
  Future<void> _enableWindowsAutoStart() async {
    try {
      final executablePath = Platform.resolvedExecutable;
      const appName = 'Panabit Client';

      logService.info('AutoStart', 'Adding Windows auto start registry entry for: $executablePath');

      // 使用注册表添加启动项
      final result = await Process.run('reg', [
        'add',
        'HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run',
        '/v',
        appName,
        '/t',
        'REG_SZ',
        '/d',
        '"$executablePath"',
        '/f'
      ], runInShell: false); // 防止显示cmd窗口

      if (result.exitCode != 0) {
        final errorMsg = result.stderr.toString().trim();
        throw Exception('Failed to add registry entry: $errorMsg');
      }

      logService.info('AutoStart', 'Windows auto start registry entry added successfully');

      // 验证注册表项是否添加成功
      final verifyResult = await _checkWindowsAutoStart();
      if (!verifyResult) {
        throw Exception('Registry entry was added but verification failed');
      }
    } catch (e) {
      logService.error('AutoStart', 'Failed to enable Windows auto start', e);
      rethrow;
    }
  }

  /// _disableWindowsAutoStart
  ///
  /// DESCRIPTION:
  ///     Windows平台禁用自动启动，通过注册表删除启动项
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 注册表操作失败时抛出异常
  Future<void> _disableWindowsAutoStart() async {
    try {
      const appName = 'Panabit Client';

      logService.info('AutoStart', 'Removing Windows auto start registry entry');

      // 从注册表删除启动项
      final result = await Process.run('reg', [
        'delete',
        'HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run',
        '/v',
        appName,
        '/f'
      ], runInShell: false); // 防止显示cmd窗口

      // 退出码1表示注册表项不存在，这是正常的
      if (result.exitCode == 0) {
        logService.info('AutoStart', 'Windows auto start registry entry removed successfully');
      } else if (result.exitCode == 1) {
        logService.info('AutoStart', 'Windows auto start registry entry was already removed');
      } else {
        final errorMsg = result.stderr.toString().trim();
        throw Exception('Failed to delete registry entry: $errorMsg');
      }

      // 验证注册表项是否删除成功
      final verifyResult = await _checkWindowsAutoStart();
      if (verifyResult) {
        throw Exception('Registry entry was deleted but still exists');
      }
    } catch (e) {
      logService.error('AutoStart', 'Failed to disable Windows auto start', e);
      rethrow;
    }
  }

  /// _enableMacOSAutoStart
  ///
  /// DESCRIPTION:
  ///     macOS平台启用自动启动，通过LaunchAgents配置文件实现
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 配置文件操作失败时抛出异常
  ///
  /// NOTE:
  ///     当前版本暂未实现，将在未来版本中添加此功能
  Future<void> _enableMacOSAutoStart() async {
    try {
      // TODO: 实现macOS LaunchAgents自动启动
      // 需要创建 ~/Library/LaunchAgents/com.panabit.client.plist 文件
      logService.warning('AutoStart', 'macOS auto start not yet implemented - feature will be added in future version');

      // 暂时不抛出异常，允许设置保存到SharedPreferences
      // throw UnimplementedError('macOS auto start not yet implemented');
    } catch (e) {
      logService.error('AutoStart', 'Failed to enable macOS auto start', e);
      rethrow;
    }
  }

  /// _disableMacOSAutoStart
  ///
  /// DESCRIPTION:
  ///     macOS平台禁用自动启动，通过删除LaunchAgents配置文件实现
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 配置文件操作失败时抛出异常
  ///
  /// NOTE:
  ///     当前版本暂未实现，将在未来版本中添加此功能
  Future<void> _disableMacOSAutoStart() async {
    try {
      // TODO: 实现macOS LaunchAgents自动启动禁用
      // 需要删除 ~/Library/LaunchAgents/com.panabit.client.plist 文件
      logService.warning('AutoStart', 'macOS auto start disable not yet implemented - feature will be added in future version');

      // 暂时不抛出异常，允许设置保存到SharedPreferences
      // throw UnimplementedError('macOS auto start disable not yet implemented');
    } catch (e) {
      logService.error('AutoStart', 'Failed to disable macOS auto start', e);
      rethrow;
    }
  }

  /// _enableLinuxAutoStart
  ///
  /// DESCRIPTION:
  ///     Linux平台启用自动启动，通过.desktop文件实现
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 配置文件操作失败时抛出异常
  ///
  /// NOTE:
  ///     当前版本暂未实现，将在未来版本中添加此功能
  Future<void> _enableLinuxAutoStart() async {
    try {
      // TODO: 实现Linux .desktop文件自动启动
      // 需要创建 ~/.config/autostart/panabit-client.desktop 文件
      logService.warning('AutoStart', 'Linux auto start not yet implemented - feature will be added in future version');

      // 暂时不抛出异常，允许设置保存到SharedPreferences
      // throw UnimplementedError('Linux auto start not yet implemented');
    } catch (e) {
      logService.error('AutoStart', 'Failed to enable Linux auto start', e);
      rethrow;
    }
  }

  /// _disableLinuxAutoStart
  ///
  /// DESCRIPTION:
  ///     Linux平台禁用自动启动，通过删除.desktop文件实现
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 配置文件操作失败时抛出异常
  ///
  /// NOTE:
  ///     当前版本暂未实现，将在未来版本中添加此功能
  Future<void> _disableLinuxAutoStart() async {
    try {
      // TODO: 实现Linux .desktop文件自动启动禁用
      // 需要删除 ~/.config/autostart/panabit-client.desktop 文件
      logService.warning('AutoStart', 'Linux auto start disable not yet implemented - feature will be added in future version');

      // 暂时不抛出异常，允许设置保存到SharedPreferences
      // throw UnimplementedError('Linux auto start disable not yet implemented');
    } catch (e) {
      logService.error('AutoStart', 'Failed to disable Linux auto start', e);
      rethrow;
    }
  }

  /// _isMobilePlatform
  ///
  /// DESCRIPTION:
  ///     检测当前是否为移动平台，包括iOS、Android和iOS应用在macOS上运行
  ///     移动平台默认开机自启动，无需用户设置
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 移动平台返回true，桌面平台返回false
  ///
  /// THROWS:
  ///     Exception - 平台检测失败时记录日志并返回false
  Future<bool> _isMobilePlatform() async {
    try {
      // Android平台
      if (Platform.isAndroid) {
        return true;
      }

      // iOS平台（包括iOS应用在macOS上运行）
      if (Platform.isIOS) {
        return true; // iOS平台都视为移动平台，包括在macOS上运行的iOS应用
      }

      // 桌面平台
      return false;
    } catch (e) {
      logService.error('AutoStart', 'Failed to detect mobile platform', e);
      return false;
    }
  }
}
