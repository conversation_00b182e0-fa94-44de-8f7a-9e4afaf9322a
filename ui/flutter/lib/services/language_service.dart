/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      language_service.dart
///
/// DESCRIPTION :    Language service for managing application localization
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path/path.dart' as path;

import '../utils/constants.dart';
import 'log_service.dart';

/// LanguageService
///
/// PURPOSE:
///     Manages application language settings and localization preferences
///     with support for user preferences, installer configuration, and fallback defaults
///
/// FEATURES:
///     - Multi-source language loading (user preferences, installer config, defaults)
///     - Persistent language storage using SharedPreferences
///     - Language validation and fallback mechanisms
///     - Real-time language change notifications
///     - Support for Chinese (zh) and English (en) locales
///
/// USAGE:
///     final languageService = serviceLocator<LanguageService>();
///     await languageService.initialize();
///     await languageService.setLanguage('en');
///     bool isChinese = languageService.isChinese;
class LanguageService extends ChangeNotifier {
  final LogService logService;
  final _LanguageServiceHelper _helper = _LanguageServiceHelper();
  Locale _locale = const Locale('zh');
  bool _isInitialized = false;

  /// LanguageService constructor
  ///
  /// DESCRIPTION:
  ///     Creates a new language service instance
  ///
  /// PARAMETERS:
  ///     logService - LogService: Service for logging operations
  LanguageService({required this.logService}) {
    _helper.setLogService(logService);
  }

  /// locale
  ///
  /// DESCRIPTION:
  ///     Gets the current application locale
  ///
  /// RETURNS:
  ///     Locale - The current locale setting
  Locale get locale => _locale;

  /// isInitialized
  ///
  /// DESCRIPTION:
  ///     Checks if the language service has been initialized
  ///
  /// RETURNS:
  ///     bool - True if initialized, false otherwise
  bool get isInitialized => _isInitialized;

  /// isChinese
  ///
  /// DESCRIPTION:
  ///     Checks if the current language is Chinese
  ///
  /// RETURNS:
  ///     bool - True if current language is Chinese, false otherwise
  bool get isChinese => _locale.languageCode == 'zh';

  /// isEnglish
  ///
  /// DESCRIPTION:
  ///     Checks if the current language is English
  ///
  /// RETURNS:
  ///     bool - True if current language is English, false otherwise
  bool get isEnglish => _locale.languageCode == 'en';

  /// initialize
  ///
  /// DESCRIPTION:
  ///     Initializes the language service by loading saved language preferences
  ///
  /// RETURNS:
  ///     Future<void> - Completes when initialization is finished
  Future<void> initialize() async {
    if (_isInitialized) return;

    await _loadLanguage();
    _isInitialized = true;
  }

  /// _loadLanguage
  ///
  /// DESCRIPTION:
  ///     Loads language settings from multiple sources with priority order:
  ///     1. User preferences (SharedPreferences)
  ///     2. Installer configuration file
  ///     3. Default language (Chinese)
  ///
  /// RETURNS:
  ///     Future<void> - Completes when language loading is finished
  Future<void> _loadLanguage() async {
    try {
      _helper.logLoadingStart();

      final languageResult = await _helper.loadLanguageFromSources();
      _locale = Locale(languageResult.languageCode);

      await _helper.saveLanguagePreference(languageResult.languageCode);
      _helper.logLoadingComplete(languageResult.languageCode, languageResult.source);

      notifyListeners();
    } catch (e) {
      _helper.logLoadingError(e);
      _locale = const Locale('zh');
      notifyListeners();
    }
  }

  /// setLanguage
  ///
  /// DESCRIPTION:
  ///     Sets the application language and saves the preference
  ///
  /// PARAMETERS:
  ///     languageCode - String: The language code to set ('zh' or 'en')
  ///
  /// RETURNS:
  ///     Future<void> - Completes when language change is finished
  Future<void> setLanguage(String languageCode) async {
    try {
      _helper.logLanguageChangeAttempt(languageCode);

      if (!_helper.isValidLanguageCode(languageCode)) {
        _helper.logInvalidLanguageCode(languageCode);
        return;
      }

      if (_locale.languageCode == languageCode) {
        _helper.logLanguageAlreadySet(languageCode);
        return;
      }

      final success = await _helper.saveLanguagePreference(languageCode);
      if (success) {
        _locale = Locale(languageCode);
        _helper.logLanguageChangeSuccess(languageCode);
        notifyListeners();
      } else {
        _helper.logLanguageChangeFailed();
      }
    } catch (e) {
      _helper.logLanguageChangeError(languageCode, e);
    }
  }

  /// getSupportedLocales
  ///
  /// DESCRIPTION:
  ///     Returns the list of supported locales
  ///
  /// RETURNS:
  ///     List<Locale> - List of supported locale objects
  List<Locale> getSupportedLocales() {
    return [
      const Locale('zh'), // Chinese
      const Locale('en'), // English
    ];
  }
}

/// _LanguageResult
///
/// PURPOSE:
///     Data class to hold language loading results
///
/// FEATURES:
///     - Language code storage
///     - Source tracking for debugging
///
/// USAGE:
///     Internal data class used by _LanguageServiceHelper
class _LanguageResult {
  final String languageCode;
  final String source;

  /// _LanguageResult constructor
  ///
  /// DESCRIPTION:
  ///     Creates a new language result instance
  ///
  /// PARAMETERS:
  ///     languageCode - String: The loaded language code
  ///     source - String: The source of the language setting
  const _LanguageResult({
    required this.languageCode,
    required this.source,
  });
}

/// _LanguageServiceHelper
///
/// PURPOSE:
///     Helper class for LanguageService to handle language loading and validation
///
/// FEATURES:
///     - Multi-source language loading
///     - Language validation and fallback
///     - Unified logging for all language operations
///     - SharedPreferences management
///
/// USAGE:
///     Internal helper class used by LanguageService
class _LanguageServiceHelper {
  LogService? _logService;

  /// setLogService
  ///
  /// DESCRIPTION:
  ///     Sets the log service instance for this helper
  ///
  /// PARAMETERS:
  ///     logService - LogService: The log service to use
  void setLogService(LogService logService) {
    _logService = logService;
  }

  /// logLoadingStart
  ///
  /// DESCRIPTION:
  ///     Logs the start of language loading process
  void logLoadingStart() {
    _logService?.info('Language', 'Starting language loading process');
  }

  /// loadLanguageFromSources
  ///
  /// DESCRIPTION:
  ///     Loads language from multiple sources with priority order:
  ///     1. User preferences (SharedPreferences)
  ///     2. Installer configuration file
  ///     3. System language (iOS/Android)
  ///     4. Default language (Chinese)
  ///
  /// RETURNS:
  ///     Future<_LanguageResult> - The language loading result
  Future<_LanguageResult> loadLanguageFromSources() async {
    String languageCode = 'zh'; // Default Chinese (will be updated based on system language)
    String languageSource = 'default';

    // Priority 1: User preferences from SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final savedLanguage = prefs.getString(StorageKeys.language);
    if (savedLanguage != null && savedLanguage.isNotEmpty) {
      languageCode = savedLanguage;
      languageSource = 'user_preference';
      _logService?.info('Language', 'Language loaded from user preferences (SharedPreferences): $languageCode');
    } else {
      // Priority 2: Installer configuration file (first run only)
      final installerLanguage = await _loadLanguageFromInstaller();
      if (installerLanguage != null) {
        languageCode = installerLanguage;
        languageSource = 'installer';
        _logService?.info('Language', 'Language loaded from installer config (first run): $languageCode');
      } else {
        // Priority 3: System language
        final systemLanguage = await _loadLanguageFromSystem();
        if (systemLanguage != null) {
          languageCode = systemLanguage;
          languageSource = 'system';
          _logService?.info('Language', 'Language loaded from system settings: $languageCode');
        } else {
          // Priority 4: Default language based on system language detection
          final defaultLanguage = await _getDefaultLanguageBasedOnSystem();
          languageCode = defaultLanguage;
          languageSource = 'system_default';
          _logService?.info('Language', 'No saved language, installer config, or supported system language found, using system-based default: $languageCode');
        }
      }
    }

    // Validate language code
    if (!isValidLanguageCode(languageCode)) {
      _logService?.warning('Language', 'Invalid language code: $languageCode, falling back to default');
      languageCode = 'zh';
      languageSource = 'fallback';
    }

    return _LanguageResult(languageCode: languageCode, source: languageSource);
  }

  /// _loadLanguageFromInstaller
  ///
  /// DESCRIPTION:
  ///     Loads language setting from installer configuration file
  ///
  /// RETURNS:
  ///     Future<String?> - The language code from installer or null if not found
  Future<String?> _loadLanguageFromInstaller() async {
    try {
      final executable = Platform.resolvedExecutable;
      final appDir = path.dirname(executable);
      final configFile = File(path.join(appDir, 'language.cfg'));

      if (await configFile.exists()) {
        final content = await configFile.readAsString();
        final lines = content.split('\n');

        for (final line in lines) {
          if (line.trim().startsWith('language=')) {
            final language = line.split('=')[1].trim();
            _logService?.info('Language', 'Found installer language config: $language');
            return language;
          }
        }
      }

      return null;
    } catch (e) {
      _logService?.warning('Language', 'Failed to read installer language config: $e');
      return null;
    }
  }

  /// _loadLanguageFromSystem
  ///
  /// DESCRIPTION:
  ///     Loads language setting from system locale
  ///
  /// RETURNS:
  ///     Future<String?> - The language code from system or null if not supported
  Future<String?> _loadLanguageFromSystem() async {
    try {
      // Get system locale
      final systemLocale = ui.PlatformDispatcher.instance.locale;
      final systemLanguageCode = systemLocale.languageCode;

      _logService?.info('Language', 'System language detected: $systemLanguageCode');

      // Map system language to supported languages
      String? mappedLanguage;
      switch (systemLanguageCode) {
        case 'zh':
        case 'zh-Hans':
        case 'zh-Hant':
        case 'zh-CN':
        case 'zh-TW':
        case 'zh-HK':
          mappedLanguage = 'zh';
          break;
        case 'en':
        case 'en-US':
        case 'en-GB':
        case 'en-CA':
        case 'en-AU':
          mappedLanguage = 'en';
          break;
        default:
          // For unsupported languages, return null to use default fallback
          _logService?.info('Language', 'Unsupported system language: $systemLanguageCode, will use default fallback');
          return null;
      }

      if (isValidLanguageCode(mappedLanguage)) {
        _logService?.info('Language', 'System language mapped to: $mappedLanguage');
        return mappedLanguage;
      }

      return null;
    } catch (e) {
      _logService?.warning('Language', 'Failed to get system language: $e');
      return null;
    }
  }

  /// _getDefaultLanguageBasedOnSystem
  ///
  /// DESCRIPTION:
  ///     Gets default language based on system language detection
  ///     If system language is Chinese-related, default to Chinese
  ///     Otherwise, default to English
  ///
  /// RETURNS:
  ///     Future<String> - The default language code
  Future<String> _getDefaultLanguageBasedOnSystem() async {
    try {
      // Get system locale
      final systemLocale = ui.PlatformDispatcher.instance.locale;
      final systemLanguageCode = systemLocale.languageCode;

      _logService?.info('Language', 'Determining default language based on system: $systemLanguageCode');

      // If system language is Chinese-related, default to Chinese
      if (systemLanguageCode.startsWith('zh')) {
        _logService?.info('Language', 'System language is Chinese-related, defaulting to Chinese');
        return 'zh';
      }

      // For all other languages, default to English
      _logService?.info('Language', 'System language is not Chinese-related, defaulting to English');
      return 'en';
    } catch (e) {
      _logService?.warning('Language', 'Failed to determine system-based default language: $e, using Chinese');
      return 'zh'; // Ultimate fallback
    }
  }

  /// isValidLanguageCode
  ///
  /// DESCRIPTION:
  ///     Validates if the language code is supported
  ///
  /// PARAMETERS:
  ///     languageCode - String: The language code to validate
  ///
  /// RETURNS:
  ///     bool - True if valid, false otherwise
  bool isValidLanguageCode(String languageCode) {
    return ['zh', 'en'].contains(languageCode);
  }

  /// saveLanguagePreference
  ///
  /// DESCRIPTION:
  ///     Saves language preference to SharedPreferences
  ///
  /// PARAMETERS:
  ///     languageCode - String: The language code to save
  ///
  /// RETURNS:
  ///     Future<bool> - True if saved successfully, false otherwise
  Future<bool> saveLanguagePreference(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(StorageKeys.language, languageCode);
    } catch (e) {
      _logService?.error('Language', 'Failed to save language preference: $e');
      return false;
    }
  }

  /// logLoadingComplete
  ///
  /// DESCRIPTION:
  ///     Logs successful completion of language loading
  ///
  /// PARAMETERS:
  ///     languageCode - String: The loaded language code
  ///     source - String: The source of the language setting
  void logLoadingComplete(String languageCode, String source) {
    _logService?.info('Language', 'Language setting saved to SharedPreferences: $languageCode (source: $source)');
    _logService?.info('Language', 'Language loading completed successfully: $languageCode (source: $source)');
  }

  /// logLoadingError
  ///
  /// DESCRIPTION:
  ///     Logs language loading error
  ///
  /// PARAMETERS:
  ///     error - dynamic: The error that occurred
  void logLoadingError(dynamic error) {
    _logService?.error('Language', 'Failed to load language', error);
  }

  /// logLanguageChangeAttempt
  ///
  /// DESCRIPTION:
  ///     Logs language change attempt
  ///
  /// PARAMETERS:
  ///     languageCode - String: The target language code
  void logLanguageChangeAttempt(String languageCode) {
    _logService?.info('Language', 'Attempting to change language to: $languageCode');
  }

  /// logInvalidLanguageCode
  ///
  /// DESCRIPTION:
  ///     Logs invalid language code warning
  ///
  /// PARAMETERS:
  ///     languageCode - String: The invalid language code
  void logInvalidLanguageCode(String languageCode) {
    _logService?.warning('Language', 'Invalid language code: $languageCode, operation cancelled');
  }

  /// logLanguageAlreadySet
  ///
  /// DESCRIPTION:
  ///     Logs that language is already set to the requested value
  ///
  /// PARAMETERS:
  ///     languageCode - String: The language code
  void logLanguageAlreadySet(String languageCode) {
    _logService?.info('Language', 'Language is already set to $languageCode, no change needed');
  }

  /// logLanguageChangeSuccess
  ///
  /// DESCRIPTION:
  ///     Logs successful language change
  ///
  /// PARAMETERS:
  ///     languageCode - String: The new language code
  void logLanguageChangeSuccess(String languageCode) {
    _logService?.info('Language', 'Language successfully changed to $languageCode and saved to preferences');
  }

  /// logLanguageChangeFailed
  ///
  /// DESCRIPTION:
  ///     Logs failed language change
  void logLanguageChangeFailed() {
    _logService?.error('Language', 'Failed to save language preference to SharedPreferences');
  }

  /// logLanguageChangeError
  ///
  /// DESCRIPTION:
  ///     Logs language change error
  ///
  /// PARAMETERS:
  ///     languageCode - String: The target language code
  ///     error - dynamic: The error that occurred
  void logLanguageChangeError(String languageCode, dynamic error) {
    _logService?.error('Language', 'Failed to set language to $languageCode', error);
  }
}
