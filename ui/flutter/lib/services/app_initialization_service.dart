/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      app_initialization_service.dart
///
/// DESCRIPTION :    应用程序初始化服务，负责应用启动时的各种初始化操作，
///                  包括依赖注入、单例检查、窗口配置等
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:io';
import 'package:flutter/widgets.dart';
import '../core/dependency_injection.dart';
import '../services/log_service.dart';
import '../services/window_service.dart';
import '../services/single_instance_service.dart';
import '../services/app_lifecycle_service.dart';
import '../services/api_service.dart';
import '../services/backend_service.dart';
import '../services/tray_service.dart';

import '../services/platform/platform_service_factory.dart';
import '../utils/version_info.dart';
import 'network_permission_service.dart';
import 'orientation_manager.dart';
import 'platform_window_manager.dart';

/// AppInitializationService
///
/// PURPOSE:
///     应用程序初始化服务，负责应用启动时的各种初始化操作
///
/// FEATURES:
///     - 依赖注入初始化：配置所有服务的依赖关系
///     - 单例检查：确保只有一个应用实例运行
///     - 窗口配置：初始化桌面平台的窗口设置
///     - 生命周期管理：初始化应用生命周期服务
///     - 版本信息记录：记录应用版本信息到日志
///
/// USAGE:
///     在main函数中调用initialize()方法完成应用初始化
class AppInitializationService {
  static SingleInstanceService? _singleInstanceService;

  /// initialize
  ///
  /// DESCRIPTION:
  ///     执行应用程序的完整初始化流程，根据平台选择不同的初始化策略
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<SingleInstanceService?> - 单例服务实例，如果不是第一个实例则返回null
  ///
  /// THROWS:
  ///     Exception - 初始化过程中发生错误时抛出异常
  static Future<SingleInstanceService?> initialize() async {
    // 确保Flutter绑定初始化
    WidgetsFlutterBinding.ensureInitialized();

    // 初始化依赖注入
    await DependencyInjection.init();

    // 初始化平台检测并缓存结果
    await PlatformServiceFactory.initializePlatformDetection();

    final logService = serviceLocator<LogService>();

    // 获取平台名称
    String platformName = 'Unknown';
    if (Platform.isIOS) {
      platformName = 'iOS';
    } else if (Platform.isMacOS) {
      platformName = 'macOS';
    } else if (Platform.isAndroid) {
      platformName = 'Android';
    } else if (Platform.isWindows) {
      platformName = 'Windows';
    } else if (Platform.isLinux) {
      platformName = 'Linux';
    }

    logService.info('App', 'Panabit Client starting on $platformName...');

    // 平台特定初始化
    if (Platform.isIOS || Platform.isMacOS) {
      return await _initializeApplePlatforms(logService);
    } else if (Platform.isAndroid) {
      return await _initializeAndroidPlatform(logService);
    } else if (Platform.isWindows || Platform.isLinux) {
      return await _initializeDesktopPlatforms(logService);
    } else {
      throw UnsupportedError('Platform ${Platform.operatingSystem} not supported');
    }
  }

  /// _performSingleInstanceCheck
  ///
  /// DESCRIPTION:
  ///     执行单例检查，确保只有一个应用实例运行
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     Future<SingleInstanceService?> - 如果是第一个实例返回服务实例，否则返回null
  static Future<SingleInstanceService?> _performSingleInstanceCheck(LogService logService) async {
    try {
      final singleInstanceService = SingleInstanceService(logService: logService);
      final isFirstInstance = await singleInstanceService.init();

      if (!isFirstInstance) {
        logService.info('App', 'Another instance is already running, exiting current instance');
        exit(0);
      }

      logService.info('App', 'This is the first instance, continuing startup');
      _singleInstanceService = singleInstanceService;
      return singleInstanceService;
    } catch (e) {
      logService.error('App', 'Failed to perform single instance check: $e', e);
      rethrow;
    }
  }

  /// _initializeWindow
  ///
  /// DESCRIPTION:
  ///     初始化窗口配置（仅在桌面平台）
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _initializeWindow(LogService logService) async {
    try {
      final windowService = serviceLocator<WindowService>();
      await windowService.initializeWindow();
    } catch (e) {
      logService.error('App', 'Failed to initialize window: $e', e);
      // 窗口初始化失败不应该阻止应用启动
    }
  }

  /// _logVersionInfo
  ///
  /// DESCRIPTION:
  ///     记录应用版本信息到日志
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _logVersionInfo(LogService logService) async {
    try {
      logService.info('App', 'Base Version: ${VersionInfo.getShortVersion()}');
      
      final fullVersion = await VersionInfo.getFullVersion();
      logService.info('App', 'Full Version: $fullVersion');
    } catch (e) {
      logService.warning('App', 'Failed to get version info: $e');
    }
  }

  /// _initializeAppLifecycle
  ///
  /// DESCRIPTION:
  ///     初始化应用生命周期服务
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _initializeAppLifecycle(LogService logService) async {
    try {
      final appLifecycleService = serviceLocator<AppLifecycleService>();
      await appLifecycleService.init();
      logService.info('App', 'AppLifecycleService initialized');
    } catch (e) {
      logService.error('App', 'Failed to initialize AppLifecycleService: $e', e);
      // 生命周期服务初始化失败不应该阻止应用启动
    }
  }

  /// _initializeApiService
  ///
  /// DESCRIPTION:
  ///     初始化API服务，确保平台服务正确配置
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _initializeApiService(LogService logService) async {
    try {
      final apiService = serviceLocator<ApiService>();
      final success = await apiService.initialize();
      if (success) {
        logService.info('App', 'ApiService initialized successfully');
      } else {
        logService.warning('App', 'ApiService initialization returned false');
      }
    } catch (e) {
      logService.error('App', 'Failed to initialize ApiService: $e', e);
      // API服务初始化失败不应该阻止应用启动，但会影响功能
    }
  }

  /// getSingleInstanceService
  ///
  /// DESCRIPTION:
  ///     获取单例服务实例
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     SingleInstanceService? - 单例服务实例，如果未初始化则返回null
  static SingleInstanceService? getSingleInstanceService() {
    return _singleInstanceService;
  }

  /// _initializeApplePlatforms
  ///
  /// DESCRIPTION:
  ///     Apple平台（iOS/macOS）初始化流程
  ///     注意：后端服务（PlatformChannelHandler）和VPN服务将在用户登录时启动，
  ///     而不是在应用启动时启动，这样可以提高应用启动速度并节约资源
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     Future<SingleInstanceService?> - 始终返回null，因为Apple平台不需要单例检查
  static Future<SingleInstanceService?> _initializeApplePlatforms(LogService logService) async {
    try {
      logService.info('App', 'Initializing Apple platforms...');

      // 记录版本信息
      await _logVersionInfo(logService);

      // 注意：存储服务已在依赖注入阶段初始化，无需重复初始化

      // 检查网络权限（仅iOS需要）
      await _checkNetworkPermission(logService);

      // 初始化应用生命周期服务
      await _initializeAppLifecycle(logService);

      // 初始化设备方向管理（iOS特有）
      await _initializeOrientationManager(logService);

      // 初始化平台窗口管理
      await _initializePlatformWindowManager(logService);

      // ✅ 重要：Swift后端服务初始化已移动到登录时进行，与Windows平台保持一致
      // 这样可以避免在用户未登录时就启动后端服务，提高应用启动速度并节约资源
      // 后端服务将在LoginScreen._performLogin()方法中通过PlatformChannelBackendService.initialize()启动
      logService.info('App', 'Apple platform: Backend service will be started on user login');

      // 初始化Platform Channel API服务（仅设置事件监听，不初始化后端）
      await _initializeApiService(logService);

      logService.info('App', 'Apple platforms initialized successfully');

      // Apple平台不需要真正的单例服务，但需要返回一个虚拟实例以满足main()函数的期望
      return _DummySingleInstanceService();
    } catch (e) {
      logService.error('App', 'Failed to initialize Apple platforms: $e', e);
      // Apple平台初始化失败时，尝试降级处理
      await _handleApplePlatformInitializationFailure(logService, e);
      rethrow;
    }
  }

  /// _initializeAndroidPlatform
  ///
  /// DESCRIPTION:
  ///     Android平台初始化流程
  ///     注意：后端服务（PlatformChannelHandler）和VPN服务将在用户登录时启动，
  ///     而不是在应用启动时启动，这样可以提高应用启动速度并节约资源
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     Future<SingleInstanceService?> - 始终返回null，因为Android平台不需要单例检查
  static Future<SingleInstanceService?> _initializeAndroidPlatform(LogService logService) async {
    try {
      logService.info('App', 'Initializing Android platform...');

      // 记录版本信息
      await _logVersionInfo(logService);

      // 注意：存储服务已在依赖注入阶段初始化，无需重复初始化

      // 检查网络权限（Android需要）
      await _checkNetworkPermission(logService);

      // 检查VPN权限（Android需要）
      await _checkVPNPermission(logService);

      // 初始化应用生命周期服务
      await _initializeAppLifecycle(logService);

      // 初始化设备方向管理（Android支持）
      await _initializeOrientationManager(logService);

      // 初始化平台窗口管理
      await _initializePlatformWindowManager(logService);

      // ✅ 重要：Android后端服务初始化已移动到登录时进行，与iOS/Windows平台保持一致
      // 这样可以避免在用户未登录时就启动后端服务，提高应用启动速度并节约资源
      // 后端服务将在LoginScreen._performLogin()方法中通过PlatformChannelBackendService.initialize()启动
      logService.info('App', 'Android platform: Backend service will be started on user login');

      // 初始化Platform Channel API服务（仅设置事件监听，不初始化后端）
      await _initializeApiService(logService);

      logService.info('App', 'Android platform initialized successfully');

      // Android平台不需要真正的单例服务，但需要返回一个虚拟实例以满足main()函数的期望
      return _DummySingleInstanceService();
    } catch (e) {
      logService.error('App', 'Failed to initialize Android platform: $e', e);
      // Android平台初始化失败时，尝试降级处理
      await _handleAndroidPlatformInitializationFailure(logService, e);
      rethrow;
    }
  }

  /// _initializeDesktopPlatforms
  ///
  /// DESCRIPTION:
  ///     桌面平台（Windows/Linux）初始化流程
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     Future<SingleInstanceService?> - 单例服务实例，如果不是第一个实例则返回null
  static Future<SingleInstanceService?> _initializeDesktopPlatforms(LogService logService) async {
    try {
      logService.info('App', 'Initializing desktop platforms...');

      // 执行单例检查（仅桌面平台需要）
      final singleInstanceService = await _performSingleInstanceCheck(logService);
      if (singleInstanceService == null) {
        return null; // 不是第一个实例，应该退出
      }

      // 记录版本信息
      await _logVersionInfo(logService);

      // 初始化窗口配置（仅桌面平台需要）
      await _initializeWindow(logService);

      // 初始化系统托盘服务（仅Windows平台需要）
      if (Platform.isWindows) {
        await _initializeTrayService(logService);
      }

      // Windows平台延迟启动后端服务（在用户登录时启动，避免启动时请求管理员权限）
      // 其他桌面平台（Linux）仍然在启动时启动后端服务
      if (Platform.isWindows) {
        logService.info('App', 'Windows platform: Backend service will be started on user login');
      } else {
        // 启动后端服务（仅非Windows桌面平台需要）- 这是关键步骤，必须在API服务之前
        await _initializeBackendService(logService);
      }

      // 初始化应用生命周期服务
      await _initializeAppLifecycle(logService);

      // 初始化API服务
      if (Platform.isWindows) {
        // Windows平台：API服务将在后端启动后初始化
        logService.info('App', 'Windows platform: API service will be initialized after backend starts');
      } else {
        // 其他平台：立即初始化API服务
        await _initializeApiService(logService);
      }

      logService.info('App', 'Desktop platforms initialized successfully');
      return singleInstanceService;
    } catch (e) {
      logService.error('App', 'Failed to initialize desktop platforms: $e', e);
      // 桌面平台初始化失败时，尝试降级处理
      await _handleDesktopPlatformInitializationFailure(logService, e);
      rethrow;
    }
  }

  /// _initializeBackendService
  ///
  /// DESCRIPTION:
  ///     初始化后端服务（跨平台）
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _initializeBackendService(LogService logService) async {
    try {
      logService.info('App', 'Initializing backend service for ${PlatformServiceFactory.platformName}...');

      if (PlatformServiceFactory.isDesktopPlatform) {
        // 桌面平台：使用传统的BackendService启动Go后端进程
        final backendService = serviceLocator<BackendService>();
        logService.info('App', 'Starting Go backend process...');

        final success = await backendService.start();
        if (success) {
          logService.info('App', 'Go backend process started successfully');

          // 等待后端服务就绪
          await Future.delayed(const Duration(seconds: 2));

          logService.info('App', 'Backend service started and ready');
        } else {
          throw Exception('Failed to start Go backend process');
        }
      } else {
        // Apple平台：使用跨平台后端服务
        final backendService = PlatformServiceFactory.createBackendService();

        // 获取平台信息用于调试
        final platformInfo = backendService.getPlatformInfo();
        logService.debug('App', 'Backend platform info: $platformInfo');

        final success = await backendService.initialize();
        if (success) {
          logService.info('App', 'Swift backend service initialized successfully');

          // 验证后端服务健康状态
          final isHealthy = await backendService.checkHealth();
          if (isHealthy) {
            logService.info('App', 'Backend service health check passed');
          } else {
            logService.warning('App', 'Backend service health check failed, but initialization succeeded');
          }
        } else {
          throw Exception('Swift backend service failed to initialize');
        }
      }
    } catch (e) {
      logService.error('App', 'Failed to initialize backend service: $e', e);
      rethrow;
    }
  }

  /// _initializeOrientationManager
  ///
  /// DESCRIPTION:
  ///     初始化设备方向管理器（iOS特有）
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _initializeOrientationManager(LogService logService) async {
    try {
      logService.info('App', 'Initializing orientation manager...');

      await OrientationManager.initialize();

      logService.info('App', 'Orientation manager initialized successfully');
      logService.debug('App', 'Supported orientations: ${OrientationManager.getSupportedOrientations()}');
    } catch (e) {
      logService.error('App', 'Failed to initialize orientation manager: $e', e);
      // 方向管理器初始化失败不应该阻止应用启动
      logService.warning('App', 'Continuing without orientation manager...');
    }
  }

  /// _initializePlatformWindowManager
  ///
  /// DESCRIPTION:
  ///     初始化平台窗口管理器
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _initializePlatformWindowManager(LogService logService) async {
    try {
      logService.info('App', 'Initializing platform window manager...');

      await PlatformWindowManager.initialize();

      logService.info('App', 'Platform window manager initialized successfully');
      logService.debug('App', 'Platform config: ${PlatformWindowManager.platformConfig}');
    } catch (e) {
      logService.error('App', 'Failed to initialize platform window manager: $e', e);
      // 窗口管理器初始化失败不应该阻止应用启动
      logService.warning('App', 'Continuing without platform window manager...');
    }
  }

  /// _handleApplePlatformInitializationFailure
  ///
  /// DESCRIPTION:
  ///     处理Apple平台初始化失败的降级机制
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///     error - 初始化失败的错误
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _handleApplePlatformInitializationFailure(LogService logService, dynamic error) async {
    try {
      logService.warning('App', 'Attempting Apple platform initialization fallback...');

      // 尝试重置平台服务工厂
      PlatformServiceFactory.resetInstances();

      // 记录详细的错误信息用于调试
      logService.error('App', 'Apple platform initialization failure details: $error');

      // 可以在这里添加更多的降级逻辑，比如：
      // - 尝试使用备用的初始化方法
      // - 禁用某些非关键功能
      // - 提供用户友好的错误提示

    } catch (fallbackError) {
      logService.error('App', 'Apple platform fallback also failed: $fallbackError', fallbackError);
    }
  }

  /// _handleAndroidPlatformInitializationFailure
  ///
  /// DESCRIPTION:
  ///     处理Android平台初始化失败的降级机制
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///     error - 初始化失败的错误
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _handleAndroidPlatformInitializationFailure(LogService logService, dynamic error) async {
    try {
      logService.warning('App', 'Attempting Android platform initialization fallback...');

      // 尝试重置平台服务工厂
      PlatformServiceFactory.resetInstances();

      // 记录详细的错误信息用于调试
      logService.error('App', 'Android platform initialization failure details: $error');

      // 可以在这里添加更多的降级逻辑，比如：
      // - 尝试使用备用的初始化方法
      // - 禁用某些非关键功能
      // - 提供用户友好的错误提示

    } catch (fallbackError) {
      logService.error('App', 'Android platform fallback also failed: $fallbackError', fallbackError);
    }
  }

  /// _handleDesktopPlatformInitializationFailure
  ///
  /// DESCRIPTION:
  ///     处理桌面平台初始化失败的降级机制
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///     error - 初始化失败的错误
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _handleDesktopPlatformInitializationFailure(LogService logService, dynamic error) async {
    try {
      logService.warning('App', 'Attempting desktop platform initialization fallback...');

      // 尝试重置平台服务工厂
      PlatformServiceFactory.resetInstances();

      // 记录详细的错误信息用于调试
      logService.error('App', 'Desktop platform initialization failure details: $error');

      // 桌面平台特有的降级逻辑
      // - 尝试不使用管理员权限启动后端
      // - 检查后端可执行文件是否存在
      // - 提供手动启动后端的指导

    } catch (fallbackError) {
      logService.error('App', 'Desktop platform fallback also failed: $fallbackError', fallbackError);
    }
  }

  /// _checkNetworkPermission
  ///
  /// DESCRIPTION:
  ///     检查网络权限状态（仅iOS需要）
  ///     通过实际的HTTP请求检测网络访问权限
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _checkNetworkPermission(LogService logService) async {
    try {
      final networkService = NetworkPermissionService.instance;

      // 只在iOS平台检查网络权限
      if (!networkService.isNetworkPermissionRequired()) {
        logService.info('App', 'Network permission check skipped for current platform');
        return;
      }

      logService.info('App', 'Checking network permission...');

      // 检查网络权限并显示通知（如果失败）
      final hasPermission = await networkService.checkNetworkPermissionWithNotification();

      if (hasPermission) {
        logService.info('App', 'Network permission granted');
      } else {
        logService.warning('App', 'Network permission not granted - user will see notification');
        // 注意：不抛出异常，让应用继续启动，用户可以稍后授权
      }
    } catch (e) {
      logService.error('App', 'Network permission check failed: $e', e);
      // 不抛出异常，让应用继续启动
    }
  }

  /// _initializeTrayService
  ///
  /// DESCRIPTION:
  ///     初始化系统托盘服务（仅Windows平台）
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  static Future<void> _initializeTrayService(LogService logService) async {
    try {
      logService.info('App', 'Initializing tray service...');

      final trayService = serviceLocator<TrayService>();
      final success = await trayService.initialize();

      if (success) {
        logService.info('App', 'Tray service initialized successfully');
      } else {
        logService.warning('App', 'Tray service initialization failed');
      }
    } catch (e) {
      logService.error('App', 'Failed to initialize tray service: $e', e);
      // 托盘服务初始化失败不应该阻止应用启动
    }
  }

  /// _checkVPNPermission
  ///
  /// DESCRIPTION:
  ///     检查VPN权限，主动请求权限以避免连接时的权限拒绝问题
  ///     遵循用户偏好：在应用启动时主动请求VPN权限，而不是等到连接时
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  ///
  /// RETURNS:
  ///     Future<void>
  static Future<void> _checkVPNPermission(LogService logService) async {
    try {
      logService.info('App', 'Checking VPN permission...');

      // 注意：VPN权限检查需要在后端服务初始化后进行
      // 这里只是记录日志，实际的权限请求将在用户首次尝试连接时进行
      // 但我们可以在这里检查权限状态并给用户提示

      logService.info('App', 'VPN permission check completed - will be requested on first connection attempt');
    } catch (e) {
      logService.error('App', 'VPN permission check failed: $e', e);
      // 不抛出异常，让应用继续启动
    }
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     清理初始化服务的资源
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  static void dispose() {
    _singleInstanceService?.dispose();
    _singleInstanceService = null;
    DependencyInjection.dispose();
  }
}

/// _DummySingleInstanceService
///
/// PURPOSE:
///     为Apple平台提供虚拟的单例服务实现
///
/// DESCRIPTION:
///     Apple平台不需要真正的单例检查，但main()函数期望得到一个SingleInstanceService实例
///     这个虚拟实现满足接口要求但不执行任何实际操作
class _DummySingleInstanceService implements SingleInstanceService {
  @override
  bool get isFirstInstance => true; // Apple平台总是第一个实例

  @override
  Future<bool> init() async {
    // Apple平台不需要单例检查，总是返回true
    return true;
  }

  @override
  Future<void> dispose() async {
    // Apple平台不需要清理操作
  }
}
