/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      backend_service.dart
///
/// DESCRIPTION :    后端服务管理器，负责启动、监控和停止后端WAN服务进程，
///                  支持管理员权限启动和跨平台兼容性
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:http/http.dart' as http;

import 'log_service.dart';
import '../utils/constants.dart';

/// BackendService
///
/// PURPOSE:
///     Windows平台专用的后端服务管理器，负责启动、监控和停止后端WAN服务进程
///
/// FEATURES:
///     - 管理员权限启动：以管理员权限启动后端服务作为子进程
///     - 子进程管理：确保后端服务作为当前进程的子进程运行
///     - 进程生命周期绑定：父进程退出时子进程也会被终止
///     - 健康检查：通过HTTP API验证服务启动状态
///     - 优雅关闭：支持HTTP shutdown和进程终止两种停止方式
///     - 启动同步：防止重复启动，支持启动状态等待
///
/// USAGE:
///     Windows平台专用，通过依赖注入获取实例，调用start()启动服务，stop()停止服务
class BackendService {
  /// 日志服务实例，用于记录后端服务相关事件
  final LogService logService;

  /// 后端服务进程实例
  Process? _process;

  /// 后端服务运行状态标志
  bool _isRunning = false;

  /// 启动操作完成器，用于同步启动状态
  Completer<bool>? _startCompleter;

  /// 启动中状态标志，防止重复启动
  bool _isStarting = false;

  /// BackendService构造函数
  ///
  /// DESCRIPTION:
  ///     创建后端服务管理器实例
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例，用于记录事件
  BackendService({required this.logService});

  /// start
  ///
  /// DESCRIPTION:
  ///     启动后端服务，必须使用管理员权限启动
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 启动成功返回true，失败返回false
  Future<bool> start() async {
    return await _startWithAdminPrivileges();
  }

  /// _startWithAdminPrivileges
  ///
  /// DESCRIPTION:
  ///     以管理员权限启动后端服务，所有平台都必须使用管理员权限
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 启动成功返回true，失败返回false
  ///
  /// THROWS:
  ///     Exception - 启动过程中发生错误时记录日志并返回false
  Future<bool> _startWithAdminPrivileges() async {
    if (_isRunning) {
      return true;
    }

    if (_isStarting) {
      // 如果正在启动，等待启动完成
      if (_startCompleter != null) {
        return await _startCompleter!.future;
      }
      return false;
    }

    _isStarting = true;
    _startCompleter = Completer<bool>();

    logService.info('Backend', 'Starting backend service with admin privileges...');

    try {
      // 获取后端服务路径
      final executablePath = await _getExecutablePath();
      if (executablePath == null) {
        logService.error('Backend', 'Backend executable not found');
        _startCompleter!.complete(false);
        _isStarting = false;
        return false;
      }

      // logService.debug('Backend', 'Found backend executable at: $executablePath');

      // Windows平台专用 - 使用PowerShell启动管理员权限进程作为子服务
      try {
        logService.info('Backend', 'Starting backend with administrator privileges as child process');

        final workingDirectory = path.dirname(executablePath);

        _process = await Process.start(
          'powershell',
          [
            '-Command',
            'Start-Process -FilePath "$executablePath" -WorkingDirectory "$workingDirectory" -Verb RunAs -WindowStyle Hidden -Wait'
                .replaceAll('\$executablePath', '"$executablePath"')
                .replaceAll('\$workingDirectory', '"$workingDirectory"')
          ],
          mode: ProcessStartMode.normal, // 保持父子进程关系
          workingDirectory: workingDirectory,
          runInShell: false,
        );

        // 监听进程输出
        _process!.stdout.transform(const SystemEncoding().decoder).listen((data) {
          logService.info('Backend', data.trim());
        });

        _process!.stderr.transform(const SystemEncoding().decoder).listen((data) {
          logService.error('Backend', data.trim());
        });

        // 监听进程退出
        _process!.exitCode.then((exitCode) {
          logService.info('Backend', 'Backend admin process exited with code: $exitCode');
          _isRunning = false;
          if (!_startCompleter!.isCompleted) {
            _isStarting = false;
            _startCompleter!.complete(false);
          }
        });

        logService.info('Backend', 'Backend process started as child service with administrator privileges');

        // 等待服务启动
        bool serviceStarted = await _waitForServiceToStart();
        _isStarting = false;
        _isRunning = serviceStarted;
        _startCompleter!.complete(serviceStarted);
        return serviceStarted;
      } catch (e) {
        logService.error('Backend', 'Error launching with admin privileges', e);
        _isStarting = false;
        if (!_startCompleter!.isCompleted) {
          _startCompleter!.complete(false);
        }
        return false;
      }
    } catch (e) {
      logService.error('Backend', 'Failed to start backend service', e);
      if (!_startCompleter!.isCompleted) {
        _startCompleter!.complete(false);
      }
      _isStarting = false;
      return false;
    }
  }



  /// _waitForServiceToStart
  ///
  /// DESCRIPTION:
  ///     等待服务启动，通过端口检查和健康检查API验证服务状态
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 服务启动成功返回true，超时返回false
  ///
  /// THROWS:
  ///     Exception - 检查过程中发生错误时记录日志并继续尝试
  Future<bool> _waitForServiceToStart() async {
    // 增加初始等待时间，给后端服务更多启动时间
    // 尝试连接服务器最多40次，每次间隔500毫秒，总共20秒
    for (int i = 0; i < 40; i++) {
      try {
        // 先尝试TCP连接检查端口是否开放
        try {
          final socket = await Socket.connect('localhost', BackendConfig.servicePort, timeout: const Duration(milliseconds: 500));
          await socket.close();
          logService.info('Backend', 'Port $BackendConfig.servicePort is open (attempt ${i+1})');
        } catch (e) {
          // 端口未开放，继续等待
          if (i % 5 == 0) { // 每5次尝试记录一次日志，避免日志过多
            // logService.debug('Backend', 'Port $BackendConfig.servicePort is not open yet (attempt ${i+1}): $e');
          }
          await Future.delayed(const Duration(milliseconds: 500));
          continue;
        }

        // 端口开放后，尝试调用健康检查API
        final client = http.Client();
        try {
          // 明确构建健康检查URL，避免字符串插值问题
          const port = BackendConfig.servicePort;
          const healthCheckUrl = 'http://localhost:$port/api/health';
          // logService.debug('Backend', 'Health check URL: $healthCheckUrl');

          // 增加超时时间，避免网络波动导致的失败
          final response = await client.get(
            Uri.parse(healthCheckUrl),
            headers: {'Content-Type': 'application/json'},
          ).timeout(const Duration(seconds: 20)); // 增加超时时间到20秒

          // 记录完整的响应内容，便于调试
          // logService.debug('Backend', 'Health check response: ${response.statusCode}, body: ${response.body}');

          if (response.statusCode >= 200 && response.statusCode < 300) {
            try {
              // 尝试解析响应体
              final data = json.decode(response.body);
              if (data['success'] == true) {
                // 检查数据中的状态
                if (data['data'] != null && data['data'] is Map<String, dynamic>) {
                  final healthData = data['data'] as Map<String, dynamic>;
                  if (healthData['status'] == 'ok') {
                    logService.info('Backend', 'Health check passed, service is ready');
                    return true;
                  }
                }
                logService.info('Backend', 'Health check passed with success=true');
                return true; // 如果成功字段为true，也认为健康
              }
              // 即使success=false，如果状态码是200，也认为服务已启动
              logService.info('Backend', 'Health check returned success=false but status code 200, considering service ready');
              return true;
            } catch (e) {
              logService.warning('Backend', 'Health check response parse error: $e');
              // 如果响应体不是JSON格式，但状态码正常，也认为健康
              logService.info('Backend', 'Health check passed with status code ${response.statusCode}');
              return true;
            }
          } else if (response.statusCode == 404) {
            // 404表示服务器在运行，但健康检查端点可能还没有注册
            // 这通常发生在服务启动过程中
            logService.info('Backend', 'Health check endpoint not found (404), but server is running. Considering service ready.');
            return true;
          } else {
            logService.warning('Backend', 'Health check returned status code: ${response.statusCode}');
          }
        } catch (e) {
          // 如果端口已开放但健康检查请求失败，可能是服务正在启动中
          // 如果已经尝试了多次，我们可以认为服务已经启动
          if (i > 30) { // 15秒后
            logService.info('Backend', 'Health check request failed but port is open. Assuming service is starting up. Considering service ready.');
            return true;
          }
          logService.warning('Backend', 'Health check request failed: $e');
        } finally {
          client.close();
        }
      } catch (e) {
        logService.warning('Backend', 'Service start check failed: $e');
      }

      // 等待一段时间再次尝试
      await Future.delayed(const Duration(milliseconds: 500));
    }

    logService.error('Backend', 'Timed out waiting for service to start');
    return false;
  }

  /// stop
  ///
  /// DESCRIPTION:
  ///     停止后端服务，先尝试HTTP shutdown，失败则强制终止进程
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 停止过程中发生错误时记录日志但不抛出异常
  Future<void> stop() async {
    if (!_isRunning) {
      return;
    }

    logService.info('Backend', 'Stopping backend service...');

    try {
      // 先尝试使用HTTP shutdown接口
      final success = await _shutdownViaHttp();

      if (success) {
        logService.info('Backend', 'Backend service stopped via HTTP shutdown');
      } else if (_process != null) {
        // 如果HTTP shutdown失败，则尝试杀死进程
        logService.warning('Backend', 'HTTP shutdown failed, killing process...');
        _process!.kill();
        logService.info('Backend', 'Backend service stopped by killing process');
      }

      _process = null;
      _isRunning = false;
    } catch (e) {
      logService.error('Backend', 'Failed to stop backend service', e);

      // 尝试杀死进程作为最后的手段
      if (_process != null) {
        try {
          _process!.kill();
          _process = null;
          _isRunning = false;
          logService.info('Backend', 'Backend service stopped by killing process after error');
        } catch (e2) {
          logService.error('Backend', 'Failed to kill process after error', e2);
        }
      }
    }
  }

  /// _shutdownViaHttp
  ///
  /// DESCRIPTION:
  ///     通过HTTP接口优雅关闭后端服务
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 关闭成功返回true，失败返回false
  ///
  /// THROWS:
  ///     Exception - HTTP请求失败时记录日志并返回true（假设服务已关闭）
  Future<bool> _shutdownViaHttp() async {
    try {
      final url = Uri.parse('$kApiBaseUrl/shutdown');
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 2));

      // 如果请求成功，返回true
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      // 忽略错误，因为服务可能已经关闭
      logService.info('Backend', 'HTTP shutdown request may have succeeded (service already closed)');
      return true;
    }
  }

  /// stopBackend
  ///
  /// DESCRIPTION:
  ///     公共API：停止后端服务，提供给外部调用的接口
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> stopBackend() async {
    logService.info('Backend', 'Stopping backend service via public API');
    await stop();
  }

  /// _getExecutablePath
  ///
  /// DESCRIPTION:
  ///     获取后端服务可执行文件路径，在应用程序目录和当前目录中查找
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<String?> - 找到可执行文件返回路径，否则返回null
  ///
  /// THROWS:
  ///     Exception - 查找过程中发生错误时记录日志并返回null
  Future<String?> _getExecutablePath() async {
    try {
      // 获取应用程序目录
      final exePath = Platform.resolvedExecutable;
      final appDir = path.dirname(exePath);
      // logService.debug('Backend', 'Application directory: $appDir');

      // 后端服务路径
      final backendPath = path.join(appDir, BackendConfig.servicePath);
      // logService.debug('Backend', 'Backend service path: $backendPath');

      // 检查文件是否存在
      final file = File(backendPath);
      final exists = await file.exists();
      // logService.debug('Backend', 'Check path: $backendPath, exists: $exists');

      if (exists) {
        return backendPath;
      }

      // 如果在应用程序目录中找不到，尝试在当前目录中查找
      final currentDir = Directory.current.path;
      logService.info('Backend', 'Current directory: $currentDir');

      final currentDirPath = path.join(currentDir, BackendConfig.servicePath);
      final currentDirExists = await File(currentDirPath).exists();
      logService.info('Backend', 'Check path: $currentDirPath, exists: $currentDirExists');

      if (currentDirExists) {
        return currentDirPath;
      }

      logService.error('Backend', 'Backend service executable not found');
      return null;
    } catch (e) {
      logService.error('Backend', 'Error finding backend executable', e);
      return null;
    }
  }

  /// isRunning
  ///
  /// DESCRIPTION:
  ///     检查后端服务是否正在运行
  ///
  /// RETURNS:
  ///     bool - 服务运行返回true，否则返回false
  bool get isRunning => _isRunning;
}
