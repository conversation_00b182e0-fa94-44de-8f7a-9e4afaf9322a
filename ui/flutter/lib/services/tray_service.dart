/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      tray_service.dart
///
/// DESCRIPTION :    系统托盘服务，负责管理应用的系统托盘功能
///
/// AUTHOR :         Panabit Team
///
/// HISTORY :        2025-01-23 create

import 'dart:io';
import 'package:tray_manager/tray_manager.dart';
import 'package:window_manager/window_manager.dart';
import 'package:get_it/get_it.dart';

import '../utils/constants.dart';
import 'log_service.dart';
import 'api_service.dart';
import 'backend_service.dart';
import 'language_service.dart';
import '../core/app_state.dart';

/// TrayService
///
/// PURPOSE:
///     系统托盘服务，负责管理应用的系统托盘功能
///
/// FEATURES:
///     - 托盘图标管理：设置和更新系统托盘图标
///     - 右键菜单：创建和管理托盘右键上下文菜单
///     - 窗口控制：通过托盘菜单控制窗口显示/隐藏
///     - 事件处理：处理托盘图标点击和菜单选择事件
///     - 应用退出：提供完整的应用退出流程
///
/// USAGE:
///     在应用初始化时创建实例并调用initialize()方法，
///     在应用退出时调用dispose()方法清理资源
class TrayService with TrayListener {
  /// 日志服务实例
  final LogService _logService;

  /// 语言服务实例
  LanguageService? _languageService;

  /// 是否已初始化标志
  bool _isInitialized = false;

  /// 窗口是否可见标志
  bool _isWindowVisible = true;

  /// TrayService构造函数
  ///
  /// DESCRIPTION:
  ///     创建系统托盘服务实例
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例
  TrayService({required LogService logService}) : _logService = logService;

  /// initialize
  ///
  /// DESCRIPTION:
  ///     初始化系统托盘服务，设置托盘图标和菜单
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 初始化是否成功
  ///
  /// THROWS:
  ///     Exception - 初始化过程中发生错误时抛出异常
  Future<bool> initialize() async {
    if (_isInitialized) {
      _logService.warning('TrayService', 'Tray service already initialized');
      return true;
    }

    try {
      _logService.info('TrayService', 'Initializing tray service...');

      // 只在Windows平台启用系统托盘
      if (!Platform.isWindows) {
        _logService.info('TrayService', 'Tray service is only supported on Windows platform');
        return false;
      }

      // 获取语言服务实例
      try {
        _languageService = GetIt.instance<LanguageService>();
        // 监听语言变化，更新托盘菜单
        _languageService?.addListener(_onLanguageChanged);
      } catch (e) {
        _logService.warning('TrayService', 'LanguageService not available, using default text: $e');
      }

      // 添加托盘事件监听器
      trayManager.addListener(this);

      // 设置托盘图标
      await _setTrayIcon();

      // 设置托盘菜单
      await _setTrayMenu();

      _isInitialized = true;
      _logService.info('TrayService', 'Tray service initialized successfully');
      return true;
    } catch (e) {
      _logService.error('TrayService', 'Failed to initialize tray service', e);
      return false;
    }
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     清理系统托盘服务资源
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> dispose() async {
    if (!_isInitialized) {
      return;
    }

    try {
      _logService.info('TrayService', 'Disposing tray service...');

      // 移除语言变化监听器
      _languageService?.removeListener(_onLanguageChanged);

      // 移除托盘事件监听器
      trayManager.removeListener(this);

      // 销毁托盘图标
      await trayManager.destroy();

      _isInitialized = false;
      _logService.info('TrayService', 'Tray service disposed successfully');
    } catch (e) {
      _logService.error('TrayService', 'Failed to dispose tray service', e);
    }
  }

  /// hideWindow
  ///
  /// DESCRIPTION:
  ///     隐藏应用窗口到系统托盘
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> hideWindow() async {
    try {
      await windowManager.hide();
      _isWindowVisible = false;
      await _updateTrayMenu(); // 更新托盘菜单文本
      _logService.info('TrayService', 'Window hidden to system tray');
    } catch (e) {
      _logService.error('TrayService', 'Failed to hide window', e);
    }
  }

  /// showWindow
  ///
  /// DESCRIPTION:
  ///     从系统托盘显示应用窗口
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> showWindow() async {
    try {
      await windowManager.show();
      await windowManager.focus();
      _isWindowVisible = true;
      await _updateTrayMenu(); // 更新托盘菜单文本
      _logService.info('TrayService', 'Window shown from system tray');
    } catch (e) {
      _logService.error('TrayService', 'Failed to show window', e);
    }
  }

  /// _setTrayIcon
  ///
  /// DESCRIPTION:
  ///     设置系统托盘图标
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _setTrayIcon() async {
    try {
      // Windows平台使用.ico格式图标
      const iconPath = 'assets/icons/app_icon.ico';
      await trayManager.setIcon(iconPath);
      
      // 设置托盘图标提示文本
      await trayManager.setToolTip(kAppTitle);
      
      _logService.info('TrayService', 'Tray icon set successfully');
    } catch (e) {
      _logService.error('TrayService', 'Failed to set tray icon', e);
      rethrow;
    }
  }

  /// _setTrayMenu
  ///
  /// DESCRIPTION:
  ///     设置系统托盘右键菜单，支持国际化文本
  ///     确保菜单具有正确的焦点管理和自动关闭行为
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _setTrayMenu() async {
    try {
      // 获取本地化文本
      String showHideLabel;
      String exitLabel;

      if (_languageService != null) {
        final locale = _languageService!.locale;
        if (locale.languageCode == 'zh') {
          showHideLabel = _isWindowVisible ? '隐藏窗口' : '显示窗口';
          exitLabel = '退出应用';
        } else {
          showHideLabel = _isWindowVisible ? 'Hide Window' : 'Show Window';
          exitLabel = 'Exit App';
        }
      } else {
        // 默认使用中文
        showHideLabel = _isWindowVisible ? '隐藏窗口' : '显示窗口';
        exitLabel = '退出应用';
      }

      // 创建菜单，确保菜单项具有正确的属性
      final menu = Menu(
        items: [
          MenuItem(
            key: 'show_hide_window',
            label: showHideLabel,
            // 确保菜单项可以正确响应点击事件
          ),
          MenuItem.separator(),
          MenuItem(
            key: 'exit_app',
            label: exitLabel,
            // 确保菜单项可以正确响应点击事件
          ),
        ],
      );

      // 设置上下文菜单
      await trayManager.setContextMenu(menu);
      _logService.info('TrayService', 'Tray menu set successfully with ${menu.items?.length ?? 0} items');
    } catch (e) {
      _logService.error('TrayService', 'Failed to set tray menu', e);
      rethrow;
    }
  }

  /// _updateTrayMenu
  ///
  /// DESCRIPTION:
  ///     更新系统托盘菜单，主要用于更新显示/隐藏窗口的菜单项文本
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _updateTrayMenu() async {
    await _setTrayMenu();
  }

  /// _ensureMenuFocusManagement
  ///
  /// DESCRIPTION:
  ///     确保托盘菜单具有正确的焦点管理
  ///     简化的实现，避免干扰窗口状态
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _ensureMenuFocusManagement() async {
    try {
      // 简化的焦点管理，只在窗口可见时进行焦点设置
      if (Platform.isWindows) {
        final isVisible = await windowManager.isVisible();
        if (isVisible) {
          // 如果窗口可见，确保它有正确的焦点状态
          await windowManager.focus();
        }
        // 不再进行临时显示/隐藏操作，避免干扰托盘事件处理
      }
    } catch (e) {
      _logService.warning('TrayService', 'Failed to ensure menu focus management: $e');
    }
  }



  /// _onLanguageChanged
  ///
  /// DESCRIPTION:
  ///     语言变化回调，更新托盘菜单文本
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _onLanguageChanged() {
    if (_isInitialized) {
      _logService.info('TrayService', 'Language changed, updating tray menu');
      _updateTrayMenu();
    }
  }

  /// _exitApplication
  ///
  /// DESCRIPTION:
  ///     执行完整的应用退出流程，与现有退出逻辑保持一致
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _exitApplication() async {
    try {
      _logService.info('TrayService', 'User chose to exit via system tray');

      final apiService = GetIt.instance<ApiService>();
      final backendService = GetIt.instance<BackendService>();
      final appState = GetIt.instance<AppState>();

      // 1. 如果已连接，先断开连接
      if (appState.connectionStatus == ConnectionStatus.connected) {
        _logService.info('TrayService', 'Disconnecting WAN...');

        try {
          // 断开WAN连接
          await apiService.disconnect();
          _logService.info('TrayService', 'WAN disconnected successfully');
          // 等待一下，确保断开连接完成
          await Future.delayed(const Duration(milliseconds: 500));
        } catch (e) {
          _logService.error('TrayService', 'Failed to disconnect WAN', e);
        }
      }

      // 2. 后端服务处理：Windows 只断开连接，其他平台关闭服务
      if (Platform.isWindows) {
        // Windows 服务化部署：只断开连接，保持后端服务运行
        _logService.info('TrayService', 'Windows platform: Only disconnecting, keeping backend service running...');
        // 注意：这里不需要额外调用 disconnect，因为步骤1已经处理了断开连接
      } else {
        // 其他平台：停止后端服务
        _logService.info('TrayService', 'Stopping backend service...');

        // 尝试通过API关闭后端服务
        bool backendShutdownSuccess = false;
        try {
          final result = await apiService.shutdownBackend();
          _logService.info('TrayService', 'Backend service shutdown request result: $result');
          // 给后端一些时间来处理关闭请求
          await Future.delayed(const Duration(milliseconds: 500));
          backendShutdownSuccess = true;
        } catch (e) {
          // 忽略错误，因为后端服务可能已经关闭
          _logService.info('TrayService', 'Backend service shutdown via API failed: $e');
        }

        // 如果通过API关闭失败，尝试通过BackendService停止后端
        if (!backendShutdownSuccess) {
          try {
            await backendService.stopBackend();
            _logService.info('TrayService', 'Backend service stopped via BackendService successfully');
            backendShutdownSuccess = true;
          } catch (e) {
            _logService.error('TrayService', 'Failed to stop backend service via BackendService', e);
          }
        }

        _logService.info('TrayService', 'Backend service shutdown ${backendShutdownSuccess ? 'successful' : 'may have failed'}');
      }

      // 3. 清理托盘资源
      await dispose();

      // 4. 关闭窗口
      _logService.info('TrayService', 'Preparing to close window...');
      try {
        // 强制关闭窗口
        _logService.info('TrayService', 'Setting preventClose to false');
        await windowManager.setPreventClose(false);
        _logService.info('TrayService', 'Calling windowManager.destroy()');
        await windowManager.destroy();
        _logService.info('TrayService', 'Window destruction completed');
      } catch (e) {
        _logService.error('TrayService', 'Failed to destroy window', e);
      }

      // 5. 退出应用
      _logService.info('TrayService', 'Force exiting application');
      exit(0);
    } catch (e) {
      _logService.error('TrayService', 'Error occurred while exiting application', e);
      // 即使出错，也强制退出
      exit(1);
    }
  }

  // TrayListener 接口实现

  /// onTrayIconMouseDown
  ///
  /// DESCRIPTION:
  ///     处理托盘图标左键点击事件，切换窗口显示/隐藏状态
  @override
  void onTrayIconMouseDown() {
    _logService.info('TrayService', 'Tray icon clicked');

    if (_isWindowVisible) {
      hideWindow();
    } else {
      showWindow();
    }
  }

  /// onTrayIconRightMouseDown
  ///
  /// DESCRIPTION:
  ///     处理托盘图标右键按下事件，显示上下文菜单
  @override
  void onTrayIconRightMouseDown() {
    _logService.info('TrayService', 'Tray icon right-clicked');

    // 直接显示上下文菜单，使用同步方式避免状态问题
    try {
      trayManager.popUpContextMenu();
    } catch (e) {
      _logService.error('TrayService', 'Failed to show context menu', e);
    }
  }

  /// onTrayIconRightMouseUp
  ///
  /// DESCRIPTION:
  ///     处理托盘图标右键释放事件
  ///     在标准实现中，菜单已经在MouseDown中显示
  @override
  void onTrayIconRightMouseUp() {
    _logService.info('TrayService', 'Tray icon right mouse up');
    // 菜单已经在MouseDown中显示，这里不需要额外操作
  }

  /// onTrayMenuItemClick
  ///
  /// DESCRIPTION:
  ///     处理托盘菜单项点击事件
  ///     菜单项被点击后，菜单会自动隐藏（这是系统默认行为）
  ///
  /// PARAMETERS:
  ///     menuItem - 被点击的菜单项
  @override
  void onTrayMenuItemClick(MenuItem menuItem) {
    _logService.info('TrayService', 'Tray menu item clicked: ${menuItem.key}');

    switch (menuItem.key) {
      case 'show_hide_window':
        if (_isWindowVisible) {
          hideWindow();
        } else {
          showWindow();
        }
        break;
      case 'exit_app':
        _exitApplication();
        break;
      default:
        _logService.warning('TrayService', 'Unknown menu item clicked: ${menuItem.key}');
    }
  }
}
