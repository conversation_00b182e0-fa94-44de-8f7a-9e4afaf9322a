/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      theme_service.dart
///
/// DESCRIPTION :    Theme service for managing application theme settings
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../utils/constants.dart';
import '../utils/design_system.dart';
import '../utils/async_operation_manager.dart';
import 'log_service.dart';

/// ThemeService
///
/// PURPOSE:
///     Manages application theme settings and provides theme switching functionality
///     with persistent storage and real-time updates
///
/// FEATURES:
///     - Support for light, dark, and system theme modes
///     - Persistent theme storage using SharedPreferences
///     - Asynchronous theme operations to avoid UI blocking
///     - Real-time theme change notifications
///     - Integration with design system for consistent theming
///
/// USAGE:
///     final themeService = serviceLocator<ThemeService>();
///     await themeService.setTheme('dark');
///     ThemeData lightTheme = themeService.getLightTheme();
///     bool isDark = themeService.isDarkMode;
class ThemeService extends ChangeNotifier {
  final LogService logService;
  final _ThemeServiceHelper _helper = _ThemeServiceHelper();
  ThemeMode _themeMode = ThemeMode.system;

  /// ThemeService constructor
  ///
  /// DESCRIPTION:
  ///     Creates a new theme service instance and loads saved theme
  ///
  /// PARAMETERS:
  ///     logService - LogService: Service for logging operations
  ThemeService({required this.logService}) {
    _helper.setLogService(logService);
    _loadTheme();
  }

  /// themeMode
  ///
  /// DESCRIPTION:
  ///     Gets the current theme mode
  ///
  /// RETURNS:
  ///     ThemeMode - The current theme mode setting
  ThemeMode get themeMode => _themeMode;

  /// isDarkMode
  ///
  /// DESCRIPTION:
  ///     Checks if the current theme is dark mode
  ///
  /// RETURNS:
  ///     bool - True if dark mode is active, false otherwise
  bool get isDarkMode => _themeMode == ThemeMode.dark;

  /// isLightMode
  ///
  /// DESCRIPTION:
  ///     Checks if the current theme is light mode
  ///
  /// RETURNS:
  ///     bool - True if light mode is active, false otherwise
  bool get isLightMode => _themeMode == ThemeMode.light;

  /// isSystemMode
  ///
  /// DESCRIPTION:
  ///     Checks if the current theme follows system settings
  ///
  /// RETURNS:
  ///     bool - True if system mode is active, false otherwise
  bool get isSystemMode => _themeMode == ThemeMode.system;

  /// _loadTheme
  ///
  /// DESCRIPTION:
  ///     Loads theme settings from SharedPreferences
  ///
  /// RETURNS:
  ///     Future<void> - Completes when theme loading is finished
  Future<void> _loadTheme() async {
    try {
      final themeString = await _helper.loadThemePreference();
      _themeMode = _helper.parseThemeMode(themeString);
      _helper.logThemeLoaded(themeString);
      notifyListeners();
    } catch (e) {
      _helper.logThemeLoadError(e);
    }
  }

  /// setTheme
  ///
  /// DESCRIPTION:
  ///     Sets the application theme and saves the preference
  ///
  /// PARAMETERS:
  ///     theme - String: The theme to set ('light', 'dark', or 'system')
  ///
  /// RETURNS:
  ///     Future<void> - Completes when theme change is finished
  Future<void> setTheme(String theme) async {
    try {
      await _helper.saveThemePreference(theme);
      _themeMode = _helper.parseThemeMode(theme);
      _helper.logThemeChanged(theme);
      notifyListeners();
    } catch (e) {
      _helper.logThemeChangeError(e);
    }
  }

  /// getLightTheme
  ///
  /// DESCRIPTION:
  ///     Gets the light theme data from design system
  ///
  /// RETURNS:
  ///     ThemeData - The light theme configuration
  ThemeData getLightTheme() {
    return DesignSystemTheme.createAppTheme();
  }

  /// getDarkTheme
  ///
  /// DESCRIPTION:
  ///     Gets the dark theme data from design system
  ///     Note: Design system uses unified theme, no distinction between light/dark
  ///
  /// RETURNS:
  ///     ThemeData - The dark theme configuration
  ThemeData getDarkTheme() {
    return DesignSystemTheme.createAppTheme();
  }
}

/// _ThemeServiceHelper
///
/// PURPOSE:
///     Helper class for ThemeService to handle theme operations and logging
///
/// FEATURES:
///     - Theme preference loading and saving
///     - Theme mode parsing and validation
///     - Unified logging for all theme operations
///     - Asynchronous operations management
///
/// USAGE:
///     Internal helper class used by ThemeService
class _ThemeServiceHelper {
  LogService? _logService;

  /// setLogService
  ///
  /// DESCRIPTION:
  ///     Sets the log service instance for this helper
  ///
  /// PARAMETERS:
  ///     logService - LogService: The log service to use
  void setLogService(LogService logService) {
    _logService = logService;
  }

  /// loadThemePreference
  ///
  /// DESCRIPTION:
  ///     Loads theme preference from SharedPreferences
  ///
  /// RETURNS:
  ///     Future<String> - The saved theme string or 'system' as default
  Future<String> loadThemePreference() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(StorageKeys.theme) ?? 'system';
  }

  /// saveThemePreference
  ///
  /// DESCRIPTION:
  ///     Saves theme preference to SharedPreferences using async operation manager
  ///
  /// PARAMETERS:
  ///     theme - String: The theme to save
  ///
  /// RETURNS:
  ///     Future<void> - Completes when save operation is finished
  Future<void> saveThemePreference(String theme) async {
    await AsyncOperationManager.executeNonBlocking<void>(
      operation: () async {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(StorageKeys.theme, theme);
      },
      operationId: 'set_theme_$theme',
      timeout: const Duration(seconds: 3),
    );
  }

  /// parseThemeMode
  ///
  /// DESCRIPTION:
  ///     Parses theme string to ThemeMode enum
  ///
  /// PARAMETERS:
  ///     themeString - String: The theme string to parse
  ///
  /// RETURNS:
  ///     ThemeMode - The corresponding theme mode
  ThemeMode parseThemeMode(String themeString) {
    switch (themeString) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      case 'system':
      default:
        return ThemeMode.system;
    }
  }

  /// logThemeLoaded
  ///
  /// DESCRIPTION:
  ///     Logs successful theme loading
  ///
  /// PARAMETERS:
  ///     themeString - String: The loaded theme string
  void logThemeLoaded(String themeString) {
    _logService?.info('Theme', 'Theme loaded: $themeString');
  }

  /// logThemeLoadError
  ///
  /// DESCRIPTION:
  ///     Logs theme loading error
  ///
  /// PARAMETERS:
  ///     error - dynamic: The error that occurred
  void logThemeLoadError(dynamic error) {
    _logService?.error('Theme', 'Failed to load theme', error);
  }

  /// logThemeChanged
  ///
  /// DESCRIPTION:
  ///     Logs successful theme change
  ///
  /// PARAMETERS:
  ///     theme - String: The new theme
  void logThemeChanged(String theme) {
    _logService?.info('Theme', 'Theme changed to $theme');
  }

  /// logThemeChangeError
  ///
  /// DESCRIPTION:
  ///     Logs theme change error
  ///
  /// PARAMETERS:
  ///     error - dynamic: The error that occurred
  void logThemeChangeError(dynamic error) {
    _logService?.error('Theme', 'Failed to set theme', error);
  }
}
