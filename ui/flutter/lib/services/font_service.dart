/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      font_service.dart
///
/// DESCRIPTION :    字体服务，负责管理应用程序的字体选择和文本样式配置，
///                  统一使用SourceHanSansCN字体以提供最佳的中英文混合显示效果
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'package:flutter/material.dart';

/// FontService
///
/// PURPOSE:
///     字体服务，负责管理应用程序的字体选择和文本样式配置
///
/// FEATURES:
///     - 统一字体体验：为中英文提供统一的SourceHanSansCN字体
///     - 字体权重映射：将标准字体权重映射到SourceHanSansCN可用权重
///     - 文本样式创建：提供便捷的TextStyle创建方法
///     - 字体回退支持：配置字体回退列表确保文本正确显示
///     - 样式应用：支持将字体应用到现有TextStyle
///
/// USAGE:
///     通过依赖注入获取实例，调用相关方法创建和配置文本样式
class FontService {
  /// FontService构造函数
  ///
  /// DESCRIPTION:
  ///     创建字体服务实例，统一使用SourceHanSansCN字体
  ///
  /// PARAMETERS:
  ///     无
  FontService() {
    // 现在完全不依赖LanguageService，统一使用SourceHanSansCN
  }

  /// primaryFontFamily
  ///
  /// DESCRIPTION:
  ///     获取主要字体族名称，始终返回SourceHanSansCN
  ///
  /// RETURNS:
  ///     String - 主要字体族名称
  String get primaryFontFamily {
    return 'SourceHanSansCN';
  }

  /// fontFamilyFallback
  ///
  /// DESCRIPTION:
  ///     获取字体回退列表，仅包含SourceHanSansCN
  ///
  /// RETURNS:
  ///     List<String> - 字体回退列表
  List<String> get fontFamilyFallback {
    // 只使用SourceHanSansCN，不使用其他字体
    return ['SourceHanSansCN'];
  }

  /// createTextStyle
  ///
  /// DESCRIPTION:
  ///     创建带有适当字体族的TextStyle，使用SourceHanSansCN字体
  ///
  /// PARAMETERS:
  ///     fontSize - 字体大小
  ///     fontWeight - 字体权重
  ///     color - 文本颜色
  ///     height - 行高
  ///     letterSpacing - 字符间距
  ///     decoration - 文本装饰
  ///     textBaseline - 文本基线
  ///     leadingDistribution - 行高分布方式
  ///
  /// RETURNS:
  ///     TextStyle - 配置好的文本样式
  TextStyle createTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
    TextDecoration? decoration,
    TextBaseline? textBaseline,
    TextLeadingDistribution? leadingDistribution,
  }) {
    return TextStyle(
      fontFamily: primaryFontFamily,
      fontFamilyFallback: fontFamilyFallback,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      letterSpacing: letterSpacing,
      decoration: decoration ?? TextDecoration.none,
      textBaseline: textBaseline,
      leadingDistribution: leadingDistribution,
    );
  }

  /// applyFontFamily
  ///
  /// DESCRIPTION:
  ///     将字体族应用到现有的TextStyle
  ///
  /// PARAMETERS:
  ///     style - 现有的TextStyle
  ///
  /// RETURNS:
  ///     TextStyle - 应用了SourceHanSansCN字体的新TextStyle
  TextStyle applyFontFamily(TextStyle style) {
    return style.copyWith(
      fontFamily: primaryFontFamily,
      fontFamilyFallback: fontFamilyFallback,
    );
  }

  /// mapFontWeight
  ///
  /// DESCRIPTION:
  ///     获取SourceHanSansCN的字体权重映射，确保使用字体文件中可用的正确权重
  ///     可用权重：Light(300)、Regular(400)、Medium(500)、Bold(700)
  ///
  /// PARAMETERS:
  ///     weight - 输入的字体权重
  ///
  /// RETURNS:
  ///     FontWeight - 映射后的字体权重
  FontWeight mapFontWeight(FontWeight? weight) {
    if (weight == null) {
      return FontWeight.normal;
    }

    // Map to available SourceHanSansCN weights
    switch (weight) {
      case FontWeight.w100:
      case FontWeight.w200:
      case FontWeight.w300:
        return FontWeight.w300; // Light
      case FontWeight.w400:
        return FontWeight.w400; // Regular
      case FontWeight.w500:
        return FontWeight.w500; // Medium
      case FontWeight.w600:
      case FontWeight.w700:
        return FontWeight.w700; // Bold
      case FontWeight.w800:
      case FontWeight.w900:
        return FontWeight.w700; // Bold (fallback for heavier weights)
      default:
        return FontWeight.w400;
    }
  }

  /// createTextStyleWithMappedWeight
  ///
  /// DESCRIPTION:
  ///     创建带有映射字体权重的TextStyle，以更好地支持SourceHanSansCN
  ///
  /// PARAMETERS:
  ///     fontSize - 字体大小
  ///     fontWeight - 字体权重（将被映射到可用权重）
  ///     color - 文本颜色
  ///     height - 行高
  ///     letterSpacing - 字符间距
  ///     decoration - 文本装饰
  ///     textBaseline - 文本基线
  ///     leadingDistribution - 行高分布方式
  ///
  /// RETURNS:
  ///     TextStyle - 配置好的文本样式，使用映射后的字体权重
  TextStyle createTextStyleWithMappedWeight({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
    TextDecoration? decoration,
    TextBaseline? textBaseline,
    TextLeadingDistribution? leadingDistribution,
  }) {
    return createTextStyle(
      fontSize: fontSize,
      fontWeight: mapFontWeight(fontWeight),
      color: color,
      height: height,
      letterSpacing: letterSpacing,
      decoration: decoration,
      textBaseline: textBaseline,
      leadingDistribution: leadingDistribution,
    );
  }
}
