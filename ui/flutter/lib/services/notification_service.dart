/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      notification_service.dart
///
/// DESCRIPTION :    Notification service for displaying UI notifications
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:async';
import 'package:flutter/material.dart';

import '../utils/constants.dart';

import '../services/log_service.dart';
import '../utils/api_exception.dart';
import '../core/dependency_injection.dart';

import '../utils/design_system.dart';

/// NotificationService
///
/// PURPOSE:
///     Manages UI notifications and provides a centralized notification system
///     for displaying status updates, errors, warnings, and informational messages
///
/// FEATURES:
///     - Stream-based notification delivery
///     - Multiple notification types (status, error, info, success, warning)
///     - Automatic error message localization
///     - Integrated logging for all notifications
///     - Color and icon management for different notification types
///
/// USAGE:
///     final notificationService = serviceLocator<NotificationService>();
///     notificationService.showSuccessNotification('Operation completed');
///     notificationService.showErrorNotification('Error occurred', code: 500);
class NotificationService {
  final StreamController<NotificationInfo> _notificationController = StreamController<NotificationInfo>.broadcast();
  final _NotificationServiceHelper _helper = _NotificationServiceHelper();

  /// notifications
  ///
  /// DESCRIPTION:
  ///     Stream of notification events for UI consumption
  ///
  /// RETURNS:
  ///     Stream<NotificationInfo> - Stream of notification events
  Stream<NotificationInfo> get notifications => _notificationController.stream;

  /// showStatusNotification
  ///
  /// DESCRIPTION:
  ///     Displays a status notification with connection status information
  ///
  /// PARAMETERS:
  ///     status - ConnectionStatus: The current connection status
  ///     message - String: The notification message to display
  void showStatusNotification(ConnectionStatus status, String message) {
    _helper.logStatusNotification(status, message);
    _addNotification(NotificationInfo(
      type: NotificationType.status,
      message: message,
      status: status,
    ));
  }

  /// showErrorNotification
  ///
  /// DESCRIPTION:
  ///     Displays an error notification with optional error code and type
  ///
  /// PARAMETERS:
  ///     message - String: The error message to display
  ///     code - int?: Optional error code for localization
  ///     type - String?: Optional error type for categorization
  void showErrorNotification(String message, {int? code, String? type}) {
    final localizedMessage = _helper.localizeErrorMessage(message, code, type);
    _helper.logErrorNotification(localizedMessage, code, type);
    _addNotification(NotificationInfo(
      type: NotificationType.error,
      message: localizedMessage,
      errorCode: code,
      errorType: type,
    ));
  }

  /// showInfoNotification
  ///
  /// DESCRIPTION:
  ///     Displays an informational notification
  ///
  /// PARAMETERS:
  ///     message - String: The information message to display
  void showInfoNotification(String message) {
    _helper.logInfoNotification(message);
    _addNotification(NotificationInfo(
      type: NotificationType.info,
      message: message,
    ));
  }

  /// showSuccessNotification
  ///
  /// DESCRIPTION:
  ///     Displays a success notification
  ///
  /// PARAMETERS:
  ///     message - String: The success message to display
  void showSuccessNotification(String message) {
    _helper.logSuccessNotification(message);
    _addNotification(NotificationInfo(
      type: NotificationType.success,
      message: message,
    ));
  }

  /// showWarningNotification
  ///
  /// DESCRIPTION:
  ///     Displays a warning notification
  ///
  /// PARAMETERS:
  ///     message - String: The warning message to display
  void showWarningNotification(String message) {
    _helper.logWarningNotification(message);
    _addNotification(NotificationInfo(
      type: NotificationType.warning,
      message: message,
    ));
  }

  /// _addNotification
  ///
  /// DESCRIPTION:
  ///     Internal method to add notification to stream
  ///
  /// PARAMETERS:
  ///     notification - NotificationInfo: The notification to add
  void _addNotification(NotificationInfo notification) {
    _notificationController.add(notification);
    _helper.logNotificationAdded(notification.type);
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     Releases notification service resources and closes streams
  void dispose() {
    _helper.logDispose();
    try {
      _notificationController.close();
    } catch (e) {
      // Ignore errors when closing stream
    }
  }
}

/// _NotificationServiceHelper
///
/// PURPOSE:
///     Helper class for NotificationService to handle logging and message localization
///
/// FEATURES:
///     - Safe service access with error handling
///     - Unified logging for all notification types
///     - Error message localization support
///
/// USAGE:
///     Internal helper class used by NotificationService
class _NotificationServiceHelper {
  LogService? _logService;

  /// getLogService
  ///
  /// DESCRIPTION:
  ///     Safely retrieves LogService instance
  ///
  /// RETURNS:
  ///     LogService - The log service instance
  LogService get logService {
    try {
      _logService ??= serviceLocator<LogService>();
      return _logService!;
    } catch (e) {
      _logService ??= LogService();
      return _logService!;
    }
  }

  /// localizeErrorMessage
  ///
  /// DESCRIPTION:
  ///     Attempts to localize error message using ApiException
  ///
  /// PARAMETERS:
  ///     message - String: Original error message
  ///     code - int?: Error code for localization
  ///     type - String?: Error type for categorization
  ///
  /// RETURNS:
  ///     String - Localized error message or original if localization fails
  String localizeErrorMessage(String message, int? code, String? type) {
    if (code == null) return message;

    try {
      final context = serviceLocator<GlobalKey<NavigatorState>>().currentContext;
      if (context != null) {
        final apiException = ApiException(message, code, type ?? 'unknown');
        final localizedMessage = apiException.getUserFriendlyMessage(context);
        logService.debug('Notification', 'Localized error message: $localizedMessage');
        return localizedMessage;
      }
    } catch (e) {
      logService.warning('Notification', 'Failed to localize error message, using original: $e');
    }
    return message;
  }

  /// logStatusNotification
  ///
  /// DESCRIPTION:
  ///     Logs status notification creation
  ///
  /// PARAMETERS:
  ///     status - ConnectionStatus: The connection status
  ///     message - String: The notification message
  void logStatusNotification(ConnectionStatus status, String message) {
    try {
      switch (status) {
        case ConnectionStatus.connected:
          logService.info('Notification', 'Creating status notification: Connected - $message');
          break;
        case ConnectionStatus.connecting:
          logService.info('Notification', 'Creating status notification: Connecting - $message');
          break;
        case ConnectionStatus.disconnecting:
          logService.info('Notification', 'Creating status notification: Disconnecting - $message');
          break;
        case ConnectionStatus.disconnected:
          logService.info('Notification', 'Creating status notification: Disconnected - $message');
          break;
        case ConnectionStatus.error:
          logService.error('Notification', 'Creating status notification: Error - $message');
          break;
      }
    } catch (e) {
      logService.error('Notification', 'Error logging status notification: $e');
    }
  }

  /// logErrorNotification
  ///
  /// DESCRIPTION:
  ///     Logs error notification creation
  ///
  /// PARAMETERS:
  ///     message - String: The error message
  ///     code - int?: Optional error code
  ///     type - String?: Optional error type
  void logErrorNotification(String message, int? code, String? type) {
    try {
      if (code != null && type != null) {
        logService.error('Notification', 'Creating error notification [$code] $type: $message');
      } else {
        logService.error('Notification', 'Creating error notification: $message');
      }
    } catch (e) {
      logService.error('Notification', 'Error logging error notification: $e');
    }
  }

  /// logInfoNotification
  ///
  /// DESCRIPTION:
  ///     Logs info notification creation
  ///
  /// PARAMETERS:
  ///     message - String: The info message
  void logInfoNotification(String message) {
    try {
      logService.debug('Notification', 'Creating info notification: $message');
    } catch (e) {
      logService.error('Notification', 'Error logging info notification: $e');
    }
  }

  /// logSuccessNotification
  ///
  /// DESCRIPTION:
  ///     Logs success notification creation
  ///
  /// PARAMETERS:
  ///     message - String: The success message
  void logSuccessNotification(String message) {
    try {
      logService.debug('Notification', 'Creating success notification: $message');
    } catch (e) {
      logService.error('Notification', 'Error logging success notification: $e');
    }
  }

  /// logWarningNotification
  ///
  /// DESCRIPTION:
  ///     Logs warning notification creation
  ///
  /// PARAMETERS:
  ///     message - String: The warning message
  void logWarningNotification(String message) {
    try {
      logService.warning('Notification', 'Creating warning notification: $message');
    } catch (e) {
      logService.error('Notification', 'Error logging warning notification: $e');
    }
  }

  /// logNotificationAdded
  ///
  /// DESCRIPTION:
  ///     Logs when notification is added to stream
  ///
  /// PARAMETERS:
  ///     type - NotificationType: The type of notification added
  void logNotificationAdded(NotificationType type) {
    try {
      logService.debug('Notification', '${type.name} notification added to stream');
    } catch (e) {
      // Ignore logging errors for this debug message
    }
  }

  /// logDispose
  ///
  /// DESCRIPTION:
  ///     Logs service disposal
  void logDispose() {
    try {
      logService.debug('Notification', 'Releasing notification service resources');
    } catch (e) {
      // Ignore logging errors during disposal
    }
  }
}

/// NotificationType
///
/// DESCRIPTION:
///     Enumeration of different notification types supported by the system
///
/// VALUES:
///     status - Connection status notifications
///     error - Error notifications
///     info - Informational notifications
///     success - Success notifications
///     warning - Warning notifications
enum NotificationType {
  status,
  error,
  info,
  success,
  warning,
}

/// NotificationInfo
///
/// PURPOSE:
///     Data class representing a notification with all its properties
///
/// FEATURES:
///     - Multiple notification types support
///     - Connection status integration
///     - Error code and type tracking
///     - Automatic timestamp generation
///     - Color and icon management
///
/// USAGE:
///     final notification = NotificationInfo(
///       type: NotificationType.success,
///       message: 'Operation completed',
///     );
class NotificationInfo {
  final NotificationType type;
  final String message;
  final ConnectionStatus? status;
  final int? errorCode;
  final String? errorType;
  final DateTime timestamp;

  /// NotificationInfo constructor
  ///
  /// DESCRIPTION:
  ///     Creates a new notification info instance
  ///
  /// PARAMETERS:
  ///     type - NotificationType: The type of notification
  ///     message - String: The notification message
  ///     status - ConnectionStatus?: Optional connection status for status notifications
  ///     errorCode - int?: Optional error code for error notifications
  ///     errorType - String?: Optional error type for error notifications
  ///     timestamp - DateTime?: Optional timestamp, defaults to current time
  NotificationInfo({
    required this.type,
    required this.message,
    this.status,
    this.errorCode,
    this.errorType,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  /// getColor
  ///
  /// DESCRIPTION:
  ///     Returns the appropriate color for this notification type
  ///
  /// RETURNS:
  ///     Color - The color to use for displaying this notification
  Color getColor() {
    switch (type) {
      case NotificationType.status:
        if (status == null) return Colors.blue;
        switch (status!) {
          case ConnectionStatus.connected:
            return AppColors.connected;
          case ConnectionStatus.connecting:
            return AppColors.connecting;
          case ConnectionStatus.disconnecting:
            return AppColors.connecting;
          case ConnectionStatus.disconnected:
            return AppColors.disconnected;
          case ConnectionStatus.error:
            return AppColors.error;
        }
      case NotificationType.error:
        return AppColors.error;
      case NotificationType.info:
        return AppColors.primary;
      case NotificationType.success:
        return AppColors.connected;
      case NotificationType.warning:
        return AppColors.connecting;
    }
  }

  /// getIcon
  ///
  /// DESCRIPTION:
  ///     Returns the appropriate icon for this notification type
  ///
  /// RETURNS:
  ///     IconData - The icon to use for displaying this notification
  IconData getIcon() {
    switch (type) {
      case NotificationType.status:
        if (status == null) return Icons.info;
        switch (status!) {
          case ConnectionStatus.connected:
            return Icons.check_circle;
          case ConnectionStatus.connecting:
            return Icons.sync;
          case ConnectionStatus.disconnecting:
            return Icons.sync;
          case ConnectionStatus.disconnected:
            return Icons.cancel;
          case ConnectionStatus.error:
            return Icons.error;
        }
      case NotificationType.error:
        return Icons.error;
      case NotificationType.info:
        return Icons.info;
      case NotificationType.success:
        return Icons.check_circle;
      case NotificationType.warning:
        return Icons.warning;
    }
  }
}


