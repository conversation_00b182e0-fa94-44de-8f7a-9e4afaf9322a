/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      single_instance_service.dart
///
/// DESCRIPTION :    单例检查服务，使用套接字确保应用程序只有一个实例在运行，
///                  支持实例间通信和窗口激活功能
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:io';
import 'dart:async';

import 'package:window_manager/window_manager.dart';

import 'log_service.dart';

/// SingleInstanceService
///
/// PURPOSE:
///     单例检查服务，使用套接字确保应用程序只有一个实例在运行
///
/// FEATURES:
///     - 单例检查：通过端口绑定检测是否已有实例运行
///     - 实例通信：支持后续实例与第一个实例的通信
///     - 窗口激活：当启动新实例时自动激活已运行实例的窗口
///     - 跨平台支持：支持Windows、macOS和Linux桌面平台
///     - 移动平台兼容：移动平台自动跳过单例检查
///     - 资源管理：提供完善的资源清理机制
///
/// USAGE:
///     在应用启动时调用init()方法检查单例状态，
///     在应用退出时调用dispose()方法清理资源
class SingleInstanceService {
  /// 日志服务实例，用于记录单例检查相关事件
  final LogService _logService;

  /// 服务器套接字，用于监听后续实例的连接
  ServerSocket? _serverSocket;

  /// 监听端口，使用一个不太可能被占用的端口
  final int _port = 56545;

  /// 是否为第一个实例的标志
  bool _isFirstInstance = false;

  /// SingleInstanceService构造函数
  ///
  /// DESCRIPTION:
  ///     创建单例检查服务实例
  ///
  /// PARAMETERS:
  ///     logService - 日志服务实例，用于记录事件
  SingleInstanceService({required LogService logService}) : _logService = logService;

  /// init
  ///
  /// DESCRIPTION:
  ///     初始化单例检查服务，通过端口绑定检测是否已有实例运行
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - true表示这是第一个实例可以继续运行，false表示已有实例在运行应该退出
  ///
  /// THROWS:
  ///     Exception - 网络操作失败时记录日志但不抛出异常
  Future<bool> init() async {
    if (!Platform.isWindows && !Platform.isLinux && !Platform.isMacOS) {
      // 移动平台不需要单例检查
      _logService.info('SingleInstance', 'Mobile platform does not require single instance check');
      _isFirstInstance = true;
      return true;
    }

    try {
      // 尝试在指定端口创建服务器套接字
      _serverSocket = await ServerSocket.bind(InternetAddress.loopbackIPv4, _port);

      // 如果成功创建，则表示这是第一个实例
      _isFirstInstance = true;

      // 监听连接，用于处理后续实例的通信
      _serverSocket!.listen(_handleConnection);

      return true;
    } catch (e) {
      // 如果端口已被占用，则表示已有实例在运行
      _logService.info('SingleInstance', 'Unable to create server socket, another instance may be running: $e');
      _isFirstInstance = false;

      // 尝试连接到第一个实例并发送激活命令
      await _notifyFirstInstance();

      return false;
    }
  }

  /// _handleConnection
  ///
  /// DESCRIPTION:
  ///     处理来自后续实例的连接，接收消息并激活窗口
  ///
  /// PARAMETERS:
  ///     socket - 来自后续实例的套接字连接
  ///
  /// RETURNS:
  ///     void
  void _handleConnection(Socket socket) {
    _logService.info('SingleInstance', 'Received connection from subsequent instance');

    // 读取数据（可以用于传递参数）
    socket.listen((data) {
      final message = String.fromCharCodes(data);
      _logService.info('SingleInstance', 'Received message: $message');

      // 激活窗口
      _activateWindow();
    });
  }

  /// _notifyFirstInstance
  ///
  /// DESCRIPTION:
  ///     通知第一个实例激活窗口，发送激活命令到第一个实例
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 连接失败时记录日志但不抛出异常
  Future<void> _notifyFirstInstance() async {
    try {
      _logService.info('SingleInstance', 'Attempting to connect to first instance');
      final socket = await Socket.connect(InternetAddress.loopbackIPv4, _port);

      // 发送激活命令
      socket.write('activate');

      // 等待数据发送完成
      await socket.flush();

      // 关闭连接
      socket.destroy();

      _logService.info('SingleInstance', 'Notified first instance to activate window');
    } catch (e) {
      _logService.error('SingleInstance', 'Unable to connect to first instance: $e');
    }
  }

  /// _activateWindow
  ///
  /// DESCRIPTION:
  ///     激活窗口，使用window_manager显示并聚焦窗口
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 窗口操作失败时记录日志但不抛出异常
  Future<void> _activateWindow() async {
    _logService.info('SingleInstance', 'Activating window');

    try {
      // 使用window_manager激活窗口
      await windowManager.show();
      await windowManager.focus();

      _logService.info('SingleInstance', 'Window activated');
    } catch (e) {
      _logService.error('SingleInstance', 'Failed to activate window: $e');
    }
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     关闭服务器套接字，清理资源
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> dispose() async {
    if (_serverSocket != null) {
      _logService.info('SingleInstance', 'Closing server socket');
      await _serverSocket!.close();
      _serverSocket = null;
    }
  }

  /// isFirstInstance
  ///
  /// DESCRIPTION:
  ///     获取是否为第一个实例的状态
  ///
  /// RETURNS:
  ///     bool - 是第一个实例返回true，否则返回false
  bool get isFirstInstance => _isFirstInstance;
}
