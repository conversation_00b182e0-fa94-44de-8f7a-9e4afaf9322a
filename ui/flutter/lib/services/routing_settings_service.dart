/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      routing_settings_service.dart
///
/// DESCRIPTION :    路由设置持久化服务，负责管理路由配置的本地存储、
///                  后端同步和设置的加载与保存操作
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/routing_settings.dart';
import '../core/dependency_injection.dart';
import '../services/log_service.dart';
import '../services/api_service.dart';

/// RoutingSettingsService
///
/// PURPOSE:
///     路由设置持久化服务，负责管理路由配置的本地存储和后端同步
///
/// FEATURES:
///     - 本地存储：使用SharedPreferences保存路由设置到本地
///     - 后端同步：支持将设置同步到后端服务器
///     - 设置加载：从本地或后端加载路由配置
///     - 默认设置：提供默认路由配置作为回退
///     - 数据一致性：确保本地和后端设置的一致性
///     - 错误处理：完善的错误处理和日志记录
///
/// USAGE:
///     通过依赖注入获取实例，调用相关方法管理路由设置
class RoutingSettingsService {
  /// SharedPreferences中存储路由设置的键名
  static const String _routingSettingsKey = 'routing_settings';

  /// 日志服务实例，用于记录路由设置相关事件
  late final LogService _logService;

  /// API服务实例，用于与后端同步路由设置
  late final ApiService _apiService;

  /// RoutingSettingsService构造函数
  ///
  /// DESCRIPTION:
  ///     创建路由设置服务实例，初始化依赖的服务
  ///
  /// PARAMETERS:
  ///     无
  RoutingSettingsService() {
    _logService = serviceLocator<LogService>();
    _apiService = serviceLocator<ApiService>();
  }

  /// saveRoutingSettings
  ///
  /// DESCRIPTION:
  ///     保存路由设置到本地存储，使用SharedPreferences持久化
  ///
  /// PARAMETERS:
  ///     settings - 要保存的路由设置模型
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 保存过程中发生错误时抛出异常
  Future<void> saveRoutingSettings(RoutingSettingsModel settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = json.encode(settings.toJson());
      await prefs.setString(_routingSettingsKey, settingsJson);

      _logService.info('RoutingSettingsService',
        'Routing settings saved to local storage: mode=${settings.mode}, customRoutes=${settings.customRoutes}');
    } catch (e) {
      _logService.error('RoutingSettingsService', 'Failed to save routing settings', e);
      rethrow;
    }
  }

  /// loadRoutingSettings
  ///
  /// DESCRIPTION:
  ///     从本地存储加载路由设置，解析JSON数据为模型对象
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<RoutingSettingsModel?> - 加载成功返回设置模型，失败或无数据返回null
  ///
  /// THROWS:
  ///     Exception - 加载过程中发生错误时记录日志并返回null
  Future<RoutingSettingsModel?> loadRoutingSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_routingSettingsKey);

      if (settingsJson != null && settingsJson.isNotEmpty) {
        final settingsData = json.decode(settingsJson) as Map<String, dynamic>;
        final settings = RoutingSettingsModel();
        settings.fromJson(settingsData);

        _logService.info('RoutingSettingsService',
          'Loaded routing settings from local storage: mode=${settings.mode}, customRoutes=${settings.customRoutes}');
        return settings;
      }

      _logService.info('RoutingSettingsService', 'No saved routing settings found locally');
      return null;
    } catch (e) {
      _logService.error('RoutingSettingsService', 'Failed to load routing settings', e);
      return null;
    }
  }

  /// clearRoutingSettings
  ///
  /// DESCRIPTION:
  ///     清除本地保存的路由设置，从SharedPreferences中删除数据
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 清除过程中发生错误时记录日志但不抛出异常
  Future<void> clearRoutingSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_routingSettingsKey);
      _logService.info('RoutingSettingsService', 'Local routing settings cleared');
    } catch (e) {
      _logService.error('RoutingSettingsService', 'Failed to clear routing settings', e);
    }
  }

  /// syncRoutingSettingsToBackend
  ///
  /// DESCRIPTION:
  ///     同步路由设置到后端，在登录后调用，将本地保存的设置同步到后端
  ///
  /// PARAMETERS:
  ///     settings - 要同步的路由设置模型
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 同步过程中发生错误时抛出异常
  Future<void> syncRoutingSettingsToBackend(RoutingSettingsModel settings) async {
    try {
      _logService.info('RoutingSettingsService',
        'Syncing routing settings to backend: mode=${settings.mode}, customRoutes=${settings.customRoutes}');

      await _apiService.setRoutingSettings(settings);

      _logService.info('RoutingSettingsService', 'Routing settings synced to backend successfully');
    } catch (e) {
      _logService.error('RoutingSettingsService', 'Failed to sync routing settings to backend', e);
      rethrow;
    }
  }

  /// fetchAndSaveRoutingSettings
  ///
  /// DESCRIPTION:
  ///     从后端获取路由设置并保存到本地，实现设置的下载和本地化
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<RoutingSettingsModel?> - 获取成功返回设置模型，失败返回null
  ///
  /// THROWS:
  ///     Exception - 获取或保存过程中发生错误时记录日志并返回null
  Future<RoutingSettingsModel?> fetchAndSaveRoutingSettings() async {
    try {
      _logService.info('RoutingSettingsService', 'Fetching routing settings from backend');

      final settingsData = await _apiService.getRoutingSettings();
      final settings = RoutingSettingsModel();
      settings.fromJson(settingsData);

      // 保存到本地
      await saveRoutingSettings(settings);

      _logService.info('RoutingSettingsService',
        'Successfully fetched and saved routing settings from backend: mode=${settings.mode}, customRoutes=${settings.customRoutes}');

      return settings;
    } catch (e) {
      _logService.error('RoutingSettingsService', 'Failed to fetch routing settings from backend', e);
      return null;
    }
  }

  /// getRoutingSettings
  ///
  /// DESCRIPTION:
  ///     获取路由设置，优先从本地加载，如果没有则使用默认设置
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<RoutingSettingsModel> - 路由设置模型，保证不为null
  Future<RoutingSettingsModel> getRoutingSettings() async {
    // 先尝试从本地加载
    final localSettings = await loadRoutingSettings();
    if (localSettings != null) {
      return localSettings;
    }

    // 如果本地没有，返回默认设置
    _logService.info('RoutingSettingsService', 'No local routing settings found, using default settings');
    return RoutingSettingsModel();
  }

  /// updateRoutingSettings
  ///
  /// DESCRIPTION:
  ///     更新路由设置，同时保存到本地和后端，确保数据一致性
  ///
  /// PARAMETERS:
  ///     settings - 要更新的路由设置模型
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 更新过程中发生错误时抛出异常
  Future<void> updateRoutingSettings(RoutingSettingsModel settings) async {
    try {
      // 先同步到后端
      await syncRoutingSettingsToBackend(settings);

      // 后端成功后再保存到本地
      await saveRoutingSettings(settings);

      _logService.info('RoutingSettingsService', 'Routing settings updated successfully');
    } catch (e) {
      _logService.error('RoutingSettingsService', 'Failed to update routing settings', e);
      rethrow;
    }
  }
}
