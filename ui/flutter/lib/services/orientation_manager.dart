/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// ******************************************************************************
/// FILE NAME :      orientation_manager.dart
///
/// DESCRIPTION :    Device orientation management for iOS platform adaptation
///
/// AUTHOR :         wei
///
/// HISTORY :        27/06/2025 create

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'platform/platform_service_factory.dart';

/// OrientationManager
///
/// PURPOSE:
///     Manages device orientation configuration for different platforms.
///     Provides iOS-specific orientation support while maintaining
///     desktop platform compatibility.
///
/// FEATURES:
///     - iOS: Portrait and landscape orientation support
///     - macOS/Windows: Fixed orientation (desktop behavior)
///     - Dynamic orientation changes based on device type
///     - Orientation lock/unlock functionality
///
/// USAGE:
///     await OrientationManager.initialize();
///     await OrientationManager.setPreferredOrientations([...]);
class OrientationManager {
  /// Whether the manager has been initialized
  static bool _isInitialized = false;

  /// Current supported orientations
  static List<DeviceOrientation> _currentOrientations = [];

  /// Initialize orientation manager
  ///
  /// DESCRIPTION:
  ///     Initializes orientation management based on platform capabilities.
  ///     Sets up appropriate orientation constraints for each platform.
  ///     iOS App on macOS is treated as desktop (no orientation changes).
  ///
  /// RETURNS:
  ///     Future<void> - Completes when initialization is finished
  static Future<void> initialize() async {
    if (_isInitialized) return;

    // Check if this is native iOS (not iOS App on macOS)
    final isNativeiOS = Platform.isIOS && !(await PlatformServiceFactory.isIOSAppOnMacOS());

    if (isNativeiOS) {
      await _initializeIOSOrientations();
    } else {
      await _initializeDesktopOrientations();
    }

    _isInitialized = true;
  }

  /// Initialize iOS orientation support
  static Future<void> _initializeIOSOrientations() async {
    try {
      // Set default iOS orientations (portrait + landscape)
      final orientations = getSupportedOrientations();
      await SystemChrome.setPreferredOrientations(orientations);
      _currentOrientations = orientations;
    } catch (e) {
      // Fallback to portrait only if setting orientations fails
      await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
      _currentOrientations = [DeviceOrientation.portraitUp];
    }
  }

  /// Initialize desktop orientation (fixed)
  static Future<void> _initializeDesktopOrientations() async {
    try {
      // Desktop platforms use fixed portrait orientation
      await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
      _currentOrientations = [DeviceOrientation.portraitUp];
    } catch (e) {
      // Desktop platforms may not support orientation changes
      _currentOrientations = [DeviceOrientation.portraitUp];
    }
  }

  /// Get supported orientations for current platform
  ///
  /// DESCRIPTION:
  ///     Returns the list of supported device orientations based on platform.
  ///
  /// RETURNS:
  ///     List<DeviceOrientation> - Supported orientations
  static List<DeviceOrientation> getSupportedOrientations() {
    // Use synchronous check for better performance
    final shouldUseDesktop = PlatformServiceFactory.shouldUseDesktopLayoutSync();

    if ((Platform.isIOS && !shouldUseDesktop) || Platform.isAndroid) {
      return [
        DeviceOrientation.portraitUp,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ];
    } else {
      // Desktop platforms use fixed orientation
      return [DeviceOrientation.portraitUp];
    }
  }

  /// Set preferred orientations
  ///
  /// DESCRIPTION:
  ///     Sets the preferred device orientations. Only effective on iOS.
  ///
  /// PARAMETERS:
  ///     orientations - List of preferred orientations
  ///
  /// RETURNS:
  ///     Future<void> - Completes when orientations are set
  static Future<void> setPreferredOrientations(List<DeviceOrientation> orientations) async {
    // Use synchronous check for better performance
    final shouldUseDesktop = PlatformServiceFactory.shouldUseDesktopLayoutSync();

    if ((!Platform.isIOS && !Platform.isAndroid) || shouldUseDesktop) {
      // Desktop platforms and iOS App on macOS don't support orientation changes
      return;
    }

    try {
      await SystemChrome.setPreferredOrientations(orientations);
      _currentOrientations = orientations;
    } catch (e) {
      // Fallback to current orientations if setting fails
    }
  }

  /// Lock orientation to portrait
  ///
  /// DESCRIPTION:
  ///     Locks device orientation to portrait mode. Only effective on iOS.
  ///
  /// RETURNS:
  ///     Future<void> - Completes when orientation is locked
  static Future<void> lockPortrait() async {
    await setPreferredOrientations([DeviceOrientation.portraitUp]);
  }

  /// Lock orientation to landscape
  ///
  /// DESCRIPTION:
  ///     Locks device orientation to landscape mode. Only effective on iOS.
  ///
  /// RETURNS:
  ///     Future<void> - Completes when orientation is locked
  static Future<void> lockLandscape() async {
    await setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  /// Unlock orientation (allow all supported orientations)
  ///
  /// DESCRIPTION:
  ///     Unlocks device orientation to allow all supported orientations.
  ///
  /// RETURNS:
  ///     Future<void> - Completes when orientation is unlocked
  static Future<void> unlockOrientation() async {
    await setPreferredOrientations(getSupportedOrientations());
  }

  /// Check if landscape orientation is supported
  ///
  /// DESCRIPTION:
  ///     Checks whether landscape orientation is supported on current platform.
  ///     iOS App on macOS is treated as desktop (no landscape support).
  ///
  /// RETURNS:
  ///     bool - True if landscape is supported
  static bool get supportsLandscape {
    // Use synchronous check for better performance
    final shouldUseDesktop = PlatformServiceFactory.shouldUseDesktopLayoutSync();
    return (Platform.isIOS && !shouldUseDesktop) || Platform.isAndroid;
  }

  /// Check if current orientation is landscape
  ///
  /// DESCRIPTION:
  ///     Checks if the current device orientation is landscape.
  ///
  /// PARAMETERS:
  ///     context - Build context for MediaQuery
  ///
  /// RETURNS:
  ///     bool - True if current orientation is landscape
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// Check if current orientation is portrait
  ///
  /// DESCRIPTION:
  ///     Checks if the current device orientation is portrait.
  ///
  /// PARAMETERS:
  ///     context - Build context for MediaQuery
  ///
  /// RETURNS:
  ///     bool - True if current orientation is portrait
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  /// Get current orientations
  ///
  /// DESCRIPTION:
  ///     Returns the currently set preferred orientations.
  ///
  /// RETURNS:
  ///     List<DeviceOrientation> - Current preferred orientations
  static List<DeviceOrientation> get currentOrientations {
    return List.from(_currentOrientations);
  }

  /// Check if orientation manager is initialized
  ///
  /// DESCRIPTION:
  ///     Checks whether the orientation manager has been initialized.
  ///
  /// RETURNS:
  ///     bool - True if initialized
  static bool get isInitialized {
    return _isInitialized;
  }

  /// Dispose orientation manager
  ///
  /// DESCRIPTION:
  ///     Cleans up orientation manager resources and resets to default.
  ///
  /// RETURNS:
  ///     Future<void> - Completes when disposal is finished
  static Future<void> dispose() async {
    try {
      // Reset to default orientation
      await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    } catch (e) {
      // Ignore errors during disposal, but still reset state
    } finally {
      // Always reset state regardless of errors
      _currentOrientations = [];
      _isInitialized = false;
    }
  }
}

/// OrientationBuilder
///
/// PURPOSE:
///     A widget that rebuilds when device orientation changes.
///     Provides orientation-aware layout building.
///
/// USAGE:
///     OrientationBuilder(
///       builder: (context, orientation) {
///         if (orientation == Orientation.landscape) {
///           return LandscapeLayout();
///         } else {
///           return PortraitLayout();
///         }
///       },
///     )
class AdaptiveOrientationBuilder extends StatelessWidget {
  /// Builder function that receives orientation
  final Widget Function(BuildContext context, Orientation orientation) builder;

  /// AdaptiveOrientationBuilder constructor
  ///
  /// DESCRIPTION:
  ///     Creates an orientation-aware widget builder.
  ///
  /// PARAMETERS:
  ///     builder - Function that builds widget based on orientation
  const AdaptiveOrientationBuilder({
    Key? key,
    required this.builder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (Platform.isIOS) {
      // Use OrientationBuilder on iOS for dynamic orientation changes
      return OrientationBuilder(
        builder: (context, orientation) => builder(context, orientation),
      );
    } else {
      // Desktop platforms always use portrait orientation
      return builder(context, Orientation.portrait);
    }
  }
}
