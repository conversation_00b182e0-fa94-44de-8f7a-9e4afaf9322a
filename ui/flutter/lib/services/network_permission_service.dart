/// FILE: network_permission_service.dart
///
/// DESCRIPTION:
///     Simple network permission detection service using HTTP requests.
///     Detects network access by making actual HTTP requests.
///
/// AUTHOR: wei
/// HISTORY: 01/07/2025 create simple network permission service

import 'dart:io';
import 'package:http/http.dart' as http;
import '../core/dependency_injection.dart';
import 'notification_service.dart';

/// NetworkPermissionService
///
/// PURPOSE:
///     Simple service for network permission detection using HTTP requests.
///     Makes actual network requests to verify network access permissions.
///
/// FEATURES:
///     - Direct HTTP request for permission detection
///     - Automatic iOS permission prompt triggering
///     - Platform-specific behavior handling
///     - Simple boolean result
///
/// USAGE:
///     Used during app startup to determine if network permissions are available.
///     On iOS, first network request will trigger system permission dialog.
class NetworkPermissionService {
  
  // ============================================================================
  // SINGLETON PATTERN
  // ============================================================================
  
  static NetworkPermissionService? _instance;
  static NetworkPermissionService get instance {
    _instance ??= NetworkPermissionService._internal();
    return _instance!;
  }
  
  NetworkPermissionService._internal();
  
  // ============================================================================
  // NETWORK PERMISSION DETECTION
  // ============================================================================
  
  /// checkNetworkPermission
  ///
  /// DESCRIPTION:
  ///     Checks network permission by making an actual HTTP request.
  ///     On iOS, this will trigger the system permission dialog if not granted.
  ///
  ///     NOTE: Network connectivity is now handled by LookupService during
  ///     client domain validation, so this method is simplified.
  ///
  /// RETURNS:
  ///     Future<bool> - true if network access is available, false otherwise
  Future<bool> checkNetworkPermission() async {
    // Network connectivity is now validated through LookupService
    // when user enters client domain, so we can simplify this check
    return true;

    // Original network test code commented out since LookupService handles connectivity
    /*
    try {
      // Use lookup service URL for network testing - this is the actual service the app will use
      final response = await http
          .get(Uri.parse('https://uctest.unisase.cn:9000'))
          .timeout(const Duration(seconds: 3));
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
    */
  }

  /// checkNetworkPermissionWithNotification
  ///
  /// DESCRIPTION:
  ///     Checks network permission and shows notification if failed.
  ///     Uses existing notification service to display user-friendly messages.
  ///
  /// RETURNS:
  ///     Future<bool> - true if network access is available, false otherwise
  Future<bool> checkNetworkPermissionWithNotification() async {
    final hasPermission = await checkNetworkPermission();

    if (!hasPermission && isNetworkPermissionRequired()) {
      // 使用现有的通知服务显示网络权限错误
      final notificationService = serviceLocator<NotificationService>();
      notificationService.showWarningNotification(
        '网络权限未授权，请在系统设置中允许应用访问网络'
      );
    }

    return hasPermission;
  }
  
  /// isNetworkPermissionRequired
  ///
  /// DESCRIPTION:
  ///     Checks if network permission detection is required for current platform.
  ///     iOS and Android require explicit network permission checking.
  ///
  /// RETURNS:
  ///     bool - true if permission check is required
  bool isNetworkPermissionRequired() {
    return Platform.isIOS || Platform.isAndroid;
  }
  
  /// getNetworkPermissionMessage
  ///
  /// DESCRIPTION:
  ///     Gets user-friendly message for network permission status.
  ///
  /// PARAMETERS:
  ///     hasPermission - whether network permission is granted
  ///
  /// RETURNS:
  ///     String - user-friendly message
  String getNetworkPermissionMessage(bool hasPermission) {
    if (hasPermission) {
      return '网络权限已授权，应用可以正常使用';
    } else {
      return '网络权限未授权，请在系统设置中允许应用访问网络';
    }
  }
  
  /// getPlatformSpecificGuidance
  ///
  /// DESCRIPTION:
  ///     Gets platform-specific guidance for network permission setup.
  ///
  /// RETURNS:
  ///     String - platform-specific guidance text
  String getPlatformSpecificGuidance() {
    if (Platform.isIOS) {
      return '请在iOS设置中允许应用访问网络：\n'
             '设置 > Panabit Client > 允许网络访问';
    } else if (Platform.isAndroid) {
      return '请确保Android设备网络连接正常：\n'
             '检查WiFi/移动数据连接状态';
    } else {
      return '桌面平台通常默认拥有网络访问权限';
    }
  }
}
