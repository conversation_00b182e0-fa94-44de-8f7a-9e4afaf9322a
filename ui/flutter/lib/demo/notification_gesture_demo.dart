// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      notification_gesture_demo.dart
//  *
//  * DESCRIPTION :    通知手势功能演示页面，展示上滑关闭通知的功能
//  *
//  * AUTHOR :         wei
//  *
//  *******************************************************************************

import 'package:flutter/material.dart';
import '../widgets/notification_manager.dart';
import '../services/notification_service.dart';
import '../utils/constants.dart';

/// NotificationGestureDemo
///
/// PURPOSE:
///     演示通知组件的上滑手势关闭功能
///
/// FEATURES:
///     - 展示不同类型的通知
///     - 演示上滑手势关闭功能
///     - 提供手势使用说明
///     - 支持重新显示通知进行测试
class NotificationGestureDemo extends StatefulWidget {
  const NotificationGestureDemo({Key? key}) : super(key: key);

  @override
  State<NotificationGestureDemo> createState() => _NotificationGestureDemoState();
}

class _NotificationGestureDemoState extends State<NotificationGestureDemo> {
  final List<NotificationInfo> _demoNotifications = [];
  int _notificationCounter = 0;

  @override
  void initState() {
    super.initState();
    _addDemoNotification();
  }

  /// 添加演示通知
  void _addDemoNotification() {
    final notifications = [
      NotificationInfo(
        type: NotificationType.info,
        message: '这是一条信息通知 - 试试向上滑动关闭！',
      ),
      NotificationInfo(
        type: NotificationType.success,
        message: '连接成功！您可以向上滑动或点击×关闭',
      ),
      NotificationInfo(
        type: NotificationType.warning,
        message: '网络连接不稳定 - 支持快速上滑关闭',
      ),
      NotificationInfo(
        type: NotificationType.error,
        message: '连接失败，请检查网络设置',
      ),
      NotificationInfo(
        type: NotificationType.status,
        message: '正在连接到服务器...',
        status: ConnectionStatus.connecting,
      ),
    ];

    setState(() {
      _demoNotifications.add(notifications[_notificationCounter % notifications.length]);
      _notificationCounter++;
    });
  }

  /// 移除通知
  void _removeNotification(int index) {
    setState(() {
      _demoNotifications.removeAt(index);
    });
  }

  /// 清除所有通知
  void _clearAllNotifications() {
    setState(() {
      _demoNotifications.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('通知手势演示'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // 说明区域
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue.shade600),
                    const SizedBox(width: 8),
                    Text(
                      '手势使用说明',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                const Text(
                  '• 向上滑动通知卡片可以关闭通知\n'
                  '• 快速向上滑动可以立即关闭\n'
                  '• 点击右侧 × 按钮也可以关闭\n'
                  '• 向下滑动不会触发关闭',
                  style: TextStyle(fontSize: 14, height: 1.5),
                ),
              ],
            ),
          ),

          // 控制按钮
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _addDemoNotification,
                    icon: const Icon(Icons.add),
                    label: const Text('添加通知'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _demoNotifications.isNotEmpty ? _clearAllNotifications : null,
                    icon: const Icon(Icons.clear_all),
                    label: const Text('清除所有'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // 通知显示区域
          Expanded(
            child: _demoNotifications.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.notifications_off,
                          size: 64,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          '暂无通知',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '点击"添加通知"按钮来测试手势功能',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _demoNotifications.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: NotificationWidget(
                          notification: _demoNotifications[index],
                          onDismiss: () => _removeNotification(index),
                        ),
                      );
                    },
                  ),
          ),

          // 底部提示
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.grey.shade100,
            child: Text(
              '💡 提示：尝试不同的滑动速度和方向来体验手势功能',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
