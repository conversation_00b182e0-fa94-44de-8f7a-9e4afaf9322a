/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// ******************************************************************************
/// FILE NAME :      android_gesture_wrapper.dart
///
/// DESCRIPTION :    Android手势包装器Widget，为子Widget提供Android平台特定的
///                  手势处理功能，包括侧边栏控制和应用最小化手势
///
/// AUTHOR :         wei
///
/// HISTORY :        17/07/2025 create

import 'dart:io';
import 'package:flutter/material.dart';
import '../services/android_gesture_service.dart';

/// AndroidGestureWrapper
///
/// PURPOSE:
///     为子Widget提供Android平台特定的手势处理功能
///
/// FEATURES:
///     - 手势检测：检测水平滑动手势
///     - 侧边栏控制：向右滑动打开，向左滑动关闭
///     - 应用最小化：当没有侧边栏时，滑动手势最小化应用
///     - 系统手势覆盖：禁用Android默认返回手势
///     - 平台兼容：只在Android平台生效
///
/// USAGE:
///     AndroidGestureWrapper(
///       scaffoldKey: _scaffoldKey,
///       onDrawerToggle: () => print('Drawer toggled'),
///       child: YourWidget(),
///     )
class AndroidGestureWrapper extends StatefulWidget {
  /// 子Widget
  final Widget child;
  
  /// Scaffold键，用于控制侧边栏
  final GlobalKey<ScaffoldState> scaffoldKey;
  
  /// 侧边栏切换回调
  final VoidCallback? onDrawerToggle;
  
  /// 是否启用手势处理（默认true）
  final bool enableGestures;
  
  /// 构造函数
  const AndroidGestureWrapper({
    Key? key,
    required this.child,
    required this.scaffoldKey,
    this.onDrawerToggle,
    this.enableGestures = true,
  }) : super(key: key);

  @override
  State<AndroidGestureWrapper> createState() => _AndroidGestureWrapperState();
}

/// _AndroidGestureWrapperState
///
/// PURPOSE:
///     AndroidGestureWrapper的状态管理类
///
/// FEATURES:
///     - 手势服务管理：初始化和管理AndroidGestureService
///     - 手势事件处理：处理滑动手势事件
///     - 生命周期管理：管理手势服务的生命周期
class _AndroidGestureWrapperState extends State<AndroidGestureWrapper> {
  /// Android手势服务实例
  late final AndroidGestureService _gestureService;
  
  @override
  void initState() {
    super.initState();
    
    // 只在Android平台初始化手势服务
    if (Platform.isAndroid) {
      _gestureService = AndroidGestureService();
      _gestureService.initialize(
        scaffoldKey: widget.scaffoldKey,
        onDrawerToggle: widget.onDrawerToggle,
      );
    }
  }
  
  @override
  void dispose() {
    // 清理手势服务资源
    if (Platform.isAndroid) {
      _gestureService.dispose();
    }
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    // 如果不是Android平台或禁用手势，直接返回子Widget
    if (!Platform.isAndroid || !widget.enableGestures) {
      return widget.child;
    }
    
    // 使用PopScope禁用系统返回手势，优先使用自定义手势
    return PopScope(
      canPop: false, // 禁用系统返回手势
      onPopInvokedWithResult: (didPop, result) {
        // 当用户尝试返回时，检查是否有侧边栏需要关闭
        if (!didPop) {
          _handleSystemBackGesture();
        }
      },
      child: GestureDetector(
        // 处理滑动手势
        onPanUpdate: (details) {
          if (widget.enableGestures) {
            final screenSize = MediaQuery.of(context).size;
            _gestureService.handlePanUpdate(details, screenSize);
          }
        },
        onPanEnd: (details) {
          if (widget.enableGestures) {
            final screenSize = MediaQuery.of(context).size;
            _gestureService.handlePanEnd(details, screenSize);
          }
        },
        // 确保手势检测器覆盖整个区域
        behavior: HitTestBehavior.translucent,
        child: widget.child,
      ),
    );
  }
  
  /// 处理系统返回手势
  void _handleSystemBackGesture() {
    // 如果侧边栏已打开，关闭它
    if (widget.scaffoldKey.currentState?.isDrawerOpen == true) {
      Navigator.of(context).pop();
      widget.onDrawerToggle?.call();
    } else {
      // 如果没有侧边栏，最小化应用
      _gestureService.handlePanEnd(
        DragEndDetails(
          velocity: const Velocity(pixelsPerSecond: Offset(-600, 0)),
        ),
        MediaQuery.of(context).size,
      );
    }
  }
}

/// AndroidGestureBuilder
///
/// PURPOSE:
///     便捷的构建器函数，用于快速创建带有Android手势支持的Widget
///
/// FEATURES:
///     - 简化API：提供更简洁的创建方式
///     - 条件包装：自动检测平台并决定是否包装手势
///     - 默认配置：提供合理的默认配置
///
/// USAGE:
///     AndroidGestureBuilder.wrap(
///       scaffoldKey: _scaffoldKey,
///       child: YourWidget(),
///     )
class AndroidGestureBuilder {
  /// 包装Widget以支持Android手势
  ///
  /// DESCRIPTION:
  ///     为Widget添加Android平台特定的手势支持
  ///
  /// PARAMETERS:
  ///     scaffoldKey - Scaffold键
  ///     child - 要包装的子Widget
  ///     onDrawerToggle - 侧边栏切换回调
  ///     enableGestures - 是否启用手势（默认true）
  ///
  /// RETURNS:
  ///     Widget - 包装后的Widget
  static Widget wrap({
    required GlobalKey<ScaffoldState> scaffoldKey,
    required Widget child,
    VoidCallback? onDrawerToggle,
    bool enableGestures = true,
  }) {
    // 只在Android平台包装手势
    if (Platform.isAndroid && enableGestures) {
      return AndroidGestureWrapper(
        scaffoldKey: scaffoldKey,
        onDrawerToggle: onDrawerToggle,
        enableGestures: enableGestures,
        child: child,
      );
    }
    
    // 其他平台直接返回子Widget
    return child;
  }
  
  /// 创建带有条件手势支持的Builder
  ///
  /// DESCRIPTION:
  ///     根据条件决定是否启用手势支持
  ///
  /// PARAMETERS:
  ///     condition - 启用条件
  ///     scaffoldKey - Scaffold键
  ///     child - 要包装的子Widget
  ///     onDrawerToggle - 侧边栏切换回调
  ///
  /// RETURNS:
  ///     Widget - 包装后的Widget
  static Widget conditional({
    required bool condition,
    required GlobalKey<ScaffoldState> scaffoldKey,
    required Widget child,
    VoidCallback? onDrawerToggle,
  }) {
    return wrap(
      scaffoldKey: scaffoldKey,
      child: child,
      onDrawerToggle: onDrawerToggle,
      enableGestures: condition,
    );
  }
}
