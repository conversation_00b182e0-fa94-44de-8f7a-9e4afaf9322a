/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// ******************************************************************************
/// FILE NAME :      custom_window_frame.dart
///
/// DESCRIPTION :    Custom window frame widget with title bar and window controls for desktop platforms
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:window_manager/window_manager.dart';
import '../utils/design_system.dart';
import '../core/dependency_injection.dart';
import '../services/api_service.dart';
import '../services/backend_service.dart';
import '../services/log_service.dart';
import '../services/tray_service.dart';
import '../core/app_state.dart';
import '../models/connection_status.dart';
import '../widgets/svg_asset_widget.dart';
import 'dart:io' as dart_io;

/// CustomWindowFrame
///
/// PURPOSE:
///     A custom window frame widget that provides a native-like window experience
///     with custom title bar, window controls, and application branding.
///
/// FEATURES:
///     - Custom title bar with application logo
///     - Window control buttons (minimize, close)
///     - Draggable window area
///     - Gradient background matching design system
///     - Platform-specific window management
///     - Complete application exit handling
///
/// USAGE:
///     CustomWindowFrame(
///       showWindowControls: true,
///       child: YourMainContent(),
///     )
class CustomWindowFrame extends StatelessWidget {
  /// The main content widget to be displayed within the window frame
  final Widget child;

  /// Whether to show window control buttons (minimize, close)
  final bool showWindowControls;

  /// CustomWindowFrame constructor
  ///
  /// DESCRIPTION:
  ///     Creates a custom window frame with optional window controls.
  ///
  /// PARAMETERS:
  ///     child - The main content widget (required)
  ///     showWindowControls - Whether to display window control buttons (default: true)
  const CustomWindowFrame({
    Key? key,
    required this.child,
    this.showWindowControls = true,
  }) : super(key: key);

  /// build
  ///
  /// DESCRIPTION:
  ///     Builds the custom window frame with gradient background and optional title bar.
  ///     Creates a rounded container with the application's design system styling.
  ///
  /// PARAMETERS:
  ///     context - Build context for the widget
  ///
  /// RETURNS:
  ///     Widget tree representing the custom window frame
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(DesignSystem.windowBorderRadius),
        gradient: const LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: AppColors.backgroundGradient,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignSystem.windowBorderRadius),
        child: Column(
          children: [
            // 自定义标题栏
            if (showWindowControls) _buildTitleBar(),

            // 主要内容区域
            Expanded(child: child),
          ],
        ),
      ),
    );
  }

  /// _buildTitleBar
  ///
  /// DESCRIPTION:
  ///     Builds the custom title bar with logo, draggable area, and window controls.
  ///     Provides native window management functionality for desktop platforms.
  ///     Left portion matches sidebar background color for visual consistency.
  ///
  /// RETURNS:
  ///     Widget representing the title bar
  Widget _buildTitleBar() {
    return SizedBox(
      height: 40,
      child: Row(
        children: [
          // 左半部分：Logo区域，背景色与侧边栏一致
          Container(
            width: DesignSystem.sidebarWidth,
            color: AppColors.sidebarBackground,
            child: _buildTitleBarLogo(),
          ),

          // 右半部分：可拖拽区域，保持米白色背景
          Expanded(
            child: GestureDetector(
              onPanStart: (details) {
                if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
                  windowManager.startDragging();
                }
              },
              child: Container(
                color: Colors.transparent, // 保持透明，显示下层的米白色背景
              ),
            ),
          ),

          // 窗口控制按钮
          _buildWindowControls(),
        ],
      ),
    );
  }

  /// _buildTitleBarLogo
  ///
  /// DESCRIPTION:
  ///     Builds the application logo displayed in the title bar.
  ///     Shows the Panabit Client shield logo with appropriate padding.
  ///     Now positioned within the sidebar-colored background area.
  ///
  /// RETURNS:
  ///     Widget representing the title bar logo
  Widget _buildTitleBarLogo() {
    return Padding(
      padding: const EdgeInsets.only(left: 12, top: 8, bottom: 8),
      child: DesignSystemSvgs.shieldLogo(
        width: 24,
        height: 24,
      ),
    );
  }

  /// _buildWindowControls
  ///
  /// DESCRIPTION:
  ///     Builds the window control buttons (minimize, close) for Windows platform.
  ///     Returns empty widget for non-Windows platforms.
  ///
  /// RETURNS:
  ///     Widget containing window control buttons or empty widget
  Widget _buildWindowControls() {
    if (!Platform.isWindows) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 最小化按钮
        _WindowControlButton(
          icon: Icons.minimize,
          onPressed: _minimizeWindow,
          tooltip: 'Minimize',
        ),

        // 关闭按钮
        _WindowControlButton(
          icon: Icons.close,
          onPressed: _closeWindow,
          tooltip: 'Close',
          isCloseButton: true,
        ),
      ],
    );
  }

  /// _minimizeWindow
  ///
  /// DESCRIPTION:
  ///     Minimizes the application window to the taskbar.
  ///     Uses the window manager to perform the minimize operation.
  void _minimizeWindow() async {
    await windowManager.minimize();
  }

  /// _closeWindow
  ///
  /// DESCRIPTION:
  ///     在Windows平台隐藏到托盘，其他平台执行完整的应用退出流程
  void _closeWindow() async {
    final logService = serviceLocator<LogService>();

    logService.info('WindowControls', 'User clicked close button');

    // 在Windows平台且托盘服务可用时，隐藏到托盘而不是退出
    if (Platform.isWindows) {
      try {
        final trayService = serviceLocator<TrayService>();
        logService.info('WindowControls', 'Hiding window to system tray');
        await trayService.hideWindow();
        return;
      } catch (e) {
        logService.warning('WindowControls', 'TrayService not available, proceeding with exit: $e');
      }
    }

    // 其他平台或托盘服务不可用时，执行完整的退出流程
    logService.info('WindowControls', 'Starting complete exit process');

    final apiService = serviceLocator<ApiService>();
    final backendService = serviceLocator<BackendService>();
    final appState = serviceLocator<AppState>();

    try {
      // 1. 如果已连接，先断开连接
      if (appState.connectionStatus == ConnectionStatus.connected) {
        logService.info('WindowControls', 'Disconnecting WAN...');

        try {
          // 断开WAN连接
          await apiService.disconnect();
          logService.info('WindowControls', 'WAN disconnected successfully');
          // 等待一下，确保断开连接完成
          await Future.delayed(const Duration(milliseconds: 500));
        } catch (e) {
          logService.error('WindowControls', 'Failed to disconnect WAN', e);
        }
      }

      // 2. 停止后端服务
      logService.info('WindowControls', 'Stopping backend service...');

      // 尝试通过API关闭后端服务
      bool backendShutdownSuccess = false;
      try {
        final result = await apiService.shutdownBackend();
        logService.info('WindowControls', 'Backend service shutdown request result: $result');
        // 给后端一些时间来处理关闭请求
        await Future.delayed(const Duration(milliseconds: 500));
        backendShutdownSuccess = true;
      } catch (e) {
        // 忽略错误，因为后端服务可能已经关闭
        logService.info('WindowControls', 'Backend service shutdown via API failed: $e');
      }

      // 如果通过API关闭失败，尝试通过BackendService停止后端
      if (!backendShutdownSuccess) {
        try {
          await backendService.stopBackend();
          logService.info('WindowControls', 'Backend service stopped via BackendService successfully');
          backendShutdownSuccess = true;
        } catch (e) {
          logService.error('WindowControls', 'Failed to stop backend service via BackendService', e);
        }
      }

      logService.info('WindowControls', 'Backend service shutdown ${backendShutdownSuccess ? 'successful' : 'may have failed'}');

      // 3. 关闭窗口
      logService.info('WindowControls', 'Preparing to close window...');

      try {
        // 强制关闭窗口
        logService.info('WindowControls', 'Setting preventClose to false');
        await windowManager.setPreventClose(false);
        logService.info('WindowControls', 'Calling windowManager.destroy()');
        await windowManager.destroy();
        logService.info('WindowControls', 'Window destruction completed');
      } catch (e) {
        logService.error('WindowControls', 'Failed to destroy window', e);
      }

      // 4. 退出应用
      logService.info('WindowControls', 'Force exiting application');
      dart_io.exit(0);
    } catch (e) {
      logService.error('WindowControls', 'Error occurred while exiting application', e);
      // 即使出错，也强制退出
      dart_io.exit(1);
    }
  }
}

/// _WindowControlButton
///
/// PURPOSE:
///     A custom window control button widget that provides hover effects
///     and platform-specific styling for window operations.
///
/// FEATURES:
///     - Hover state management with visual feedback
///     - Special styling for close button (red background on hover)
///     - Tooltip support for accessibility
///     - Consistent sizing and alignment
///
/// USAGE:
///     _WindowControlButton(
///       icon: Icons.close,
///       onPressed: () => closeWindow(),
///       tooltip: 'Close',
///       isCloseButton: true,
///     )
class _WindowControlButton extends StatefulWidget {
  /// The icon to display in the button
  final IconData icon;

  /// Callback function triggered when the button is pressed
  final VoidCallback onPressed;

  /// Tooltip text displayed on hover
  final String tooltip;

  /// Whether this is a close button (affects hover styling)
  final bool isCloseButton;

  /// _WindowControlButton constructor
  ///
  /// DESCRIPTION:
  ///     Creates a window control button with hover effects and tooltip.
  ///
  /// PARAMETERS:
  ///     icon - The icon to display (required)
  ///     onPressed - Callback for button press events (required)
  ///     tooltip - Tooltip text for accessibility (required)
  ///     isCloseButton - Whether this is a close button for special styling (default: false)
  const _WindowControlButton({
    Key? key,
    required this.icon,
    required this.onPressed,
    required this.tooltip,
    this.isCloseButton = false,
  }) : super(key: key);

  @override
  _WindowControlButtonState createState() => _WindowControlButtonState();
}

class _WindowControlButtonState extends State<_WindowControlButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: Tooltip(
        message: widget.tooltip,
        child: InkWell(
          onTap: widget.onPressed,
          child: Container(
            width: 46,
            height: 40,
            decoration: BoxDecoration(
              color: _getBackgroundColor(),
              borderRadius: BorderRadius.zero,
            ),
            alignment: Alignment.center, // 确保图标在按钮中垂直和水平居中
            child: Icon(
              widget.icon,
              size: 16,
              color: _getIconColor(),
            ),
          ),
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    if (!_isHovered) return Colors.transparent;

    if (widget.isCloseButton) {
      return AppColors.error; // Close button hover color is red
    }

    return AppColors.textLight.withValues(alpha: 0.1); // Other buttons hover color is light
  }

  Color _getIconColor() {
    if (widget.isCloseButton && _isHovered) {
      return Colors.white; // 关闭按钮悬停时图标为白色
    }

    return Colors.black; // 修改为黑色图标，与米白色背景形成对比
  }
}

/// AppWindowWrapper
///
/// PURPOSE:
///     A wrapper widget that initializes and manages window properties for desktop platforms.
///     Handles window sizing, positioning, and system integration.
///
/// FEATURES:
///     - Window size and position management
///     - Custom title bar configuration
///     - Window listener integration
///     - Platform-specific window settings
///     - Automatic window centering and display
///
/// USAGE:
///     AppWindowWrapper(
///       child: YourMainApplication(),
///     )
class AppWindowWrapper extends StatefulWidget {
  /// The main application widget to be wrapped
  final Widget child;

  /// AppWindowWrapper constructor
  ///
  /// DESCRIPTION:
  ///     Creates a window wrapper that manages desktop window properties.
  ///
  /// PARAMETERS:
  ///     child - The main application widget (required)
  const AppWindowWrapper({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<AppWindowWrapper> createState() => _AppWindowWrapperState();
}

/// _AppWindowWrapperState
///
/// PURPOSE:
///     State class for AppWindowWrapper that manages window lifecycle and properties.
///     Implements WindowListener to handle window events.
///
/// FEATURES:
///     - Window initialization and configuration
///     - Window event handling
///     - Resource cleanup on disposal
class _AppWindowWrapperState extends State<AppWindowWrapper> with WindowListener {
  @override
  void initState() {
    super.initState();
    _initializeWindow();
  }

  @override
  void dispose() {
    // Only remove window listener on desktop platforms
    if (!Platform.isIOS && !Platform.isAndroid) {
      windowManager.removeListener(this);
    }
    super.dispose();
  }

  /// _initializeWindow
  ///
  /// DESCRIPTION:
  ///     Initializes window properties including size, position, and appearance.
  ///     Sets up window manager configuration for desktop platforms only.
  ///     On mobile platforms (iOS/Android), this method does nothing.
  Future<void> _initializeWindow() async {
    // Only initialize window manager on desktop platforms
    if (!Platform.isIOS && !Platform.isAndroid) {
      // 等待窗口管理器准备就绪
      await windowManager.ensureInitialized();

      // 添加窗口监听器
      windowManager.addListener(this);

      // 设置窗口属性
      await windowManager.setSize(
        const Size(DesignSystem.windowWidth, DesignSystem.windowHeight),
      );

      // 设置最小窗口大小
      await windowManager.setMinimumSize(
        const Size(DesignSystem.windowWidth, DesignSystem.windowHeight),
      );

      // 设置最大窗口大小（固定大小）
      await windowManager.setMaximumSize(
        const Size(DesignSystem.windowWidth, DesignSystem.windowHeight),
      );

      // 只在Windows/Linux隐藏系统标题栏，macOS使用系统标题栏
      if (Platform.isWindows || Platform.isLinux) {
        await windowManager.setTitleBarStyle(
          TitleBarStyle.hidden,
          windowButtonVisibility: false,
        );
      }

      // 设置窗口为不可调整大小
      await windowManager.setResizable(false);

      // 居中显示窗口
      await windowManager.center();

      // 显示窗口
      await windowManager.show();
    }
  }

  /// build
  ///
  /// DESCRIPTION:
  ///     Builds the widget tree. Simply returns the child widget as the window
  ///     configuration is handled in initState.
  ///
  /// PARAMETERS:
  ///     context - Build context for the widget
  ///
  /// RETURNS:
  ///     The child widget passed to the wrapper
  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  /// onWindowClose
  ///
  /// DESCRIPTION:
  ///     Handles window close events from the window manager.
  ///     Performs cleanup and destroys the window.
  @override
  void onWindowClose() async {
    // 处理窗口关闭事件
    // 这里可以添加关闭前的清理逻辑
    await windowManager.destroy();
  }
}
