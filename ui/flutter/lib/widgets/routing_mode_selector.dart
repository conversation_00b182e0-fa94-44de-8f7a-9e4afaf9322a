// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      routing_mode_selector.dart
//  *
//  * DESCRIPTION :    路由模式选择器组件，用于配置VPN路由设置，支持全部路由
//  *                  和自定义网段路由两种模式
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  ******************************************************************************/

import 'dart:async';
import 'package:flutter/material.dart';
import '../models/routing_settings.dart';

import '../utils/design_system.dart';
import '../generated/l10n/app_localizations.dart';
import '../services/font_service.dart';
import '../core/dependency_injection.dart';

/// RoutingModeSelector
///
/// PURPOSE:
///     A widget that allows users to select and configure VPN routing modes.
///     Supports both all-traffic routing and custom network segment routing.
///
/// FEATURES:
///     - Radio button selection for routing modes
///     - Custom network segment input with validation
///     - Debounced input handling for performance
///     - Language-aware font styling
///     - Loading state indicator
///     - Animated custom route input area
///     - CIDR format validation hints
///
/// USAGE:
///     RoutingModeSelector(
///       settings: routingSettings,
///       onSettingsChanged: (settings) => updateSettings(settings),
///       isLoading: false,
///     )
class RoutingModeSelector extends StatefulWidget {
  /// Current routing settings model
  final RoutingSettingsModel settings;

  /// Callback function when settings are changed
  final Function(RoutingSettingsModel) onSettingsChanged;

  /// Whether the widget is in loading state
  final bool isLoading;

  /// RoutingModeSelector constructor
  ///
  /// DESCRIPTION:
  ///     Creates a routing mode selector with the provided settings and callback.
  ///
  /// PARAMETERS:
  ///     settings - Current RoutingSettingsModel object
  ///     onSettingsChanged - Callback function triggered when settings change
  ///     isLoading - Loading state indicator (default: false)
  const RoutingModeSelector({
    Key? key,
    required this.settings,
    required this.onSettingsChanged,
    this.isLoading = false,
  }) : super(key: key);

  @override
  State<RoutingModeSelector> createState() => _RoutingModeSelectorState();
}

/// _RoutingModeSelectorState
///
/// PURPOSE:
///     State management for RoutingModeSelector widget.
///     Handles text input, debouncing, and widget lifecycle management.
///
/// FEATURES:
///     - Text controller management for custom routes
///     - Debounced input handling to prevent excessive updates
///     - Widget update synchronization
///     - Proper resource cleanup
class _RoutingModeSelectorState extends State<RoutingModeSelector> {
  /// Text controller for custom routes input field
  late TextEditingController _customRoutesController;

  /// Timer for debouncing custom routes input changes
  Timer? _debounceTimer;

  /// initState
  ///
  /// DESCRIPTION:
  ///     Initializes the state and sets up the text controller with current settings.
  @override
  void initState() {
    super.initState();
    _customRoutesController = TextEditingController(text: widget.settings.customRoutes);
  }

  /// didUpdateWidget
  ///
  /// DESCRIPTION:
  ///     Updates the text controller when widget settings change externally.
  ///
  /// PARAMETERS:
  ///     oldWidget - Previous RoutingModeSelector widget instance
  @override
  void didUpdateWidget(RoutingModeSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.settings.customRoutes != widget.settings.customRoutes) {
      _customRoutesController.text = widget.settings.customRoutes;
    }
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     Cleans up resources when the widget is disposed.
  ///     Cancels debounce timer and disposes text controller.
  @override
  void dispose() {
    _debounceTimer?.cancel();
    _customRoutesController.dispose();
    super.dispose();
  }

  /// _onCustomRoutesChanged
  ///
  /// DESCRIPTION:
  ///     Handles custom routes input changes with debouncing to prevent excessive updates.
  ///     Cancels previous timer and sets a new one for 500ms delay.
  ///
  /// PARAMETERS:
  ///     value - New custom routes text value
  void _onCustomRoutesChanged(String value) {
    // 取消之前的定时器
    _debounceTimer?.cancel();

    // 设置新的定时器，500ms后触发变更
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      widget.settings.updateSettings(customRoutes: value);
      widget.onSettingsChanged(widget.settings);
    });
  }

  /// _getTextStyle
  ///
  /// DESCRIPTION:
  ///     Creates a text style with language-aware font selection and customizable properties.
  ///     Provides fallback fonts for better cross-platform compatibility.
  ///
  /// PARAMETERS:
  ///     fontSize - Font size for the text style
  ///     fontWeight - Font weight (optional, default: FontWeight.w400)
  ///     color - Text color (optional, default: AppColors.textPrimary)
  ///     fontStyle - Font style (optional, default: normal)
  ///
  /// RETURNS:
  ///     TextStyle object with appropriate font and styling
  TextStyle _getTextStyle({
    required double fontSize,
    FontWeight? fontWeight,
    Color? color,
    FontStyle? fontStyle,
  }) {
    try {
      final fontService = serviceLocator<FontService>();
      return fontService.createTextStyleWithMappedWeight(
        fontSize: fontSize,
        fontWeight: fontWeight ?? FontWeight.w400,
        color: color ?? AppColors.textPrimary,
      ).copyWith(fontStyle: fontStyle);
    } catch (e) {
      return TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight ?? FontWeight.w400,
        color: color ?? AppColors.textPrimary,
        fontStyle: fontStyle,
        fontFamilyFallback: const ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'],
      );
    }
  }

  /// build
  ///
  /// DESCRIPTION:
  ///     Builds the routing mode selector widget with radio buttons and custom input area.
  ///     Provides animated transitions and loading state indicators.
  ///
  /// PARAMETERS:
  ///     context - Build context for the widget
  ///
  /// RETURNS:
  ///     Widget tree representing the routing mode selector
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Container(
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.textSecondary.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 路由模式选择
            Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: AppColors.cardBackground,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      // 全部路由选项
                      _buildRadioTile(
                        title: l10n.allRouting,
                        subtitle: l10n.allRoutingDescription,
                        value: RoutingMode.all,
                        groupValue: widget.settings.mode,
                        onChanged: (value) {
                          if (value != null) {
                            widget.settings.updateSettings(mode: value);
                            widget.onSettingsChanged(widget.settings);
                          }
                        },
                      ),

                      const Divider(
                        height: 1,
                        thickness: 1,
                        color: AppColors.divider, // 使用设计系统颜色
                        indent: 16,
                        endIndent: 16,
                      ),

                      // 按网段路由选项
                      _buildRadioTile(
                        title: l10n.customRouting,
                        subtitle: l10n.customRoutingDescription,
                        value: RoutingMode.custom,
                        groupValue: widget.settings.mode,
                        onChanged: (value) {
                          if (value != null) {
                            widget.settings.updateSettings(mode: value);
                            widget.onSettingsChanged(widget.settings);
                          }
                        },
                      ),
                    ],
                  ),
                ),
                // 加载指示器
                if (widget.isLoading)
                  const Positioned(
                    top: 8,
                    right: 8,
                    child: SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                      ),
                    ),
                  ),
              ],
            ),

            // 自定义路由输入框
            if (widget.settings.mode == RoutingMode.custom)
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: const EdgeInsets.only(top: 16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppColors.primary.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n.enterNetworkSegments,
                      style: _getTextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      l10n.networkSegmentsExample,
                      style: _getTextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      controller: _customRoutesController,
                      decoration: InputDecoration(
                        hintText: l10n.enterNetworkSegmentsHint,
                        hintStyle: _getTextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: AppColors.divider),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: AppColors.divider),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: AppColors.primary, width: 2),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                        fillColor: Colors.white,
                        filled: true,
                      ),
                      style: _getTextStyle(fontSize: 14),
                      minLines: 3,
                      maxLines: 5,
                      textInputAction: TextInputAction.done,
                      keyboardType: TextInputType.multiline,
                      onChanged: _onCustomRoutesChanged,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(
                          Icons.info_outline,
                          size: 14,
                          color: AppColors.textSecondary,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            l10n.ensureCorrectCidrFormat,
                            style: _getTextStyle(
                              fontSize: 12,
                              color: AppColors.textSecondary,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// _buildRadioTile
  ///
  /// DESCRIPTION:
  ///     Builds a radio list tile for routing mode selection with title and subtitle.
  ///     Provides visual feedback and proper styling for selection states.
  ///
  /// PARAMETERS:
  ///     title - Main title text for the radio option
  ///     subtitle - Subtitle text describing the option
  ///     value - RoutingMode value for this option
  ///     groupValue - Currently selected RoutingMode value
  ///     onChanged - Callback function when selection changes
  ///
  /// RETURNS:
  ///     Widget representing a radio list tile
  Widget _buildRadioTile({
    required String title,
    required String subtitle,
    required RoutingMode value,
    required RoutingMode groupValue,
    required Function(RoutingMode?) onChanged,
  }) {
    return RadioListTile<RoutingMode>(
      title: Text(
        title,
        style: _getTextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: AppColors.textPrimary,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: _getTextStyle(
          fontSize: 12,
          color: AppColors.textSecondary,
        ),
      ),
      value: value,
      groupValue: groupValue,
      onChanged: widget.isLoading ? null : onChanged,
      activeColor: AppColors.primary,
      // 设置未选中状态的颜色，与语言设置单选框保持一致
      fillColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
        if (states.contains(WidgetState.selected)) {
          return AppColors.primary; // 选中时使用主色
        }
        return AppColors.textSecondary; // 未选中时使用与语言设置相同的颜色
      }),
      contentPadding: EdgeInsets.zero,
      dense: true,
      visualDensity: VisualDensity.compact,
    );
  }
}
