// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      svg_asset_widget.dart
//  *
//  * DESCRIPTION :    SVG asset widgets and utilities for design system integration
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  ******************************************************************************/

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../utils/design_system.dart';

/// SvgAssetWidget
///
/// PURPOSE:
///     A utility widget for rendering SVG assets from the design system.
///     Provides consistent SVG rendering with color filtering and sizing options.
///
/// FEATURES:
///     - Asset path-based SVG rendering
///     - Color filtering support with BlendMode.srcIn
///     - Flexible sizing with width and height parameters
///     - BoxFit options for different scaling behaviors
///     - Accessibility support with semantics labels
///     - Optimized rendering performance
///
/// USAGE:
///     SvgAssetWidget(
///       assetPath: 'assets/images/logo.svg',
///       width: 100,
///       height: 100,
///       color: Colors.blue,
///       semanticsLabel: 'Company Logo',
///     )
class SvgAssetWidget extends StatelessWidget {
  /// Path to the SVG asset file
  final String assetPath;

  /// Width of the rendered SVG (optional)
  final double? width;

  /// Height of the rendered SVG (optional)
  final double? height;

  /// Color to apply to the SVG using color filtering (optional)
  final Color? color;

  /// How the SVG should fit within the available space
  final BoxFit fit;

  /// Accessibility label for screen readers (optional)
  final String? semanticsLabel;

  /// SvgAssetWidget constructor
  ///
  /// DESCRIPTION:
  ///     Creates an SVG asset widget with the specified parameters.
  ///
  /// PARAMETERS:
  ///     assetPath - Path to the SVG asset file
  ///     width - Width of the rendered SVG (optional)
  ///     height - Height of the rendered SVG (optional)
  ///     color - Color to apply to the SVG (optional)
  ///     fit - How the SVG should fit within available space (default: BoxFit.contain)
  ///     semanticsLabel - Accessibility label (optional)
  const SvgAssetWidget({
    Key? key,
    required this.assetPath,
    this.width,
    this.height,
    this.color,
    this.fit = BoxFit.contain,
    this.semanticsLabel,
  }) : super(key: key);

  /// build
  ///
  /// DESCRIPTION:
  ///     Builds the SVG widget using flutter_svg package with the specified parameters.
  ///     Applies color filtering if a color is provided and includes error handling.
  ///
  /// PARAMETERS:
  ///     context - Build context for the widget
  ///
  /// RETURNS:
  ///     SvgPicture widget configured with the provided parameters, or error widget on failure
  @override
  Widget build(BuildContext context) {
    // 检查是否为PNG文件，如果是则使用Image.asset
    if (assetPath.endsWith('.png')) {
      return Image.asset(
        assetPath,
        width: width,
        height: height,
        fit: fit,
        semanticLabel: semanticsLabel,
        color: color,
        colorBlendMode: color != null ? BlendMode.srcIn : null,
        errorBuilder: (BuildContext context, Object error, StackTrace? stackTrace) {
          debugPrint('PNG loading error for $assetPath: $error');
          return Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: AppColors.error.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: AppColors.error.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.broken_image_outlined,
              size: (width != null && height != null)
                ? (width! < height! ? width! * 0.5 : height! * 0.5)
                : 24,
              color: AppColors.error,
            ),
          );
        },
      );
    }

    // 原有的SVG处理逻辑
    return SvgPicture.asset(
      assetPath,
      width: width,
      height: height,
      colorFilter: color != null
        ? ColorFilter.mode(color!, BlendMode.srcIn)
        : null,
      fit: fit,
      semanticsLabel: semanticsLabel,
      placeholderBuilder: (BuildContext context) => Container(
        width: width,
        height: height,
        color: AppColors.divider.withValues(alpha: 0.1),
        child: const Center(
          child: SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.textSecondary),
            ),
          ),
        ),
      ),
      // 添加错误处理
      errorBuilder: (BuildContext context, Object error, StackTrace? stackTrace) {
        debugPrint('SVG loading error for $assetPath: $error');
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: AppColors.error.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: AppColors.error.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Icon(
            Icons.broken_image_outlined,
            size: (width != null && height != null)
              ? (width! < height! ? width! * 0.5 : height! * 0.5)
              : 24,
            color: AppColors.error,
          ),
        );
      },
    );
  }
}

/// SvgAssets
///
/// PURPOSE:
///     Constants class containing all SVG asset paths used in the design system.
///     Provides centralized management of SVG resources with localization support.
///
/// FEATURES:
///     - Centralized asset path management
///     - Localized asset path resolution
///     - Type-safe asset path constants
///     - Language-aware asset selection
///     - Consistent naming conventions
///     - Asset path validation
class SvgAssets {
  /// Base path for all SVG assets
  static const String _basePath = 'assets/images/';

  // 主要Logo
  /// Panabit main logo asset path (用于连接、用户、关于界面)
  static const String itforceLogo = 'assets/icons/panabit_r.svg';

  /// Panabit shield logo asset path (用于登录界面)
  static const String shieldLogo = 'assets/icons/panabit_r.svg';

  /// Panabit letter logo light version asset path
  static const String letterLogoLight = 'assets/icons/panabit_r.svg';

  // 连接状态图标 - 中文版本
  /// Connected button icon (Chinese version) asset path
  static const String connectedButton = '${_basePath}connected.svg';

  /// Disconnected button icon (Chinese version) asset path
  static const String disconnectedButton = '${_basePath}disconnected.svg';

  // 连接状态图标 - 英文版本
  /// Connected button icon (English version) asset path
  static const String connectedButtonEn = '${_basePath}connected_en.svg';

  /// Disconnected button icon (English version) asset path
  static const String disconnectedButtonEn = '${_basePath}disconnected_en.svg';

  // 背景和装饰
  /// Background mask overlay asset path
  static const String maskOverlay = '${_basePath}mask.svg';

  /// getConnectedButtonPath
  ///
  /// DESCRIPTION:
  ///     Returns the appropriate connected button SVG path based on language preference.
  ///
  /// PARAMETERS:
  ///     isEnglish - Whether to use English version (true) or Chinese version (false)
  ///
  /// RETURNS:
  ///     String path to the appropriate connected button SVG asset
  static String getConnectedButtonPath(bool isEnglish) {
    return isEnglish ? connectedButtonEn : connectedButton;
  }

  /// getDisconnectedButtonPath
  ///
  /// DESCRIPTION:
  ///     Returns the appropriate disconnected button SVG path based on language preference.
  ///
  /// PARAMETERS:
  ///     isEnglish - Whether to use English version (true) or Chinese version (false)
  ///
  /// RETURNS:
  ///     String path to the appropriate disconnected button SVG asset
  static String getDisconnectedButtonPath(bool isEnglish) {
    return isEnglish ? disconnectedButtonEn : disconnectedButton;
  }

  /// getAllAssetPaths
  ///
  /// DESCRIPTION:
  ///     Returns a list of all SVG asset paths for validation and preloading purposes.
  ///
  /// RETURNS:
  ///     List of all SVG asset paths defined in this class
  static List<String> getAllAssetPaths() {
    return [
      itforceLogo,
      shieldLogo,
      letterLogoLight,
      connectedButton,
      disconnectedButton,
      connectedButtonEn,
      disconnectedButtonEn,
      maskOverlay,
    ];
  }
}

/// DesignSystemSvgs
///
/// PURPOSE:
///     Factory class providing pre-configured SVG widgets for the design system.
///     Offers convenient methods to create commonly used SVG components.
///
/// FEATURES:
///     - Pre-configured SVG widget factories
///     - Consistent semantics labels for accessibility
///     - Flexible sizing and coloring options
///     - Localization support for language-specific assets
///     - Type-safe widget creation methods
class DesignSystemSvgs {
  /// itforceLogo
  ///
  /// DESCRIPTION:
  ///     Creates a Panabit main logo SVG widget with optional sizing and coloring.
  ///
  /// PARAMETERS:
  ///     width - Width of the logo (optional)
  ///     height - Height of the logo (optional)
  ///     color - Color to apply to the logo (optional)
  ///
  /// RETURNS:
  ///     SvgAssetWidget configured for Panabit main logo
  static Widget itforceLogo({
    double? width,
    double? height,
    Color? color,
  }) {
    return SvgAssetWidget(
      assetPath: SvgAssets.itforceLogo,
      width: width,
      height: height,
      color: color,
      semanticsLabel: 'Panabit Logo',
    );
  }

  /// shieldLogo
  ///
  /// DESCRIPTION:
  ///     Creates a Panabit shield logo SVG widget with optional sizing and coloring.
  ///
  /// PARAMETERS:
  ///     width - Width of the logo (optional)
  ///     height - Height of the logo (optional)
  ///     color - Color to apply to the logo (optional)
  ///
  /// RETURNS:
  ///     SvgAssetWidget configured for Panabit shield logo
  static Widget shieldLogo({
    double? width,
    double? height,
    Color? color,
  }) {
    return SvgAssetWidget(
      assetPath: SvgAssets.shieldLogo,
      width: width,
      height: height,
      color: color,
      semanticsLabel: 'Panabit Shield Logo',
    );
  }

  /// letterLogoLight
  ///
  /// DESCRIPTION:
  ///     Creates an ITFORCE letter logo (light version) SVG widget with optional sizing and coloring.
  ///
  /// PARAMETERS:
  ///     width - Width of the logo (optional)
  ///     height - Height of the logo (optional)
  ///     color - Color to apply to the logo (optional)
  ///
  /// RETURNS:
  ///     SvgAssetWidget configured for ITFORCE letter logo light version
  static Widget letterLogoLight({
    double? width,
    double? height,
    Color? color,
  }) {
    return SvgAssetWidget(
      assetPath: SvgAssets.letterLogoLight,
      width: width,
      height: height,
      color: color,
      semanticsLabel: 'ITFORCE Letter Logo Light',
    );
  }

  /// connectedButton
  ///
  /// DESCRIPTION:
  ///     Creates a connected button icon SVG widget using the default Chinese version.
  ///
  /// PARAMETERS:
  ///     width - Width of the button icon (optional)
  ///     height - Height of the button icon (optional)
  ///     color - Color to apply to the button icon (optional)
  ///
  /// RETURNS:
  ///     SvgAssetWidget configured for connected button (Chinese version)
  static Widget connectedButton({
    double? width,
    double? height,
    Color? color,
  }) {
    return SvgAssetWidget(
      assetPath: SvgAssets.connectedButton,
      width: width,
      height: height,
      color: color,
      semanticsLabel: 'Connected Button',
    );
  }

  /// disconnectedButton
  ///
  /// DESCRIPTION:
  ///     Creates a disconnected button icon SVG widget using the default Chinese version.
  ///
  /// PARAMETERS:
  ///     width - Width of the button icon (optional)
  ///     height - Height of the button icon (optional)
  ///     color - Color to apply to the button icon (optional)
  ///
  /// RETURNS:
  ///     SvgAssetWidget configured for disconnected button (Chinese version)
  static Widget disconnectedButton({
    double? width,
    double? height,
    Color? color,
  }) {
    return SvgAssetWidget(
      assetPath: SvgAssets.disconnectedButton,
      width: width,
      height: height,
      color: color,
      semanticsLabel: 'Disconnected Button',
    );
  }

  /// localizedConnectedButton
  ///
  /// DESCRIPTION:
  ///     Creates a localized connected button icon SVG widget based on language preference.
  ///     Automatically selects between English and Chinese versions.
  ///
  /// PARAMETERS:
  ///     isEnglish - Whether to use English version (true) or Chinese version (false)
  ///     width - Width of the button icon (optional)
  ///     height - Height of the button icon (optional)
  ///     color - Color to apply to the button icon (optional)
  ///
  /// RETURNS:
  ///     SvgAssetWidget configured for localized connected button
  static Widget localizedConnectedButton({
    required bool isEnglish,
    double? width,
    double? height,
    Color? color,
  }) {
    return SvgAssetWidget(
      assetPath: SvgAssets.getConnectedButtonPath(isEnglish),
      width: width,
      height: height,
      color: color,
      semanticsLabel: isEnglish ? 'Connected Button (EN)' : 'Connected Button (ZH)',
    );
  }

  /// localizedDisconnectedButton
  ///
  /// DESCRIPTION:
  ///     Creates a localized disconnected button icon SVG widget based on language preference.
  ///     Automatically selects between English and Chinese versions.
  ///
  /// PARAMETERS:
  ///     isEnglish - Whether to use English version (true) or Chinese version (false)
  ///     width - Width of the button icon (optional)
  ///     height - Height of the button icon (optional)
  ///     color - Color to apply to the button icon (optional)
  ///
  /// RETURNS:
  ///     SvgAssetWidget configured for localized disconnected button
  static Widget localizedDisconnectedButton({
    required bool isEnglish,
    double? width,
    double? height,
    Color? color,
  }) {
    return SvgAssetWidget(
      assetPath: SvgAssets.getDisconnectedButtonPath(isEnglish),
      width: width,
      height: height,
      color: color,
      semanticsLabel: isEnglish ? 'Disconnected Button (EN)' : 'Disconnected Button (ZH)',
    );
  }

  /// maskOverlay
  ///
  /// DESCRIPTION:
  ///     Creates a background mask overlay SVG widget with configurable opacity.
  ///     Used for creating subtle background effects and visual depth.
  ///
  /// PARAMETERS:
  ///     width - Width of the mask overlay (optional)
  ///     height - Height of the mask overlay (optional)
  ///     opacity - Opacity level for the mask (default: 0.12)
  ///
  /// RETURNS:
  ///     Opacity widget containing SvgAssetWidget configured for background mask
  static Widget maskOverlay({
    double? width,
    double? height,
    double opacity = 0.12,
  }) {
    return Opacity(
      opacity: opacity,
      child: SvgAssetWidget(
        assetPath: SvgAssets.maskOverlay,
        width: width,
        height: height,
        fit: BoxFit.cover,
        semanticsLabel: 'Background Mask',
      ),
    );
  }
}

/// CombinedLogoWidget
///
/// PURPOSE:
///     A composite widget that combines the shield logo and letter logo into a single layout.
///     Provides a pre-configured arrangement of ITFORCE branding elements.
///
/// FEATURES:
///     - Combines shield logo and letter logo in a stack layout
///     - Proportional sizing based on overall dimensions
///     - Non-overlapping logo positioning
///     - Configurable overall size with maintained aspect ratio
///     - Optimized for branding and header usage
///
/// USAGE:
///     CombinedLogoWidget(
///       width: 120,
///       height: 135,
///     )
class CombinedLogoWidget extends StatelessWidget {
  /// Overall width of the combined logo widget (optional)
  final double? width;

  /// Overall height of the combined logo widget (optional)
  final double? height;

  /// CombinedLogoWidget constructor
  ///
  /// DESCRIPTION:
  ///     Creates a combined logo widget with optional sizing parameters.
  ///
  /// PARAMETERS:
  ///     width - Overall width of the widget (optional, default: 90)
  ///     height - Overall height of the widget (optional, default: 101.54)
  const CombinedLogoWidget({
    Key? key,
    this.width,
    this.height,
  }) : super(key: key);

  /// build
  ///
  /// DESCRIPTION:
  ///     Builds the combined logo widget with shield and letter logos positioned in a stack.
  ///     Uses proportional sizing to maintain visual balance.
  ///
  /// PARAMETERS:
  ///     context - Build context for the widget
  ///
  /// RETURNS:
  ///     Widget tree representing the combined logo layout
  @override
  Widget build(BuildContext context) {
    final logoWidth = width ?? 90;
    final logoHeight = height ?? 101.54;

    return SizedBox(
      width: logoWidth,
      height: logoHeight,
      child: Stack(
        children: [
          // 盾牌Logo作为背景
          Positioned(
            left: 0,
            top: 0,
            child: DesignSystemSvgs.shieldLogo(
              width: logoWidth * 0.8, // 盾牌Logo占80%宽度
              height: logoHeight * 0.8, // 盾牌Logo占80%高度
            ),
          ),
          // 字母Logo在右上角，不重叠
          Positioned(
            right: 0,
            top: 0,
            child: DesignSystemSvgs.letterLogoLight(
              width: logoWidth * 0.3, // 字母Logo占30%宽度
              height: logoHeight * 0.3, // 字母Logo占30%高度
            ),
          ),
        ],
      ),
    );
  }
}
