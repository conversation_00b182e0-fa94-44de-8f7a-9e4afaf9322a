/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// ******************************************************************************
/// FILE NAME :      responsive_layout.dart
///
/// DESCRIPTION :    Responsive layout manager for iOS/macOS platform adaptation
///
/// AUTHOR :         wei
///
/// HISTORY :        27/06/2025 create

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../utils/design_system.dart';
import '../services/platform/platform_service_factory.dart';



/// ResponsiveLayout
///
/// PURPOSE:
///     A responsive layout manager that adapts the application layout
///     based on platform, screen orientation, and device size.
///     Provides optimal user experience across iOS, macOS, and Windows.
///
/// FEATURES:
///     - iOS: Responsive layout with orientation support
///     - macOS/Windows: Maintains existing desktop layout
///     - Tablet/Phone detection and adaptation
///     - Landscape/Portrait layout switching
///     - Safe area handling for iOS devices
///
/// USAGE:
///     ResponsiveLayout(
///       sidebar: AdaptiveSidebar(...),
///       content: MainContent(...),
///     )
class ResponsiveLayout extends StatelessWidget {
  /// Sidebar widget to display
  final Widget sidebar;

  /// Main content widget to display
  final Widget content;

  /// ResponsiveLayout constructor
  ///
  /// DESCRIPTION:
  ///     Creates a responsive layout that adapts to platform and screen size.
  ///
  /// PARAMETERS:
  ///     sidebar - Sidebar widget (required)
  ///     content - Main content widget (required)
  const ResponsiveLayout({
    Key? key,
    required this.sidebar,
    required this.content,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use desktop layout for desktop platforms and iOS App on macOS
    return FutureBuilder<bool>(
      future: PlatformServiceFactory.shouldUseDesktopLayout(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          // While checking, use synchronous fallback
          final shouldUseDesktop = PlatformServiceFactory.shouldUseDesktopLayoutSync();
          return shouldUseDesktop ? _buildDesktopLayout(context) : _buildIOSLayout(context);
        }

        final shouldUseDesktop = snapshot.data ?? PlatformServiceFactory.shouldUseDesktopLayoutSync();
        return shouldUseDesktop ? _buildDesktopLayout(context) : _buildIOSLayout(context);
      },
    );
  }

  /// Build iOS-specific responsive layout
  Widget _buildIOSLayout(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final orientation = mediaQuery.orientation;
    final isTablet = mediaQuery.size.shortestSide >= LayoutBreakpoints.tabletPortrait;

    return SafeArea(
      child: orientation == Orientation.landscape && isTablet
          ? _buildLandscapeLayout()
          : _buildPortraitLayout(context),
    );
  }

  /// Build desktop layout (macOS/Windows)
  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        sidebar,
        Expanded(child: content),
      ],
    );
  }

  /// Build landscape layout for iOS tablets
  Widget _buildLandscapeLayout() {
    return Row(
      children: [
        // Sidebar is always visible in landscape on tablets
        sidebar,
        Expanded(child: content),
      ],
    );
  }

  /// Build portrait layout for iOS phones and tablets
  Widget _buildPortraitLayout(BuildContext context) {
    return Stack(
      children: [
        // Main content takes full screen
        content,
        
        // Sidebar overlay (handled by AdaptiveSidebar's collapse functionality)
        // The sidebar will be collapsible and overlay when needed
        sidebar,
      ],
    );
  }
}

/// LayoutBreakpoints
///
/// PURPOSE:
///     Defines responsive layout breakpoints for different screen sizes.
///     Used to determine appropriate layout strategies across devices.
///
/// FEATURES:
///     - Mobile portrait/landscape breakpoints
///     - Tablet portrait/landscape breakpoints
///     - Consistent sizing across the application
class LayoutBreakpoints {
  /// Mobile portrait breakpoint (iPhone)
  static const double mobilePortrait = 414;

  /// Mobile landscape breakpoint (iPhone rotated)
  static const double mobileLandscape = 736;

  /// Tablet portrait breakpoint (iPad)
  static const double tabletPortrait = 768;

  /// Tablet landscape breakpoint (iPad rotated)
  static const double tabletLandscape = 1024;

  /// Desktop minimum width
  static const double desktop = 1200;

  /// Check if current screen size is mobile
  static bool isMobile(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return size.shortestSide < tabletPortrait;
  }

  /// Check if current screen size is tablet
  static bool isTablet(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return size.shortestSide >= tabletPortrait && size.shortestSide < desktop;
  }

  /// Check if current screen size is desktop
  static bool isDesktop(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return size.shortestSide >= desktop;
  }

  /// Get appropriate sidebar width for current screen size
  static double getSidebarWidth(BuildContext context) {
    // Use desktop behavior for desktop platforms and iOS App on macOS
    final shouldUseDesktop = PlatformServiceFactory.shouldUseDesktopLayoutSync();
    if (shouldUseDesktop) {
      return DesignSystem.sidebarWidth; // Desktop always full width
    } else if (Platform.isIOS) {
      if (isMobile(context)) {
        return DesignSystem.sidebarCollapsedWidth; // Start collapsed on mobile
      } else {
        return DesignSystem.sidebarWidth; // Full width on tablet
      }
    } else {
      return DesignSystem.sidebarWidth; // Default full width
    }
  }
}

/// OrientationConfig
///
/// PURPOSE:
///     Manages device orientation configuration for different platforms.
///     Provides platform-specific orientation support.
///
/// FEATURES:
///     - iOS: Portrait and landscape support
///     - Desktop: Portrait only (fixed window)
///     - Consistent orientation handling
class OrientationConfig {
  /// Get supported orientations for current platform
  static List<DeviceOrientation> get supportedOrientations {
    if (Platform.isIOS) {
      return [
        DeviceOrientation.portraitUp,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ];
    } else {
      // Desktop platforms use fixed orientation
      return [DeviceOrientation.portraitUp];
    }
  }

  /// Check if landscape orientation is supported
  static bool get supportsLandscape {
    return Platform.isIOS;
  }

  /// Check if current orientation is landscape
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// Check if current orientation is portrait
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }
}
