/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// ******************************************************************************
/// FILE NAME :      enhanced_connection_button.dart
///
/// DESCRIPTION :    Enhanced connection button widget with animations and status indicators
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../utils/constants.dart';
import '../utils/design_system.dart';

/// EnhancedConnectionButton
///
/// PURPOSE:
///     A sophisticated connection button widget that provides visual feedback
///     for different connection states with smooth animations and interactive effects.
///
/// FEATURES:
///     - Multiple connection status support (connected, connecting, disconnecting, error, disconnected)
///     - Pulse animation for connected state
///     - Rotation animation for connecting/disconnecting states
///     - Scale animation for touch feedback
///     - Customizable size and animation settings
///     - Gradient backgrounds and shadow effects
///     - Status-specific icons and colors
///
/// USAGE:
///     EnhancedConnectionButton(
///       status: ConnectionStatus.connected,
///       onPressed: () => handleConnectionToggle(),
///       size: 160.0,
///       showPulseAnimation: true,
///     )
class EnhancedConnectionButton extends StatefulWidget {
  /// Current connection status that determines the button's appearance and behavior
  final ConnectionStatus status;

  /// Callback function triggered when the button is pressed
  final VoidCallback? onPressed;

  /// Size of the button in logical pixels (width and height)
  final double size;

  /// Whether to show pulse animation when connected
  final bool showPulseAnimation;

  /// EnhancedConnectionButton constructor
  ///
  /// DESCRIPTION:
  ///     Creates an enhanced connection button with customizable status, size, and animations.
  ///
  /// PARAMETERS:
  ///     status - Current connection status (required)
  ///     onPressed - Callback for button press events
  ///     size - Button size in logical pixels (default: 160.0)
  ///     showPulseAnimation - Enable pulse animation for connected state (default: true)
  const EnhancedConnectionButton({
    Key? key,
    required this.status,
    this.onPressed,
    this.size = 160.0,
    this.showPulseAnimation = true,
  }) : super(key: key);

  @override
  State<EnhancedConnectionButton> createState() => _EnhancedConnectionButtonState();
}

/// _EnhancedConnectionButtonState
///
/// PURPOSE:
///     State class for EnhancedConnectionButton that manages animations and user interactions.
///     Handles multiple animation controllers for different visual effects.
///
/// FEATURES:
///     - Pulse animation controller for connected state
///     - Rotation animation controller for loading states
///     - Scale animation controller for touch feedback
///     - Automatic animation state management based on connection status
class _EnhancedConnectionButtonState extends State<EnhancedConnectionButton>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late AnimationController _scaleController;

  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  /// initState
  ///
  /// DESCRIPTION:
  ///     Initializes animation controllers and animations for the button.
  ///     Sets up pulse, rotation, and scale animations with appropriate durations and curves.
  @override
  void initState() {
    super.initState();

    // 脉冲动画控制器
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // 旋转动画控制器
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // 缩放动画控制器
    _scaleController = AnimationController(
      duration: DesignSystem.animationMedium,
      vsync: this,
    );

    // 脉冲动画
    _pulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // 旋转动画
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    // 缩放动画
    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: DesignSystem.curveEmphasized,
    ));

    _updateAnimations();
  }

  /// _updateAnimations
  ///
  /// DESCRIPTION:
  ///     Updates animation states based on the current connection status.
  ///     Controls which animations should be running for each status.
  void _updateAnimations() {
    switch (widget.status) {
      case ConnectionStatus.connected:
        if (widget.showPulseAnimation) {
          _pulseController.repeat();
        }
        _rotationController.stop();
        _scaleController.forward();
        break;
      case ConnectionStatus.connecting:
      case ConnectionStatus.disconnecting:
        _pulseController.stop();
        _rotationController.repeat();
        _scaleController.forward();
        break;
      default:
        _pulseController.stop();
        _rotationController.stop();
        _scaleController.forward();
    }
  }

  /// didUpdateWidget
  ///
  /// DESCRIPTION:
  ///     Called when the widget configuration changes.
  ///     Updates animations if the connection status has changed.
  ///
  /// PARAMETERS:
  ///     oldWidget - Previous widget configuration
  @override
  void didUpdateWidget(EnhancedConnectionButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.status != widget.status) {
      _updateAnimations();
    }
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     Cleans up animation controllers to prevent memory leaks.
  ///     Called when the widget is removed from the widget tree.
  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  /// build
  ///
  /// DESCRIPTION:
  ///     Builds the enhanced connection button widget with animations and interactive elements.
  ///     Creates a layered structure with pulse ring, main button, loading ring, and center icon.
  ///
  /// PARAMETERS:
  ///     context - Build context for the widget
  ///
  /// RETURNS:
  ///     Widget tree representing the enhanced connection button
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _pulseController,
        _rotationController,
        _scaleController,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _scaleController.reverse(),
            onTapUp: (_) {
              _scaleController.forward();
              widget.onPressed?.call();
            },
            onTapCancel: () => _scaleController.forward(),
            child: SizedBox(
              width: widget.size,
              height: widget.size,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // 外层脉冲环
                  if (widget.status == ConnectionStatus.connected && widget.showPulseAnimation)
                    _buildPulseRing(),

                  // 主按钮
                  _buildMainButton(),

                  // 旋转加载环
                  if (widget.status == ConnectionStatus.connecting ||
                      widget.status == ConnectionStatus.disconnecting)
                    _buildLoadingRing(),

                  // 中心图标
                  _buildCenterIcon(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// _buildPulseRing
  ///
  /// DESCRIPTION:
  ///     Builds the animated pulse ring that appears around the button when connected.
  ///     Creates an expanding circular border with fading opacity.
  ///
  /// RETURNS:
  ///     Widget representing the pulse ring animation
  Widget _buildPulseRing() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Container(
          width: widget.size + (20 * _pulseAnimation.value),
          height: widget.size + (20 * _pulseAnimation.value),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: _getStatusColor().withValues(alpha: 0.3 * (1 - _pulseAnimation.value)),
              width: 2,
            ),
          ),
        );
      },
    );
  }

  /// _buildMainButton
  ///
  /// DESCRIPTION:
  ///     Builds the main circular button with gradient background and shadow effects.
  ///     Provides the primary visual element of the connection button.
  ///
  /// RETURNS:
  ///     Widget representing the main button container
  Widget _buildMainButton() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: _getStatusGradient(),
        boxShadow: [
          BoxShadow(
            color: _getStatusColor().withValues(alpha: 0.3),
            blurRadius: 20,
            spreadRadius: 2,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Container(
        margin: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white.withValues(alpha: 0.1),
        ),
      ),
    );
  }

  /// _buildLoadingRing
  ///
  /// DESCRIPTION:
  ///     Builds the rotating loading ring that appears during connecting/disconnecting states.
  ///     Creates a circular progress indicator with rotation animation.
  ///
  /// RETURNS:
  ///     Widget representing the loading ring animation
  Widget _buildLoadingRing() {
    return AnimatedBuilder(
      animation: _rotationAnimation,
      builder: (context, child) {
        return Transform.rotate(
          angle: _rotationAnimation.value,
          child: SizedBox(
            width: widget.size + 8,
            height: widget.size + 8,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(
                Colors.white.withValues(alpha: 0.8),
              ),
              backgroundColor: Colors.white.withValues(alpha: 0.2),
            ),
          ),
        );
      },
    );
  }

  /// _buildCenterIcon
  ///
  /// DESCRIPTION:
  ///     Builds the center icon that represents the current connection status.
  ///     Icon changes based on the connection state.
  ///
  /// RETURNS:
  ///     Widget representing the center status icon
  Widget _buildCenterIcon() {
    return Icon(
      _getStatusIcon(),
      size: widget.size * 0.3,
      color: Colors.white,
    );
  }

  /// _getStatusColor
  ///
  /// DESCRIPTION:
  ///     Returns the appropriate color for the current connection status.
  ///     Used for button background, shadows, and other visual elements.
  ///
  /// RETURNS:
  ///     Color corresponding to the current connection status
  Color _getStatusColor() {
    switch (widget.status) {
      case ConnectionStatus.connected:
        return AppColors.connected;
      case ConnectionStatus.connecting:
        return AppColors.connecting;
      case ConnectionStatus.disconnecting:
        return AppColors.connecting;
      case ConnectionStatus.error:
        return AppColors.error;
      default:
        return AppColors.disconnected;
    }
  }

  /// _getStatusGradient
  ///
  /// DESCRIPTION:
  ///     Creates a linear gradient based on the current connection status color.
  ///     Provides a subtle gradient effect for the button background.
  ///
  /// RETURNS:
  ///     LinearGradient with status-appropriate colors
  LinearGradient _getStatusGradient() {
    final color = _getStatusColor();
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        color,
        color.withValues(alpha: 0.8),
      ],
    );
  }

  /// _getStatusIcon
  ///
  /// DESCRIPTION:
  ///     Returns the appropriate icon for the current connection status.
  ///     Each status has a distinct icon to provide clear visual feedback.
  ///
  /// RETURNS:
  ///     IconData corresponding to the current connection status
  IconData _getStatusIcon() {
    switch (widget.status) {
      case ConnectionStatus.connected:
        return Icons.check_rounded;
      case ConnectionStatus.connecting:
        return Icons.link_rounded;
      case ConnectionStatus.disconnecting:
        return Icons.link_off_rounded;
      case ConnectionStatus.error:
        return Icons.error_outline_rounded;
      default:
        return Icons.power_settings_new_rounded;
    }
  }
}
