/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// ******************************************************************************
/// FILE NAME :      app_builder.dart
///
/// DESCRIPTION :    应用构建器，负责构建MaterialApp和处理应用级别的错误状态，
///                  简化主应用Widget的复杂度
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import '../generated/l10n/app_localizations.dart';
import '../core/dependency_injection.dart';
import '../services/language_service.dart';
import '../services/log_service.dart';
import '../services/notification_service.dart';
import '../services/window_close_handler.dart';
import '../screens/login_screen.dart';
import '../screens/main_screen.dart';
import '../widgets/notification_manager.dart';
import '../utils/design_system.dart';
import '../utils/constants.dart';

/// AppBuilder
///
/// PURPOSE:
///     应用构建器，负责构建MaterialApp和处理应用级别的错误状态
///
/// FEATURES:
///     - MaterialApp构建：配置主题、国际化、路由等
///     - 错误处理：处理语言服务初始化和构建错误
///     - 加载状态：显示应用初始化加载界面
///     - 窗口管理：初始化桌面平台的窗口关闭处理
///
/// USAGE:
///     在MyApp的build方法中使用buildApp方法构建应用
class AppBuilder {
  static WindowCloseHandler? _windowCloseHandler;

  /// buildApp
  ///
  /// DESCRIPTION:
  ///     构建完整的MaterialApp，包含错误处理和加载状态
  ///
  /// PARAMETERS:
  ///     context - 构建上下文
  ///     navigatorKey - 全局导航键
  ///
  /// RETURNS:
  ///     Widget - 构建的应用Widget
  static Widget buildApp(BuildContext context, GlobalKey<NavigatorState> navigatorKey) {
    final logService = serviceLocator<LogService>();
    logService.info('App', 'Building MyApp widget');

    return Consumer<LanguageService>(
      builder: (context, languageService, child) {
        try {
          logService.info('App', 'Building MaterialApp with LanguageService, initialized: ${languageService.isInitialized}');

          // 如果语言服务还未初始化，显示加载界面
          if (!languageService.isInitialized) {
            return _buildLoadingApp();
          }
        } catch (e, stackTrace) {
          logService.error('App', 'Error in Consumer builder start: $e', e);
          logService.warning('App', 'Stack trace: $stackTrace');
          return _buildErrorApp('Consumer Error: $e');
        }

        try {
          // 初始化窗口关闭处理器
          _initializeWindowCloseHandler(context);

          // 注册 NavigatorKey 到依赖注入
          DependencyInjection.registerNavigatorKey(navigatorKey);
        } catch (e, stackTrace) {
          logService.error('App', 'Error in initialization: $e', e);
          logService.warning('App', 'Stack trace: $stackTrace');
        }

        try {
          return _buildMaterialApp(navigatorKey, languageService);
        } catch (e, stackTrace) {
          logService.error('App', 'Error creating MaterialApp: $e', e);
          logService.warning('App', 'Stack trace: $stackTrace');
          return _buildErrorApp('App Error: $e');
        }
      },
    );
  }

  /// _buildLoadingApp
  ///
  /// DESCRIPTION:
  ///     构建加载状态的应用界面
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Widget - 加载界面Widget
  static Widget _buildLoadingApp() {
    return MaterialApp(
      title: kAppTitle,
      theme: DesignSystemTheme.createAppTheme(),
      debugShowCheckedModeBanner: false,
      home: const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading...'),
            ],
          ),
        ),
      ),
    );
  }

  /// _buildErrorApp
  ///
  /// DESCRIPTION:
  ///     构建错误状态的应用界面
  ///
  /// PARAMETERS:
  ///     errorMessage - 错误消息
  ///
  /// RETURNS:
  ///     Widget - 错误界面Widget
  static Widget _buildErrorApp(String errorMessage) {
    return MaterialApp(
      title: kAppTitle,
      theme: DesignSystemTheme.createAppTheme(),
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text(errorMessage, style: const TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ),
    );
  }

  /// _buildMaterialApp
  ///
  /// DESCRIPTION:
  ///     构建完整的MaterialApp
  ///
  /// PARAMETERS:
  ///     navigatorKey - 全局导航键
  ///     languageService - 语言服务实例
  ///
  /// RETURNS:
  ///     Widget - MaterialApp Widget
  static Widget _buildMaterialApp(GlobalKey<NavigatorState> navigatorKey, LanguageService languageService) {
    final theme = DesignSystemTheme.createAppTheme();
    final supportedLocales = languageService.getSupportedLocales();

    return MaterialApp(
      navigatorKey: navigatorKey,
      title: kAppTitle,
      theme: theme,
      debugShowCheckedModeBanner: false,
      locale: languageService.locale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: supportedLocales,
      routes: _buildRoutes(),
      initialRoute: '/',
    );
  }

  /// _buildRoutes
  ///
  /// DESCRIPTION:
  ///     构建应用路由配置
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Map<String, WidgetBuilder> - 路由配置映射
  static Map<String, WidgetBuilder> _buildRoutes() {
    return {
      '/': (context) => _buildNotificationWrapper(const LoginScreen()),
      '/main': (context) => _buildNotificationWrapper(const MainScreen()),
    };
  }

  /// _buildNotificationWrapper
  ///
  /// DESCRIPTION:
  ///     为页面包装通知管理器
  ///
  /// PARAMETERS:
  ///     child - 子Widget
  ///
  /// RETURNS:
  ///     Widget - 包装后的Widget
  static Widget _buildNotificationWrapper(Widget child) {
    return NotificationManager(
      notifications: serviceLocator<NotificationService>().notifications,
      duration: const Duration(seconds: 2),
      maxNotifications: 1, // 只显示一个通知，新通知替换旧通知
      child: child,
    );
  }

  /// _initializeWindowCloseHandler
  ///
  /// DESCRIPTION:
  ///     初始化窗口关闭处理器（仅桌面平台）
  ///
  /// PARAMETERS:
  ///     context - 构建上下文
  ///
  /// RETURNS:
  ///     void
  static void _initializeWindowCloseHandler(BuildContext context) {
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      _windowCloseHandler ??= WindowCloseHandler(context: context);
    }
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     清理AppBuilder的资源
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  static void dispose() {
    _windowCloseHandler?.dispose();
    _windowCloseHandler = null;
  }
}
