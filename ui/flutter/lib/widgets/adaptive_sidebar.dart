/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// ******************************************************************************
/// FILE NAME :      adaptive_sidebar.dart
///
/// DESCRIPTION :    Adaptive sidebar widget for iOS/macOS platform compatibility
///
/// AUTHOR :         wei
///
/// HISTORY :        27/06/2025 create

import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/user_info.dart';
import '../utils/design_system.dart';
import '../services/platform/platform_service_factory.dart';
import 'design_system_sidebar.dart';

/// AdaptiveSidebar
///
/// PURPOSE:
///     A platform-adaptive sidebar widget that provides different behaviors
///     for iOS and desktop platforms. On iOS, it supports collapsible functionality
///     for better mobile experience, while maintaining the existing behavior on desktop.
///
/// FEATURES:
///     - iOS: Collapsible sidebar with tap to show/hide functionality
///     - macOS/Windows: Maintains existing desktop navigation behavior
///     - Smooth animations for collapse/expand transitions
///     - Platform-specific interaction patterns
///     - Consistent design system integration
///
/// USAGE:
///     AdaptiveSidebar(
///       selectedIndex: 0,
///       onItemSelected: (index) => handleNavigation(index),
///       userInfo: currentUser,
///       onLogout: () => performLogout(),
///     )
class AdaptiveSidebar extends StatefulWidget {
  /// Currently selected navigation item index
  final int selectedIndex;

  /// Callback function triggered when a navigation item is selected
  final Function(int) onItemSelected;

  /// User information for profile display
  final UserInfo userInfo;

  /// Optional callback for editing user profile
  final VoidCallback? onEditProfile;

  /// Callback function for user logout
  final VoidCallback onLogout;

  /// Whether to enable keyboard tab navigation
  final bool enableTabNavigation;

  /// AdaptiveSidebar constructor
  ///
  /// DESCRIPTION:
  ///     Creates an adaptive sidebar that adjusts behavior based on platform.
  ///
  /// PARAMETERS:
  ///     selectedIndex - Currently selected navigation item index (required)
  ///     onItemSelected - Callback for navigation item selection (required)
  ///     userInfo - User information for profile display (required)
  ///     onEditProfile - Optional callback for profile editing
  ///     onLogout - Callback for user logout (required)
  ///     enableTabNavigation - Enable keyboard navigation (default: true)
  const AdaptiveSidebar({
    Key? key,
    required this.selectedIndex,
    required this.onItemSelected,
    required this.userInfo,
    this.onEditProfile,
    required this.onLogout,
    this.enableTabNavigation = true,
  }) : super(key: key);

  @override
  State<AdaptiveSidebar> createState() => _AdaptiveSidebarState();

  /// Build simplified drawer content for iOS navigation (no user info)
  static Widget buildDrawerContent({
    required BuildContext context,
    required int selectedIndex,
    required Function(int) onItemSelected,
  }) {
    return Container(
      width: 280,
      decoration: const BoxDecoration(
        color: AppColors.sidebarBackground,
      ),
      child: SafeArea(
        child: Column(
          children: [
            // App title/logo area
            Container(
              height: 80,
              padding: const EdgeInsets.all(16),
              child: const Center(
                child: Text(
                  'Panabit iWAN',
                  style: TextStyle(
                    color: AppColors.textLight,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),

            // Navigation items
            Expanded(
              child: _buildNavigationList(context, selectedIndex, onItemSelected),
            ),
          ],
        ),
      ),
    );
  }

  /// Build navigation list for drawer
  static Widget _buildNavigationList(BuildContext context, int selectedIndex, Function(int) onItemSelected) {
    final l10n = AppLocalizations.of(context)!;
    final items = [
      {'icon': Icons.vpn_key, 'title': l10n.connection, 'index': 0},
      {'icon': Icons.person, 'title': l10n.user, 'index': 1},
      {'icon': Icons.analytics, 'title': l10n.statisticsNav, 'index': 2},
      {'icon': Icons.settings, 'title': l10n.settingsNav, 'index': 3},
      {'icon': Icons.description, 'title': l10n.logsNav, 'index': 4},
      {'icon': Icons.info, 'title': l10n.aboutNav, 'index': 5},
    ];

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        final isSelected = selectedIndex == item['index'];

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.sidebarActiveBackground : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: isSelected ? Border.all(color: AppColors.sidebarActiveBorder) : null,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => onItemSelected(item['index'] as int),
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                child: Row(
                  children: [
                    Icon(
                      item['icon'] as IconData,
                      color: isSelected ? AppColors.primary : AppColors.textLight,
                      size: 24,
                    ),
                    const SizedBox(width: 16),
                    Text(
                      item['title'] as String,
                      style: TextStyle(
                        color: isSelected ? AppColors.primary : AppColors.textLight,
                        fontSize: 16,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// _AdaptiveSidebarState
///
/// PURPOSE:
///     State management for AdaptiveSidebar widget.
///     Handles platform-specific behavior and collapse/expand animations.
///
/// FEATURES:
///     - Platform detection and behavior switching
///     - Collapse/expand state management for iOS
///     - Smooth transition animations
///     - Navigation item rendering based on state
class _AdaptiveSidebarState extends State<AdaptiveSidebar> {

  @override
  Widget build(BuildContext context) {
    // Use desktop layout for desktop platforms and iOS App on macOS
    return FutureBuilder<bool>(
      future: PlatformServiceFactory.shouldUseDesktopLayout(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          // While checking, use synchronous fallback
          final shouldUseDesktop = PlatformServiceFactory.shouldUseDesktopLayoutSync();
          return shouldUseDesktop ? _buildDesktopSidebar() : _buildIOSSidebar();
        }

        final shouldUseDesktop = snapshot.data ?? PlatformServiceFactory.shouldUseDesktopLayoutSync();
        return shouldUseDesktop ? _buildDesktopSidebar() : _buildIOSSidebar();
      },
    );
  }

  /// Build iOS-specific sidebar - returns empty container as sidebar is handled by drawer
  Widget _buildIOSSidebar() {
    // On iOS, we don't show a persistent sidebar
    // Instead, navigation is handled through a drawer that can be opened with a menu button
    return const SizedBox.shrink();
  }

  /// Build desktop sidebar (macOS/Windows) with existing behavior
  Widget _buildDesktopSidebar() {
    return DesignSystemSidebar(
      selectedIndex: widget.selectedIndex,
      onItemSelected: widget.onItemSelected,
      userInfo: widget.userInfo,
      onEditProfile: widget.onEditProfile,
      onLogout: widget.onLogout,
      enableTabNavigation: widget.enableTabNavigation,
    );
  }
}
