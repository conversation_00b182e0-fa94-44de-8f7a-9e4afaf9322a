/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// ******************************************************************************
/// FILE NAME :      design_system_sidebar.dart
///
/// DESCRIPTION :    Design system sidebar widget with navigation menu and user profile
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'package:flutter/material.dart';
import '../models/user_info.dart';
import '../utils/design_system.dart';
// import '../core/dependency_injection.dart';
// import '../services/log_service.dart';
import '../generated/l10n/app_localizations.dart';

/// DesignSystemSidebar
///
/// PURPOSE:
///     A navigation sidebar widget that provides application navigation
///     with design system styling and internationalization support.
///
/// FEATURES:
///     - Navigation menu with icons and labels
///     - Selected state management with visual feedback
///     - Internationalization support with language-specific styling
///     - User profile integration
///     - Keyboard navigation support
///     - Responsive design with consistent spacing
///
/// USAGE:
///     DesignSystemSidebar(
///       selectedIndex: 0,
///       onItemSelected: (index) => handleNavigation(index),
///       userInfo: currentUser,
///       onLogout: () => performLogout(),
///     )
class DesignSystemSidebar extends StatefulWidget {
  /// Currently selected navigation item index
  final int selectedIndex;

  /// Callback function triggered when a navigation item is selected
  final Function(int) onItemSelected;

  /// User information for profile display
  final UserInfo userInfo;

  /// Optional callback for editing user profile
  final VoidCallback? onEditProfile;

  /// Callback function for user logout
  final VoidCallback onLogout;

  /// Whether to enable keyboard tab navigation
  final bool enableTabNavigation;

  /// DesignSystemSidebar constructor
  ///
  /// DESCRIPTION:
  ///     Creates a design system sidebar with navigation menu and user profile.
  ///
  /// PARAMETERS:
  ///     selectedIndex - Currently selected navigation item index (required)
  ///     onItemSelected - Callback for navigation item selection (required)
  ///     userInfo - User information for profile display (required)
  ///     onEditProfile - Optional callback for profile editing
  ///     onLogout - Callback for user logout (required)
  ///     enableTabNavigation - Enable keyboard navigation (default: true)
  const DesignSystemSidebar({
    Key? key,
    required this.selectedIndex,
    required this.onItemSelected,
    required this.userInfo,
    this.onEditProfile,
    required this.onLogout,
    this.enableTabNavigation = true,
  }) : super(key: key);

  @override
  State<DesignSystemSidebar> createState() => _DesignSystemSidebarState();
}

/// _DesignSystemSidebarState
///
/// PURPOSE:
///     State class for DesignSystemSidebar that manages the sidebar's UI and interactions.
///     Handles navigation menu rendering and user interactions.
///
/// FEATURES:
///     - Navigation menu construction
///     - Language-aware text styling
///     - Interactive navigation items with hover effects
class _DesignSystemSidebarState extends State<DesignSystemSidebar> {

  /// build
  ///
  /// DESCRIPTION:
  ///     Builds the sidebar widget with navigation menu and design system styling.
  ///     Creates a fixed-width sidebar with border and background styling.
  ///
  /// PARAMETERS:
  ///     context - Build context for the widget
  ///
  /// RETURNS:
  ///     Widget tree representing the design system sidebar
  @override
  Widget build(BuildContext context) {
    return Container(
      width: DesignSystem.sidebarWidth,
      decoration: const BoxDecoration(
        color: AppColors.sidebarBackground,
        border: Border(
          right: BorderSide(
            color: AppColors.sidebarBorder,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // 导航菜单
          Expanded(
            child: _buildNavigationMenu(),
          ),
        ],
      ),
    );
  }

  /// _buildNavigationMenu
  ///
  /// DESCRIPTION:
  ///     Builds the navigation menu with all available navigation items.
  ///     Uses internationalization for labels and provides consistent styling.
  ///
  /// RETURNS:
  ///     Widget containing the navigation menu items
  Widget _buildNavigationMenu() {
    final l10n = AppLocalizations.of(context)!;
    return ListView(
      padding: EdgeInsets.zero,
      children: [
        // 连接 (主页)
        _buildNavItem(
          index: 0,
          icon: Icons.link,
          label: l10n.connection,
          isSelected: widget.selectedIndex == 0,
        ),

        // 用户
        _buildNavItem(
          index: 1,
          icon: Icons.person,
          label: l10n.user,
          isSelected: widget.selectedIndex == 1,
        ),

        // 统计
        _buildNavItem(
          index: 2,
          icon: Icons.trending_up,
          label: l10n.statisticsNav,
          isSelected: widget.selectedIndex == 2,
        ),

        // 设置
        _buildNavItem(
          index: 3,
          icon: Icons.settings,
          label: l10n.settingsNav,
          isSelected: widget.selectedIndex == 3,
        ),

        // 日志
        _buildNavItem(
          index: 4,
          icon: Icons.description,
          label: l10n.logsNav,
          isSelected: widget.selectedIndex == 4,
        ),

        // 关于
        _buildNavItem(
          index: 5,
          icon: Icons.info,
          label: l10n.aboutNav,
          isSelected: widget.selectedIndex == 5,
        ),
      ],
    );
  }

  /// _buildNavItem
  ///
  /// DESCRIPTION:
  ///     Builds a single navigation item with icon, label, and interactive effects.
  ///     Provides visual feedback for selection state and hover interactions.
  ///
  /// PARAMETERS:
  ///     index - Navigation item index
  ///     icon - Icon to display
  ///     label - Text label for the item
  ///     isSelected - Whether this item is currently selected
  ///
  /// RETURNS:
  ///     Widget representing a navigation item
  Widget _buildNavItem({
    required int index,
    required IconData icon,
    required String label,
    required bool isSelected,
  }) {
    return Container(
      height: DesignSystem.sidebarItemHeight,
      decoration: BoxDecoration(
        color: isSelected ? AppColors.sidebarActiveBackground : Colors.transparent,
        border: Border(
          right: BorderSide(
            color: isSelected ? AppColors.sidebarActiveBorder : Colors.transparent,
            width: 4,
          ),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            try {
              // final logService = serviceLocator<LogService>();
              // logService.debug('Sidebar', 'Navigate to: $label (index: $index)');
              widget.onItemSelected(index);
            } catch (e) {
              // 如果日志服务不可用，仍然执行导航
              widget.onItemSelected(index);
            }
          },
          splashColor: AppColors.primary.withValues(alpha: 0.1),
          highlightColor: AppColors.primary.withValues(alpha: 0.05),
          canRequestFocus: widget.enableTabNavigation,
          focusColor: widget.enableTabNavigation ? AppColors.primary.withValues(alpha: 0.1) : Colors.transparent,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: DesignSystem.spacing42,
              vertical: DesignSystem.spacing24,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 图标容器
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                    color: isSelected
                      ? AppColors.primary.withValues(alpha: 0.1)
                      : Colors.transparent,
                  ),
                  child: Icon(
                    icon,
                    color: isSelected
                      ? AppColors.primary
                      : AppColors.textLight,
                    size: 20,
                  ),
                ),

                const SizedBox(height: DesignSystem.spacing8),

                // 标签
                _buildNavLabel(label, isSelected),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// _buildNavLabel
  ///
  /// DESCRIPTION:
  ///     Builds the navigation label with language-specific font sizing and styling.
  ///     Adjusts font size, line height, and letter spacing based on the current locale.
  ///
  /// PARAMETERS:
  ///     label - Text label to display
  ///     isSelected - Whether this navigation item is currently selected
  ///
  /// RETURNS:
  ///     Widget representing the styled navigation label
  Widget _buildNavLabel(String label, bool isSelected) {
    final isEnglish = Localizations.localeOf(context).languageCode == 'en';

    return Text(
      label,
      style: DesignSystem.bodyMedium.copyWith(
        color: AppColors.textLight,
        fontSize: isEnglish ? 12 : 14, // 英文使用12px，中文使用14px
        fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
        height: isEnglish ? 1.1 : 1.43, // 调整行高以适应不同字体大小
        letterSpacing: isEnglish ? -0.1 : 0, // 英文字母间距稍微紧凑
      ),
      textAlign: TextAlign.center,
      maxLines: 1, // 强制所有语言都使用单行
      overflow: TextOverflow.visible, // 确保英文文本完全可见
    );
  }

}

/// SidebarNavItem
///
/// PURPOSE:
///     Data model representing a single navigation item in the sidebar.
///     Contains the necessary information for rendering navigation items.
///
/// FEATURES:
///     - Index for navigation routing
///     - Icon for visual representation
///     - Label for text display
///
/// USAGE:
///     const SidebarNavItem(
///       index: 0,
///       icon: Icons.home,
///       label: 'Home',
///     )
class SidebarNavItem {
  /// Navigation item index for routing
  final int index;

  /// Icon to display for this navigation item
  final IconData icon;

  /// Text label for this navigation item
  final String label;

  /// SidebarNavItem constructor
  ///
  /// DESCRIPTION:
  ///     Creates a navigation item with index, icon, and label.
  ///
  /// PARAMETERS:
  ///     index - Navigation item index (required)
  ///     icon - Icon to display (required)
  ///     label - Text label (required)
  const SidebarNavItem({
    required this.index,
    required this.icon,
    required this.label,
  });
}

/// SidebarNavItems
///
/// PURPOSE:
///     Static collection of predefined navigation items for the sidebar.
///     Provides default navigation structure with icons and placeholder labels.
///
/// FEATURES:
///     - Predefined navigation items with consistent structure
///     - Static data for application navigation
///     - Placeholder labels that should be replaced with localized text
///
/// USAGE:
///     final items = SidebarNavItems.items;
///
/// NOTE:
///     The labels in this class are placeholders and should be replaced
///     with localized text from AppLocalizations in actual usage.
class SidebarNavItems {
  static const List<SidebarNavItem> items = [
    SidebarNavItem(
      index: 0,
      icon: Icons.link,
      label: 'Connection', // 这些将被动态替换
    ),
    SidebarNavItem(
      index: 1,
      icon: Icons.person,
      label: 'User',
    ),
    SidebarNavItem(
      index: 2,
      icon: Icons.trending_up,
      label: 'Statistics',
    ),
    SidebarNavItem(
      index: 3,
      icon: Icons.settings,
      label: 'Settings',
    ),
    SidebarNavItem(
      index: 4,
      icon: Icons.description,
      label: 'Logs',
    ),
    SidebarNavItem(
      index: 5,
      icon: Icons.info,
      label: 'About',
    ),
  ];
}
