/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// ******************************************************************************
/// FILE NAME :      safe_area_wrapper.dart
///
/// DESCRIPTION :    Safe area wrapper for iOS platform adaptation
///
/// AUTHOR :         wei
///
/// HISTORY :        27/06/2025 create

import 'dart:io';
import 'package:flutter/material.dart';
import '../utils/design_system.dart';
import '../services/platform/platform_service_factory.dart';


/// SafeAreaWrapper
///
/// PURPOSE:
///     A platform-adaptive safe area wrapper that handles iOS-specific
///     safe area requirements while maintaining compatibility with desktop platforms.
///     Ensures content is properly positioned around device notches, status bars,
///     and other system UI elements.
///
/// FEATURES:
///     - iOS: Automatic safe area handling for notches and system UI
///     - macOS/Windows: No safe area constraints (desktop behavior)
///     - Configurable safe area edges
///     - Consistent cross-platform behavior
///
/// USAGE:
///     SafeAreaWrapper(
///       child: MyContent(),
///       top: true,
///       bottom: true,
///     )
class SafeAreaWrapper extends StatelessWidget {
  /// Child widget to wrap with safe area
  final Widget child;

  /// Whether to apply safe area to the top edge
  final bool top;

  /// Whether to apply safe area to the bottom edge
  final bool bottom;

  /// Whether to apply safe area to the left edge
  final bool left;

  /// Whether to apply safe area to the right edge
  final bool right;

  /// Minimum safe area padding (fallback for older devices)
  final EdgeInsets minimum;

  /// Background color for iOS safe area extension (optional)
  final Color? backgroundColor;

  /// SafeAreaWrapper constructor
  ///
  /// DESCRIPTION:
  ///     Creates a safe area wrapper that adapts to platform requirements.
  ///
  /// PARAMETERS:
  ///     child - Widget to wrap (required)
  ///     top - Apply safe area to top edge (default: true)
  ///     bottom - Apply safe area to bottom edge (default: true)
  ///     left - Apply safe area to left edge (default: true)
  ///     right - Apply safe area to right edge (default: true)
  ///     minimum - Minimum padding for fallback (default: EdgeInsets.zero)
  ///     backgroundColor - Background color for iOS safe area (optional, uses gradient if null)
  const SafeAreaWrapper({
    Key? key,
    required this.child,
    this.top = true,
    this.bottom = true,
    this.left = true,
    this.right = true,
    this.minimum = EdgeInsets.zero,
    this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use desktop layout for desktop platforms and iOS App on macOS
    return FutureBuilder<bool>(
      future: PlatformServiceFactory.shouldUseDesktopLayout(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          // While checking, use synchronous fallback
          final shouldUseDesktop = PlatformServiceFactory.shouldUseDesktopLayoutSync();
          return shouldUseDesktop ? _buildDesktopWrapper() : _buildIOSSafeArea(context);
        }

        final shouldUseDesktop = snapshot.data ?? PlatformServiceFactory.shouldUseDesktopLayoutSync();
        return shouldUseDesktop ? _buildDesktopWrapper() : _buildIOSSafeArea(context);
      },
    );
  }

  /// Build iOS safe area that extends content background to edges
  Widget _buildIOSSafeArea(BuildContext context) {
    // For iOS, extend background to full screen but don't apply SafeArea constraints
    // This allows content to use the full screen while maintaining proper background
    return Container(
      decoration: backgroundColor != null
          ? BoxDecoration(color: backgroundColor)
          : const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: AppColors.backgroundGradient,
              ),
            ),
      child: child, // No SafeArea wrapper - content uses full screen
    );
  }

  /// Build desktop wrapper (no safe area constraints)
  Widget _buildDesktopWrapper() {
    return child;
  }
}

/// SafeAreaManager
///
/// PURPOSE:
///     Utility class for managing safe area information and calculations.
///     Provides helper methods for safe area-aware layout calculations.
///
/// FEATURES:
///     - Safe area padding calculations
///     - Platform-specific safe area detection
///     - Layout helper methods
class SafeAreaManager {
  /// Get safe area padding for current context
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    if (Platform.isIOS) {
      return MediaQuery.of(context).padding;
    } else {
      return EdgeInsets.zero;
    }
  }

  /// Get safe area insets for current context
  static EdgeInsets getSafeAreaInsets(BuildContext context) {
    if (Platform.isIOS) {
      final mediaQuery = MediaQuery.of(context);
      return EdgeInsets.only(
        top: mediaQuery.padding.top,
        bottom: mediaQuery.padding.bottom + mediaQuery.viewInsets.bottom,
        left: mediaQuery.padding.left,
        right: mediaQuery.padding.right,
      );
    } else {
      return EdgeInsets.zero;
    }
  }

  /// Check if device has a notch or dynamic island
  static bool hasNotch(BuildContext context) {
    if (Platform.isIOS) {
      final padding = MediaQuery.of(context).padding;
      return padding.top > 20; // Standard status bar height is 20
    } else {
      return false;
    }
  }

  /// Check if device has home indicator
  static bool hasHomeIndicator(BuildContext context) {
    if (Platform.isIOS) {
      final padding = MediaQuery.of(context).padding;
      return padding.bottom > 0;
    } else {
      return false;
    }
  }

  /// Get available screen height excluding safe areas
  static double getAvailableHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    if (Platform.isIOS) {
      return mediaQuery.size.height - 
             mediaQuery.padding.top - 
             mediaQuery.padding.bottom;
    } else {
      return mediaQuery.size.height;
    }
  }

  /// Get available screen width excluding safe areas
  static double getAvailableWidth(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    if (Platform.isIOS) {
      return mediaQuery.size.width - 
             mediaQuery.padding.left - 
             mediaQuery.padding.right;
    } else {
      return mediaQuery.size.width;
    }
  }

  /// Calculate content padding that respects safe areas
  static EdgeInsets getContentPadding(
    BuildContext context, {
    double horizontal = 16.0,
    double vertical = 16.0,
  }) {
    if (Platform.isIOS) {
      final safeArea = getSafeAreaPadding(context);
      return EdgeInsets.only(
        top: vertical + safeArea.top,
        bottom: vertical + safeArea.bottom,
        left: horizontal + safeArea.left,
        right: horizontal + safeArea.right,
      );
    } else {
      return EdgeInsets.symmetric(
        horizontal: horizontal,
        vertical: vertical,
      );
    }
  }
}

/// AdaptiveScaffold
///
/// PURPOSE:
///     A scaffold wrapper that automatically handles safe areas and
///     platform-specific layout requirements.
///
/// FEATURES:
///     - Automatic safe area handling
///     - Platform-specific behavior
///     - Consistent scaffold configuration
class AdaptiveScaffold extends StatelessWidget {
  /// Scaffold body widget
  final Widget body;

  /// App bar widget (optional)
  final PreferredSizeWidget? appBar;

  /// Background color
  final Color? backgroundColor;

  /// Whether to resize body when keyboard appears
  final bool resizeToAvoidBottomInset;

  /// AdaptiveScaffold constructor
  const AdaptiveScaffold({
    Key? key,
    required this.body,
    this.appBar,
    this.backgroundColor,
    this.resizeToAvoidBottomInset = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar,
      backgroundColor: backgroundColor,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      body: SafeAreaWrapper(
        child: body,
      ),
    );
  }
}
