// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      notification_manager.dart
//  *
//  * DESCRIPTION :    通知管理组件，提供通知显示和管理功能，包括通知卡片组件
//  *                  和通知管理器组件，支持动画效果和自动消失
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  ******************************************************************************/

import 'dart:async';
import 'package:flutter/material.dart';

import '../services/notification_service.dart';
// import '../services/log_service.dart';
import '../services/font_service.dart';
import '../services/platform/platform_service_factory.dart';
import '../core/dependency_injection.dart';
import '../utils/design_system.dart';
import '../utils/constants.dart';


/// NotificationWidget
///
/// PURPOSE:
///     A widget that displays individual notification messages with
///     type-specific styling, icons, and dismiss functionality.
///
/// FEATURES:
///     - Type-specific color coding and icons
///     - Language-aware font styling
///     - Dismiss button with callback
///     - Card-based design with elevation
///     - Safe service access with fallback
///     - Status-aware styling for connection states
///     - Swipe-up gesture for dismissal with smooth animation
///
/// USAGE:
///     NotificationWidget(
///       notification: notificationInfo,
///       onDismiss: () => dismissNotification(),
///     )
class NotificationWidget extends StatefulWidget {
  /// Notification information to display
  final NotificationInfo notification;

  /// Callback function when notification is dismissed
  final VoidCallback onDismiss;

  /// NotificationWidget constructor
  ///
  /// DESCRIPTION:
  ///     Creates a notification widget with the provided notification data and dismiss callback.
  ///
  /// PARAMETERS:
  ///     notification - NotificationInfo object containing notification details
  ///     onDismiss - Callback function triggered when user dismisses the notification
  const NotificationWidget({
    Key? key,
    required this.notification,
    required this.onDismiss,
  }) : super(key: key);

  @override
  State<NotificationWidget> createState() => _NotificationWidgetState();
}

/// _NotificationWidgetState
///
/// PURPOSE:
///     State class for NotificationWidget that handles swipe gestures and animations
///
/// FEATURES:
///     - Swipe-up gesture detection for dismissal
///     - Smooth animation for swipe dismissal
///     - Velocity-based gesture recognition
///     - Automatic animation cleanup
class _NotificationWidgetState extends State<NotificationWidget>
    with SingleTickerProviderStateMixin {

  /// Animation controller for swipe dismissal animation
  late AnimationController _swipeController;

  /// Animation for vertical translation during swipe
  late Animation<double> _swipeAnimation;

  /// Animation for opacity fade during swipe
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize swipe animation controller
    _swipeController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );

    // Create swipe animation for vertical movement
    _swipeAnimation = Tween<double>(
      begin: 0.0,
      end: -100.0, // Move up by 100 pixels
    ).animate(CurvedAnimation(
      parent: _swipeController,
      curve: Curves.easeInCubic,
    ));

    // Create fade animation for opacity
    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _swipeController,
      curve: Curves.easeInCubic,
    ));
  }

  @override
  void dispose() {
    _swipeController.dispose();
    super.dispose();
  }

  /// _getTextStyle
  ///
  /// DESCRIPTION:
  ///     Creates a text style for notification messages with language-aware font
  ///     selection and primary color theming.
  ///
  /// RETURNS:
  ///     TextStyle object with appropriate font and color settings
  TextStyle _getTextStyle() {
    try {
      final fontService = serviceLocator<FontService>();
      return fontService.createTextStyleWithMappedWeight(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: AppColors.primary,
      );
    } catch (e) {
      return const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: AppColors.primary,
        fontFamilyFallback: ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'],
      );
    }
  }

  /// _handleSwipeUp
  ///
  /// DESCRIPTION:
  ///     Handles the swipe-up gesture by triggering dismissal animation
  ///     and calling the onDismiss callback when animation completes.
  void _handleSwipeUp() {
    // Start the swipe animation
    _swipeController.forward().then((_) {
      // Call the dismiss callback when animation completes
      if (mounted) {
        widget.onDismiss();
      }
    });
  }

  /// build
  ///
  /// DESCRIPTION:
  ///     Builds the notification widget with swipe gesture support and animations.
  ///     Provides visual feedback based on notification type and connection status.
  ///
  /// PARAMETERS:
  ///     context - Build context for the widget
  ///
  /// RETURNS:
  ///     Widget tree representing the notification card with gesture support
  @override
  Widget build(BuildContext context) {
    // 安全获取LogService实例
    try {
      // final logService = serviceLocator<LogService>();
      // logService.debug('NotificationUI', 'Building notification: ${widget.notification.type} - ${widget.notification.message}');
    } catch (e) {
      // LogService获取失败，忽略日志记录
    }

    // 根据通知类型选择背景颜色和图标（保持原有的颜色逻辑）
    Color backgroundColor;
    IconData icon;

    switch (widget.notification.type) {
      case NotificationType.status:
        if (widget.notification.status == ConnectionStatus.connected) {
          backgroundColor = AppColors.connected;
          icon = Icons.check_circle;
        } else if (widget.notification.status == ConnectionStatus.connecting) {
          backgroundColor = AppColors.connecting;
          icon = Icons.sync;
        } else if (widget.notification.status == ConnectionStatus.error) {
          backgroundColor = AppColors.error;
          icon = Icons.error;
        } else {
          backgroundColor = AppColors.disconnected;
          icon = Icons.info;
        }
        break;
      case NotificationType.error:
        backgroundColor = AppColors.error;
        icon = Icons.error;
        break;
      case NotificationType.warning:
        backgroundColor = AppColors.connecting;
        icon = Icons.warning;
        break;
      case NotificationType.success:
        backgroundColor = AppColors.connected;
        icon = Icons.check_circle;
        break;
      case NotificationType.info:
        backgroundColor = AppColors.primary;
        icon = Icons.info;
        break;
    }

    // Wrap the notification card with gesture detection and animation
    return AnimatedBuilder(
      animation: _swipeController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _swipeAnimation.value),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: GestureDetector(
              onPanUpdate: (details) {
                // Detect upward swipe gesture
                if (details.delta.dy < -2) { // Swipe up threshold
                  _handleSwipeUp();
                }
              },
              onPanEnd: (details) {
                // Handle swipe velocity for more responsive gesture
                if (details.velocity.pixelsPerSecond.dy < -500) { // Velocity threshold
                  _handleSwipeUp();
                }
              },
              child: Card(
                margin: const EdgeInsets.only(bottom: 8),
                elevation: 2,
                color: Colors.white.withValues(alpha: 0.95), // 白色背景，95% 不透明度，完全遮挡背景
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(color: backgroundColor.withValues(alpha: 0.3), width: 1), // 保持状态相关的边框颜色
                ),
                child: ListTile(
                  leading: Icon(icon, color: backgroundColor, size: 24), // 图标保持原有颜色
                  title: Text(
                    widget.notification.message,
                    style: _getTextStyle(), // 文本使用橙色（在_getTextStyle中设置）
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.close, color: AppColors.textSecondary), // 关闭按钮恢复原色
                    onPressed: widget.onDismiss,
                    iconSize: 20,
                    tooltip: 'Close notification', // 添加工具提示
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  dense: false, // 确保有足够的空间
                ),
              ),
            ),
          ),
        );
      },
    );
  }


}

/// NotificationManager
///
/// PURPOSE:
///     A widget that manages and displays notifications over the main content.
///     Provides animated notification display with automatic dismissal and overlay positioning.
///
/// FEATURES:
///     - Stream-based notification listening
///     - Animated notification appearance and dismissal
///     - Automatic notification replacement (latest replaces previous)
///     - Configurable display duration
///     - Positioned overlay display avoiding sidebar and title bar
///     - Safe service access with error handling
///     - Memory management for animations and timers
///
/// USAGE:
///     NotificationManager(
///       child: mainContent,
///       notifications: notificationStream,
///       duration: const Duration(seconds: 3),
///     )
class NotificationManager extends StatefulWidget {
  /// Child widget to display under the notification overlay
  final Widget child;

  /// Stream of notifications to display
  final Stream<NotificationInfo> notifications;

  /// Duration to display each notification before auto-dismissal
  final Duration duration;

  /// Maximum number of notifications to display simultaneously (currently unused)
  final int maxNotifications;

  /// NotificationManager constructor
  ///
  /// DESCRIPTION:
  ///     Creates a notification manager with the provided child widget and notification stream.
  ///
  /// PARAMETERS:
  ///     child - Widget to display as the main content
  ///     notifications - Stream of NotificationInfo objects to display
  ///     duration - How long to display each notification (default: 2 seconds)
  ///     maxNotifications - Maximum concurrent notifications (default: 3, currently unused)
  const NotificationManager({
    Key? key,
    required this.child,
    required this.notifications,
    this.duration = const Duration(seconds: 2),
    this.maxNotifications = 3,
  }) : super(key: key);

  @override
  State<NotificationManager> createState() => _NotificationManagerState();
}

/// _NotificationManagerState
///
/// PURPOSE:
///     State management for NotificationManager widget.
///     Handles notification stream subscription, animation management, and UI updates.
///
/// FEATURES:
///     - Stream subscription management
///     - Animation controller lifecycle management
///     - Timer-based auto-dismissal
///     - Safe service access with error handling
///     - Memory leak prevention through proper disposal
class _NotificationManagerState extends State<NotificationManager> with TickerProviderStateMixin {
  /// List of currently active notifications with their animation controllers
  final List<_NotificationEntry> _activeNotifications = [];

  /// Subscription to the notification stream
  StreamSubscription? _subscription;

  /// initState
  ///
  /// DESCRIPTION:
  ///     Initializes the notification manager state and sets up stream subscription.
  ///     Uses microtask to safely access services after widget initialization.
  @override
  void initState() {
    super.initState();

    // 延迟获取LogService，使用serviceLocator避免Provider依赖
    Future.microtask(() {
      try {
        // final logService = serviceLocator<LogService>();
        // logService.debug('NotificationUI', 'Initializing notification manager, starting to listen to notification stream');

        _subscription = widget.notifications.listen((notification) {
          // logService.debug('NotificationUI', 'Received notification from stream: ${notification.type} - ${notification.message}');
          _handleNotification(notification);
        });

        // logService.debug('NotificationUI', 'Notification stream listener has been set up');
      } catch (e) {
        // 即使LogService失败，也要设置通知监听
        _subscription = widget.notifications.listen((notification) {
          _handleNotification(notification);
        });
      }
    });
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     Cleans up resources when the widget is disposed.
  ///     Cancels stream subscription, timers, and disposes animation controllers.
  @override
  void dispose() {
    _subscription?.cancel();
    // 清理所有活动通知的计时器和动画控制器
    for (var entry in _activeNotifications) {
      entry.timer?.cancel();
      entry.controller.dispose();
    }
    _activeNotifications.clear();
    super.dispose();
  }

  /// _handleNotification
  ///
  /// DESCRIPTION:
  ///     Processes a new notification by removing existing ones and displaying the new one.
  ///     Creates animation controller and sets up auto-dismissal timer.
  ///
  /// PARAMETERS:
  ///     notification - NotificationInfo object to display
  void _handleNotification(NotificationInfo notification) {
    // LogService? logService;
    try {
      // logService = serviceLocator<LogService>();
      // logService.debug('NotificationUI', 'Processing notification: ${notification.type} - ${notification.message}');
    } catch (e) {
      // 获取LogService失败，忽略
    }

    // 移除所有现有通知（实现替换而非叠加）
    if (_activeNotifications.isNotEmpty) {
      try {
        // logService?.debug('NotificationUI', 'Removing existing notifications to show new notification');
      } catch (e) {
        // 日志记录失败，忽略
      }
      // 立即移除所有现有通知
      for (final entry in List.from(_activeNotifications)) {
        _removeNotificationImmediately(entry);
      }
    }

    // 创建动画控制器
    final controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 创建通知条目
    final entry = _NotificationEntry(
      notification: notification,
      controller: controller,
    );

    // 添加新通知
    setState(() {
      _activeNotifications.add(entry);
      try {
        // logService?.debug('NotificationUI', 'Added new notification, current count: ${_activeNotifications.length}');
      } catch (e) {
        // 日志记录失败，忽略
      }
    });

    // 播放动画
    controller.forward();

    // 设置自动移除计时器
    entry.timer = Timer(widget.duration, () {
      _removeNotification(entry);
    });
  }

  /// _removeNotification
  ///
  /// DESCRIPTION:
  ///     Removes a notification with animation. Cancels timer and plays reverse animation
  ///     before removing from the active list.
  ///
  /// PARAMETERS:
  ///     entry - _NotificationEntry to remove
  void _removeNotification(_NotificationEntry entry) {
    // 如果通知已经被移除，不做任何操作
    if (!_activeNotifications.contains(entry)) {
      return;
    }

    // 取消计时器
    entry.timer?.cancel();

    // 反向播放动画，然后移除通知
    entry.controller.reverse().then((_) {
      if (mounted) {
        setState(() {
          _activeNotifications.remove(entry);
        });
      }
      entry.controller.dispose();
    });
  }

  /// _removeNotificationImmediately
  ///
  /// DESCRIPTION:
  ///     Removes a notification immediately without animation. Used when replacing
  ///     notifications to avoid visual conflicts.
  ///
  /// PARAMETERS:
  ///     entry - _NotificationEntry to remove immediately
  void _removeNotificationImmediately(_NotificationEntry entry) {
    // 如果通知已经被移除，不做任何操作
    if (!_activeNotifications.contains(entry)) {
      return;
    }

    // 取消计时器
    entry.timer?.cancel();

    // 立即移除通知，不播放动画
    if (mounted) {
      setState(() {
        _activeNotifications.remove(entry);
      });
    }
    entry.controller.dispose();
  }

  /// build
  ///
  /// DESCRIPTION:
  ///     Builds the notification manager widget with overlay positioning.
  ///     Displays notifications above the main content with proper positioning
  ///     to avoid sidebar and title bar areas.
  ///
  /// PARAMETERS:
  ///     context - Build context for the widget
  ///
  /// RETURNS:
  ///     Widget tree with main content and notification overlay
  @override
  Widget build(BuildContext context) {
    // 判断是否为桌面平台（有侧边栏）- 包括 iOS App on macOS
    final bool isDesktop = PlatformServiceFactory.shouldUseDesktopLayoutSync();

    return Stack(
      children: [
        widget.child,
        if (_activeNotifications.isNotEmpty)
          isDesktop
            ? _buildDesktopNotification() // 桌面端：基于主内容区域（除侧边栏外）居中
            : _buildMobileNotification(), // 移动端：基于整个屏幕居中
      ],
    );
  }

  /// 构建桌面端通知（基于主内容区域居中）
  Widget _buildDesktopNotification() {
    return Positioned(
      top: 16,
      left: DesignSystem.sidebarWidth + 16, // 侧边栏宽度 + 边距
      right: 16,
      child: Align(
        alignment: Alignment.topCenter,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400), // 限制最大宽度
          child: Material(
            color: Colors.transparent,
            child: AnimatedBuilder(
              animation: _activeNotifications.first.controller,
              builder: (context, child) {
                final animation = CurvedAnimation(
                  parent: _activeNotifications.first.controller,
                  curve: Curves.easeOutCubic,
                );
                return FadeTransition(
                  opacity: animation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, -0.5),
                      end: Offset.zero,
                    ).animate(animation),
                    child: child,
                  ),
                );
              },
              child: NotificationWidget(
                notification: _activeNotifications.first.notification,
                onDismiss: () => _removeNotification(_activeNotifications.first),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建移动端通知（基于整个屏幕居中）
  Widget _buildMobileNotification() {
    return Positioned.fill(
      child: Align(
        alignment: Alignment.topCenter,
        child: Container(
          margin: const EdgeInsets.only(top: 60, left: 16, right: 16), // iOS 状态栏 + 边距
          constraints: const BoxConstraints(maxWidth: 400), // 限制最大宽度
          child: Material(
            color: Colors.transparent,
            child: AnimatedBuilder(
              animation: _activeNotifications.first.controller,
              builder: (context, child) {
                final animation = CurvedAnimation(
                  parent: _activeNotifications.first.controller,
                  curve: Curves.easeOutCubic,
                );
                return FadeTransition(
                  opacity: animation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, -0.5),
                      end: Offset.zero,
                    ).animate(animation),
                    child: child,
                  ),
                );
              },
              child: NotificationWidget(
                notification: _activeNotifications.first.notification,
                onDismiss: () => _removeNotification(_activeNotifications.first),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// _NotificationEntry
///
/// PURPOSE:
///     Internal data class that holds notification information along with
///     its associated animation controller and auto-dismissal timer.
///
/// FEATURES:
///     - Notification data storage
///     - Animation controller management
///     - Timer for auto-dismissal
///     - Resource cleanup support
class _NotificationEntry {
  /// The notification information to display
  final NotificationInfo notification;

  /// Animation controller for entrance/exit animations
  final AnimationController controller;

  /// Timer for automatic notification dismissal
  Timer? timer;

  /// _NotificationEntry constructor
  ///
  /// DESCRIPTION:
  ///     Creates a notification entry with the provided notification and animation controller.
  ///
  /// PARAMETERS:
  ///     notification - NotificationInfo object containing notification details
  ///     controller - AnimationController for managing notification animations
  _NotificationEntry({
    required this.notification,
    required this.controller,
  });
}
