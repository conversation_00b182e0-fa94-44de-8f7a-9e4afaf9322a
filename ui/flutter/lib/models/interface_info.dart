/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      interface_info.dart
 *
 * DESCRIPTION :    Network interface information data model that represents
 *                  VPN interface configuration including local IP, TUN IP,
 *                  and interface name with timestamp information.
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/



/// InterfaceInfo Data Model
///
/// PURPOSE:
///     Represents network interface information for VPN connections including
///     local IP address, TUN interface IP, and interface name with timestamp
///
/// FEATURES:
///     - Network interface name identification
///     - Local IP address information
///     - TUN interface IP address for VPN tunnel
///     - Timestamp for information collection time
///     - JSON serialization/deserialization support
///     - Immutable design for thread safety
///     - Factory constructor for empty/default state
///     - Safe timestamp handling with fallback to current time
///     - Nullable fields for optional network information
///
/// USAGE:
///     // Create from JSON
///     final interfaceInfo = InterfaceInfo.fromJson(jsonData);
///
///     // Create empty interface info
///     final emptyInfo = InterfaceInfo.empty();
///
///     // Access interface data
///     final hasValidIp = interfaceInfo.hasValidLocalIp;
class InterfaceInfo {
  /// Interface Information Constants
  static const int kMillisecondsPerSecond = 1000;

  /// Network interface name (e.g., 'eth0', 'wlan0')
  final String? interfaceName;

  /// Local IP address of the interface
  final String? localIp;

  /// TUN interface IP address for VPN tunnel
  final String? tunIp;

  /// Timestamp when this interface information was collected
  final DateTime timestamp;

  /// InterfaceInfo Constructor
  ///
  /// DESCRIPTION:
  ///     Creates a new InterfaceInfo instance with the specified network data
  ///
  /// PARAMETERS:
  ///     interfaceName - Network interface name (optional)
  ///     localIp - Local IP address (optional)
  ///     tunIp - TUN interface IP address (optional)
  ///     timestamp - When info was collected (default: current time)
  InterfaceInfo({
    this.interfaceName,
    this.localIp,
    this.tunIp,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  /// fromJson Factory Constructor
  ///
  /// DESCRIPTION:
  ///     Creates an InterfaceInfo instance from JSON data with safe type
  ///     conversion and timestamp handling
  ///
  /// PARAMETERS:
  ///     json - Map containing interface information data from JSON
  ///
  /// RETURNS:
  ///     InterfaceInfo instance with data populated from JSON
  factory InterfaceInfo.fromJson(Map<String, dynamic> json) {
    return InterfaceInfo(
      interfaceName: _parseStringSafely(json['interface_name']),
      localIp: _parseStringSafely(json['local_ip']),
      tunIp: _parseStringSafely(json['tun_ip']),
      timestamp: _parseTimestampSafely(json['timestamp']),
    );
  }

  /// toJson Serialization Method
  ///
  /// DESCRIPTION:
  ///     Converts InterfaceInfo instance to JSON-compatible Map for serialization
  ///
  /// RETURNS:
  ///     Map containing all interface information data in JSON format
  Map<String, dynamic> toJson() {
    return {
      'interface_name': interfaceName,
      'local_ip': localIp,
      'tun_ip': tunIp,
      'timestamp': timestamp.millisecondsSinceEpoch ~/ kMillisecondsPerSecond,
    };
  }

  /// empty Factory Constructor
  ///
  /// DESCRIPTION:
  ///     Creates an empty InterfaceInfo instance for initialization or
  ///     when no interface information is available
  ///
  /// RETURNS:
  ///     InterfaceInfo instance with all null values and current timestamp
  factory InterfaceInfo.empty() {
    return InterfaceInfo();
  }

  /// Safe string parsing helper
  ///
  /// DESCRIPTION:
  ///     Safely parses dynamic value to string with null handling and trimming
  ///
  /// PARAMETERS:
  ///     value - Dynamic value to parse
  ///
  /// RETURNS:
  ///     Parsed and trimmed string or null if value is null/empty
  static String? _parseStringSafely(dynamic value) {
    if (value == null) return null;
    final stringValue = value.toString().trim();
    return stringValue.isEmpty ? null : stringValue;
  }

  /// Safe timestamp parsing helper
  ///
  /// DESCRIPTION:
  ///     Safely parses timestamp from JSON with fallback to current time
  ///
  /// PARAMETERS:
  ///     value - Dynamic timestamp value (usually int seconds)
  ///
  /// RETURNS:
  ///     DateTime instance from timestamp or current time if parsing fails
  static DateTime _parseTimestampSafely(dynamic value) {
    if (value == null) return DateTime.now();

    try {
      if (value is int) {
        return DateTime.fromMillisecondsSinceEpoch(value * kMillisecondsPerSecond);
      }
      if (value is String) {
        final intValue = int.tryParse(value);
        if (intValue != null) {
          return DateTime.fromMillisecondsSinceEpoch(intValue * kMillisecondsPerSecond);
        }
      }
    } catch (e) {
      // If parsing fails, return current time
    }

    return DateTime.now();
  }

  /// toString Override
  ///
  /// DESCRIPTION:
  ///     Provides a string representation of the InterfaceInfo for debugging
  ///
  /// RETURNS:
  ///     String representation including interface name and IP addresses
  @override
  String toString() {
    return 'InterfaceInfo(interface: $interfaceName, localIp: $localIp, tunIp: $tunIp, time: ${timestamp.toIso8601String()})';
  }

  /// Equality operator override
  ///
  /// DESCRIPTION:
  ///     Compares two InterfaceInfo instances for equality
  ///
  /// PARAMETERS:
  ///     other - Object to compare with
  ///
  /// RETURNS:
  ///     true if all fields are equal, false otherwise
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InterfaceInfo &&
        other.interfaceName == interfaceName &&
        other.localIp == localIp &&
        other.tunIp == tunIp &&
        other.timestamp == timestamp;
  }

  /// Hash code override
  ///
  /// DESCRIPTION:
  ///     Generates hash code for use in collections
  ///
  /// RETURNS:
  ///     Hash code based on all fields
  @override
  int get hashCode {
    return Object.hash(interfaceName, localIp, tunIp, timestamp);
  }

  /// hasValidLocalIp Getter
  ///
  /// DESCRIPTION:
  ///     Checks if the interface has a valid local IP address
  ///
  /// RETURNS:
  ///     true if local IP is not null and not empty, false otherwise
  bool get hasValidLocalIp => localIp != null && localIp!.isNotEmpty;

  /// hasValidTunIp Getter
  ///
  /// DESCRIPTION:
  ///     Checks if the interface has a valid TUN IP address
  ///
  /// RETURNS:
  ///     true if TUN IP is not null and not empty, false otherwise
  bool get hasValidTunIp => tunIp != null && tunIp!.isNotEmpty;

  /// hasValidInterfaceName Getter
  ///
  /// DESCRIPTION:
  ///     Checks if the interface has a valid interface name
  ///
  /// RETURNS:
  ///     true if interface name is not null and not empty, false otherwise
  bool get hasValidInterfaceName => interfaceName != null && interfaceName!.isNotEmpty;

  /// isComplete Getter
  ///
  /// DESCRIPTION:
  ///     Checks if the interface information is complete (has all required fields)
  ///
  /// RETURNS:
  ///     true if all fields are valid, false otherwise
  bool get isComplete => hasValidInterfaceName && hasValidLocalIp && hasValidTunIp;

  /// isEmpty Getter
  ///
  /// DESCRIPTION:
  ///     Checks if the interface information is empty (no valid fields)
  ///
  /// RETURNS:
  ///     true if all fields are null or empty, false otherwise
  bool get isEmpty => !hasValidInterfaceName && !hasValidLocalIp && !hasValidTunIp;
}
