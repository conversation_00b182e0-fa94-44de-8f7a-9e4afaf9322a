/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      connection_status.dart
 *
 * DESCRIPTION :    Connection status model compatibility layer that re-exports
 *                  ConnectionStatus enum from constants.dart for backward
 *                  compatibility while maintaining clean architecture.
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

/// ConnectionStatus Compatibility Layer
///
/// PURPOSE:
///     Provides backward compatibility for ConnectionStatus enum imports
///     while maintaining clean architecture by re-exporting from constants.dart
///
/// FEATURES:
///     - Re-exports ConnectionStatus enum from utils/constants.dart
///     - Maintains backward compatibility for existing imports
///     - Supports clean architecture by centralizing enum definitions
///     - Provides migration path for legacy code
///
/// USAGE:
///     import '../models/connection_status.dart';
///
///     ConnectionStatus status = ConnectionStatus.connected;
///
/// MIGRATION NOTE:
///     New code should import directly from '../utils/constants.dart'
///     This file exists for backward compatibility only

// Re-export ConnectionStatus enum to maintain compatibility
export '../utils/constants.dart' show ConnectionStatus;
