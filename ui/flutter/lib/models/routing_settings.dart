/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      routing_settings.dart
///
/// DESCRIPTION :    Routing settings data model that manages VPN routing
///                  configuration including routing modes, custom routes,
///                  and auto-start settings with change notification.
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'package:flutter/foundation.dart';

/// RoutingMode Enumeration
///
/// PURPOSE:
///     Defines the available VPN routing modes for traffic management
///
/// VALUES:
///     all - Route all traffic through VPN (全部路由)
///     custom - Route only specified networks through VPN (按网段路由)
enum RoutingMode {
  /// Route all traffic through VPN
  all,

  /// Route only custom-specified networks through VPN
  custom,
}

/// RoutingSettingsModel Data Model
///
/// PURPOSE:
///     Manages VPN routing configuration settings with change notification
///     support for reactive UI updates and state persistence
///
/// FEATURES:
///     - Routing mode selection (all traffic vs custom networks)
///     - Custom route configuration for selective routing
///     - Auto-start functionality for automatic VPN connection
///     - Loading state management for UI feedback
///     - Change notification support via ChangeNotifier
///     - JSON serialization/deserialization for persistence
///     - Safe state updates with change detection
///
/// USAGE:
///     // Create and configure routing settings
///     final settings = RoutingSettingsModel();
///     settings.updateSettings(mode: RoutingMode.custom, customRoutes: '192.168.1.0/24');
///
///     // Listen to changes
///     settings.addListener(() => print('Settings changed'));
class RoutingSettingsModel extends ChangeNotifier {
  /// Routing Settings Constants
  static const String kEmptyRoutes = '';
  static const String kModeAll = 'all';
  static const String kModeCustom = 'custom';
  static const String kCustomRoutesKey = 'custom_routes';
  static const String kAutoStartKey = 'auto_start';
  static const String kModeKey = 'mode';

  /// Current routing mode
  RoutingMode _mode = RoutingMode.all;

  /// Custom routes configuration (CIDR notation, comma-separated)
  String _customRoutes = kEmptyRoutes;

  /// Loading state for UI feedback
  bool _isLoading = false;

  /// Auto-start VPN connection setting
  bool _autoStart = false;

  /// mode Getter
  ///
  /// DESCRIPTION:
  ///     Returns the current routing mode
  ///
  /// RETURNS:
  ///     Current RoutingMode value
  RoutingMode get mode => _mode;

  /// customRoutes Getter
  ///
  /// DESCRIPTION:
  ///     Returns the custom routes configuration string
  ///
  /// RETURNS:
  ///     Custom routes in CIDR notation, comma-separated
  String get customRoutes => _customRoutes;

  /// isLoading Getter
  ///
  /// DESCRIPTION:
  ///     Returns the current loading state
  ///
  /// RETURNS:
  ///     true if currently loading, false otherwise
  bool get isLoading => _isLoading;

  /// autoStart Getter
  ///
  /// DESCRIPTION:
  ///     Returns the auto-start setting
  ///
  /// RETURNS:
  ///     true if auto-start is enabled, false otherwise
  bool get autoStart => _autoStart;

  /// autoStart Setter
  ///
  /// DESCRIPTION:
  ///     Sets the auto-start setting and notifies listeners if changed
  ///
  /// PARAMETERS:
  ///     value - New auto-start setting value
  set autoStart(bool value) {
    if (_autoStart != value) {
      _autoStart = value;
      notifyListeners();
    }
  }

  /// updateSettings Method
  ///
  /// DESCRIPTION:
  ///     Updates routing settings and notifies listeners if any changes
  ///     were made, providing reactive state management
  ///
  /// PARAMETERS:
  ///     mode - New routing mode (optional)
  ///     customRoutes - New custom routes configuration (optional)
  ///     autoStart - New auto-start setting (optional)
  void updateSettings({
    RoutingMode? mode,
    String? customRoutes,
    bool? autoStart,
  }) {
    bool hasChanged = false;

    if (mode != null && _mode != mode) {
      _mode = mode;
      hasChanged = true;
    }

    if (customRoutes != null && _customRoutes != customRoutes) {
      _customRoutes = _sanitizeCustomRoutes(customRoutes);
      hasChanged = true;
    }

    if (autoStart != null && _autoStart != autoStart) {
      _autoStart = autoStart;
      hasChanged = true;
    }

    if (hasChanged) {
      notifyListeners();
    }
  }

  /// setLoading Method
  ///
  /// DESCRIPTION:
  ///     Sets the loading state and notifies listeners if changed
  ///
  /// PARAMETERS:
  ///     isLoading - New loading state
  void setLoading(bool isLoading) {
    if (_isLoading != isLoading) {
      _isLoading = isLoading;
      notifyListeners();
    }
  }

  /// Custom routes sanitization helper
  ///
  /// DESCRIPTION:
  ///     Sanitizes custom routes input by trimming whitespace
  ///
  /// PARAMETERS:
  ///     routes - Raw custom routes string
  ///
  /// RETURNS:
  ///     Sanitized custom routes string
  String _sanitizeCustomRoutes(String routes) {
    return routes.trim();
  }

  /// fromJson Method
  ///
  /// DESCRIPTION:
  ///     Updates routing settings from JSON data with safe type conversion
  ///
  /// PARAMETERS:
  ///     json - Map containing routing settings data from JSON
  void fromJson(Map<String, dynamic> json) {
    updateSettings(
      mode: _parseRoutingMode(json[kModeKey]),
      customRoutes: _parseStringSafely(json[kCustomRoutesKey]) ?? kEmptyRoutes,
      autoStart: _parseBoolSafely(json[kAutoStartKey]) ?? false,
    );
  }

  /// toJson Serialization Method
  ///
  /// DESCRIPTION:
  ///     Converts routing settings to JSON-compatible Map for serialization
  ///
  /// RETURNS:
  ///     Map containing all routing settings data in JSON format
  Map<String, dynamic> toJson() {
    return {
      kModeKey: _mode == RoutingMode.custom ? kModeCustom : kModeAll,
      kCustomRoutesKey: _customRoutes,
      kAutoStartKey: _autoStart,
    };
  }

  /// Safe routing mode parsing helper
  ///
  /// DESCRIPTION:
  ///     Safely parses routing mode from dynamic value
  ///
  /// PARAMETERS:
  ///     value - Dynamic value to parse
  ///
  /// RETURNS:
  ///     Parsed RoutingMode or default (all) if parsing fails
  RoutingMode _parseRoutingMode(dynamic value) {
    if (value == null) return RoutingMode.all;
    final stringValue = value.toString().toLowerCase();
    return stringValue == kModeCustom ? RoutingMode.custom : RoutingMode.all;
  }

  /// Safe string parsing helper
  ///
  /// DESCRIPTION:
  ///     Safely parses dynamic value to string with null handling
  ///
  /// PARAMETERS:
  ///     value - Dynamic value to parse
  ///
  /// RETURNS:
  ///     Parsed string or null if value is null
  static String? _parseStringSafely(dynamic value) {
    if (value == null) return null;
    return value.toString().trim();
  }

  /// Safe boolean parsing helper
  ///
  /// DESCRIPTION:
  ///     Safely parses dynamic value to boolean with null handling
  ///
  /// PARAMETERS:
  ///     value - Dynamic value to parse
  ///
  /// RETURNS:
  ///     Parsed boolean or null if parsing fails
  static bool? _parseBoolSafely(dynamic value) {
    if (value == null) return null;
    if (value is bool) return value;
    if (value is String) {
      return value.toLowerCase() == 'true' || value == '1';
    }
    if (value is int) return value != 0;
    return null;
  }

  /// toString Override
  ///
  /// DESCRIPTION:
  ///     Provides a string representation of the routing settings for debugging
  ///
  /// RETURNS:
  ///     String representation including mode, routes, and auto-start
  @override
  String toString() {
    return 'RoutingSettingsModel(mode: $_mode, customRoutes: $_customRoutes, autoStart: $_autoStart, isLoading: $_isLoading)';
  }

  /// isCustomMode Getter
  ///
  /// DESCRIPTION:
  ///     Checks if the current mode is custom routing
  ///
  /// RETURNS:
  ///     true if mode is custom, false otherwise
  bool get isCustomMode => _mode == RoutingMode.custom;

  /// hasCustomRoutes Getter
  ///
  /// DESCRIPTION:
  ///     Checks if custom routes are configured
  ///
  /// RETURNS:
  ///     true if custom routes are not empty, false otherwise
  bool get hasCustomRoutes => _customRoutes.isNotEmpty;

  /// isValidConfiguration Getter
  ///
  /// DESCRIPTION:
  ///     Checks if the current configuration is valid
  ///
  /// RETURNS:
  ///     true if configuration is valid (custom mode requires routes), false otherwise
  bool get isValidConfiguration {
    if (_mode == RoutingMode.custom) {
      return _customRoutes.isNotEmpty;
    }
    return true; // All mode is always valid
  }

  /// reset Method
  ///
  /// DESCRIPTION:
  ///     Resets all settings to default values
  void reset() {
    updateSettings(
      mode: RoutingMode.all,
      customRoutes: kEmptyRoutes,
      autoStart: false,
    );
  }
}
