/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      server.dart
 *
 * DESCRIPTION :    Server information data model that represents VPN server
 *                  configuration, status, and metadata with support for
 *                  internationalization and JSON serialization.
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

/// Server Data Model
///
/// PURPOSE:
///     Represents a VPN server with all necessary configuration and status
///     information, supporting internationalization and JSON serialization
///
/// FEATURES:
///     - Server identification and naming (with i18n support)
///     - Network configuration (hostname, port)
///     - Connection status and ping information
///     - Auto-selection and default server flags
///     - JSON serialization/deserialization
///     - Immutable design with copyWith support
///     - Language-aware display name methods
///     - Equality comparison based on server ID
///
/// USAGE:
///     // Create from JSON
///     final server = Server.fromJson(jsonData);
///
///     // Get localized name
///     final displayName = server.getDisplayName('en');
///
///     // Update server status
///     final updatedServer = server.copyWith(status: 'connected');
class Server {
  /// Server Constants
  static const String kDefaultStatus = 'disconnected';
  static const int kInvalidPing = -1;
  static const int kDefaultPort = 0;

  /// Unique server identifier
  final String id;

  /// Server display name in default language (usually Chinese)
  final String name;

  /// Server display name in English
  final String nameEn;

  /// Server hostname or IP address
  final String serverName;

  /// Server port number
  final int serverPort;

  /// Current ping latency in milliseconds (-1 if unknown)
  int ping;

  /// Whether this server supports auto-selection
  final bool isAuto;

  /// Current connection status
  String status;

  /// Whether this is the default server
  bool isDefault;

  /// Server Constructor
  ///
  /// DESCRIPTION:
  ///     Creates a new Server instance with the specified configuration
  ///
  /// PARAMETERS:
  ///     id - Unique server identifier
  ///     name - Server display name in default language
  ///     nameEn - Server display name in English
  ///     serverName - Server hostname or IP address
  ///     serverPort - Server port number
  ///     ping - Current ping latency (default: -1)
  ///     isAuto - Auto-selection support flag (default: false)
  ///     status - Connection status (default: 'disconnected')
  ///     isDefault - Default server flag (default: false)
  Server({
    required this.id,
    required this.name,
    required this.nameEn,
    required this.serverName,
    required this.serverPort,
    this.ping = kInvalidPing,
    this.isAuto = false,
    this.status = kDefaultStatus,
    this.isDefault = false,
  });

  /// fromJson Factory Constructor
  ///
  /// DESCRIPTION:
  ///     Creates a Server instance from JSON data with safe type conversion
  ///     and default value handling
  ///
  /// PARAMETERS:
  ///     json - Map containing server data from JSON
  ///
  /// RETURNS:
  ///     Server instance with data populated from JSON
  factory Server.fromJson(Map<String, dynamic> json) {
    return Server(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      nameEn: json['name_en']?.toString() ?? '',
      serverName: json['server_name']?.toString() ?? '',
      serverPort: _parseIntSafely(json['server_port']) ?? kDefaultPort,
      ping: _parseIntSafely(json['ping']) ?? kInvalidPing,
      isAuto: _parseBoolSafely(json['isauto']) ?? false,
      status: json['status']?.toString() ?? kDefaultStatus,
      isDefault: _parseBoolSafely(json['isdefault']) ?? false,
    );
  }

  /// empty Factory Constructor
  ///
  /// DESCRIPTION:
  ///     Creates an empty Server instance for use in error cases or
  ///     when no server is available
  ///
  /// RETURNS:
  ///     Empty Server instance with default values
  factory Server.empty() {
    return Server(
      id: '',
      name: '',
      nameEn: '',
      serverName: '',
      serverPort: kDefaultPort,
      ping: kInvalidPing,
      isAuto: false,
      status: kDefaultStatus,
      isDefault: false,
    );
  }

  /// Safe integer parsing helper
  ///
  /// DESCRIPTION:
  ///     Safely parses dynamic value to integer with null handling
  ///
  /// PARAMETERS:
  ///     value - Dynamic value to parse
  ///
  /// RETURNS:
  ///     Parsed integer or null if parsing fails
  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) return int.tryParse(value);
    return null;
  }

  /// Safe boolean parsing helper
  ///
  /// DESCRIPTION:
  ///     Safely parses dynamic value to boolean with null handling
  ///
  /// PARAMETERS:
  ///     value - Dynamic value to parse
  ///
  /// RETURNS:
  ///     Parsed boolean or null if parsing fails
  static bool? _parseBoolSafely(dynamic value) {
    if (value == null) return null;
    if (value is bool) return value;
    if (value is String) {
      return value.toLowerCase() == 'true' || value == '1';
    }
    if (value is int) return value != 0;
    return null;
  }

  /// toJson Serialization Method
  ///
  /// DESCRIPTION:
  ///     Converts Server instance to JSON-compatible Map for serialization
  ///
  /// RETURNS:
  ///     Map containing all server data in JSON format
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'name_en': nameEn,
      'server_name': serverName,
      'server_port': serverPort,
      'ping': ping,
      'isauto': isAuto,
      'status': status,
      'isdefault': isDefault,
    };
  }

  /// copyWith Method
  ///
  /// DESCRIPTION:
  ///     Creates a new Server instance with specified fields updated,
  ///     maintaining immutability while allowing selective updates
  ///
  /// PARAMETERS:
  ///     id - New server ID (optional)
  ///     name - New server name (optional)
  ///     nameEn - New English name (optional)
  ///     serverName - New hostname (optional)
  ///     serverPort - New port number (optional)
  ///     ping - New ping value (optional)
  ///     isAuto - New auto-selection flag (optional)
  ///     status - New connection status (optional)
  ///     isDefault - New default flag (optional)
  ///
  /// RETURNS:
  ///     New Server instance with updated fields
  Server copyWith({
    String? id,
    String? name,
    String? nameEn,
    String? serverName,
    int? serverPort,
    int? ping,
    bool? isAuto,
    String? status,
    bool? isDefault,
  }) {
    return Server(
      id: id ?? this.id,
      name: name ?? this.name,
      nameEn: nameEn ?? this.nameEn,
      serverName: serverName ?? this.serverName,
      serverPort: serverPort ?? this.serverPort,
      ping: ping ?? this.ping,
      isAuto: isAuto ?? this.isAuto,
      status: status ?? this.status,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  /// Equality Operator Override
  ///
  /// DESCRIPTION:
  ///     Compares two Server instances for equality based on server ID
  ///
  /// PARAMETERS:
  ///     other - Object to compare with
  ///
  /// RETURNS:
  ///     true if servers have the same ID, false otherwise
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Server && other.id == id;
  }

  /// Hash Code Override
  ///
  /// DESCRIPTION:
  ///     Generates hash code based on server ID for use in collections
  ///
  /// RETURNS:
  ///     Hash code based on server ID
  @override
  int get hashCode => id.hashCode;

  /// getDisplayName Method
  ///
  /// DESCRIPTION:
  ///     Returns the appropriate display name based on language code,
  ///     supporting internationalization requirements
  ///
  /// PARAMETERS:
  ///     languageCode - Language code (e.g., 'en', 'zh')
  ///
  /// RETURNS:
  ///     Localized server display name
  String getDisplayName(String languageCode) {
    if (languageCode == 'en' && nameEn.isNotEmpty) {
      return nameEn;
    }
    return name;
  }

  /// getDisplayNameByLocale Method
  ///
  /// DESCRIPTION:
  ///     Returns the appropriate display name based on locale preference,
  ///     providing a boolean-based alternative to getDisplayName
  ///
  /// PARAMETERS:
  ///     isEnglish - true for English locale, false for default locale
  ///
  /// RETURNS:
  ///     Localized server display name
  String getDisplayNameByLocale(bool isEnglish) {
    if (isEnglish && nameEn.isNotEmpty) {
      return nameEn;
    }
    return name;
  }

  /// toString Override
  ///
  /// DESCRIPTION:
  ///     Provides a string representation of the Server for debugging
  ///
  /// RETURNS:
  ///     String representation including ID and display name
  @override
  String toString() {
    return 'Server(id: $id, name: $name, serverName: $serverName:$serverPort, status: $status)';
  }

  /// isValid Getter
  ///
  /// DESCRIPTION:
  ///     Checks if the server has valid configuration data
  ///
  /// RETURNS:
  ///     true if server has valid ID and server name, false otherwise
  bool get isValid {
    return id.isNotEmpty && serverName.isNotEmpty && serverPort > 0;
  }

  /// isConnected Getter
  ///
  /// DESCRIPTION:
  ///     Checks if the server is currently connected
  ///
  /// RETURNS:
  ///     true if server status is 'connected', false otherwise
  bool get isConnected {
    return status == 'connected';
  }

  /// hasValidPing Getter
  ///
  /// DESCRIPTION:
  ///     Checks if the server has a valid ping measurement
  ///
  /// RETURNS:
  ///     true if ping is greater than or equal to 0, false otherwise
  bool get hasValidPing {
    return ping >= 0;
  }
}
