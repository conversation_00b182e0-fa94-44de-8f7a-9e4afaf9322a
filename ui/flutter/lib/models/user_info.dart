/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      user_info.dart
///
/// DESCRIPTION :    User information data model that represents user profile
///                  data including display name, department, and position
///                  with change notification support for UI updates.
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'package:flutter/foundation.dart';

/// UserInfo Data Model
///
/// PURPOSE:
///     Represents user profile information with change notification support
///     for reactive UI updates and state management
///
/// FEATURES:
///     - User profile data (display name, department, position)
///     - Change notification support via ChangeNotifier
///     - JSON serialization/deserialization with backward compatibility
///     - Immutable copyWith method for state updates
///     - Mutable update method with change detection
///     - Backward compatibility with legacy 'name' field
///     - Safe field validation and sanitization
///
/// USAGE:
///     // Create from JSON
///     final userInfo = UserInfo.fromJson(jsonData);
///
///     // Listen to changes
///     userInfo.addListener(() => print('User info changed'));
///
///     // Update user info
///     userInfo.update(displayName: 'New Name');
class UserInfo extends ChangeNotifier {
  /// User Information Constants
  static const String kEmptyString = '';
  static const String kLegacyNameField = 'name';
  static const String kDisplayNameField = 'displayName';

  /// User's display name (personal name)
  String displayName;

  /// User's department or organizational unit
  String department;

  /// User's job position or title
  String position;

  /// UserInfo Constructor
  ///
  /// DESCRIPTION:
  ///     Creates a new UserInfo instance with the specified user data
  ///
  /// PARAMETERS:
  ///     displayName - User's display name (default: empty string)
  ///     department - User's department (default: empty string)
  ///     position - User's position (default: empty string)
  UserInfo({
    this.displayName = kEmptyString,
    this.department = kEmptyString,
    this.position = kEmptyString,
  });

  /// fromJson Factory Constructor
  ///
  /// DESCRIPTION:
  ///     Creates a UserInfo instance from JSON data with backward compatibility
  ///     for legacy 'name' field and safe string conversion
  ///
  /// PARAMETERS:
  ///     json - Map containing user information data from JSON
  ///
  /// RETURNS:
  ///     UserInfo instance with data populated from JSON
  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      displayName: _parseStringSafely(json[kDisplayNameField]) ??
                   _parseStringSafely(json[kLegacyNameField]) ??
                   kEmptyString,
      department: _parseStringSafely(json['department']) ?? kEmptyString,
      position: _parseStringSafely(json['position']) ?? kEmptyString,
    );
  }

  /// toJson Serialization Method
  ///
  /// DESCRIPTION:
  ///     Converts UserInfo instance to JSON-compatible Map for serialization
  ///
  /// RETURNS:
  ///     Map containing all user information data in JSON format
  Map<String, dynamic> toJson() {
    return {
      kDisplayNameField: displayName,
      'department': department,
      'position': position,
    };
  }

  /// Safe string parsing helper
  ///
  /// DESCRIPTION:
  ///     Safely parses dynamic value to string with null handling
  ///
  /// PARAMETERS:
  ///     value - Dynamic value to parse
  ///
  /// RETURNS:
  ///     Parsed string or null if value is null
  static String? _parseStringSafely(dynamic value) {
    if (value == null) return null;
    return value.toString().trim();
  }

  /// update Method
  ///
  /// DESCRIPTION:
  ///     Updates user information fields and notifies listeners if any
  ///     changes were made, providing reactive state management
  ///
  /// PARAMETERS:
  ///     displayName - New display name (optional)
  ///     department - New department (optional)
  ///     position - New position (optional)
  void update({
    String? displayName,
    String? department,
    String? position,
  }) {
    bool hasChanged = false;

    if (displayName != null && this.displayName != displayName) {
      this.displayName = _sanitizeString(displayName);
      hasChanged = true;
    }

    if (department != null && this.department != department) {
      this.department = _sanitizeString(department);
      hasChanged = true;
    }

    if (position != null && this.position != position) {
      this.position = _sanitizeString(position);
      hasChanged = true;
    }

    if (hasChanged) {
      notifyListeners();
    }
  }

  /// copyWith Method
  ///
  /// DESCRIPTION:
  ///     Creates a new UserInfo instance with specified fields updated,
  ///     maintaining immutability while allowing selective updates
  ///
  /// PARAMETERS:
  ///     displayName - New display name (optional)
  ///     department - New department (optional)
  ///     position - New position (optional)
  ///
  /// RETURNS:
  ///     New UserInfo instance with updated fields
  UserInfo copyWith({
    String? displayName,
    String? department,
    String? position,
  }) {
    return UserInfo(
      displayName: displayName ?? this.displayName,
      department: department ?? this.department,
      position: position ?? this.position,
    );
  }

  /// String sanitization helper
  ///
  /// DESCRIPTION:
  ///     Sanitizes input strings by trimming whitespace and handling null
  ///
  /// PARAMETERS:
  ///     value - String value to sanitize
  ///
  /// RETURNS:
  ///     Sanitized string or empty string if null
  String _sanitizeString(String? value) {
    return value?.trim() ?? kEmptyString;
  }

  /// Legacy name getter for backward compatibility
  ///
  /// DESCRIPTION:
  ///     Provides backward compatibility for legacy code using 'name' field
  ///
  /// RETURNS:
  ///     Current display name value
  String get name => displayName;

  /// Legacy name setter for backward compatibility
  ///
  /// DESCRIPTION:
  ///     Provides backward compatibility for legacy code setting 'name' field
  ///
  /// PARAMETERS:
  ///     value - New name value to set
  set name(String value) {
    if (displayName != value) {
      displayName = _sanitizeString(value);
      notifyListeners();
    }
  }

  /// toString Override
  ///
  /// DESCRIPTION:
  ///     Provides a string representation of the UserInfo for debugging
  ///
  /// RETURNS:
  ///     String representation including all user fields
  @override
  String toString() {
    return 'UserInfo(displayName: $displayName, department: $department, position: $position)';
  }

  /// isEmpty Getter
  ///
  /// DESCRIPTION:
  ///     Checks if all user information fields are empty
  ///
  /// RETURNS:
  ///     true if all fields are empty, false otherwise
  bool get isEmpty {
    return displayName.isEmpty && department.isEmpty && position.isEmpty;
  }

  /// isValid Getter
  ///
  /// DESCRIPTION:
  ///     Checks if user information has at least a display name
  ///
  /// RETURNS:
  ///     true if display name is not empty, false otherwise
  bool get isValid {
    return displayName.isNotEmpty;
  }

  /// fullInfo Getter
  ///
  /// DESCRIPTION:
  ///     Returns a formatted string with all available user information
  ///
  /// RETURNS:
  ///     Formatted string combining name, department, and position
  String get fullInfo {
    final parts = <String>[];

    if (displayName.isNotEmpty) parts.add(displayName);
    if (department.isNotEmpty) parts.add(department);
    if (position.isNotEmpty) parts.add(position);

    return parts.join(' - ');
  }
}
