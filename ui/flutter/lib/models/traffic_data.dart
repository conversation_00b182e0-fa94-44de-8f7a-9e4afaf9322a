/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      traffic_data.dart
 *
 * DESCRIPTION :    Traffic data model that represents network traffic
 *                  information including bytes transferred and current
 *                  speeds with timestamp for data visualization.
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

/// TrafficData Data Model
///
/// PURPOSE:
///     Represents network traffic data including cumulative bytes transferred
///     and current transfer speeds with timestamp for data visualization
///
/// FEATURES:
///     - Cumulative upload and download byte counts
///     - Current upload and download speeds (bytes per second)
///     - Timestamp for data collection time
///     - JSON serialization/deserialization support
///     - Immutable design with copyWith support
///     - Computed properties for total bytes and speed
///     - Static empty instance for initialization
///     - Safe JSON parsing with fallback values
///     - Equality comparison and hash code support
///
/// USAGE:
///     // Create traffic data
///     final data = TrafficData(
///       uploadBytes: 1024,
///       downloadBytes: 2048,
///       uploadSpeed: 100,
///       downloadSpeed: 200
///     );
///
///     // Create from JSON
///     final data = TrafficData.fromJson(jsonData);
///
///     // Get total traffic
///     final total = data.totalBytes;
class TrafficData {
  /// Traffic Data Constants
  static const int kZeroBytes = 0;
  static const int kZeroSpeed = 0;

  /// Total bytes uploaded since connection start
  final int uploadBytes;

  /// Total bytes downloaded since connection start
  final int downloadBytes;

  /// Current upload speed in bytes per second
  final int uploadSpeed;

  /// Current download speed in bytes per second
  final int downloadSpeed;

  /// Timestamp when this traffic data was collected
  final DateTime timestamp;

  /// TrafficData Constructor
  ///
  /// DESCRIPTION:
  ///     Creates a new TrafficData instance with the specified traffic information
  ///
  /// PARAMETERS:
  ///     uploadBytes - Total bytes uploaded (default: 0)
  ///     downloadBytes - Total bytes downloaded (default: 0)
  ///     uploadSpeed - Current upload speed in bytes/sec (default: 0)
  ///     downloadSpeed - Current download speed in bytes/sec (default: 0)
  ///     timestamp - When data was collected (default: current time)
  TrafficData({
    this.uploadBytes = kZeroBytes,
    this.downloadBytes = kZeroBytes,
    this.uploadSpeed = kZeroSpeed,
    this.downloadSpeed = kZeroSpeed,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  /// Static empty TrafficData instance
  ///
  /// DESCRIPTION:
  ///     Provides a reusable empty TrafficData instance for initialization
  ///     and default states
  static final TrafficData empty = TrafficData();

  /// fromJson Factory Constructor
  ///
  /// DESCRIPTION:
  ///     Creates a TrafficData instance from JSON data with safe type
  ///     conversion and timestamp handling
  ///
  /// PARAMETERS:
  ///     json - Map containing traffic data from JSON
  ///
  /// RETURNS:
  ///     TrafficData instance with data populated from JSON
  factory TrafficData.fromJson(Map<String, dynamic> json) {
    return TrafficData(
      uploadBytes: _parseIntSafely(json['uploadBytes']) ?? kZeroBytes,
      downloadBytes: _parseIntSafely(json['downloadBytes']) ?? kZeroBytes,
      uploadSpeed: _parseIntSafely(json['uploadSpeed']) ?? kZeroSpeed,
      downloadSpeed: _parseIntSafely(json['downloadSpeed']) ?? kZeroSpeed,
      timestamp: _parseTimestampSafely(json['timestamp']),
    );
  }

  /// toJson Serialization Method
  ///
  /// DESCRIPTION:
  ///     Converts TrafficData instance to JSON-compatible Map for serialization
  ///
  /// RETURNS:
  ///     Map containing all traffic data in JSON format
  Map<String, dynamic> toJson() {
    return {
      'uploadBytes': uploadBytes,
      'downloadBytes': downloadBytes,
      'uploadSpeed': uploadSpeed,
      'downloadSpeed': downloadSpeed,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// copyWith Method
  ///
  /// DESCRIPTION:
  ///     Creates a new TrafficData instance with specified fields updated,
  ///     maintaining immutability while allowing selective updates
  ///
  /// PARAMETERS:
  ///     uploadBytes - New upload bytes count (optional)
  ///     downloadBytes - New download bytes count (optional)
  ///     uploadSpeed - New upload speed (optional)
  ///     downloadSpeed - New download speed (optional)
  ///     timestamp - New timestamp (optional)
  ///
  /// RETURNS:
  ///     New TrafficData instance with updated fields
  TrafficData copyWith({
    int? uploadBytes,
    int? downloadBytes,
    int? uploadSpeed,
    int? downloadSpeed,
    DateTime? timestamp,
  }) {
    return TrafficData(
      uploadBytes: uploadBytes ?? this.uploadBytes,
      downloadBytes: downloadBytes ?? this.downloadBytes,
      uploadSpeed: uploadSpeed ?? this.uploadSpeed,
      downloadSpeed: downloadSpeed ?? this.downloadSpeed,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  /// Safe integer parsing helper
  ///
  /// DESCRIPTION:
  ///     Safely parses dynamic value to integer with null handling
  ///
  /// PARAMETERS:
  ///     value - Dynamic value to parse
  ///
  /// RETURNS:
  ///     Parsed integer or null if parsing fails
  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) return int.tryParse(value);
    if (value is double) return value.toInt();
    return null;
  }

  /// Safe timestamp parsing helper
  ///
  /// DESCRIPTION:
  ///     Safely parses timestamp from JSON with fallback to current time
  ///
  /// PARAMETERS:
  ///     value - Dynamic timestamp value (ISO string or int)
  ///
  /// RETURNS:
  ///     DateTime instance from timestamp or current time if parsing fails
  static DateTime _parseTimestampSafely(dynamic value) {
    if (value == null) return DateTime.now();

    try {
      if (value is String) {
        return DateTime.parse(value);
      }
      if (value is int) {
        return DateTime.fromMillisecondsSinceEpoch(value * 1000);
      }
    } catch (e) {
      // If parsing fails, return current time
    }

    return DateTime.now();
  }

  /// totalBytes Getter
  ///
  /// DESCRIPTION:
  ///     Calculates total bytes transferred (upload + download)
  ///
  /// RETURNS:
  ///     Sum of upload and download bytes
  int get totalBytes => uploadBytes + downloadBytes;

  /// totalSpeed Getter
  ///
  /// DESCRIPTION:
  ///     Calculates total current speed (upload + download)
  ///
  /// RETURNS:
  ///     Sum of upload and download speeds in bytes per second
  int get totalSpeed => uploadSpeed + downloadSpeed;

  /// toString Override
  ///
  /// DESCRIPTION:
  ///     Provides a string representation of the TrafficData for debugging
  ///
  /// RETURNS:
  ///     Formatted string with bytes and speeds information
  @override
  String toString() {
    return 'TrafficData(upload: ${uploadBytes}B, download: ${downloadBytes}B, '
           'uploadSpeed: ${uploadSpeed}B/s, downloadSpeed: ${downloadSpeed}B/s, '
           'time: ${timestamp.toIso8601String()})';
  }

  /// Equality operator override
  ///
  /// DESCRIPTION:
  ///     Compares two TrafficData instances for equality
  ///
  /// PARAMETERS:
  ///     other - Object to compare with
  ///
  /// RETURNS:
  ///     true if all fields are equal, false otherwise
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TrafficData &&
        other.uploadBytes == uploadBytes &&
        other.downloadBytes == downloadBytes &&
        other.uploadSpeed == uploadSpeed &&
        other.downloadSpeed == downloadSpeed &&
        other.timestamp == timestamp;
  }

  /// Hash code override
  ///
  /// DESCRIPTION:
  ///     Generates hash code for use in collections
  ///
  /// RETURNS:
  ///     Hash code based on all fields
  @override
  int get hashCode {
    return Object.hash(
      uploadBytes,
      downloadBytes,
      uploadSpeed,
      downloadSpeed,
      timestamp,
    );
  }

  /// hasActivity Getter
  ///
  /// DESCRIPTION:
  ///     Checks if there is any current network activity
  ///
  /// RETURNS:
  ///     true if either upload or download speed is greater than zero
  bool get hasActivity => uploadSpeed > 0 || downloadSpeed > 0;

  /// hasData Getter
  ///
  /// DESCRIPTION:
  ///     Checks if any data has been transferred
  ///
  /// RETURNS:
  ///     true if either upload or download bytes is greater than zero
  bool get hasData => uploadBytes > 0 || downloadBytes > 0;

  /// isEmpty Getter
  ///
  /// DESCRIPTION:
  ///     Checks if this is empty traffic data (no bytes or speed)
  ///
  /// RETURNS:
  ///     true if all values are zero, false otherwise
  bool get isEmpty => uploadBytes == 0 && downloadBytes == 0 &&
                      uploadSpeed == 0 && downloadSpeed == 0;

  /// uploadRatio Getter
  ///
  /// DESCRIPTION:
  ///     Calculates the ratio of upload bytes to total bytes with NaN protection
  ///
  /// RETURNS:
  ///     Upload ratio as double between 0.0 and 1.0, or 0.0 if no data or invalid
  double get uploadRatio {
    final total = totalBytes;
    if (total <= 0 || uploadBytes < 0) return 0.0;
    final ratio = uploadBytes / total;
    return ratio.isNaN || ratio.isInfinite ? 0.0 : ratio.clamp(0.0, 1.0);
  }

  /// downloadRatio Getter
  ///
  /// DESCRIPTION:
  ///     Calculates the ratio of download bytes to total bytes with NaN protection
  ///
  /// RETURNS:
  ///     Download ratio as double between 0.0 and 1.0, or 0.0 if no data or invalid
  double get downloadRatio {
    final total = totalBytes;
    if (total <= 0 || downloadBytes < 0) return 0.0;
    final ratio = downloadBytes / total;
    return ratio.isNaN || ratio.isInfinite ? 0.0 : ratio.clamp(0.0, 1.0);
  }
}
