/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      log_entry.dart
///
/// DESCRIPTION :    Log entry data model that represents a single log message
///                  with timestamp, level, message content, and module
///                  information for application logging system.
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import '../utils/constants.dart';

/// LogEntry Data Model
///
/// PURPOSE:
///     Represents a single log entry with timestamp, level, message, and
///     module information for structured application logging
///
/// FEATURES:
///     - Timestamp for log entry creation time
///     - Log level classification (debug, info, warning, error)
///     - Message content with module identification
///     - JSON serialization/deserialization support
///     - Immutable design for thread safety
///     - Safe log level parsing with fallback defaults
///     - Automatic timestamp generation if not provided
///     - String conversion utilities for log levels
///
/// USAGE:
///     // Create log entry
///     final logEntry = LogEntry(
///       level: LogLevel.info,
///       message: 'Connection established',
///       module: 'ConnectionManager'
///     );
///
///     // Create from JSON
///     final entry = LogEntry.fromJson(jsonData);
class LogEntry {
  /// Log Entry Constants
  static const int kMillisecondsPerSecond = 1000;
  static const String kEmptyString = '';
  static const String kDefaultModule = 'Unknown';

  /// Timestamp when the log entry was created
  final DateTime timestamp;

  /// Log level indicating severity/importance
  final LogLevel level;

  /// Log message content
  final String message;

  /// Module or component that generated the log
  final String module;

  /// LogEntry Constructor
  ///
  /// DESCRIPTION:
  ///     Creates a new LogEntry instance with the specified log data
  ///
  /// PARAMETERS:
  ///     level - Log level (debug, info, warning, error)
  ///     message - Log message content
  ///     module - Module or component name that generated the log
  ///     timestamp - When log was created (default: current time)
  LogEntry({
    required this.level,
    required this.message,
    required this.module,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  /// fromJson Factory Constructor
  ///
  /// DESCRIPTION:
  ///     Creates a LogEntry instance from JSON data with safe type conversion
  ///     and timestamp handling
  ///
  /// PARAMETERS:
  ///     json - Map containing log entry data from JSON
  ///
  /// RETURNS:
  ///     LogEntry instance with data populated from JSON
  factory LogEntry.fromJson(Map<String, dynamic> json) {
    return LogEntry(
      level: _parseLogLevel(json['level']),
      message: _parseStringSafely(json['message']) ?? kEmptyString,
      module: _parseStringSafely(json['module']) ?? kDefaultModule,
      timestamp: _parseTimestampSafely(json['timestamp']),
    );
  }

  /// toJson Serialization Method
  ///
  /// DESCRIPTION:
  ///     Converts LogEntry instance to JSON-compatible Map for serialization
  ///
  /// RETURNS:
  ///     Map containing all log entry data in JSON format
  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.millisecondsSinceEpoch ~/ kMillisecondsPerSecond,
      'level': _logLevelToString(level),
      'message': message,
      'module': module,
    };
  }

  /// Safe log level parsing helper
  ///
  /// DESCRIPTION:
  ///     Safely parses log level from string with fallback to info level
  ///
  /// PARAMETERS:
  ///     levelStr - String representation of log level
  ///
  /// RETURNS:
  ///     Parsed LogLevel or LogLevel.info if parsing fails
  static LogLevel _parseLogLevel(dynamic levelStr) {
    if (levelStr == null) return LogLevel.info;

    final level = levelStr.toString().toLowerCase().trim();
    switch (level) {
      case 'debug':
        return LogLevel.debug;
      case 'info':
        return LogLevel.info;
      case 'warning':
      case 'warn':
        return LogLevel.warning;
      case 'error':
        return LogLevel.error;
      default:
        return LogLevel.info;
    }
  }

  /// Log level to string conversion helper
  ///
  /// DESCRIPTION:
  ///     Converts LogLevel enum to string representation
  ///
  /// PARAMETERS:
  ///     level - LogLevel enum value
  ///
  /// RETURNS:
  ///     String representation of the log level
  static String _logLevelToString(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return 'debug';
      case LogLevel.info:
        return 'info';
      case LogLevel.warning:
        return 'warning';
      case LogLevel.error:
        return 'error';
    }
  }

  /// Safe string parsing helper
  ///
  /// DESCRIPTION:
  ///     Safely parses dynamic value to string with null handling and trimming
  ///
  /// PARAMETERS:
  ///     value - Dynamic value to parse
  ///
  /// RETURNS:
  ///     Parsed and trimmed string or null if value is null/empty
  static String? _parseStringSafely(dynamic value) {
    if (value == null) return null;
    final stringValue = value.toString().trim();
    return stringValue.isEmpty ? null : stringValue;
  }

  /// Safe timestamp parsing helper
  ///
  /// DESCRIPTION:
  ///     Safely parses timestamp from JSON with fallback to current time
  ///
  /// PARAMETERS:
  ///     value - Dynamic timestamp value (usually int seconds)
  ///
  /// RETURNS:
  ///     DateTime instance from timestamp or current time if parsing fails
  static DateTime _parseTimestampSafely(dynamic value) {
    if (value == null) return DateTime.now();

    try {
      if (value is int) {
        return DateTime.fromMillisecondsSinceEpoch(value * kMillisecondsPerSecond);
      }
      if (value is String) {
        final intValue = int.tryParse(value);
        if (intValue != null) {
          return DateTime.fromMillisecondsSinceEpoch(intValue * kMillisecondsPerSecond);
        }
      }
    } catch (e) {
      // If parsing fails, return current time
    }

    return DateTime.now();
  }

  /// toString Override
  ///
  /// DESCRIPTION:
  ///     Provides a string representation of the LogEntry for debugging
  ///
  /// RETURNS:
  ///     Formatted string with timestamp, level, module, and message
  @override
  String toString() {
    final levelStr = _logLevelToString(level).toUpperCase();
    final timeStr = timestamp.toIso8601String();
    return '[$timeStr] $levelStr [$module] $message';
  }

  /// Equality operator override
  ///
  /// DESCRIPTION:
  ///     Compares two LogEntry instances for equality
  ///
  /// PARAMETERS:
  ///     other - Object to compare with
  ///
  /// RETURNS:
  ///     true if all fields are equal, false otherwise
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LogEntry &&
        other.timestamp == timestamp &&
        other.level == level &&
        other.message == message &&
        other.module == module;
  }

  /// Hash code override
  ///
  /// DESCRIPTION:
  ///     Generates hash code for use in collections
  ///
  /// RETURNS:
  ///     Hash code based on all fields
  @override
  int get hashCode {
    return Object.hash(timestamp, level, message, module);
  }

  /// isError Getter
  ///
  /// DESCRIPTION:
  ///     Checks if this log entry is an error level
  ///
  /// RETURNS:
  ///     true if level is error, false otherwise
  bool get isError => level == LogLevel.error;

  /// isWarning Getter
  ///
  /// DESCRIPTION:
  ///     Checks if this log entry is a warning level
  ///
  /// RETURNS:
  ///     true if level is warning, false otherwise
  bool get isWarning => level == LogLevel.warning;

  /// isInfo Getter
  ///
  /// DESCRIPTION:
  ///     Checks if this log entry is an info level
  ///
  /// RETURNS:
  ///     true if level is info, false otherwise
  bool get isInfo => level == LogLevel.info;

  /// isDebug Getter
  ///
  /// DESCRIPTION:
  ///     Checks if this log entry is a debug level
  ///
  /// RETURNS:
  ///     true if level is debug, false otherwise
  bool get isDebug => level == LogLevel.debug;

  /// levelName Getter
  ///
  /// DESCRIPTION:
  ///     Returns the string representation of the log level
  ///
  /// RETURNS:
  ///     String name of the log level
  String get levelName => _logLevelToString(level);

  /// formattedTimestamp Getter
  ///
  /// DESCRIPTION:
  ///     Returns a formatted timestamp string for display
  ///
  /// RETURNS:
  ///     Formatted timestamp string (HH:mm:ss.SSS)
  String get formattedTimestamp {
    return '${timestamp.hour.toString().padLeft(2, '0')}:'
           '${timestamp.minute.toString().padLeft(2, '0')}:'
           '${timestamp.second.toString().padLeft(2, '0')}.'
           '${timestamp.millisecond.toString().padLeft(3, '0')}';
  }
}
