/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      traffic_stats.dart
 *
 * DESCRIPTION :    Traffic statistics data model that represents network
 *                  traffic information including upload/download speeds
 *                  and total data transfer amounts with timestamp.
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/



/// TrafficStats Data Model
///
/// PURPOSE:
///     Represents network traffic statistics including real-time speeds
///     and cumulative data transfer amounts with timestamp information
///
/// FEATURES:
///     - Real-time upload and download speeds (bytes per second)
///     - Cumulative upload and download totals (bytes)
///     - Timestamp for data collection time
///     - JSON serialization/deserialization support
///     - Immutable design for thread safety
///     - Factory constructor for empty/default state
///     - Safe timestamp handling with fallback to current time
///
/// USAGE:
///     // Create from JSON
///     final stats = TrafficStats.fromJson(jsonData);
///
///     // Create empty stats
///     final emptyStats = TrafficStats.empty();
///
///     // Access speed data
///     final uploadKbps = stats.uploadSpeed / 1024;
class TrafficStats {
  /// Traffic Statistics Constants
  static const int kZeroBytes = 0;
  static const int kMillisecondsPerSecond = 1000;

  /// Current upload speed in bytes per second
  final int uploadSpeed;

  /// Current download speed in bytes per second
  final int downloadSpeed;

  /// Total bytes uploaded since connection start
  final int totalUpload;

  /// Total bytes downloaded since connection start
  final int totalDownload;

  /// Timestamp when these statistics were collected
  final DateTime timestamp;

  /// TrafficStats Constructor
  ///
  /// DESCRIPTION:
  ///     Creates a new TrafficStats instance with the specified traffic data
  ///
  /// PARAMETERS:
  ///     uploadSpeed - Current upload speed in bytes per second
  ///     downloadSpeed - Current download speed in bytes per second
  ///     totalUpload - Total bytes uploaded
  ///     totalDownload - Total bytes downloaded
  ///     timestamp - When stats were collected (default: current time)
  TrafficStats({
    required this.uploadSpeed,
    required this.downloadSpeed,
    required this.totalUpload,
    required this.totalDownload,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  /// fromJson Factory Constructor
  ///
  /// DESCRIPTION:
  ///     Creates a TrafficStats instance from JSON data with safe type
  ///     conversion and timestamp handling
  ///
  /// PARAMETERS:
  ///     json - Map containing traffic statistics data from JSON
  ///
  /// RETURNS:
  ///     TrafficStats instance with data populated from JSON
  factory TrafficStats.fromJson(Map<String, dynamic> json) {
    return TrafficStats(
      uploadSpeed: _parseIntSafely(json['upload_speed']) ?? kZeroBytes,
      downloadSpeed: _parseIntSafely(json['download_speed']) ?? kZeroBytes,
      totalUpload: _parseIntSafely(json['total_upload']) ?? kZeroBytes,
      totalDownload: _parseIntSafely(json['total_download']) ?? kZeroBytes,
      timestamp: _parseTimestampSafely(json['timestamp']),
    );
  }

  /// toJson Serialization Method
  ///
  /// DESCRIPTION:
  ///     Converts TrafficStats instance to JSON-compatible Map for serialization
  ///
  /// RETURNS:
  ///     Map containing all traffic statistics data in JSON format
  Map<String, dynamic> toJson() {
    return {
      'upload_speed': uploadSpeed,
      'download_speed': downloadSpeed,
      'total_upload': totalUpload,
      'total_download': totalDownload,
      'timestamp': timestamp.millisecondsSinceEpoch ~/ kMillisecondsPerSecond,
    };
  }

  /// empty Factory Constructor
  ///
  /// DESCRIPTION:
  ///     Creates an empty TrafficStats instance with zero values for
  ///     initialization or error cases
  ///
  /// RETURNS:
  ///     TrafficStats instance with all zero values and current timestamp
  factory TrafficStats.empty() {
    return TrafficStats(
      uploadSpeed: kZeroBytes,
      downloadSpeed: kZeroBytes,
      totalUpload: kZeroBytes,
      totalDownload: kZeroBytes,
    );
  }

  /// Safe integer parsing helper
  ///
  /// DESCRIPTION:
  ///     Safely parses dynamic value to integer with null handling
  ///
  /// PARAMETERS:
  ///     value - Dynamic value to parse
  ///
  /// RETURNS:
  ///     Parsed integer or null if parsing fails
  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) return int.tryParse(value);
    if (value is double) return value.toInt();
    return null;
  }

  /// Safe timestamp parsing helper
  ///
  /// DESCRIPTION:
  ///     Safely parses timestamp from JSON with fallback to current time
  ///
  /// PARAMETERS:
  ///     value - Dynamic timestamp value (usually int seconds)
  ///
  /// RETURNS:
  ///     DateTime instance from timestamp or current time if parsing fails
  static DateTime _parseTimestampSafely(dynamic value) {
    if (value == null) return DateTime.now();

    try {
      if (value is int) {
        return DateTime.fromMillisecondsSinceEpoch(value * kMillisecondsPerSecond);
      }
      if (value is String) {
        final intValue = int.tryParse(value);
        if (intValue != null) {
          return DateTime.fromMillisecondsSinceEpoch(intValue * kMillisecondsPerSecond);
        }
      }
    } catch (e) {
      // If parsing fails, return current time
      // If parsing fails, return current time (avoid debugPrint in production)
    }

    return DateTime.now();
  }

  /// toString Override
  ///
  /// DESCRIPTION:
  ///     Provides a string representation of the TrafficStats for debugging
  ///
  /// RETURNS:
  ///     String representation including speeds and totals
  @override
  String toString() {
    return 'TrafficStats(up: ${uploadSpeed}B/s, down: ${downloadSpeed}B/s, '
           'totalUp: ${totalUpload}B, totalDown: ${totalDownload}B, '
           'time: ${timestamp.toIso8601String()})';
  }

  /// Equality operator override
  ///
  /// DESCRIPTION:
  ///     Compares two TrafficStats instances for equality
  ///
  /// PARAMETERS:
  ///     other - Object to compare with
  ///
  /// RETURNS:
  ///     true if all fields are equal, false otherwise
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TrafficStats &&
        other.uploadSpeed == uploadSpeed &&
        other.downloadSpeed == downloadSpeed &&
        other.totalUpload == totalUpload &&
        other.totalDownload == totalDownload &&
        other.timestamp == timestamp;
  }

  /// Hash code override
  ///
  /// DESCRIPTION:
  ///     Generates hash code for use in collections
  ///
  /// RETURNS:
  ///     Hash code based on all fields
  @override
  int get hashCode {
    return Object.hash(
      uploadSpeed,
      downloadSpeed,
      totalUpload,
      totalDownload,
      timestamp,
    );
  }

  /// totalBytes Getter
  ///
  /// DESCRIPTION:
  ///     Calculates total bytes transferred (upload + download)
  ///
  /// RETURNS:
  ///     Sum of total upload and download bytes
  int get totalBytes => totalUpload + totalDownload;

  /// totalSpeed Getter
  ///
  /// DESCRIPTION:
  ///     Calculates total current speed (upload + download)
  ///
  /// RETURNS:
  ///     Sum of current upload and download speeds in bytes per second
  int get totalSpeed => uploadSpeed + downloadSpeed;

  /// hasActivity Getter
  ///
  /// DESCRIPTION:
  ///     Checks if there is any current network activity
  ///
  /// RETURNS:
  ///     true if either upload or download speed is greater than zero
  bool get hasActivity => uploadSpeed > 0 || downloadSpeed > 0;
}
