import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/app_state.dart';
import '../core/dependency_injection.dart';
import '../services/connection_manager.dart';
import '../services/data_manager.dart';
import '../services/log_service.dart';
import '../services/language_service.dart';
import '../services/notification_service.dart';
import '../services/websocket_service.dart';
import '../services/auth_service.dart';
import '../services/platform/platform_service_factory.dart';
import '../widgets/svg_asset_widget.dart';
import '../utils/design_system.dart';
import '../utils/ping_color_utils.dart';
import '../utils/animations.dart';
import '../models/connection_status.dart';

import '../models/server.dart';
import '../generated/l10n/app_localizations.dart';

/// 连接屏幕 - 基于新设计系统
class ConnectionScreen extends StatefulWidget {
  const ConnectionScreen({super.key});

  @override
  State<ConnectionScreen> createState() => _ConnectionScreenState();
}

class _ConnectionScreenState extends State<ConnectionScreen>
    with TickerProviderStateMixin {
  late final ConnectionManager _connectionManager;
  late final LogService _logService;
  late final LanguageService _languageService;
  late final NotificationService _notificationService;
  late final WebSocketService _webSocketService;
  late final AuthService _authService;

  // 延迟测试状态
  bool _isTestingLatency = false;

  // 对话框状态管理
  StateSetter? _dialogStateSetter;

  // 动画控制器
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late AnimationController _rippleController;

  late Animation<double> _rotationAnimation;

  // 连接时间更新定时器
  Timer? _connectionTimer;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeAnimations();
    _setupWebSocketListeners();
    _checkAutoConnect();
  }

  void _initializeServices() {
    _connectionManager = serviceLocator<ConnectionManager>();
    _logService = serviceLocator<LogService>();
    _languageService = serviceLocator<LanguageService>();
    _notificationService = serviceLocator<NotificationService>();
    _webSocketService = serviceLocator<WebSocketService>();
    _authService = serviceLocator<AuthService>();
  }

  /// 获取当前的DataManager实例
  ///
  /// DESCRIPTION:
  ///     每次调用都从服务定位器获取最新的DataManager实例，
  ///     避免使用已释放的实例引用
  ///
  /// RETURNS:
  ///     DataManager - 当前活跃的DataManager实例
  DataManager get _currentDataManager => serviceLocator<DataManager>();

  /// 获取按延迟排序的服务器列表
  ///
  /// DESCRIPTION:
  ///     对服务器列表按延迟从低到高排序，确保最佳服务器显示在前面。
  ///     排序规则：
  ///     1. 在线服务器优先于离线服务器
  ///     2. 在线服务器按延迟从低到高排序
  ///     3. ping失败的服务器(ping=0或-1)排在最后
  ///
  /// PARAMETERS:
  ///     servers - 原始服务器列表
  ///
  /// RETURNS:
  ///     List<Server> - 排序后的服务器列表
  List<Server> _getSortedServerList(List<Server> servers) {
    if (servers.isEmpty) return servers;

    return List<Server>.from(servers)..sort((a, b) {
      // 处理ping值为-1的情况（未知延迟）
      final aPing = a.ping < 0 ? 9999 : a.ping;
      final bPing = b.ping < 0 ? 9999 : b.ping;

      // ping失败的服务器(ping=0)排在最后
      if (aPing == 0 && bPing > 0) return 1;
      if (aPing > 0 && bPing == 0) return -1;
      if (aPing == 0 && bPing == 0) return 0;

      // 都有有效ping值，按延迟从低到高排序
      return aPing.compareTo(bPing);
    });
  }

  void _initializeAnimations() {
    // 脉冲动画（连接状态指示）
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );


    // 旋转动画（连接中状态）
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    // 波纹动画（已连接状态）
    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
  }

  /// 设置WebSocket事件监听器
  void _setupWebSocketListeners() {
    // _logService.debug('ConnectionScreen', 'Setting up WebSocket listeners for ping events');

    // 监听ping结果事件，当延迟测试完成时显示通知
    _webSocketService.handlePingResultsEvent((servers) {
      // _logService.debug('ConnectionScreen', 'Received ping results event: ${servers.length} servers, _isTestingLatency=$_isTestingLatency');

      if (_isTestingLatency && mounted) {
        // _logService.debug('ConnectionScreen', 'Processing ping results - resetting testing state');

        // 重置主界面状态
        setState(() {
          _isTestingLatency = false;
        });

        // 如果对话框打开，也重置对话框状态并刷新服务器列表显示
        if (_dialogStateSetter != null) {
          // _logService.debug('ConnectionScreen', 'Resetting dialog state and refreshing server list');
          _dialogStateSetter!(() {
            _isTestingLatency = false;
            // 对话框会通过AnimatedBuilder自动刷新服务器列表
          });
        }

        final l10n = AppLocalizations.of(context)!;
        _notificationService.showSuccessNotification(l10n.latencyTestComplete);
        // _logService.debug('ConnectionScreen', 'Latency test completed, received ${servers.length} server results');
      } else {
        // _logService.debug('ConnectionScreen', 'Ping results received but not processing: _isTestingLatency=$_isTestingLatency, mounted=$mounted');
      }
    });

    // 监听ping开始和完成事件
    _webSocketService.handlePingEvents(
      () {
        // _logService.debug('ConnectionScreen', 'Received ping start event');
      },
      () {
        // _logService.debug('ConnectionScreen', 'Received ping complete event');
      }
    );

    // 监听服务器列表更新事件，确保对话框实时更新
    _webSocketService.handleServerListEvent((servers) {
      // _logService.debug('ConnectionScreen', 'Received server list update: ${servers.length} servers');

      // 如果对话框打开，更新对话框状态以刷新服务器列表显示
      if (_dialogStateSetter != null && mounted) {
        // _logService.debug('ConnectionScreen', 'Updating dialog state after server list update');
        _dialogStateSetter!(() {
          // 对话框会通过AnimatedBuilder自动刷新服务器列表
        });
      }
    });
  }

  /// 检查是否需要自动连接
  ///
  /// DESCRIPTION:
  ///     在首次进入连接界面时检查是否需要自动连接，
  ///     如果需要则自动发起连接操作
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _checkAutoConnect() {
    // 使用WidgetsBinding.instance.addPostFrameCallback确保在界面构建完成后执行
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        // 检查是否需要自动连接
        if (_authService.shouldAutoConnect) {
          _logService.info('ConnectionScreen', 'Auto-connect flag detected, starting automatic connection');

          // 清除自动连接标记，避免重复连接
          _authService.clearAutoConnectFlag();

          // 等待一小段时间确保UI完全加载
          await Future.delayed(const Duration(milliseconds: 500));

          // 检查当前连接状态，只有在未连接状态下才自动连接
          final appState = serviceLocator<AppState>();
          if (appState.connectionStatus == ConnectionStatus.disconnected &&
              appState.selectedServer != null) {

            _logService.info('ConnectionScreen', 'Starting auto-connect to server: ${appState.selectedServer!.name}');

            // 自动发起连接
            final success = await _connectionManager.connect();

            if (success) {
              _logService.info('ConnectionScreen', 'Auto-connect initiated successfully');
            } else {
              _logService.warning('ConnectionScreen', 'Auto-connect failed to initiate');
            }
          } else {
            _logService.info('ConnectionScreen', 'Auto-connect skipped - invalid state or no server selected');
          }
        } else {
          _logService.debug('ConnectionScreen', 'No auto-connect flag, skipping automatic connection');
        }
      } catch (e) {
        _logService.error('ConnectionScreen', 'Error during auto-connect check', e);
      }
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    _rippleController.dispose();
    _connectionTimer?.cancel();
    _dialogStateSetter = null; // 清理对话框状态设置器引用
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        _updateAnimations(appState.connectionStatus);

        return Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: AppColors.backgroundGradient,
            ),
          ),
          child: Stack(
            children: [
              // 主要内容
              _buildMainContent(appState),

              // 底部遮罩
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: DesignSystemSvgs.maskOverlay(
                  width: double.infinity,
                  height: 100,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建主要内容
  Widget _buildMainContent(AppState appState) {
    return Padding(
      padding: const EdgeInsets.all(DesignSystem.spacing32),
      child: Column(
        children: [
          // Panabit主Logo
          _buildMainLogo(),

          const SizedBox(height: DesignSystem.spacing46),

          // 连接状态区域
          Expanded(
            child: _buildConnectionArea(appState),
          ),

          // 移除底部信息区域
        ],
      ),
    );
  }

  /// 构建主Logo
  Widget _buildMainLogo() {
    return DesignSystemSvgs.shieldLogo(
      width: 40, // 与用户界面保持一致
      height: 45, // 与用户界面保持一致
    );
  }

  /// 构建连接区域
  Widget _buildConnectionArea(AppState appState) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 连接按钮
          _buildConnectionButton(appState),

          const SizedBox(height: DesignSystem.spacing32),

          // 连接状态文本
          _buildConnectionStatusText(appState),

          const SizedBox(height: DesignSystem.spacing24),

          // 连接计时（在所有状态下显示）
          _buildConnectionTimer(appState),

          const SizedBox(height: DesignSystem.spacing24),

          // 服务器选择区域
          _buildServerSelection(appState),

          const SizedBox(height: DesignSystem.spacing16),

          // 实时速率信息（为保持布局一致性，在所有状态下都预留空间）
          _buildSpeedInfoArea(appState),
        ],
      ),
    );
  }

  /// 构建连接按钮
  Widget _buildConnectionButton(AppState appState) {
    final isConnected = appState.connectionStatus == ConnectionStatus.connected;
    final isConnecting = appState.connectionStatus == ConnectionStatus.connecting;
    final isDisconnecting = appState.connectionStatus == ConnectionStatus.disconnecting;

    Widget buttonWidget;

    if (isConnecting || isDisconnecting) {
      // 连接中或断开连接中 - 显示旋转动画
      buttonWidget = AnimatedBuilder(
        animation: _rotationAnimation,
        builder: (context, child) {
          return Transform.rotate(
            angle: _rotationAnimation.value * 2 * 3.14159,
            child: Container(
              width: 120,
              height: 120,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.connecting,
              ),
              child: const Icon(
                Icons.sync,
                size: 48,
                color: Colors.white,
              ),
            ),
          );
        },
      );
    } else if (isConnected) {
      // 已连接 - 显示断开连接按钮（带波纹动画）- 使用本地化版本
      buttonWidget = RippleAnimation(
        color: AppColors.connected,
        numberOfRipples: 3,
        duration: const Duration(milliseconds: 2000),
        child: DesignSystemSvgs.localizedConnectedButton(
          isEnglish: _languageService.isEnglish,
          width: 120,
          height: 120,
        ),
      );
    } else {
      // 未连接 - 显示连接按钮 - 使用本地化版本
      buttonWidget = DesignSystemSvgs.localizedDisconnectedButton(
        isEnglish: _languageService.isEnglish,
        width: 120,
        height: 120,
      );
    }

    return GestureDetector(
      onTap: () => _handleConnectionToggle(appState),
      child: Container(
        width: 140,
        height: 140,
        alignment: Alignment.center, // 确保内容居中对齐
        child: SizedBox(
          width: 120,
          height: 120,
          child: buttonWidget,
        ),
      ),
    );
  }

  /// 构建连接状态文本
  Widget _buildConnectionStatusText(AppState appState) {
    final l10n = AppLocalizations.of(context)!;
    String statusText;
    Color statusColor;
    IconData? statusIcon;

    switch (appState.connectionStatus) {
      case ConnectionStatus.connected:
        statusText = l10n.connected;
        statusColor = AppColors.connected;
        statusIcon = Icons.check_circle;
        break;
      case ConnectionStatus.connecting:
        statusText = l10n.connecting;
        statusColor = AppColors.connecting;
        break;
      case ConnectionStatus.disconnecting:
        statusText = l10n.disconnecting;
        statusColor = AppColors.connecting;
        break;
      case ConnectionStatus.disconnected:
      default:
        statusText = l10n.disconnected;
        statusColor = AppColors.disconnected; // 使用黄色
        statusIcon = Icons.warning;
        break;
    }

    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (statusIcon != null) ...[
              Icon(
                statusIcon,
                color: statusColor,
                size: 20,
              ),
              const SizedBox(width: DesignSystem.spacing8),
            ],
            Text(
              statusText,
              style: DesignSystem.displayLarge.copyWith(
                color: statusColor,
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),

        // 移除连接消息显示，特别是已连接状态下的服务器信息
      ],
    );
  }

  /// 构建连接计时器
  Widget _buildConnectionTimer(AppState appState) {
    String timeText;

    // 根据连接状态显示不同的时间
    if (appState.connectionStatus == ConnectionStatus.connected && appState.connectedTime != null) {
      // 已连接状态：显示实际的累积连接时间
      final duration = DateTime.now().difference(appState.connectedTime!);
      final hours = duration.inHours;
      final minutes = duration.inMinutes % 60;
      final seconds = duration.inSeconds % 60;
      timeText = '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      // 未连接、连接中、断开连接中状态：显示 00:00:00
      timeText = '00:00:00';
    }

    return Text(
      timeText,
      style: DesignSystem.displayLarge.copyWith(
        color: AppColors.textDarkGray, // 深灰色字体
        fontSize: 32,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  /// 构建服务器选择区域
  Widget _buildServerSelection(AppState appState) {
    return AnimatedBuilder(
      animation: _currentDataManager,
      builder: (context, child) {
        final servers = _getSortedServerList(_currentDataManager.servers);
        final selectedServer = appState.selectedServer;

        // 检查是否应该禁用服务器选择
        final currentStatus = appState.connectionStatus;
        final isDisabled = currentStatus == ConnectionStatus.connecting ||
                          currentStatus == ConnectionStatus.disconnecting;

        // 直接从ping测试结果中获取最新的延迟数据
        Server? currentServerWithLatestPing = selectedServer;
        if (selectedServer != null && servers.isNotEmpty) {
          // 从服务器列表中查找当前选中服务器的最新ping数据
          final latestServerData = servers.firstWhere(
            (server) => server.id == selectedServer.id,
            orElse: () => selectedServer,
          );
          currentServerWithLatestPing = latestServerData;
        }

        return GestureDetector(
          onTap: isDisabled ? null : () => _showServerSelectionDialog(appState),
          child: Container(
            // 使用响应式宽度而不是固定宽度，确保在不同平台上都能正确显示
            constraints: const BoxConstraints(
              maxWidth: 400,
              minWidth: 300,
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: DesignSystem.spacing20,
              vertical: DesignSystem.spacing12,
            ),
            decoration: BoxDecoration(
              color: AppColors.cardBackground.withValues(alpha: 0.24),
              borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
              border: Border.all(
                color: AppColors.textDarkGray.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // 服务器信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        currentServerWithLatestPing?.getDisplayNameByLocale(_languageService.isEnglish) ?? AppLocalizations.of(context)!.selectServerFirst,
                        style: DesignSystem.titleMedium.copyWith(
                          color: AppColors.textDarkGray,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: DesignSystem.spacing4),
                      Row(
                        children: [
                          // 延迟显示容器，带颜色背景 - 直接使用ping测试结果
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: _getPingColorForWhiteBackground(currentServerWithLatestPing?.ping ?? 0).withValues(alpha: 0.15),
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(
                                color: _getPingColorForWhiteBackground(currentServerWithLatestPing?.ping ?? 0).withValues(alpha: 0.4),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.speed,
                                  size: 12,
                                  color: _getPingColorForWhiteBackground(currentServerWithLatestPing?.ping ?? 0),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  PingColorUtils.getPingText(
                                    currentServerWithLatestPing?.ping ?? 0,
                                    unreachableText: AppLocalizations.of(context)!.unreachable,
                                  ),
                                  style: DesignSystem.labelSmall.copyWith(
                                    color: _getPingColorForWhiteBackground(currentServerWithLatestPing?.ping ?? 0),
                                    fontWeight: FontWeight.w600,
                                    fontSize: 11,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // 下拉箭头
                Container(
                  padding: const EdgeInsets.all(DesignSystem.spacing4),
                  child: Icon(
                    Icons.keyboard_arrow_down,
                    color: isDisabled
                      ? AppColors.textDarkGray.withValues(alpha: 0.3)
                      : AppColors.textDarkGray,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建速率信息区域（为保持布局一致性，在所有状态下都预留空间）
  Widget _buildSpeedInfoArea(AppState appState) {
    // 为速率信息预留固定高度的空间，保持布局一致性
    return SizedBox(
      width: 334,
      height: 20, // 固定高度，与速率信息文字高度一致
      child: appState.connectionStatus == ConnectionStatus.connected
          ? _buildSpeedInfo(appState)
          : const SizedBox.shrink(), // 未连接时显示空白但保持高度
    );
  }

  /// 构建简化的速率信息（只显示文字）
  Widget _buildSpeedInfo(AppState appState) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 上行速率
        Text(
          '${AppLocalizations.of(context)!.uploadSpeed}：${_formatSpeed(appState.trafficStats.uploadSpeed)}',
          style: DesignSystem.bodySmall.copyWith(
            color: AppColors.textDarkGray,
            fontSize: 12,
          ),
        ),

        // 下行速率
        Text(
          '${AppLocalizations.of(context)!.downloadSpeed}：${_formatSpeed(appState.trafficStats.downloadSpeed)}',
          style: DesignSystem.bodySmall.copyWith(
            color: AppColors.textDarkGray,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// 显示服务器选择对话框
  Future<void> _showServerSelectionDialog(AppState appState) async {
    final servers = _currentDataManager.servers;

    if (servers.isEmpty) {
      // 显示加载提示
      return;
    }

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext dialogContext, StateSetter setDialogState) {
            // 存储对话框状态设置器的引用
            _dialogStateSetter = setDialogState;

            // 使用 AnimatedBuilder 来监听 DataManager 的变化
            return AnimatedBuilder(
              animation: _currentDataManager,
              builder: (context, child) {
                // 获取最新的服务器列表并按延迟排序
                final currentServers = _getSortedServerList(_currentDataManager.servers);

                return GestureDetector(
                  onTap: () => Navigator.of(context).pop(), // 点击外部区域关闭弹窗
                  child: Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: Colors.transparent,
                    child: Align(
                      alignment: PlatformServiceFactory.shouldUseDesktopLayoutSync()
                          ? Alignment.bottomRight
                          : Alignment.bottomCenter,
                      child: GestureDetector(
                        onTap: () {}, // 阻止点击弹窗内容区域时关闭
                        child: Container(
                          width: PlatformServiceFactory.shouldUseDesktopLayoutSync()
                              ? MediaQuery.of(context).size.width - DesignSystem.sidebarWidth // 桌面端减去侧边栏宽度
                              : MediaQuery.of(context).size.width, // iOS使用全屏宽度
                          height: MediaQuery.of(context).size.height * 0.7,
                          decoration: const BoxDecoration(
                            color: Colors.white, // 使用白色背景
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(8), // 更平的上方圆角
                              topRight: Radius.circular(8),
                            ),
                          ),
                          child: Column(
                            children: [
                              // 标题栏
                              Container(
                                padding: const EdgeInsets.fromLTRB(24, 20, 16, 16),
                                decoration: BoxDecoration(
                                  border: Border(
                                    bottom: BorderSide(
                                      color: Colors.grey.withValues(alpha: 0.2),
                                      width: 1,
                                    ),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      AppLocalizations.of(context)!.selectServer,
                                      style: DesignSystem.titleMedium.copyWith(
                                        color: Colors.black87, // 深色文字适配白色背景
                                      ),
                                    ),
                                    IconButton(
                                      onPressed: () => Navigator.of(context).pop(),
                                      icon: const Icon(Icons.close, color: Colors.black54),
                                      iconSize: 20,
                                    ),
                                  ],
                                ),
                              ),

                              // 服务器列表内容
                              Expanded(
                                child: Column(
                                  children: [
                                    // 服务器列表 - 会通过WebSocket事件自动更新延迟数据
                                    Expanded(
                                      child: ListView.builder(
                                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                        itemCount: currentServers.length,
                                        itemBuilder: (context, index) {
                                          final server = currentServers[index];
                                          final currentAppState = serviceLocator<AppState>();
                                          final isSelected = currentAppState.selectedServer?.id == server.id;

                                          return Container(
                                            margin: const EdgeInsets.only(bottom: 8),
                                            decoration: BoxDecoration(
                                              color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.05),
                                              borderRadius: BorderRadius.circular(12),
                                              border: Border.all(
                                                color: isSelected ? AppColors.primary : Colors.grey.withValues(alpha: 0.2),
                                                width: isSelected ? 2 : 1,
                                              ),
                                            ),
                                            child: ListTile(
                                              title: Text(
                                                server.getDisplayNameByLocale(_languageService.isEnglish),
                                                style: DesignSystem.bodyMedium.copyWith(
                                                  color: Colors.black87, // 深色文字适配白色背景
                                                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                                                ),
                                              ),
                                              subtitle: Row(
                                                children: [
                                                  // 延迟显示容器，带颜色背景
                                                  Container(
                                                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                                    decoration: BoxDecoration(
                                                      color: _getPingColorForWhiteBackground(server.ping).withValues(alpha: 0.15),
                                                      borderRadius: BorderRadius.circular(8),
                                                      border: Border.all(
                                                        color: _getPingColorForWhiteBackground(server.ping).withValues(alpha: 0.4),
                                                        width: 1,
                                                      ),
                                                    ),
                                                    child: Row(
                                                      mainAxisSize: MainAxisSize.min,
                                                      children: [
                                                        Icon(
                                                          Icons.speed,
                                                          size: 12,
                                                          color: _getPingColorForWhiteBackground(server.ping),
                                                        ),
                                                        const SizedBox(width: 4),
                                                        Text(
                                                          PingColorUtils.getPingText(
                                                            server.ping,
                                                            unreachableText: AppLocalizations.of(context)!.unreachable,
                                                          ),
                                                          style: DesignSystem.labelSmall.copyWith(
                                                            color: _getPingColorForWhiteBackground(server.ping),
                                                            fontWeight: FontWeight.w600,
                                                            fontSize: 11,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  const SizedBox(width: 8),
                                                  // 服务器地址
                                                  Expanded(
                                                    child: Text(
                                                      server.serverName,
                                                      style: DesignSystem.labelSmall.copyWith(
                                                        color: Colors.black54, // 深色文字适配白色背景
                                                      ),
                                                      overflow: TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              trailing: isSelected
                                                ? const Icon(Icons.check, color: AppColors.primary)
                                                : null,
                                              onTap: () async {
                                                Navigator.of(context).pop();
                                                try {
                                                  await _connectionManager.selectServer(server);
                                                } catch (e) {
                                                  final serverDisplayName = server.getDisplayNameByLocale(_languageService.isEnglish);
                                                  _logService.error('ConnectionScreen', 'Failed to select server: $serverDisplayName', e);
                                                }
                                              },
                                            ),
                                          );
                                        },
                                      ),
                                    ),

                                    // 测试延迟按钮区域
                                    Container(
                                      padding: const EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        border: Border(
                                          top: BorderSide(
                                            color: Colors.grey.withValues(alpha: 0.2),
                                            width: 1,
                                          ),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          ElevatedButton.icon(
                                            onPressed: _isTestingLatency ? null : () async {
                                              // 不关闭对话框，直接执行延迟测试
                                              await _testServerLatencyInDialog(setDialogState);
                                            },
                                            icon: _isTestingLatency
                                                ? const SizedBox(
                                                    width: 16,
                                                    height: 16,
                                                    child: CircularProgressIndicator(
                                                      strokeWidth: 2,
                                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                                    ),
                                                  )
                                                : const Icon(Icons.speed, size: 16),
                                            label: Text(
                                              _isTestingLatency
                                                  ? AppLocalizations.of(context)!.testingLatency
                                                  : AppLocalizations.of(context)!.testLatency,
                                              style: const TextStyle(fontSize: 14),
                                            ),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: _isTestingLatency
                                                  ? AppColors.connecting
                                                  : AppColors.primary,
                                              foregroundColor: Colors.white,
                                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(8),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );

    // 对话框关闭后清理状态设置器引用
    _dialogStateSetter = null;
  }

  /// 更新动画状态
  void _updateAnimations(ConnectionStatus status) {
    switch (status) {
      case ConnectionStatus.connected:
        // 已连接状态启用波纹动画
        _pulseController.stop();
        _rotationController.stop();
        _rippleController.repeat();
        _startConnectionTimer();
        break;
      case ConnectionStatus.connecting:
      case ConnectionStatus.disconnecting:
        _rotationController.repeat();
        _pulseController.stop();
        _rippleController.stop();
        _stopConnectionTimer();
        break;
      case ConnectionStatus.disconnected:
      default:
        _pulseController.stop();
        _rotationController.stop();
        _rippleController.stop();
        _stopConnectionTimer();
        break;
    }
  }

  /// 启动连接时间定时器
  void _startConnectionTimer() {
    _connectionTimer?.cancel();
    _connectionTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          // 触发重建以更新连接时间显示
        });
      }
    });
  }

  /// 停止连接时间定时器
  void _stopConnectionTimer() {
    _connectionTimer?.cancel();
    _connectionTimer = null;
  }

  /// 处理连接切换
  Future<void> _handleConnectionToggle(AppState appState) async {
    try {
      // 检查并修复状态不一致问题
      _connectionManager.checkAndFixStateInconsistency();

      if (appState.connectionStatus == ConnectionStatus.connected) {
        _logService.info('ConnectionScreen', 'User requested disconnection');
        final success = await _connectionManager.disconnect();

        // 如果断开连接失败，只清理状态，不自动重试
        if (!success) {
          _logService.warning('ConnectionScreen', 'Disconnection failed, cleaning up state');
          _connectionManager.forceCleanupConnectionState();
        }
      } else if (appState.connectionStatus == ConnectionStatus.disconnected ||
                 appState.connectionStatus == ConnectionStatus.error) {
        // 处理连接请求 - 包括断开连接状态和错误状态
        if (appState.connectionStatus == ConnectionStatus.error) {
          _logService.info('ConnectionScreen', 'User requested connection retry after error');
        } else {
          _logService.info('ConnectionScreen', 'User requested connection');
        }

        final success = await _connectionManager.connect();

        // 如果连接失败，只清理状态，不自动重试，让用户自己选择
        if (!success) {
          _logService.warning('ConnectionScreen', 'Connection failed, cleaning up state, waiting for user retry');
          _connectionManager.forceCleanupConnectionState();
        }
      } else {
        // 如果当前状态是connecting、disconnecting或reconnecting，提示用户等待
        final statusName = appState.connectionStatus == ConnectionStatus.connecting
            ? "connecting"
            : appState.connectionStatus == ConnectionStatus.disconnecting
                ? "disconnecting"
                : "reconnecting";
        _logService.info('ConnectionScreen', 'Currently $statusName, please wait...');
      }
    } catch (e) {
      _logService.error('ConnectionScreen', 'Connection toggle failed', e);

      // 发生异常时，强制清理状态以避免UI锁定
      _connectionManager.forceCleanupConnectionState();
    }
  }

  /// 格式化速率（字节/秒转换为比特/秒，使用1000进制）
  String _formatSpeed(int bytesPerSecond) {
    // 将字节转换为比特（1字节 = 8比特）
    final bitsPerSecond = bytesPerSecond * 8;

    if (bitsPerSecond < 1000) return '${bitsPerSecond}bps';
    if (bitsPerSecond < 1000 * 1000) return '${(bitsPerSecond / 1000).toStringAsFixed(1)}kbps';
    if (bitsPerSecond < 1000 * 1000 * 1000) return '${(bitsPerSecond / (1000 * 1000)).toStringAsFixed(1)}Mbps';
    return '${(bitsPerSecond / (1000 * 1000 * 1000)).toStringAsFixed(1)}Gbps';
  }

  /// 获取延迟颜色（适配白色背景）- 不可达显示红色
  Color _getPingColorForWhiteBackground(int ping) {
    if (ping <= 0) {
      // 在白色背景下，不可达使用红色
      return AppColors.error;
    }
    // 其他情况使用正常颜色
    return PingColorUtils.getPingColor(ping, useNewColors: true);
  }

  /// 在对话框中测试服务器延迟（保持对话框打开）
  Future<void> _testServerLatencyInDialog(StateSetter setDialogState) async {
    // _logService.debug('ConnectionScreen', 'Starting latency test in dialog, current _isTestingLatency=$_isTestingLatency');

    if (_isTestingLatency) {
      // _logService.debug('ConnectionScreen', 'Latency test already in progress, ignoring request');
      return; // 防止重复触发
    }

    // _logService.debug('ConnectionScreen', 'Setting testing state to true');

    // 更新对话框状态
    setDialogState(() {
      _isTestingLatency = true;
    });

    // 同时更新主界面状态
    setState(() {
      _isTestingLatency = true;
    });

    final l10n = AppLocalizations.of(context)!;

    // 移除超时保护机制，依赖Swift端的5秒超时和Flutter端的8秒超时
    // Swift端并发ping操作会在5秒内完成，Flutter端AsyncOperationManager提供8秒超时保护

    try {
      // _logService.debug('ConnectionScreen', 'User triggered latency test in dialog');

      // 显示开始测试的通知
      _notificationService.showInfoNotification(l10n.testingLatency);
      // _logService.debug('ConnectionScreen', 'Displayed testing notification');

      // 调用连接管理器的ping方法
      // _logService.debug('ConnectionScreen', 'Calling connectionManager.pingServers()');
      await _connectionManager.pingServers();

      // _logService.debug('ConnectionScreen', 'Latency test request sent (dialog mode) - waiting for WebSocket events');

      // 注意：测试完成的通知将在WebSocket事件监听器中处理
      // 对话框状态也会在WebSocket监听器中重置
      // 超时保护定时器会在WebSocket事件监听器中被取消
    } catch (e) {
      _logService.error('ConnectionScreen', 'Latency test failed in dialog', e);

      // 重置对话框状态
      if (mounted) {
        // _logService.debug('ConnectionScreen', 'Resetting testing state due to error');
        setDialogState(() {
          _isTestingLatency = false;
        });

        // 重置主界面状态
        setState(() {
          _isTestingLatency = false;
        });
      }

      // 显示错误通知
      _notificationService.showErrorNotification(l10n.pingServersFailed(e.toString()));
    }
  }
}
