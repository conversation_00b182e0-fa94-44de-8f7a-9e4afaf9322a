// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      server_list_screen.dart
//  *
//  * DESCRIPTION :    服务器列表页面，显示所有可用服务器，支持搜索、排序、
//  *                  延迟测试和服务器连接功能
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  ******************************************************************************/

import 'dart:io';
import 'package:flutter/material.dart';

import '../models/server.dart';
import '../models/connection_status.dart';
import '../core/app_state.dart';
import '../core/dependency_injection.dart';
import '../services/api_service.dart';
import '../services/log_service.dart';
import '../services/language_service.dart';
import '../services/auth_service.dart';
import '../utils/simple_encryptor.dart';
import '../utils/constants.dart';
import '../utils/ping_color_utils.dart';
import '../utils/design_system.dart';
import '../generated/l10n/app_localizations.dart';

class ServerListScreen extends StatefulWidget {
  const ServerListScreen({super.key});

  @override
  State<ServerListScreen> createState() => _ServerListScreenState();
}

class _ServerListScreenState extends State<ServerListScreen> {
  List<Server> _servers = [];
  bool _isLoading = true;
  bool _isPinging = false;
  String _errorMessage = '';
  String _searchQuery = '';
  String _sortBy = 'ping';
  bool _sortAscending = true;
  DateTime? _lastPingTime; // 记录最后一次ping的时间

  @override
  void initState() {
    super.initState();
    _loadServers();
  }

  // 加载服务器列表
  Future<void> _loadServers() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    final apiService = serviceLocator<ApiService>();
    final logService = serviceLocator<LogService>();

    try {
      final servers = await apiService.getServers();

      setState(() {
        _servers = servers;
        _isLoading = false;
      });

      _sortServers();

      // logService.debug('ServerList', 'Loaded ${servers.length} servers');
    } catch (e) {
      logService.error('ServerList', 'Failed to load servers', e);

      setState(() {
        _errorMessage = '加载服务器列表失败: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  // 测试服务器延迟
  Future<void> _pingServers() async {
    // 确保不会重复触发
    if (_isPinging) {
      return;
    }

    setState(() {
      _isPinging = true;
    });

    final apiService = serviceLocator<ApiService>();
    final logService = serviceLocator<LogService>();

    try {
      await apiService.pingServers();

      // 只有当组件仍然挂载时才继续
      if (mounted) {
        await _loadServers(); // 重新加载服务器列表以获取更新的延迟
        // logService.debug('ServerList', 'Ping completed');
      }
    } catch (e) {
      logService.error('ServerList', 'Ping failed', e);

      // 只有当组件仍然挂载时才更新UI
      if (mounted) {
        final l10n = AppLocalizations.of(context)!;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(l10n.pingServersFailed(e.toString()))),
        );
      }
    } finally {
      // 确保状态被重置，即使在异常情况下
      if (mounted) {
        setState(() {
          _isPinging = false;
        });
      }
    }
  }

  // 排序服务器列表
  void _sortServers() {
    setState(() {
      _servers.sort((a, b) {
        // 处理ping值为-1的情况（未知延迟）
        final aPing = a.ping < 0 ? 9999 : a.ping;
        final bPing = b.ping < 0 ? 9999 : b.ping;

        // ping失败的服务器(ping=0)排在最后
        if (aPing == 0 && bPing > 0) return _sortAscending ? 1 : -1;
        if (aPing > 0 && bPing == 0) return _sortAscending ? -1 : 1;
        if (aPing == 0 && bPing == 0) return 0;

        return _sortAscending
            ? aPing.compareTo(bPing)
            : bPing.compareTo(aPing);
      });
    });
  }

  // 改变排序方式
  void _changeSortOrder(String sortBy) {
    setState(() {
      if (_sortBy == sortBy) {
        _sortAscending = !_sortAscending;
      } else {
        _sortBy = sortBy;
        _sortAscending = true;
      }
      _sortServers();
    });
  }



  // 连接到服务器
  Future<void> _connect(Server server) async {
    final apiService = serviceLocator<ApiService>();
    final appState = serviceLocator<AppState>();
    final logService = serviceLocator<LogService>();
    final languageService = serviceLocator<LanguageService>();
    final authService = serviceLocator<AuthService>();

    // 设置选中的服务器
    appState.selectServer(server, source: ServerSelectionSource.userSelection);

    try {
      final serverDisplayName = server.getDisplayNameByLocale(languageService.isEnglish);
      logService.info('ServerList', 'User selected server for connection: $serverDisplayName');

      // 获取用户认证信息
      final username = authService.username;
      final password = authService.password;

      if (username.isEmpty || password.isEmpty) {
        throw Exception('User credentials not available');
      }

      // 根据平台决定密码格式，与login方法保持一致
      String connectPassword;
      if (Platform.isIOS || Platform.isMacOS || Platform.isAndroid) {
        // iOS/macOS/Android: 传递明文密码
        connectPassword = password;
      } else {
        // Windows等其他平台: 传递加密密码保持兼容性
        final encryptor = SimpleEncryptor();
        connectPassword = await encryptor.encrypt(password);
      }

      await apiService.connect(server.id, username, connectPassword);

      // 返回主屏幕
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      logService.error('ServerList', 'Failed to connect', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('连接失败: ${e.toString()}')),
        );
      }
    }
  }

  // 筛选服务器列表
  List<Server> _getFilteredServers() {
    if (_searchQuery.isEmpty) {
      return _servers;
    }

    final query = _searchQuery.toLowerCase();
    return _servers.where((server) {
      return server.name.toLowerCase().contains(query) ||
          server.nameEn.toLowerCase().contains(query) ||
          server.serverName.toLowerCase().contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.serverList),
        backgroundColor: AppColors.primary,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadServers,
            tooltip: l10n.refreshServers,
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.cardBackground,
              AppColors.cardBackground.withValues(alpha: 0.9),
            ],
          ),
        ),
        child: Column(
          children: [
            // 搜索和排序区域
            Container(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Column(
                children: [
                  // 搜索栏
                  TextField(
                    decoration: InputDecoration(
                      hintText: 'Search servers...', // TODO: Add to localization
                      prefixIcon: const Icon(Icons.search, color: AppColors.primary),
                      suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear, color: AppColors.textSecondary),
                            onPressed: () {
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                      filled: true,
                      fillColor: Colors.white,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: AppColors.divider, width: 1.5),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: AppColors.divider, width: 1.5),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: AppColors.primary, width: 1.5),
                      ),
                      contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  // 排序选项
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.sort, size: 18, color: AppColors.textSecondary),
                          SizedBox(width: 8),
                          Text(
                            '排序方式:',
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          DropdownButton<String>(
                            value: _sortBy,
                            underline: Container(
                              height: 1,
                              color: AppColors.divider,
                            ),
                            icon: Icon(
                              _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                              size: 18,
                              color: AppColors.primary,
                            ),
                            items: const [
                              DropdownMenuItem(value: 'ping', child: Text('Latency')), // TODO: Add to localization
                            ],
                            onChanged: (value) {
                              if (value != null) {
                                _changeSortOrder(value);
                              }
                            },
                          ),
                          IconButton(
                            icon: Icon(
                              _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                              color: AppColors.primary,
                              size: 18,
                            ),
                            onPressed: () => _changeSortOrder(_sortBy),
                            tooltip: _sortAscending ? 'Sort descending' : 'Sort ascending', // TODO: Add to localization
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // 服务器列表
            Expanded(
              child: _isLoading
                  ? const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                      ),
                    )
                  : _errorMessage.isNotEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.error_outline,
                                size: 48,
                                color: AppColors.error.withValues(alpha: 0.7),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                _errorMessage,
                                style: const TextStyle(
                                  color: AppColors.textSecondary,
                                  fontSize: 16,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 24),
                              ElevatedButton.icon(
                                onPressed: _loadServers,
                                icon: const Icon(Icons.refresh),
                                label: Text(l10n.retry),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.primary,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      : _getFilteredServers().isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.search_off,
                                    size: 48,
                                    color: AppColors.textSecondary.withValues(alpha: 0.7),
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    l10n.noServersAvailable,
                                    style: const TextStyle(
                                      color: AppColors.textSecondary,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : RefreshIndicator(
                              onRefresh: () async {
                                await _loadServers();
                              },
                              color: AppColors.primary,
                              child: ListView.builder(
                                padding: const EdgeInsets.fromLTRB(16, 8, 16, 80), // 底部留出空间，避免被FAB遮挡
                                itemCount: _getFilteredServers().length,
                                itemBuilder: (context, index) {
                                  final server = _getFilteredServers()[index];
                                  return _buildServerItem(context, server);
                                },
                              ),
                            ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        // 即使在_isPinging状态下也允许点击，但会显示提示信息
        onPressed: () {
          if (_isPinging) {
            // 显示提示信息
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('正在测试服务器延迟，请稍候...'),
                duration: Duration(seconds: 2),
              ),
            );

            // 检查是否已经超过10秒，如果是则强制重置状态（Swift端5秒+缓冲）
            final now = DateTime.now();
            if (_lastPingTime != null &&
                now.difference(_lastPingTime!).inSeconds > 10) {
              setState(() {
                _isPinging = false;
              });

              // 显示提示信息
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('延迟测试已重置，可以重新测试'),
                  duration: Duration(seconds: 2),
                ),
              );
            }
          } else {
            // 记录开始ping的时间
            _lastPingTime = DateTime.now();
            _pingServers();
          }
        },
        backgroundColor: _isPinging ? AppColors.textSecondary : AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 4,
        tooltip: l10n.testLatency,
        child: _isPinging
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 2,
                ),
              )
            : const Icon(Icons.network_check),
      ),
    );
  }

  // 构建服务器列表项
  Widget _buildServerItem(BuildContext context, Server server) {
    final appState = serviceLocator<AppState>();
    final languageService = serviceLocator<LanguageService>();
    final l10n = AppLocalizations.of(context)!;
    final isSelected = appState.selectedServer?.id == server.id;
    final isConnected = appState.connectionStatus == ConnectionStatus.connected && isSelected;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 6),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
          ? const BorderSide(color: AppColors.primary, width: 1.5)
          : BorderSide.none,
      ),
      child: InkWell(
        onTap: () {
          final appState = serviceLocator<AppState>();
          appState.selectServer(server, source: ServerSelectionSource.userSelection);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 服务器名称和状态
              Row(
                children: [
                  _buildSignalIcon(server.ping),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          server.getDisplayNameByLocale(languageService.isEnglish),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: isSelected ? AppColors.primary : AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          server.serverName,
                          style: const TextStyle(
                            fontSize: 13,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // 延迟显示
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getPingColor(server.ping).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: _getPingColor(server.ping).withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.speed,
                          size: 14,
                          color: _getPingColor(server.ping),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          PingColorUtils.getPingText(
                            server.ping,
                            unreachableText: AppLocalizations.of(context)!.unreachable,
                          ),
                          style: TextStyle(
                            color: _getPingColor(server.ping),
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // 底部操作区域
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 默认服务器标记
                  if (server.isAuto)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.star,
                            size: 14,
                            color: AppColors.primary,
                          ),
                          SizedBox(width: 4),
                          Text(
                            'Default Server', // TODO: Add to localization
                            style: TextStyle(
                              color: AppColors.primary,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    )
                  else
                    const SizedBox(), // 占位

                  // 连接按钮
                  if (isSelected && !isConnected)
                    ElevatedButton.icon(
                      onPressed: () => _connect(server),
                      icon: const Icon(Icons.power_settings_new, size: 16),
                      label: Text(l10n.connect),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    )
                  else if (isConnected)
                    ElevatedButton.icon(
                      onPressed: null,
                      icon: const Icon(Icons.check_circle, size: 16),
                      label: Text(l10n.connected),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.connected,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    )
                  else
                    TextButton.icon(
                      onPressed: () {
                        final appState = serviceLocator<AppState>();
                        appState.selectServer(server, source: ServerSelectionSource.userSelection);
                      },
                      icon: const Icon(Icons.check, size: 16),
                      label: Text(l10n.selectServer),
                      style: TextButton.styleFrom(
                        foregroundColor: AppColors.primary,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建信号强度图标
  Widget _buildSignalIcon(int ping) {
    Color color;
    IconData icon;

    if (ping <= 0) {
      color = AppColors.textSecondary;
      icon = Icons.signal_wifi_off;
    } else if (ping < 100) {
      color = AppColors.connected;
      icon = Icons.signal_wifi_4_bar;
    } else if (ping < 200) {
      color = AppColors.connecting;
      icon = Icons.signal_wifi_4_bar;
    } else {
      color = AppColors.error;
      icon = Icons.signal_wifi_0_bar;
    }

    return Container(
      width: 36,
      height: 36,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Icon(
          icon,
          color: color,
          size: 20,
        ),
      ),
    );
  }

  // 获取延迟颜色 - 使用统一的延迟颜色标准，适配白色背景
  Color _getPingColor(int ping) {
    if (ping <= 0) {
      // 在白色背景下，不可达使用红色确保可见性
      return AppColors.error;
    }
    return PingColorUtils.getPingColor(ping, useNewColors: false);
  }
}
