/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      about_screen.dart
///
/// DESCRIPTION :    About screen displaying application information, version, and legal links
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../services/language_service.dart';
import '../utils/constants.dart';
import '../utils/device_info.dart';
import '../utils/design_system.dart';
import '../widgets/svg_asset_widget.dart';
import '../generated/l10n/app_localizations.dart';
import '../services/font_service.dart';
import '../core/dependency_injection.dart';

/// AboutScreen
///
/// PURPOSE:
///     A screen that displays application information including version, device ID,
///     legal documents, and contact information with external link functionality.
///
/// FEATURES:
///     - Application logo and branding display
///     - Version information and device ID retrieval
///     - Terms of service and privacy policy links
///     - Official website link with external browser launch
///     - Language-aware font styling and content
///     - Error handling for link opening and device ID retrieval
///     - Responsive card-based layout design
///     - Consistent styling with other application screens
///
/// USAGE:
///     Navigator.push(context, MaterialPageRoute(builder: (context) => AboutScreen()))
class AboutScreen extends StatefulWidget {
  /// AboutScreen constructor
  ///
  /// DESCRIPTION:
  ///     Creates an about screen widget for displaying application information.
  const AboutScreen({Key? key}) : super(key: key);

  @override
  State<AboutScreen> createState() => _AboutScreenState();
}

/// _AboutScreenState
///
/// PURPOSE:
///     State management for AboutScreen widget.
///     Handles device ID retrieval, external link opening, and UI state management.
///
/// FEATURES:
///     - Device ID asynchronous loading with error handling
///     - External URL launching for legal documents and website
///     - Language-aware font styling
///     - Responsive UI state management
class _AboutScreenState extends State<AboutScreen> {
  /// Application version string from constants
  final String version = kAppVersion;

  /// Copyright information text
  final String copyright = '© 2025 PANABIT corporation. All rights reserved.';

  /// Official website URL
  final String website = 'https://www.panabit.com';

  /// Device ID string, updated asynchronously
  String? _deviceId;

  /// Flag to track if device ID loading has been initiated
  bool _deviceIdLoadingStarted = false;

  /// _loadDeviceId
  ///
  /// DESCRIPTION:
  ///     Asynchronously loads the device ID and updates the UI state.
  ///     Shows loading text initially, then displays the device ID or error message.
  Future<void> _loadDeviceId() async {
    if (!mounted || _deviceIdLoadingStarted) return;

    _deviceIdLoadingStarted = true;

    // Get l10n from context safely
    final l10n = AppLocalizations.of(context)!;

    setState(() {
      _deviceId = l10n.gettingDeviceId;
    });

    try {
      final deviceInfo = DeviceInfo();
      final deviceId = await deviceInfo.getDeviceId();
      if (mounted) {
        setState(() {
          _deviceId = deviceId;
        });
      }
    } catch (e) {
      if (mounted) {
        final l10nError = AppLocalizations.of(context)!;
        setState(() {
          _deviceId = l10nError.getDeviceIdFailed;
        });
      }
    }
  }

  /// _openTermsOfService
  ///
  /// DESCRIPTION:
  ///     Opens the terms of service URL in an external browser.
  ///     Uses language-specific URLs and handles launch errors.
  ///
  /// PARAMETERS:
  ///     context - Build context for showing error messages
  Future<void> _openTermsOfService(BuildContext context) async {
    final languageService = Provider.of<LanguageService>(context, listen: false);
    final url = languageService.isChinese ? LicenseUrls.termsServiceZh : LicenseUrls.termsServiceEn;

    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context)!.openLinkFailed)),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.openLinkFailedWithError)),
        );
      }
    }
  }

  /// _openPrivacyPolicy
  ///
  /// DESCRIPTION:
  ///     Opens the privacy policy URL in an external browser.
  ///     Uses language-specific URLs and handles launch errors.
  ///
  /// PARAMETERS:
  ///     context - Build context for showing error messages
  Future<void> _openPrivacyPolicy(BuildContext context) async {
    final languageService = Provider.of<LanguageService>(context, listen: false);
    final url = languageService.isChinese ? LicenseUrls.privacyPolicyZh : LicenseUrls.privacyPolicyEn;

    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context)!.openLinkFailed)),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.openLinkFailedWithError)),
        );
      }
    }
  }

  /// _openOfficialWebsite
  ///
  /// DESCRIPTION:
  ///     Opens the official website URL in an external browser.
  ///     Handles launch errors with user-friendly error messages.
  ///
  /// PARAMETERS:
  ///     context - Build context for showing error messages
  Future<void> _openOfficialWebsite(BuildContext context) async {
    try {
      if (await canLaunchUrl(Uri.parse(website))) {
        await launchUrl(Uri.parse(website), mode: LaunchMode.externalApplication);
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context)!.openLinkFailed)),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.openLinkFailedWithError)),
        );
      }
    }
  }

  /// _getTitleStyle
  ///
  /// DESCRIPTION:
  ///     Creates a title text style with language-aware font selection.
  ///     Maintains consistency with statistics screen styling.
  ///
  /// RETURNS:
  ///     TextStyle object for section titles
  TextStyle _getTitleStyle() {
    try {
      final fontService = serviceLocator<FontService>();
      return fontService.createTextStyleWithMappedWeight(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: const Color(0xFF131211), // 与统计界面保持一致
      );
    } catch (e) {
      return const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: Color(0xFF131211),
        fontFamilyFallback: ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'],
      );
    }
  }

  /// _getTextStyle
  ///
  /// DESCRIPTION:
  ///     Creates a text style with language-aware font selection and customizable properties.
  ///     Provides fallback fonts for better cross-platform compatibility.
  ///
  /// PARAMETERS:
  ///     fontSize - Font size for the text style
  ///     fontWeight - Font weight (optional, default: FontWeight.normal)
  ///     color - Text color (optional, default: Colors.black)
  ///
  /// RETURNS:
  ///     TextStyle object with appropriate font and styling
  TextStyle _getTextStyle({
    required double fontSize,
    FontWeight? fontWeight,
    Color? color,
  }) {
    try {
      final fontService = serviceLocator<FontService>();
      return fontService.createTextStyleWithMappedWeight(
        fontSize: fontSize,
        fontWeight: fontWeight ?? FontWeight.normal,
        color: color ?? Colors.black,
      );
    } catch (e) {
      return TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight ?? FontWeight.normal,
        color: color ?? Colors.black,
        fontFamilyFallback: const ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'],
      );
    }
  }

  /// build
  ///
  /// DESCRIPTION:
  ///     Builds the about screen widget with application information, legal links, and contact details.
  ///     Creates a scrollable layout with cards for different information sections.
  ///
  /// PARAMETERS:
  ///     context - Build context for the widget
  ///
  /// RETURNS:
  ///     Widget tree representing the about screen
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    // Start loading device ID if not already started
    if (!_deviceIdLoadingStarted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadDeviceId();
      });
    }

    // 设置状态栏样式为深色（适用于白色背景）
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);

    return Scaffold(
      backgroundColor: AppColors.backgroundStart,
      body: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: 12),
            // 应用图标 - 使用与用户界面相同的盾牌Logo
            DesignSystemSvgs.shieldLogo(
              width: 80,
              height: 90,
            ),
            const SizedBox(height: 20),

            // 应用信息卡片
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: const [
                  BoxShadow(
                    color: Color(0x1A66809E), // rgba(102, 128, 158, 0.1) - 与统计界面保持一致
                    blurRadius: 4,
                    offset: Offset(0, 0),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  children: [
                    _buildInfoItem(context, Icons.info_outline, l10n.appName, 'Panabit iWAN'),
                    const Divider(
                      height: 1,
                      thickness: 1,
                      color: Color(0xFFEBEBEB), // 与设置界面分割线样式保持一致
                      indent: 16,
                      endIndent: 16,
                    ),
                    const SizedBox(height: 15),
                    _buildInfoItem(context, Icons.verified_user_outlined, l10n.versionNumber, version),
                    const Divider(
                      height: 1,
                      thickness: 1,
                      color: Color(0xFFEBEBEB), // 与设置界面分割线样式保持一致
                      indent: 16,
                      endIndent: 16,
                    ),
                    const SizedBox(height: 15),
                    _buildInfoItem(context, Icons.device_unknown, l10n.deviceId, _deviceId ?? l10n.gettingDeviceId),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 协议条款与联系方式标题 - 与统计界面标题样式保持一致，左对齐
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  l10n.agreementsAndContact,
                  style: _getTitleStyle(),
                ),
              ),
            ),

            const SizedBox(height: 12), // 缩小间距为原来的一半

            // 协议条款卡片
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: const [
                  BoxShadow(
                    color: Color(0x1A66809E), // rgba(102, 128, 158, 0.1) - 与统计界面保持一致
                    blurRadius: 4,
                    offset: Offset(0, 0),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    InkWell(
                      onTap: () => _openTermsOfService(context),
                      child: _buildInfoItem(context, Icons.description, l10n.termsOfService, l10n.clickToView, isLink: true),
                    ),
                    const Divider(
                      height: 1,
                      thickness: 1,
                      color: Color(0xFFEBEBEB), // 与设置界面分割线样式保持一致
                      indent: 16,
                      endIndent: 16,
                    ),
                    const SizedBox(height: 15),
                    InkWell(
                      onTap: () => _openPrivacyPolicy(context),
                      child: _buildInfoItem(context, Icons.privacy_tip, l10n.privacyPolicy, l10n.clickToView, isLink: true),
                    ),
                    const Divider(
                      height: 1,
                      thickness: 1,
                      color: Color(0xFFEBEBEB), // 与设置界面分割线样式保持一致
                      indent: 16,
                      endIndent: 16,
                    ),
                    const SizedBox(height: 15),
                    InkWell(
                      onTap: () => _openOfficialWebsite(context),
                      child: _buildInfoItem(context, Icons.language, l10n.officialWebsite, website, isLink: true),
                    ),
                  ],
                ),
              ),
            ),

            const Spacer(),

            // 版权信息
            Text(
              copyright,
              style: _getTextStyle(
                fontSize: 11,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
          ],
        ),
      ),
    );
  }

  /// _buildInfoItem
  ///
  /// DESCRIPTION:
  ///     Builds an information item row with icon, title, value, and optional link indicator.
  ///     Creates a horizontal layout for displaying various types of information.
  ///
  /// PARAMETERS:
  ///     context - Build context for the widget
  ///     icon - IconData for the information type
  ///     title - String title for the information field
  ///     value - String value to display
  ///     isLink - Whether this item is a clickable link (default: false)
  ///
  /// RETURNS:
  ///     Row widget containing the formatted information item
  Widget _buildInfoItem(BuildContext context, IconData icon, String title, String value, {bool isLink = false}) {
    return Row(
      children: [
        Icon(
          icon,
          size: 18,
          color: AppColors.primary, // 绿色主题色 05683E
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: _getTextStyle(
                  fontSize: 11,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 1),
              Text(
                value,
                style: _getTextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  color: isLink ? AppColors.primary : Colors.black, // 绿色主题色 05683E
                ),
              ),
            ],
          ),
        ),
        if (isLink)
          Icon(
            Icons.arrow_forward_ios,
            size: 12,
            color: Colors.grey[400],
          ),
      ],
    );
  }
}
