/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// ******************************************************************************
/// FILE NAME :      settings_screen.dart
///
/// DESCRIPTION :    Settings screen for application and routing configuration management
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../models/routing_settings.dart';
import '../models/connection_status.dart';
import '../core/app_state.dart';
import '../services/log_service.dart';
import '../services/notification_service.dart';
import '../services/auto_start_service.dart';
import '../services/routing_settings_service.dart';
import '../services/language_service.dart';
import '../services/connection_manager.dart';
import '../utils/design_system.dart';
import '../widgets/routing_mode_selector.dart';
import '../generated/l10n/app_localizations.dart';
import '../core/dependency_injection.dart';
import '../services/font_service.dart';

/// SettingsScreen
///
/// PURPOSE:
///     Settings screen that provides configuration options for application and routing settings.
///     Manages language preferences, auto-start functionality, and routing mode configurations.
///
/// FEATURES:
///     - Language selection with immediate switching (Chinese/English)
///     - Auto-start application setting with system integration
///     - Routing mode configuration with custom route management
///     - Real-time settings validation and application
///     - Error handling with user-friendly notifications
///     - Language-aware font styling and text rendering
///     - Consistent design system integration
///     - Settings change tracking and apply button management
///
/// USAGE:
///     Navigator.push(context, MaterialPageRoute(builder: (context) => SettingsScreen()))
class SettingsScreen extends StatefulWidget {
  /// SettingsScreen constructor
  ///
  /// DESCRIPTION:
  ///     Creates a settings screen widget for application configuration.
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

/// _SettingsScreenState
///
/// PURPOSE:
///     State management for SettingsScreen widget.
///     Handles settings loading, validation, and application with change tracking.
///
/// FEATURES:
///     - Settings loading state management
///     - Original settings tracking for change detection
///     - Language-aware text styling with font service integration
///     - Real-time settings validation and error handling
class _SettingsScreenState extends State<SettingsScreen> {
  /// Loading state for routing settings operations
  bool _isLoadingRoutingSettings = false;

  /// Loading state for application settings operations
  bool _isLoadingAppSettings = false;

  /// Original settings snapshot for change detection
  RoutingSettingsModel? _originalSettings;

  /// _settingItemTitleStyle
  ///
  /// DESCRIPTION:
  ///     Creates a standardized text style for setting item titles.
  ///     Uses language-aware font selection with fallback fonts.
  ///
  /// RETURNS:
  ///     TextStyle object for setting item titles
  TextStyle get _settingItemTitleStyle {
    try {
      final fontService = serviceLocator<FontService>();
      return fontService.createTextStyleWithMappedWeight(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: AppColors.textPrimary,
        height: 1.43, // 20/14
      );
    } catch (e) {
      return const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: AppColors.textPrimary,
        fontFamilyFallback: ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'],
        height: 1.43,
        decoration: TextDecoration.none,
      );
    }
  }

  /// _sectionTitleStyle
  ///
  /// DESCRIPTION:
  ///     Creates a standardized text style for section titles.
  ///     Uses language-aware font selection with proper typography settings.
  ///
  /// RETURNS:
  ///     TextStyle object for section titles
  TextStyle get _sectionTitleStyle {
    try {
      final fontService = serviceLocator<FontService>();
      return fontService.createTextStyleWithMappedWeight(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: AppColors.textPrimary,
        height: 1.33, // 24/18
        letterSpacing: 0.0,
        textBaseline: TextBaseline.alphabetic,
      );
    } catch (e) {
      return const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: AppColors.textPrimary,
        fontFamilyFallback: ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'],
        height: 1.33,
        decoration: TextDecoration.none,
        letterSpacing: 0.0,
        textBaseline: TextBaseline.alphabetic,
      );
    }
  }

  /// _dropdownItemStyle
  ///
  /// DESCRIPTION:
  ///     Creates a standardized text style for dropdown menu items.
  ///     Uses language-aware font selection with fallback fonts.
  ///
  /// RETURNS:
  ///     TextStyle object for dropdown menu items
  TextStyle get _dropdownItemStyle {
    try {
      final fontService = serviceLocator<FontService>();
      return fontService.createTextStyleWithMappedWeight(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: AppColors.textPrimary,
        height: 1.43, // 20/14
      );
    } catch (e) {
      return const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: AppColors.textPrimary,
        fontFamilyFallback: ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'],
        height: 1.43,
        decoration: TextDecoration.none,
      );
    }
  }

  /// _buttonTextStyle
  ///
  /// DESCRIPTION:
  ///     Creates a standardized text style for button text.
  ///     Uses language-aware font selection with proper weight and color.
  ///
  /// RETURNS:
  ///     TextStyle object for button text
  TextStyle get _buttonTextStyle {
    try {
      final fontService = serviceLocator<FontService>();
      return fontService.createTextStyleWithMappedWeight(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: Colors.white,
        height: 1.25, // 20/16
        leadingDistribution: TextLeadingDistribution.even,
      );
    } catch (e) {
      return const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: Colors.white,
        fontFamilyFallback: ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'],
        height: 1.25,
        decoration: TextDecoration.none,
        leadingDistribution: TextLeadingDistribution.even,
      );
    }
  }

  /// initState
  ///
  /// DESCRIPTION:
  ///     Initializes the state and starts settings loading process.
  @override
  void initState() {
    super.initState();
    _initializeSettings();
  }

  /// _initializeSettings
  ///
  /// DESCRIPTION:
  ///     Initializes all settings by loading routing and auto-start configurations.
  ///     Creates a snapshot of original settings for change detection.
  Future<void> _initializeSettings() async {
    // 并行加载路由设置和自动启动设置
    await Future.wait([
      _loadRoutingSettings(),
      _loadAutoStartSetting(),
    ]);

    // 确保原始设置完整初始化
    if (!mounted) return;
    final routingSettingsModel = Provider.of<RoutingSettingsModel>(context, listen: false);
    _originalSettings ??= RoutingSettingsModel();
    _originalSettings!.updateSettings(
      mode: routingSettingsModel.mode,
      customRoutes: routingSettingsModel.customRoutes,
      autoStart: routingSettingsModel.autoStart,
    );
  }

  /// build
  ///
  /// DESCRIPTION:
  ///     Builds the settings screen widget with application and routing configuration options.
  ///     Provides comprehensive error handling and loading states.
  ///
  /// PARAMETERS:
  ///     context - Build context for the widget
  ///
  /// RETURNS:
  ///     Widget tree representing the settings screen
  @override
  Widget build(BuildContext context) {
    try {
      final l10n = AppLocalizations.of(context);
      if (l10n == null) {
        return Container(
          color: AppColors.backgroundStart,
          width: double.infinity,
          height: double.infinity,
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
                SizedBox(height: 16),
                Text(
                  'Loading localizations...',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                    fontFamilyFallback: ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'],
                  ),
                ),
              ],
            ),
          ),
        );
      }

      // 设置状态栏样式为深色（适用于白色背景）
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);

      return Container(
        // FCF6F1 背景色，与登录连接界面保持一致，占满整个屏幕
        color: AppColors.backgroundStart,
        width: double.infinity,
        height: double.infinity,
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(DesignSystem.spacing32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 应用设置部分
                _buildAppSettingsTitle(l10n),
                const SizedBox(height: 12),
                _buildAppSettingsCard(l10n),
                const SizedBox(height: 24),

                // 路由设置部分
                _buildRoutingSettingsTitle(l10n),
                const SizedBox(height: 12),
                _buildRoutingSettingsCard(),
                const SizedBox(height: 24),

                // 应用按钮 - 仅在有路由设置变更时显示
                Consumer<RoutingSettingsModel>(
                  builder: (context, routingSettingsModel, child) {
                    // 只检查路由设置变更，自动启动设置立即生效无需应用按钮
                    bool hasRoutingChanges = _hasRoutingChanges(routingSettingsModel);

                    if (hasRoutingChanges) {
                      return Column(
                        children: [
                          _buildApplyButton(l10n),
                          const SizedBox(height: 32),
                        ],
                      );
                    }
                    return const SizedBox(height: 32);
                  },
                ),
              ],
            ),
          ),
        ),
      );
    } catch (e, stackTrace) {
      final logService = serviceLocator<LogService>();
      logService.error('Settings', 'Error in build(): $e', e);
      logService.warning('Settings', 'Stack trace: $stackTrace');

      // 返回错误状态的UI
      return Container(
        color: AppColors.backgroundStart,
        width: double.infinity,
        height: double.infinity,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 48,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Settings Error: $e',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.red,
                  fontFamilyFallback: ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
  }



  /// 检测是否应该显示自动启动设置
  ///
  /// DESCRIPTION:
  ///     判断当前平台是否应该显示自动启动设置选项
  ///     移动平台（iOS、Android、iOS应用在macOS上运行）隐藏该选项
  ///     桌面平台（Windows、原生macOS、Linux）显示该选项
  ///
  /// RETURNS:
  ///     Future<bool> - true表示应该显示自动启动设置，false表示应该隐藏
  Future<bool> _shouldShowAutoStartSetting() async {
    // 如果是Windows或Linux，直接显示
    if (Platform.isWindows || Platform.isLinux) {
      return true;
    }

    // 如果是Android，隐藏
    if (Platform.isAndroid) {
      return false;
    }

    // 如果是iOS，隐藏（包括原生iOS和iOS应用在macOS上运行）
    if (Platform.isIOS) {
      // iOS平台都视为移动平台，隐藏自动启动设置
      return false;
    }

    // 如果是原生macOS，显示
    if (Platform.isMacOS) {
      return true;
    }

    // 其他平台默认隐藏
    return false;
  }

  // 构建应用设置卡片
  Widget _buildAppSettingsCard(AppLocalizations l10n) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
        boxShadow: const [
          BoxShadow(
            color: Color(0x1A66809E), // rgba(102, 128, 158, 0.1) - 与统计界面保持一致
            blurRadius: 4,
            offset: Offset(0, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // 语言设置
          Consumer<LanguageService>(
            builder: (context, languageService, child) {
              return ListTile(
                title: Text(
                  l10n.languageSettings,
                  style: _settingItemTitleStyle, // 使用标准化的设置项标题样式
                ),
                trailing: _buildLanguageToggle(languageService, l10n),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              );
            },
          ),

          // 自动启动设置 - 仅在桌面平台显示
          FutureBuilder<bool>(
            future: _shouldShowAutoStartSetting(),
            builder: (context, snapshot) {
              // 如果正在检测平台或者不应该显示自动启动设置，则不显示
              if (snapshot.connectionState == ConnectionState.waiting ||
                  !(snapshot.data ?? false)) {
                return const SizedBox.shrink();
              }

              return Column(
                children: [
                  // 分隔线
                  Divider(
                    height: 1,
                    thickness: 1,
                    color: AppColors.textSecondary.withValues(alpha: 0.2),
                    indent: 16,
                    endIndent: 16,
                  ),
                  // 自动启动设置
                  Consumer<RoutingSettingsModel>(
                    builder: (context, routingSettingsModel, child) {
                      return Theme(
                        data: Theme.of(context).copyWith(
                          switchTheme: SwitchThemeData(
                            thumbColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
                              return Colors.white; // 圆点始终为白色
                            }),
                            trackColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
                              if (states.contains(WidgetState.selected)) {
                                return AppColors.primary; // 开启时的轨道颜色（橙色）
                              }
                              return Colors.grey.shade200; // 关闭时的轨道颜色（浅灰色）
                            }),
                            trackOutlineColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
                              if (states.contains(WidgetState.selected)) {
                                return AppColors.primary; // 开启时的边框颜色
                              }
                              return AppColors.textSecondary; // 关闭时的边框颜色（与语言设置单选框保持一致）
                            }),
                          ),
                        ),
                        child: SwitchListTile(
                          title: Text(
                            l10n.autoStart,
                            style: _settingItemTitleStyle, // 使用标准化的设置项标题样式，与语言设置完全一致
                          ),
                          value: routingSettingsModel.autoStart,
                          onChanged: (value) async {
                            final autoStartService = serviceLocator<AutoStartService>();
                            final notificationService = serviceLocator<NotificationService>();

                            // 立即更新UI状态
                            routingSettingsModel.autoStart = value;

                            // 异步保存设置并显示通知
                            try {
                              await autoStartService.setAutoStartSetting(value);

                              // 更新原始设置
                              if (_originalSettings != null) {
                                _originalSettings!.autoStart = value;
                              }

                              // 显示成功通知
                              notificationService.showSuccessNotification(
                                value ? l10n.autoStartEnabled : l10n.autoStartDisabled
                              );
                            } catch (e) {
                              // 如果保存失败，恢复原来的状态
                              routingSettingsModel.autoStart = !value;

                              // 显示错误通知
                              notificationService.showErrorNotification(
                                '${l10n.saveAutoStartSettingFailed}: ${e.toString()}'
                              );
                            }
                          },
                          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                        ),
                      );
                    },
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  // 构建路由设置卡片
  Widget _buildRoutingSettingsCard() {
    return Consumer<RoutingSettingsModel>(
      builder: (context, routingSettingsModel, child) {
        return RoutingModeSelector(
          settings: routingSettingsModel,
          onSettingsChanged: (settings) {
            // 路由设置变更时触发UI更新，以便显示应用按钮
            setState(() {
              // 触发UI重建，让应用按钮的显示逻辑重新计算
            });
          },
          isLoading: _isLoadingRoutingSettings,
        );
      },
    );
  }

  // 构建语言切换组件 - 使用单选按钮样式，类似路由模式选择
  Widget _buildLanguageToggle(LanguageService languageService, AppLocalizations l10n) {
    final currentLanguage = languageService.locale.languageCode;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 中文选项
        GestureDetector(
          onTap: () async {
            if (currentLanguage != 'zh') {
              final l10n = AppLocalizations.of(context)!;
              await languageService.setLanguage('zh');
              if (mounted) {
                final notificationService = serviceLocator<NotificationService>();
                notificationService.showSuccessNotification(l10n.languageChanged);
              }
            }
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: currentLanguage == 'zh' ? AppColors.primary : AppColors.textSecondary,
                    width: 2,
                  ),
                ),
                child: currentLanguage == 'zh'
                    ? Center(
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.primary,
                          ),
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: 8),
              Text(
                '中文',
                style: _dropdownItemStyle.copyWith(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
        const SizedBox(width: 24),
        // English选项
        GestureDetector(
          onTap: () async {
            if (currentLanguage != 'en') {
              final l10n = AppLocalizations.of(context)!;
              await languageService.setLanguage('en');
              if (mounted) {
                final notificationService = serviceLocator<NotificationService>();
                notificationService.showSuccessNotification(l10n.languageChanged);
              }
            }
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: currentLanguage == 'en' ? AppColors.primary : AppColors.textSecondary,
                    width: 2,
                  ),
                ),
                child: currentLanguage == 'en'
                    ? Center(
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.primary,
                          ),
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: 8),
              Text(
                'English',
                style: _dropdownItemStyle.copyWith(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
      ],
    );
  }



  // 构建应用设置标题 - 完全独立的实现
  Widget _buildAppSettingsTitle(AppLocalizations l10n) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(left: 8.0, bottom: 4.0),
      child: MediaQuery(
        data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1.0)),
        child: Material(
          type: MaterialType.transparency,
          child: Text(
            l10n.appSettings,
            style: _sectionTitleStyle,
            textAlign: TextAlign.left,
            textDirection: TextDirection.ltr,
            softWrap: false,
            overflow: TextOverflow.visible,
          ),
        ),
      ),
    );
  }

  // 构建路由设置标题 - 完全独立的实现，与应用设置标题完全相同
  Widget _buildRoutingSettingsTitle(AppLocalizations l10n) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(left: 8.0, bottom: 4.0),
      child: MediaQuery(
        data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1.0)),
        child: Material(
          type: MaterialType.transparency,
          child: Text(
            l10n.routingSettings,
            style: _sectionTitleStyle,
            textAlign: TextAlign.left,
            textDirection: TextDirection.ltr,
            softWrap: false,
            overflow: TextOverflow.visible,
          ),
        ),
      ),
    );
  }

  // 构建应用按钮
  Widget _buildApplyButton(AppLocalizations l10n) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoadingRoutingSettings || _isLoadingAppSettings
            ? null
            : _applySettings,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: _isLoadingRoutingSettings || _isLoadingAppSettings
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(l10n.applying),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.check, size: 18),
                  const SizedBox(width: 8),
                  Text(
                    l10n.applySettings,
                    style: _buttonTextStyle, // 使用标准化的按钮文字样式
                  ),
                ],
              ),
      ),
    );
  }

  // 加载自动启动设置
  Future<void> _loadAutoStartSetting() async {
    final logService = serviceLocator<LogService>();
    final autoStartService = serviceLocator<AutoStartService>();

    setState(() {
      _isLoadingAppSettings = true;
    });

    try {
      // 使用AutoStartService加载自动启动设置
      final autoStart = await autoStartService.getAutoStartSetting();
      if (!mounted) return;
      final routingSettingsModel = Provider.of<RoutingSettingsModel>(context, listen: false);

      // 更新自动启动设置
      routingSettingsModel.autoStart = autoStart;

      // 如果原始设置已经初始化，也更新原始设置中的自动启动
      if (_originalSettings != null) {
        _originalSettings!.autoStart = autoStart;
      }

      logService.info('Settings', 'Auto start setting loaded: $autoStart');
    } catch (e) {
      logService.error('Settings', 'Failed to load auto start setting', e);
    } finally {
      setState(() {
        _isLoadingAppSettings = false;
      });
    }
  }

  // 加载路由设置
  Future<void> _loadRoutingSettings() async {
    final routingSettingsService = serviceLocator<RoutingSettingsService>();
    final routingSettingsModel = Provider.of<RoutingSettingsModel>(context, listen: false);
    final logService = serviceLocator<LogService>();

    setState(() {
      _isLoadingRoutingSettings = true;
    });

    try {
      // 使用路由设置服务获取设置（优先从本地加载）
      final settings = await routingSettingsService.getRoutingSettings();
      routingSettingsModel.updateSettings(
        mode: settings.mode,
        customRoutes: settings.customRoutes,
        autoStart: settings.autoStart,
      );

      // 保存原始设置用于比较
      _originalSettings = RoutingSettingsModel();
      _originalSettings!.updateSettings(
        mode: settings.mode,
        customRoutes: settings.customRoutes,
        autoStart: settings.autoStart,
      );

      logService.info('Settings', 'Routing settings loaded from service');
    } catch (e) {
      logService.error('Settings', 'Failed to load routing settings', e);
      // 显示错误提示
      if (!mounted) return;
      final l10n = AppLocalizations.of(context)!;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${l10n.getRoutingSettingsFailed}: ${e.toString()}')),
      );
    } finally {
      setState(() {
        _isLoadingRoutingSettings = false;
      });
    }
  }



  // 应用路由设置 - 自动启动设置立即生效无需通过此方法
  Future<void> _applySettings() async {
    final routingSettingsModel = Provider.of<RoutingSettingsModel>(context, listen: false);
    final appState = Provider.of<AppState>(context, listen: false);
    final logService = serviceLocator<LogService>();

    try {
      // 只检查路由设置变更，自动启动设置已立即生效
      bool hasRoutingChanges = _hasRoutingChanges(routingSettingsModel);

      logService.info('Settings', 'Applying routing settings - routing changes: $hasRoutingChanges');

      // 保存路由设置
      if (hasRoutingChanges) {
        logService.info('Settings', 'Routing settings changed, saving...');

        // iOS/macOS/Android平台需要特殊处理：VPN连接时需要重连才能应用路由配置
        // Android平台由于系统限制，VPN连接建立后无法动态修改路由，需要重建连接
        if (Platform.isIOS || Platform.isMacOS || Platform.isAndroid) {
          // 检查VPN连接状态，如果已连接则显示确认弹窗
          final isVpnConnected = appState.connectionStatus == ConnectionStatus.connected ||
                                appState.connectionStatus == ConnectionStatus.connecting;

          if (isVpnConnected) {
            // 显示确认弹窗
            final shouldReconnect = await _showRoutingChangeConfirmDialog();
            if (!shouldReconnect) {
              return; // 用户取消操作
            }

            // 用户确认重连，执行断开+保存+重连流程
            await _applyRoutingSettingsWithReconnect(routingSettingsModel);
          } else {
            // 未连接状态，直接保存设置
            await _saveRoutingSettings(routingSettingsModel);
            _updateOriginalSettings(routingSettingsModel);
            _showSuccessNotification();
          }
        } else {
          // Windows/Linux平台：保持原有逻辑，直接保存设置
          await _saveRoutingSettings(routingSettingsModel);
          _updateOriginalSettings(routingSettingsModel);
          _showSuccessNotification();
        }
      } else {
        // 没有变更，显示提示
        final notificationService = serviceLocator<NotificationService>();
        final l10n = AppLocalizations.of(context)!;
        notificationService.showInfoNotification(l10n.noChangesToApply);
      }
    } catch (e) {
      logService.error('Settings', 'Failed to apply routing settings', e);
      // 错误已在保存方法中处理
    }
  }

  // 检查路由设置是否有变更
  bool _hasRoutingChanges(RoutingSettingsModel current) {
    // final logService = serviceLocator<LogService>();

    if (_originalSettings == null) {
      // 如果原始设置未初始化，但当前设置不是默认值，则认为有变更
      bool hasChanges = current.mode != RoutingMode.all || current.customRoutes.isNotEmpty;
      // logService.debug('Settings', 'Checking routing changes (no original): current.mode=${current.mode}, current.customRoutes="${current.customRoutes}", hasChanges=$hasChanges');
      return hasChanges;
    }

    bool modeChanged = current.mode != _originalSettings!.mode;
    bool routesChanged = current.customRoutes != _originalSettings!.customRoutes;
    bool hasChanges = modeChanged || routesChanged;

    // logService.debug('Settings', 'Checking routing changes: original.mode=${_originalSettings!.mode}, current.mode=${current.mode}, original.customRoutes="${_originalSettings!.customRoutes}", current.customRoutes="${current.customRoutes}", modeChanged=$modeChanged, routesChanged=$routesChanged, hasChanges=$hasChanges');

    return hasChanges;
  }



  // 保存路由设置
  Future<void> _saveRoutingSettings(RoutingSettingsModel settings) async {
    final routingSettingsService = serviceLocator<RoutingSettingsService>();
    final logService = serviceLocator<LogService>();

    setState(() {
      _isLoadingRoutingSettings = true;
    });

    try {
      // 使用路由设置服务保存设置（同时保存到本地和后端）
      await routingSettingsService.updateRoutingSettings(settings);
      logService.info('Settings', 'Routing settings saved via service');
    } catch (e) {
      logService.error('Settings', 'Failed to save routing settings', e);
      // 显示错误提示
      if (!mounted) return;
      final l10n = AppLocalizations.of(context)!;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${l10n.saveRoutingSettingsFailed}: ${e.toString()}')),
      );
      rethrow;
    } finally {
      setState(() {
        _isLoadingRoutingSettings = false;
      });
    }
  }

  /// _showRoutingChangeConfirmDialog
  ///
  /// DESCRIPTION:
  ///     显示路由设置变更确认弹窗，在iOS/macOS/Android平台VPN已连接时使用
  ///     由于这些平台的系统限制，VPN连接时无法动态修改路由，需要重建连接
  ///
  /// RETURNS:
  ///     Future<bool> - 用户是否确认重连
  Future<bool> _showRoutingChangeConfirmDialog() async {
    final l10n = AppLocalizations.of(context)!;

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            l10n.confirmReconnection,
            style: _sectionTitleStyle,
          ),
          content: Text(
            l10n.routingChangeRequiresReconnection,
            style: _settingItemTitleStyle,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                l10n.cancel,
                style: _settingItemTitleStyle.copyWith(color: AppColors.textSecondary),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: Text(
                l10n.confirmAndReconnect,
                style: _settingItemTitleStyle.copyWith(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  /// _applyRoutingSettingsWithReconnect
  ///
  /// DESCRIPTION:
  ///     执行路由设置变更的重连流程：确保断开→保存路由→重连
  ///     使用100ms轮询确认断开状态，5秒超时，所有通知都国际化
  ///
  /// PARAMETERS:
  ///     routingSettingsModel - 新的路由设置
  Future<void> _applyRoutingSettingsWithReconnect(RoutingSettingsModel routingSettingsModel) async {
    final connectionManager = serviceLocator<ConnectionManager>();
    final appState = Provider.of<AppState>(context, listen: false);
    final logService = serviceLocator<LogService>();
    final notificationService = serviceLocator<NotificationService>();
    final l10n = AppLocalizations.of(context)!;

    // 记录当前选择的服务器，用于重连
    final currentServer = appState.selectedServer;

    logService.info('Settings', 'Starting routing settings reconnection flow: server=${currentServer?.name ?? 'none'}, mode=${routingSettingsModel.mode.toString()}');

    try {
      // 步骤1：确保VPN完全断开
      await _ensureVPNDisconnected(connectionManager, appState, logService, l10n);

      // 步骤2：保存新的路由设置（只有在确认断开后才保存）
      logService.info('Settings', 'Step 2: Saving new routing settings');
      await _saveRoutingSettings(routingSettingsModel);
      _updateOriginalSettings(routingSettingsModel);
      logService.info('Settings', 'Step 2 completed: Routing settings saved successfully');

      // 步骤3：重新连接（如果有服务器的话）
      if (currentServer != null) {
        logService.info('Settings', 'Step 3: Reconnecting to server: ${currentServer.name}');
        final connectResult = await connectionManager.connect();

        if (connectResult) {
          notificationService.showSuccessNotification(l10n.routingSettingsAppliedAndReconnected);
          logService.info('Settings', 'Step 3 completed: Reconnection initiated successfully');
        } else {
          notificationService.showErrorNotification(l10n.routingSettingsReconnectionFailed);
          logService.error('Settings', 'Step 3 failed: Connect method returned false');
        }
      } else {
        notificationService.showSuccessNotification(l10n.routingSettingsAppliedDisconnected);
        logService.info('Settings', 'No server selected, skipping reconnection');
      }

      logService.info('Settings', 'Routing settings reconnection flow completed');
    } catch (e) {
      logService.error('Settings', 'Routing settings reconnection flow failed', e);
      notificationService.showErrorNotification('${l10n.routingSettingsReconnectionFailed}: ${e.toString()}');
      rethrow;
    }
  }

  /// _updateOriginalSettings
  ///
  /// DESCRIPTION:
  ///     更新原始设置快照，用于变更检测
  ///
  /// PARAMETERS:
  ///     routingSettingsModel - 当前路由设置
  void _updateOriginalSettings(RoutingSettingsModel routingSettingsModel) {
    _originalSettings ??= RoutingSettingsModel();
    _originalSettings!.updateSettings(
      mode: routingSettingsModel.mode,
      customRoutes: routingSettingsModel.customRoutes,
      autoStart: routingSettingsModel.autoStart,
    );
  }

  /// _showSuccessNotification
  ///
  /// DESCRIPTION:
  ///     显示设置保存成功的通知
  void _showSuccessNotification() {
    final notificationService = serviceLocator<NotificationService>();
    final l10n = AppLocalizations.of(context)!;
    notificationService.showSuccessNotification(l10n.settingsAppliedSuccessfully);
  }

  /// _ensureVPNDisconnected
  ///
  /// DESCRIPTION:
  ///     确保VPN完全断开，使用100ms轮询检查状态，5秒超时
  ///     所有错误信息都使用国际化
  ///
  /// PARAMETERS:
  ///     connectionManager - 连接管理器
  ///     appState - 应用状态
  ///     logService - 日志服务
  ///     l10n - 国际化对象
  Future<void> _ensureVPNDisconnected(
    ConnectionManager connectionManager,
    AppState appState,
    LogService logService,
    AppLocalizations l10n,
  ) async {
    logService.info('Settings', 'Step 1: Ensuring VPN is completely disconnected');

    // 调用disconnect方法
    final disconnectResult = await connectionManager.disconnect();
    logService.info('Settings', 'Disconnect method returned: $disconnectResult');

    // 使用100ms轮询检查断开状态，最多等待5秒
    const maxWaitTime = Duration(seconds: 5);
    const checkInterval = Duration(milliseconds: 100);
    final startTime = DateTime.now();
    int checkCount = 0;

    while (DateTime.now().difference(startTime) < maxWaitTime) {
      checkCount++;
      final currentStatus = appState.connectionStatus;

      logService.debug('Settings', 'Disconnect check #$checkCount: ${currentStatus.toString()}');

      // 检查是否已断开
      if (currentStatus == ConnectionStatus.disconnected) {
        logService.info('Settings', 'VPN successfully disconnected after ${checkCount * 100}ms');
        return;
      }

      // 错误状态也视为断开
      if (currentStatus == ConnectionStatus.error) {
        logService.info('Settings', 'VPN in error state, treating as disconnected after ${checkCount * 100}ms');
        return;
      }

      // 等待下次检查
      await Future.delayed(checkInterval);
    }

    // 超时处理 - 使用国际化错误信息
    final finalStatus = appState.connectionStatus;
    logService.error('Settings', 'VPN disconnect timeout after 5 seconds: final_status=${finalStatus.toString()}, total_checks=$checkCount');

    throw Exception('${l10n.operationTimeout}: ${finalStatus.toString()}');
  }

}
