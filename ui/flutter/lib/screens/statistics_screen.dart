/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// ******************************************************************************
/// FILE NAME :      statistics_screen.dart
///
/// DESCRIPTION :    Statistics screen displaying connection status and traffic information
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../core/app_state.dart';
import '../core/dependency_injection.dart';
import '../models/connection_status.dart';
import '../utils/design_system.dart';
import '../generated/l10n/app_localizations.dart';
import '../services/font_service.dart';

/// StatisticsScreen
///
/// PURPOSE:
///     Statistics screen that displays real-time connection status and traffic information.
///     Provides comprehensive network statistics and interface details.
///
/// FEATURES:
///     - Real-time connection status monitoring
///     - Traffic statistics display (upload/download)
///     - Network interface information
///     - IP address information (local and TUN)
///     - Formatted data display with proper units
///     - Language-aware font styling
///     - Consistent design system integration
///     - Auto-refreshing interface information
///
/// USAGE:
///     Navigator.push(context, MaterialPageRoute(builder: (context) => StatisticsScreen()))
class StatisticsScreen extends StatefulWidget {
  /// StatisticsScreen constructor
  ///
  /// DESCRIPTION:
  ///     Creates a statistics screen widget for displaying network information.
  const StatisticsScreen({Key? key}) : super(key: key);

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

/// _StatisticsScreenState
///
/// PURPOSE:
///     State management for StatisticsScreen widget.
///     Handles data management, service integration, and UI updates.
///
/// FEATURES:
///     - Data manager integration for interface information
///     - App state monitoring for real-time updates
///     - Language-aware text styling
///     - Automatic data refresh on initialization
class _StatisticsScreenState extends State<StatisticsScreen> {
  /// Application state service instance
  late final AppState _appState;

  /// initState
  ///
  /// DESCRIPTION:
  ///     Initializes the state and sets up service instances and data loading.
  @override
  void initState() {
    super.initState();
    _appState = serviceLocator<AppState>();
    _refreshData();
  }

  /// _refreshData
  ///
  /// DESCRIPTION:
  ///     接口信息和流量统计都通过WebSocket实时推送，不需要手动刷新API
  Future<void> _refreshData() async {
    // 接口信息通过WebSocket实时推送，不需要API调用
    // 流量统计也通过WebSocket实时推送，不需要手动刷新
    // 移除API调用，避免与WebSocket推送的数据竞争

    // await _dataManager.refreshInterfaceInfo(); // 已禁用
  }

  /// _getTitleStyle
  ///
  /// DESCRIPTION:
  ///     Creates a title text style with language-aware font selection.
  ///     Uses design system colors for consistency.
  ///
  /// RETURNS:
  ///     TextStyle object for screen title
  TextStyle _getTitleStyle() {
    try {
      final fontService = serviceLocator<FontService>();
      return fontService.createTextStyleWithMappedWeight(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: const Color(0xFF131211), // 参考 chart.css 的深色文字
      );
    } catch (e) {
      return const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: Color(0xFF131211),
        fontFamilyFallback: ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'],
      );
    }
  }

  /// build
  ///
  /// DESCRIPTION:
  ///     Builds the statistics screen widget with real-time data display.
  ///     Creates a responsive layout with app state integration.
  ///
  /// PARAMETERS:
  ///     context - Build context for the widget
  ///
  /// RETURNS:
  ///     Widget tree representing the statistics screen
  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.dark, // 白色背景使用深色状态栏文字
      child: ChangeNotifierProvider<AppState>.value(
        value: _appState,
        child: Container(
          // FCF6F1 背景色，与登录连接界面保持一致
          color: AppColors.backgroundStart,
          child: Padding(
            padding: const EdgeInsets.all(DesignSystem.spacing32),
            child: Consumer<AppState>(
              builder: (context, appState, child) {
                return _buildStatisticsContent(appState);
              },
            ),
          ),
        ),
      ),
    );
  }

  /// 构建统计信息内容
  Widget _buildStatisticsContent(AppState appState) {
    final l10n = AppLocalizations.of(context)!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 统计信息标题 - 参考 chart.css 的标题样式
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Text(
            l10n.statisticsInfo,
            style: _getTitleStyle(),
          ),
        ),

        const SizedBox(height: DesignSystem.spacing24),

        // 统计信息卡片 - 参考 chart.css 的白色卡片设计
        _buildStatisticsCard(appState),
      ],
    );
  }

  /// 构建统计信息卡片 - 参考 chart.css 的设计
  Widget _buildStatisticsCard(AppState appState) {
    final l10n = AppLocalizations.of(context)!;
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x1A66809E), // rgba(102, 128, 158, 0.1)
            blurRadius: 4,
            offset: Offset(0, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // 状态
          _buildStatisticsItem(
            l10n.status,
            _getStatusText(appState.connectionStatus),
            _getStatusColor(appState.connectionStatus),
            isFirst: true,
          ),

          // 接口
          _buildStatisticsItem(
            l10n.interface,
            appState.interfaceInfo.interfaceName ?? '',
            const Color(0xFF333333), // 深字色
          ),

          // 上行（总上传流量）
          _buildStatisticsItem(
            l10n.upload,
            _formatBytes(appState.trafficStats.totalUpload),
            const Color(0xFF333333), // 深字色
          ),

          // 下行（总下载流量）
          _buildStatisticsItem(
            l10n.download,
            _formatBytes(appState.trafficStats.totalDownload),
            const Color(0xFF333333), // 深字色
          ),

          // 本地IP
          _buildStatisticsItem(
            l10n.localIp,
            appState.interfaceInfo.localIp ?? '',
            const Color(0xFF333333), // 深字色
          ),

          // ItForce IP（使用TUN IP）
          _buildStatisticsItem(
            l10n.itforceIp,
            appState.interfaceInfo.tunIp ?? '',
            const Color(0xFF333333), // 深字色
            isLast: true,
          ),
        ],
      ),
    );
  }

  /// 构建单个统计信息项目 - 参考 chart.css 的行设计
  Widget _buildStatisticsItem(
    String label,
    String value,
    Color valueColor, {
    bool isFirst = false,
    bool isLast = false,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: isLast
            ? BorderSide.none
            : const BorderSide(
                color: Color(0xFFEBEBEB), // 参考 chart.css 的边框色
                width: 1,
                style: BorderStyle.solid,
              ),
        ),
        borderRadius: isFirst
          ? const BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            )
          : isLast
            ? const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              )
            : BorderRadius.zero,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: DesignSystem.createTextStyle(
              fontSize: 14,
              color: const Color(0xFF66809E), // 参考 chart.css 的标签颜色
              fontWeight: FontWeight.w400,
            ),
          ),
          Text(
            value,
            style: DesignSystem.createTextStyle(
              fontSize: 14,
              color: valueColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// _formatBytes
  ///
  /// DESCRIPTION:
  ///     Formats byte values into human-readable strings with appropriate units.
  ///     Converts bytes to KB, MB, or GB as needed with NaN protection.
  ///
  /// PARAMETERS:
  ///     bytes - Number of bytes to format
  ///
  /// RETURNS:
  ///     Formatted string with appropriate unit
  String _formatBytes(int bytes) {
    if (bytes < 0) return '0 B';
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final kb = bytes / 1024;
      return kb.isNaN || kb.isInfinite ? '0 B' : '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final mb = bytes / (1024 * 1024);
      return mb.isNaN || mb.isInfinite ? '0 B' : '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = bytes / (1024 * 1024 * 1024);
      return gb.isNaN || gb.isInfinite ? '0 B' : '${gb.toStringAsFixed(1)} GB';
    }
  }

  /// _getStatusText
  ///
  /// DESCRIPTION:
  ///     Returns localized text for connection status.
  ///     Maps connection status enum to user-friendly text.
  ///
  /// PARAMETERS:
  ///     status - ConnectionStatus enum value
  ///
  /// RETURNS:
  ///     Localized status text string
  String _getStatusText(ConnectionStatus status) {
    final l10n = AppLocalizations.of(context)!;
    switch (status) {
      case ConnectionStatus.connected:
        return l10n.connected;
      case ConnectionStatus.connecting:
        return l10n.connecting;
      case ConnectionStatus.disconnecting:
        return l10n.disconnecting;
      case ConnectionStatus.error:
        return l10n.connectionFailed;
      case ConnectionStatus.disconnected:
        return l10n.disconnected;
    }
  }

  /// _getStatusColor
  ///
  /// DESCRIPTION:
  ///     Returns appropriate color for connection status display.
  ///     Uses design system colors for consistency.
  ///
  /// PARAMETERS:
  ///     status - ConnectionStatus enum value
  ///
  /// RETURNS:
  ///     Color object for status display
  Color _getStatusColor(ConnectionStatus status) {
    switch (status) {
      case ConnectionStatus.connected:
        return const Color(0xFF33DB87); // 参考 chart.css 的绿色 #33DB87
      case ConnectionStatus.connecting:
      case ConnectionStatus.disconnecting:
        return const Color(0xFF05683E); // 绿色
      case ConnectionStatus.error:
        return const Color(0xFFEF4444); // 红色
      case ConnectionStatus.disconnected:
        return const Color(0xFF05683E); // 绿色
    }
  }
}
