import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/app_state.dart';
import '../core/dependency_injection.dart';

import '../services/auth_service.dart';
import '../services/data_manager.dart';
import '../services/connection_manager.dart';
import '../services/log_service.dart';
import '../services/language_service.dart';
import '../utils/constants.dart';
import '../utils/design_system.dart';

import '../widgets/svg_asset_widget.dart';
import 'login_screen.dart';
import '../generated/l10n/app_localizations.dart';
import '../services/font_service.dart';
import '../services/notification_service.dart';


/// 用户信息屏幕
class UserScreen extends StatefulWidget {
  final Function(bool)? onEditingStateChanged;

  const UserScreen({
    Key? key,
    this.onEditingStateChanged,
  }) : super(key: key);

  @override
  State<UserScreen> createState() => _UserScreenState();
}

class _UserScreenState extends State<UserScreen> {
  bool _isEditing = false;
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _displayNameController;
  late TextEditingController _departmentController;
  late TextEditingController _positionController;

  // 焦点管理
  final _displayNameFocusNode = FocusNode();
  final _departmentFocusNode = FocusNode();
  final _positionFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _displayNameController = TextEditingController();
    _departmentController = TextEditingController();
    _positionController = TextEditingController();
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _departmentController.dispose();
    _positionController.dispose();
    _displayNameFocusNode.dispose();
    _departmentFocusNode.dispose();
    _positionFocusNode.dispose();
    super.dispose();
  }

  // 获取文本样式 - 使用语言感知的字体
  TextStyle _getTextStyle({
    required double fontSize,
    FontWeight? fontWeight,
    Color? color,
  }) {
    try {
      final fontService = serviceLocator<FontService>();
      return fontService.createTextStyleWithMappedWeight(
        fontSize: fontSize,
        fontWeight: fontWeight ?? FontWeight.normal,
        color: color ?? AppColors.textPrimary,
      );
    } catch (e) {
      return TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight ?? FontWeight.normal,
        color: color ?? AppColors.textPrimary,
        fontFamilyFallback: const ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'],
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child: Column(
          children: [
            // 使用Expanded确保内容在一页中显示
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(DesignSystem.spacing16), // 减少外边距
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Logo区域
                    _buildLogoArea(),

                    const SizedBox(height: DesignSystem.spacing16), // 减少间距

                    // 个人信息卡片（根据版本控制显示）
                    if (FeatureVersion.showPersonalInfo == 1) ...[
                      Consumer<AppState>(
                        builder: (context, appState, child) {
                          return _buildPersonalInfoCard(appState);
                        },
                      ),
                      const SizedBox(height: DesignSystem.spacing12), // 减少间距
                    ],

                    // 账户信息卡片
                    Consumer<AuthService>(
                      builder: (context, authService, child) {
                        return _buildAccountInfoCard(authService);
                      },
                    ),

                    const SizedBox(height: DesignSystem.spacing16), // 减少间距

                    // 注销按钮
                    _buildLogoutButton(context),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建Logo区域
  Widget _buildLogoArea() {
    return Column(
      children: [
        // 只显示盾牌Logo - 更小尺寸
        DesignSystemSvgs.shieldLogo(
          width: 80, // 减小尺寸
          height: 90, // 减小尺寸
        ),
      ],
    );
  }

  /// 构建个人信息卡片（内联编辑）
  Widget _buildPersonalInfoCard(AppState appState) {
    final l10n = AppLocalizations.of(context)!;
    // 初始化控制器的值
    if (!_isEditing) {
      _displayNameController.text = appState.userInfo.displayName;
      _departmentController.text = appState.userInfo.department;
      _positionController.text = appState.userInfo.position;
    }

    return Container(
      padding: const EdgeInsets.all(DesignSystem.spacing16), // 增加padding以适应新样式
      decoration: BoxDecoration(
        color: AppColors.userCardBackground, // 透明背景
        borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        border: Border.all(
          color: AppColors.userCardBorder, // 白色边框
          width: 1,
        ),
        // 移除阴影效果
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏 - 更紧凑
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.person_rounded,
                    color: AppColors.textDarkGray,
                    size: 20, // 减小图标尺寸
                  ),
                  const SizedBox(width: 6),
                  Text(
                    l10n.personalInfo,
                    style: DesignSystem.titleMedium.copyWith(
                      color: AppColors.textDarkGray,
                      fontWeight: FontWeight.w600,
                      fontSize: 14, // 减小字体
                    ),
                  ),
                ],
              ),
              if (_isEditing) ...[
                // 编辑模式下的按钮
                Row(
                  children: [
                    TextButton(
                      onPressed: _cancelEditing,
                      child: Text(
                        l10n.cancel,
                        style: _getTextStyle(
                          fontSize: 12,
                          color: AppColors.textDarkGray.withValues(alpha: 0.8),
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    ElevatedButton(
                      onPressed: () => _saveProfile(appState),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8), // 增加垂直内边距
                        minimumSize: const Size(0, 36), // 增加最小高度
                      ),
                      child: Text(
                        l10n.save,
                        style: _getTextStyle(
                          fontSize: 12,
                          color: Colors.white,
                        ).copyWith(
                          leadingDistribution: TextLeadingDistribution.even,
                        ),
                      ),
                    ),
                  ],
                ),
              ] else ...[
                // 编辑按钮
                IconButton(
                  onPressed: _startEditing,
                  icon: const Icon(
                    Icons.edit_rounded,
                    color: AppColors.primary,
                    size: 18,
                  ),
                  tooltip: l10n.editPersonalInfo,
                  padding: const EdgeInsets.all(4),
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                ),
              ],
            ],
          ),

          const SizedBox(height: DesignSystem.spacing12), // 减少间距

          // 表单字段 - 更紧凑的布局
          Form(
            key: _formKey,
            child: Column(
              children: [
                // 显示名称
                _buildCompactEditableField(
                  l10n.name,
                  _displayNameController,
                  Icons.person_outline,
                  focusNode: _displayNameFocusNode,
                  textInputAction: TextInputAction.next,
                  onSubmitted: (_) => _departmentFocusNode.requestFocus(),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return l10n.pleaseEnterName;
                    }
                    return null;
                  },
                ),

                // 分界线
                if (!_isEditing) ...[
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: DesignSystem.spacing8),
                    child: Divider(
                      color: AppColors.textDarkGray.withValues(alpha: 0.2),
                      thickness: 0.5,
                      height: 1,
                    ),
                  ),
                ] else ...[
                  const SizedBox(height: DesignSystem.spacing8),
                ],

                // 部门
                _buildCompactEditableField(
                  l10n.department,
                  _departmentController,
                  Icons.business_outlined,
                  focusNode: _departmentFocusNode,
                  textInputAction: TextInputAction.next,
                  onSubmitted: (_) => _positionFocusNode.requestFocus(),
                ),

                // 分界线
                if (!_isEditing) ...[
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: DesignSystem.spacing8),
                    child: Divider(
                      color: AppColors.textDarkGray.withValues(alpha: 0.2),
                      thickness: 0.5,
                      height: 1,
                    ),
                  ),
                ] else ...[
                  const SizedBox(height: DesignSystem.spacing8),
                ],

                // 职位
                _buildCompactEditableField(
                  l10n.position,
                  _positionController,
                  Icons.work_outline,
                  focusNode: _positionFocusNode,
                  textInputAction: TextInputAction.done,
                  onSubmitted: (_) => _saveProfile(appState),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建账户信息卡片
  Widget _buildAccountInfoCard(AuthService authService) {
    final l10n = AppLocalizations.of(context)!;
    return Container(
      padding: const EdgeInsets.all(DesignSystem.spacing16), // 增加padding以适应新样式
      decoration: BoxDecoration(
        color: AppColors.userCardBackground, // 透明背景
        borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        border: Border.all(
          color: AppColors.userCardBorder, // 白色边框
          width: 1,
        ),
        // 移除阴影效果
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.account_circle_outlined,
                color: AppColors.textDarkGray,
                size: 20, // 减小图标尺寸
              ),
              const SizedBox(width: 6),
              Text(
                l10n.accountInfo,
                style: DesignSystem.titleMedium.copyWith(
                  color: AppColors.textDarkGray,
                  fontWeight: FontWeight.w600,
                  fontSize: 14, // 减小字体
                ),
              ),
            ],
          ),
          const SizedBox(height: DesignSystem.spacing12), // 减少间距

          _buildAccountInfoRow(
            icon: Icons.language,
            label: l10n.clientDomainLabel,
            value: authService.domain,
          ),

          // 分界线
          Padding(
            padding: const EdgeInsets.symmetric(vertical: DesignSystem.spacing8),
            child: Divider(
              color: AppColors.textDarkGray.withValues(alpha: 0.2),
              thickness: 0.5,
              height: 1,
            ),
          ),

          _buildAccountInfoRow(
            icon: Icons.account_circle,
            label: l10n.usernameLabel,
            value: authService.username,
          ),

          // 分界线
          Padding(
            padding: const EdgeInsets.symmetric(vertical: DesignSystem.spacing8),
            child: Divider(
              color: AppColors.textDarkGray.withValues(alpha: 0.2),
              thickness: 0.5,
              height: 1,
            ),
          ),

          _buildAccountInfoRow(
            icon: Icons.devices,
            label: l10n.deviceInfo,
            value: authService.deviceId,
          ),
        ],
      ),
    );
  }

  /// 构建信息行（账户信息用）
  Widget _buildAccountInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppColors.textDarkGray.withValues(alpha: 0.8),
        ),
        const SizedBox(width: DesignSystem.spacing8),
        Text(
          '$label: ',
          style: DesignSystem.bodyMedium.copyWith(
            color: AppColors.textDarkGray.withValues(alpha: 0.8),
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            textAlign: TextAlign.right, // 文字右对齐
            style: DesignSystem.bodyMedium.copyWith(
              color: AppColors.textDarkGray,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建注销按钮
  Widget _buildLogoutButton(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return SizedBox(
      width: double.infinity,
      height: 48, // 保持原有高度
      child: ElevatedButton(
        onPressed: () => _handleLogout(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.error,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          ),
          elevation: 0,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24), // 增加垂直内边距
        ),
        child: Text(
          l10n.logoutButton,
          style: DesignSystem.bodyMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
            leadingDistribution: TextLeadingDistribution.even, // 确保文字居中
          ),
        ),
      ),
    );
  }

  /// 开始编辑模式
  void _startEditing() {
    setState(() {
      _isEditing = true;
    });
    // 通知父组件编辑状态改变
    widget.onEditingStateChanged?.call(true);
  }

  /// 取消编辑
  void _cancelEditing() {
    setState(() {
      _isEditing = false;
    });
    // 通知父组件编辑状态改变
    widget.onEditingStateChanged?.call(false);
    // 重置控制器的值
    final appState = Provider.of<AppState>(context, listen: false);
    _displayNameController.text = appState.userInfo.displayName;
    _departmentController.text = appState.userInfo.department;
    _positionController.text = appState.userInfo.position;
  }

  /// 保存个人资料
  Future<void> _saveProfile(AppState appState) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      // 创建更新的用户信息
      final updatedUserInfo = appState.userInfo.copyWith(
        displayName: _displayNameController.text.trim(),
        department: _departmentController.text.trim(),
        position: _positionController.text.trim(),
      );

      // 获取当前登录的用户名
      final authService = serviceLocator<AuthService>();
      final currentUsername = authService.username;

      // 保存到数据管理器
      final dataManager = serviceLocator<DataManager>();
      await dataManager.saveUserInfo(updatedUserInfo, currentUsername);

      // 显示成功消息 - 使用统一的通知系统
      if (!mounted) return;
      final l10n = AppLocalizations.of(context)!;
      final notificationService = serviceLocator<NotificationService>();
      notificationService.showSuccessNotification(l10n.personalInfoSaved);

      // 退出编辑模式
      setState(() {
        _isEditing = false;
      });
      // 通知父组件编辑状态改变
      widget.onEditingStateChanged?.call(false);

    } catch (e) {
      // 显示错误消息 - 使用统一的通知系统
      if (!mounted) return;
      final l10n = AppLocalizations.of(context)!;
      final notificationService = serviceLocator<NotificationService>();
      notificationService.showErrorNotification(l10n.saveFailed);
    }
  }



  /// 构建紧凑的可编辑字段
  Widget _buildCompactEditableField(
    String label,
    TextEditingController controller,
    IconData icon, {
    String? Function(String?)? validator,
    FocusNode? focusNode,
    TextInputAction? textInputAction,
    Function(String)? onSubmitted,
  }) {
    if (_isEditing) {
      return TextFormField(
        controller: controller,
        focusNode: focusNode,
        textInputAction: textInputAction,
        onFieldSubmitted: onSubmitted,
        decoration: InputDecoration(
          labelText: label,
          labelStyle: _getTextStyle(
            fontSize: 14,
            color: AppColors.textDarkGray.withValues(alpha: 0.8),
          ),
          prefixIcon: Icon(icon, color: AppColors.textDarkGray.withValues(alpha: 0.8), size: 20),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: AppColors.textDarkGray.withValues(alpha: 0.3)),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: AppColors.textDarkGray.withValues(alpha: 0.3)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: AppColors.primary, width: 2),
          ),
          filled: true,
          fillColor: Colors.transparent,
          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          isDense: true,
        ),
        validator: validator,
        style: _getTextStyle(
          fontSize: 14,
          color: AppColors.textDarkGray,
        ),
      );
    } else {
      // 显示模式 - 与账户信息保持一致的布局
      return Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: AppColors.textDarkGray.withValues(alpha: 0.8),
          ),
          const SizedBox(width: DesignSystem.spacing8),
          Text(
            '$label: ',
            style: DesignSystem.bodyMedium.copyWith(
              color: AppColors.textDarkGray.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
          Expanded(
            child: Text(
              controller.text.isEmpty ? AppLocalizations.of(context)!.notSet : controller.text,
              textAlign: TextAlign.right, // 文字右对齐
              style: DesignSystem.bodyMedium.copyWith(
                color: controller.text.isEmpty
                    ? AppColors.textDarkGray.withValues(alpha: 0.6)
                    : AppColors.textDarkGray,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      );
    }
  }

  /// 处理退出登录
  void _handleLogout(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final appState = Provider.of<AppState>(context, listen: false);
    final languageService = serviceLocator<LanguageService>();
    final isVpnConnected = appState.connectionStatus == ConnectionStatus.connected ||
                          appState.connectionStatus == ConnectionStatus.connecting;

    String dialogContent = l10n.logoutConfirmation;
    if (isVpnConnected) {
      dialogContent = l10n.logoutWithVpnWarning;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(
              Icons.logout_rounded,
              color: AppColors.error,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(l10n.confirmLogout),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(dialogContent),
            if (isVpnConnected) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.connecting.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: AppColors.connecting.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.vpn_lock_rounded,
                      color: AppColors.connecting,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        l10n.currentlyConnectedToServer(
                          appState.selectedServer?.getDisplayNameByLocale(languageService.isEnglish) ?? l10n.unknownServer
                        ),
                        style: _getTextStyle(
                          fontSize: 12,
                          color: AppColors.connecting,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performLogout(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16), // 增加内边距
            ),
            child: Text(
              isVpnConnected ? l10n.disconnectAndExit : l10n.exit,
              style: const TextStyle(
                leadingDistribution: TextLeadingDistribution.even,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 执行退出登录
  Future<void> _performLogout(BuildContext context) async {
    final logService = serviceLocator<LogService>();

    try {
      // 获取服务实例
      final appState = serviceLocator<AppState>();
      final authService = serviceLocator<AuthService>();

      logService.info('UserScreen', 'Starting logout process');

      // 立即导航到登录页面，避免在异步操作后context失效
      logService.info('UserScreen', 'Navigate to login page immediately to avoid context issues');

      // 检查widget是否仍然mounted
      if (!mounted) {
        logService.warning('UserScreen', 'Widget has been disposed, cannot execute navigation');
        return;
      }

      // 立即执行导航，在后台继续清理工作
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) {
          logService.info('UserScreen', 'Building LoginScreen after logout...');
          return LoginScreen.createAfterLogout();
        }),
        (route) {
          logService.info('UserScreen', 'Removing route: ${route.settings.name}');
          return false;
        },
      );

      logService.info('UserScreen', 'Navigation to login page completed, starting background cleanup');

      // 在后台执行清理工作，不阻塞UI导航
      _performBackgroundCleanup(appState, authService, logService);

    } catch (e) {
      logService.error('UserScreen', 'Unexpected error occurred during logout process', e);

      // 即使出错也要确保用户能够返回登录页面
      if (mounted) {
        try {
          final appState = Provider.of<AppState>(context, listen: false);
          appState.reset();
        } catch (resetError) {
          logService.error('UserScreen', 'Force reset state failed', resetError);
        }

        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => LoginScreen.createAfterLogout()),
          (route) => false,
        );
      }
    }
  }

  /// 在后台执行清理工作，不阻塞UI导航
  Future<void> _performBackgroundCleanup(AppState appState, AuthService authService, LogService logService) async {
    try {
      // 第一步：检查并断开WAN连接
      if (appState.connectionStatus == ConnectionStatus.connected ||
          appState.connectionStatus == ConnectionStatus.connecting) {

        logService.info('UserScreen', 'Active WAN connection detected, disconnecting...');

        try {
          final connectionManager = serviceLocator<ConnectionManager>();

          // 更新状态显示正在断开连接
          appState.updateConnectionStatus(
            ConnectionStatus.disconnecting,
            message: '正在断开WAN连接...'
          );

          // 执行WAN断开连接
          final disconnectSuccess = await connectionManager.disconnect();

          if (disconnectSuccess) {
            logService.info('UserScreen', 'WAN connection disconnected successfully');
          } else {
            logService.warning('UserScreen', 'WAN disconnection returned failure, but continuing logout process');
          }

          // 等待一小段时间确保断开完成
          await Future.delayed(const Duration(milliseconds: 500));

        } catch (e) {
          logService.error('UserScreen', 'Error occurred during WAN disconnection, but continuing logout process', e);
        }
      } else {
        logService.info('UserScreen', 'No active WAN connection, skipping disconnection step');
      }

      // 第二步：清除认证数据和敏感信息
      logService.info('UserScreen', 'Clearing authentication data and sensitive information...');
      await authService.logout();

      // 第三步：重置应用状态（包括连接状态和服务器信息）
      logService.info('UserScreen', 'Resetting application state...');
      appState.reset();

      // 第四步：清除所有敏感数据（包括密码）并重置DataManager
      logService.info('UserScreen', 'Clearing all sensitive data and resetting DataManager...');
      try {
        final dataManager = serviceLocator<DataManager>();
        await dataManager.clearAllSensitiveData();
        // 重置DataManager状态，避免在重新登录时使用已释放的实例
        dataManager.reset();
      } catch (e) {
        logService.error('UserScreen', 'Failed to clear sensitive data, but continue logout process', e);
      }

      logService.info('UserScreen', 'Background cleanup completed');

    } catch (e) {
      logService.error('UserScreen', 'Error occurred during background cleanup', e);
    }
  }


}




