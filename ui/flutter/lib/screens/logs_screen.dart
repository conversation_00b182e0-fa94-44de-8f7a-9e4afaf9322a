/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      logs_screen.dart
///
/// DESCRIPTION :    Logs screen for viewing, filtering, and managing application logs
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';


import '../models/log_entry.dart';
import '../services/log_service.dart';
import '../core/dependency_injection.dart';
import '../utils/constants.dart';
import '../utils/formatters.dart';

import '../utils/design_system.dart';
import '../generated/l10n/app_localizations.dart';
import '../services/font_service.dart';

/// LogsScreen
///
/// PURPOSE:
///     A screen that displays application logs with filtering, searching, and management capabilities.
///     Provides real-time log viewing with level-based filtering and export functionality.
///
/// FEATURES:
///     - Real-time log display with automatic updates
///     - Search functionality for log messages and modules
///     - Level-based filtering (debug, info, warning, error)
///     - Log export to file functionality
///     - Log clearing with confirmation dialog
///     - Copy individual log entries to clipboard
///     - Auto-scroll to latest logs
///     - Responsive UI with floating action button for navigation
///     - Language-aware font styling
///
/// USAGE:
///     Navigator.push(context, MaterialPageRoute(builder: (context) => LogsScreen()))
class LogsScreen extends StatefulWidget {
  /// LogsScreen constructor
  ///
  /// DESCRIPTION:
  ///     Creates a logs screen widget for viewing and managing application logs.
  const LogsScreen({Key? key}) : super(key: key);

  @override
  State<LogsScreen> createState() => _LogsScreenState();
}

/// _LogsScreenState
///
/// PURPOSE:
///     State management for LogsScreen widget.
///     Handles log data, filtering, searching, and UI interactions.
///
/// FEATURES:
///     - Log data management and real-time updates
///     - Search and filter state management
///     - Scroll controller for auto-scroll functionality
///     - Log service integration for data operations
class _LogsScreenState extends State<LogsScreen> {
  /// List of log entries to display
  List<LogEntry> _logs = [];

  /// Current search query string
  String _searchQuery = '';

  /// Selected log level for filtering (null means all levels)
  LogLevel? _selectedLevel;

  /// Whether the search input field is visible
  bool _isSearchVisible = false;

  /// Scroll controller for the log list
  final ScrollController _scrollController = ScrollController();

  /// Log service instance for data operations
  late final LogService _logService;

  /// initState
  ///
  /// DESCRIPTION:
  ///     Initializes the state and sets up log service and data loading.
  @override
  void initState() {
    super.initState();
    _logService = serviceLocator<LogService>();
    _loadLogs();
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     Cleans up resources when the widget is disposed.
  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// _loadLogs
  ///
  /// DESCRIPTION:
  ///     Loads initial logs and sets up real-time log stream listening.
  ///     Automatically scrolls to bottom when new logs arrive if already at bottom.
  void _loadLogs() {
    setState(() {
      _logs = _logService.logs;
    });

    // 监听新日志
    _logService.logStream.listen((log) {
      if (mounted) {
        setState(() {
          _logs = _logService.logs;
        });

        // 如果滚动在顶部，自动滚动以显示新日志（倒序显示，新日志在顶部）
        if (_scrollController.hasClients &&
            _scrollController.position.pixels == 0) {
          Future.delayed(const Duration(milliseconds: 100), () {
            if (_scrollController.hasClients) {
              _scrollController.animateTo(
                0,
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeOut,
              );
            }
          });
        }
      }
    });
  }

  /// _clearLogs
  ///
  /// DESCRIPTION:
  ///     Shows a confirmation dialog and clears all logs if confirmed.
  ///     Updates the UI state after clearing logs.
  void _clearLogs() {
    final l10n = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.clearLogs),
        content: Text(l10n.confirmClearLogs),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () {
              _logService.clearLogs();
              setState(() {
                _logs = [];
              });
              Navigator.of(context).pop();
            },
            child: Text(l10n.confirm),
          ),
        ],
      ),
    );
  }

  /// _exportLogs
  ///
  /// DESCRIPTION:
  ///     Exports all logs to a file and shows success/error message.
  ///     Uses the log service to handle file operations.
  Future<void> _exportLogs() async {
    final l10n = AppLocalizations.of(context)!;
    try {
      final path = await _logService.exportLogs();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${l10n.logsExportedTo}: $path')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${l10n.exportLogsFailed}: ${e.toString()}')),
        );
      }
    }
  }

  /// _copyLog
  ///
  /// DESCRIPTION:
  ///     Copies a single log entry to the clipboard in formatted text.
  ///     Shows a confirmation message after copying.
  ///
  /// PARAMETERS:
  ///     log - LogEntry object to copy to clipboard
  void _copyLog(LogEntry log) {
    final l10n = AppLocalizations.of(context)!;
    final timeStr = Formatters.formatTime(log.timestamp);
    final levelStr = log.level.toString().split('.').last.toUpperCase();
    final text = '[$timeStr] [$levelStr] [${log.module}] ${log.message}';

    Clipboard.setData(ClipboardData(text: text));

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(l10n.logCopiedToClipboard)),
    );
  }

  /// _getFilteredLogs
  ///
  /// DESCRIPTION:
  ///     Returns filtered logs based on current search query and selected level.
  ///     Applies both level filtering and text search filtering.
  ///     Returns logs in reverse chronological order (newest first).
  ///
  /// RETURNS:
  ///     List of LogEntry objects matching current filters in reverse order
  List<LogEntry> _getFilteredLogs() {
    List<LogEntry> filteredLogs = List.from(_logs);

    // 按级别筛选
    if (_selectedLevel != null) {
      filteredLogs = filteredLogs.where((log) => log.level == _selectedLevel).toList();
    }

    // 按关键词搜索
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filteredLogs = filteredLogs.where((log) {
        return log.message.toLowerCase().contains(query) ||
            log.module.toLowerCase().contains(query);
      }).toList();
    }

    // 倒序排列：新日志在上面
    return filteredLogs.reversed.toList();
  }

  /// _getLevelColor
  ///
  /// DESCRIPTION:
  ///     Returns the appropriate color for a given log level.
  ///     Uses design system colors for consistency.
  ///
  /// PARAMETERS:
  ///     level - LogLevel enum value
  ///
  /// RETURNS:
  ///     Color object for the specified log level
  Color _getLevelColor(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return AppColors.textSecondary;
      case LogLevel.info:
        return AppColors.primary;
      case LogLevel.warning:
        return const Color(0xFFFF8C00); // 固定橙色，不跟随主题变化
      case LogLevel.error:
        return AppColors.error;
    }
  }

  /// _getLevelIcon
  ///
  /// DESCRIPTION:
  ///     Returns the appropriate icon for a given log level.
  ///     Provides visual distinction between different log types.
  ///
  /// PARAMETERS:
  ///     level - LogLevel enum value
  ///
  /// RETURNS:
  ///     IconData object for the specified log level
  IconData _getLevelIcon(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return Icons.bug_report;
      case LogLevel.info:
        return Icons.info_outline;
      case LogLevel.warning:
        return Icons.warning_amber_outlined;
      case LogLevel.error:
        return Icons.error_outline;
    }
  }

  /// _getLevelName
  ///
  /// DESCRIPTION:
  ///     Returns the display name for a given log level.
  ///     Handles null level for "all levels" display.
  ///
  /// PARAMETERS:
  ///     level - LogLevel enum value (nullable)
  ///
  /// RETURNS:
  ///     String representation of the log level
  String _getLevelName(LogLevel? level) {
    final l10n = AppLocalizations.of(context)!;
    if (level == null) return l10n.allLevels;

    switch (level) {
      case LogLevel.debug:
        return 'DEBUG';
      case LogLevel.info:
        return 'INFO';
      case LogLevel.warning:
        return 'WARNING';
      case LogLevel.error:
        return 'ERROR';
    }
  }

  /// _getTitleStyle
  ///
  /// DESCRIPTION:
  ///     Creates a title text style with language-aware font selection.
  ///     Provides fallback fonts for better cross-platform compatibility.
  ///
  /// RETURNS:
  ///     TextStyle object for screen title
  TextStyle _getTitleStyle() {
    try {
      final fontService = serviceLocator<FontService>();
      return fontService.createTextStyleWithMappedWeight(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: AppColors.textPrimary,
      );
    } catch (e) {
      return const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: AppColors.textPrimary,
        fontFamilyFallback: ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'],
      );
    }
  }

  /// build
  ///
  /// DESCRIPTION:
  ///     Builds the logs screen widget with search, filter, and log display functionality.
  ///     Creates a comprehensive UI for log management and viewing.
  ///
  /// PARAMETERS:
  ///     context - Build context for the widget
  ///
  /// RETURNS:
  ///     Widget tree representing the logs screen
  @override
  Widget build(BuildContext context) {
    final filteredLogs = _getFilteredLogs();
    final l10n = AppLocalizations.of(context)!;



    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.dark, // 强制设置深色状态栏样式
      child: Scaffold(
        backgroundColor: AppColors.backgroundStart,
        appBar: AppBar(
          title: _isSearchVisible
              ? TextField(
                  autofocus: true,
                  decoration: InputDecoration(
                    hintText: l10n.searchLogs,
                    hintStyle: const TextStyle(color: AppColors.textSecondary),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.fromLTRB(72, 14, 16, 14), // 左侧72px让输入框从浮动按钮右侧开始
                  ),
                  style: const TextStyle(color: AppColors.textPrimary),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                )
              : Text(
                  l10n.logsTitle,
                  style: _getTitleStyle(),
                  textAlign: TextAlign.left, // 左对齐
                ),
          backgroundColor: Colors.transparent,
          elevation: 0,
          foregroundColor: AppColors.textPrimary,
          systemOverlayStyle: SystemUiOverlayStyle.dark, // 白色背景使用深色状态栏文字

        actions: [
          // 搜索按钮
          IconButton(
            icon: Icon(_isSearchVisible ? Icons.close : Icons.search),
            tooltip: _isSearchVisible ? l10n.closeSearch : l10n.searchLogsTooltip,
            onPressed: () {
              setState(() {
                if (_isSearchVisible) {
                  _isSearchVisible = false;
                  _searchQuery = '';
                } else {
                  _isSearchVisible = true;
                }
              });
            },
          ),
          // 筛选按钮
          PopupMenuButton<LogLevel?>(
            icon: const Icon(Icons.filter_list),
            tooltip: l10n.filterByLevel,
            onSelected: (level) {
              setState(() {
                _selectedLevel = level;
              });
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: null,
                child: Row(
                  children: [
                    const Icon(
                      Icons.filter_alt_off,
                      color: AppColors.textSecondary,
                      size: 18,
                    ),
                    const SizedBox(width: 12),
                    Text(l10n.allLevels),
                  ],
                ),
              ),
              ...LogLevel.values.map((level) => PopupMenuItem(
                value: level,
                child: Row(
                  children: [
                    Icon(
                      _getLevelIcon(level),
                      color: _getLevelColor(level),
                      size: 18,
                    ),
                    const SizedBox(width: 12),
                    Text(_getLevelName(level)),
                  ],
                ),
              )).toList(),
            ],
          ),
          // 更多操作按钮
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            tooltip: l10n.moreActions,
            onSelected: (value) {
              if (value == 'clear') {
                _clearLogs();
              } else if (value == 'export') {
                _exportLogs();
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'clear',
                child: Row(
                  children: [
                    const Icon(
                      Icons.delete_outline,
                      color: AppColors.error,
                      size: 18,
                    ),
                    const SizedBox(width: 12),
                    Text(l10n.clearLogs),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    const Icon(
                      Icons.save_alt,
                      color: AppColors.primary,
                      size: 18,
                    ),
                    const SizedBox(width: 12),
                    Text(l10n.exportLogs),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
          children: [
            // 筛选信息
            if (_selectedLevel != null || _searchQuery.isNotEmpty)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                decoration: BoxDecoration(
                  color: AppColors.backgroundStart,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.filter_alt,
                      size: 16,
                      color: AppColors.primary,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: RichText(
                        text: TextSpan(
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                          children: [
                            TextSpan(text: l10n.filterPrefix),
                            if (_selectedLevel != null)
                              TextSpan(
                                text: _getLevelName(_selectedLevel),
                                style: TextStyle(
                                  color: _getLevelColor(_selectedLevel!),
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            if (_selectedLevel != null && _searchQuery.isNotEmpty)
                              const TextSpan(text: ' + '),
                            if (_searchQuery.isNotEmpty)
                              TextSpan(
                                text: '"$_searchQuery"',
                                style: const TextStyle(
                                  fontStyle: FontStyle.italic,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.clear, size: 18, color: AppColors.textSecondary),
                      tooltip: l10n.clearFilter,
                      onPressed: () {
                        setState(() {
                          _selectedLevel = null;
                          _searchQuery = '';
                          _isSearchVisible = false;
                        });
                      },
                    ),
                  ],
                ),
              ),

          // 日志列表
          Expanded(
            child: filteredLogs.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.article_outlined,
                          size: 48,
                          color: AppColors.textSecondary.withValues(alpha: 0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          l10n.noLogs,
                          style: const TextStyle(
                            fontSize: 16,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        if (_selectedLevel != null || _searchQuery.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: TextButton.icon(
                              icon: const Icon(Icons.filter_alt_off, size: 16),
                              label: Text(l10n.clearFilter),
                              onPressed: () {
                                setState(() {
                                  _selectedLevel = null;
                                  _searchQuery = '';
                                  _isSearchVisible = false;
                                });
                              },
                              style: TextButton.styleFrom(
                                foregroundColor: AppColors.primary,
                              ),
                            ),
                          ),
                      ],
                    ),
                  )
                : ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 80), // 底部留出空间，避免被FAB遮挡
                    itemCount: filteredLogs.length,
                    itemBuilder: (context, index) {
                      final log = filteredLogs[index];
                      return _buildLogItem(log);
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          if (_scrollController.hasClients) {
            _scrollController.animateTo(
              0,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        },
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 4,
        mini: true,
        tooltip: l10n.scrollToLatestLog,
        child: const Icon(Icons.arrow_upward),
      ),
    ), // 关闭 AnnotatedRegion
  );
  }

  /// _buildLogItem
  ///
  /// DESCRIPTION:
  ///     Builds a single log entry widget with formatted display.
  ///     Creates a card with level indicator, timestamp, module, and message.
  ///
  /// PARAMETERS:
  ///     log - LogEntry object to display
  ///
  /// RETURNS:
  ///     Widget representing a single log entry
  Widget _buildLogItem(LogEntry log) {
    final timeStr = Formatters.formatTime(log.timestamp);
    final levelStr = log.level.toString().split('.').last.toUpperCase();
    final color = _getLevelColor(log.level);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: color.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () => _copyLog(log),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 时间、级别和模块
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 级别图标
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Icon(
                        _getLevelIcon(log.level),
                        color: color,
                        size: 14,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),

                  // 时间和模块
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            // 级别标签
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                              decoration: BoxDecoration(
                                color: color.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(3),
                              ),
                              child: Text(
                                levelStr,
                                style: TextStyle(
                                  fontSize: 9,
                                  color: color,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            const SizedBox(width: 4),

                            // 模块标签
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                              decoration: BoxDecoration(
                                color: AppColors.textSecondary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(3),
                              ),
                              child: Text(
                                log.module,
                                style: const TextStyle(
                                  fontSize: 9,
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Text(
                          timeStr,
                          style: const TextStyle(
                            fontSize: 10,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // 复制按钮
                  InkWell(
                    onTap: () => _copyLog(log),
                    borderRadius: BorderRadius.circular(4),
                    child: const Padding(
                      padding: EdgeInsets.all(4),
                      child: Icon(
                        Icons.copy,
                        size: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),

              // 分隔线
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 6.0),
                child: Divider(height: 1, color: AppColors.divider),
              ),

              // 日志消息
              Text(
                log.message,
                style: const TextStyle(
                  fontSize: 12,
                  color: AppColors.textPrimary,
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
