// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      design_system.dart
//  *
//  * DESCRIPTION :    应用程序设计系统定义文件，包含颜色、字体、间距、
//  *                  动画等设计规范，以及主题配置和样式管理
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  ******************************************************************************/

import 'package:flutter/material.dart';
import '../services/font_service.dart';
import '../core/dependency_injection.dart';

/// 设计系统核心配置
///
/// PURPOSE:
///     定义应用程序的设计系统规范，包括尺寸、间距、字体、
///     动画等核心设计元素，确保UI的一致性和可维护性
///
/// FEATURES:
///     - 应用窗口和组件尺寸定义
///     - 统一的间距和圆角系统
///     - 语言感知的字体配置
///     - 标准化的文本样式
///     - 阴影和动画配置
///
/// USAGE:
///     import 'package:panabit_client/utils/design_system.dart';
///
///     // 使用间距系统
///     padding: const EdgeInsets.all(DesignSystem.spacing16);
///
///     // 使用文本样式
///     style: DesignSystem.titleMedium;
///
///     // 使用颜色系统
///     color: AppColors.primary;
class DesignSystem {
  // 应用窗口尺寸
  static const double windowWidth = 590.0; // 恢复原始宽度
  static const double windowHeight = 844.0;
  static const double windowBorderRadius = 0.0; // 修改为方角

  // 侧边栏尺寸
  static const double sidebarWidth = 120.0; // 恢复原始宽度
  static const double sidebarCollapsedWidth = 60.0; // iOS收缩状态宽度
  static const double sidebarItemHeight = 112.0;

  // 间距系统
  static const double spacing4 = 4.0;
  static const double spacing8 = 8.0;
  static const double spacing12 = 12.0;
  static const double spacing16 = 16.0;
  static const double spacing20 = 20.0;
  static const double spacing24 = 24.0;
  static const double spacing32 = 32.0;
  static const double spacing42 = 16.0; // 侧边栏内边距 - 减少以适应英文文本
  static const double spacing46 = 46.0;
  static const double spacing86 = 86.0;

  // 圆角系统
  static const double radiusSmall = 4.0;
  static const double radiusMedium = 8.0;
  static const double radiusLarge = 12.0;

  // ==========================================================================
  // 字体系统配置
  // ==========================================================================

  /// 获取主要字体族名称
  ///
  /// DESCRIPTION:
  ///     获取当前语言环境下的主要字体族名称，
  ///     通过FontService提供语言感知的字体选择
  ///
  /// RETURNS:
  ///     String - 主要字体族名称，失败时返回默认字体
  static String get fontFamily {
    try {
      final fontService = serviceLocator<FontService>();
      return fontService.primaryFontFamily;
    } catch (e) {
      // 如果FontService不可用，返回默认字体
      return 'PingFang SC';
    }
  }

  /// 获取字体族回退列表
  ///
  /// DESCRIPTION:
  ///     获取字体族的回退列表，确保在主字体不可用时
  ///     能够正确显示文本内容
  ///
  /// RETURNS:
  ///     List<String> - 字体族回退列表
  static List<String> get fontFamilyFallback {
    try {
      final fontService = serviceLocator<FontService>();
      return fontService.fontFamilyFallback;
    } catch (e) {
      // 如果FontService不可用，返回默认回退字体
      return ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'];
    }
  }

  /// 创建带有语言感知字体的TextStyle
  ///
  /// DESCRIPTION:
  ///     创建支持多语言的TextStyle，通过FontService
  ///     自动选择合适的字体和字重映射
  ///
  /// PARAMETERS:
  ///     fontSize - 字体大小
  ///     fontWeight - 字体粗细
  ///     color - 文字颜色
  ///     height - 行高倍数
  ///     letterSpacing - 字符间距
  ///     decoration - 文字装饰
  ///     textBaseline - 文字基线
  ///
  /// RETURNS:
  ///     TextStyle - 配置了语言感知字体的文本样式
  static TextStyle createTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
    TextDecoration? decoration,
    TextBaseline? textBaseline,
    TextLeadingDistribution? leadingDistribution,
  }) {
    try {
      final fontService = serviceLocator<FontService>();
      return fontService.createTextStyleWithMappedWeight(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
        height: height,
        letterSpacing: letterSpacing,
        decoration: decoration,
        textBaseline: textBaseline,
        leadingDistribution: leadingDistribution,
      );
    } catch (e) {
      // 如果FontService不可用，使用默认样式
      return TextStyle(
        fontFamilyFallback: const ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'],
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
        height: height,
        letterSpacing: letterSpacing,
        decoration: decoration ?? TextDecoration.none,
        textBaseline: textBaseline,
        leadingDistribution: leadingDistribution,
      );
    }
  }

  // ==========================================================================
  // 标准文本样式定义
  // ==========================================================================

  /// 大标题文本样式
  ///
  /// DESCRIPTION:
  ///     用于页面主标题的大号文本样式，字重较粗，
  ///     适用于重要信息的突出显示
  static TextStyle get displayLarge => createTextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w700,
    letterSpacing: -0.05,
    height: 1.15, // 23/20
  );

  /// 中等标题文本样式
  ///
  /// DESCRIPTION:
  ///     用于次级标题的中等大小文本样式
  static TextStyle get displayMedium => createTextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );

  /// 大号标题文本样式
  ///
  /// DESCRIPTION:
  ///     用于页面标题的大号文本样式
  static TextStyle get headlineLarge => createTextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
  );

  /// 中号标题文本样式
  ///
  /// DESCRIPTION:
  ///     用于章节标题的中号文本样式
  static TextStyle get headlineMedium => createTextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
  );

  /// 大号标题文本样式
  ///
  /// DESCRIPTION:
  ///     用于卡片标题等的大号文本样式
  static TextStyle get titleLarge => createTextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );

  /// 中等标题文本样式
  ///
  /// DESCRIPTION:
  ///     用于组件标题的中等文本样式，支持字母基线对齐
  static TextStyle get titleMedium => createTextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 1.25, // 20/16
    textBaseline: TextBaseline.alphabetic,
  );

  /// 大号正文文本样式
  ///
  /// DESCRIPTION:
  ///     用于重要正文内容的大号文本样式
  static TextStyle get bodyLarge => createTextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
  );

  /// 中等正文文本样式
  ///
  /// DESCRIPTION:
  ///     用于一般正文内容的标准文本样式
  static TextStyle get bodyMedium => createTextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 1.43, // 20/14
    leadingDistribution: TextLeadingDistribution.even,
  );

  /// 小号正文文本样式
  ///
  /// DESCRIPTION:
  ///     用于次要正文内容的小号文本样式，字重稍粗
  static TextStyle get bodySmall => createTextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.43, // 20/14
  );

  /// 小号标签文本样式
  ///
  /// DESCRIPTION:
  ///     用于标签、说明文字等的小号文本样式
  static TextStyle get labelSmall => createTextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    height: 1.67, // 20/12
  );

  // 阴影系统
  static const List<BoxShadow> elevationLow = [
    BoxShadow(
      offset: Offset(0, 1),
      blurRadius: 2,
      color: Color(0x1A000000),
    ),
  ];

  static const List<BoxShadow> elevationMedium = [
    BoxShadow(
      offset: Offset(0, 2),
      blurRadius: 4,
      color: Color(0x1A000000),
    ),
  ];

  // 用户信息卡片阴影
  static const List<BoxShadow> cardShadow = [
    BoxShadow(
      offset: Offset(0, 4),
      blurRadius: 12,
      color: Color(0x26000000), // 15% 黑色阴影
    ),
  ];

  // 动画时长
  static const Duration animationFast = Duration(milliseconds: 150);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);

  // 动画曲线
  static const Curve curveStandard = Curves.easeInOut;
  static const Curve curveEmphasized = Curves.easeOutCubic;
}

/// 应用程序颜色系统
///
/// PURPOSE:
///     定义应用程序中使用的所有颜色常量，确保UI颜色的一致性
///     和设计规范的统一实施
///
/// FEATURES:
///     - 主色调和辅助色定义
///     - 背景和渐变色配置
///     - 文本和界面元素颜色
///     - 状态指示颜色
///     - 主题相关颜色配置
///
/// USAGE:
///     import 'package:panabit_client/utils/design_system.dart';
///
///     // 使用主色调
///     color: AppColors.primary;
///
///     // 使用状态颜色
///     color: AppColors.connected;
class AppColors {
  // ==========================================================================
  // 主色调系统
  // ==========================================================================

  /// 主要品牌色 - 绿色
  static const Color primary = Color(0xFF05683E);

  /// 主色调亮色变体
  static const Color primaryLight = Color(0xFF10B981);

  /// 主色调暗色变体
  static const Color primaryDark = Color(0xFF034A2B);

  /// 次要色调 - 灰色
  static const Color secondary = Color(0xFF6B7280);

  /// 强调色 - 蓝色
  static const Color accent = Color(0xFF3B82F6);

  // ==========================================================================
  // 背景色系统
  // ==========================================================================

  /// 背景渐变起始色
  static const Color backgroundStart = Color(0xFFFCF6F1);

  /// 背景渐变结束色
  static const Color backgroundEnd = Color(0xFFFCF6F1);

  /// 背景渐变色列表
  static const List<Color> backgroundGradient = [backgroundStart, backgroundEnd];

  /// 深色主题背景色
  static const Color backgroundDark = Color(0xFF1A1A1A);

  /// 深色主题表面色
  static const Color surfaceDark = Color(0xFF2A2A2A);

  // ==========================================================================
  // 侧边栏颜色系统
  // ==========================================================================

  /// 侧边栏背景色
  static const Color sidebarBackground = Color(0xFF313F51);

  /// 侧边栏边框色 - 24%透明度白色
  static const Color sidebarBorder = Color(0x3DFFFFFF);

  /// 侧边栏激活状态背景色
  static const Color sidebarActiveBackground = Color(0xFF272D37);

  /// 侧边栏激活状态边框色
  static const Color sidebarActiveBorder = Color(0xFF05683E);

  // ==========================================================================
  // 文本颜色系统
  // ==========================================================================

  /// 主要文本颜色 - 深灰色
  static const Color textPrimary = Color(0xFF1F2937);

  /// 界面通用文本颜色 - 深灰色 (用于白色背景界面)
  static const Color textDarkGray = Color(0xFF1F2937);

  /// 次要文本颜色 - 灰色
  static const Color textSecondary = Color(0xFFA7B1C2);

  /// 浅色文本颜色 - 白色
  static const Color textLight = Color(0xFFFFFFFF);

  /// 占位符文本颜色
  static const Color textPlaceholder = Color(0xFFA7B1C2);

  // ==========================================================================
  // 输入框颜色系统
  // ==========================================================================

  /// 输入框背景色
  static const Color inputBackground = Color(0xFFFAFBFC);

  /// 输入框边框色
  static const Color inputBorder = Color(0xFFDAE0E6);

  /// 输入框聚焦边框色
  static const Color inputFocusBorder = Color(0xFF05683E);

  // ==========================================================================
  // 状态指示颜色
  // ==========================================================================

  /// 连接成功状态色 - 绿色
  static const Color connected = Color(0xFF33DB87);

  /// 断开连接状态色 - 绿色
  static const Color disconnected = Color(0xFF05683E);

  /// 正在连接状态色 - 绿色
  static const Color connecting = Color(0xFF05683E);

  /// 错误状态色 - 红色
  static const Color error = Color(0xFFEF4444);

  /// 成功状态色 - 绿色
  static const Color success = Color(0xFF05683E);

  /// 警告状态色 - 黄色
  static const Color warning = Color(0xFFF59E0B);

  /// 信息状态色 - 蓝色
  static const Color info = Color(0xFF2196F3);

  // ==========================================================================
  // 界面元素颜色
  // ==========================================================================

  /// 遮罩覆盖层颜色 - 12%透明度黑色
  static const Color maskOverlay = Color(0x1F000000);

  /// 卡片背景色 - 白色
  static const Color cardBackground = Color(0xFFFFFFFF);

  /// 分割线颜色
  static const Color divider = Color(0xFFE5E7EB);

  /// 用户信息卡片背景色 - 透明
  static const Color userCardBackground = Colors.transparent;

  /// 用户信息卡片边框色 - 20%透明度白色
  static const Color userCardBorder = Color(0x33FFFFFF);

  // ==========================================================================
  // 渐变色系统
  // ==========================================================================

  /// 护盾渐变色1 - 蓝灰色
  static const Color gradientBlue1 = Color(0xFF66809E);

  /// 护盾渐变色2 - 深蓝灰色
  static const Color gradientBlue2 = Color(0xFF334050);

  /// 护盾渐变色列表
  static const List<Color> shieldGradient = [gradientBlue1, gradientBlue2];

  // ==========================================================================
  // 图标颜色系统
  // ==========================================================================

  /// 主要图标颜色 - 绿色
  static const Color iconPrimary = Color(0xFF05683E);

  /// 次要图标颜色 - 深蓝灰色
  static const Color iconSecondary = Color(0xFF3A495B);

  /// 第三级图标颜色 - 深蓝灰色
  static const Color iconTertiary = Color(0xFF3C4C5E);

  /// 搜索图标颜色 - 蓝灰色
  static const Color searchIconColor = Color(0xFF66809E);

  // ==========================================================================
  // 品牌和特殊颜色
  // ==========================================================================

  /// Logo绿色
  static const Color logoGreen = Color(0xFF05683E);

  /// Logo米色
  static const Color logoBeige = Color(0xFFFDCBAD);
}

/// 设计系统主题配置
///
/// PURPOSE:
///     基于设计系统创建MaterialApp主题配置，提供统一的
///     主题管理和样式配置，确保应用UI的一致性
///
/// FEATURES:
///     - 基于FontService的字体配置
///     - 统一的颜色主题配置
///     - 标准化的组件样式
///     - 支持回退的默认主题
///     - Material 3设计规范支持
///
/// USAGE:
///     import 'package:panabit_client/utils/design_system.dart';
///
///     // 创建应用主题
///     theme: DesignSystemTheme.createAppTheme();
class DesignSystemTheme {
  /// 创建应用程序主题
  ///
  /// DESCRIPTION:
  ///     创建基于新设计系统的MaterialApp主题配置，
  ///     集成字体服务和新颜色系统
  ///
  /// RETURNS:
  ///     ThemeData - 配置了新设计系统的主题
  static ThemeData createAppTheme() {
    try {
      final fontService = serviceLocator<FontService>();
      return _createThemeWithFontService(fontService);
    } catch (e) {
      // 如果FontService不可用，返回默认主题
      return _createDefaultTheme();
    }
  }

  /// 使用FontService创建主题
  ///
  /// DESCRIPTION:
  ///     创建支持多语言字体配置的主题，
  ///     集成FontService提供的字体配置和新设计系统样式
  ///
  /// PARAMETERS:
  ///     fontService - 字体服务实例，提供语言感知的字体配置
  ///
  /// RETURNS:
  ///     ThemeData - 配置了字体服务的主题
  static ThemeData _createThemeWithFontService(FontService fontService) {
    return ThemeData(
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: Colors.transparent, // 使用透明背景，让渐变背景显示
      colorScheme: const ColorScheme.light(
        primary: AppColors.primary,
        secondary: AppColors.secondary,
        tertiary: AppColors.accent,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        surface: AppColors.cardBackground,
        error: AppColors.error,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(DesignSystem.radiusLarge),
            bottomRight: Radius.circular(DesignSystem.radiusLarge),
          ),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: DesignSystem.spacing24,
            vertical: DesignSystem.spacing16,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.cardBackground,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
          borderSide: const BorderSide(color: AppColors.divider),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
          borderSide: const BorderSide(color: AppColors.divider),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: DesignSystem.spacing16,
          vertical: DesignSystem.spacing16,
        ),
      ),
      cardTheme: CardThemeData(
        color: AppColors.cardBackground,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        ),
      ),
      textTheme: TextTheme(
        displayLarge: fontService.applyFontFamily(DesignSystem.displayLarge),
        displayMedium: fontService.applyFontFamily(DesignSystem.displayMedium),
        headlineLarge: fontService.applyFontFamily(DesignSystem.headlineLarge),
        headlineMedium: fontService.applyFontFamily(DesignSystem.headlineMedium),
        titleLarge: fontService.applyFontFamily(DesignSystem.titleLarge),
        titleMedium: fontService.applyFontFamily(DesignSystem.titleMedium),
        bodyLarge: fontService.applyFontFamily(DesignSystem.bodyLarge),
        bodyMedium: fontService.applyFontFamily(DesignSystem.bodyMedium),
        bodySmall: fontService.applyFontFamily(DesignSystem.bodySmall),
      ),
      fontFamily: fontService.primaryFontFamily,
      fontFamilyFallback: fontService.fontFamilyFallback,
      useMaterial3: true,
    );
  }

  /// 创建默认主题
  ///
  /// DESCRIPTION:
  ///     创建不依赖FontService的默认主题配置，
  ///     用于FontService不可用时的回退方案
  ///
  /// RETURNS:
  ///     ThemeData - 默认主题配置
  static ThemeData _createDefaultTheme() {
    return ThemeData(
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: Colors.transparent,
      colorScheme: const ColorScheme.light(
        primary: AppColors.primary,
        secondary: AppColors.secondary,
        tertiary: AppColors.accent,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        surface: AppColors.cardBackground,
        error: AppColors.error,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(DesignSystem.radiusLarge),
            bottomRight: Radius.circular(DesignSystem.radiusLarge),
          ),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
          ),
        ),
      ),
      textTheme: TextTheme(
        displayLarge: DesignSystem.displayLarge,
        displayMedium: DesignSystem.displayMedium,
        headlineLarge: DesignSystem.headlineLarge,
        headlineMedium: DesignSystem.headlineMedium,
        titleLarge: DesignSystem.titleLarge,
        titleMedium: DesignSystem.titleMedium,
        bodyLarge: DesignSystem.bodyLarge,
        bodyMedium: DesignSystem.bodyMedium,
        bodySmall: DesignSystem.bodySmall,
      ),
      fontFamilyFallback: const ['PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Roboto'],
      useMaterial3: true,
    );
  }
}
