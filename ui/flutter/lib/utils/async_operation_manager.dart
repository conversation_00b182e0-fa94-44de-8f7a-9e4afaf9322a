// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      async_operation_manager.dart
//  *
//  * DESCRIPTION :    异步操作管理器，提供非阻塞异步操作执行、计算密集型任务处理、
//  *                  防抖动操作管理等功能，解决UI阻塞和性能问题
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  ******************************************************************************/

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';

/// AsyncOperationManager
///
/// PURPOSE:
///     提供非阻塞的异步操作执行机制，解决UI阻塞问题，
///     支持操作取消、超时处理、加载指示器等功能
///
/// FEATURES:
///     - 非阻塞异步操作执行
///     - 计算密集型任务Isolate处理
///     - 操作重复检测和防护
///     - 超时处理和取消机制
///     - 加载指示器自动管理
///     - 防抖动操作支持
///
/// USAGE:
///     import 'package:panabit_client/utils/async_operation_manager.dart';
///
///     // 执行非阻塞操作
///     final result = await AsyncOperationManager.executeNonBlocking(
///       operation: () async => await someAsyncTask(),
///       operationId: 'unique_operation',
///       showLoading: true,
///       context: context,
///     );
class AsyncOperationManager {
  static final Map<String, Timer> _activeOperations = {};
  static final Map<String, Completer> _operationCompleters = {};
  static final Map<String, CancelToken> _cancelTokens = {};

  /// executeNonBlocking
  ///
  /// DESCRIPTION:
  ///     执行非阻塞异步操作，避免UI阻塞，支持超时、取消和加载指示器
  ///
  /// PARAMETERS:
  ///     operation - 要执行的异步操作函数
  ///     operationId - 操作ID，用于防止重复操作
  ///     timeout - 超时时间，默认25秒
  ///     showLoading - 是否显示加载指示器
  ///     context - BuildContext（显示加载指示器时需要）
  ///     loadingMessage - 加载消息
  ///     forceExecute - 是否强制执行，即使有相同operationId的操作正在进行
  ///
  /// RETURNS:
  ///     Future<T?> - 操作结果，如果操作被取消或重复则返回null
  ///
  /// THROWS:
  ///     Exception - 操作执行过程中发生的异常
  static Future<T?> executeNonBlocking<T>({
    required Future<T> Function() operation,
    String? operationId,
    Duration timeout = const Duration(seconds: 25), // 与iOS和Android后端保持一致
    bool showLoading = false,
    BuildContext? context,
    String? loadingMessage,
    bool forceExecute = false,
  }) async {
    // 检查重复操作
    if (operationId != null && _activeOperations.containsKey(operationId)) {
      if (!forceExecute) {
        // 生产环境不输出调试信息到控制台
        // // debugPrint('操作 $operationId 正在进行中，跳过重复请求');
        return null;
      } else {
        // 强制执行时，先清理之前的操作
        _cleanupOperation(operationId);
      }
    }

    // 创建取消令牌
    final cancelToken = CancelToken();
    if (operationId != null) {
      _cancelTokens[operationId] = cancelToken;
    }

    // 显示加载指示器
    if (showLoading && context != null) {
      final l10n = AppLocalizations.of(context)!;
      final message = loadingMessage ?? l10n.processing;
      _showLoadingDialog(context, message, operationId);
    }

    // 设置超时定时器
    Timer? timeoutTimer;
    if (operationId != null) {
      timeoutTimer = Timer(timeout, () {
        _handleTimeout(operationId, context);
      });
      _activeOperations[operationId] = timeoutTimer;
    }

    try {
      // 在微任务中执行操作，避免阻塞UI
      final result = await Future.microtask(() async {
        if (cancelToken.isCancelled) {
          throw OperationCancelledException();
        }
        return await operation().timeout(timeout);
      });

      // 清理资源
      if (operationId != null) {
        _cleanupOperation(operationId);
      }

      // 关闭加载对话框
      if (showLoading && context != null && context.mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      return result;
    } catch (e) {
      // 清理资源
      if (operationId != null) {
        _cleanupOperation(operationId);
      }

      // 关闭加载对话框
      if (showLoading && context != null && context.mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      if (e is OperationCancelledException) {
        // debugPrint('操作已取消: $operationId');
        return null;
      }

      rethrow;
    }
  }

  /// executeComputation
  ///
  /// DESCRIPTION:
  ///     执行计算密集型操作，在Isolate中运行避免阻塞主线程
  ///
  /// PARAMETERS:
  ///     computation - 计算函数
  ///     data - 传递给计算函数的数据
  ///     operationId - 操作ID，用于防止重复操作
  ///     context - BuildContext，用于显示加载指示器
  ///     loadingMessage - 加载消息
  ///
  /// RETURNS:
  ///     Future<T?> - 计算结果，如果操作重复则返回null
  ///
  /// THROWS:
  ///     Exception - 计算过程中发生的异常
  static Future<T?> executeComputation<T, D>({
    required T Function(D) computation,
    required D data,
    String? operationId,
    BuildContext? context,
    String? loadingMessage,
  }) async {
    // 检查重复操作
    if (operationId != null && _activeOperations.containsKey(operationId)) {
      // 生产环境不输出调试信息到控制台
      // // debugPrint('计算操作 $operationId 正在进行中，跳过重复请求');
      return null;
    }

    // 显示加载指示器
    if (context != null) {
      final l10n = AppLocalizations.of(context)!;
      final message = loadingMessage ?? l10n.calculating;
      _showLoadingDialog(context, message, operationId);
    }

    try {
      // 在Isolate中执行计算
      final result = await compute(computation, data);

      // 清理资源
      if (operationId != null) {
        _cleanupOperation(operationId);
      }

      // 关闭加载对话框
      if (context != null && context.mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      return result;
    } catch (e) {
      // 清理资源
      if (operationId != null) {
        _cleanupOperation(operationId);
      }

      // 关闭加载对话框
      if (context != null && context.mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      rethrow;
    }
  }

  /// cancelOperation
  ///
  /// DESCRIPTION:
  ///     取消指定的异步操作
  ///
  /// PARAMETERS:
  ///     operationId - 要取消的操作ID
  ///
  /// RETURNS:
  ///     void
  static void cancelOperation(String operationId) {
    final cancelToken = _cancelTokens[operationId];
    if (cancelToken != null) {
      cancelToken.cancel();
    }
    _cleanupOperation(operationId);
  }

  /// 显示加载对话框
  static void _showLoadingDialog(BuildContext context, String message, String? operationId) {
    final l10n = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false,
        child: AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 20),
              Expanded(child: Text(message)),
            ],
          ),
          actions: operationId != null ? [
            TextButton(
              onPressed: () {
                cancelOperation(operationId);
                Navigator.of(context).pop();
              },
              child: Text(l10n.cancel),
            ),
          ] : null,
        ),
      ),
    );
  }

  /// 处理超时
  static void _handleTimeout(String operationId, BuildContext? context) {
    _cleanupOperation(operationId);

    if (context != null) {
      // 关闭加载对话框
      if (Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      // 显示超时消息
      final l10n = AppLocalizations.of(context)!;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(l10n.operationTimeout)),
      );
    }
  }

  /// 清理操作资源
  static void _cleanupOperation(String operationId) {
    _activeOperations[operationId]?.cancel();
    _activeOperations.remove(operationId);
    _operationCompleters.remove(operationId);
    _cancelTokens.remove(operationId);
  }

  /// 清理所有操作
  static void cleanupAll() {
    for (final timer in _activeOperations.values) {
      timer.cancel();
    }
    for (final token in _cancelTokens.values) {
      token.cancel();
    }
    _activeOperations.clear();
    _operationCompleters.clear();
    _cancelTokens.clear();
  }
}

/// 取消令牌
class CancelToken {
  bool _isCancelled = false;

  bool get isCancelled => _isCancelled;

  void cancel() {
    _isCancelled = true;
  }
}

/// 操作取消异常
class OperationCancelledException implements Exception {
  final String message;

  OperationCancelledException([this.message = 'Operation cancelled']);

  @override
  String toString() => message;
}

/// 防抖动操作管理器
class DebounceManager {
  static final Map<String, Timer> _debounceTimers = {};

  /// 防抖动执行操作
  ///
  /// [operationId] 操作ID
  /// [operation] 要执行的操作
  /// [delay] 防抖动延迟时间
  static void debounce({
    required String operationId,
    required VoidCallback operation,
    Duration delay = const Duration(milliseconds: 500),
  }) {
    // 取消之前的定时器
    _debounceTimers[operationId]?.cancel();

    // 创建新的定时器
    _debounceTimers[operationId] = Timer(delay, () {
      operation();
      _debounceTimers.remove(operationId);
    });
  }

  /// 取消防抖动操作
  static void cancel(String operationId) {
    _debounceTimers[operationId]?.cancel();
    _debounceTimers.remove(operationId);
  }

  /// 清理所有防抖动操作
  static void cleanupAll() {
    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
  }
}
