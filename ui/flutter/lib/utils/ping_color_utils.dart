// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      ping_color_utils.dart
//  *
//  * DESCRIPTION :    Ping延迟颜色工具类，统一管理延迟颜色显示标准，
//  *                  提供延迟值的颜色映射、文本显示和等级评定功能
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  ******************************************************************************/

import 'package:flutter/material.dart';

import 'design_system.dart';

/// PingColorUtils
///
/// PURPOSE:
///     Ping延迟颜色工具类，统一管理延迟颜色显示标准
///
/// FEATURES:
///     - 延迟颜色映射：根据ping值返回对应颜色（绿色0-100ms，黄色100-200ms，红色200ms+）
///     - 文本显示：格式化ping值为可读文本
///     - 状态描述：提供延迟状态的中文描述
///     - 等级评定：将ping值转换为1-5星等级
///     - 设计系统兼容：支持新旧设计系统颜色
///
/// USAGE:
///     使用静态方法获取ping值对应的颜色、文本和等级信息
class PingColorUtils {
  /// getPingColor
  ///
  /// DESCRIPTION:
  ///     获取延迟颜色，统一标准：绿色(0-100ms)，黄色(100-200ms)，红色(200ms+)，灰色(不可达)
  ///
  /// PARAMETERS:
  ///     ping - 延迟值（毫秒）
  ///     useNewColors - 是否使用新设计系统颜色，默认为false使用旧颜色系统
  ///
  /// RETURNS:
  ///     Color - 对应延迟范围的颜色
  static Color getPingColor(int ping, {bool useNewColors = false}) {
    if (ping <= 0) {
      // 0ms 显示不可达 - 灰色
      return AppColors.textSecondary;
    }

    if (ping < 100) {
      // 0-100ms 绿色
      return AppColors.connected;
    }

    if (ping < 200) {
      // 100-200ms 黄色
      return const Color(0xFFFFC107); // 固定黄色
    }

    // 200ms+ 红色
    return AppColors.error;
  }

  /// getPingText
  ///
  /// DESCRIPTION:
  ///     获取延迟文本显示，格式化ping值为可读文本
  ///
  /// PARAMETERS:
  ///     ping - 延迟值（毫秒）
  ///     unreachableText - 不可达时显示的文本，默认为中文"不可达"
  ///
  /// RETURNS:
  ///     String - 格式化的延迟文本（如"50ms"或"不可达"）
  static String getPingText(int ping, {String unreachableText = 'Unreachable'}) { // TODO: Add to localization
    return ping > 0 ? '${ping}ms' : unreachableText;
  }

  /// getPingDescription
  ///
  /// DESCRIPTION:
  ///     获取延迟状态描述，提供延迟质量的中文描述
  ///
  /// PARAMETERS:
  ///     ping - 延迟值（毫秒）
  ///
  /// RETURNS:
  ///     String - 延迟状态描述（极佳、良好、一般、较差、很差、不可达）
  static String getPingDescription(int ping) {
    // TODO: Add proper localization for ping descriptions
    if (ping <= 0) return 'Unreachable';
    if (ping < 50) return 'Excellent';
    if (ping < 100) return 'Good';
    if (ping < 200) return 'Fair';
    if (ping < 300) return 'Poor';
    return 'Very Poor';
  }

  /// getPingRating
  ///
  /// DESCRIPTION:
  ///     获取延迟等级，将ping值转换为1-5星等级评定
  ///
  /// PARAMETERS:
  ///     ping - 延迟值（毫秒）
  ///
  /// RETURNS:
  ///     int - 延迟等级（0=不可达，1-5星，5星最佳）
  static int getPingRating(int ping) {
    if (ping <= 0) return 0;
    if (ping < 50) return 5;
    if (ping < 100) return 4;
    if (ping < 200) return 3;
    if (ping < 300) return 2;
    return 1;
  }
}
