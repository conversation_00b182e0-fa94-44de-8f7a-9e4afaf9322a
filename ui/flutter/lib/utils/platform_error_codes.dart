/**
 * FILE: platform_error_codes.dart
 *
 * DESCRIPTION:
 *     Unified error code mapping for Flutter Platform Channel integration.
 *     Provides consistent error code mapping between Flutter and iOS/macOS backend.
 *     Ensures compatibility with Go backend error system.
 *
 * AUTHOR: wei
 * HISTORY: 01/07/2025 create unified error code mapping system
 */

/// 错误类型枚举
enum ErrorType {
  vpnService,
  network,
  authentication,
  tunnel,
  configuration,
  platform,
  protocol,
  general,
}

/// Platform Channel错误码映射类
///
/// PURPOSE:
///     提供Flutter与iOS/macOS后端之间统一的错误码映射，
///     确保错误码的一致性和兼容性
///
/// FEATURES:
///     - 统一的错误码定义和映射
///     - 与iOS/macOS VPNService错误码完全兼容
///     - 与Go后端错误码系统保持一致
///     - 支持错误类型分类和严重程度判断
///
/// USAGE:
///     // 检查是否是VPN服务错误
///     if (PlatformErrorCodes.isVPNServiceError(errorCode)) {
///       // 处理VPN服务错误
///     }
///
///     // 获取错误类型
///     final errorType = PlatformErrorCodes.getErrorType(errorCode);
class PlatformErrorCodes {
  // ============================================================================
  // VPN SERVICE ERROR CODES (1001-1015)
  // 与iOS/macOS PlatformChannelExtensions.swift中的VPNServiceError.errorCode保持一致
  // ============================================================================
  
  /// VPN服务未启动
  static const int serviceNotStarted = 1001;
  
  /// VPN服务已启动
  static const int serviceAlreadyStarted = 1002;
  
  /// 连接管理器错误
  static const int connectionManagerError = 1003;
  
  /// 服务器服务错误
  static const int serverServiceError = 1004;
  
  /// 配置无效
  static const int configurationInvalid = 1005;
  
  /// VPN权限被拒绝
  static const int vpnPermissionDenied = 1006;
  
  /// 网络扩展错误
  static const int networkExtensionError = 1007;
  
  /// 操作进行中
  static const int operationInProgress = 1008;
  
  /// 未配置凭据
  static const int noCredentialsConfigured = 1009;
  
  /// 连接进行中
  static const int connectionInProgress = 1010;
  
  /// 断开连接进行中
  static const int disconnectionInProgress = 1011;
  
  /// 网络扩展不可用
  static const int networkExtensionNotAvailable = 1012;
  
  /// 未选择服务器
  static const int serverNotSelected = 1013;
  
  /// 认证失败
  static const int authenticationFailed = 1014;
  
  /// Platform Channel错误
  static const int platformChannelError = 1015;

  // ============================================================================
  // STANDARDIZED ERROR CODES (1016-6999)
  // 与iOS/macOS ErrorCode枚举和Go后端错误码系统保持一致
  // ============================================================================
  
  // 网络错误 (1016-1999)
  static const int networkUnreachable = 1016;
  static const int networkTimeout = 1017;
  static const int networkDNSFailure = 1018;
  static const int networkConnectionReset = 1019;
  static const int networkConnectionClosed = 1020;
  static const int networkProxyError = 1021;
  static const int networkTLSError = 1022;
  
  // 认证错误 (2000-2999)
  static const int authInvalidCredentials = 2000;
  static const int authExpiredCredentials = 2001;
  static const int authRateLimited = 2002;
  static const int authAccountLocked = 2003;
  static const int authTokenInvalid = 2004;
  static const int authTokenExpired = 2005;
  static const int authMissingCredentials = 2006;
  
  // 隧道错误 (3000-3999)
  static const int tunnelInitFailed = 3000;
  static const int tunnelClosedUnexpected = 3001;
  static const int tunnelPacketDropped = 3002;
  static const int tunnelDeviceError = 3003;
  static const int tunnelRouteError = 3004;
  static const int tunnelDNSError = 3005;
  static const int tunnelEncryptionError = 3006;
  
  // 配置错误 (4000-4999)
  static const int configInvalid = 4000;
  static const int configMissing = 4001;
  static const int configPermissionDenied = 4002;
  static const int configReadError = 4003;
  static const int configWriteError = 4004;
  static const int configParseError = 4005;
  static const int configValidationError = 4006;
  
  // 平台错误 (5000-5999)
  static const int platformUnsupported = 5000;
  static const int platformPermissionDenied = 5001;
  static const int platformDriverError = 5002;
  static const int platformSystemError = 5003;
  static const int platformResourceError = 5004;
  static const int platformNetworkError = 5005;
  static const int platformFirewallError = 5006;
  
  // 协议错误 (6000-6999)
  static const int protocolVersionMismatch = 6000;
  static const int protocolInvalidFormat = 6001;
  static const int protocolUnsupported = 6002;
  static const int protocolHandshakeFailed = 6003;
  static const int protocolEncryptionError = 6004;
  static const int protocolDecryptionError = 6005;
  static const int protocolAuthError = 6006;

  // ============================================================================
  // ERROR TYPE CLASSIFICATION
  // ============================================================================

  /// 检查是否是VPN服务错误
  static bool isVPNServiceError(int code) {
    return code >= 1001 && code <= 1015;
  }

  /// 检查是否是网络错误
  static bool isNetworkError(int code) {
    return code >= 1016 && code <= 1999;
  }

  /// 检查是否是认证错误
  static bool isAuthError(int code) {
    return code >= 2000 && code <= 2999;
  }

  /// 检查是否是隧道错误
  static bool isTunnelError(int code) {
    return code >= 3000 && code <= 3999;
  }

  /// 检查是否是配置错误
  static bool isConfigError(int code) {
    return code >= 4000 && code <= 4999;
  }

  /// 检查是否是平台错误
  static bool isPlatformError(int code) {
    return code >= 5000 && code <= 5999;
  }

  /// 检查是否是协议错误
  static bool isProtocolError(int code) {
    return code >= 6000 && code <= 6999;
  }

  /// 获取错误类型
  static ErrorType getErrorType(int code) {
    if (isVPNServiceError(code)) return ErrorType.vpnService;
    if (isNetworkError(code)) return ErrorType.network;
    if (isAuthError(code)) return ErrorType.authentication;
    if (isTunnelError(code)) return ErrorType.tunnel;
    if (isConfigError(code)) return ErrorType.configuration;
    if (isPlatformError(code)) return ErrorType.platform;
    if (isProtocolError(code)) return ErrorType.protocol;
    return ErrorType.general;
  }

  /// 获取错误类型字符串
  static String getErrorTypeString(int code) {
    switch (getErrorType(code)) {
      case ErrorType.vpnService:
        return 'vpn_service';
      case ErrorType.network:
        return 'network';
      case ErrorType.authentication:
        return 'authentication';
      case ErrorType.tunnel:
        return 'tunnel';
      case ErrorType.configuration:
        return 'configuration';
      case ErrorType.platform:
        return 'platform';
      case ErrorType.protocol:
        return 'protocol';
      case ErrorType.general:
        return 'general';
    }
  }

  /// 检查错误是否可恢复
  static bool isRecoverable(int code) {
    switch (getErrorType(code)) {
      case ErrorType.network:
        return true; // 网络错误通常可恢复
      case ErrorType.authentication:
        return code == authTokenExpired || code == authRateLimited;
      case ErrorType.tunnel:
        return code != tunnelDeviceError; // 设备错误通常不可恢复
      case ErrorType.configuration:
        return false; // 配置错误需要用户干预
      case ErrorType.platform:
        return code == platformResourceError; // 资源错误可能可恢复
      case ErrorType.protocol:
        return code == protocolVersionMismatch; // 版本不匹配可能可恢复
      case ErrorType.vpnService:
        return code == operationInProgress || code == connectionInProgress || code == disconnectionInProgress;
      case ErrorType.general:
        return true;
    }
  }
}
