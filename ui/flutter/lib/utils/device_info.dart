// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      device_info.dart
//  *
//  * DESCRIPTION :    设备信息工具类，提供跨平台的设备唯一标识符获取功能，
//  *                  支持Windows、macOS、Linux、Android、iOS和Web平台
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  ******************************************************************************/

import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

/// 设备信息工具类
///
/// PURPOSE:
///     提供跨平台的设备唯一标识符获取功能，确保在不同平台上
///     都能获取到稳定且唯一的设备标识符
///
/// FEATURES:
///     - 支持多平台设备ID获取（Windows、macOS、Linux、Android、iOS、Web）
///     - 设备ID缓存机制，提高性能
///     - 备用方案确保在任何情况下都能获取到设备ID
///     - 单例模式确保全局唯一实例
///     - 自动错误处理和回退机制
///
/// USAGE:
///     import 'package:panabit_client/utils/device_info.dart';
///
///     // 获取设备ID
///     final deviceInfo = DeviceInfo();
///     final deviceId = await deviceInfo.getDeviceId();
class DeviceInfo {
  /// 单例实例
  static final DeviceInfo _instance = DeviceInfo._internal();

  /// 设备信息插件实例
  static final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();

  /// 缓存的设备ID
  String? _cachedDeviceId;

  /// 工厂构造函数
  ///
  /// DESCRIPTION:
  ///     返回DeviceInfo的单例实例，确保全局只有一个实例
  ///
  /// RETURNS:
  ///     DeviceInfo - 单例实例
  factory DeviceInfo() {
    return _instance;
  }

  /// 私有构造函数
  ///
  /// DESCRIPTION:
  ///     私有构造函数，用于实现单例模式
  DeviceInfo._internal();

  /// 获取设备唯一标识符
  ///
  /// DESCRIPTION:
  ///     根据不同平台使用相应的方法获取设备唯一标识符，
  ///     支持缓存机制以提高性能，包含错误处理和回退方案
  ///
  /// PLATFORM SUPPORT:
  ///     - Windows: 使用计算机名和产品ID组合
  ///     - macOS: 使用系统GUID
  ///     - Linux: 使用机器ID
  ///     - Android: 使用Android ID
  ///     - iOS: 使用identifierForVendor
  ///     - Web: 生成并存储UUID
  ///
  /// RETURNS:
  ///     Future<String> - 设备唯一标识符
  ///
  /// THROWS:
  ///     无异常抛出，失败时使用备用方案
  Future<String> getDeviceId() async {
    // 如果已经缓存了设备ID，直接返回
    if (_cachedDeviceId != null) {
      return _cachedDeviceId!;
    }
    
    String deviceId;
    
    try {
      if (kIsWeb) {
        // Web平台使用localStorage存储生成的UUID
        deviceId = await _getWebDeviceId();
      } else if (Platform.isWindows) {
        // Windows平台
        deviceId = await _getWindowsDeviceId();
      } else if (Platform.isMacOS) {
        // macOS平台
        deviceId = await _getMacOSDeviceId();
      } else if (Platform.isLinux) {
        // Linux平台
        deviceId = await _getLinuxDeviceId();
      } else if (Platform.isAndroid) {
        // Android平台
        deviceId = await _getAndroidDeviceId();
      } else if (Platform.isIOS) {
        // iOS平台
        deviceId = await _getIOSDeviceId();
      } else {
        // 其他平台生成一个UUID并存储
        deviceId = await _getFallbackDeviceId();
      }
    } catch (e) {
      // 如果获取设备ID失败，使用备用方法
      deviceId = await _getFallbackDeviceId();
    }
    
    // 缓存设备ID
    _cachedDeviceId = deviceId;
    return deviceId;
  }
  
  /// 获取Windows平台设备ID
  ///
  /// DESCRIPTION:
  ///     获取Windows平台的设备唯一标识符，使用计算机名和产品ID的组合
  ///     确保在同一台Windows机器上的唯一性
  ///
  /// RETURNS:
  ///     Future<String> - Windows设备ID（格式：计算机名-产品ID）
  Future<String> _getWindowsDeviceId() async {
    final windowsInfo = await _deviceInfoPlugin.windowsInfo;
    return '${windowsInfo.computerName}-${windowsInfo.productId}';
  }

  /// 获取macOS平台设备ID
  ///
  /// DESCRIPTION:
  ///     获取macOS平台的设备唯一标识符，优先使用系统GUID，
  ///     如果不可用则生成UUID作为备用方案
  ///
  /// RETURNS:
  ///     Future<String> - macOS设备ID
  Future<String> _getMacOSDeviceId() async {
    final macOsInfo = await _deviceInfoPlugin.macOsInfo;
    return macOsInfo.systemGUID ?? const Uuid().v4();
  }

  /// 获取Linux平台设备ID
  ///
  /// DESCRIPTION:
  ///     获取Linux平台的设备唯一标识符，优先使用机器ID，
  ///     如果不可用则生成UUID作为备用方案
  ///
  /// RETURNS:
  ///     Future<String> - Linux设备ID
  Future<String> _getLinuxDeviceId() async {
    final linuxInfo = await _deviceInfoPlugin.linuxInfo;
    return linuxInfo.machineId ?? const Uuid().v4();
  }

  /// 获取Android平台设备ID
  ///
  /// DESCRIPTION:
  ///     获取Android平台的设备唯一标识符，使用Android系统提供的设备ID
  ///
  /// RETURNS:
  ///     Future<String> - Android设备ID
  Future<String> _getAndroidDeviceId() async {
    final androidInfo = await _deviceInfoPlugin.androidInfo;
    return androidInfo.id;
  }

  /// 获取iOS平台设备ID
  ///
  /// DESCRIPTION:
  ///     获取iOS平台的设备唯一标识符，优先使用identifierForVendor，
  ///     如果不可用则生成UUID作为备用方案
  ///
  /// RETURNS:
  ///     Future<String> - iOS设备ID
  Future<String> _getIOSDeviceId() async {
    final iosInfo = await _deviceInfoPlugin.iosInfo;
    return iosInfo.identifierForVendor ?? const Uuid().v4();
  }
  
  /// 获取Web平台设备ID
  ///
  /// DESCRIPTION:
  ///     获取Web平台的设备唯一标识符，由于Web平台无法获取硬件信息，
  ///     生成UUID并存储在本地存储中以保持一致性
  ///
  /// RETURNS:
  ///     Future<String> - Web平台设备ID（存储的UUID）
  Future<String> _getWebDeviceId() async {
    final prefs = await SharedPreferences.getInstance();
    String? deviceId = prefs.getString('device_id');

    if (deviceId?.isEmpty ?? true) {
      deviceId = const Uuid().v4();
      await prefs.setString('device_id', deviceId);
    }

    return deviceId!;
  }

  /// 备用设备ID获取方法
  ///
  /// DESCRIPTION:
  ///     当平台特定的设备ID获取方法失败时使用的备用方案，
  ///     生成UUID并存储在本地存储中，确保在任何情况下都能获取到设备ID
  ///
  /// RETURNS:
  ///     Future<String> - 备用设备ID（存储的UUID）
  Future<String> _getFallbackDeviceId() async {
    final prefs = await SharedPreferences.getInstance();
    String? deviceId = prefs.getString('device_id');

    if (deviceId?.isEmpty ?? true) {
      deviceId = const Uuid().v4();
      await prefs.setString('device_id', deviceId);
    }

    return deviceId!;
  }
}
