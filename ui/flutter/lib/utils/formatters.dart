// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      formatters.dart
//  *
//  * DESCRIPTION :    数据格式化工具类，提供各种数据类型的格式化功能，
//  *                  包括时间、流量、速率、日期等格式化方法
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  ******************************************************************************/

import 'dart:math' as math;
import 'package:intl/intl.dart';

/// 数据格式化工具类
///
/// PURPOSE:
///     提供应用程序中各种数据类型的格式化功能，确保数据显示的一致性和可读性
///
/// FEATURES:
///     - 时间和持续时间格式化
///     - 字节和流量数据格式化
///     - 网络速率格式化
///     - 日期时间格式化
///     - 网络延迟格式化
///     - 支持自定义精度和本地化
///
/// USAGE:
///     // 格式化持续时间
///     final duration = Formatters.formatconst Duration(3661); // "01:01:01"
///
///     // 格式化文件大小
///     final size = Formatters.formatBytes(1048576); // "1.00 MB"
///
///     // 格式化网络速率
///     final speed = Formatters.formatBytesPerSecond(1000); // "8.00 kbps"
class Formatters {
  // ==========================================================================
  // 时间和持续时间格式化
  // ==========================================================================

  /// 格式化连接持续时间
  ///
  /// DESCRIPTION:
  ///     将秒数转换为HH:MM:SS格式的时间字符串，
  ///     适用于显示连接时长、会话时间等
  ///
  /// PARAMETERS:
  ///     seconds - 总秒数
  ///
  /// RETURNS:
  ///     String - 格式化后的时间字符串（HH:MM:SS）
  ///
  /// EXAMPLE:
  ///     formatDuration(3661) -> "01:01:01"
  ///     formatDuration(125) -> "00:02:05"
  static String formatDuration(int seconds) {
    final duration = Duration(seconds: seconds);
    final hours = duration.inHours.toString().padLeft(2, '0');
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final secs = duration.inSeconds.remainder(60).toString().padLeft(2, '0');
    return '$hours:$minutes:$secs';
  }

  // ==========================================================================
  // 数据大小和流量格式化
  // ==========================================================================

  /// 格式化字节数据大小
  ///
  /// DESCRIPTION:
  ///     将字节数转换为人类可读的格式（B, KB, MB, GB等），
  ///     自动选择合适的单位并保留指定的小数位数
  ///
  /// PARAMETERS:
  ///     bytes - 字节数
  ///     decimals - 小数位数，默认为2位
  ///
  /// RETURNS:
  ///     String - 格式化后的大小字符串
  ///
  /// EXAMPLE:
  ///     formatBytes(1024) -> "1.00 KB"
  ///     formatBytes(1048576) -> "1.00 MB"
  ///     formatBytes(0) -> "0 B"
  static String formatBytes(int bytes, {int decimals = 2}) {
    if (bytes <= 0) return '0 B';
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    var i = (_safeLog(bytes) / _safeLog(1024)).floor();
    i = math.min(i, suffixes.length - 1); // 防止数组越界
    return '${(bytes / _safePow(1024, i)).toStringAsFixed(decimals)} ${suffixes[i]}';
  }

  /// 格式化网络传输速率
  ///
  /// DESCRIPTION:
  ///     将每秒字节数转换为人类可读的速率格式（bps, kbps, Mbps等），
  ///     适用于显示网络传输速度、下载速度等，使用1000作为进制基数
  ///
  /// PARAMETERS:
  ///     bytesPerSecond - 每秒字节数
  ///     decimals - 小数位数，默认为2位
  ///
  /// RETURNS:
  ///     String - 格式化后的速率字符串
  ///
  /// EXAMPLE:
  ///     formatBytesPerSecond(1000) -> "8.00 kbps"
  ///     formatBytesPerSecond(1000000) -> "8.00 Mbps"
  ///     formatBytesPerSecond(0) -> "0 bps"
  static String formatBytesPerSecond(int bytesPerSecond, {int decimals = 2}) {
    if (bytesPerSecond <= 0) return '0 bps';

    // 将字节转换为比特（1字节 = 8比特）
    final bitsPerSecond = bytesPerSecond * 8;

    const suffixes = ['bps', 'kbps', 'Mbps', 'Gbps', 'Tbps'];
    var i = (_safeLog(bitsPerSecond) / _safeLog(1000)).floor();
    i = math.min(i, suffixes.length - 1); // 防止数组越界
    return '${(bitsPerSecond / _safePow(1000, i)).toStringAsFixed(decimals)} ${suffixes[i]}';
  }

  // ==========================================================================
  // 日期时间格式化
  // ==========================================================================

  /// 格式化完整日期时间
  ///
  /// DESCRIPTION:
  ///     将DateTime对象格式化为标准的日期时间字符串，
  ///     格式为"yyyy-MM-dd HH:mm:ss"
  ///
  /// PARAMETERS:
  ///     dateTime - 要格式化的DateTime对象
  ///
  /// RETURNS:
  ///     String - 格式化后的日期时间字符串
  ///
  /// EXAMPLE:
  ///     formatDateTime(DateTime(2025, 6, 23, 14, 30, 45)) -> "2025-06-23 14:30:45"
  static String formatDateTime(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
  }

  /// 格式化时间（仅时分秒）
  ///
  /// DESCRIPTION:
  ///     将DateTime对象格式化为时间字符串，
  ///     格式为"HH:mm:ss"，仅显示时分秒
  ///
  /// PARAMETERS:
  ///     dateTime - 要格式化的DateTime对象
  ///
  /// RETURNS:
  ///     String - 格式化后的时间字符串
  ///
  /// EXAMPLE:
  ///     formatTime(DateTime(2025, 6, 23, 14, 30, 45)) -> "14:30:45"
  static String formatTime(DateTime dateTime) {
    return DateFormat('HH:mm:ss').format(dateTime);
  }

  // ==========================================================================
  // 网络性能格式化
  // ==========================================================================

  /// 格式化网络延迟
  ///
  /// DESCRIPTION:
  ///     将毫秒数格式化为延迟显示字符串，
  ///     负数或无效值显示为"未知"
  ///
  /// PARAMETERS:
  ///     ping - 延迟毫秒数
  ///
  /// RETURNS:
  ///     String - 格式化后的延迟字符串
  ///
  /// EXAMPLE:
  ///     formatPing(25) -> "25 ms"
  ///     formatPing(-1) -> "未知"
  static String formatPing(int ping) {
    if (ping < 0) return 'Unknown'; // TODO: Add to localization
    return '$ping ms';
  }

  // ==========================================================================
  // 私有辅助方法
  // ==========================================================================

  /// 安全的对数计算
  ///
  /// DESCRIPTION:
  ///     计算对数值，处理边界情况，避免数学错误和NaN
  ///
  /// PARAMETERS:
  ///     x - 要计算对数的数值
  ///     base - 对数底数，默认为自然对数
  ///
  /// RETURNS:
  ///     double - 对数值，无效输入返回0
  static double _safeLog(num x, [num? base]) {
    if (x.toDouble() <= 0) return 0;
    if (base == null) {
      final result = math.log(x);
      return result.isNaN || result.isInfinite ? 0 : result;
    } else {
      if (base.toDouble() <= 0 || base == 1) return 0;
      final logX = math.log(x);
      final logBase = math.log(base);
      if (logX.isNaN || logX.isInfinite || logBase.isNaN || logBase.isInfinite || logBase == 0) {
        return 0;
      }
      final result = logX / logBase;
      return result.isNaN || result.isInfinite ? 0 : result;
    }
  }

  /// 安全的幂运算
  ///
  /// DESCRIPTION:
  ///     计算幂值，确保返回有效的double类型结果
  ///
  /// PARAMETERS:
  ///     x - 底数
  ///     exponent - 指数
  ///
  /// RETURNS:
  ///     double - 幂运算结果
  static double _safePow(num x, num exponent) {
    return math.pow(x, exponent).toDouble();
  }
}

// =============================================================================
// 全局辅助函数（向后兼容）
// =============================================================================

/// 全局对数函数（已弃用，建议使用Formatters._safeLog）
///
/// @deprecated 使用 Formatters._safeLog 替代
double log(num x, [num? base]) {
  if (base == null) {
    if (x.toDouble() <= 0) return 0;
    final result = math.log(x);
    return result.isNaN || result.isInfinite ? 0 : result;
  } else {
    if (x.toDouble() <= 0 || base.toDouble() <= 0 || base == 1) return 0;
    final logX = math.log(x);
    final logBase = math.log(base);
    if (logX.isNaN || logX.isInfinite || logBase.isNaN || logBase.isInfinite || logBase == 0) {
      return 0;
    }
    final result = logX / logBase;
    return result.isNaN || result.isInfinite ? 0 : result;
  }
}

/// 全局幂函数（已弃用，建议使用Formatters._safePow）
///
/// @deprecated 使用 Formatters._safePow 替代
double pow(num x, num exponent) {
  return math.pow(x, exponent).toDouble();
}
