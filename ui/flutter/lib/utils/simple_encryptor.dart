// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      simple_encryptor.dart
//  *
//  * DESCRIPTION :    简单的凭据加密工具类，使用基本的加密方法避免复杂依赖，
//  *                  提供用户凭据的加密和解密功能
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  ******************************************************************************/

import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';

/// SimpleEncryptor
///
/// PURPOSE:
///     简单的凭据加密工具类，使用基本的加密方法避免复杂的依赖
///
/// FEATURES:
///     - 单例模式：确保全局唯一的加密器实例
///     - XOR加密：使用XOR对称加密算法
///     - 密钥派生：基于固定设备ID和应用密钥生成加密密钥
///     - Base64编码：加密后的数据使用Base64编码存储
///     - 密钥缓存：缓存生成的密钥提高性能
///     - 前后端一致：确保与后端加密解密算法一致
///
/// USAGE:
///     使用单例模式获取实例，调用encrypt()和decrypt()方法处理凭据
class SimpleEncryptor {
  /// 应用程序密钥，与后端保持一致
  static const String _appKey = "VPNClientAppKey-2025";

  /// 单例实例
  static final SimpleEncryptor _instance = SimpleEncryptor._internal();

  /// 工厂构造函数，返回单例实例
  factory SimpleEncryptor() => _instance;

  /// 私有构造函数，用于单例模式
  SimpleEncryptor._internal();

  /// 缓存的加密密钥，避免重复生成
  Uint8List? _cachedKey;

  /// encrypt
  ///
  /// DESCRIPTION:
  ///     加密用户凭据，使用简单的加密方法
  ///
  /// ALGORITHM:
  ///     1. 使用固定的设备ID
  ///     2. 派生加密密钥
  ///     3. 使用XOR加密数据
  ///     4. 编码为Base64
  ///
  /// PARAMETERS:
  ///     plaintext - 要加密的明文字符串
  ///
  /// RETURNS:
  ///     Future<String> - 加密后的Base64编码字符串
  ///
  /// THROWS:
  ///     Exception - 加密失败时返回原文（生产环境应抛出异常）
  Future<String> encrypt(String plaintext) async {
    try {
      // 获取或生成密钥
      final key = _getKey();

      // 将明文转换为字节数组
      final plaintextBytes = utf8.encode(plaintext);

      // 使用XOR加密
      final encryptedBytes = _xorEncrypt(plaintextBytes, key);

      // 编码为Base64
      return base64.encode(encryptedBytes);
    } catch (e) {
      // 如果加密失败，返回原文（在生产环境中应该抛出异常）
      return plaintext;
    }
  }

  /// decrypt
  ///
  /// DESCRIPTION:
  ///     解密用户凭据，使用简单的解密方法
  ///
  /// ALGORITHM:
  ///     1. 解码Base64
  ///     2. 使用固定的设备ID
  ///     3. 派生加密密钥
  ///     4. 使用XOR解密数据
  ///
  /// PARAMETERS:
  ///     encoded - 要解密的Base64编码字符串
  ///
  /// RETURNS:
  ///     Future<String> - 解密后的明文字符串
  ///
  /// THROWS:
  ///     Exception - 解密失败时返回空字符串
  Future<String> decrypt(String encoded) async {
    try {
      // 解码Base64
      final encryptedBytes = base64.decode(encoded);

      // 获取或生成密钥
      final key = _getKey();

      // 使用XOR解密（XOR是对称的，加密和解密使用相同的算法）
      final decryptedBytes = _xorEncrypt(encryptedBytes, key);

      // 转换为字符串
      return utf8.decode(decryptedBytes);
    } catch (e) {
      // debugPrint('解密失败: $e');
      // 如果解密失败，返回空字符串
      return '';
    }
  }

  /// _xorEncrypt
  ///
  /// DESCRIPTION:
  ///     XOR加密/解密，XOR是对称的，所以加密和解密使用相同的算法
  ///
  /// PARAMETERS:
  ///     data - 要加密/解密的数据字节数组
  ///     key - 加密密钥字节数组
  ///
  /// RETURNS:
  ///     Uint8List - 加密/解密后的字节数组
  Uint8List _xorEncrypt(List<int> data, List<int> key) {
    final result = Uint8List(data.length);

    for (int i = 0; i < data.length; i++) {
      result[i] = data[i] ^ key[i % key.length];
    }

    return result;
  }

  /// _getKey
  ///
  /// DESCRIPTION:
  ///     获取加密密钥，使用固定的设备ID确保前后端一致
  ///
  /// ALGORITHM:
  ///     1. 检查缓存的密钥
  ///     2. 使用固定设备ID + 应用密钥创建盐值
  ///     3. 使用SHA-256生成密钥
  ///     4. 缓存密钥以提高性能
  ///
  /// RETURNS:
  ///     Uint8List - 生成的加密密钥字节数组
  ///
  /// NOTE:
  ///     使用固定设备ID不是最安全的方法，但可确保前后端加密/解密一致
  Uint8List _getKey() {
    // 如果已经缓存了密钥，直接返回
    if (_cachedKey != null) {
      return _cachedKey!;
    }

    // 使用固定的设备ID，确保前后端一致
    // 这不是最安全的方法，但可以确保前后端加密/解密一致
    const deviceId = "fixed-device-id-for-encryption";

    // 创建盐值（设备ID + 应用程序密钥）
    final salt = utf8.encode(deviceId + _appKey);

    // 创建输入数据（与后端保持一致）
    final input = utf8.encode(deviceId) + salt;

    // 使用SHA-256生成密钥
    final digest = sha256.convert(input);
    final key = Uint8List.fromList(digest.bytes);

    // 缓存密钥
    _cachedKey = key;

    return key;
  }
}
