// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      api_exception.dart
//  *
//  * DESCRIPTION :    API异常处理类，提供统一的异常处理机制，
//  *                  支持多语言错误消息和错误类型分类
//  *                  
//  *                  UPDATED: 24/07/2025 - 支持与Windows版本一致的错误代码
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  *                  24/07/2025 update - 添加Windows兼容错误代码
//  ******************************************************************************/

import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';

/// API异常处理类
///
/// PURPOSE:
///     提供统一的API异常处理机制，支持错误分类、本地化消息显示，
///     以及用户友好的错误提示，提升应用程序的错误处理体验
///
/// FEATURES:
///     - 统一的异常格式和错误码管理
///     - 支持多语言本地化错误消息
///     - 错误类型自动分类（API、网络、认证等）
///     - 用户友好的错误消息提取和显示
///     - 详细的错误信息和附加信息处理
///     - 与Windows版本一致的错误代码体系 (1000-6999)
///
/// USAGE:
///     // 创建API异常
///     final exception = ApiException('Invalid credentials', 2000, 'auth_error');
///
///     // 获取本地化错误消息
///     final message = exception.getUserFriendlyMessage(context);
///
///     // 检查错误类型
///     if (exception.isAuthError()) {
///       // 处理认证错误
///     }
class ApiException implements Exception {
  /// 错误消息
  final String message;

  /// 错误码
  final int code;

  /// 错误类型
  final String type;

  /// 构造函数
  ///
  /// DESCRIPTION:
  ///     创建API异常实例
  ///
  /// PARAMETERS:
  ///     message - 错误消息
  ///     code - 错误码
  ///     type - 错误类型
  ApiException(this.message, this.code, this.type);

  /// 转换为字符串表示
  ///
  /// DESCRIPTION:
  ///     返回异常的字符串表示，包含错误消息、错误码和类型
  ///
  /// RETURNS:
  ///     String - 异常的字符串表示
  @override
  String toString() {
    return 'ApiException: $message (Code: $code, Type: $type)';
  }

  // ==========================================================================
  // 用户友好的错误消息处理
  // ==========================================================================

  /// 获取用户友好的错误消息
  ///
  /// DESCRIPTION:
  ///     根据错误码和上下文返回本地化的用户友好错误消息，
  ///     自动提取原始消息中的有用信息并进行本地化处理
  ///
  /// PARAMETERS:
  ///     context - 构建上下文，用于获取本地化资源（可选）
  ///
  /// RETURNS:
  ///     String - 本地化的用户友好错误消息
  ///
  /// EXAMPLE:
  ///     final message = exception.getUserFriendlyMessage(context);
  String getUserFriendlyMessage([BuildContext? context]) {
    final l10n = context != null ? AppLocalizations.of(context) : null;

    // 对于特定的错误码，提供本地化消息，但保留原始消息的有用信息
    String baseMessage = _getBaseLocalizedMessage(l10n);

    // 尝试提取并本地化原始消息中的有用信息
    String? additionalInfo = _extractAdditionalInfo(l10n);
    if (additionalInfo != null) {
      return '$baseMessage ($additionalInfo)';
    }

    return baseMessage;
  }

  // 获取基础的本地化消息
  String _getBaseLocalizedMessage(AppLocalizations? l10n) {
    switch (code) {
      // General errors (0-999)
      case 0:
        return l10n?.unknownError ?? 'Unknown error occurred';
      case 1:
        return 'Internal system error';
      case 2:
        return 'Invalid parameter provided';
      case 5:
        return l10n?.permissionDenied ?? 'Permission denied';
      case 6:
        return l10n?.authenticationFailed ?? 'Authentication required';
      case 12:
        return 'Service temporarily unavailable';

      // Network errors (1000-1999) - 与Windows版本保持一致
      case 1000:
        return l10n?.networkUnreachable ?? 'Network unreachable, please check your internet connection';
      case 1001:
        return l10n?.operationTimeout ?? 'Network operation timed out, please try again';
      case 1002:
        return l10n?.networkDnsFailure ?? 'DNS resolution failed, please check server address';
      case 1003:
        return l10n?.networkConnectionReset ?? 'Network connection was reset';
      case 1004:
        return l10n?.networkConnectionClosed ?? 'Network connection was closed';
      case 1005:
        return l10n?.networkProxyError ?? 'Proxy server error';
      case 1006:
        return l10n?.networkTlsError ?? 'TLS/SSL connection error';

      // Domain lookup errors (1100-1199) - 域名查找专用错误码
      case 1101:
        return l10n?.domainNotFound ?? 'Domain not found, please check the domain name';
      case 1102:
        return l10n?.domainInvalid ?? 'Invalid domain format';
      case 1103:
        return l10n?.domainRequired ?? 'Domain parameter is required';
      case 1104:
        return l10n?.domainLookupTimeout ?? 'Domain lookup timeout, please try again later';
      case 1105:
        return l10n?.domainLookupNetworkError ?? 'Network error during domain lookup, please check your connection';

      // Server list errors (1200-1299) - 服务器列表专用错误码
      case 1201:
        return l10n?.serverListNotFound ?? 'Server list not found, please check the server URL';
      case 1202:
        return l10n?.serverListInvalid ?? 'Invalid server list format';
      case 1203:
        return l10n?.serverListTimeout ?? 'Server list request timeout, please try again later';
      case 1204:
        return l10n?.serverListNetworkError ?? 'Network error while fetching server list, please check your connection';

      // Authentication errors (2000-2999) - 与Windows版本保持一致
      case 2000:
        return l10n?.apiInvalidCredentials ?? 'Invalid username or password';
      case 2001:
        return l10n?.authenticationFailed ?? 'Your credentials have expired, please login again';
      case 2002:
        return l10n?.apiRateLimit ?? 'Too many authentication attempts, please wait and try again';
      case 2003:
        return l10n?.authenticationFailed ?? 'Account has been locked, please contact administrator';
      case 2004:
        return l10n?.authenticationFailed ?? 'Invalid authentication token';
      case 2005:
        return l10n?.authenticationFailed ?? 'Authentication token has expired, please login again';
      case 2006:
        return l10n?.authMissingCredentials ?? 'Authentication credentials are missing';

      // Tunnel errors (3000-3999)
      case 3000:
        return l10n?.connectionFailedGeneric ?? 'Failed to initialize VPN tunnel';
      case 3001:
        return l10n?.connectionFailedGeneric ?? 'VPN tunnel closed unexpectedly';
      case 3003:
        return l10n?.connectionFailedGeneric ?? 'VPN device error occurred';

      // Configuration errors (4000-4999)
      case 4000:
        return l10n?.configInvalid ?? 'Invalid VPN configuration';
      case 4001:
        return l10n?.configInvalid ?? 'VPN configuration is missing';
      case 4002:
        return l10n?.permissionDenied ?? 'Permission denied to access configuration';

      // Platform errors (5000-5999)
      case 5001:
        return l10n?.permissionDenied ?? 'VPN permission denied, please grant VPN access in system settings';
      case 5000:
        return 'This platform is not supported';
      case 5003:
        return 'System error occurred';

      // Protocol errors (6000-6999)
      case 6003:
        return l10n?.connectionFailedGeneric ?? 'VPN protocol handshake failed';
      case 6004:
        return l10n?.connectionFailedGeneric ?? 'Protocol encryption error';
      case 6005:
        return l10n?.connectionFailedGeneric ?? 'Protocol decryption error';

      // Legacy iOS/macOS VPNService错误码 (1001-1015) - 保持向后兼容
      case 1014:
        return l10n?.authenticationFailed ?? 'Authentication failed';
      case 1015:
        return l10n?.networkConnectionClosed ?? 'Platform channel error';

      // API错误 (7000-7999) - 保持向后兼容
      case 7000:
        return l10n?.apiInvalidRequest ?? 'Invalid request format or parameters';
      case 7001:
        return l10n?.apiInvalidCredentials ?? 'Invalid username or password';
      case 7002:
        return l10n?.apiServerError ?? 'Server internal error';
      case 7003:
        return l10n?.apiResourceNotFound ?? 'Requested resource not found';
      case 7004:
        return l10n?.apiUnauthorized ?? 'Unauthorized access, please login again';
      case 7005:
        return l10n?.apiForbidden ?? 'Access to this resource is forbidden';
      case 7006:
        return l10n?.apiTimeout ?? 'Request timeout, please try again later';
      case 7007:
        return l10n?.apiConflict ?? 'Resource conflict';
      case 7008:
        return l10n?.apiRateLimit ?? 'Too many requests, please try again later';
      case 7009:
        return l10n?.apiGatewayError ?? 'Gateway error';
      case 7010:
        return l10n?.apiServiceUnavailable ?? 'Service temporarily unavailable, please try again later';
      case 7011:
        return l10n?.apiTimeout ?? 'Gateway timeout, please try again later';
      case 7012:
        return l10n?.noAutoServersAvailable ?? 'No servers available, please check network connection or contact administrator';
      case 7013:
        return l10n?.authenticationFailed ?? 'Authentication failed, please login again';
      case 7014:
        return l10n?.connectionFailedGeneric ?? 'Connection failed';
      case 7015:
        return l10n?.connectionFailedGeneric ?? 'Disconnection failed';

      default:
        // 对于未知错误码，尝试根据消息内容推断错误类型
        if (message.toLowerCase().contains('network') || 
            message.toLowerCase().contains('connection') ||
            message.toLowerCase().contains('timeout')) {
          return l10n?.networkUnreachable ?? 'Network connection error, please check your internet connection';
        } else if (message.toLowerCase().contains('auth') || 
                   message.toLowerCase().contains('login') ||
                   message.toLowerCase().contains('credential')) {
          return l10n?.authenticationFailed ?? 'Authentication failed, please check your credentials';
        } else if (message.toLowerCase().contains('permission') ||
                   message.toLowerCase().contains('denied')) {
          return l10n?.permissionDenied ?? 'Permission denied, please check your access rights';
        } else {
          return l10n?.unknownError ?? 'An unexpected error occurred, please try again';
        }
    }
  }

  // 提取附加信息
  String? _extractAdditionalInfo(AppLocalizations? l10n) {
    final isEnglish = l10n?.localeName == 'en';

    // 对于域名查找错误，提取域名信息
    if (code >= 1101 && code <= 1105) {
      // 尝试从消息中提取域名信息
      final domainMatch = RegExp(r'domain not found: (.+)').firstMatch(message);
      if (domainMatch != null) {
        final domain = domainMatch.group(1);
        return isEnglish ? 'domain: $domain' : '域名: $domain';
      }

      // 尝试从消息中提取详细信息
      if (message.contains(':')) {
        final parts = message.split(':');
        if (parts.length > 1) {
          return parts.last.trim();
        }
      }
    }

    // 对于服务器列表错误，提取状态码信息
    if (code >= 1201 && code <= 1204) {
      // 尝试从消息中提取HTTP状态码
      final statusCodeMatch = RegExp(r'status code (\d+)').firstMatch(message);
      if (statusCodeMatch != null) {
        final statusCode = statusCodeMatch.group(1);
        return isEnglish ? 'HTTP $statusCode' : 'HTTP状态码 $statusCode';
      }

      // 尝试从消息中提取URL信息
      final urlMatch = RegExp(r'https?://[^\s]+').firstMatch(message);
      if (urlMatch != null) {
        final url = urlMatch.group(0);
        return isEnglish ? 'URL: $url' : '地址: $url';
      }
    }

    // 提取服务器地址信息
    final serverMatch = RegExp(r'server[:\s]+([^\s,]+)', caseSensitive: false).firstMatch(message);
    if (serverMatch != null) {
      return isEnglish ? 'server: ${serverMatch.group(1)}' : '服务器: ${serverMatch.group(1)}';
    }

    // 提取端口信息
    final portMatch = RegExp(r'port[:\s]+(\d+)', caseSensitive: false).firstMatch(message);
    if (portMatch != null) {
      return isEnglish ? 'port: ${portMatch.group(1)}' : '端口: ${portMatch.group(1)}';
    }

    // 提取超时信息
    if (message.contains('timeout')) {
      return isEnglish ? 'timeout' : '超时';
    }

    // 对于包含认证失败的连接错误，提取认证失败信息
    if (code == 7014 && message.contains('authentication failed')) {
      return isEnglish ? 'authentication failed' : '认证失败';
    }

    return null;
  }

  // ==========================================================================
  // 错误类型判断方法
  // ==========================================================================

  /// 是否为网络错误
  bool isNetworkError() {
    return (code >= 1000 && code <= 1999) || 
           code == 7006 || code == 7009 || code == 7011 ||
           type.contains('network') || type.contains('timeout');
  }

  /// 是否为认证错误
  bool isAuthError() {
    return (code >= 2000 && code <= 2999) ||
           code == 7001 || code == 7004 || code == 7013 || code == 1014 ||
           type.contains('auth') || type.contains('credential');
  }

  /// 是否为配置错误
  bool isConfigError() {
    return (code >= 4000 && code <= 4999) ||
           code == 7000 || code == 7003 || code == 7005 || code == 7007 ||
           type.contains('config') || type.contains('invalid');
  }

  /// 是否为平台错误
  bool isPlatformError() {
    return (code >= 5000 && code <= 5999) ||
           code == 1015 ||
           type.contains('platform') || type.contains('permission');
  }

  /// 是否为隧道错误
  bool isTunnelError() {
    return (code >= 3000 && code <= 3999) ||
           type.contains('tunnel') || type.contains('vpn');
  }

  /// 是否为协议错误
  bool isProtocolError() {
    return (code >= 6000 && code <= 6999) ||
           type.contains('protocol');
  }

  /// 是否为服务器错误
  bool isServerError() {
    return code == 7002 || code == 7010 ||
           type.contains('server') || type.contains('service');
  }

  /// 是否为临时错误（可重试）
  bool isTemporaryError() {
    return isNetworkError() || 
           code == 7008 || code == 7010 || code == 7011 || // 限流、服务不可用、网关超时
           code == 2002; // 认证限流
  }

  /// 是否需要用户干预
  bool requiresUserAction() {
    return isAuthError() || isPlatformError() || isConfigError();
  }
}
