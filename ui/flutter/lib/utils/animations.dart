// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      animations.dart
//  *
//  * DESCRIPTION :    动画工具类，提供各种动画效果和组件，包括淡入淡出、
//  *                  缩放、滑动、波纹等动画效果
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  ******************************************************************************/

import 'package:flutter/material.dart';

import 'design_system.dart';
import 'constants.dart';

/// AnimationUtils
///
/// PURPOSE:
///     动画工具类，提供各种动画效果和组件
///
/// FEATURES:
///     - 基础动画：淡入淡出、缩放、滑动动画
///     - 组合动画：多种动画效果的组合
///     - 脉冲动画：创建脉冲效果的动画控制器和动画
///     - 波纹动画：创建波纹扩散效果
///     - 连接状态动画：根据连接状态显示不同的动画效果
///
/// USAGE:
///     使用静态方法创建各种动画效果，或使用提供的动画组件
class AnimationUtils {
  /// fadeTransition
  ///
  /// DESCRIPTION:
  ///     创建淡入淡出动画组件
  ///
  /// PARAMETERS:
  ///     child - 要应用动画的子组件
  ///     animation - 透明度动画控制器
  ///
  /// RETURNS:
  ///     Widget - 带有淡入淡出效果的组件
  static Widget fadeTransition({
    required Widget child,
    required Animation<double> animation,
  }) {
    return FadeTransition(
      opacity: animation,
      child: child,
    );
  }

  /// scaleTransition
  ///
  /// DESCRIPTION:
  ///     创建缩放动画组件
  ///
  /// PARAMETERS:
  ///     child - 要应用动画的子组件
  ///     animation - 缩放动画控制器
  ///
  /// RETURNS:
  ///     Widget - 带有缩放效果的组件
  static Widget scaleTransition({
    required Widget child,
    required Animation<double> animation,
  }) {
    return ScaleTransition(
      scale: animation,
      child: child,
    );
  }

  /// slideTransition
  ///
  /// DESCRIPTION:
  ///     创建滑动动画组件
  ///
  /// PARAMETERS:
  ///     child - 要应用动画的子组件
  ///     animation - 位置偏移动画控制器
  ///
  /// RETURNS:
  ///     Widget - 带有滑动效果的组件
  static Widget slideTransition({
    required Widget child,
    required Animation<Offset> animation,
  }) {
    return SlideTransition(
      position: animation,
      child: child,
    );
  }

  /// fadeScaleTransition
  ///
  /// DESCRIPTION:
  ///     创建组合动画（淡入+缩放）
  ///
  /// PARAMETERS:
  ///     child - 要应用动画的子组件
  ///     animation - 同时控制透明度和缩放的动画控制器
  ///
  /// RETURNS:
  ///     Widget - 带有淡入淡出和缩放效果的组件
  static Widget fadeScaleTransition({
    required Widget child,
    required Animation<double> animation,
  }) {
    return FadeTransition(
      opacity: animation,
      child: ScaleTransition(
        scale: animation,
        child: child,
      ),
    );
  }

  /// fadeSlideTransition
  ///
  /// DESCRIPTION:
  ///     创建组合动画（淡入+滑动）
  ///
  /// PARAMETERS:
  ///     child - 要应用动画的子组件
  ///     fadeAnimation - 透明度动画控制器
  ///     slideAnimation - 滑动位置动画控制器
  ///
  /// RETURNS:
  ///     Widget - 带有淡入淡出和滑动效果的组件
  static Widget fadeSlideTransition({
    required Widget child,
    required Animation<double> fadeAnimation,
    required Animation<Offset> slideAnimation,
  }) {
    return FadeTransition(
      opacity: fadeAnimation,
      child: SlideTransition(
        position: slideAnimation,
        child: child,
      ),
    );
  }

  /// createPulseController
  ///
  /// DESCRIPTION:
  ///     创建脉冲动画控制器，自动重复播放
  ///
  /// PARAMETERS:
  ///     vsync - 动画同步提供者
  ///
  /// RETURNS:
  ///     AnimationController - 脉冲动画控制器
  static AnimationController createPulseController(TickerProvider vsync) {
    return AnimationController(
      vsync: vsync,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);
  }

  /// createPulseAnimation
  ///
  /// DESCRIPTION:
  ///     创建脉冲动画，在0.95到1.05之间缩放
  ///
  /// PARAMETERS:
  ///     controller - 动画控制器
  ///
  /// RETURNS:
  ///     Animation<double> - 脉冲缩放动画
  static Animation<double> createPulseAnimation(AnimationController controller) {
    return Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ),
    );
  }

  /// createRippleController
  ///
  /// DESCRIPTION:
  ///     创建波纹动画控制器，持续重复播放
  ///
  /// PARAMETERS:
  ///     vsync - 动画同步提供者
  ///
  /// RETURNS:
  ///     AnimationController - 波纹动画控制器
  static AnimationController createRippleController(TickerProvider vsync) {
    return AnimationController(
      vsync: vsync,
      duration: const Duration(milliseconds: 2000),
    )..repeat();
  }

  /// createRippleAnimation
  ///
  /// DESCRIPTION:
  ///     创建波纹扩散动画，从0到1的缓出动画
  ///
  /// PARAMETERS:
  ///     controller - 动画控制器
  ///
  /// RETURNS:
  ///     Animation<double> - 波纹扩散动画
  static Animation<double> createRippleAnimation(AnimationController controller) {
    return Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: controller,
        curve: Curves.easeOut,
      ),
    );
  }
}

/// RippleAnimation
///
/// PURPOSE:
///     波纹效果组件，创建多层波纹扩散动画效果
///
/// FEATURES:
///     - 多层波纹：支持配置波纹层数
///     - 自定义颜色：可设置波纹颜色
///     - 动画时长：可配置动画持续时间
///     - 中心内容：支持在波纹中心显示子组件
///
/// USAGE:
///     包装需要波纹效果的组件，自动播放波纹动画
class RippleAnimation extends StatefulWidget {
  /// 波纹颜色
  final Color color;

  /// 中心显示的子组件
  final Widget child;

  /// 波纹层数
  final int numberOfRipples;

  /// 动画持续时间
  final Duration duration;

  /// RippleAnimation构造函数
  ///
  /// DESCRIPTION:
  ///     创建波纹动画组件
  ///
  /// PARAMETERS:
  ///     child - 中心显示的子组件
  ///     color - 波纹颜色，默认为蓝色
  ///     numberOfRipples - 波纹层数，默认为3层
  ///     duration - 动画持续时间，默认为2秒
  const RippleAnimation({
    Key? key,
    required this.child,
    this.color = Colors.blue,
    this.numberOfRipples = 3,
    this.duration = const Duration(milliseconds: 2000),
  }) : super(key: key);

  @override
  State<RippleAnimation> createState() => _RippleAnimationState();
}

class _RippleAnimationState extends State<RippleAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Stack(
        alignment: Alignment.center,
        children: [
          ...List.generate(
            widget.numberOfRipples,
            (index) => AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                final double delay = index / widget.numberOfRipples;
                final Animation<double> animation = Tween<double>(begin: 0.0, end: 1.0).animate(
                  CurvedAnimation(
                    parent: _controller,
                    curve: Interval(
                      delay,
                      1.0,
                      curve: Curves.easeOut,
                    ),
                  ),
                );
                return Opacity(
                  opacity: (1.0 - animation.value).clamp(0.0, 1.0),
                  child: Transform.scale(
                    scale: animation.value * 2.0 + 1.0,
                    child: Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: widget.color.withValues(alpha: 0.3 * (1 - animation.value)),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          widget.child,
        ],
      ),
    );
  }
}

/// ConnectionStatusAnimation
///
/// PURPOSE:
///     连接状态动画组件，根据连接状态显示不同的动画效果
///
/// FEATURES:
///     - 状态感知：根据连接状态自动切换动画
///     - 动态颜色：根据状态显示对应颜色
///     - 可配置大小：支持自定义组件大小
///     - 波纹效果：连接中和已连接状态显示波纹动画
///
/// USAGE:
///     传入连接状态，自动显示对应的动画效果
class ConnectionStatusAnimation extends StatefulWidget {
  /// 连接状态
  final ConnectionStatus status;

  /// 组件大小
  final double size;

  /// 自定义颜色，如果不设置则使用状态默认颜色
  final Color? color;

  /// ConnectionStatusAnimation构造函数
  ///
  /// DESCRIPTION:
  ///     创建连接状态动画组件
  ///
  /// PARAMETERS:
  ///     status - 连接状态
  ///     size - 组件大小，默认为100.0
  ///     color - 自定义颜色，不设置则使用状态默认颜色
  const ConnectionStatusAnimation({
    Key? key,
    required this.status,
    this.size = 100.0,
    this.color,
  }) : super(key: key);

  @override
  State<ConnectionStatusAnimation> createState() => _ConnectionStatusAnimationState();
}

class _ConnectionStatusAnimationState extends State<ConnectionStatusAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;


  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );



    _updateControllerState();
  }

  @override
  void didUpdateWidget(ConnectionStatusAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.status != widget.status) {
      _updateControllerState();
    }
  }

  void _updateControllerState() {
    if (widget.status == ConnectionStatus.connecting ||
        widget.status == ConnectionStatus.disconnecting) {
      _controller.repeat(reverse: true);
    } else {
      _controller.stop();
      _controller.value = 0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Color _getStatusColor() {
    if (widget.color != null) return widget.color!;
    
    switch (widget.status) {
      case ConnectionStatus.connected:
        return AppColors.connected;
      case ConnectionStatus.connecting:
        return AppColors.connecting;
      case ConnectionStatus.disconnecting:
        return AppColors.connecting;
      case ConnectionStatus.disconnected:
        return AppColors.disconnected;
      case ConnectionStatus.error:
        return AppColors.error;
    }
  }

  @override
  Widget build(BuildContext context) {
    final color = _getStatusColor();
    
    if (widget.status == ConnectionStatus.connecting ||
        widget.status == ConnectionStatus.disconnecting) {
      return AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return RippleAnimation(
            color: color,
            child: _buildStatusIcon(color),
          );
        },
      );
    } else if (widget.status == ConnectionStatus.connected) {
      return RippleAnimation(
        color: color,
        numberOfRipples: 2,
        child: _buildStatusIcon(color),
      );
    } else {
      return _buildStatusIcon(color);
    }
  }

  Widget _buildStatusIcon(Color color) {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color.withValues(alpha: 0.2),
      ),
      child: Center(
        child: Icon(
          _getStatusIcon(),
          color: color,
          size: widget.size * 0.5,
        ),
      ),
    );
  }

  IconData _getStatusIcon() {
    switch (widget.status) {
      case ConnectionStatus.disconnected:
        return Icons.cloud_off;
      case ConnectionStatus.connecting:
        return Icons.cloud_sync;
      case ConnectionStatus.connected:
        return Icons.cloud_done;
      case ConnectionStatus.disconnecting:
        return Icons.cloud_sync;
      case ConnectionStatus.error:
        return Icons.error;
    }
  }
}
