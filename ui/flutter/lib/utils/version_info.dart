// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      version_info.dart
//  *
//  * DESCRIPTION :    应用程序版本信息管理工具类，提供版本号获取和管理功能，
//  *                  支持基础版本号和包含构建信息的完整版本号
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  ******************************************************************************/

import 'dart:io';

import 'package:path/path.dart' as path;
import 'constants.dart';

/// 应用程序版本信息管理类
///
/// PURPOSE:
///     提供应用程序版本信息的获取和管理功能，支持基础版本号
///     和包含Git提交信息、构建时间的完整版本号
///
/// FEATURES:
///     - 基础版本号管理（来自常量定义）
///     - 完整版本号获取（包含构建信息）
///     - 版本文件读取和缓存机制
///     - 错误处理和回退机制
///     - 简短版本号获取
///
/// USAGE:
///     import 'package:panabit_client/utils/version_info.dart';
///
///     // 获取完整版本号
///     final fullVersion = await VersionInfo.getFullVersion();
///
///     // 获取简短版本号
///     final shortVersion = VersionInfo.getShortVersion();
class VersionInfo {
  /// 基础版本号
  ///
  /// DESCRIPTION:
  ///     应用程序的基础版本号，来自常量定义
  static const String baseVersion = kAppVersion;

  /// 缓存的完整版本号
  ///
  /// DESCRIPTION:
  ///     缓存的完整版本号，包含Git提交信息和构建时间
  static String? _fullVersion;

  /// 获取完整版本号
  ///
  /// DESCRIPTION:
  ///     获取包含构建信息的完整版本号，优先从version.txt文件读取，
  ///     如果文件不存在或读取失败则返回基础版本号
  ///
  /// RETURNS:
  ///     Future<String> - 完整版本号字符串
  ///
  /// EXAMPLE:
  ///     final version = await VersionInfo.getFullVersion();
  ///     // 可能返回: "1.0.2-abc123-20250623"
  static Future<String> getFullVersion() async {
    if (_fullVersion != null) {
      return _fullVersion!;
    }

    try {
      // 尝试从可执行文件目录中的版本文件读取
      final executableDir = path.dirname(Platform.resolvedExecutable);
      final versionFile = File(path.join(executableDir, 'version.txt'));

      if (await versionFile.exists()) {
        _fullVersion = await versionFile.readAsString();
        _fullVersion = _fullVersion!.trim();
        return _fullVersion!;
      }
    } catch (e) {
      // 生产环境不输出调试信息，静默处理错误
      // 版本文件读取失败不影响应用正常运行
    }

    // 如果无法读取版本文件，则返回基础版本号作为回退方案
    _fullVersion = baseVersion;
    return baseVersion;
  }

  /// 获取简短版本号
  ///
  /// DESCRIPTION:
  ///     获取基础版本号，不包含构建信息，适用于简单的版本显示
  ///
  /// RETURNS:
  ///     String - 基础版本号字符串
  ///
  /// EXAMPLE:
  ///     final version = VersionInfo.getShortVersion();
  ///     // 返回: "1.0.2"
  static String getShortVersion() {
    return baseVersion;
  }
}
