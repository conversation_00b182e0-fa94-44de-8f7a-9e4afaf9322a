/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      main.dart
///
/// DESCRIPTION :    Panabit Client客户端应用程序入口点，负责应用初始化、
///                  依赖注入配置、窗口管理和路由设置
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

import 'core/dependency_injection.dart';
import 'core/app_state.dart';
import 'services/connection_manager.dart';
import 'services/data_manager.dart';
import 'services/auth_service.dart';
import 'services/language_service.dart';
import 'services/single_instance_service.dart';
import 'services/log_service.dart';
import 'services/routing_settings_service.dart';
import 'services/app_initialization_service.dart';
import 'models/routing_settings.dart';
import 'widgets/app_builder.dart';
import 'utils/constants.dart';

/// main
///
/// DESCRIPTION:
///     应用程序入口点，负责初始化应用并启动主界面
///
/// PARAMETERS:
///     无
///
/// RETURNS:
///     Future<void> - 异步操作完成标识
///
/// THROWS:
///     Exception - 应用初始化失败时抛出异常
void main() async {
  try {
    // 执行应用初始化
    final singleInstanceService = await AppInitializationService.initialize();

    // 如果不是第一个实例，初始化服务会自动退出应用
    if (singleInstanceService == null) {
      return;
    }

    // 启动应用
    runApp(MyApp(singleInstanceService: singleInstanceService));

    // 记录应用启动成功
    final logService = serviceLocator<LogService>();
    logService.info(kLogModuleApp, kMessageAppStarted);
  } catch (e) {
    // 如果初始化失败，记录详细错误信息并退出
    // 使用日志服务记录关键错误信息（如果可用）
    try {
      final logService = serviceLocator<LogService>();
      logService.error(kLogModuleApp, '$kErrorAppInitializationFailed: $e');
    } catch (_) {
      // 如果日志服务不可用，静默处理错误
      // 应用初始化失败时直接退出，避免使用print
    }
    exit(kExitCodeInitializationFailed);
  }
}

/// MyApp
///
/// PURPOSE:
///     应用程序主Widget，负责配置MaterialApp和Provider状态管理
///
/// FEATURES:
///     - Provider状态管理配置：配置全局状态管理
///     - 多语言支持：配置国际化和本地化
///     - 主题配置：应用设计系统主题
///     - 路由配置：配置应用路由和导航
///     - 错误处理：处理应用级别的错误
///
/// USAGE:
///     作为应用的根Widget，在main函数中启动
class MyApp extends StatefulWidget {
  final SingleInstanceService singleInstanceService;

  /// MyApp构造函数
  ///
  /// DESCRIPTION:
  ///     创建应用主Widget实例
  ///
  /// PARAMETERS:
  ///     singleInstanceService - 单例服务实例
  const MyApp({
    super.key,
    required this.singleInstanceService,
  });

  @override
  State<MyApp> createState() => _MyAppState();
}

/// _MyAppState
///
/// PURPOSE:
///     MyApp的状态管理类，负责应用的生命周期管理和资源清理
///
/// FEATURES:
///     - 导航键管理：管理全局导航状态
///     - 窗口关闭处理：处理桌面平台的窗口关闭事件
///     - 资源清理：在应用销毁时清理所有资源
///     - Provider配置：配置全局状态管理
///
/// USAGE:
///     作为MyApp的私有状态类，自动管理应用生命周期
class _MyAppState extends State<MyApp> {
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();

  /// dispose
  ///
  /// DESCRIPTION:
  ///     清理应用资源，包括单例服务、窗口处理器和依赖注入
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  @override
  void dispose() {
    try {
      // 释放单例检查服务资源
      widget.singleInstanceService.dispose();

      // 清理AppBuilder资源
      AppBuilder.dispose();

      // 清理应用初始化服务
      AppInitializationService.dispose();
    } catch (e) {
      // 记录清理错误，但不阻止应用关闭
      // 静默处理清理错误，避免影响应用关闭
    }

    super.dispose();
  }

  /// build
  ///
  /// DESCRIPTION:
  ///     构建应用主界面，配置Provider状态管理和MaterialApp
  ///
  /// PARAMETERS:
  ///     context - 构建上下文
  ///
  /// RETURNS:
  ///     Widget - 应用主界面Widget
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: _buildProviders(),
      child: AppBuilder.buildApp(context, _navigatorKey),
    );
  }

  /// _buildProviders
  ///
  /// DESCRIPTION:
  ///     构建Provider列表，配置全局状态管理
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     List<SingleChildWidget> - Provider列表
  List<SingleChildWidget> _buildProviders() {
    return [
      ..._buildCoreProviders(),
      ..._buildServiceProviders(),
      _buildRoutingSettingsProvider(),
    ];
  }

  /// _buildCoreProviders
  ///
  /// DESCRIPTION:
  ///     构建核心Provider列表，包括应用状态和语言服务
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     List<SingleChildWidget> - 核心Provider列表
  List<SingleChildWidget> _buildCoreProviders() {
    return [
      ChangeNotifierProvider<AppState>.value(
        value: serviceLocator<AppState>(),
      ),
      ChangeNotifierProvider<LanguageService>.value(
        value: serviceLocator<LanguageService>(),
      ),
    ];
  }

  /// _buildServiceProviders
  ///
  /// DESCRIPTION:
  ///     构建服务Provider列表，包括认证和连接管理服务
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     List<SingleChildWidget> - 服务Provider列表
  List<SingleChildWidget> _buildServiceProviders() {
    return [
      ChangeNotifierProvider<AuthService>.value(
        value: serviceLocator<AuthService>(),
      ),
      ChangeNotifierProvider<ConnectionManager>.value(
        value: serviceLocator<ConnectionManager>(),
      ),
      ChangeNotifierProvider<DataManager>.value(
        value: serviceLocator<DataManager>(),
      ),
    ];
  }

  /// _buildRoutingSettingsProvider
  ///
  /// DESCRIPTION:
  ///     构建路由设置Provider，使用GetIt中注册的RoutingSettingsModel实例
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     ChangeNotifierProvider<RoutingSettingsModel> - 路由设置Provider
  ChangeNotifierProvider<RoutingSettingsModel> _buildRoutingSettingsProvider() {
    final logService = serviceLocator<LogService>();

    return ChangeNotifierProvider<RoutingSettingsModel>(
      create: (_) {
        logService.info(kLogModuleApp, 'Getting RoutingSettingsModel from service locator');
        // 使用GetIt中注册的单例实例，确保全局状态一致
        final model = serviceLocator<RoutingSettingsModel>();
        // 异步加载保存的设置
        _loadSavedRoutingSettings(model);
        return model;
      },
    );
  }

  /// _loadSavedRoutingSettings
  ///
  /// DESCRIPTION:
  ///     异步加载保存的路由设置到RoutingSettingsModel中
  ///
  /// PARAMETERS:
  ///     model - 路由设置模型实例
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 加载设置失败时记录错误但不抛出异常
  void _loadSavedRoutingSettings(RoutingSettingsModel model) async {
    try {
      final routingSettingsService = serviceLocator<RoutingSettingsService>();
      final logService = serviceLocator<LogService>();

      logService.info(kLogModuleApp, 'Loading saved routing settings for provider initialization');

      // 从本地加载保存的设置
      final savedSettings = await routingSettingsService.loadRoutingSettings();
      if (savedSettings != null) {
        // 更新模型
        model.updateSettings(
          mode: savedSettings.mode,
          customRoutes: savedSettings.customRoutes,
          autoStart: savedSettings.autoStart,
        );

        logService.info(kLogModuleApp, 'Saved routing settings loaded into provider: mode=${savedSettings.mode}, customRoutes=${savedSettings.customRoutes}');
      } else {
        logService.info(kLogModuleApp, 'No saved routing settings found, using defaults');
      }
    } catch (e) {
      final logService = serviceLocator<LogService>();
      logService.error(kLogModuleApp, 'Failed to load saved routing settings for provider: $e');
    }
  }
}
