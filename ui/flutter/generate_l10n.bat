@echo off
echo Generating localization files...

REM 检查Flutter是否可用
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Flutter command not found. Please ensure Flutter is installed and in PATH.
    pause
    exit /b 1
)

REM 生成本地化文件
echo Running flutter gen-l10n...
flutter gen-l10n

if %errorlevel% equ 0 (
    echo Localization files generated successfully!
) else (
    echo Failed to generate localization files.
    pause
    exit /b 1
)

echo.
echo Generated files:
echo - lib/generated/l10n/app_localizations.dart
echo - lib/generated/l10n/app_localizations_en.dart
echo - lib/generated/l10n/app_localizations_zh.dart

pause
