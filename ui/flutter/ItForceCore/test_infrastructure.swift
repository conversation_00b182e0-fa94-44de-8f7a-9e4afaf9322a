#!/usr/bin/env swift

/**
 * FILE: test_infrastructure.swift
 *
 * DESCRIPTION:
 *     Simple test script to verify Infrastructure layer functionality
 *     Tests basic functionality without complex test framework dependencies
 *
 * AUTHOR: wei
 * HISTORY: 01/07/2025 create
 */

import Foundation

// Import the ItForceCore module
#if canImport(ItForceCore)
import ItForceCore
#endif

// Simple test framework
class SimpleTest {
    private var testCount = 0
    private var passCount = 0
    
    func assert(_ condition: Bool, _ message: String) {
        testCount += 1
        if condition {
            passCount += 1
            print("✅ PASS: \(message)")
        } else {
            print("❌ FAIL: \(message)")
        }
    }
    
    func assertEqual<T: Equatable>(_ actual: T, _ expected: T, _ message: String) {
        assert(actual == expected, "\(message) - Expected: \(expected), Actual: \(actual)")
    }
    
    func assertNotNil<T>(_ value: T?, _ message: String) {
        assert(value != nil, "\(message) - Value should not be nil")
    }
    
    func printSummary() {
        print("\n📊 Test Summary:")
        print("Total tests: \(testCount)")
        print("Passed: \(passCount)")
        print("Failed: \(testCount - passCount)")
        print("Success rate: \(passCount)/\(testCount) (\(Int(Double(passCount)/Double(testCount)*100))%)")
    }
}

// Test Infrastructure components
func testInfrastructure() {
    let test = SimpleTest()
    
    print("🧪 Testing Infrastructure Layer Components\n")
    
    // Test 1: LoggingConfiguration
    print("1️⃣ Testing LoggingConfiguration...")
    let loggingConfig = LoggingConfiguration.development
    test.assertEqual(loggingConfig.level, .debug, "Development config should have debug level")
    test.assertEqual(loggingConfig.subsystem, "com.itforce.vpn.dev", "Development config subsystem")
    test.assertEqual(loggingConfig.category, "development", "Development config category")
    test.assert(loggingConfig.includeCaller, "Development config should include caller")
    test.assert(loggingConfig.enablePerformanceTracking, "Development config should enable performance tracking")
    
    // Test 2: LoggingSystem
    print("\n2️⃣ Testing LoggingSystem...")
    LoggingSystem.shared.configure(with: loggingConfig)
    let logger = LoggingSystem.shared.defaultLogger
    test.assertNotNil(logger, "Default logger should be available")
    test.assertEqual(logger.getLevel(), .debug, "Logger should have debug level")
    
    // Test 3: PerformanceConfiguration
    print("\n3️⃣ Testing PerformanceConfiguration...")
    let perfConfig = PerformanceConfiguration.default
    test.assertNotNil(perfConfig, "Default performance configuration should exist")
    
    // Test 4: ErrorHandlingConfiguration
    print("\n4️⃣ Testing ErrorHandlingConfiguration...")
    let errorConfig = ErrorHandlingConfiguration.default
    test.assertNotNil(errorConfig, "Default error handling configuration should exist")
    
    // Test 5: CoreConfiguration
    print("\n5️⃣ Testing CoreConfiguration...")
    let coreConfig = CoreConfiguration.default
    test.assertEqual(coreConfig.logging.level, .info, "Core config should have info level")
    test.assertNotNil(coreConfig.performance, "Core config should have performance config")
    test.assertNotNil(coreConfig.errorHandling, "Core config should have error handling config")
    
    // Test 6: ItForceError types
    print("\n6️⃣ Testing ItForceError types...")
    let networkError = ItForceError.networkTimeout
    test.assertNotNil(networkError.errorDescription, "Network timeout error should have description")
    
    let connectionError = ItForceError.connectionFailed(reason: "Test failure")
    test.assertNotNil(connectionError.errorDescription, "Connection failed error should have description")
    test.assert(connectionError.localizedDescription.contains("Test failure"), "Error should contain reason")
    
    // Test 7: BufferPoolConfiguration
    print("\n7️⃣ Testing BufferPoolConfiguration...")
    let bufferConfig = BufferPoolConfiguration(initialSize: 10, maxSize: 100, bufferSize: 1500, memoryLimit: 10*1024*1024)
    test.assertNotNil(bufferConfig, "Buffer pool configuration should be created")
    
    // Test 8: PerformanceManager (basic initialization)
    print("\n8️⃣ Testing PerformanceManager...")
    let perfManager = PerformanceManager.shared
    test.assertNotNil(perfManager, "Performance manager should be available")
    
    test.printSummary()
}

// Test async functionality
func testAsyncInfrastructure() async {
    let test = SimpleTest()
    
    print("\n🔄 Testing Async Infrastructure Components\n")
    
    // Test 1: PerformanceManager async operations
    print("1️⃣ Testing PerformanceManager async operations...")
    let perfManager = PerformanceManager.shared
    
    do {
        // Configure with a simple logger
        let logger = LoggingSystem.shared.defaultLogger
        await perfManager.configure(logger: logger)
        test.assert(true, "Performance manager configuration succeeded")
        
        // Test starting and stopping
        try await perfManager.start()
        test.assert(true, "Performance manager start succeeded")
        
        // Test counter increment
        await perfManager.incrementCounter("test_counter")
        test.assert(true, "Counter increment succeeded")
        
        // Test operation tracking
        let result = await perfManager.trackOperation("test_operation") {
            return "test_result"
        }
        test.assertEqual(result, "test_result", "Operation tracking should return correct result")
        
        // Get metrics
        let metrics = await perfManager.getCurrentMetrics()
        test.assertNotNil(metrics, "Metrics should be available")
        
        // Stop performance manager
        await perfManager.stop()
        test.assert(true, "Performance manager stop succeeded")
        
    } catch {
        test.assert(false, "Performance manager operations failed: \(error)")
    }
    
    // Test 2: DefaultBufferPoolManager
    print("\n2️⃣ Testing DefaultBufferPoolManager...")
    let bufferManager = DefaultBufferPoolManager()
    
    do {
        let logger = LoggingSystem.shared.defaultLogger
        await bufferManager.configure(logger: logger)
        test.assert(true, "Buffer pool manager configuration succeeded")
        
        // Test buffer allocation
        let buffer = await bufferManager.getPacketBuffer()
        test.assertNotNil(buffer, "Packet buffer should be allocated")
        test.assert(buffer.count >= 0, "Buffer should have valid size")
        
        // Test buffer return
        await bufferManager.returnPacketBuffer(buffer)
        test.assert(true, "Buffer return succeeded")
        
        // Test statistics
        let stats = await bufferManager.getOverallStatistics()
        test.assertNotNil(stats, "Buffer pool statistics should be available")
        
        // Shutdown
        await bufferManager.shutdown()
        test.assert(true, "Buffer pool manager shutdown succeeded")
        
    } catch {
        test.assert(false, "Buffer pool manager operations failed: \(error)")
    }
    
    test.printSummary()
}

// Main execution
func main() async {
    print("🚀 Infrastructure Layer Testing Started\n")
    
    // Test synchronous components
    testInfrastructure()
    
    // Test asynchronous components
    await testAsyncInfrastructure()
    
    print("\n✨ Infrastructure Layer Testing Completed!")
}

// Run the tests
Task {
    await main()
    exit(0)
}

// Keep the script running
RunLoop.main.run()
