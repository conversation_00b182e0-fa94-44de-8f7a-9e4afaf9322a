/**
 * FILE: main.swift
 *
 * DESCRIPTION:
 *     Main entry point for Infrastructure layer tests
 *     Runs all Infrastructure tests independently
 *
 * AUTHOR: wei
 * HISTORY: 01/07/2025 create
 */

import Foundation
import ItForceCore

// Simple test framework
class TestRunner {
    private var testCount = 0
    private var passCount = 0
    private var failCount = 0
    
    func run(_ testName: String, _ test: () throws -> Void) {
        testCount += 1
        do {
            try test()
            passCount += 1
            print("✅ PASS: \(testName)")
        } catch {
            failCount += 1
            print("❌ FAIL: \(testName) - \(error)")
        }
    }
    
    func runAsync(_ testName: String, _ test: () async throws -> Void) async {
        testCount += 1
        do {
            try await test()
            passCount += 1
            print("✅ PASS: \(testName)")
        } catch {
            failCount += 1
            print("❌ FAIL: \(testName) - \(error)")
        }
    }
    
    func assert(_ condition: Bool, _ message: String) throws {
        if !condition {
            throw TestError.assertionFailed(message)
        }
    }
    
    func assertEqual<T: Equatable>(_ actual: T, _ expected: T, _ message: String) throws {
        if actual != expected {
            throw TestError.assertionFailed("\(message) - Expected: \(expected), Actual: \(actual)")
        }
    }
    
    func assertNotNil<T>(_ value: T?, _ message: String) throws {
        if value == nil {
            throw TestError.assertionFailed("\(message) - Value should not be nil")
        }
    }
    
    func printSummary() {
        print("\n📊 Test Summary:")
        print("Total tests: \(testCount)")
        print("Passed: \(passCount)")
        print("Failed: \(failCount)")
        if testCount > 0 {
            print("Success rate: \(passCount)/\(testCount) (\(Int(Double(passCount)/Double(testCount)*100))%)")
        }
        
        if failCount == 0 {
            print("🎉 All tests passed!")
        } else {
            print("⚠️  Some tests failed")
        }
    }
}

enum TestError: Error {
    case assertionFailed(String)
}

// Test Infrastructure components
func runInfrastructureTests() async {
    let runner = TestRunner()
    
    print("🧪 Running Infrastructure Layer Tests\n")
    
    // Test 1: LoggingConfiguration
    runner.run("LoggingConfiguration Development") {
        let config = LoggingConfiguration.development
        try runner.assertEqual(config.level, .debug, "Development config should have debug level")
        try runner.assertEqual(config.subsystem, "com.itforce.vpn.dev", "Development config subsystem")
        try runner.assertEqual(config.category, "development", "Development config category")
        try runner.assert(config.includeCaller, "Development config should include caller")
        try runner.assert(config.enablePerformanceTracking, "Development config should enable performance tracking")
    }
    
    runner.run("LoggingConfiguration Production") {
        let config = LoggingConfiguration.production
        try runner.assertEqual(config.level, .info, "Production config should have info level")
        try runner.assertEqual(config.subsystem, "com.itforce.vpn", "Production config subsystem")
        try runner.assertEqual(config.category, "production", "Production config category")
        try runner.assert(!config.includeCaller, "Production config should not include caller")
        try runner.assert(!config.enablePerformanceTracking, "Production config should not enable performance tracking")
    }
    
    // Test 2: LoggingSystem
    runner.run("LoggingSystem Configuration") {
        let config = LoggingConfiguration.development
        LoggingSystem.shared.configure(with: config)
        let logger = LoggingSystem.shared.defaultLogger
        try runner.assertNotNil(logger, "Default logger should be available")
        try runner.assertEqual(logger.getLevel(), .debug, "Logger should have debug level")
    }
    
    // Test 3: CoreConfiguration
    runner.run("CoreConfiguration Default") {
        let config = CoreConfiguration.default
        try runner.assertEqual(config.logging.level, .info, "Core config should have info level")
        try runner.assertNotNil(config.performance, "Core config should have performance config")
        try runner.assertNotNil(config.errorHandling, "Core config should have error handling config")
    }
    
    // Test 4: ItForceError types
    runner.run("ItForceError Types") {
        let networkError = ItForceError.networkTimeout
        try runner.assertNotNil(networkError.errorDescription, "Network timeout error should have description")
        
        let connectionError = ItForceError.connectionFailed(reason: "Test failure")
        try runner.assertNotNil(connectionError.errorDescription, "Connection failed error should have description")
        try runner.assert(connectionError.localizedDescription.contains("Test failure"), "Error should contain reason")
    }
    
    // Test 5: BufferPoolConfiguration
    runner.run("BufferPoolConfiguration") {
        let config = BufferPoolConfiguration(initialSize: 10, maxSize: 100, bufferSize: 1500, memoryLimit: 10*1024*1024)
        try runner.assertNotNil(config, "Buffer pool configuration should be created")
    }
    
    // Test 6: PerformanceManager basic functionality
    await runner.runAsync("PerformanceManager Basic") {
        let perfManager = PerformanceManager.shared
        try runner.assertNotNil(perfManager, "Performance manager should be available")
        
        let logger = LoggingSystem.shared.defaultLogger
        await perfManager.configure(logger: logger)
        
        try await perfManager.start()
        await perfManager.incrementCounter("test_counter")
        
        let metrics = await perfManager.getCurrentMetrics()
        try runner.assertNotNil(metrics, "Metrics should be available")
        
        await perfManager.stop()
    }
    
    // Test 7: DefaultBufferPoolManager
    await runner.runAsync("DefaultBufferPoolManager") {
        let bufferManager = DefaultBufferPoolManager()
        let logger = LoggingSystem.shared.defaultLogger
        await bufferManager.configure(logger: logger)
        
        let buffer = await bufferManager.getPacketBuffer()
        try runner.assertNotNil(buffer, "Packet buffer should be allocated")
        try runner.assert(buffer.count >= 0, "Buffer should have valid size")
        
        await bufferManager.returnPacketBuffer(buffer)
        
        let stats = await bufferManager.getOverallStatistics()
        try runner.assertNotNil(stats, "Buffer pool statistics should be available")
        
        await bufferManager.shutdown()
    }
    
    // Test 8: Logger functionality
    runner.run("Logger Functionality") {
        let logger = LoggingSystem.shared.defaultLogger
        
        // Test basic logging (should not throw)
        logger.info("Test info message")
        logger.debug("Test debug message")
        logger.warning("Test warning message")
        logger.error("Test error message")
        
        // Test with fields
        logger.info("Test with fields", fields: LogField(key: "test_key", value: "test_value"))
    }
    
    // Test 9: Performance tracking
    await runner.runAsync("Performance Tracking") {
        let perfManager = PerformanceManager.shared
        let logger = LoggingSystem.shared.defaultLogger
        await perfManager.configure(logger: logger)
        
        try await perfManager.start()
        
        let result = await perfManager.trackOperation("test_operation") {
            return "operation_result"
        }
        
        try runner.assertEqual(result, "operation_result", "Operation tracking should return correct result")
        
        await perfManager.stop()
    }
    
    runner.printSummary()
}

// Main execution
@main
struct InfrastructureTestsMain {
    static func main() async {
        print("🚀 Infrastructure Layer Testing Started\n")
        await runInfrastructureTests()
        print("\n✨ Infrastructure Layer Testing Completed!")
    }
}
