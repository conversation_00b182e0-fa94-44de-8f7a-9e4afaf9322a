/**
 * FILE: NetworkStatisticsTests.swift
 *
 * DESCRIPTION:
 *     Comprehensive test suite for NetworkStatistics class.
 *     Tests delay measurement, packet statistics, and network quality monitoring
 *     to ensure functional equivalence with Go backend statistics tracking.
 *
 * AUTHOR: wei
 * HISTORY: 01/07/2025 create comprehensive NetworkStatistics test suite
 */

import XCTest
@testable import ItForceCore

/**
 * NAME: NetworkStatisticsTests
 *
 * DESCRIPTION:
 *     Test suite for NetworkStatistics functionality.
 *     Covers delay measurement, packet counting, statistics calculation,
 *     and network quality monitoring features.
 *
 * TEST COVERAGE:
 *     - Network statistics initialization
 *     - Delay measurement and tracking (current, min, max, average)
 *     - Packet statistics (sent, received, bytes transferred)
 *     - Statistics calculation and aggregation
 *     - Statistics reset and maintenance
 *     - Delay measurement helper class
 *     - Statistics summary generation
 *     - Concurrent operation safety
 *     - Performance and accuracy validation
 */
final class NetworkStatisticsTests: XCTestCase {
    
    // MARK: - Test Properties
    
    private var networkStats: NetworkStatistics!
    
    // MARK: - Test Lifecycle
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Create network statistics instance
        networkStats = NetworkStatistics()
    }
    
    override func tearDown() async throws {
        // Clean up network statistics
        networkStats = nil
        
        try await super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    /**
     * NAME: testNetworkStatisticsInitialization
     *
     * DESCRIPTION:
     *     Tests network statistics initialization with default values.
     *     Verifies initial state and property values.
     */
    func testNetworkStatisticsInitialization() async throws {
        // Test: Initial delay values
        let currentDelay = await networkStats.getCurrentDelay()
        let minDelay = await networkStats.getMinDelay()
        let maxDelay = await networkStats.getMaxDelay()
        let averageDelay = await networkStats.getAverageDelay()
        
        XCTAssertEqual(currentDelay, 0, "Initial current delay should be 0")
        XCTAssertEqual(minDelay, 0, "Initial min delay should be 0")
        XCTAssertEqual(maxDelay, 0, "Initial max delay should be 0")
        XCTAssertEqual(averageDelay, 0.0, "Initial average delay should be 0.0")
        
        // Test: Initial packet statistics
        let packetStats = await networkStats.getPacketStatistics()
        XCTAssertEqual(packetStats.sent, 0, "Initial packets sent should be 0")
        XCTAssertEqual(packetStats.received, 0, "Initial packets received should be 0")
        XCTAssertEqual(packetStats.bytes, 0, "Initial bytes transferred should be 0")
    }
    
    // MARK: - Delay Measurement Tests
    
    /**
     * NAME: testDelayRecording
     *
     * DESCRIPTION:
     *     Tests delay recording and basic statistics updates.
     *     Verifies current delay tracking and min/max updates.
     */
    func testDelayRecording() async throws {
        // Test: Record first delay
        let firstDelay: UInt32 = 50
        await networkStats.recordDelay(firstDelay)
        
        let currentDelay = await networkStats.getCurrentDelay()
        let minDelay = await networkStats.getMinDelay()
        let maxDelay = await networkStats.getMaxDelay()
        
        XCTAssertEqual(currentDelay, firstDelay, "Current delay should match recorded delay")
        XCTAssertEqual(minDelay, firstDelay, "Min delay should be first recorded delay")
        XCTAssertEqual(maxDelay, firstDelay, "Max delay should be first recorded delay")
        
        // Test: Record lower delay
        let lowerDelay: UInt32 = 30
        await networkStats.recordDelay(lowerDelay)
        
        let newMinDelay = await networkStats.getMinDelay()
        let newCurrentDelay = await networkStats.getCurrentDelay()
        
        XCTAssertEqual(newCurrentDelay, lowerDelay, "Current delay should be updated")
        XCTAssertEqual(newMinDelay, lowerDelay, "Min delay should be updated to lower value")
        
        // Test: Record higher delay
        let higherDelay: UInt32 = 100
        await networkStats.recordDelay(higherDelay)
        
        let newMaxDelay = await networkStats.getMaxDelay()
        let finalCurrentDelay = await networkStats.getCurrentDelay()
        
        XCTAssertEqual(finalCurrentDelay, higherDelay, "Current delay should be updated")
        XCTAssertEqual(newMaxDelay, higherDelay, "Max delay should be updated to higher value")
    }
    
    /**
     * NAME: testAverageDelayCalculation
     *
     * DESCRIPTION:
     *     Tests average delay calculation over multiple measurements.
     *     Verifies rolling average computation accuracy.
     */
    func testAverageDelayCalculation() async throws {
        // Test: Record multiple delays
        let delays: [UInt32] = [10, 20, 30, 40, 50]
        
        for delay in delays {
            await networkStats.recordDelay(delay)
        }
        
        // Test: Calculate expected average
        let expectedAverage = Double(delays.reduce(0, +)) / Double(delays.count)
        let actualAverage = await networkStats.getAverageDelay()
        
        XCTAssertEqual(actualAverage, expectedAverage, accuracy: 0.01, "Average delay should be calculated correctly")
    }
    
    /**
     * NAME: testDelayStatisticsRetrieval
     *
     * DESCRIPTION:
     *     Tests delay statistics retrieval as a tuple.
     *     Verifies comprehensive delay information access.
     */
    func testDelayStatisticsRetrieval() async throws {
        // Setup: Record various delays
        await networkStats.recordDelay(25)
        await networkStats.recordDelay(75)
        await networkStats.recordDelay(50)
        
        // Test: Get delay statistics
        let delayStats = await networkStats.getDelayStatistics()
        
        XCTAssertEqual(delayStats.current, 50, "Current delay should match last recorded")
        XCTAssertEqual(delayStats.min, 25, "Min delay should be lowest recorded")
        XCTAssertEqual(delayStats.max, 75, "Max delay should be highest recorded")
    }
    
    /**
     * NAME: testDelayMeasurementRollingWindow
     *
     * DESCRIPTION:
     *     Tests delay measurement rolling window behavior.
     *     Verifies old measurements are discarded when limit is reached.
     */
    func testDelayMeasurementRollingWindow() async throws {
        // Test: Record more than maximum measurements (100)
        for i in 1...150 {
            await networkStats.recordDelay(UInt32(i))
        }
        
        // Test: Average should be calculated from last 100 measurements
        let averageDelay = await networkStats.getAverageDelay()
        
        // Expected average of measurements 51-150
        let expectedAverage = Double((51...150).reduce(0, +)) / 100.0
        
        XCTAssertEqual(averageDelay, expectedAverage, accuracy: 0.01, "Average should use rolling window")
    }
    
    // MARK: - Packet Statistics Tests
    
    /**
     * NAME: testPacketSentRecording
     *
     * DESCRIPTION:
     *     Tests packet sent recording and byte counting.
     *     Verifies packet count and byte transfer tracking.
     */
    func testPacketSentRecording() async throws {
        // Test: Record sent packets
        await networkStats.recordPacketSent(bytes: 100)
        await networkStats.recordPacketSent(bytes: 200)
        await networkStats.recordPacketSent(bytes: 150)
        
        // Test: Verify packet statistics
        let packetStats = await networkStats.getPacketStatistics()
        
        XCTAssertEqual(packetStats.sent, 3, "Should record 3 sent packets")
        XCTAssertEqual(packetStats.bytes, 450, "Should record total bytes sent")
    }
    
    /**
     * NAME: testPacketReceivedRecording
     *
     * DESCRIPTION:
     *     Tests packet received recording and byte counting.
     *     Verifies received packet tracking accuracy.
     */
    func testPacketReceivedRecording() async throws {
        // Test: Record received packets
        await networkStats.recordPacketReceived(bytes: 80)
        await networkStats.recordPacketReceived(bytes: 120)
        await networkStats.recordPacketReceived(bytes: 90)
        
        // Test: Verify packet statistics
        let packetStats = await networkStats.getPacketStatistics()
        
        XCTAssertEqual(packetStats.received, 3, "Should record 3 received packets")
        XCTAssertEqual(packetStats.bytes, 290, "Should record total bytes received")
    }
    
    /**
     * NAME: testMixedPacketStatistics
     *
     * DESCRIPTION:
     *     Tests mixed packet recording (sent and received).
     *     Verifies separate tracking and combined byte counting.
     */
    func testMixedPacketStatistics() async throws {
        // Test: Record mixed packets
        await networkStats.recordPacketSent(bytes: 100)
        await networkStats.recordPacketReceived(bytes: 80)
        await networkStats.recordPacketSent(bytes: 150)
        await networkStats.recordPacketReceived(bytes: 120)
        
        // Test: Verify separate packet counts
        let packetStats = await networkStats.getPacketStatistics()
        
        XCTAssertEqual(packetStats.sent, 2, "Should record 2 sent packets")
        XCTAssertEqual(packetStats.received, 2, "Should record 2 received packets")
        XCTAssertEqual(packetStats.bytes, 450, "Should record total bytes transferred")
    }
    
    // MARK: - Statistics Reset Tests
    
    /**
     * NAME: testStatisticsReset
     *
     * DESCRIPTION:
     *     Tests statistics reset functionality.
     *     Verifies all statistics are properly reset to initial values.
     */
    func testStatisticsReset() async throws {
        // Setup: Record some statistics
        await networkStats.recordDelay(50)
        await networkStats.recordPacketSent(bytes: 100)
        await networkStats.recordPacketReceived(bytes: 80)
        
        // Test: Reset statistics
        await networkStats.resetStatistics()
        
        // Test: Verify all statistics are reset
        let currentDelay = await networkStats.getCurrentDelay()
        let minDelay = await networkStats.getMinDelay()
        let maxDelay = await networkStats.getMaxDelay()
        let averageDelay = await networkStats.getAverageDelay()
        let packetStats = await networkStats.getPacketStatistics()
        
        XCTAssertEqual(currentDelay, 0, "Current delay should be reset")
        XCTAssertEqual(minDelay, 0, "Min delay should be reset")
        XCTAssertEqual(maxDelay, 0, "Max delay should be reset")
        XCTAssertEqual(averageDelay, 0.0, "Average delay should be reset")
        XCTAssertEqual(packetStats.sent, 0, "Sent packets should be reset")
        XCTAssertEqual(packetStats.received, 0, "Received packets should be reset")
        XCTAssertEqual(packetStats.bytes, 0, "Bytes transferred should be reset")
    }
    
    // MARK: - Statistics Summary Tests
    
    /**
     * NAME: testStatisticsSummary
     *
     * DESCRIPTION:
     *     Tests statistics summary generation.
     *     Verifies comprehensive statistics reporting.
     */
    func testStatisticsSummary() async throws {
        // Setup: Record various statistics
        await networkStats.recordDelay(25)
        await networkStats.recordDelay(75)
        await networkStats.recordPacketSent(bytes: 100)
        await networkStats.recordPacketReceived(bytes: 80)

        // Test: Get statistics summary
        let summary = await networkStats.getStatisticsSummary()

        // Test: Verify summary contains expected keys
        XCTAssertNotNil(summary["current_delay_ms"], "Summary should contain current delay")
        XCTAssertNotNil(summary["min_delay_ms"], "Summary should contain min delay")
        XCTAssertNotNil(summary["max_delay_ms"], "Summary should contain max delay")
        XCTAssertNotNil(summary["average_delay_ms"], "Summary should contain average delay")
        XCTAssertNotNil(summary["packets_sent"], "Summary should contain packets sent")
        XCTAssertNotNil(summary["packets_received"], "Summary should contain packets received")
        XCTAssertNotNil(summary["bytes_transferred"], "Summary should contain bytes transferred")
        XCTAssertNotNil(summary["measurement_count"], "Summary should contain measurement count")

        // Test: Verify summary values
        XCTAssertEqual(summary["current_delay_ms"] as? UInt32, 75, "Current delay should match")
        XCTAssertEqual(summary["min_delay_ms"] as? UInt32, 25, "Min delay should match")
        XCTAssertEqual(summary["max_delay_ms"] as? UInt32, 75, "Max delay should match")
        XCTAssertEqual(summary["packets_sent"] as? UInt64, 1, "Packets sent should match")
        XCTAssertEqual(summary["packets_received"] as? UInt64, 1, "Packets received should match")
        XCTAssertEqual(summary["bytes_transferred"] as? UInt64, 180, "Bytes transferred should match")
    }

    // MARK: - DelayMeasurement Helper Tests

    /**
     * NAME: testDelayMeasurementHelper
     *
     * DESCRIPTION:
     *     Tests DelayMeasurement helper class functionality.
     *     Verifies elapsed time calculation accuracy.
     */
    func testDelayMeasurementHelper() async throws {
        // Test: Create delay measurement
        let delayMeasurement = DelayMeasurement()

        // Test: Wait a small amount of time
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds

        // Test: Get elapsed time
        let elapsedMs = delayMeasurement.getElapsedMilliseconds()

        // Test: Elapsed time should be approximately 100ms
        XCTAssertGreaterThan(elapsedMs, 90, "Elapsed time should be at least 90ms")
        XCTAssertLessThan(elapsedMs, 200, "Elapsed time should be less than 200ms")
    }

    /**
     * NAME: testDelayMeasurementAccuracy
     *
     * DESCRIPTION:
     *     Tests DelayMeasurement timing accuracy.
     *     Verifies measurement precision for different time intervals.
     */
    func testDelayMeasurementAccuracy() async throws {
        // Test: Multiple measurements with different intervals
        let intervals: [UInt64] = [50_000_000, 100_000_000, 200_000_000] // 50ms, 100ms, 200ms

        for interval in intervals {
            let measurement = DelayMeasurement()
            try await Task.sleep(nanoseconds: interval)
            let elapsed = measurement.getElapsedMilliseconds()

            let expectedMs = UInt32(interval / 1_000_000)
            let tolerance = expectedMs / 2 // 50% tolerance for timing variations

            XCTAssertGreaterThan(elapsed, expectedMs - tolerance, "Elapsed time should be close to expected")
            XCTAssertLessThan(elapsed, expectedMs + tolerance, "Elapsed time should be close to expected")
        }
    }

    // MARK: - Concurrent Operation Tests

    /**
     * NAME: testConcurrentDelayRecording
     *
     * DESCRIPTION:
     *     Tests thread safety of concurrent delay recording.
     *     Verifies Actor model provides proper isolation.
     */
    func testConcurrentDelayRecording() async throws {
        // Test: Concurrent delay recordings
        let tasks = (0..<100).map { index in
            Task {
                await networkStats.recordDelay(UInt32(index + 1))
            }
        }

        // Wait for all tasks to complete
        for task in tasks {
            await task.value
        }

        // Test: Verify final state is consistent
        let currentDelay = await networkStats.getCurrentDelay()
        let minDelay = await networkStats.getMinDelay()
        let maxDelay = await networkStats.getMaxDelay()

        XCTAssertGreaterThan(currentDelay, 0, "Current delay should be set")
        XCTAssertEqual(minDelay, 1, "Min delay should be 1")
        XCTAssertEqual(maxDelay, 100, "Max delay should be 100")
    }

    /**
     * NAME: testConcurrentPacketRecording
     *
     * DESCRIPTION:
     *     Tests thread safety of concurrent packet recording.
     *     Verifies packet counts are accurate under concurrent access.
     */
    func testConcurrentPacketRecording() async throws {
        // Test: Concurrent packet recordings
        let sentTasks = (0..<50).map { _ in
            Task {
                await networkStats.recordPacketSent(bytes: 100)
            }
        }

        let receivedTasks = (0..<50).map { _ in
            Task {
                await networkStats.recordPacketReceived(bytes: 80)
            }
        }

        // Wait for all tasks to complete
        for task in sentTasks + receivedTasks {
            await task.value
        }

        // Test: Verify packet counts
        let packetStats = await networkStats.getPacketStatistics()

        XCTAssertEqual(packetStats.sent, 50, "Should record 50 sent packets")
        XCTAssertEqual(packetStats.received, 50, "Should record 50 received packets")
        XCTAssertEqual(packetStats.bytes, 9000, "Should record total bytes (50*100 + 50*80)")
    }

    /**
     * NAME: testConcurrentStatisticsAccess
     *
     * DESCRIPTION:
     *     Tests concurrent statistics reading and writing.
     *     Verifies data consistency during concurrent operations.
     */
    func testConcurrentStatisticsAccess() async throws {
        // Test: Concurrent read and write operations
        let writeTasks = (0..<20).map { index in
            Task {
                await networkStats.recordDelay(UInt32(index * 10))
                await networkStats.recordPacketSent(bytes: index * 10)
            }
        }

        let readTasks = (0..<20).map { _ in
            Task {
                let _ = await networkStats.getCurrentDelay()
                let _ = await networkStats.getPacketStatistics()
                let _ = await networkStats.getStatisticsSummary()
            }
        }

        // Wait for all tasks to complete
        for task in writeTasks + readTasks {
            await task.value
        }

        // Test: Verify final state is consistent
        let summary = await networkStats.getStatisticsSummary()
        XCTAssertNotNil(summary, "Statistics summary should be available")
    }

    // MARK: - Performance Tests

    /**
     * NAME: testDelayRecordingPerformance
     *
     * DESCRIPTION:
     *     Tests performance of delay recording operations.
     *     Verifies operations complete within reasonable time.
     */
    func testDelayRecordingPerformance() async throws {
        // Test: Measure performance of delay recording
        let startTime = Date()

        for i in 1...1000 {
            await networkStats.recordDelay(UInt32(i))
        }

        let elapsedTime = Date().timeIntervalSince(startTime)

        // Test: Should complete within reasonable time (1 second)
        XCTAssertLessThan(elapsedTime, 1.0, "1000 delay recordings should complete within 1 second")
    }

    /**
     * NAME: testPacketRecordingPerformance
     *
     * DESCRIPTION:
     *     Tests performance of packet recording operations.
     *     Verifies high-frequency packet recording efficiency.
     */
    func testPacketRecordingPerformance() async throws {
        // Test: Measure performance of packet recording
        let startTime = Date()

        for i in 1...1000 {
            await networkStats.recordPacketSent(bytes: i)
            await networkStats.recordPacketReceived(bytes: i)
        }

        let elapsedTime = Date().timeIntervalSince(startTime)

        // Test: Should complete within reasonable time (2 seconds)
        XCTAssertLessThan(elapsedTime, 2.0, "2000 packet recordings should complete within 2 seconds")
    }

    // MARK: - Edge Case Tests

    /**
     * NAME: testZeroDelayRecording
     *
     * DESCRIPTION:
     *     Tests recording of zero delay values.
     *     Verifies proper handling of edge case values.
     */
    func testZeroDelayRecording() async throws {
        // Test: Record zero delay
        await networkStats.recordDelay(0)

        let currentDelay = await networkStats.getCurrentDelay()
        let minDelay = await networkStats.getMinDelay()
        let maxDelay = await networkStats.getMaxDelay()

        XCTAssertEqual(currentDelay, 0, "Should handle zero delay")
        XCTAssertEqual(minDelay, 0, "Min delay should be zero")
        XCTAssertEqual(maxDelay, 0, "Max delay should be zero")
    }

    /**
     * NAME: testMaxDelayRecording
     *
     * DESCRIPTION:
     *     Tests recording of maximum delay values.
     *     Verifies proper handling of large delay values.
     */
    func testMaxDelayRecording() async throws {
        // Test: Record maximum delay value
        let maxDelay = UInt32.max
        await networkStats.recordDelay(maxDelay)

        let currentDelay = await networkStats.getCurrentDelay()
        let recordedMaxDelay = await networkStats.getMaxDelay()

        XCTAssertEqual(currentDelay, maxDelay, "Should handle maximum delay value")
        XCTAssertEqual(recordedMaxDelay, maxDelay, "Max delay should be maximum value")
    }

    /**
     * NAME: testZeroBytePackets
     *
     * DESCRIPTION:
     *     Tests recording of zero-byte packets.
     *     Verifies proper handling of empty packets.
     */
    func testZeroBytePackets() async throws {
        // Test: Record zero-byte packets
        await networkStats.recordPacketSent(bytes: 0)
        await networkStats.recordPacketReceived(bytes: 0)

        let packetStats = await networkStats.getPacketStatistics()

        XCTAssertEqual(packetStats.sent, 1, "Should count zero-byte sent packet")
        XCTAssertEqual(packetStats.received, 1, "Should count zero-byte received packet")
        XCTAssertEqual(packetStats.bytes, 0, "Total bytes should be zero")
    }
}
