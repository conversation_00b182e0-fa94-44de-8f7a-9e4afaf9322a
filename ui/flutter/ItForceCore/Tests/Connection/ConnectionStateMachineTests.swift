/**
 * FILE: ConnectionStateMachineTests.swift
 *
 * DESCRIPTION:
 *     Comprehensive test suite for ConnectionStateMachine class.
 *     Tests state transitions, session management, callback handling, and error tracking
 *     to ensure functional equivalence with Go backend state machine.
 *
 * AUTHOR: wei
 * HISTORY: 01/07/2025 create comprehensive ConnectionStateMachine test suite
 */

import XCTest
@testable import ItForceCore

/**
 * NAME: ConnectionStateMachineTests
 *
 * DESCRIPTION:
 *     Test suite for ConnectionStateMachine functionality.
 *     Covers state transitions, session management, callback handling,
 *     packet handler registration, and error tracking.
 *
 * TEST COVERAGE:
 *     - State machine initialization and configuration
 *     - Connection state transitions and validation
 *     - Session information management (ID and token)
 *     - State change callback registration and notification
 *     - Error callback handling and error tracking
 *     - Packet handler registration and management
 *     - Heartbeat timestamp tracking
 *     - Configuration key-value storage
 *     - Invalid state transition handling
 *     - Concurrent operation safety
 */
final class ConnectionStateMachineTests: XCTestCase {
    
    // MARK: - Test Properties
    
    private var stateMachine: ConnectionStateMachine!
    private var mockLogger: MockLogger!
    
    // MARK: - Test Lifecycle
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Create mock dependencies
        mockLogger = MockLogger()
        
        // Create state machine
        stateMachine = ConnectionStateMachine(logger: mockLogger)
    }
    
    override func tearDown() async throws {
        // Clean up state machine
        stateMachine = nil
        mockLogger = nil
        
        try await super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    /**
     * NAME: testStateMachineInitialization
     *
     * DESCRIPTION:
     *     Tests state machine initialization with default values.
     *     Verifies initial state and property values.
     */
    func testStateMachineInitialization() async throws {
        // Test: Initial state should be initializing
        let initialState = await stateMachine.getState()
        XCTAssertEqual(initialState, .initializing, "Initial state should be initializing")

        // Test: No session info initially
        let sessionInfo = await stateMachine.getSessionInfo()
        XCTAssertNil(sessionInfo, "Should have no session info initially")

        // Test: No error initially
        let errorInfo = await stateMachine.getLastError()
        XCTAssertNil(errorInfo, "Should have no error initially")

        // Test: Configuration should be empty
        let config = await stateMachine.getAllConfiguration()
        XCTAssertTrue(config.isEmpty, "Configuration should be empty initially")
    }
    
    // MARK: - State Transition Tests
    
    /**
     * NAME: testValidStateTransitions
     *
     * DESCRIPTION:
     *     Tests valid state transitions through connection lifecycle.
     *     Verifies proper state progression and timestamp updates.
     */
    func testValidStateTransitions() async throws {
        // Test: Transition to server resolved
        await stateMachine.setState(.serverResolved)
        let resolvedState = await stateMachine.getState()
        XCTAssertEqual(resolvedState, .serverResolved, "Should transition to server resolved")

        // Test: Transition to authenticating
        await stateMachine.setState(.authenticating)
        let authState = await stateMachine.getState()
        XCTAssertEqual(authState, .authenticating, "Should transition to authenticating")

        // Test: Transition to connected
        await stateMachine.setState(.connected)
        let connectedState = await stateMachine.getState()
        XCTAssertEqual(connectedState, .connected, "Should transition to connected")

        // Test: Transition to disconnected
        await stateMachine.setState(.disconnected)
        let disconnectedState = await stateMachine.getState()
        XCTAssertEqual(disconnectedState, .disconnected, "Should transition to disconnected")
    }
    
    /**
     * NAME: testStateChangeTimestamp
     *
     * DESCRIPTION:
     *     Tests state change timestamp tracking.
     *     Verifies timestamp updates on state transitions.
     */
    func testStateChangeTimestamp() async throws {
        // Test: Get initial timestamp
        let initialTime = await stateMachine.getStateChangedTime()

        // Test: Change state and verify timestamp update
        await stateMachine.setState(.serverResolved)
        let updatedTime = await stateMachine.getStateChangedTime()

        XCTAssertGreaterThan(updatedTime, initialTime, "State change should update timestamp")
    }
    
    /**
     * NAME: testSameStateTransition
     *
     * DESCRIPTION:
     *     Tests behavior when setting same state multiple times.
     *     Should not trigger callbacks or update timestamps unnecessarily.
     */
    func testSameStateTransition() async throws {
        // Setup: Register state change callback
        var callbackCount = 0
        await stateMachine.registerStateChangeCallback { _, _ in
            callbackCount += 1
        }
        
        // Test: Set same state multiple times
        await stateMachine.setState(.serverResolved)
        await stateMachine.setState(.serverResolved)
        await stateMachine.setState(.serverResolved)
        
        // Test: Callback should only be triggered once
        XCTAssertEqual(callbackCount, 1, "Callback should only be triggered for actual state change")
    }
    
    // MARK: - Session Management Tests
    
    /**
     * NAME: testSessionInfoManagement
     *
     * DESCRIPTION:
     *     Tests session information management (ID and token).
     *     Verifies session creation, updates, and retrieval.
     */
    func testSessionInfoManagement() async throws {
        // Test: Set session info
        let sessionId: UInt16 = 12345
        let token: UInt32 = 67890
        await stateMachine.setSessionInfo(sessionID: sessionId, authToken: token)
        
        // Test: Retrieve session info
        let sessionInfo = await stateMachine.getSessionInfo()
        XCTAssertNotNil(sessionInfo, "Session info should be set")
        XCTAssertEqual(sessionInfo?.sessionID, sessionId, "Session ID should match")
        XCTAssertEqual(sessionInfo?.authToken, token, "Token should match")
    }
    
    /**
     * NAME: testSessionInfoUpdate
     *
     * DESCRIPTION:
     *     Tests session information updates.
     *     Verifies session info can be updated with new values.
     */
    func testSessionInfoUpdate() async throws {
        // Setup: Set initial session info
        await stateMachine.setSessionInfo(sessionID: 111, authToken: 222)

        // Test: Update session info
        let newSessionId: UInt16 = 333
        let newToken: UInt32 = 444
        await stateMachine.setSessionInfo(sessionID: newSessionId, authToken: newToken)

        // Test: Verify updated values
        let sessionInfo = await stateMachine.getSessionInfo()
        XCTAssertEqual(sessionInfo?.sessionID, newSessionId, "Session ID should be updated")
        XCTAssertEqual(sessionInfo?.authToken, newToken, "Token should be updated")
    }
    
    // MARK: - Callback Tests
    
    /**
     * NAME: testStateChangeCallbacks
     *
     * DESCRIPTION:
     *     Tests state change callback registration and notification.
     *     Verifies callbacks are triggered with correct parameters.
     */
    func testStateChangeCallbacks() async throws {
        // Setup: Register state change callback
        var callbackTriggered = false
        var oldStateReceived: ConnectionState?
        var newStateReceived: ConnectionState?
        
        await stateMachine.registerStateChangeCallback { oldState, newState in
            callbackTriggered = true
            oldStateReceived = oldState
            newStateReceived = newState
        }
        
        // Test: Change state
        await stateMachine.setState(.serverResolved)
        
        // Test: Callback should be triggered with correct parameters
        XCTAssertTrue(callbackTriggered, "State change callback should be triggered")
        XCTAssertEqual(oldStateReceived, .initializing, "Old state should be initializing")
        XCTAssertEqual(newStateReceived, .serverResolved, "New state should be serverResolved")
    }
    
    /**
     * NAME: testMultipleStateChangeCallbacks
     *
     * DESCRIPTION:
     *     Tests registration of multiple state change callbacks.
     *     Verifies all callbacks are triggered on state changes.
     */
    func testMultipleStateChangeCallbacks() async throws {
        // Setup: Register multiple callbacks
        var callback1Triggered = false
        var callback2Triggered = false
        
        await stateMachine.registerStateChangeCallback { _, _ in
            callback1Triggered = true
        }
        
        await stateMachine.registerStateChangeCallback { _, _ in
            callback2Triggered = true
        }
        
        // Test: Change state
        await stateMachine.setState(.serverResolved)
        
        // Test: Both callbacks should be triggered
        XCTAssertTrue(callback1Triggered, "First callback should be triggered")
        XCTAssertTrue(callback2Triggered, "Second callback should be triggered")
    }
    
    /**
     * NAME: testErrorCallbacks
     *
     * DESCRIPTION:
     *     Tests error callback registration and notification.
     *     Verifies error callbacks are triggered with correct parameters.
     */
    func testErrorCallbacks() async throws {
        // Setup: Register error callback
        var errorCallbackTriggered = false
        var errorCodeReceived: Int?
        var errorMessageReceived: String?

        await stateMachine.registerErrorCallback { errorCode, errorMessage in
            errorCallbackTriggered = true
            errorCodeReceived = errorCode
            errorMessageReceived = errorMessage
        }

        // Test: Set error
        let testErrorCode = 500
        let testErrorMessage = "Test error message"
        await stateMachine.setError(code: testErrorCode, message: testErrorMessage)

        // Test: Error callback should be triggered
        XCTAssertTrue(errorCallbackTriggered, "Error callback should be triggered")
        XCTAssertEqual(errorCodeReceived, testErrorCode, "Error code should match")
        XCTAssertEqual(errorMessageReceived, testErrorMessage, "Error message should match")
    }

    // MARK: - Packet Handler Tests

    /**
     * NAME: testPacketHandlerRegistration
     *
     * DESCRIPTION:
     *     Tests packet handler registration and management.
     *     Verifies handlers can be registered and retrieved by packet type.
     */
    func testPacketHandlerRegistration() async throws {
        // Setup: Create test packet handler
        var handlerCalled = false
        let testHandler: PacketHandler = { packet in
            handlerCalled = true
        }

        // Test: Register packet handler
        let packetType: UInt8 = 0x01
        await stateMachine.registerPacketHandler(for: packetType, handler: testHandler)

        // Test: Handler registration should complete without error
        // Note: In real implementation, would test packet handling
        XCTAssertTrue(true, "Packet handler registration should complete")
    }

    /**
     * NAME: testMultiplePacketHandlers
     *
     * DESCRIPTION:
     *     Tests registration of multiple packet handlers for different types.
     *     Verifies handlers are properly isolated by packet type.
     */
    func testMultiplePacketHandlers() async throws {
        // Setup: Create multiple handlers
        let handler1: PacketHandler = { _ in }
        let handler2: PacketHandler = { _ in }

        // Test: Register handlers for different packet types
        await stateMachine.registerPacketHandler(for: 0x01, handler: handler1)
        await stateMachine.registerPacketHandler(for: 0x02, handler: handler2)

        // Test: Multiple handler registration should complete without error
        XCTAssertTrue(true, "Multiple packet handler registration should complete")
    }

    // MARK: - Heartbeat Tests

    /**
     * NAME: testHeartbeatTimestamp
     *
     * DESCRIPTION:
     *     Tests heartbeat timestamp tracking.
     *     Verifies heartbeat timestamp updates and retrieval.
     */
    func testHeartbeatTimestamp() async throws {
        // Test: Get initial heartbeat timestamp
        let initialHeartbeat = await stateMachine.getLastHeartbeat()

        // Test: Update heartbeat timestamp
        await stateMachine.updateHeartbeat()
        let updatedHeartbeat = await stateMachine.getLastHeartbeat()

        // Test: Timestamp should be updated
        XCTAssertGreaterThan(updatedHeartbeat, initialHeartbeat, "Heartbeat timestamp should be updated")
    }

    /**
     * NAME: testHeartbeatInterval
     *
     * DESCRIPTION:
     *     Tests heartbeat interval calculation.
     *     Verifies time since last heartbeat calculation.
     */
    func testHeartbeatInterval() async throws {
        // Test: Update heartbeat
        await stateMachine.updateHeartbeat()

        // Test: Wait a small amount of time
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds

        // Test: Get time since last heartbeat
        let timeSinceHeartbeat = await stateMachine.getTimeSinceLastHeartbeat()
        XCTAssertGreaterThan(timeSinceHeartbeat, 0, "Time since heartbeat should be positive")
        XCTAssertLessThan(timeSinceHeartbeat, 1.0, "Time since heartbeat should be less than 1 second")
    }

    // MARK: - Configuration Tests

    /**
     * NAME: testConfigurationManagement
     *
     * DESCRIPTION:
     *     Tests configuration key-value storage and retrieval.
     *     Verifies configuration can be set and retrieved correctly.
     */
    func testConfigurationManagement() async throws {
        // Test: Set configuration values
        await stateMachine.setConfiguration("server_address", value: "*************")
        await stateMachine.setConfiguration("server_port", value: "8080")
        await stateMachine.setConfiguration("encryption", value: "aes")

        // Test: Retrieve configuration values
        let serverAddress = await stateMachine.getConfiguration("server_address")
        let serverPort = await stateMachine.getConfiguration("server_port")
        let encryption = await stateMachine.getConfiguration("encryption")

        XCTAssertEqual(serverAddress, "*************", "Server address should match")
        XCTAssertEqual(serverPort, "8080", "Server port should match")
        XCTAssertEqual(encryption, "aes", "Encryption should match")
    }

    /**
     * NAME: testConfigurationUpdate
     *
     * DESCRIPTION:
     *     Tests configuration value updates.
     *     Verifies existing configuration can be updated with new values.
     */
    func testConfigurationUpdate() async throws {
        // Setup: Set initial configuration
        await stateMachine.setConfiguration("timeout", value: "30")

        // Test: Update configuration
        await stateMachine.setConfiguration("timeout", value: "60")

        // Test: Verify updated value
        let timeout = await stateMachine.getConfiguration("timeout")
        XCTAssertEqual(timeout, "60", "Configuration should be updated")
    }

    /**
     * NAME: testNonExistentConfiguration
     *
     * DESCRIPTION:
     *     Tests retrieval of non-existent configuration key.
     *     Should return nil for unset keys.
     */
    func testNonExistentConfiguration() async throws {
        // Test: Retrieve non-existent configuration
        let value = await stateMachine.getConfiguration("nonexistent")
        XCTAssertNil(value, "Should return nil for non-existent configuration key")
    }

    // MARK: - Error Tracking Tests

    /**
     * NAME: testErrorTracking
     *
     * DESCRIPTION:
     *     Tests error information tracking and retrieval.
     *     Verifies error code and message storage.
     */
    func testErrorTracking() async throws {
        // Test: Set error information
        let errorCode = 404
        let errorMessage = "Server not found"
        await stateMachine.setError(code: errorCode, message: errorMessage)

        // Test: Retrieve error information
        let errorInfo = await stateMachine.getLastError()
        XCTAssertNotNil(errorInfo, "Error info should be set")
        XCTAssertEqual(errorInfo?.code, errorCode, "Error code should match")
        XCTAssertEqual(errorInfo?.message, errorMessage, "Error message should match")
    }

    /**
     * NAME: testErrorClearing
     *
     * DESCRIPTION:
     *     Tests error information clearing.
     *     Verifies errors can be cleared and reset.
     */
    func testErrorClearing() async throws {
        // Setup: Set error
        await stateMachine.setError(code: 500, message: "Internal error")

        // Test: Reset state machine (which clears errors)
        await stateMachine.reset()

        // Test: Error should be cleared after reset
        let errorInfo = await stateMachine.getLastError()
        XCTAssertNil(errorInfo, "Error should be cleared after reset")
    }
}
