/**
 * FILE: VPNAuthorizationTests.swift
 *
 * DESCRIPTION:
 *     Unit tests for VPN authorization functionality.
 *     Tests VPN Manager, VPN Permission Handler, and authorization flow.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import XCTest
import NetworkExtension
@testable import ItForceCore

/**
 * NAME: VPNAuthorizationTests
 *
 * DESCRIPTION:
 *     Test suite for VPN authorization components.
 *     Tests VPN permission handling, configuration creation, and error scenarios.
 */
final class VPNAuthorizationTests: XCTestCase {
    
    // MARK: - Test Properties
    
    private var vpnManager: VPNManager!
    private var vpnPermissionHandler: VPNPermissionHandler!
    private var logger: LoggerProtocol!
    
    // MARK: - Test Setup
    
    override func setUp() {
        super.setUp()
        
        // Create test logger
        logger = TestLogger()
        
        // Create VPN manager
        vpnManager = VPNManager(logger: logger)
        
        // Create VPN permission handler
        vpnPermissionHandler = VPNPermissionHandler(vpnManager: vpnManager, logger: logger)
    }
    
    override func tearDown() {
        vpnPermissionHandler = nil
        vpnManager = nil
        logger = nil
        super.tearDown()
    }
    
    // MARK: - VPN Manager Tests
    
    /**
     * NAME: testVPNManagerInitialization
     *
     * DESCRIPTION:
     *     Tests VPN Manager initialization.
     */
    func testVPNManagerInitialization() {
        XCTAssertNotNil(vpnManager, "VPN Manager should be initialized")
        XCTAssertEqual(vpnManager.getConnectionStatus(), .invalid, "Initial VPN status should be invalid")
        XCTAssertFalse(vpnManager.isVPNConnected(), "VPN should not be connected initially")
    }
    
    /**
     * NAME: testVPNConfigurationCreation
     *
     * DESCRIPTION:
     *     Tests VPN configuration creation with valid parameters.
     */
    func testVPNConfigurationCreation() {
        let configuration = VPNConfiguration(
            serverAddress: "test.server.com",
            serverPort: 443,
            username: "testuser",
            password: "testpass",
            mtu: 1400,
            encryptionMethod: 1,
            dnsServers: ["*******", "*******"],
            excludedIPs: ["***********"],
            tunnelIP: "********"
        )
        
        XCTAssertEqual(configuration.serverAddress, "test.server.com")
        XCTAssertEqual(configuration.serverPort, 443)
        XCTAssertEqual(configuration.username, "testuser")
        XCTAssertEqual(configuration.password, "testpass")
        XCTAssertEqual(configuration.mtu, 1400)
        XCTAssertEqual(configuration.encryptionMethod, 1)
        XCTAssertEqual(configuration.dnsServers, ["*******", "*******"])
        XCTAssertEqual(configuration.excludedIPs, ["***********"])
        XCTAssertEqual(configuration.tunnelIP, "********")
    }
    
    /**
     * NAME: testVPNConfigurationDefaults
     *
     * DESCRIPTION:
     *     Tests VPN configuration creation with default parameters.
     */
    func testVPNConfigurationDefaults() {
        let configuration = VPNConfiguration(
            serverAddress: "test.server.com",
            serverPort: 443,
            username: "testuser",
            password: "testpass"
        )
        
        XCTAssertEqual(configuration.mtu, 1400, "Default MTU should be 1400")
        XCTAssertEqual(configuration.encryptionMethod, 1, "Default encryption method should be 1")
        XCTAssertEqual(configuration.dnsServers, ["*******", "*******"], "Default DNS servers should be Google DNS")
        XCTAssertTrue(configuration.excludedIPs.isEmpty, "Default excluded IPs should be empty")
        XCTAssertEqual(configuration.tunnelIP, "********", "Default tunnel IP should be ********")
    }
    
    // MARK: - VPN Permission Handler Tests
    
    /**
     * NAME: testVPNPermissionHandlerInitialization
     *
     * DESCRIPTION:
     *     Tests VPN Permission Handler initialization.
     */
    func testVPNPermissionHandlerInitialization() {
        XCTAssertNotNil(vpnPermissionHandler, "VPN Permission Handler should be initialized")
    }
    
    /**
     * NAME: testVPNPermissionStatusCheck
     *
     * DESCRIPTION:
     *     Tests VPN permission status checking.
     */
    func testVPNPermissionStatusCheck() async {
        let permissionResult = await vpnPermissionHandler.checkVPNPermissionStatus()
        
        XCTAssertNotNil(permissionResult, "Permission result should not be nil")
        XCTAssertFalse(permissionResult.userMessage.isEmpty, "User message should not be empty")
        
        // Initial status should be not determined
        XCTAssertEqual(permissionResult.status, .notDetermined, "Initial permission status should be not determined")
        XCTAssertFalse(permissionResult.granted, "Permission should not be granted initially")
    }
    
    // MARK: - VPN Permission Status Tests
    
    /**
     * NAME: testVPNPermissionStatusRawValues
     *
     * DESCRIPTION:
     *     Tests VPN permission status raw values for Platform Channel compatibility.
     */
    func testVPNPermissionStatusRawValues() {
        XCTAssertEqual(VPNPermissionStatus.notDetermined.rawValue, "not_determined")
        XCTAssertEqual(VPNPermissionStatus.denied.rawValue, "denied")
        XCTAssertEqual(VPNPermissionStatus.authorized.rawValue, "authorized")
        XCTAssertEqual(VPNPermissionStatus.restricted.rawValue, "restricted")
    }
    
    /**
     * NAME: testVPNPermissionStatusCaseIterable
     *
     * DESCRIPTION:
     *     Tests VPN permission status case iteration.
     */
    func testVPNPermissionStatusCaseIterable() {
        let allCases = VPNPermissionStatus.allCases
        XCTAssertEqual(allCases.count, 4, "Should have 4 permission status cases")
        XCTAssertTrue(allCases.contains(.notDetermined))
        XCTAssertTrue(allCases.contains(.denied))
        XCTAssertTrue(allCases.contains(.authorized))
        XCTAssertTrue(allCases.contains(.restricted))
    }
    
    // MARK: - VPN Manager Error Tests
    
    /**
     * NAME: testVPNManagerErrorDescriptions
     *
     * DESCRIPTION:
     *     Tests VPN Manager error descriptions for user-friendly messages.
     */
    func testVPNManagerErrorDescriptions() {
        let permissionDeniedError = VPNManagerError.permissionDenied
        XCTAssertEqual(permissionDeniedError.localizedDescription, "VPN permission was denied by user")
        
        let configurationFailedError = VPNManagerError.configurationFailed("Test error")
        XCTAssertEqual(configurationFailedError.localizedDescription, "VPN configuration failed: Test error")
        
        let tunnelNotConfiguredError = VPNManagerError.tunnelNotConfigured
        XCTAssertEqual(tunnelNotConfiguredError.localizedDescription, "VPN tunnel is not configured")
        
        let systemRestrictedError = VPNManagerError.systemRestricted
        XCTAssertEqual(systemRestrictedError.localizedDescription, "VPN is restricted by system policy")
    }
    
    // MARK: - VPN Permission Result Tests
    
    /**
     * NAME: testVPNPermissionResultCreation
     *
     * DESCRIPTION:
     *     Tests VPN permission result creation with different scenarios.
     */
    func testVPNPermissionResultCreation() {
        // Test granted permission result
        let grantedResult = VPNPermissionResult(
            granted: true,
            status: .authorized,
            userMessage: "Permission granted"
        )
        
        XCTAssertTrue(grantedResult.granted)
        XCTAssertEqual(grantedResult.status, .authorized)
        XCTAssertNil(grantedResult.error)
        XCTAssertEqual(grantedResult.userMessage, "Permission granted")
        
        // Test denied permission result with error
        let deniedResult = VPNPermissionResult(
            granted: false,
            status: .denied,
            error: .permissionDenied,
            userMessage: "Permission denied"
        )
        
        XCTAssertFalse(deniedResult.granted)
        XCTAssertEqual(deniedResult.status, .denied)
        XCTAssertNotNil(deniedResult.error)
        XCTAssertEqual(deniedResult.userMessage, "Permission denied")
    }
    
    // MARK: - Integration Tests
    
    /**
     * NAME: testVPNManagerLoadExistingConfiguration
     *
     * DESCRIPTION:
     *     Tests loading existing VPN configuration.
     */
    func testVPNManagerLoadExistingConfiguration() async {
        let configurationLoaded = await vpnManager.loadExistingConfiguration()
        
        // Should return false initially as no configuration exists in test environment
        XCTAssertFalse(configurationLoaded, "Should not find existing configuration in test environment")
    }
}

// MARK: - Test Logger Implementation

/**
 * NAME: TestLogger
 *
 * DESCRIPTION:
 *     Simple test logger implementation for unit tests.
 */
private class TestLogger: LoggerProtocol {
    func log(level: LogLevel, message: String, fields: [LogField], file: String = #file, line: Int = #line, function: String = #function) {}

    func debug(_ message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {}
    func info(_ message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {}
    func warning(_ message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {}
    func error(_ message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {}
    func critical(_ message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {}

    func withModule(_ module: String) -> LoggerProtocol { return self }
    func withFields(_ fields: [LogField]) -> LoggerProtocol { return self }

    func traceMethod(_ methodName: String, file: String = #file, line: Int = #line, function: String = #function) -> () -> Void {
        return {}
    }
    func traceError(_ error: Error, message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {}

    func setLevel(_ level: LogLevel) {}
    func getLevel() -> LogLevel { return .info }
}
