/**
 * FILE: LoggingTests.swift
 *
 * DESCRIPTION:
 *     Unit tests for logging infrastructure
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import XCTest
@testable import ItForceCore

final class LoggingTests: XCTestCase {

    func testCallerInformationCapture() throws {
        // Create a test logger that captures log output
        let testLogger = TestLogger()

        // Call logging methods from this test function
        testLogger.info("Test message from testCallerInformationCapture")
        testLogger.debug("Debug message", fields: LogField(key: "key", value: "value"))

        // Verify that the captured caller information is correct
        XCTAssertEqual(testLogger.lastLoggedFile, "LoggingTests.swift")
        XCTAssertEqual(testLogger.lastLoggedFunction, "testCallerInformationCapture()")
        XCTAssertTrue(testLogger.lastLoggedLine > 0)
    }

    func testSimplifiedLoggingMethods() throws {
        let testLogger = TestLogger()

        // Test simplified logging methods
        testLogger.info("Simplified info message")
        testLogger.debug("Simplified debug message")

        // Verify caller information is captured correctly
        XCTAssertEqual(testLogger.lastLoggedFile, "LoggingTests.swift")
        XCTAssertEqual(testLogger.lastLoggedFunction, "testSimplifiedLoggingMethods()")
    }
}

// MARK: - Test Helper Classes

/**
 * Test logger that captures log output for verification
 */
private class TestLogger: LoggerProtocol {
    var lastLoggedMessage: String = ""
    var lastLoggedLevel: LogLevel = .info
    var lastLoggedFile: String = ""
    var lastLoggedLine: Int = 0
    var lastLoggedFunction: String = ""
    var lastLoggedFields: [LogField] = []

    func log(level: LogLevel, message: String, fields: [LogField], file: String, line: Int, function: String) {
        lastLoggedMessage = message
        lastLoggedLevel = level
        lastLoggedFile = (file as NSString).lastPathComponent
        lastLoggedLine = line
        lastLoggedFunction = function
        lastLoggedFields = fields
    }

    func logDebugInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .debug, message: message, fields: fields, file: file, line: line, function: function)
    }

    func logInfoInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .info, message: message, fields: fields, file: file, line: line, function: function)
    }

    func logWarningInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .warning, message: message, fields: fields, file: file, line: line, function: function)
    }

    func logErrorInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .error, message: message, fields: fields, file: file, line: line, function: function)
    }

    func logCriticalInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .critical, message: message, fields: fields, file: file, line: line, function: function)
    }

    func debug(_ message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {
        log(level: .debug, message: message, fields: Array(fields), file: file, line: line, function: function)
    }

    func info(_ message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {
        log(level: .info, message: message, fields: Array(fields), file: file, line: line, function: function)
    }

    func warning(_ message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {
        log(level: .warning, message: message, fields: Array(fields), file: file, line: line, function: function)
    }

    func error(_ message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {
        log(level: .error, message: message, fields: Array(fields), file: file, line: line, function: function)
    }

    func critical(_ message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {
        log(level: .critical, message: message, fields: Array(fields), file: file, line: line, function: function)
    }

    func withModule(_ module: String) -> LoggerProtocol {
        return self
    }

    func withFields(_ fields: [LogField]) -> LoggerProtocol {
        return self
    }

    func traceMethodInternal(_ methodName: String, file: String, line: Int, function: String) -> () -> Void {
        return {}
    }

    func traceErrorInternal(_ error: Error, message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .error, message: message, fields: fields, file: file, line: line, function: function)
    }

    func setLevel(_ level: LogLevel) {
        // No-op for test
    }

    func getLevel() -> LogLevel {
        return .debug
    }
}
