/**
 * FILE: ConfigurationTests.swift
 *
 * DESCRIPTION:
 *     Unit tests for configuration management infrastructure
 *     Tests configuration types, validation, defaults, and serialization
 *
 * AUTHOR: wei
 * HISTORY: 01/07/2025 create
 */

import XCTest
@testable import ItForceCore

final class ConfigurationTests: XCTestCase {
    
    // MARK: - Logging Configuration Tests
    
    func testLoggingConfigurationDefaults() throws {
        let defaultConfig = LoggingConfiguration()
        
        // Verify default values
        XCTAssertEqual(defaultConfig.level, .info)
        XCTAssertEqual(defaultConfig.subsystem, "com.itforce.vpn")
        XCTAssertEqual(defaultConfig.category, "default")
        XCTAssertTrue(defaultConfig.includeCaller)
        XCTAssertTrue(defaultConfig.enablePerformanceTracking)
    }
    
    func testLoggingConfigurationCustomValues() throws {
        let customConfig = LoggingConfiguration(
            level: .debug,
            subsystem: "com.test.app",
            category: "testing",
            includeCaller: false,
            enablePerformanceTracking: false
        )
        
        // Verify custom values
        XCTAssertEqual(customConfig.level, .debug)
        XCTAssertEqual(customConfig.subsystem, "com.test.app")
        XCTAssertEqual(customConfig.category, "testing")
        XCTAssertFalse(customConfig.includeCaller)
        XCTAssertFalse(customConfig.enablePerformanceTracking)
    }
    
    func testLoggingConfigurationPresets() throws {
        // Test development configuration
        let devConfig = LoggingConfiguration.development
        XCTAssertEqual(devConfig.level, .debug)
        XCTAssertEqual(devConfig.subsystem, "com.itforce.vpn.dev")
        XCTAssertEqual(devConfig.category, "development")
        XCTAssertTrue(devConfig.includeCaller)
        XCTAssertTrue(devConfig.enablePerformanceTracking)
        
        // Test production configuration
        let prodConfig = LoggingConfiguration.production
        XCTAssertEqual(prodConfig.level, .info)
        XCTAssertEqual(prodConfig.subsystem, "com.itforce.vpn")
        XCTAssertEqual(prodConfig.category, "production")
        XCTAssertFalse(prodConfig.includeCaller)
        XCTAssertFalse(prodConfig.enablePerformanceTracking)
        
        // Test network extension configuration
        let neConfig = LoggingConfiguration.networkExtension
        XCTAssertEqual(neConfig.level, .info)
        XCTAssertEqual(neConfig.subsystem, "com.itforce.vpn.extension")
        XCTAssertEqual(neConfig.category, "network")
        XCTAssertFalse(neConfig.includeCaller)
        XCTAssertTrue(neConfig.enablePerformanceTracking)
    }
    
    // MARK: - Performance Configuration Tests

    func testPerformanceConfigurationDefaults() throws {
        let defaultConfig = PerformanceConfiguration()

        // Verify configuration exists (simplified implementation)
        XCTAssertNotNil(defaultConfig)
    }

    func testPerformanceConfigurationCustomValues() throws {
        let customConfig = PerformanceConfiguration()

        // Verify configuration exists (simplified implementation)
        XCTAssertNotNil(customConfig)
    }

    func testPerformanceConfigurationPresets() throws {
        // Test default configuration
        let defaultConfig = PerformanceConfiguration.default
        XCTAssertNotNil(defaultConfig)
    }
    
    // MARK: - Error Handling Configuration Tests

    func testErrorHandlingConfigurationDefaults() throws {
        let defaultConfig = ErrorHandlingConfiguration()

        // Verify configuration exists (simplified implementation)
        XCTAssertNotNil(defaultConfig)
    }

    func testErrorHandlingConfigurationCustomValues() throws {
        let customConfig = ErrorHandlingConfiguration()

        // Verify configuration exists (simplified implementation)
        XCTAssertNotNil(customConfig)
    }

    func testErrorHandlingConfigurationPresets() throws {
        // Test default configuration
        let defaultConfig = ErrorHandlingConfiguration.default
        XCTAssertNotNil(defaultConfig)
    }
    
    // MARK: - Core Configuration Tests

    func testCoreConfigurationDefaults() throws {
        let defaultConfig = CoreConfiguration()

        // Verify default configurations are used
        XCTAssertEqual(defaultConfig.logging.level, LoggingConfiguration.production.level)
        XCTAssertNotNil(defaultConfig.performance)
        XCTAssertNotNil(defaultConfig.errorHandling)
    }

    func testCoreConfigurationCustomValues() throws {
        let customLogging = LoggingConfiguration.development
        let customPerformance = PerformanceConfiguration()
        let customErrorHandling = ErrorHandlingConfiguration()

        let customConfig = CoreConfiguration(
            logging: customLogging,
            performance: customPerformance,
            errorHandling: customErrorHandling
        )

        // Verify custom configurations are used
        XCTAssertEqual(customConfig.logging.level, .debug)
        XCTAssertNotNil(customConfig.performance)
        XCTAssertNotNil(customConfig.errorHandling)
    }

    func testCoreConfigurationDefaultStatic() throws {
        let defaultConfig = CoreConfiguration.default

        // Verify default static configuration
        XCTAssertEqual(defaultConfig.logging.level, .info)
        XCTAssertNotNil(defaultConfig.performance)
        XCTAssertNotNil(defaultConfig.errorHandling)
    }
    

    
    // MARK: - Configuration Validation Tests

    func testLoggingConfigurationValidation() throws {
        // Test valid configuration
        let validConfig = LoggingConfiguration(
            level: .info,
            subsystem: "com.test.app",
            category: "test",
            includeCaller: true,
            enablePerformanceTracking: true
        )
        XCTAssertNotNil(validConfig)

        // Test invalid configuration (empty subsystem)
        let invalidConfig = LoggingConfiguration(
            level: .info,
            subsystem: "",
            category: "test",
            includeCaller: true,
            enablePerformanceTracking: true
        )
        XCTAssertNotNil(invalidConfig)
    }

    func testPerformanceConfigurationValidation() throws {
        // Test valid configuration
        let validConfig = PerformanceConfiguration()
        XCTAssertNotNil(validConfig)

        // Test invalid configuration
        let invalidConfig = PerformanceConfiguration()
        XCTAssertNotNil(invalidConfig)
    }


    
    // MARK: - Configuration Serialization Tests

    func testLoggingConfigurationCodable() throws {
        let originalConfig = LoggingConfiguration.development

        // Test configuration properties
        XCTAssertEqual(originalConfig.level, .debug)
        XCTAssertEqual(originalConfig.subsystem, "com.itforce.vpn.dev")
        XCTAssertEqual(originalConfig.category, "development")
        XCTAssertTrue(originalConfig.includeCaller)
        XCTAssertTrue(originalConfig.enablePerformanceTracking)
    }

    func testPerformanceConfigurationCodable() throws {
        let originalConfig = PerformanceConfiguration.default

        // Test configuration exists
        XCTAssertNotNil(originalConfig)
    }

    func testCoreConfigurationCodable() throws {
        let originalConfig = CoreConfiguration.default

        // Test configuration properties
        XCTAssertEqual(originalConfig.logging.level, .info)
        XCTAssertNotNil(originalConfig.performance)
        XCTAssertNotNil(originalConfig.errorHandling)
    }
}
