/**
 * FILE: InfrastructureIntegrationTests.swift
 *
 * DESCRIPTION:
 *     Integration tests for Infrastructure layer components
 *     Tests component interaction, resource management, and system coordination
 *
 * AUTHOR: wei
 * HISTORY: 01/07/2025 create
 */

import XCTest
@testable import ItForceCore

final class InfrastructureIntegrationTests: XCTestCase {
    
    private var testLogger: TestLogger!
    private var performanceManager: PerformanceManager!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Initialize test logger
        testLogger = TestLogger()
        
        // Initialize performance manager
        performanceManager = PerformanceManager.shared
        await performanceManager.configure(logger: testLogger)
    }
    
    override func tearDown() async throws {
        await performanceManager?.stop()
        testLogger = nil
        performanceManager = nil
        try await super.tearDown()
    }
    
    // MARK: - Core Initialization Tests
    
    func testCoreInitialization() async throws {
        // Test that all core infrastructure components can be initialized together
        let loggingConfig = LoggingConfiguration.development

        // Initialize logging system
        LoggingSystem.shared.configure(with: loggingConfig)
        let logger = LoggingSystem.shared.defaultLogger
        XCTAssertNotNil(logger)

        // Initialize performance monitoring
        await performanceManager.configure(logger: logger)
        try await performanceManager.start()

        // Verify all components are working
        logger.info("Infrastructure initialization test")
        await performanceManager.incrementCounter("test_counter")
        
        // Verify metrics collection
        let metrics = await performanceManager.getCurrentMetrics()
        XCTAssertNotNil(metrics)
        
        await performanceManager.stop()
    }
    
    // MARK: - Component Interaction Tests
    
    func testComponentInteraction() async throws {
        try await performanceManager.start()
        
        // Test logging and performance monitoring interaction
        let logger = LoggingSystem.shared.defaultLogger
        
        // Log messages while monitoring performance
        for i in 0..<10 {
            logger.info("Test message \(i)")
            await performanceManager.incrementCounter("log_messages")
        }
        
        // Test performance tracking
        let _ = await performanceManager.trackOperation("performance_test") {
            return "test_result"
        }
        
        // Verify metrics were collected
        let metrics = await performanceManager.getCurrentMetrics()
        XCTAssertNotNil(metrics)
        
        if let logMessages = metrics?.custom["log_messages"] as? UInt64 {
            XCTAssertEqual(logMessages, 10)
        }
        
        await performanceManager.stop()
    }
    
    // MARK: - Resource Management Tests
    
    func testResourceManagement() async throws {
        // Test buffer pool integration with performance monitoring
        let bufferPoolManager = DefaultBufferPoolManager()
        await bufferPoolManager.configure(logger: testLogger)
        
        try await performanceManager.start()
        
        // Allocate and return buffers while monitoring
        var buffers: [Data] = []
        for _ in 0..<5 {
            let buffer = await bufferPoolManager.getPacketBuffer()
            buffers.append(buffer)
            await performanceManager.incrementCounter("buffer_allocations")
        }
        
        // Return buffers
        for buffer in buffers {
            await bufferPoolManager.returnPacketBuffer(buffer)
            await performanceManager.incrementCounter("buffer_returns")
        }
        
        // Verify statistics
        let poolStats = await bufferPoolManager.getOverallStatistics()
        XCTAssertNotNil(poolStats)
        
        let metrics = await performanceManager.getCurrentMetrics()
        XCTAssertNotNil(metrics)
        
        await bufferPoolManager.shutdown()
        await performanceManager.stop()
    }
    
    // MARK: - Error Propagation Tests
    
    func testErrorPropagation() async throws {
        try await performanceManager.start()
        
        // Test error propagation through different components
        let logger = LoggingSystem.shared.defaultLogger

        // Simulate error in performance monitoring
        do {
            let _ = try await performanceManager.trackOperation("failing_operation") {
                throw TestError.operationFailed
            }
            XCTFail("Operation should have thrown an error")
        } catch TestError.operationFailed {
            // Expected error - log it
            logger.error("Handled performance operation failure")
        }
        
        // Verify error was tracked (check if metrics are available)
        let metrics = await performanceManager.getCurrentMetrics()
        XCTAssertNotNil(metrics, "Metrics should be available")
        // Note: Error count tracking may not be implemented yet, so we just verify metrics exist
        
        await performanceManager.stop()
    }
    
    // MARK: - Memory Management Tests
    
    func testMemoryManagement() async throws {
        try await performanceManager.start()
        
        // Test memory usage tracking
        let initialMetrics = await performanceManager.getCurrentMetrics()
        let initialMemory = initialMetrics?.memory.usedBytes ?? 0
        
        // Allocate some memory through buffer pools
        let bufferPoolManager = DefaultBufferPoolManager()
        await bufferPoolManager.configure(logger: testLogger)
        
        var buffers: [Data] = []
        for _ in 0..<20 {
            let buffer = await bufferPoolManager.getPacketBuffer()
            buffers.append(buffer)
        }
        
        // Wait for metrics collection
        try await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        let afterAllocMetrics = await performanceManager.getCurrentMetrics()
        let afterAllocMemory = afterAllocMetrics?.memory.usedBytes ?? 0
        
        // Memory usage should have increased (though this might be optimized away)
        XCTAssertGreaterThanOrEqual(afterAllocMemory, initialMemory)
        
        // Clean up
        for buffer in buffers {
            await bufferPoolManager.returnPacketBuffer(buffer)
        }
        
        await bufferPoolManager.shutdown()
        await performanceManager.stop()
    }
    
    // MARK: - Shutdown Sequence Tests
    
    func testShutdownSequence() async throws {
        // Test proper shutdown sequence for all components
        try await performanceManager.start()
        
        // Use components
        let logger = LoggingSystem.shared.defaultLogger
        logger.info("Testing shutdown sequence")

        await performanceManager.incrementCounter("shutdown_test")
        
        // Shutdown in proper order
        await performanceManager.stop()
        
        // Verify components are properly shut down
        let finalMetrics = await performanceManager.getCurrentMetrics()
        XCTAssertNotNil(finalMetrics) // Should still be accessible
    }
}

// MARK: - Test Helper Classes

private class TestLogger: LoggerProtocol {
    var lastLoggedMessage: String = ""
    var lastLoggedLevel: LogLevel = .info
    
    func log(level: LogLevel, message: String, fields: [LogField], file: String, line: Int, function: String) {
        lastLoggedLevel = level
        lastLoggedMessage = message
    }
    
    func logDebugInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .debug, message: message, fields: fields, file: file, line: line, function: function)
    }
    
    func logInfoInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .info, message: message, fields: fields, file: file, line: line, function: function)
    }
    
    func logWarningInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .warning, message: message, fields: fields, file: file, line: line, function: function)
    }
    
    func logErrorInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .error, message: message, fields: fields, file: file, line: line, function: function)
    }
    
    func logCriticalInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .critical, message: message, fields: fields, file: file, line: line, function: function)
    }
    
    func withModule(_ module: String) -> LoggerProtocol { return self }
    func withFields(_ fields: [LogField]) -> LoggerProtocol { return self }
    func traceMethodInternal(_ methodName: String, file: String, line: Int, function: String) -> () -> Void { return {} }
    func traceErrorInternal(_ error: Error, message: String, fields: [LogField], file: String, line: Int, function: String) {}
    func setLevel(_ level: LogLevel) {}
    func getLevel() -> LogLevel { return .info }
}

private enum TestError: Error {
    case testError
    case operationFailed
}
