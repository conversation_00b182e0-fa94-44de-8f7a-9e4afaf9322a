/**
 * FILE: AtomicStatisticsPerformanceTests.swift
 *
 * DESCRIPTION:
 *     Performance benchmark tests for AtomicStatisticsManager to verify
 *     high-frequency packet processing performance characteristics.
 *
 * AUTHOR: wei
 * DATE: 2025-01-21
 */

import XCTest
@testable import ItForceCore

/**
 * NAME: AtomicStatisticsPerformanceTests
 *
 * DESCRIPTION:
 *     Performance benchmark test suite for AtomicStatisticsManager.
 *     Measures and validates performance under various load conditions.
 */
final class AtomicStatisticsPerformanceTests: XCTestCase {
    
    private var statisticsManager: AtomicStatisticsManager!
    
    override func setUp() {
        super.setUp()
        statisticsManager = AtomicStatisticsManager()
    }
    
    override func tearDown() {
        statisticsManager.stopPeriodicSync()
        statisticsManager = nil
        super.tearDown()
    }
    
    // MARK: - Single-threaded Performance Tests
    
    /**
     * Benchmark single-threaded TUN statistics updates
     */
    func testTUNStatisticsUpdatePerformance() {
        let operationCount = 100000
        
        measure {
            for _ in 0..<operationCount {
                statisticsManager.updateTUNReceiveStatistics(packets: 1, bytes: 1500)
            }
        }
        
        // Verify operations completed successfully
        let expectation = XCTestExpectation(description: "Operations completion")
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 2.0)
        
        let stats = statisticsManager.getCurrentStatistics()
        XCTAssertGreaterThan(stats.tunPacketsReceived, 0)
        print("TUN Statistics Update Performance: \(operationCount) operations completed")
    }
    
    /**
     * Benchmark single-threaded UDP statistics updates
     */
    func testUDPStatisticsUpdatePerformance() {
        let operationCount = 100000
        
        measure {
            for _ in 0..<operationCount {
                statisticsManager.updateUDPSendStatistics(packets: 1, bytes: 64)
            }
        }
        
        // Verify operations completed successfully
        let expectation = XCTestExpectation(description: "Operations completion")
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 2.0)
        
        let stats = statisticsManager.getCurrentStatistics()
        XCTAssertGreaterThan(stats.udpPacketsSent, 0)
        print("UDP Statistics Update Performance: \(operationCount) operations completed")
    }
    
    /**
     * Benchmark mixed statistics updates (realistic packet processing scenario)
     */
    func testMixedStatisticsUpdatePerformance() {
        let operationCount = 50000
        
        measure {
            for i in 0..<operationCount {
                if i % 2 == 0 {
                    // Simulate TUN packet receive
                    statisticsManager.updateTUNReceiveStatistics(packets: 1, bytes: 1500)
                } else {
                    // Simulate UDP packet send
                    statisticsManager.updateUDPSendStatistics(packets: 1, bytes: 64)
                }
            }
        }
        
        print("Mixed Statistics Update Performance: \(operationCount) operations completed")
    }
    
    // MARK: - Multi-threaded Performance Tests
    
    /**
     * Benchmark concurrent statistics updates from multiple threads
     */
    func testConcurrentStatisticsUpdatePerformance() {
        let threadCount = 8
        let operationsPerThread = 10000
        let totalOperations = threadCount * operationsPerThread
        
        measure {
            let group = DispatchGroup()
            
            for threadIndex in 0..<threadCount {
                DispatchQueue.global(qos: .userInitiated).async(group: group) {
                    for _ in 0..<operationsPerThread {
                        if threadIndex % 2 == 0 {
                            self.statisticsManager.updateTUNReceiveStatistics(packets: 1, bytes: 1500)
                        } else {
                            self.statisticsManager.updateUDPSendStatistics(packets: 1, bytes: 64)
                        }
                    }
                }
            }
            
            group.wait()
        }
        
        print("Concurrent Statistics Update Performance: \(totalOperations) operations across \(threadCount) threads")
    }
    
    /**
     * Benchmark high-frequency packet processing simulation
     */
    func testHighFrequencyPacketProcessingSimulation() {
        let packetsPerSecond = 10000  // Simulate 10K packets/second
        let durationSeconds = 1
        let totalPackets = packetsPerSecond * durationSeconds
        
        measure {
            let startTime = CFAbsoluteTimeGetCurrent()
            var packetCount = 0
            
            while packetCount < totalPackets {
                let currentTime = CFAbsoluteTimeGetCurrent()
                if currentTime - startTime >= Double(durationSeconds) {
                    break
                }
                
                // Simulate realistic packet processing
                statisticsManager.updateTUNReceiveStatistics(packets: 1, bytes: 1500)
                statisticsManager.updateUDPSendStatistics(packets: 1, bytes: 64)
                packetCount += 1
            }
        }
        
        print("High-frequency packet processing simulation: \(totalPackets) packets processed")
    }
    
    // MARK: - Memory Performance Tests
    
    /**
     * Test memory usage under sustained load
     */
    func testMemoryUsageUnderLoad() {
        let operationCount = 1000000  // 1 million operations
        
        // Measure memory before
        let memoryBefore = getMemoryUsage()
        
        // Perform sustained operations
        for i in 0..<operationCount {
            if i % 4 == 0 {
                statisticsManager.updateTUNReceiveStatistics(packets: 1, bytes: 1500)
            } else if i % 4 == 1 {
                statisticsManager.updateTUNSendStatistics(packets: 1, bytes: 1400)
            } else if i % 4 == 2 {
                statisticsManager.updateUDPReceiveStatistics(packets: 1, bytes: 64)
            } else {
                statisticsManager.updateUDPSendStatistics(packets: 1, bytes: 60)
            }
        }
        
        // Allow operations to complete
        let expectation = XCTestExpectation(description: "Memory test completion")
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 5.0)
        
        // Measure memory after
        let memoryAfter = getMemoryUsage()
        let memoryIncrease = memoryAfter - memoryBefore
        
        print("Memory usage test:")
        print("Operations: \(operationCount)")
        print("Memory before: \(memoryBefore) MB")
        print("Memory after: \(memoryAfter) MB")
        print("Memory increase: \(memoryIncrease) MB")
        
        // Memory increase should be minimal (less than 10MB for 1M operations)
        XCTAssertLessThan(memoryIncrease, 10.0, "Memory increase should be minimal")
    }
    
    // MARK: - Comparison Tests
    
    /**
     * Compare performance with traditional synchronous approach
     */
    func testPerformanceComparisonWithSynchronousApproach() {
        let operationCount = 10000
        
        // Test atomic statistics manager
        let atomicStartTime = CFAbsoluteTimeGetCurrent()
        for _ in 0..<operationCount {
            statisticsManager.updateUDPSendStatistics(packets: 1, bytes: 64)
        }
        let atomicEndTime = CFAbsoluteTimeGetCurrent()
        let atomicTime = atomicEndTime - atomicStartTime
        
        // Test traditional synchronous approach (simulated)
        var syncCounter: UInt64 = 0
        let syncQueue = DispatchQueue(label: "sync.test", qos: .utility)
        
        let syncStartTime = CFAbsoluteTimeGetCurrent()
        for _ in 0..<operationCount {
            syncQueue.sync {
                syncCounter += 1
            }
        }
        let syncEndTime = CFAbsoluteTimeGetCurrent()
        let syncTime = syncEndTime - syncStartTime
        
        let performanceImprovement = syncTime / atomicTime
        
        print("Performance comparison:")
        print("Atomic approach: \(atomicTime * 1000)ms")
        print("Synchronous approach: \(syncTime * 1000)ms")
        print("Performance improvement: \(performanceImprovement)x")
        
        // Atomic approach should be significantly faster
        XCTAssertGreaterThan(performanceImprovement, 2.0, "Atomic approach should be at least 2x faster")
    }
    
    // MARK: - Helper Methods
    
    /**
     * Get current memory usage in MB
     */
    private func getMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        guard result == KERN_SUCCESS else {
            return 0.0
        }
        
        return Double(info.resident_size) / 1024.0 / 1024.0  // Convert to MB
    }
}
