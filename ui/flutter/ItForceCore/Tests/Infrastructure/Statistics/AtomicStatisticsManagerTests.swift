/**
 * FILE: AtomicStatisticsManagerTests.swift
 *
 * DESCRIPTION:
 *     Unit tests for AtomicStatisticsManager to verify thread-safe statistics operations
 *     and performance characteristics for iOS network packet processing.
 *
 * AUTHOR: wei
 * DATE: 2025-01-21
 */

import XCTest
@testable import ItForceCore

/**
 * NAME: AtomicStatisticsManagerTests
 *
 * DESCRIPTION:
 *     Test suite for AtomicStatisticsManager functionality including:
 *     - Thread-safe atomic operations
 *     - Performance characteristics
 *     - App Group synchronization
 *     - Statistics accuracy under high load
 */
final class AtomicStatisticsManagerTests: XCTestCase {
    
    private var statisticsManager: AtomicStatisticsManager!
    
    override func setUp() {
        super.setUp()
        statisticsManager = AtomicStatisticsManager()
    }
    
    override func tearDown() {
        statisticsManager.stopPeriodicSync()
        statisticsManager = nil
        super.tearDown()
    }
    
    // MARK: - Basic Functionality Tests
    
    /**
     * Test basic TUN interface statistics updates
     */
    func testTUNStatisticsUpdates() {
        // Test TUN receive statistics
        statisticsManager.updateTUNReceiveStatistics(packets: 10, bytes: 1500)
        statisticsManager.updateTUNReceiveStatistics(packets: 5, bytes: 750)
        
        // Test TUN send statistics
        statisticsManager.updateTUNSendStatistics(packets: 8, bytes: 1200)
        statisticsManager.updateTUNSendStatistics(packets: 3, bytes: 450)
        
        // Test error statistics
        statisticsManager.updateTUNErrorStatistics(errors: 2, dropped: 1)
        
        // Allow time for async operations to complete
        let expectation = XCTestExpectation(description: "Statistics update")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        // Verify statistics
        let stats = statisticsManager.getCurrentStatistics()
        XCTAssertEqual(stats.tunPacketsReceived, 15)
        XCTAssertEqual(stats.tunBytesReceived, 2250)
        XCTAssertEqual(stats.tunPacketsSent, 11)
        XCTAssertEqual(stats.tunBytesSent, 1650)
        XCTAssertEqual(stats.tunErrors, 2)
        XCTAssertEqual(stats.tunDropped, 1)
    }
    
    /**
     * Test basic UDP protocol statistics updates
     */
    func testUDPStatisticsUpdates() {
        // Test UDP receive statistics
        statisticsManager.updateUDPReceiveStatistics(packets: 20, bytes: 3000)
        statisticsManager.updateUDPReceiveStatistics(packets: 15, bytes: 2250)
        
        // Test UDP send statistics
        statisticsManager.updateUDPSendStatistics(packets: 18, bytes: 2700)
        statisticsManager.updateUDPSendStatistics(packets: 12, bytes: 1800)
        
        // Allow time for async operations to complete
        let expectation = XCTestExpectation(description: "UDP statistics update")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        // Verify statistics
        let stats = statisticsManager.getCurrentStatistics()
        XCTAssertEqual(stats.udpPacketsReceived, 35)
        XCTAssertEqual(stats.udpBytesReceived, 5250)
        XCTAssertEqual(stats.udpPacketsSent, 30)
        XCTAssertEqual(stats.udpBytesSent, 4500)
    }
    
    /**
     * Test statistics reset functionality
     */
    func testStatisticsReset() {
        // Add some statistics
        statisticsManager.updateTUNReceiveStatistics(packets: 10, bytes: 1500)
        statisticsManager.updateUDPSendStatistics(packets: 5, bytes: 750)
        
        // Allow time for async operations
        let updateExpectation = XCTestExpectation(description: "Statistics update")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            updateExpectation.fulfill()
        }
        wait(for: [updateExpectation], timeout: 1.0)
        
        // Verify statistics are not zero
        var stats = statisticsManager.getCurrentStatistics()
        XCTAssertGreaterThan(stats.tunPacketsReceived, 0)
        XCTAssertGreaterThan(stats.udpPacketsSent, 0)
        
        // Reset statistics
        statisticsManager.resetStatistics()
        
        // Verify statistics are reset to zero
        stats = statisticsManager.getCurrentStatistics()
        XCTAssertEqual(stats.tunBytesReceived, 0)
        XCTAssertEqual(stats.tunBytesSent, 0)
        XCTAssertEqual(stats.tunPacketsReceived, 0)
        XCTAssertEqual(stats.tunPacketsSent, 0)
        XCTAssertEqual(stats.udpBytesReceived, 0)
        XCTAssertEqual(stats.udpBytesSent, 0)
        XCTAssertEqual(stats.udpPacketsReceived, 0)
        XCTAssertEqual(stats.udpPacketsSent, 0)
    }
    
    // MARK: - Concurrency Tests
    
    /**
     * Test thread safety under concurrent access
     */
    func testConcurrentStatisticsUpdates() {
        let concurrentOperations = 1000
        let expectation = XCTestExpectation(description: "Concurrent operations")
        expectation.expectedFulfillmentCount = concurrentOperations
        
        // Perform concurrent updates from multiple threads
        DispatchQueue.concurrentPerform(iterations: concurrentOperations) { iteration in
            // Simulate high-frequency packet processing
            statisticsManager.updateTUNReceiveStatistics(packets: 1, bytes: 100)
            statisticsManager.updateUDPSendStatistics(packets: 1, bytes: 150)
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 10.0)
        
        // Allow time for all async operations to complete
        let completionExpectation = XCTestExpectation(description: "Operations completion")
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            completionExpectation.fulfill()
        }
        wait(for: [completionExpectation], timeout: 2.0)
        
        // Verify final statistics
        let stats = statisticsManager.getCurrentStatistics()
        XCTAssertEqual(stats.tunPacketsReceived, UInt64(concurrentOperations))
        XCTAssertEqual(stats.tunBytesReceived, UInt64(concurrentOperations * 100))
        XCTAssertEqual(stats.udpPacketsSent, UInt64(concurrentOperations))
        XCTAssertEqual(stats.udpBytesSent, UInt64(concurrentOperations * 150))
    }
    
    // MARK: - Performance Tests
    
    /**
     * Test performance of atomic operations under high load
     */
    func testPerformanceUnderHighLoad() {
        let operationCount = 10000
        
        // Measure time for high-frequency updates
        let startTime = CFAbsoluteTimeGetCurrent()
        
        for _ in 0..<operationCount {
            statisticsManager.updateUDPSendStatistics(packets: 1, bytes: 64)
        }
        
        let endTime = CFAbsoluteTimeGetCurrent()
        let totalTime = endTime - startTime
        let averageTimePerOperation = totalTime / Double(operationCount)
        
        // Verify performance is within acceptable limits
        // Each operation should take less than 10μs on average
        XCTAssertLessThan(averageTimePerOperation, 0.00001, "Average operation time should be less than 10μs")
        
        print("Performance test results:")
        print("Total operations: \(operationCount)")
        print("Total time: \(totalTime * 1000)ms")
        print("Average time per operation: \(averageTimePerOperation * 1000000)μs")
    }
    
    /**
     * Test periodic sync functionality
     */
    func testPeriodicSync() {
        // Start periodic sync
        statisticsManager.startPeriodicSync()
        
        // Add some statistics
        statisticsManager.updateTUNReceiveStatistics(packets: 100, bytes: 15000)
        statisticsManager.updateUDPSendStatistics(packets: 50, bytes: 7500)
        
        // Wait for at least one sync cycle (2+ seconds)
        let expectation = XCTestExpectation(description: "Periodic sync")
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 5.0)
        
        // Verify statistics are still accurate after sync
        let stats = statisticsManager.getCurrentStatistics()
        XCTAssertGreaterThan(stats.tunPacketsReceived, 0)
        XCTAssertGreaterThan(stats.udpPacketsSent, 0)
        
        // Stop periodic sync
        statisticsManager.stopPeriodicSync()
    }
    
    // MARK: - Edge Cases
    
    /**
     * Test handling of zero values
     */
    func testZeroValues() {
        statisticsManager.updateTUNReceiveStatistics(packets: 0, bytes: 0)
        statisticsManager.updateUDPSendStatistics(packets: 0, bytes: 0)
        statisticsManager.updateTUNErrorStatistics(errors: 0, dropped: 0)
        
        let stats = statisticsManager.getCurrentStatistics()
        XCTAssertEqual(stats.tunPacketsReceived, 0)
        XCTAssertEqual(stats.tunBytesReceived, 0)
        XCTAssertEqual(stats.udpPacketsSent, 0)
        XCTAssertEqual(stats.udpBytesSent, 0)
    }
    
    /**
     * Test handling of large values
     */
    func testLargeValues() {
        let largePacketCount: UInt64 = UInt64.max / 2
        let largeByteCount: UInt64 = UInt64.max / 2
        
        statisticsManager.updateTUNReceiveStatistics(packets: largePacketCount, bytes: largeByteCount)
        
        // Allow time for async operation
        let expectation = XCTestExpectation(description: "Large value update")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        let stats = statisticsManager.getCurrentStatistics()
        XCTAssertEqual(stats.tunPacketsReceived, largePacketCount)
        XCTAssertEqual(stats.tunBytesReceived, largeByteCount)
    }
}
