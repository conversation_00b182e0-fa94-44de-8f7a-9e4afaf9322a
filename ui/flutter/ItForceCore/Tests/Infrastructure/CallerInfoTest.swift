/**
 * FILE: CallerInfoTest.swift
 *
 * DESCRIPTION:
 *     Test to verify that caller information is correctly captured in logging
 *
 * AUTHOR: wei
 * HISTORY: 30/06/2025 create - Fix caller information capture in logging
 */

import XCTest
@testable import ItForceCore

final class CallerInfoTest: XCTestCase {
    
    func testCallerInfoCapture() throws {
        // Create a test logger that captures caller information
        let testLogger = CallerInfoTestLogger()
        
        // Call from this specific line - remember this line number!
        testLogger.debug("Test debug message")  // This should show CallerInfoTest.swift and this line
        
        // Verify the captured information
        XCTAssertEqual(testLogger.capturedFile, "CallerInfoTest.swift", "File name should be CallerInfoTest.swift")
        XCTAssertEqual(testLogger.capturedFunction, "testCallerInfoCapture()", "Function should be testCallerInfoCapture()")
        XCTAssertEqual(testLogger.capturedLine, 21, "Line number should be 21 (the line where debug was called)")
        
        // Test with metadata
        testLogger.info("Test info message", metadata: ["key": "value"])
        XCTAssertEqual(testLogger.capturedFile, "CallerInfoTest.swift")
        XCTAssertEqual(testLogger.capturedFunction, "testCallerInfoCapture()")
        XCTAssertEqual(testLogger.capturedLine, 29, "Line number should be 29")
        
        // Test with fields
        testLogger.warning("Test warning", fields: LogField.string("test", "value"))
        XCTAssertEqual(testLogger.capturedFile, "CallerInfoTest.swift")
        XCTAssertEqual(testLogger.capturedFunction, "testCallerInfoCapture()")
        XCTAssertEqual(testLogger.capturedLine, 35, "Line number should be 35")
    }
    
    func testCallerInfoFromDifferentMethod() throws {
        let testLogger = CallerInfoTestLogger()
        
        // Call from a helper method
        callLoggerFromHelper(testLogger)
        
        // Should show the helper method as caller, not this method
        XCTAssertEqual(testLogger.capturedFile, "CallerInfoTest.swift")
        XCTAssertEqual(testLogger.capturedFunction, "callLoggerFromHelper(_:)")
        XCTAssertEqual(testLogger.capturedLine, 54, "Should show line from helper method")
    }
    
    private func callLoggerFromHelper(_ logger: LoggerProtocol) {
        logger.error("Error from helper method")  // Line 44
    }
}

// MARK: - Test Helper

/**
 * Test logger implementation that captures caller information for verification
 */
private class CallerInfoTestLogger: LoggerProtocol {
    var capturedFile: String = ""
    var capturedLine: Int = 0
    var capturedFunction: String = ""
    var capturedMessage: String = ""
    var capturedLevel: LogLevel = .info
    
    func log(level: LogLevel, message: String, fields: [LogField], file: String, line: Int, function: String) {
        capturedLevel = level
        capturedMessage = message
        capturedFile = (file as NSString).lastPathComponent
        capturedLine = line
        capturedFunction = function
        
        // Print for manual verification during development
        print("📍 Captured: \(capturedFile):\(capturedLine) \(capturedFunction) - \(message)")
    }
    
    func debug(_ message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {
        log(level: .debug, message: message, fields: Array(fields), file: file, line: line, function: function)
    }
    
    func info(_ message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {
        log(level: .info, message: message, fields: Array(fields), file: file, line: line, function: function)
    }
    
    func warning(_ message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {
        log(level: .warning, message: message, fields: Array(fields), file: file, line: line, function: function)
    }
    
    func error(_ message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {
        log(level: .error, message: message, fields: Array(fields), file: file, line: line, function: function)
    }
    
    func critical(_ message: String, fields: LogField..., file: String = #file, line: Int = #line, function: String = #function) {
        log(level: .critical, message: message, fields: Array(fields), file: file, line: line, function: function)
    }
    
    func logDebugInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .debug, message: message, fields: fields, file: file, line: line, function: function)
    }

    func logInfoInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .info, message: message, fields: fields, file: file, line: line, function: function)
    }

    func logWarningInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .warning, message: message, fields: fields, file: file, line: line, function: function)
    }

    func logErrorInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .error, message: message, fields: fields, file: file, line: line, function: function)
    }

    func logCriticalInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .critical, message: message, fields: fields, file: file, line: line, function: function)
    }

    func withModule(_ module: String) -> LoggerProtocol { return self }
    func withFields(_ fields: [LogField]) -> LoggerProtocol { return self }
    func traceMethodInternal(_ methodName: String, file: String, line: Int, function: String) -> () -> Void { return {} }
    func traceErrorInternal(_ error: Error, message: String, fields: [LogField], file: String, line: Int, function: String) {}
    func setLevel(_ level: LogLevel) {}
    func getLevel() -> LogLevel { return .debug }
}
