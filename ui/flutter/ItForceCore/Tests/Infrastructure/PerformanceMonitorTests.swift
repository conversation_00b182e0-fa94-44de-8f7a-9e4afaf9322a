/**
 * FILE: PerformanceMonitorTests.swift
 *
 * DESCRIPTION:
 *     Unit tests for performance monitoring infrastructure
 *     Tests metrics collection, actor thread safety, and performance tracking
 *
 * AUTHOR: wei
 * HISTORY: 01/07/2025 create
 */

import XCTest
@testable import ItForceCore

final class PerformanceMonitorTests: XCTestCase {

    private var performanceMonitor: DefaultPerformanceMonitor!
    private var testLogger: TestLogger!

    override func setUp() async throws {
        try await super.setUp()

        // Create test logger
        testLogger = TestLogger()

        // Initialize performance monitor with test configuration
        performanceMonitor = DefaultPerformanceMonitor(
            logger: testLogger,
            collectionInterval: 0.1 // Fast collection for testing
        )
    }
    
    override func tearDown() async throws {
        if performanceMonitor != nil {
            await performanceMonitor.stop()
            performanceMonitor = nil
        }
        testLogger = nil
        try await super.tearDown()
    }
    
    // MARK: - Performance Monitor Lifecycle Tests
    
    func testPerformanceMonitorInitialization() async throws {
        // Verify initial state
        let initialMetrics = await performanceMonitor.getCurrentMetrics()
        XCTAssertNotNil(initialMetrics)
        XCTAssertEqual(initialMetrics.uptime, 0, accuracy: 0.1)
    }
    
    func testPerformanceMonitorStartStop() async throws {
        // Start monitoring
        try await performanceMonitor.start()
        
        // Wait a bit for metrics collection
        try await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Verify monitoring is active
        let metrics = await performanceMonitor.getCurrentMetrics()
        XCTAssertGreaterThan(metrics.uptime, 0)
        
        // Stop monitoring
        await performanceMonitor.stop()
        
        // Verify monitoring stopped
        // (Additional verification would depend on implementation details)
    }
    
    // MARK: - Metrics Collection Tests
    
    func testMemoryMetricsCollection() async throws {
        try await performanceMonitor.start()
        
        // Wait for metrics collection
        try await Task.sleep(nanoseconds: 150_000_000) // 0.15 seconds
        
        let metrics = await performanceMonitor.getCurrentMetrics()
        
        // Verify memory metrics are collected
        XCTAssertGreaterThan(metrics.memory.usedBytes, 0)
        XCTAssertGreaterThanOrEqual(metrics.memory.usagePercentage, 0)
        XCTAssertLessThanOrEqual(metrics.memory.usagePercentage, 100)
        XCTAssertGreaterThan(metrics.memory.availableBytes, 0)
        
        await performanceMonitor.stop()
    }
    
    func testNetworkMetricsCollection() async throws {
        try await performanceMonitor.start()
        
        // Wait for metrics collection
        try await Task.sleep(nanoseconds: 150_000_000) // 0.15 seconds
        
        let metrics = await performanceMonitor.getCurrentMetrics()
        
        // Verify network metrics are collected
        XCTAssertGreaterThanOrEqual(metrics.network.bytesReceived, 0)
        XCTAssertGreaterThanOrEqual(metrics.network.bytesSent, 0)
        XCTAssertGreaterThanOrEqual(metrics.network.packetsReceived, 0)
        XCTAssertGreaterThanOrEqual(metrics.network.packetsSent, 0)
        
        await performanceMonitor.stop()
    }
    
    func testApplicationMetricsCollection() async throws {
        try await performanceMonitor.start()

        // Wait for metrics collection
        try await Task.sleep(nanoseconds: 150_000_000) // 0.15 seconds

        let metrics = await performanceMonitor.getCurrentMetrics()

        // Verify application metrics are collected
        XCTAssertGreaterThanOrEqual(metrics.application.requestCount, 0)
        XCTAssertGreaterThanOrEqual(metrics.application.errorCount, 0)
        XCTAssertGreaterThanOrEqual(metrics.application.errorRate, 0)

        await performanceMonitor.stop()
    }
    
    // MARK: - Custom Metrics Tests
    
    func testCustomCounterMetrics() async throws {
        try await performanceMonitor.start()
        
        // Increment custom counters
        await performanceMonitor.incrementCounter("test_counter", by: 5)
        await performanceMonitor.incrementCounter("test_counter", by: 3)
        await performanceMonitor.incrementCounter("another_counter", by: 1)
        
        // Wait for metrics collection
        try await Task.sleep(nanoseconds: 150_000_000) // 0.15 seconds
        
        let metrics = await performanceMonitor.getCurrentMetrics()
        
        // Verify custom counters
        if let testCounterValue = metrics.custom["test_counter"] as? UInt64 {
            XCTAssertEqual(testCounterValue, 8)
        } else {
            XCTFail("test_counter should be present in custom metrics")
        }
        
        if let anotherCounterValue = metrics.custom["another_counter"] as? UInt64 {
            XCTAssertEqual(anotherCounterValue, 1)
        } else {
            XCTFail("another_counter should be present in custom metrics")
        }
        
        await performanceMonitor.stop()
    }
    
    func testCustomGaugeMetrics() async throws {
        try await performanceMonitor.start()
        
        // Set custom gauges
        await performanceMonitor.setGauge("temperature", value: 25.5)
        await performanceMonitor.setGauge("connection_count", value: 42)
        await performanceMonitor.setGauge("status", value: "active")
        
        // Wait for metrics collection
        try await Task.sleep(nanoseconds: 150_000_000) // 0.15 seconds
        
        let metrics = await performanceMonitor.getCurrentMetrics()
        
        // Verify custom gauges
        if let temperature = metrics.custom["temperature"] as? Double {
            XCTAssertEqual(temperature, 25.5, accuracy: 0.01)
        } else {
            XCTFail("temperature should be present in custom metrics")
        }
        
        if let connectionCount = metrics.custom["connection_count"] as? Int {
            XCTAssertEqual(connectionCount, 42)
        } else {
            XCTFail("connection_count should be present in custom metrics")
        }
        
        if let status = metrics.custom["status"] as? String {
            XCTAssertEqual(status, "active")
        } else {
            XCTFail("status should be present in custom metrics")
        }
        
        await performanceMonitor.stop()
    }
    
    // MARK: - Metric Collector Tests
    
    func testMetricCollectorRegistration() async throws {
        let testCollector = TestMetricCollector()
        
        // Register collector
        await performanceMonitor.registerCollector(testCollector)
        
        try await performanceMonitor.start()
        
        // Wait for metrics collection
        try await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        let metrics = await performanceMonitor.getCurrentMetrics()
        
        // Verify custom collector metrics are included (if supported)
        if let testMetric = metrics.custom["test_metric"] as? String {
            XCTAssertEqual(testMetric, "test_value")
        } else {
            // Custom metric collection may not be fully implemented yet
            // Just verify that metrics collection is working
            XCTAssertNotNil(metrics, "Metrics should be available")
        }
        
        await performanceMonitor.stop()
        
        // Remove collector
        await performanceMonitor.removeCollector(name: "TestCollector")
    }
    
    // MARK: - Performance Tracking Tests
    
    func testOperationTracking() async throws {
        try await performanceMonitor.start()

        // Track an operation
        let result = try await performanceMonitor.trackOperation("test_operation") {
            // Simulate some work
            try await Task.sleep(nanoseconds: 50_000_000) // 0.05 seconds
            return "operation_result"
        }

        // Verify operation result
        XCTAssertEqual(result, "operation_result")

        // Wait for metrics collection
        try await Task.sleep(nanoseconds: 150_000_000) // 0.15 seconds

        let metrics = await performanceMonitor.getCurrentMetrics()

        // Verify operation was tracked (implementation-dependent)
        XCTAssertGreaterThan(metrics.application.requestCount, 0)

        await performanceMonitor.stop()
    }
    
    func testOperationTrackingWithError() async throws {
        try await performanceMonitor.start()

        // Track an operation that throws
        do {
            let _ = try await performanceMonitor.trackOperation("failing_operation") {
                throw TestError.operationFailed
            }
            XCTFail("Operation should have thrown an error")
        } catch TestError.operationFailed {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }

        // Wait for metrics collection
        try await Task.sleep(nanoseconds: 150_000_000) // 0.15 seconds

        let metrics = await performanceMonitor.getCurrentMetrics()

        // Verify error was tracked
        XCTAssertGreaterThan(metrics.application.errorCount, 0)

        await performanceMonitor.stop()
    }
    
    // MARK: - Actor Thread Safety Tests
    
    func testConcurrentMetricsAccess() async throws {
        try await performanceMonitor.start()
        
        // Create multiple concurrent tasks accessing metrics
        let tasks = (0..<10).map { index in
            Task {
                await performanceMonitor.incrementCounter("concurrent_counter", by: 1)
                await performanceMonitor.setGauge("concurrent_gauge_\(index)", value: index)
                return await performanceMonitor.getCurrentMetrics()
            }
        }
        
        // Wait for all tasks to complete
        let results = await withTaskGroup(of: PerformanceMetrics.self) { group in
            for task in tasks {
                group.addTask { await task.value }
            }
            
            var metrics: [PerformanceMetrics] = []
            for await result in group {
                metrics.append(result)
            }
            return metrics
        }
        
        // Verify all tasks completed successfully
        XCTAssertEqual(results.count, 10)
        
        // Wait for final metrics collection
        try await Task.sleep(nanoseconds: 150_000_000) // 0.15 seconds
        
        let finalMetrics = await performanceMonitor.getCurrentMetrics()
        
        // Verify concurrent counter was properly incremented
        if let concurrentCounter = finalMetrics.custom["concurrent_counter"] as? UInt64 {
            XCTAssertEqual(concurrentCounter, 10)
        } else {
            XCTFail("concurrent_counter should be present and equal to 10")
        }
        
        await performanceMonitor.stop()
    }
    
    // MARK: - Performance Benchmark Tests
    
    func testMetricsCollectionPerformance() async throws {
        try await performanceMonitor.start()
        
        let startTime = Date()
        
        // Collect metrics multiple times
        for _ in 0..<100 {
            let _ = await performanceMonitor.getCurrentMetrics()
        }
        
        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        
        // Verify metrics collection is fast (should be under 1 second for 100 calls)
        XCTAssertLessThan(duration, 1.0, "Metrics collection should be fast")
        
        await performanceMonitor.stop()
    }
}

// MARK: - Test Helper Classes

private class TestLogger: LoggerProtocol {
    var lastLoggedMessage: String = ""
    var lastLoggedLevel: LogLevel = .info

    func log(level: LogLevel, message: String, fields: [LogField], file: String, line: Int, function: String) {
        lastLoggedLevel = level
        lastLoggedMessage = message
    }

    func logDebugInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .debug, message: message, fields: fields, file: file, line: line, function: function)
    }

    func logInfoInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .info, message: message, fields: fields, file: file, line: line, function: function)
    }

    func logWarningInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .warning, message: message, fields: fields, file: file, line: line, function: function)
    }

    func logErrorInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .error, message: message, fields: fields, file: file, line: line, function: function)
    }

    func logCriticalInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .critical, message: message, fields: fields, file: file, line: line, function: function)
    }

    func withModule(_ module: String) -> LoggerProtocol { return self }
    func withFields(_ fields: [LogField]) -> LoggerProtocol { return self }
    func traceMethodInternal(_ methodName: String, file: String, line: Int, function: String) -> () -> Void { return {} }
    func traceErrorInternal(_ error: Error, message: String, fields: [LogField], file: String, line: Int, function: String) {}
    func setLevel(_ level: LogLevel) {}
    func getLevel() -> LogLevel { return .info }
}

private class TestMetricCollector: MetricCollectorProtocol {
    let name = "TestCollector"

    func collect() async -> [String: Any] {
        return ["test_metric": "test_value"]
    }
}

private enum TestError: Error {
    case operationFailed
}
