/**
 * FILE: PacketParserTests.swift
 *
 * DESCRIPTION:
 *     Unit tests for PacketParser class and packet parsing.
 *     Verifies packet parsing compatibility with Go backend implementation.
 *
 * AUTHOR: wei
 * HISTORY: 26/06/2025 create
 */

import XCTest
import CryptoKit
@testable import ItForceCore

class PacketParserTests: XCTestCase {
    
    var packetParser: PacketParser!
    var packetBuilder: PacketBuilder!
    
    override func setUp() {
        super.setUp()
        packetParser = PacketParser()
        packetBuilder = PacketBuilder()
    }
    
    override func tearDown() {
        packetParser = nil
        packetBuilder = nil
        super.tearDown()
    }
    
    // MARK: - Header Parsing Tests
    
    func testParseHeader() throws {
        // Create test header data
        let data = Data([
            0x13, // PacketType.open
            0x02, // EncryptionMethod.aes
            0x12, 0x34, // SessionID = 0x1234
            0x56, 0x78, 0x9A, 0xBC // Token = 0x56789ABC
        ])
        
        let header = try packetParser.parseHeader(from: data)
        
        XCTAssertEqual(header.type, .open)
        XCTAssertEqual(header.encrypt, .aes)
        XCTAssertEqual(header.sessionID, 0x1234)
        XCTAssertEqual(header.token, 0x56789ABC)
    }
    
    func testParseHeaderInsufficientData() {
        let shortData = Data([0x13, 0x02, 0x12]) // Only 3 bytes
        
        XCTAssertThrowsError(try packetParser.parseHeader(from: shortData)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidHeaderSize)
        }
    }
    
    func testParseHeaderInvalidPacketType() {
        let data = Data([
            0xFF, // Invalid packet type
            0x02, // EncryptionMethod.aes
            0x12, 0x34, // SessionID
            0x56, 0x78, 0x9A, 0xBC // Token
        ])
        
        XCTAssertThrowsError(try packetParser.parseHeader(from: data)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidPacketType)
        }
    }
    
    // MARK: - Data Packet Parsing Tests
    
    func testParseDataPacket() throws {
        // Create data packet: Header + IP payload
        let header = PacketHeader(type: .data, encrypt: .none, sessionID: 1, token: 123)
        let payload = "IP packet data".data(using: .utf8)!
        let packetData = try packetBuilder.buildDataPacket(header: header, payload: payload)
        
        let parsedPacket = try packetParser.parsePacket(from: packetData)
        
        XCTAssertEqual(parsedPacket.header.type, .data)
        XCTAssertEqual(parsedPacket.header.encrypt, .none)
        XCTAssertEqual(parsedPacket.header.sessionID, 1)
        XCTAssertEqual(parsedPacket.header.token, 123)
        XCTAssertEqual(parsedPacket.payload, payload)
        XCTAssertNil(parsedPacket.signature) // Data packets don't have signatures
        XCTAssertTrue(parsedPacket.attributes.isEmpty) // Data packets don't have attributes
    }
    
    func testParseDataPacketEmpty() throws {
        let header = PacketHeader(type: .data, encrypt: .none, sessionID: 1, token: 123)
        let emptyPayload = Data()
        let packetData = try packetBuilder.buildDataPacket(header: header, payload: emptyPayload)
        
        let parsedPacket = try packetParser.parsePacket(from: packetData)
        
        XCTAssertEqual(parsedPacket.header.type, .data)
        XCTAssertEqual(parsedPacket.payload, emptyPayload)
    }
    
    func testParseDataPacketLarge() throws {
        let header = PacketHeader(type: .data, encrypt: .aes, sessionID: 1, token: 123)
        let largePayload = Data(repeating: 0x42, count: 1500)
        let packetData = try packetBuilder.buildDataPacket(header: header, payload: largePayload)
        
        let parsedPacket = try packetParser.parsePacket(from: packetData)
        
        XCTAssertEqual(parsedPacket.header.type, .data)
        XCTAssertEqual(parsedPacket.payload, largePayload)
    }
    
    // MARK: - Control Packet Parsing Tests
    
    func testParseControlPacketWithSignature() throws {
        let header = PacketHeader(type: .echoRequest, encrypt: .aes, sessionID: 1, token: 123)
        let attributes = [TLVAttribute(mtu: 1420)]
        let packetData = try packetBuilder.buildPacketWithSignature(header: header, attributes: attributes)
        
        let parsedPacket = try packetParser.parsePacket(from: packetData)
        
        XCTAssertEqual(parsedPacket.header.type, .echoRequest)
        XCTAssertEqual(parsedPacket.header.encrypt, .aes)
        XCTAssertEqual(parsedPacket.header.sessionID, 1)
        XCTAssertEqual(parsedPacket.header.token, 123)
        XCTAssertNotNil(parsedPacket.signature)
        XCTAssertEqual(parsedPacket.signature?.count, 16) // MD5 signature
        XCTAssertEqual(parsedPacket.attributes.count, 1)
        XCTAssertEqual(parsedPacket.attributes[0].type, .mtu)
    }
    
    func testParseControlPacketNoAttributes() throws {
        let header = PacketHeader(type: .echoRequest, encrypt: .aes, sessionID: 1, token: 123)
        let packetData = try packetBuilder.buildPacketWithSignature(header: header, attributes: [])
        
        let parsedPacket = try packetParser.parsePacket(from: packetData)
        
        XCTAssertEqual(parsedPacket.header.type, .echoRequest)
        XCTAssertNotNil(parsedPacket.signature)
        XCTAssertTrue(parsedPacket.attributes.isEmpty)
        XCTAssertEqual(parsedPacket.payload.count, 0) // No payload after signature
    }
    
    func testParseControlPacketInsufficientDataForSignature() {
        // Create packet with header but insufficient data for signature
        let headerData = Data([
            0x15, // PacketType.echoRequest
            0x02, // EncryptionMethod.aes
            0x00, 0x01, // SessionID = 1
            0x00, 0x00, 0x00, 0x7B, // Token = 123
            0x01, 0x02, 0x03 // Only 3 bytes instead of 16 for signature
        ])
        
        XCTAssertThrowsError(try packetParser.parsePacket(from: headerData)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidPacketFormat)
        }
    }
    
    // MARK: - OPEN Packet Parsing Tests
    
    func testParseOpenPacket() throws {
        let username = "testuser"
        let password = "testpass"
        let mtu: UInt16 = 1420
        let encryptionMethod = EncryptionMethod.aes
        
        let packetData = try packetBuilder.buildOpenPacket(
            username: username,
            password: password,
            mtu: mtu,
            encryptionMethod: encryptionMethod
        )
        
        let parsedPacket = try packetParser.parsePacket(from: packetData)
        
        XCTAssertEqual(parsedPacket.header.type, .open)
        XCTAssertEqual(parsedPacket.header.encrypt, encryptionMethod)
        XCTAssertEqual(parsedPacket.header.sessionID, 0)
        XCTAssertEqual(parsedPacket.header.token, 0)
        XCTAssertNotNil(parsedPacket.signature)
        XCTAssertGreaterThan(parsedPacket.attributes.count, 0)
        
        // Verify attributes are parsed correctly
        let mtuAttribute = parsedPacket.attributes.first { $0.type == .mtu }
        XCTAssertNotNil(mtuAttribute)
        
        let usernameAttribute = parsedPacket.attributes.first { $0.type == .username }
        XCTAssertNotNil(usernameAttribute)
        
        let passwordAttribute = parsedPacket.attributes.first { $0.type == .password }
        XCTAssertNotNil(passwordAttribute)
    }
    
    // MARK: - TLV Attribute Parsing Tests
    
    func testParseTLVAttributes() throws {
        // Create TLV attributes data
        var attributesData = Data()
        
        // MTU attribute: Type(1) + Length(1) + Value(2)
        attributesData.append(0x03) // TLVAttributeType.mtu
        attributesData.append(0x04) // Length = 4
        attributesData.append(0x05) // MTU high byte (1420 = 0x058C)
        attributesData.append(0x8C) // MTU low byte
        
        // Username attribute: Type(1) + Length(1) + Value(n)
        let username = "test"
        let usernameData = username.data(using: .utf8)!
        attributesData.append(0x01) // TLVAttributeType.username
        attributesData.append(UInt8(usernameData.count + 2)) // Length
        attributesData.append(usernameData)
        
        let attributes = try packetParser.parseTLVAttributes(from: attributesData)
        
        XCTAssertEqual(attributes.count, 2)
        
        // Verify MTU attribute
        let mtuAttribute = attributes.first { $0.type == .mtu }!
        XCTAssertEqual(mtuAttribute.length, 4)
        let mtuValue = mtuAttribute.value.withUnsafeBytes { $0.load(as: UInt16.self).bigEndian }
        XCTAssertEqual(mtuValue, 1420)
        
        // Verify username attribute
        let usernameAttribute = attributes.first { $0.type == .username }!
        XCTAssertEqual(usernameAttribute.value, usernameData)
    }
    
    func testParseTLVAttributesEmpty() throws {
        let emptyData = Data()
        let attributes = try packetParser.parseTLVAttributes(from: emptyData)
        
        XCTAssertTrue(attributes.isEmpty)
    }
    
    func testParseTLVAttributesInvalidLength() {
        // Create malformed TLV data
        let malformedData = Data([
            0x01, // TLVAttributeType.username
            0x10, // Claims length 16
            0x74, 0x65, 0x73, 0x74 // But only has 4 bytes
        ])
        
        XCTAssertThrowsError(try packetParser.parseTLVAttributes(from: malformedData)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidTLVLength)
        }
    }
    
    func testParseTLVAttributesInvalidType() {
        let invalidData = Data([
            0xFF, // Invalid TLV type
            0x03, // Length
            0x01 // Value
        ])
        
        XCTAssertThrowsError(try packetParser.parseTLVAttributes(from: invalidData)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidTLVType)
        }
    }
    
    // MARK: - Signature Verification Tests
    
    func testVerifySignature() throws {
        let header = PacketHeader(type: .echoRequest, encrypt: .aes, sessionID: 1, token: 123)
        let expectedSignature = try packetBuilder.calculateSignature(for: header)
        
        // Verification should succeed with correct signature
        XCTAssertNoThrow(try packetParser.verifySignature(expectedSignature, for: header))
    }
    
    func testVerifySignatureInvalid() throws {
        let header = PacketHeader(type: .echoRequest, encrypt: .aes, sessionID: 1, token: 123)
        let invalidSignature = Data(repeating: 0xFF, count: 16)
        
        XCTAssertThrowsError(try packetParser.verifySignature(invalidSignature, for: header)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidSignature)
        }
    }
    
    func testVerifySignatureWrongLength() throws {
        let header = PacketHeader(type: .echoRequest, encrypt: .aes, sessionID: 1, token: 123)
        let shortSignature = Data([0x01, 0x02, 0x03]) // Only 3 bytes
        
        XCTAssertThrowsError(try packetParser.verifySignature(shortSignature, for: header)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidSignature)
        }
    }
    
    // MARK: - Round Trip Tests
    
    func testPacketBuildParseRoundTrip() throws {
        let header = PacketHeader(type: .echoRequest, encrypt: .aes, sessionID: 1234, token: 0x56789ABC)
        let attributes = [
            TLVAttribute(mtu: 1420),
            TLVAttribute(username: "testuser"),
            TLVAttribute(encryptionMethod: .aes)
        ]
        
        // Build packet
        let packetData = try packetBuilder.buildPacketWithSignature(header: header, attributes: attributes)
        
        // Parse packet
        let parsedPacket = try packetParser.parsePacket(from: packetData)
        
        // Verify round trip
        XCTAssertEqual(parsedPacket.header.type, header.type)
        XCTAssertEqual(parsedPacket.header.encrypt, header.encrypt)
        XCTAssertEqual(parsedPacket.header.sessionID, header.sessionID)
        XCTAssertEqual(parsedPacket.header.token, header.token)
        XCTAssertEqual(parsedPacket.attributes.count, attributes.count)
        
        // Verify attributes
        for (original, parsed) in zip(attributes, parsedPacket.attributes) {
            XCTAssertEqual(original.type, parsed.type)
            XCTAssertEqual(original.value, parsed.value)
        }
    }
    
    func testDataPacketBuildParseRoundTrip() throws {
        let header = PacketHeader(type: .data, encrypt: .aes, sessionID: 1, token: 123)
        let payload = "Test IP packet payload".data(using: .utf8)!
        
        // Build packet
        let packetData = try packetBuilder.buildDataPacket(header: header, payload: payload)
        
        // Parse packet
        let parsedPacket = try packetParser.parsePacket(from: packetData)
        
        // Verify round trip
        XCTAssertEqual(parsedPacket.header.type, header.type)
        XCTAssertEqual(parsedPacket.header.encrypt, header.encrypt)
        XCTAssertEqual(parsedPacket.header.sessionID, header.sessionID)
        XCTAssertEqual(parsedPacket.header.token, header.token)
        XCTAssertEqual(parsedPacket.payload, payload)
        XCTAssertNil(parsedPacket.signature)
        XCTAssertTrue(parsedPacket.attributes.isEmpty)
    }
    
    // MARK: - Edge Cases
    
    func testParsePacketMinimumSize() {
        // Packet with only header (8 bytes) - should fail for control packets
        let headerOnlyData = Data([
            0x15, // PacketType.echoRequest (control packet)
            0x02, // EncryptionMethod.aes
            0x00, 0x01, // SessionID = 1
            0x00, 0x00, 0x00, 0x7B // Token = 123
        ])
        
        XCTAssertThrowsError(try packetParser.parsePacket(from: headerOnlyData)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidPacketFormat)
        }
    }
    
    func testParsePacketEmptyData() {
        let emptyData = Data()
        
        XCTAssertThrowsError(try packetParser.parsePacket(from: emptyData)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidHeaderSize)
        }
    }
}
