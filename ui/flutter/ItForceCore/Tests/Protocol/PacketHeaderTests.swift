/**
 * FILE: PacketHeaderTests.swift
 *
 * DESCRIPTION:
 *     Unit tests for PacketHeader structure and related types.
 *     Verifies packet header serialization, deserialization, and compatibility.
 *
 * AUTHOR: wei
 * HISTORY: 26/06/2025 create
 */

import XCTest
@testable import ItForceCore

class PacketHeaderTests: XCTestCase {
    
    // MARK: - PacketType Tests
    
    func testPacketTypeRawValues() {
        // Test that packet type raw values match Go backend constants
        XCTAssertEqual(PacketType.openReject.rawValue, 0x11)
        XCTAssertEqual(PacketType.openAck.rawValue, 0x12)
        XCTAssertEqual(PacketType.open.rawValue, 0x13)
        XCTAssertEqual(PacketType.data.rawValue, 0x14)
        XCTAssertEqual(PacketType.echoRequest.rawValue, 0x15)
        XCTAssertEqual(PacketType.echoResponse.rawValue, 0x16)
        XCTAssertEqual(PacketType.close.rawValue, 0x17)
        XCTAssertEqual(PacketType.dataEncrypt.rawValue, 0x18)
        XCTAssertEqual(PacketType.dataDup.rawValue, 0x19)
        XCTAssertEqual(PacketType.dataEncDup.rawValue, 0x1A)
    }
    
    func testPacketTypeIsDataPacket() {
        // Test data packet identification
        XCTAssertTrue(PacketType.data.isDataPacket)
        XCTAssertTrue(PacketType.dataEncrypt.isDataPacket)
        XCTAssertTrue(PacketType.dataDup.isDataPacket)
        XCTAssertTrue(PacketType.dataEncDup.isDataPacket)
        XCTAssertTrue(PacketType.data6.isDataPacket)
        
        // Test control packet identification
        XCTAssertFalse(PacketType.open.isDataPacket)
        XCTAssertFalse(PacketType.openAck.isDataPacket)
        XCTAssertFalse(PacketType.openReject.isDataPacket)
        XCTAssertFalse(PacketType.echoRequest.isDataPacket)
        XCTAssertFalse(PacketType.echoResponse.isDataPacket)
        XCTAssertFalse(PacketType.close.isDataPacket)
    }
    
    func testPacketTypeDescription() {
        XCTAssertEqual(PacketType.open.description, "OPEN")
        XCTAssertEqual(PacketType.openAck.description, "OPEN_ACK")
        XCTAssertEqual(PacketType.data.description, "DATA")
        XCTAssertEqual(PacketType.echoRequest.description, "ECHO_REQUEST")
    }
    
    // MARK: - EncryptionMethod Tests
    
    func testEncryptionMethodRawValues() {
        // Test that encryption method raw values match Go backend constants
        XCTAssertEqual(EncryptionMethod.none.rawValue, 0x00)
        XCTAssertEqual(EncryptionMethod.xor.rawValue, 0x01)
        XCTAssertEqual(EncryptionMethod.aes.rawValue, 0x02)
    }
    
    func testEncryptionMethodDescription() {
        XCTAssertEqual(EncryptionMethod.none.description, "NONE")
        XCTAssertEqual(EncryptionMethod.xor.description, "XOR")
        XCTAssertEqual(EncryptionMethod.aes.description, "AES")
    }
    
    // MARK: - PacketHeader Tests
    
    func testPacketHeaderInitialization() {
        let header = PacketHeader(
            type: .open,
            encrypt: .aes,
            sessionID: 0x1234,
            token: 0x56789ABC
        )
        
        XCTAssertEqual(header.type, .open)
        XCTAssertEqual(header.encrypt, .aes)
        XCTAssertEqual(header.sessionID, 0x1234)
        XCTAssertEqual(header.token, 0x56789ABC)
    }
    
    func testPacketHeaderSerialization() {
        let header = PacketHeader(
            type: .open,
            encrypt: .aes,
            sessionID: 0x1234,
            token: 0x56789ABC
        )
        
        let data = header.toData()
        
        // Verify header size
        XCTAssertEqual(data.count, 8)
        
        // Verify field values (Big Endian)
        XCTAssertEqual(data[0], 0x13) // PacketType.open
        XCTAssertEqual(data[1], 0x02) // EncryptionMethod.aes
        XCTAssertEqual(data[2], 0x12) // SessionID high byte
        XCTAssertEqual(data[3], 0x34) // SessionID low byte
        XCTAssertEqual(data[4], 0x56) // Token byte 0
        XCTAssertEqual(data[5], 0x78) // Token byte 1
        XCTAssertEqual(data[6], 0x9A) // Token byte 2
        XCTAssertEqual(data[7], 0xBC) // Token byte 3
    }
    
    func testPacketHeaderDeserialization() throws {
        // Create test data (Big Endian)
        let data = Data([
            0x13, // PacketType.open
            0x02, // EncryptionMethod.aes
            0x12, 0x34, // SessionID = 0x1234
            0x56, 0x78, 0x9A, 0xBC // Token = 0x56789ABC
        ])
        
        let header = try PacketHeader(from: data)
        
        XCTAssertEqual(header.type, .open)
        XCTAssertEqual(header.encrypt, .aes)
        XCTAssertEqual(header.sessionID, 0x1234)
        XCTAssertEqual(header.token, 0x56789ABC)
    }
    
    func testPacketHeaderDeserializationInvalidSize() {
        // Test with insufficient data
        let shortData = Data([0x13, 0x02, 0x12])
        
        XCTAssertThrowsError(try PacketHeader(from: shortData)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidHeaderSize)
        }
    }
    
    func testPacketHeaderDeserializationInvalidPacketType() {
        // Test with invalid packet type
        let data = Data([
            0xFF, // Invalid packet type
            0x02, // EncryptionMethod.aes
            0x12, 0x34, // SessionID
            0x56, 0x78, 0x9A, 0xBC // Token
        ])
        
        XCTAssertThrowsError(try PacketHeader(from: data)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidPacketType)
        }
    }
    
    func testPacketHeaderDeserializationInvalidEncryptionMethod() {
        // Test with invalid encryption method
        let data = Data([
            0x13, // PacketType.open
            0xFF, // Invalid encryption method
            0x12, 0x34, // SessionID
            0x56, 0x78, 0x9A, 0xBC // Token
        ])
        
        XCTAssertThrowsError(try PacketHeader(from: data)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidEncryptionMethod)
        }
    }
    
    func testPacketHeaderRoundTrip() throws {
        let originalHeader = PacketHeader(
            type: .echoRequest,
            encrypt: .xor,
            sessionID: 0xABCD,
            token: 0x12345678
        )
        
        let data = originalHeader.toData()
        let deserializedHeader = try PacketHeader(from: data)
        
        XCTAssertEqual(originalHeader.type, deserializedHeader.type)
        XCTAssertEqual(originalHeader.encrypt, deserializedHeader.encrypt)
        XCTAssertEqual(originalHeader.sessionID, deserializedHeader.sessionID)
        XCTAssertEqual(originalHeader.token, deserializedHeader.token)
    }
    
    func testPacketHeaderConstants() {
        // Test header size constant
        XCTAssertEqual(PacketHeader.headerSize, 8)
    }
    
    // MARK: - Edge Cases
    
    func testPacketHeaderZeroValues() {
        let header = PacketHeader(
            type: .open,
            encrypt: .none,
            sessionID: 0,
            token: 0
        )
        
        let data = header.toData()
        XCTAssertEqual(data.count, 8)
        XCTAssertEqual(data[0], 0x13) // PacketType.open
        XCTAssertEqual(data[1], 0x00) // EncryptionMethod.none
        XCTAssertEqual(data[2], 0x00) // SessionID high byte
        XCTAssertEqual(data[3], 0x00) // SessionID low byte
        XCTAssertEqual(data[4], 0x00) // Token byte 0
        XCTAssertEqual(data[5], 0x00) // Token byte 1
        XCTAssertEqual(data[6], 0x00) // Token byte 2
        XCTAssertEqual(data[7], 0x00) // Token byte 3
    }
    
    func testPacketHeaderMaxValues() {
        let header = PacketHeader(
            type: .data,
            encrypt: .aes,
            sessionID: UInt16.max,
            token: UInt32.max
        )
        
        let data = header.toData()
        XCTAssertEqual(data.count, 8)
        XCTAssertEqual(data[0], 0x14) // PacketType.data
        XCTAssertEqual(data[1], 0x02) // EncryptionMethod.aes
        XCTAssertEqual(data[2], 0xFF) // SessionID high byte
        XCTAssertEqual(data[3], 0xFF) // SessionID low byte
        XCTAssertEqual(data[4], 0xFF) // Token byte 0
        XCTAssertEqual(data[5], 0xFF) // Token byte 1
        XCTAssertEqual(data[6], 0xFF) // Token byte 2
        XCTAssertEqual(data[7], 0xFF) // Token byte 3
    }
}
