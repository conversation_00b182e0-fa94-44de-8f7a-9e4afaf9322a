/**
 * FILE: GoSwiftInteropTests.swift
 *
 * DESCRIPTION:
 *     Go-Swift protocol interoperability tests.
 *     Parses Go-generated packets and generates Swift packets for Go validation.
 *
 * AUTHOR: wei
 * HISTORY: 26/06/2025 create
 */

import XCTest
import Foundation
@testable import ItForceCore

class GoSwiftInteropTests: XCTestCase {
    
    var packetBuilder: PacketBuilder!
    var packetParser: PacketParser!
    
    let testDataDir = "test_data"
    let goToSwiftDir = "go_to_swift"
    let swiftToGoDir = "swift_to_go"
    
    override func setUp() {
        super.setUp()
        packetBuilder = PacketBuilder()
        packetParser = PacketParser()
        
        // Create test directories
        createTestDirectories()
    }
    
    override func tearDown() {
        packetBuilder = nil
        packetParser = nil
        super.tearDown()
    }
    
    // MARK: - Directory Setup
    
    func createTestDirectories() {
        let fileManager = FileManager.default
        let baseURL = getTestDataURL()
        
        let directories = [
            baseURL,
            baseURL.appendingPathComponent(goToSwiftDir),
            baseURL.appendingPathComponent(swiftToGoDir)
        ]
        
        for directory in directories {
            try? fileManager.createDirectory(at: directory, withIntermediateDirectories: true)
        }
    }
    
    func getTestDataURL() -> URL {
        // Get the project root directory
        let currentFile = URL(fileURLWithPath: #file)
        let projectRoot = currentFile
            .deletingLastPathComponent() // Tests
            .deletingLastPathComponent() // Protocol
            .deletingLastPathComponent() // ItForceCore
            .deletingLastPathComponent() // flutter
            .deletingLastPathComponent() // ui
            .deletingLastPathComponent() // mobile
        
        return projectRoot.appendingPathComponent(testDataDir)
    }
    
    // MARK: - Go Packet Validation Tests
    
    func testParseGoOpenPacket() throws {
        let packetData = try loadGoPacket("open_packet.bin")
        let parsedPacket = try packetParser.parsePacket(from: packetData)
        
        // Verify header
        XCTAssertEqual(parsedPacket.header.type, .open)
        XCTAssertEqual(parsedPacket.header.encrypt, .aes)
        XCTAssertEqual(parsedPacket.header.sessionID, 0)
        XCTAssertEqual(parsedPacket.header.token, 0)
        
        // Verify signature is present
        XCTAssertNotNil(parsedPacket.signature)
        XCTAssertEqual(parsedPacket.signature?.count, 16)
        
        // Verify attributes are present
        XCTAssertGreaterThan(parsedPacket.attributes.count, 0)
        
        // Look for expected attributes
        let mtuAttr = parsedPacket.attributes.first { $0.type == .mtu }
        XCTAssertNotNil(mtuAttr, "MTU attribute should be present")
        
        let usernameAttr = parsedPacket.attributes.first { $0.type == .username }
        XCTAssertNotNil(usernameAttr, "Username attribute should be present")
        
        let passwordAttr = parsedPacket.attributes.first { $0.type == .password }
        XCTAssertNotNil(passwordAttr, "Password attribute should be present")
        
        print("✓ Successfully parsed Go OPEN packet")
        print("  - Header: \(parsedPacket.header)")
        print("  - Attributes: \(parsedPacket.attributes.count)")
    }
    
    func testParseGoEchoRequestPacket() throws {
        let packetData = try loadGoPacket("echo_request_packet.bin")
        let parsedPacket = try packetParser.parsePacket(from: packetData)
        
        // Verify header
        XCTAssertEqual(parsedPacket.header.type, .echoRequest)
        XCTAssertEqual(parsedPacket.header.encrypt, .aes)
        XCTAssertEqual(parsedPacket.header.sessionID, 1234)
        XCTAssertEqual(parsedPacket.header.token, 0x56789ABC)
        
        // Verify signature is present
        XCTAssertNotNil(parsedPacket.signature)
        XCTAssertEqual(parsedPacket.signature?.count, 16)
        
        // Verify timestamp payload
        XCTAssertEqual(parsedPacket.payload.count, 8, "Echo request should have 8-byte timestamp")
        
        print("✓ Successfully parsed Go Echo Request packet")
        print("  - Header: \(parsedPacket.header)")
        print("  - Payload size: \(parsedPacket.payload.count)")
    }
    
    func testParseGoDataPacket() throws {
        let packetData = try loadGoPacket("data_packet.bin")
        let parsedPacket = try packetParser.parsePacket(from: packetData)
        
        // Verify header
        XCTAssertEqual(parsedPacket.header.type, .data)
        XCTAssertEqual(parsedPacket.header.encrypt, .aes)
        XCTAssertEqual(parsedPacket.header.sessionID, 1234)
        XCTAssertEqual(parsedPacket.header.token, 0x56789ABC)
        
        // Data packets should not have signatures or attributes
        XCTAssertNil(parsedPacket.signature)
        XCTAssertTrue(parsedPacket.attributes.isEmpty)
        
        // Verify payload (IPv4 packet)
        XCTAssertGreaterThan(parsedPacket.payload.count, 0)
        
        print("✓ Successfully parsed Go Data packet")
        print("  - Header: \(parsedPacket.header)")
        print("  - Payload size: \(parsedPacket.payload.count)")
        print("  - Payload (hex): \(parsedPacket.payload.map { String(format: "%02x", $0) }.joined())")
    }
    
    // MARK: - Swift Packet Generation Tests
    
    func testGenerateSwiftOpenPacket() throws {
        let packet = try packetBuilder.buildOpenPacket(
            username: "swiftuser",
            password: "swiftpass",
            mtu: 1500,
            encryptionMethod: .aes
        )
        
        try saveSwiftPacket("swift_open_packet.bin", data: packet)
        
        // Verify we can parse our own packet
        let parsedPacket = try packetParser.parsePacket(from: packet)
        XCTAssertEqual(parsedPacket.header.type, .open)
        XCTAssertEqual(parsedPacket.header.encrypt, .aes)
        
        print("✓ Generated Swift OPEN packet")
        print("  - Size: \(packet.count) bytes")
        print("  - Header: \(parsedPacket.header)")
    }
    
    func testGenerateSwiftEchoRequestPacket() throws {
        let packet = try packetBuilder.buildEchoRequestPacket(
            sessionID: 5678,
            token: 0xDEADBEEF,
            encryptionMethod: .xor
        )
        
        try saveSwiftPacket("swift_echo_request_packet.bin", data: packet)
        
        // Verify we can parse our own packet
        let parsedPacket = try packetParser.parsePacket(from: packet)
        XCTAssertEqual(parsedPacket.header.type, .echoRequest)
        XCTAssertEqual(parsedPacket.header.encrypt, .xor)
        XCTAssertEqual(parsedPacket.header.sessionID, 5678)
        XCTAssertEqual(parsedPacket.header.token, 0xDEADBEEF)
        
        print("✓ Generated Swift Echo Request packet")
        print("  - Size: \(packet.count) bytes")
        print("  - Header: \(parsedPacket.header)")
    }
    
    func testGenerateSwiftDataPacket() throws {
        // Create sample IPv4 packet
        let ipPacket = Data([
            0x45, 0x00, 0x00, 0x20, // Version, IHL, ToS, Total Length
            0x00, 0x02, 0x40, 0x00, // ID, Flags, Fragment Offset
            0x40, 0x01, 0x00, 0x00, // TTL, Protocol, Checksum
            0xC0, 0xA8, 0x01, 0x65, // Source IP: ***********01
            0xC0, 0xA8, 0x01, 0x01, // Dest IP: ***********
            // Payload: "Swift!"
            0x53, 0x77, 0x69, 0x66, 0x74, 0x21, 0x00, 0x00
        ])
        
        let header = PacketHeader(
            type: .data,
            encrypt: .xor,
            sessionID: 5678,
            token: 0xDEADBEEF
        )
        
        let packet = packetBuilder.buildDataPacket(header: header, payload: ipPacket)
        
        try saveSwiftPacket("swift_data_packet.bin", data: packet)
        
        // Verify we can parse our own packet
        let parsedPacket = try packetParser.parsePacket(from: packet)
        XCTAssertEqual(parsedPacket.header.type, .data)
        XCTAssertEqual(parsedPacket.payload, ipPacket)
        
        print("✓ Generated Swift Data packet")
        print("  - Size: \(packet.count) bytes")
        print("  - Header: \(parsedPacket.header)")
        print("  - Payload size: \(parsedPacket.payload.count)")
    }
    
    // MARK: - Cross-Validation Tests
    
    func testGoSwiftPacketCompatibility() throws {
        // Test that Swift can parse all Go packets
        let goPackets = ["open_packet.bin", "echo_request_packet.bin", "data_packet.bin"]
        
        for packetFile in goPackets {
            do {
                let packetData = try loadGoPacket(packetFile)
                let parsedPacket = try packetParser.parsePacket(from: packetData)
                
                print("✓ Swift successfully parsed Go packet: \(packetFile)")
                print("  - Type: \(parsedPacket.header.type)")
                print("  - Size: \(packetData.count) bytes")
                
            } catch {
                // If file doesn't exist, skip (Go tool hasn't run yet)
                if (error as NSError).code == NSFileReadNoSuchFileError {
                    print("⚠ Go packet \(packetFile) not found - run Go interop tool first")
                    continue
                } else {
                    throw error
                }
            }
        }
    }
    
    func testGenerateSwiftTestVectors() throws {
        // Generate comprehensive test vectors for Go validation
        try generateAllSwiftPackets()
        try generateSwiftTestVectorDocumentation()
        
        print("✓ Generated complete Swift test vector suite")
    }
    
    // MARK: - Helper Methods
    
    func loadGoPacket(_ filename: String) throws -> Data {
        let url = getTestDataURL()
            .appendingPathComponent(goToSwiftDir)
            .appendingPathComponent(filename)
        
        return try Data(contentsOf: url)
    }
    
    func saveSwiftPacket(_ filename: String, data: Data) throws {
        let url = getTestDataURL()
            .appendingPathComponent(swiftToGoDir)
            .appendingPathComponent(filename)
        
        try data.write(to: url)
    }
    
    func generateAllSwiftPackets() throws {
        // Generate various packet types with different parameters
        let testCases = [
            ("swift_open_aes.bin", try packetBuilder.buildOpenPacket(
                username: "testuser", password: "testpass", mtu: 1420, encryptionMethod: .aes)),
            ("swift_open_xor.bin", try packetBuilder.buildOpenPacket(
                username: "user2", password: "pass2", mtu: 1500, encryptionMethod: .xor)),
            ("swift_open_none.bin", try packetBuilder.buildOpenPacket(
                username: "user3", password: "pass3", mtu: 576, encryptionMethod: .none)),
        ]
        
        for (filename, packet) in testCases {
            try saveSwiftPacket(filename, data: packet)
        }
        
        // Generate echo requests
        let echoPacket = try packetBuilder.buildEchoRequestPacket(
            sessionID: 1111, token: 0x12345678, encryptionMethod: .aes)
        try saveSwiftPacket("swift_echo_aes.bin", data: echoPacket)
        
        // Generate data packets
        let samplePayload = "Hello from Swift!".data(using: .utf8)!
        let dataHeader = PacketHeader(type: .data, encrypt: .none, sessionID: 2222, token: 0x87654321)
        let dataPacket = packetBuilder.buildDataPacket(header: dataHeader, payload: samplePayload)
        try saveSwiftPacket("swift_data_none.bin", data: dataPacket)
    }
    
    func generateSwiftTestVectorDocumentation() throws {
        let content = """
# Swift-Go Protocol Interoperability Test Vectors

This directory contains binary packet files generated by Swift implementation for Go validation.

## Test Cases:

### OPEN Packets
- **swift_open_aes.bin**: username=testuser, password=testpass, MTU=1420, encryption=AES
- **swift_open_xor.bin**: username=user2, password=pass2, MTU=1500, encryption=XOR  
- **swift_open_none.bin**: username=user3, password=pass3, MTU=576, encryption=None

### Echo Request Packets
- **swift_echo_aes.bin**: sessionID=1111, token=0x12345678, encryption=AES

### Data Packets
- **swift_data_none.bin**: Sample text payload, sessionID=2222, token=0x87654321, encryption=None

## Usage:

1. Run Swift tests to generate these packets
2. Use Go interop tool to parse and validate Swift-generated packets
3. Compare results with Go-generated equivalents

## Validation Points:

- Header format (8 bytes, Big Endian)
- Packet type constants
- TLV attribute encoding
- Signature calculation (MD5)
- Encryption compatibility

"""
        
        let url = getTestDataURL()
            .appendingPathComponent(swiftToGoDir)
            .appendingPathComponent("README.md")
        
        try content.write(to: url, atomically: true, encoding: .utf8)
    }
}
