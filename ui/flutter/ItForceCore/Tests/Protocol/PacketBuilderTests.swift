/**
 * FILE: PacketBuilderTests.swift
 *
 * DESCRIPTION:
 *     Unit tests for PacketBuilder class and packet construction.
 *     Verifies packet building compatibility with Go backend implementation.
 *
 * AUTHOR: wei
 * HISTORY: 26/06/2025 create
 */

import XCTest
import CryptoKit
@testable import ItForceCore

class PacketBuilderTests: XCTestCase {
    
    var packetBuilder: PacketBuilder!
    
    override func setUp() {
        super.setUp()
        packetBuilder = PacketBuilder()
    }
    
    override func tearDown() {
        packetBuilder = nil
        super.tearDown()
    }
    
    // MARK: - Signature Calculation Tests
    
    func testCalculateSignature() throws {
        let header = PacketHeader(type: .open, encrypt: .aes, sessionID: 0, token: 0)
        let signature = try packetBuilder.calculateSignature(for: header)
        
        // Signature should be 16 bytes (MD5)
        XCTAssertEqual(signature.count, 16)
        
        // Verify signature calculation: MD5(header + "mw")
        let headerData = header.toData()
        let salt = Data([109, 119]) // ASCII 'm', 'w'
        let expectedHash = Insecure.MD5.hash(data: headerData + salt)
        
        XCTAssertEqual(signature, Data(expectedHash))
    }
    
    func testCalculateSignatureDifferentHeaders() throws {
        let header1 = PacketHeader(type: .open, encrypt: .aes, sessionID: 0, token: 0)
        let header2 = PacketHeader(type: .echoRequest, encrypt: .xor, sessionID: 1, token: 123)
        
        let signature1 = try packetBuilder.calculateSignature(for: header1)
        let signature2 = try packetBuilder.calculateSignature(for: header2)
        
        // Different headers should produce different signatures
        XCTAssertNotEqual(signature1, signature2)
    }
    
    func testCalculateSignatureConsistency() throws {
        let header = PacketHeader(type: .echoRequest, encrypt: .aes, sessionID: 1234, token: 0x56789ABC)
        
        let signature1 = try packetBuilder.calculateSignature(for: header)
        let signature2 = try packetBuilder.calculateSignature(for: header)
        
        // Same header should produce same signature
        XCTAssertEqual(signature1, signature2)
    }
    
    // MARK: - Data Packet Building Tests
    
    func testBuildDataPacket() throws {
        let header = PacketHeader(type: .data, encrypt: .none, sessionID: 1, token: 123)
        let payload = "IP packet data".data(using: .utf8)!
        
        let packet = try packetBuilder.buildDataPacket(header: header, payload: payload)
        
        // Verify packet structure: Header + Payload (no signature for data packets)
        XCTAssertEqual(packet.count, 8 + payload.count)
        
        // Verify header
        let headerData = packet.prefix(8)
        let reconstructedHeader = try PacketHeader(from: headerData)
        XCTAssertEqual(reconstructedHeader.type, header.type)
        XCTAssertEqual(reconstructedHeader.encrypt, header.encrypt)
        XCTAssertEqual(reconstructedHeader.sessionID, header.sessionID)
        XCTAssertEqual(reconstructedHeader.token, header.token)
        
        // Verify payload
        let payloadData = packet.dropFirst(8)
        XCTAssertEqual(payloadData, payload)
    }
    
    func testBuildDataPacketEmptyPayload() throws {
        let header = PacketHeader(type: .data, encrypt: .none, sessionID: 1, token: 123)
        let emptyPayload = Data()
        
        let packet = try packetBuilder.buildDataPacket(header: header, payload: emptyPayload)
        
        // Should contain only header
        XCTAssertEqual(packet.count, 8)
        
        let headerData = packet.prefix(8)
        let reconstructedHeader = try PacketHeader(from: headerData)
        XCTAssertEqual(reconstructedHeader.type, header.type)
    }
    
    func testBuildDataPacketLargePayload() throws {
        let header = PacketHeader(type: .data, encrypt: .aes, sessionID: 1, token: 123)
        let largePayload = Data(repeating: 0x42, count: 1500) // MTU-sized payload
        
        let packet = try packetBuilder.buildDataPacket(header: header, payload: largePayload)
        
        XCTAssertEqual(packet.count, 8 + largePayload.count)
        
        let payloadData = packet.dropFirst(8)
        XCTAssertEqual(payloadData, largePayload)
    }
    
    // MARK: - Control Packet Building Tests
    
    func testBuildPacketWithSignature() throws {
        let header = PacketHeader(type: .echoRequest, encrypt: .aes, sessionID: 1, token: 123)
        let attributes = [
            TLVAttribute(mtu: 1420),
            TLVAttribute(username: "testuser")
        ]
        
        let packet = try packetBuilder.buildPacketWithSignature(header: header, attributes: attributes)
        
        // Verify packet structure: Header + Signature + Attributes
        XCTAssertGreaterThan(packet.count, 8 + 16) // At least header + signature
        
        // Verify header
        let headerData = packet.prefix(8)
        let reconstructedHeader = try PacketHeader(from: headerData)
        XCTAssertEqual(reconstructedHeader.type, header.type)
        
        // Verify signature
        let signatureData = packet.subdata(in: 8..<24)
        let expectedSignature = try packetBuilder.calculateSignature(for: header)
        XCTAssertEqual(signatureData, expectedSignature)
        
        // Verify attributes are present
        let attributesData = packet.dropFirst(24)
        XCTAssertGreaterThan(attributesData.count, 0)
    }
    
    func testBuildPacketWithSignatureNoAttributes() throws {
        let header = PacketHeader(type: .echoRequest, encrypt: .aes, sessionID: 1, token: 123)
        
        let packet = try packetBuilder.buildPacketWithSignature(header: header, attributes: [])
        
        // Should contain header + signature only
        XCTAssertEqual(packet.count, 8 + 16)
        
        // Verify signature
        let signatureData = packet.subdata(in: 8..<24)
        let expectedSignature = try packetBuilder.calculateSignature(for: header)
        XCTAssertEqual(signatureData, expectedSignature)
    }
    
    // MARK: - OPEN Packet Building Tests
    
    func testBuildOpenPacket() throws {
        let username = "testuser"
        let password = "testpass"
        let mtu: UInt16 = 1420
        let encryptionMethod = EncryptionMethod.aes
        
        let packet = try packetBuilder.buildOpenPacket(
            username: username,
            password: password,
            mtu: mtu,
            encryptionMethod: encryptionMethod
        )
        
        // Verify packet is not empty
        XCTAssertGreaterThan(packet.count, 8 + 16) // Header + signature + attributes
        
        // Verify header (first 8 bytes)
        let headerData = packet.prefix(8)
        let header = try PacketHeader(from: headerData)
        XCTAssertEqual(header.type, .open)
        XCTAssertEqual(header.encrypt, encryptionMethod)
        XCTAssertEqual(header.sessionID, 0) // OPEN packets have sessionID = 0
        XCTAssertEqual(header.token, 0) // OPEN packets have token = 0
        
        // Verify signature (bytes 8-23)
        let signatureData = packet.subdata(in: 8..<24)
        let expectedSignature = try packetBuilder.calculateSignature(for: header)
        XCTAssertEqual(signatureData, expectedSignature)
        
        // Verify attributes are present (bytes 24+)
        let attributesData = packet.dropFirst(24)
        XCTAssertGreaterThan(attributesData.count, 0)
    }
    
    func testBuildOpenPacketAttributeOrder() throws {
        let username = "testuser"
        let password = "testpass"
        let mtu: UInt16 = 1420
        let encryptionMethod = EncryptionMethod.aes
        
        let packet = try packetBuilder.buildOpenPacket(
            username: username,
            password: password,
            mtu: mtu,
            encryptionMethod: encryptionMethod
        )
        
        // Parse attributes to verify order
        let attributesData = packet.dropFirst(24) // Skip header + signature
        var offset = 0
        
        // First attribute should be MTU
        XCTAssertGreaterThan(attributesData.count, offset)
        XCTAssertEqual(attributesData[offset], 0x03) // TLVAttributeType.mtu
        
        // Skip to next attribute
        let mtuLength = Int(attributesData[offset + 1])
        offset += mtuLength
        
        // Second attribute should be Username
        XCTAssertGreaterThan(attributesData.count, offset)
        XCTAssertEqual(attributesData[offset], 0x01) // TLVAttributeType.username
        
        // Skip to next attribute
        let usernameLength = Int(attributesData[offset + 1])
        offset += usernameLength
        
        // Third attribute should be Password
        XCTAssertGreaterThan(attributesData.count, offset)
        XCTAssertEqual(attributesData[offset], 0x02) // TLVAttributeType.password
    }
    
    func testBuildOpenPacketPasswordEncryption() throws {
        let username = "testuser"
        let password = "testpass"
        let mtu: UInt16 = 1420
        let encryptionMethod = EncryptionMethod.aes
        
        let packet = try packetBuilder.buildOpenPacket(
            username: username,
            password: password,
            mtu: mtu,
            encryptionMethod: encryptionMethod
        )
        
        // Extract password attribute and verify it's encrypted
        let attributesData = packet.dropFirst(24)
        var offset = 0
        
        // Skip MTU attribute
        let mtuLength = Int(attributesData[offset + 1])
        offset += mtuLength
        
        // Skip Username attribute
        let usernameLength = Int(attributesData[offset + 1])
        offset += usernameLength
        
        // Parse Password attribute
        XCTAssertEqual(attributesData[offset], 0x02) // TLVAttributeType.password
        XCTAssertEqual(attributesData[offset + 1], 0x12) // Length = 18 (type + length + 16 bytes)
        
        let encryptedPassword = attributesData.subdata(in: (offset + 2)..<(offset + 18))
        XCTAssertEqual(encryptedPassword.count, 16)
        
        // Verify password is encrypted (should not match plaintext)
        XCTAssertNotEqual(encryptedPassword, password.data(using: .utf8))
        
        // Verify encryption is consistent
        let expectedEncrypted = try PasswordEncryption.encryptPassword(password, username: username)
        XCTAssertEqual(encryptedPassword, expectedEncrypted)
    }
    
    func testBuildOpenPacketDifferentEncryptionMethods() throws {
        let username = "testuser"
        let password = "testpass"
        let mtu: UInt16 = 1420
        
        let encryptionMethods: [EncryptionMethod] = [.none, .xor, .aes]
        
        for method in encryptionMethods {
            let packet = try packetBuilder.buildOpenPacket(
                username: username,
                password: password,
                mtu: mtu,
                encryptionMethod: method
            )
            
            // Verify header contains correct encryption method
            let headerData = packet.prefix(8)
            let header = try PacketHeader(from: headerData)
            XCTAssertEqual(header.encrypt, method)
            
            // For non-none encryption, verify encrypt attribute is present
            if method != .none {
                let attributesData = packet.dropFirst(24)
                // Should contain encrypt attribute at the end
                XCTAssertTrue(attributesData.contains(0x08)) // TLVAttributeType.encrypt
            }
        }
    }
    
    // MARK: - Echo Request Packet Building Tests
    
    func testBuildEchoRequestPacket() throws {
        let sessionID: UInt16 = 1234
        let token: UInt32 = 0x56789ABC
        let encryptionMethod = EncryptionMethod.aes
        
        let packet = try packetBuilder.buildEchoRequestPacket(
            sessionID: sessionID,
            token: token,
            encryptionMethod: encryptionMethod
        )
        
        // Verify packet structure: Header + Signature + Timestamp
        XCTAssertEqual(packet.count, 8 + 16 + 8) // Header + signature + 8-byte timestamp
        
        // Verify header
        let headerData = packet.prefix(8)
        let header = try PacketHeader(from: headerData)
        XCTAssertEqual(header.type, .echoRequest)
        XCTAssertEqual(header.encrypt, encryptionMethod)
        XCTAssertEqual(header.sessionID, sessionID)
        XCTAssertEqual(header.token, token)
        
        // Verify signature
        let signatureData = packet.subdata(in: 8..<24)
        let expectedSignature = try packetBuilder.calculateSignature(for: header)
        XCTAssertEqual(signatureData, expectedSignature)
        
        // Verify timestamp is present (8 bytes)
        let timestampData = packet.dropFirst(24)
        XCTAssertEqual(timestampData.count, 8)
    }
    
    // MARK: - Edge Cases and Error Handling
    
    func testBuildOpenPacketEmptyUsername() throws {
        let packet = try packetBuilder.buildOpenPacket(
            username: "",
            password: "testpass",
            mtu: 1420,
            encryptionMethod: .aes
        )
        
        // Should still build successfully
        XCTAssertGreaterThan(packet.count, 8 + 16)
    }
    
    func testBuildOpenPacketEmptyPassword() throws {
        let packet = try packetBuilder.buildOpenPacket(
            username: "testuser",
            password: "",
            mtu: 1420,
            encryptionMethod: .aes
        )
        
        // Should still build successfully
        XCTAssertGreaterThan(packet.count, 8 + 16)
    }
    
    func testBuildOpenPacketZeroMTU() throws {
        let packet = try packetBuilder.buildOpenPacket(
            username: "testuser",
            password: "testpass",
            mtu: 0,
            encryptionMethod: .aes
        )
        
        // Should still build successfully
        XCTAssertGreaterThan(packet.count, 8 + 16)
    }
    
    func testBuildOpenPacketMaxMTU() throws {
        let packet = try packetBuilder.buildOpenPacket(
            username: "testuser",
            password: "testpass",
            mtu: UInt16.max,
            encryptionMethod: .aes
        )
        
        // Should still build successfully
        XCTAssertGreaterThan(packet.count, 8 + 16)
    }
}
