/**
 * FILE: DataPacketEncryptionTests.swift
 *
 * DESCRIPTION:
 *     Tests for data packet encryption compatibility with Go backend.
 *     Validates that Swift implementation produces identical encrypted packets.
 *     Tests both XOR and AES encryption methods.
 *
 * AUTHOR: wei
 * HISTORY: 30/06/2025 create data packet encryption validation tests
 */

import XCTest
@testable import ItForceCore

class DataPacketEncryptionTests: XCTestCase {
    
    private var packetBuilder: PacketBuilder!
    private var xorEncryptor: XOREncryption!
    private var aesEncryptor: AESEncryption!
    
    override func setUp() {
        super.setUp()
        packetBuilder = PacketBuilder()
        
        // Initialize encryptors with test credentials
        do {
            xorEncryptor = try XOREncryption(username: "testuser", password: "testpass")
            aesEncryptor = try AESEncryption(username: "testuser", password: "testpass")
        } catch {
            XCTFail("Failed to initialize encryptors: \(error)")
        }
    }
    
    override func tearDown() {
        packetBuilder = nil
        xorEncryptor = nil
        aesEncryptor = nil
        super.tearDown()
    }
    
    // MARK: - Basic Data Packet Tests
    
    func testBuildUnencryptedDataPacket() throws {
        // Create sample IPv4 packet
        let ipPacket = Data([
            0x45, 0x00, 0x00, 0x1C, // Version, IHL, ToS, Total Length
            0x00, 0x01, 0x40, 0x00, // ID, Flags, Fragment Offset
            0x40, 0x01, 0x00, 0x00, // TTL, Protocol, Checksum
            0xC0, 0xA8, 0x01, 0x64, // Source IP: ***********00
            0xC0, 0xA8, 0x01, 0x01, // Dest IP: ***********
            0x48, 0x65, 0x6C, 0x6C, // Payload: "Hell"
            0x6F, 0x21, 0x00, 0x00  // Payload: "o!" + padding
        ])
        
        let header = PacketHeader(
            type: .data,
            encrypt: .none,
            sessionID: 1234,
            token: 0x56789ABC
        )
        
        let packet = packetBuilder.buildDataPacket(header: header, payload: ipPacket)
        
        // Verify packet structure
        XCTAssertEqual(packet.count, 8 + ipPacket.count, "Unencrypted data packet size should be header + payload")
        
        // Verify header
        XCTAssertEqual(packet[0], 0x14, "Should be DATA packet type")
        XCTAssertEqual(packet[1], 0x00, "Should be no encryption")
        
        // Verify session ID
        let sessionID = UInt16(packet[2]) << 8 | UInt16(packet[3])
        XCTAssertEqual(sessionID, 1234, "Session ID should match")
        
        // Verify token
        let token = UInt32(packet[4]) << 24 | UInt32(packet[5]) << 16 | UInt32(packet[6]) << 8 | UInt32(packet[7])
        XCTAssertEqual(token, 0x56789ABC, "Token should match")
        
        // Verify payload
        let payloadData = packet.subdata(in: 8..<packet.count)
        XCTAssertEqual(payloadData, ipPacket, "Payload should match original IP packet")
        
        print("✓ Unencrypted data packet validated")
        print("  - Total size: \(packet.count) bytes")
        print("  - Header: 8 bytes")
        print("  - Payload: \(ipPacket.count) bytes")
    }
    
    // MARK: - XOR Encryption Tests
    
    func testBuildXOREncryptedDataPacket() throws {
        // Create sample IPv4 packet
        let ipPacket = Data([
            0x45, 0x00, 0x00, 0x1C, // Version, IHL, ToS, Total Length
            0x00, 0x01, 0x40, 0x00, // ID, Flags, Fragment Offset
            0x40, 0x01, 0x00, 0x00, // TTL, Protocol, Checksum
            0xC0, 0xA8, 0x01, 0x64, // Source IP: ***********00
            0xC0, 0xA8, 0x01, 0x01, // Dest IP: ***********
            0x48, 0x65, 0x6C, 0x6C, // Payload: "Hell"
            0x6F, 0x21, 0x00, 0x00  // Payload: "o!" + padding
        ])
        
        let packet = try packetBuilder.buildDataEncryptPacket(
            sessionID: 1234,
            token: 0x56789ABC,
            payload: ipPacket,
            encryptionMethod: .xor,
            encryptor: xorEncryptor
        )
        
        // Verify packet structure
        XCTAssertEqual(packet[0], 0x18, "Should be DATAENCRYPT packet type")
        XCTAssertEqual(packet[1], 0x01, "Should be XOR encryption")
        
        // Verify session ID
        let sessionID = UInt16(packet[2]) << 8 | UInt16(packet[3])
        XCTAssertEqual(sessionID, 1234, "Session ID should match")
        
        // Verify token
        let token = UInt32(packet[4]) << 24 | UInt32(packet[5]) << 16 | UInt32(packet[6]) << 8 | UInt32(packet[7])
        XCTAssertEqual(token, 0x56789ABC, "Token should match")
        
        // Verify encrypted payload
        let encryptedPayload = packet.subdata(in: 8..<packet.count)
        XCTAssertNotEqual(encryptedPayload, ipPacket, "Encrypted payload should differ from original")
        
        // Test decryption
        let decryptedPayload = try xorEncryptor.decrypt(encryptedPayload)
        XCTAssertEqual(decryptedPayload, ipPacket, "Decrypted payload should match original")
        
        print("✓ XOR encrypted data packet validated")
        print("  - Total size: \(packet.count) bytes")
        print("  - Header: 8 bytes")
        print("  - Encrypted payload: \(encryptedPayload.count) bytes")
        print("  - Encryption/decryption: ✓")
    }
    
    // MARK: - AES Encryption Tests
    
    func testBuildAESEncryptedDataPacket() throws {
        // Create sample IPv4 packet
        let ipPacket = Data([
            0x45, 0x00, 0x00, 0x1C, // Version, IHL, ToS, Total Length
            0x00, 0x01, 0x40, 0x00, // ID, Flags, Fragment Offset
            0x40, 0x01, 0x00, 0x00, // TTL, Protocol, Checksum
            0xC0, 0xA8, 0x01, 0x64, // Source IP: ***********00
            0xC0, 0xA8, 0x01, 0x01, // Dest IP: ***********
            0x48, 0x65, 0x6C, 0x6C, // Payload: "Hell"
            0x6F, 0x21, 0x00, 0x00  // Payload: "o!" + padding
        ])
        
        let packet = try packetBuilder.buildDataEncryptPacket(
            sessionID: 5678,
            token: 0xDEADBEEF,
            payload: ipPacket,
            encryptionMethod: .aes,
            encryptor: aesEncryptor
        )
        
        // Verify packet structure
        XCTAssertEqual(packet[0], 0x18, "Should be DATAENCRYPT packet type")
        XCTAssertEqual(packet[1], 0x02, "Should be AES encryption")
        
        // Verify session ID
        let sessionID = UInt16(packet[2]) << 8 | UInt16(packet[3])
        XCTAssertEqual(sessionID, 5678, "Session ID should match")
        
        // Verify token
        let token = UInt32(packet[4]) << 24 | UInt32(packet[5]) << 16 | UInt32(packet[6]) << 8 | UInt32(packet[7])
        XCTAssertEqual(token, 0xDEADBEEF, "Token should match")
        
        // Verify encrypted payload
        let encryptedPayload = packet.subdata(in: 8..<packet.count)
        XCTAssertNotEqual(encryptedPayload, ipPacket, "Encrypted payload should differ from original")
        
        // AES padding should make encrypted payload larger or equal
        XCTAssertGreaterThanOrEqual(encryptedPayload.count, ipPacket.count, "AES encrypted payload should be padded")
        
        // Test decryption
        let decryptedPayload = try aesEncryptor.decrypt(encryptedPayload)
        
        // For AES, we need to trim padding to match original
        let trimmedPayload = decryptedPayload.prefix(ipPacket.count)
        XCTAssertEqual(Data(trimmedPayload), ipPacket, "Decrypted payload should match original (after trimming)")
        
        print("✓ AES encrypted data packet validated")
        print("  - Total size: \(packet.count) bytes")
        print("  - Header: 8 bytes")
        print("  - Original payload: \(ipPacket.count) bytes")
        print("  - Encrypted payload: \(encryptedPayload.count) bytes")
        print("  - Encryption/decryption: ✓")
    }
    
    // MARK: - Go Backend Compatibility Tests
    
    func testGoBackendCompatibility() throws {
        // Test cases that should match Go backend exactly
        let testCases = [
            (sessionID: UInt16(1234), token: UInt32(0x56789ABC), encrypt: EncryptionMethod.xor),
            (sessionID: UInt16(5678), token: UInt32(0xDEADBEEF), encrypt: EncryptionMethod.aes),
            (sessionID: UInt16(9999), token: UInt32(0x12345678), encrypt: EncryptionMethod.none)
        ]
        
        // Standard test IPv4 packet (matches Go backend test data)
        let standardIPPacket = Data([
            0x45, 0x00, 0x00, 0x1C, // Version, IHL, ToS, Total Length
            0x00, 0x01, 0x40, 0x00, // ID, Flags, Fragment Offset
            0x40, 0x01, 0x00, 0x00, // TTL, Protocol, Checksum
            0xC0, 0xA8, 0x01, 0x64, // Source IP: ***********00
            0xC0, 0xA8, 0x01, 0x01, // Dest IP: ***********
            0x48, 0x65, 0x6C, 0x6C, // Payload: "Hell"
            0x6F, 0x21, 0x00, 0x00  // Payload: "o!" + padding
        ])
        
        for (index, testCase) in testCases.enumerated() {
            print("Test case \(index + 1):")
            print("  - Session ID: \(testCase.sessionID)")
            print("  - Token: 0x\(String(format: "%08X", testCase.token))")
            print("  - Encryption: \(testCase.encrypt)")
            
            let packet: Data
            
            if testCase.encrypt == .none {
                // Unencrypted data packet
                let header = PacketHeader(
                    type: .data,
                    encrypt: .none,
                    sessionID: testCase.sessionID,
                    token: testCase.token
                )
                packet = packetBuilder.buildDataPacket(header: header, payload: standardIPPacket)
            } else {
                // Encrypted data packet
                let encryptor: any EncryptionService = testCase.encrypt == .xor ? xorEncryptor : aesEncryptor
                packet = try packetBuilder.buildDataEncryptPacket(
                    sessionID: testCase.sessionID,
                    token: testCase.token,
                    payload: standardIPPacket,
                    encryptionMethod: testCase.encrypt,
                    encryptor: encryptor
                )
            }
            
            // Verify basic structure
            let expectedType: UInt8 = testCase.encrypt == .none ? 0x14 : 0x18
            XCTAssertEqual(packet[0], expectedType, "Test case \(index): Packet type should match")
            XCTAssertEqual(packet[1], testCase.encrypt.rawValue, "Test case \(index): Encryption method should match")
            
            // Print packet for manual verification against Go backend
            let hexString = packet.prefix(32).map { String(format: "%02x", $0) }.joined(separator: " ")
            print("  - Packet header: \(hexString)")
            print("  ✅ Validated")
            print()
        }
        
        print("✓ Go backend compatibility tests completed")
    }
    
    // MARK: - Performance Tests
    
    func testDataPacketBuildingPerformance() throws {
        let ipPacket = Data(repeating: 0x42, count: 1400) // MTU-sized packet
        
        measure {
            for _ in 0..<1000 {
                do {
                    _ = try packetBuilder.buildDataEncryptPacket(
                        sessionID: 1234,
                        token: 0x56789ABC,
                        payload: ipPacket,
                        encryptionMethod: .aes,
                        encryptor: aesEncryptor
                    )
                } catch {
                    XCTFail("Packet building failed: \(error)")
                }
            }
        }
        
        print("✓ Performance test completed")
    }
}
