/**
 * FILE: SimpleHeartbeatTest.swift
 *
 * DESCRIPTION:
 *     Simple standalone test for heartbeat packet validation.
 *     Tests basic packet structure without complex dependencies.
 *
 * AUTHOR: wei
 * HISTORY: 30/06/2025 create simple heartbeat validation test
 */

import XCTest
@testable import ItForceCore

class SimpleHeartbeatTest: XCTestCase {
    
    func testBasicEchoPacketStructure() throws {
        let packetBuilder = PacketBuilder()
        
        // Test complete echo packet
        let header = PacketHeader(
            type: .echoRequest,
            encrypt: .xor,
            sessionID: 1234,
            token: 0x56789ABC
        )
        
        let packet = try packetBuilder.buildEchoPacket(
            header: header,
            currentDelay: 25,
            minDelay: 15,
            maxDelay: 50
        )
        
        // Basic structure validation
        XCTAssertEqual(packet.count, 48, "Echo packet should be 48 bytes")
        
        // Header validation
        XCTAssertEqual(packet[0], 0x15, "Should be ECHO_REQUEST")
        XCTAssertEqual(packet[1], 0x01, "Should be XOR encryption")
        
        // Session ID (big-endian)
        let sessionID = UInt16(packet[2]) << 8 | UInt16(packet[3])
        XCTAssertEqual(sessionID, 1234, "Session ID should match")
        
        // Token (big-endian)
        let token = UInt32(packet[4]) << 24 | UInt32(packet[5]) << 16 | UInt32(packet[6]) << 8 | UInt32(packet[7])
        XCTAssertEqual(token, 0x56789ABC, "Token should match")
        
        // Signature should be 16 bytes starting at offset 8
        let signatureRange = 8..<24
        XCTAssertEqual(signatureRange.count, 16, "Signature should be 16 bytes")
        
        // Timestamp should be 8 bytes starting at offset 24
        let timestampRange = 24..<32
        XCTAssertEqual(timestampRange.count, 8, "Timestamp should be 8 bytes")
        
        // Delay data should be 12 bytes starting at offset 32
        let delayRange = 32..<44
        XCTAssertEqual(delayRange.count, 12, "Delay data should be 12 bytes")
        
        // SDRT tag should be 4 bytes starting at offset 44
        let sdrtRange = 44..<48
        let sdrtData = packet.subdata(in: sdrtRange)
        let expectedSDRT = Data([0x53, 0x44, 0x52, 0x54]) // "SDRT"
        XCTAssertEqual(sdrtData, expectedSDRT, "SDRT tag should match")
        
        print("✓ Basic echo packet structure validated")
        print("  - Total size: \(packet.count) bytes")
        print("  - Header: 8 bytes")
        print("  - Signature: 16 bytes") 
        print("  - Timestamp: 8 bytes")
        print("  - Delays: 12 bytes")
        print("  - SDRT: 4 bytes")
        
        // Print hex dump for manual verification
        let hexString = packet.map { String(format: "%02x", $0) }.joined(separator: " ")
        print("  - Hex: \(hexString)")
    }
    
    func testClosePacketStructure() throws {
        let packetBuilder = PacketBuilder()
        
        let packet = try packetBuilder.buildClosePacket(
            sessionID: 9999,
            token: 0x12345678,
            encryptionMethod: .none
        )
        
        // Basic structure validation
        XCTAssertEqual(packet.count, 8, "Close packet should be 8 bytes")
        
        // Header validation
        XCTAssertEqual(packet[0], 0x17, "Should be CLOSE")
        XCTAssertEqual(packet[1], 0x00, "Should be no encryption")
        
        // Session ID (big-endian)
        let sessionID = UInt16(packet[2]) << 8 | UInt16(packet[3])
        XCTAssertEqual(sessionID, 9999, "Session ID should match")
        
        // Token (big-endian)
        let token = UInt32(packet[4]) << 24 | UInt32(packet[5]) << 16 | UInt32(packet[6]) << 8 | UInt32(packet[7])
        XCTAssertEqual(token, 0x12345678, "Token should match")
        
        print("✓ Close packet structure validated")
        print("  - Total size: \(packet.count) bytes")
        
        // Print hex dump
        let hexString = packet.map { String(format: "%02x", $0) }.joined(separator: " ")
        print("  - Hex: \(hexString)")
    }
    
    func testSimpleEchoRequestPacket() throws {
        let packetBuilder = PacketBuilder()
        
        let packet = try packetBuilder.buildEchoRequestPacket(
            sessionID: 5678,
            token: 0xDEADBEEF,
            encryptionMethod: .aes
        )
        
        // Basic structure validation
        XCTAssertEqual(packet.count, 32, "Simple echo request should be 32 bytes")
        
        // Header validation
        XCTAssertEqual(packet[0], 0x15, "Should be ECHO_REQUEST")
        XCTAssertEqual(packet[1], 0x02, "Should be AES encryption")
        
        print("✓ Simple echo request packet validated")
        print("  - Total size: \(packet.count) bytes")
        
        // Print hex dump
        let hexString = packet.map { String(format: "%02x", $0) }.joined(separator: " ")
        print("  - Hex: \(hexString)")
    }
    
    func testPacketSizeComparison() throws {
        let packetBuilder = PacketBuilder()
        
        let header = PacketHeader(
            type: .echoRequest,
            encrypt: .xor,
            sessionID: 1234,
            token: 0x56789ABC
        )
        
        // Simple echo request (32 bytes)
        let simplePacket = try packetBuilder.buildEchoRequestPacket(
            sessionID: header.sessionID,
            token: header.token,
            encryptionMethod: header.encrypt
        )
        
        // Complete echo packet (48 bytes)
        let completePacket = try packetBuilder.buildEchoPacket(
            header: header,
            currentDelay: 25,
            minDelay: 15,
            maxDelay: 50
        )
        
        XCTAssertEqual(simplePacket.count, 32, "Simple packet should be 32 bytes")
        XCTAssertEqual(completePacket.count, 48, "Complete packet should be 48 bytes")
        
        print("✓ Packet size comparison validated")
        print("  - Simple echo request: \(simplePacket.count) bytes")
        print("  - Complete echo packet: \(completePacket.count) bytes")
        print("  - Difference: \(completePacket.count - simplePacket.count) bytes (delay info + SDRT)")
    }
}
