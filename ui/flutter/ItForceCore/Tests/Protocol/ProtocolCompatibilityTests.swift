/**
 * FILE: ProtocolCompatibilityTests.swift
 *
 * DESCRIPTION:
 *     Comprehensive compatibility tests between Swift implementation and Go backend.
 *     Verifies protocol-level compatibility using known test vectors and edge cases.
 *
 * AUTHOR: wei
 * HISTORY: 26/06/2025 create
 */

import XCTest
import CryptoKit
import Network
@testable import ItForceCore

class ProtocolCompatibilityTests: XCTestCase {
    
    var packetBuilder: PacketBuilder!
    var packetParser: PacketParser!
    
    override func setUp() {
        super.setUp()
        packetBuilder = PacketBuilder()
        packetParser = PacketParser()
    }
    
    override func tearDown() {
        packetBuilder = nil
        packetParser = nil
        super.tearDown()
    }
    
    // MARK: - Go Backend Compatibility Test Vectors
    
    func testPasswordEncryptionGoCompatibility() throws {
        // Test vectors that should match Go backend encryptPasswordAES function
        let testVectors = [
            (username: "user1", password: "pass1"),
            (username: "testuser", password: "testpass"),
            (username: "admin", password: "admin123"),
            (username: "长用户名", password: "中文密码"), // Unicode test
            (username: "", password: ""), // Empty credentials
            (username: "user", password: String(repeating: "a", count: 50)), // Long password
        ]
        
        for vector in testVectors {
            // Test password encryption consistency
            let encrypted1 = try PasswordEncryption.encryptPassword(vector.password, username: vector.username)
            let encrypted2 = try PasswordEncryption.encryptPassword(vector.password, username: vector.username)
            
            XCTAssertEqual(encrypted1, encrypted2, 
                          "Password encryption should be deterministic for \(vector.username):\(vector.password)")
            XCTAssertEqual(encrypted1.count, 16, 
                          "Encrypted password should be exactly 16 bytes for \(vector.username):\(vector.password)")
            
            // Verify key generation matches Go: MD5("mw" + username)
            let expectedKey = Insecure.MD5.hash(data: ("mw" + vector.username).data(using: .utf8)!)
            let actualKey = try PasswordEncryption.generatePasswordKey(username: vector.username)
            XCTAssertEqual(actualKey, Data(expectedKey), 
                          "Password key should match Go implementation for username: \(vector.username)")
        }
    }
    
    func testSessionKeyGenerationGoCompatibility() throws {
        // Test vectors for session key generation
        let testVectors = [
            (username: "user1", password: "pass1"),
            (username: "testuser", password: "testpass"),
            (username: "admin", password: "admin123"),
            (username: "测试用户", password: "测试密码"), // Unicode test
        ]
        
        for vector in testVectors {
            let keyManager = KeyManager()
            try keyManager.generateSessionKey(username: vector.username, password: vector.password)
            let sessionKey = keyManager.getSessionKey()!
            
            // Verify session key matches Go: MD5(username + password)
            let expectedKey = Insecure.MD5.hash(data: (vector.username + vector.password).data(using: .utf8)!)
            XCTAssertEqual(sessionKey, Data(expectedKey), 
                          "Session key should match Go implementation for \(vector.username):\(vector.password)")
        }
    }
    
    func testPacketHeaderSerializationGoCompatibility() throws {
        // Test vectors for packet header serialization (Big Endian)
        let testVectors = [
            (type: PacketType.open, encrypt: EncryptionMethod.none, sessionID: UInt16(0), token: UInt32(0)),
            (type: PacketType.openAck, encrypt: EncryptionMethod.aes, sessionID: UInt16(1234), token: UInt32(0x56789ABC)),
            (type: PacketType.data, encrypt: EncryptionMethod.xor, sessionID: UInt16.max, token: UInt32.max),
            (type: PacketType.echoRequest, encrypt: EncryptionMethod.aes, sessionID: UInt16(0x1234), token: UInt32(0xDEADBEEF)),
        ]
        
        for vector in testVectors {
            let header = PacketHeader(
                type: vector.type,
                encrypt: vector.encrypt,
                sessionID: vector.sessionID,
                token: vector.token
            )
            
            let serialized = header.toData()
            XCTAssertEqual(serialized.count, 8, "Header should be exactly 8 bytes")
            
            // Verify Big Endian serialization
            XCTAssertEqual(serialized[0], vector.type.rawValue)
            XCTAssertEqual(serialized[1], vector.encrypt.rawValue)
            XCTAssertEqual(UInt16(bigEndian: serialized.withUnsafeBytes { $0.load(fromByteOffset: 2, as: UInt16.self) }), vector.sessionID)
            XCTAssertEqual(UInt32(bigEndian: serialized.withUnsafeBytes { $0.load(fromByteOffset: 4, as: UInt32.self) }), vector.token)
            
            // Verify round trip
            let deserialized = try PacketHeader(from: serialized)
            XCTAssertEqual(deserialized.type, vector.type)
            XCTAssertEqual(deserialized.encrypt, vector.encrypt)
            XCTAssertEqual(deserialized.sessionID, vector.sessionID)
            XCTAssertEqual(deserialized.token, vector.token)
        }
    }
    
    func testSignatureCalculationGoCompatibility() throws {
        // Test vectors for MD5 signature calculation
        let testVectors = [
            PacketHeader(type: .open, encrypt: .none, sessionID: 0, token: 0),
            PacketHeader(type: .echoRequest, encrypt: .aes, sessionID: 1234, token: 0x56789ABC),
            PacketHeader(type: .openAck, encrypt: .xor, sessionID: UInt16.max, token: UInt32.max),
        ]
        
        for header in testVectors {
            let signature = try packetBuilder.calculateSignature(for: header)
            
            // Verify signature is 16 bytes (MD5)
            XCTAssertEqual(signature.count, 16, "Signature should be 16 bytes (MD5)")
            
            // Verify signature calculation: MD5(header + "mw")
            let headerData = header.toData()
            let salt = Data([109, 119]) // ASCII 'm', 'w'
            let expectedSignature = Insecure.MD5.hash(data: headerData + salt)
            XCTAssertEqual(signature, Data(expectedSignature), 
                          "Signature should match Go implementation for header: \(header)")
            
            // Verify signature verification
            XCTAssertNoThrow(try packetParser.verifySignature(signature, for: header))
        }
    }
    
    // MARK: - OPEN Packet Compatibility Tests
    
    func testOpenPacketGoCompatibility() throws {
        // Test vectors for OPEN packet construction
        let testVectors = [
            (username: "user1", password: "pass1", mtu: UInt16(1420), encrypt: EncryptionMethod.aes),
            (username: "testuser", password: "testpass", mtu: UInt16(1500), encrypt: EncryptionMethod.xor),
            (username: "admin", password: "admin123", mtu: UInt16(576), encrypt: EncryptionMethod.none),
            (username: "", password: "", mtu: UInt16(1420), encrypt: EncryptionMethod.aes), // Edge case
        ]
        
        for vector in testVectors {
            let packet = try packetBuilder.buildOpenPacket(
                username: vector.username,
                password: vector.password,
                mtu: vector.mtu,
                encryptionMethod: vector.encrypt
            )
            
            // Parse the packet back
            let parsed = try packetParser.parsePacket(from: packet)
            
            // Verify header
            XCTAssertEqual(parsed.header.type, .open)
            XCTAssertEqual(parsed.header.encrypt, vector.encrypt)
            XCTAssertEqual(parsed.header.sessionID, 0) // OPEN packets have sessionID = 0
            XCTAssertEqual(parsed.header.token, 0) // OPEN packets have token = 0
            
            // Verify signature is present and valid
            XCTAssertNotNil(parsed.signature)
            XCTAssertNoThrow(try packetParser.verifySignature(parsed.signature!, for: parsed.header))
            
            // Verify TLV attributes are present and in correct order
            XCTAssertGreaterThan(parsed.attributes.count, 2) // At least MTU, Username, Password
            
            // Verify MTU attribute (should be first)
            let mtuAttr = parsed.attributes.first { $0.type == .mtu }
            XCTAssertNotNil(mtuAttr, "MTU attribute should be present")
            
            // Verify Username attribute (should be second)
            let usernameAttr = parsed.attributes.first { $0.type == .username }
            XCTAssertNotNil(usernameAttr, "Username attribute should be present")
            
            // Verify Password attribute (should be third)
            let passwordAttr = parsed.attributes.first { $0.type == .password }
            XCTAssertNotNil(passwordAttr, "Password attribute should be present")
            XCTAssertEqual(passwordAttr!.value.count, 16, "Password should be 16 bytes (encrypted)")
            
            // Verify Encrypt attribute (if encryption is not none)
            if vector.encrypt != .none {
                let encryptAttr = parsed.attributes.first { $0.type == .encrypt }
                XCTAssertNotNil(encryptAttr, "Encrypt attribute should be present for non-none encryption")
            }
        }
    }
    
    // MARK: - TLV Attribute Compatibility Tests
    
    func testTLVAttributeGoCompatibility() throws {
        // Test various TLV attributes for Go compatibility
        let testAttributes = [
            TLVAttribute(mtu: 1420),
            TLVAttribute(mtu: 576),
            TLVAttribute(mtu: UInt16.max),
            TLVAttribute(username: "testuser"),
            TLVAttribute(username: ""),
            TLVAttribute(username: "很长的用户名测试"),
            TLVAttribute(encryptionMethod: .none),
            TLVAttribute(encryptionMethod: .xor),
            TLVAttribute(encryptionMethod: .aes),
            TLVAttribute(keepAlive: 30),
            TLVAttribute(keepAlive: 0),
            TLVAttribute(keepAlive: UInt16.max),
        ]
        
        // Test IPv4 attributes
        if let ip = IPv4Address("*************") {
            testAttributes.append(TLVAttribute(ip: ip))
            testAttributes.append(TLVAttribute(gateway: ip))
            testAttributes.append(TLVAttribute(netmask: IPv4Address("*************")!))
        }
        
        if let dns1 = IPv4Address("*******"), let dns2 = IPv4Address("*******") {
            testAttributes.append(TLVAttribute(dns: [dns1, dns2]))
        }
        
        for attribute in testAttributes {
            // Test serialization
            let serialized = attribute.toData()
            
            // Verify TLV format: Type(1) + Length(1) + Value(n)
            XCTAssertGreaterThanOrEqual(serialized.count, 2, "TLV should have at least type and length")
            XCTAssertEqual(serialized[0], attribute.type.rawValue, "Type should match")
            XCTAssertEqual(serialized[1], attribute.length, "Length should match")
            
            // Test deserialization
            let deserialized = try TLVAttribute(from: serialized)
            XCTAssertEqual(deserialized.type, attribute.type, "Type should match after round trip")
            XCTAssertEqual(deserialized.length, attribute.length, "Length should match after round trip")
            XCTAssertEqual(deserialized.value, attribute.value, "Value should match after round trip")
        }
    }
    
    // MARK: - Data Packet Compatibility Tests
    
    func testDataPacketGoCompatibility() throws {
        // Test data packet format compatibility
        let testVectors = [
            (sessionID: UInt16(1), token: UInt32(123), encrypt: EncryptionMethod.none, payload: Data()),
            (sessionID: UInt16(1234), token: UInt32(0x56789ABC), encrypt: EncryptionMethod.aes, payload: "Hello".data(using: .utf8)!),
            (sessionID: UInt16.max, token: UInt32.max, encrypt: EncryptionMethod.xor, payload: Data(repeating: 0x42, count: 1500)),
        ]
        
        for vector in testVectors {
            let header = PacketHeader(
                type: .data,
                encrypt: vector.encrypt,
                sessionID: vector.sessionID,
                token: vector.token
            )
            
            let packet = try packetBuilder.buildDataPacket(header: header, payload: vector.payload)
            let parsed = try packetParser.parsePacket(from: packet)
            
            // Verify header
            XCTAssertEqual(parsed.header.type, .data)
            XCTAssertEqual(parsed.header.encrypt, vector.encrypt)
            XCTAssertEqual(parsed.header.sessionID, vector.sessionID)
            XCTAssertEqual(parsed.header.token, vector.token)
            
            // Verify payload
            XCTAssertEqual(parsed.payload, vector.payload)
            
            // Verify no signature or attributes for data packets
            XCTAssertNil(parsed.signature)
            XCTAssertTrue(parsed.attributes.isEmpty)
        }
    }
    
    // MARK: - Echo Packet Compatibility Tests
    
    func testEchoPacketGoCompatibility() throws {
        let testVectors = [
            (sessionID: UInt16(1), token: UInt32(123), encrypt: EncryptionMethod.aes),
            (sessionID: UInt16(1234), token: UInt32(0x56789ABC), encrypt: EncryptionMethod.xor),
            (sessionID: UInt16.max, token: UInt32.max, encrypt: EncryptionMethod.none),
        ]
        
        for vector in testVectors {
            let packet = try packetBuilder.buildEchoRequestPacket(
                sessionID: vector.sessionID,
                token: vector.token,
                encryptionMethod: vector.encrypt
            )
            
            let parsed = try packetParser.parsePacket(from: packet)
            
            // Verify header
            XCTAssertEqual(parsed.header.type, .echoRequest)
            XCTAssertEqual(parsed.header.encrypt, vector.encrypt)
            XCTAssertEqual(parsed.header.sessionID, vector.sessionID)
            XCTAssertEqual(parsed.header.token, vector.token)
            
            // Verify signature
            XCTAssertNotNil(parsed.signature)
            XCTAssertNoThrow(try packetParser.verifySignature(parsed.signature!, for: parsed.header))
            
            // Verify timestamp payload (8 bytes)
            XCTAssertEqual(parsed.payload.count, 8, "Echo request should have 8-byte timestamp")
        }
    }
    
    // MARK: - Edge Cases and Error Handling
    
    func testProtocolErrorCompatibility() {
        // Test that error conditions match Go backend behavior
        
        // Invalid packet type
        let invalidTypeData = Data([0xFF, 0x02, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC])
        XCTAssertThrowsError(try packetParser.parsePacket(from: invalidTypeData)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidPacketType)
        }
        
        // Invalid encryption method
        let invalidEncryptData = Data([0x13, 0xFF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC])
        XCTAssertThrowsError(try packetParser.parsePacket(from: invalidEncryptData)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidEncryptionMethod)
        }
        
        // Insufficient data
        let shortData = Data([0x13, 0x02, 0x12])
        XCTAssertThrowsError(try packetParser.parsePacket(from: shortData)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidHeaderSize)
        }
    }
    
    // MARK: - Performance and Stress Tests
    
    func testLargePacketCompatibility() throws {
        // Test with large packets to ensure no buffer overflow issues
        let largePayload = Data(repeating: 0x42, count: 65535) // Maximum possible payload
        let header = PacketHeader(type: .data, encrypt: .none, sessionID: 1, token: 123)
        
        let packet = try packetBuilder.buildDataPacket(header: header, payload: largePayload)
        let parsed = try packetParser.parsePacket(from: packet)
        
        XCTAssertEqual(parsed.payload, largePayload)
    }
    
    func testManyAttributesCompatibility() throws {
        // Test with many TLV attributes
        var attributes: [TLVAttribute] = []
        for i in 0..<100 {
            attributes.append(TLVAttribute(username: "user\(i)"))
        }
        
        let header = PacketHeader(type: .open, encrypt: .aes, sessionID: 0, token: 0)
        let packet = try packetBuilder.buildPacketWithSignature(header: header, attributes: attributes)
        let parsed = try packetParser.parsePacket(from: packet)
        
        XCTAssertEqual(parsed.attributes.count, attributes.count)
    }
}
