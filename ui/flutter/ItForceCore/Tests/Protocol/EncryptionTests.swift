/**
 * FILE: EncryptionTests.swift
 *
 * DESCRIPTION:
 *     Unit tests for encryption algorithms and key management.
 *     Verifies encryption compatibility with Go backend implementation.
 *
 * AUTHOR: wei
 * HISTORY: 26/06/2025 create
 */

import XCTest
import CryptoKit
@testable import ItForceCore

class EncryptionTests: XCTestCase {
    
    // MARK: - Password Encryption Tests
    
    func testPasswordEncryptionKeyGeneration() throws {
        let username = "testuser"
        let expectedKey = Insecure.MD5.hash(data: ("mw" + username).data(using: .utf8)!)
        
        let actualKey = try PasswordEncryption.generatePasswordKey(username: username)
        
        XCTAssertEqual(actualKey, Data(expectedKey))
    }
    
    func testPasswordEncryptionBasic() throws {
        let password = "testpass"
        let username = "testuser"
        
        let encryptedPassword = try PasswordEncryption.encryptPassword(password, username: username)
        
        // Verify encrypted password is exactly 16 bytes (first AES block)
        XCTAssertEqual(encryptedPassword.count, 16)
        
        // Verify it's not the original password
        XCTAssertNotEqual(encryptedPassword, password.data(using: .utf8))
    }
    
    func testPasswordEncryptionConsistency() throws {
        let password = "testpass"
        let username = "testuser"
        
        // Encrypt the same password multiple times
        let encrypted1 = try PasswordEncryption.encryptPassword(password, username: username)
        let encrypted2 = try PasswordEncryption.encryptPassword(password, username: username)
        
        // Results should be identical (deterministic encryption)
        XCTAssertEqual(encrypted1, encrypted2)
    }
    
    func testPasswordEncryptionDifferentUsernames() throws {
        let password = "testpass"
        let username1 = "user1"
        let username2 = "user2"
        
        let encrypted1 = try PasswordEncryption.encryptPassword(password, username: username1)
        let encrypted2 = try PasswordEncryption.encryptPassword(password, username: username2)
        
        // Different usernames should produce different encrypted passwords
        XCTAssertNotEqual(encrypted1, encrypted2)
    }
    
    func testPasswordEncryptionEmptyPassword() throws {
        let password = ""
        let username = "testuser"
        
        let encryptedPassword = try PasswordEncryption.encryptPassword(password, username: username)
        
        // Should still produce 16-byte result
        XCTAssertEqual(encryptedPassword.count, 16)
    }
    
    func testPasswordEncryptionLongPassword() throws {
        let password = String(repeating: "a", count: 100) // Longer than 32 bytes
        let username = "testuser"
        
        let encryptedPassword = try PasswordEncryption.encryptPassword(password, username: username)
        
        // Should still produce 16-byte result (truncated to first block)
        XCTAssertEqual(encryptedPassword.count, 16)
    }
    
    func testPasswordEncryptionSpecialCharacters() throws {
        let password = "test@#$%^&*()"
        let username = "user测试"
        
        let encryptedPassword = try PasswordEncryption.encryptPassword(password, username: username)
        
        XCTAssertEqual(encryptedPassword.count, 16)
    }
    
    // MARK: - AES Encryption Tests
    
    func testAESEncryptionInitialization() throws {
        let username = "testuser"
        let password = "testpass"
        
        let aesEncryption = try AESEncryption(username: username, password: password)
        
        // Verify session key is generated correctly
        let expectedSessionKey = Insecure.MD5.hash(data: (username + password).data(using: .utf8)!)
        let actualSessionKey = aesEncryption.keyManager.getSessionKey()
        
        XCTAssertEqual(actualSessionKey, Data(expectedSessionKey))
    }
    
    func testAESEncryptionBasic() throws {
        let username = "testuser"
        let password = "testpass"
        let aesEncryption = try AESEncryption(username: username, password: password)
        
        let plaintext = "Hello, World!".data(using: .utf8)!
        let encrypted = try aesEncryption.encrypt(plaintext)
        
        // Encrypted data should be different from plaintext
        XCTAssertNotEqual(encrypted, plaintext)
        
        // Encrypted data should be padded to block size (16 bytes)
        XCTAssertEqual(encrypted.count % 16, 0)
        XCTAssertGreaterThanOrEqual(encrypted.count, plaintext.count)
    }
    
    func testAESEncryptionDecryption() throws {
        let username = "testuser"
        let password = "testpass"
        let aesEncryption = try AESEncryption(username: username, password: password)
        
        let plaintext = "Hello, World! This is a test message.".data(using: .utf8)!
        let encrypted = try aesEncryption.encrypt(plaintext)
        let decrypted = try aesEncryption.decrypt(encrypted)
        
        // Decrypted data should match original plaintext
        XCTAssertEqual(decrypted.prefix(plaintext.count), plaintext)
    }
    
    func testAESEncryptionEmptyData() throws {
        let username = "testuser"
        let password = "testpass"
        let aesEncryption = try AESEncryption(username: username, password: password)
        
        let emptyData = Data()
        let encrypted = try aesEncryption.encrypt(emptyData)
        
        // Empty data should return empty data
        XCTAssertEqual(encrypted, emptyData)
    }
    
    func testAESEncryptionLargeData() throws {
        let username = "testuser"
        let password = "testpass"
        let aesEncryption = try AESEncryption(username: username, password: password)
        
        // Create large data (multiple blocks)
        let largeData = Data(repeating: 0x42, count: 1024)
        let encrypted = try aesEncryption.encrypt(largeData)
        let decrypted = try aesEncryption.decrypt(encrypted)
        
        XCTAssertEqual(decrypted.prefix(largeData.count), largeData)
    }
    
    func testAESEncryptionBlockAlignment() throws {
        let username = "testuser"
        let password = "testpass"
        let aesEncryption = try AESEncryption(username: username, password: password)
        
        // Test data that's exactly one block (16 bytes)
        let blockData = Data(repeating: 0x55, count: 16)
        let encrypted = try aesEncryption.encrypt(blockData)
        
        // Should be exactly one block (no padding needed)
        XCTAssertEqual(encrypted.count, 16)
        
        let decrypted = try aesEncryption.decrypt(encrypted)
        XCTAssertEqual(decrypted, blockData)
    }
    
    // MARK: - XOR Encryption Tests
    
    func testXOREncryptionInitialization() throws {
        let username = "testuser"
        let password = "testpass"
        
        let xorEncryption = try XOREncryption(username: username, password: password)
        
        // Verify session key is generated correctly
        let expectedSessionKey = Insecure.MD5.hash(data: (username + password).data(using: .utf8)!)
        let actualSessionKey = xorEncryption.keyManager.getSessionKey()
        
        XCTAssertEqual(actualSessionKey, Data(expectedSessionKey))
    }
    
    func testXOREncryptionBasic() throws {
        let username = "testuser"
        let password = "testpass"
        let xorEncryption = try XOREncryption(username: username, password: password)
        
        let plaintext = "Hello, World!".data(using: .utf8)!
        let encrypted = try xorEncryption.encrypt(plaintext)
        
        // Encrypted data should be different from plaintext
        XCTAssertNotEqual(encrypted, plaintext)
        
        // XOR encryption preserves data length
        XCTAssertEqual(encrypted.count, plaintext.count)
    }
    
    func testXOREncryptionDecryption() throws {
        let username = "testuser"
        let password = "testpass"
        let xorEncryption = try XOREncryption(username: username, password: password)
        
        let plaintext = "Hello, World! This is a test message.".data(using: .utf8)!
        let encrypted = try xorEncryption.encrypt(plaintext)
        let decrypted = try xorEncryption.decrypt(encrypted)
        
        // XOR is symmetric: decrypt(encrypt(data)) == data
        XCTAssertEqual(decrypted, plaintext)
    }
    
    func testXOREncryptionSymmetric() throws {
        let username = "testuser"
        let password = "testpass"
        let xorEncryption = try XOREncryption(username: username, password: password)
        
        let data = "Test data for XOR encryption".data(using: .utf8)!
        
        // XOR twice should return original data
        let encrypted = try xorEncryption.encrypt(data)
        let doubleEncrypted = try xorEncryption.encrypt(encrypted)
        
        XCTAssertEqual(doubleEncrypted, data)
    }
    
    func testXOREncryptionEmptyData() throws {
        let username = "testuser"
        let password = "testpass"
        let xorEncryption = try XOREncryption(username: username, password: password)
        
        let emptyData = Data()
        let encrypted = try xorEncryption.encrypt(emptyData)
        
        // Empty data should return empty data
        XCTAssertEqual(encrypted, emptyData)
    }
    
    // MARK: - Key Manager Tests
    
    func testKeyManagerSessionKeyGeneration() throws {
        let username = "testuser"
        let password = "testpass"
        let keyManager = KeyManager()
        
        try keyManager.generateSessionKey(username: username, password: password)
        
        let sessionKey = keyManager.getSessionKey()
        XCTAssertNotNil(sessionKey)
        XCTAssertEqual(sessionKey?.count, 16) // MD5 produces 16 bytes
        
        // Verify key is MD5(username + password)
        let expectedKey = Insecure.MD5.hash(data: (username + password).data(using: .utf8)!)
        XCTAssertEqual(sessionKey, Data(expectedKey))
    }
    
    func testKeyManagerPasswordKeyGeneration() throws {
        let username = "testuser"
        let keyManager = KeyManager()
        
        let passwordKey = try keyManager.generatePasswordKey(username: username)
        
        XCTAssertEqual(passwordKey.count, 16) // MD5 produces 16 bytes
        
        // Verify key is MD5("mw" + username)
        let expectedKey = Insecure.MD5.hash(data: ("mw" + username).data(using: .utf8)!)
        XCTAssertEqual(passwordKey, Data(expectedKey))
    }
    
    func testKeyManagerClearSessionKey() throws {
        let username = "testuser"
        let password = "testpass"
        let keyManager = KeyManager()
        
        try keyManager.generateSessionKey(username: username, password: password)
        XCTAssertNotNil(keyManager.getSessionKey())
        
        keyManager.clearSessionKey()
        XCTAssertNil(keyManager.getSessionKey())
    }
    
    // MARK: - Packet Encryption Tests
    
    func testPacketEncryptionDataPacket() throws {
        let username = "testuser"
        let password = "testpass"
        let aesEncryption = try AESEncryption(username: username, password: password)
        
        // Create a data packet
        let header = PacketHeader(type: .data, encrypt: .aes, sessionID: 1, token: 123)
        let payload = "IP packet data".data(using: .utf8)!
        let packet = ParsedPacket(header: header, payload: payload)
        
        let encryptedPacket = try aesEncryption.encryptPacket(packet)
        
        // Header should remain unchanged
        XCTAssertEqual(encryptedPacket.header.type, header.type)
        XCTAssertEqual(encryptedPacket.header.encrypt, header.encrypt)
        XCTAssertEqual(encryptedPacket.header.sessionID, header.sessionID)
        XCTAssertEqual(encryptedPacket.header.token, header.token)
        
        // Payload should be encrypted
        XCTAssertNotEqual(encryptedPacket.payload, payload)
    }
    
    func testPacketEncryptionControlPacket() throws {
        let username = "testuser"
        let password = "testpass"
        let aesEncryption = try AESEncryption(username: username, password: password)
        
        // Create a control packet (should not be encrypted)
        let header = PacketHeader(type: .echoRequest, encrypt: .aes, sessionID: 1, token: 123)
        let payload = "Echo request data".data(using: .utf8)!
        let packet = ParsedPacket(header: header, payload: payload)
        
        let result = try aesEncryption.encryptPacket(packet)
        
        // Control packets should not be encrypted
        XCTAssertEqual(result.header.type, header.type)
        XCTAssertEqual(result.payload, payload) // Payload unchanged
    }
    
    // MARK: - Compatibility Tests with Go Backend
    
    func testPasswordEncryptionGoCompatibility() throws {
        // Test vectors that should match Go backend implementation
        let testCases = [
            ("user1", "pass1"),
            ("testuser", "testpass"),
            ("admin", "admin123"),
            ("", ""), // Edge case: empty credentials
        ]
        
        for (username, password) in testCases {
            let encrypted1 = try PasswordEncryption.encryptPassword(password, username: username)
            let encrypted2 = try PasswordEncryption.encryptPassword(password, username: username)
            
            // Should be deterministic
            XCTAssertEqual(encrypted1, encrypted2, "Password encryption should be deterministic for \(username):\(password)")
            
            // Should be exactly 16 bytes
            XCTAssertEqual(encrypted1.count, 16, "Encrypted password should be 16 bytes for \(username):\(password)")
        }
    }
    
    func testSessionKeyGenerationGoCompatibility() throws {
        // Test vectors that should match Go backend implementation
        let testCases = [
            ("user1", "pass1"),
            ("testuser", "testpass"),
            ("admin", "admin123"),
        ]
        
        for (username, password) in testCases {
            let keyManager = KeyManager()
            try keyManager.generateSessionKey(username: username, password: password)
            let sessionKey = keyManager.getSessionKey()!
            
            // Verify key generation matches Go: MD5(username + password)
            let expectedKey = Insecure.MD5.hash(data: (username + password).data(using: .utf8)!)
            XCTAssertEqual(sessionKey, Data(expectedKey), "Session key should match Go implementation for \(username):\(password)")
        }
    }
}
