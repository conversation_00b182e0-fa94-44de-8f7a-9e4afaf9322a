/**
 * FILE: HeartbeatPacketTests.swift
 *
 * DESCRIPTION:
 *     Unit tests for heartbeat packet construction and validation.
 *     Verifies Swift implementation matches Go backend format exactly.
 *     Tests both simplified and complete heartbeat packet formats.
 *
 * AUTHOR: wei
 * HISTORY: 30/06/2025 create heartbeat packet validation tests
 */

import XCTest
@testable import ItForceCore

class HeartbeatPacketTests: XCTestCase {
    
    private var packetBuilder: PacketBuilder!
    
    override func setUp() {
        super.setUp()
        packetBuilder = PacketBuilder()
    }
    
    override func tearDown() {
        packetBuilder = nil
        super.tearDown()
    }
    
    // MARK: - Echo Request Packet Tests
    
    func testBuildEchoRequestPacketSimple() throws {
        // Test simplified echo request packet (32 bytes)
        let sessionID: UInt16 = 1234
        let token: UInt32 = 0x56789ABC
        let encryptionMethod = EncryptionMethod.xor
        
        let packet = try packetBuilder.buildEchoRequestPacket(
            sessionID: sessionID,
            token: token,
            encryptionMethod: encryptionMethod
        )
        
        // Verify packet structure
        XCTAssertEqual(packet.count, 32, "Simple echo request packet should be 32 bytes")
        
        // Verify header (first 8 bytes)
        XCTAssertEqual(packet[0], 0x15, "Packet type should be ECHO_REQUEST (0x15)")
        XCTAssertEqual(packet[1], 0x01, "Encryption method should be XOR (0x01)")
        
        // Verify session ID (bytes 2-3, big-endian)
        let packetSessionID = UInt16(packet[2]) << 8 | UInt16(packet[3])
        XCTAssertEqual(packetSessionID, sessionID, "Session ID should match")
        
        // Verify token (bytes 4-7, big-endian)
        let packetToken = UInt32(packet[4]) << 24 | UInt32(packet[5]) << 16 | UInt32(packet[6]) << 8 | UInt32(packet[7])
        XCTAssertEqual(packetToken, token, "Token should match")
        
        // Verify signature exists (bytes 8-23)
        let signature = packet.subdata(in: 8..<24)
        XCTAssertEqual(signature.count, 16, "MD5 signature should be 16 bytes")
        
        // Verify timestamp exists (bytes 24-31)
        let timestampData = packet.subdata(in: 24..<32)
        XCTAssertEqual(timestampData.count, 8, "Timestamp should be 8 bytes")
        
        print("✓ Simple echo request packet structure validated")
    }
    
    func testBuildEchoPacketComplete() throws {
        // Test complete echo packet with delay information (48 bytes)
        let header = PacketHeader(
            type: .echoRequest,
            encrypt: .aes,
            sessionID: 5678,
            token: 0xDEADBEEF
        )
        
        let currentDelay: UInt32 = 25
        let minDelay: UInt32 = 15
        let maxDelay: UInt32 = 50
        
        let packet = try packetBuilder.buildEchoPacket(
            header: header,
            currentDelay: currentDelay,
            minDelay: minDelay,
            maxDelay: maxDelay
        )
        
        // Verify packet structure
        XCTAssertEqual(packet.count, 48, "Complete echo packet should be 48 bytes")
        
        // Verify header (first 8 bytes)
        XCTAssertEqual(packet[0], 0x15, "Packet type should be ECHO_REQUEST (0x15)")
        XCTAssertEqual(packet[1], 0x02, "Encryption method should be AES (0x02)")
        
        // Verify session ID (bytes 2-3, big-endian)
        let packetSessionID = UInt16(packet[2]) << 8 | UInt16(packet[3])
        XCTAssertEqual(packetSessionID, 5678, "Session ID should match")
        
        // Verify token (bytes 4-7, big-endian)
        let packetToken = UInt32(packet[4]) << 24 | UInt32(packet[5]) << 16 | UInt32(packet[6]) << 8 | UInt32(packet[7])
        XCTAssertEqual(packetToken, 0xDEADBEEF, "Token should match")
        
        // Verify signature (bytes 8-23)
        let signature = packet.subdata(in: 8..<24)
        XCTAssertEqual(signature.count, 16, "MD5 signature should be 16 bytes")
        
        // Verify timestamp (bytes 24-31)
        let timestampData = packet.subdata(in: 24..<32)
        XCTAssertEqual(timestampData.count, 8, "Timestamp should be 8 bytes")
        
        // Verify delay information (bytes 32-43)
        let currentDelayBytes = packet.subdata(in: 32..<36)
        let minDelayBytes = packet.subdata(in: 36..<40)
        let maxDelayBytes = packet.subdata(in: 40..<44)
        
        // Convert back to UInt32 (big-endian)
        let packetCurrentDelay = currentDelayBytes.withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }
        let packetMinDelay = minDelayBytes.withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }
        let packetMaxDelay = maxDelayBytes.withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }
        
        XCTAssertEqual(packetCurrentDelay, currentDelay, "Current delay should match")
        XCTAssertEqual(packetMinDelay, minDelay, "Min delay should match")
        XCTAssertEqual(packetMaxDelay, maxDelay, "Max delay should match")
        
        // Verify SDRT tag (bytes 44-47)
        let sdrtTag = packet.subdata(in: 44..<48)
        let expectedSDRT = Data([0x53, 0x44, 0x52, 0x54]) // "SDRT"
        XCTAssertEqual(sdrtTag, expectedSDRT, "SDRT tag should match")
        
        print("✓ Complete echo packet structure validated")
        print("  - Total size: \(packet.count) bytes")
        print("  - Header: 8 bytes")
        print("  - Signature: 16 bytes")
        print("  - Timestamp: 8 bytes")
        print("  - Delays: 12 bytes")
        print("  - SDRT: 4 bytes")
    }
    
    // MARK: - Close Packet Tests
    
    func testBuildClosePacket() throws {
        let sessionID: UInt16 = 9999
        let token: UInt32 = 0x12345678
        let encryptionMethod = EncryptionMethod.none
        
        let packet = try packetBuilder.buildClosePacket(
            sessionID: sessionID,
            token: token,
            encryptionMethod: encryptionMethod
        )
        
        // Verify packet structure
        XCTAssertEqual(packet.count, 8, "Close packet should be 8 bytes (header only)")
        
        // Verify header
        XCTAssertEqual(packet[0], 0x17, "Packet type should be CLOSE (0x17)")
        XCTAssertEqual(packet[1], 0x00, "Encryption method should be NONE (0x00)")
        
        // Verify session ID (bytes 2-3, big-endian)
        let packetSessionID = UInt16(packet[2]) << 8 | UInt16(packet[3])
        XCTAssertEqual(packetSessionID, sessionID, "Session ID should match")
        
        // Verify token (bytes 4-7, big-endian)
        let packetToken = UInt32(packet[4]) << 24 | UInt32(packet[5]) << 16 | UInt32(packet[6]) << 8 | UInt32(packet[7])
        XCTAssertEqual(packetToken, token, "Token should match")
        
        print("✓ Close packet structure validated")
    }
    
    // MARK: - Go Backend Compatibility Tests
    
    func testGoBackendCompatibility() throws {
        // Test packet format compatibility with Go backend
        // This test generates packets that should match Go backend exactly
        
        let testCases = [
            (sessionID: UInt16(1234), token: UInt32(0x56789ABC), encrypt: EncryptionMethod.xor),
            (sessionID: UInt16(5678), token: UInt32(0xDEADBEEF), encrypt: EncryptionMethod.aes),
            (sessionID: UInt16(0), token: UInt32(0), encrypt: EncryptionMethod.none)
        ]
        
        for (index, testCase) in testCases.enumerated() {
            let header = PacketHeader(
                type: .echoRequest,
                encrypt: testCase.encrypt,
                sessionID: testCase.sessionID,
                token: testCase.token
            )
            
            let packet = try packetBuilder.buildEchoPacket(
                header: header,
                currentDelay: 30,
                minDelay: 25,
                maxDelay: 35
            )
            
            // Verify basic structure
            XCTAssertEqual(packet.count, 48, "Test case \(index): Packet should be 48 bytes")
            XCTAssertEqual(packet[0], 0x15, "Test case \(index): Should be ECHO_REQUEST")
            XCTAssertEqual(packet[1], testCase.encrypt.rawValue, "Test case \(index): Encryption method should match")
            
            // Print packet for manual verification against Go backend
            let hexString = packet.map { String(format: "%02x", $0) }.joined(separator: " ")
            print("Test case \(index) packet: \(hexString)")
        }
        
        print("✓ Go backend compatibility tests completed")
    }
    
    // MARK: - Performance Tests
    
    func testPacketBuildingPerformance() throws {
        let header = PacketHeader(
            type: .echoRequest,
            encrypt: .aes,
            sessionID: 1234,
            token: 0x56789ABC
        )
        
        measure {
            for _ in 0..<1000 {
                do {
                    _ = try packetBuilder.buildEchoPacket(
                        header: header,
                        currentDelay: 25,
                        minDelay: 15,
                        maxDelay: 50
                    )
                } catch {
                    XCTFail("Packet building failed: \(error)")
                }
            }
        }
        
        print("✓ Performance test completed")
    }
}
