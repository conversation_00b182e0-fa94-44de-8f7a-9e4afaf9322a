/**
 * FILE: TLVAttributeTests.swift
 *
 * DESCRIPTION:
 *     Unit tests for TLV attribute structure and encoding/decoding.
 *     Verifies TLV attribute compatibility with Go backend implementation.
 *
 * AUTHOR: wei
 * HISTORY: 26/06/2025 create
 */

import XCTest
import Network
@testable import ItForceCore

class TLVAttributeTests: XCTestCase {
    
    // MARK: - TLVAttributeType Tests
    
    func testTLVAttributeTypeRawValues() {
        // Test that TLV attribute type raw values match Go backend constants
        XCTAssertEqual(TLVAttributeType.username.rawValue, 0x01)
        XCTAssertEqual(TLVAttributeType.password.rawValue, 0x02)
        XCTAssertEqual(TLVAttributeType.mtu.rawValue, 0x03)
        XCTAssertEqual(TLVAttributeType.ip.rawValue, 0x04)
        XCTAssertEqual(TLVAttributeType.dns.rawValue, 0x05)
        XCTAssertEqual(TLVAttributeType.gateway.rawValue, 0x06)
        XCTAssertEqual(TLVAttributeType.netmask.rawValue, 0x07)
        XCTAssertEqual(TLVAttributeType.encrypt.rawValue, 0x08)
        XCTAssertEqual(TLVAttributeType.dupPkt.rawValue, 0x09)
        XCTAssertEqual(TLVAttributeType.link.rawValue, 0x0A)
        XCTAssertEqual(TLVAttributeType.ip6.rawValue, 0x0B)
        XCTAssertEqual(TLVAttributeType.dns6.rawValue, 0x0C)
        XCTAssertEqual(TLVAttributeType.gateway6.rawValue, 0x0D)
        XCTAssertEqual(TLVAttributeType.serverConfig.rawValue, 0x0E)
        XCTAssertEqual(TLVAttributeType.keepAlive.rawValue, 0x0F)
        XCTAssertEqual(TLVAttributeType.rejectReason.rawValue, 0x10)
    }
    
    func testTLVAttributeTypeExpectedLength() {
        // Test expected lengths for fixed-size attributes
        XCTAssertEqual(TLVAttributeType.ip.expectedLength, 6)      // Type(1) + Length(1) + IPv4(4)
        XCTAssertEqual(TLVAttributeType.gateway.expectedLength, 6)
        XCTAssertEqual(TLVAttributeType.netmask.expectedLength, 6)
        XCTAssertEqual(TLVAttributeType.dns.expectedLength, 10)    // Type(1) + Length(1) + IPv4(4) * 2
        XCTAssertEqual(TLVAttributeType.mtu.expectedLength, 4)     // Type(1) + Length(1) + Value(2)
        XCTAssertEqual(TLVAttributeType.keepAlive.expectedLength, 4)
        XCTAssertEqual(TLVAttributeType.ip6.expectedLength, 18)    // Type(1) + Length(1) + IPv6(16)
        XCTAssertEqual(TLVAttributeType.dns6.expectedLength, 18)
        XCTAssertEqual(TLVAttributeType.gateway6.expectedLength, 18)
        XCTAssertEqual(TLVAttributeType.encrypt.expectedLength, 3) // Type(1) + Length(1) + Value(1)
        XCTAssertEqual(TLVAttributeType.dupPkt.expectedLength, 3)
        XCTAssertEqual(TLVAttributeType.rejectReason.expectedLength, 3)
        
        // Test variable length attributes
        XCTAssertEqual(TLVAttributeType.username.expectedLength, 0)
        XCTAssertEqual(TLVAttributeType.password.expectedLength, 0)
        XCTAssertEqual(TLVAttributeType.link.expectedLength, 0)
        XCTAssertEqual(TLVAttributeType.serverConfig.expectedLength, 0)
    }
    
    func testTLVAttributeTypeDescription() {
        XCTAssertEqual(TLVAttributeType.username.description, "USERNAME")
        XCTAssertEqual(TLVAttributeType.password.description, "PASSWORD")
        XCTAssertEqual(TLVAttributeType.mtu.description, "MTU")
        XCTAssertEqual(TLVAttributeType.ip.description, "IP")
        XCTAssertEqual(TLVAttributeType.dns.description, "DNS")
        XCTAssertEqual(TLVAttributeType.gateway.description, "GATEWAY")
        XCTAssertEqual(TLVAttributeType.encrypt.description, "ENCRYPT")
    }
    
    // MARK: - TLVAttribute Creation Tests
    
    func testTLVAttributeUsernameCreation() {
        let username = "testuser"
        let attribute = TLVAttribute(username: username)
        
        XCTAssertEqual(attribute.type, .username)
        XCTAssertEqual(attribute.value, username.data(using: .utf8))
        XCTAssertEqual(attribute.length, UInt8(username.utf8.count + 2)) // +2 for type and length
    }
    
    func testTLVAttributeEncryptedPasswordCreation() {
        let encryptedPassword = Data([0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
                                     0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10])
        let attribute = TLVAttribute(encryptedPassword: encryptedPassword)
        
        XCTAssertEqual(attribute.type, .password)
        XCTAssertEqual(attribute.value, encryptedPassword)
        XCTAssertEqual(attribute.length, UInt8(encryptedPassword.count + 2))
    }
    
    func testTLVAttributeMTUCreation() {
        let mtu: UInt16 = 1420
        let attribute = TLVAttribute(mtu: mtu)
        
        XCTAssertEqual(attribute.type, .mtu)
        XCTAssertEqual(attribute.length, 4) // Type(1) + Length(1) + Value(2)
        
        // Verify MTU value in Big Endian format
        let expectedData = Data([0x05, 0x8C]) // 1420 in Big Endian
        XCTAssertEqual(attribute.value, expectedData)
    }
    
    func testTLVAttributeIPCreation() throws {
        let ipAddress = try IPv4Address("*************")!
        let attribute = TLVAttribute(ip: ipAddress)
        
        XCTAssertEqual(attribute.type, .ip)
        XCTAssertEqual(attribute.length, 6) // Type(1) + Length(1) + IPv4(4)
        
        // Verify IP address bytes
        let expectedData = Data([192, 168, 1, 100])
        XCTAssertEqual(attribute.value, expectedData)
    }
    
    func testTLVAttributeDNSCreation() throws {
        let dns1 = try IPv4Address("*******")!
        let dns2 = try IPv4Address("*******")!
        let attribute = TLVAttribute(dns: [dns1, dns2])
        
        XCTAssertEqual(attribute.type, .dns)
        XCTAssertEqual(attribute.length, 10) // Type(1) + Length(1) + IPv4(4) * 2
        
        // Verify DNS server bytes
        let expectedData = Data([8, 8, 8, 8, 8, 8, 4, 4])
        XCTAssertEqual(attribute.value, expectedData)
    }
    
    func testTLVAttributeGatewayCreation() throws {
        let gateway = try IPv4Address("***********")!
        let attribute = TLVAttribute(gateway: gateway)
        
        XCTAssertEqual(attribute.type, .gateway)
        XCTAssertEqual(attribute.length, 6) // Type(1) + Length(1) + IPv4(4)
        
        // Verify gateway bytes
        let expectedData = Data([192, 168, 1, 1])
        XCTAssertEqual(attribute.value, expectedData)
    }
    
    func testTLVAttributeNetmaskCreation() throws {
        let netmask = try IPv4Address("*************")!
        let attribute = TLVAttribute(netmask: netmask)
        
        XCTAssertEqual(attribute.type, .netmask)
        XCTAssertEqual(attribute.length, 6) // Type(1) + Length(1) + IPv4(4)
        
        // Verify netmask bytes
        let expectedData = Data([255, 255, 255, 0])
        XCTAssertEqual(attribute.value, expectedData)
    }
    
    func testTLVAttributeEncryptionMethodCreation() {
        let attribute = TLVAttribute(encryptionMethod: .aes)
        
        XCTAssertEqual(attribute.type, .encrypt)
        XCTAssertEqual(attribute.length, 3) // Type(1) + Length(1) + Value(1)
        
        // Verify encryption method value
        let expectedData = Data([0x02]) // EncryptionMethod.aes
        XCTAssertEqual(attribute.value, expectedData)
    }
    
    func testTLVAttributeKeepAliveCreation() {
        let keepAlive: UInt16 = 30
        let attribute = TLVAttribute(keepAlive: keepAlive)
        
        XCTAssertEqual(attribute.type, .keepAlive)
        XCTAssertEqual(attribute.length, 4) // Type(1) + Length(1) + Value(2)
        
        // Verify keep alive value in Big Endian format
        let expectedData = Data([0x00, 0x1E]) // 30 in Big Endian
        XCTAssertEqual(attribute.value, expectedData)
    }
    
    // MARK: - TLVAttribute Serialization Tests
    
    func testTLVAttributeSerialization() {
        let username = "testuser"
        let attribute = TLVAttribute(username: username)
        let data = attribute.toData()
        
        // Verify serialized format: Type(1) + Length(1) + Value(n)
        XCTAssertEqual(data[0], 0x01) // TLVAttributeType.username
        XCTAssertEqual(data[1], UInt8(username.utf8.count + 2)) // Length including type and length bytes
        
        let valueData = data.dropFirst(2)
        XCTAssertEqual(valueData, username.data(using: .utf8))
    }
    
    func testTLVAttributeSerializationMTU() {
        let mtu: UInt16 = 1500
        let attribute = TLVAttribute(mtu: mtu)
        let data = attribute.toData()
        
        XCTAssertEqual(data.count, 4)
        XCTAssertEqual(data[0], 0x03) // TLVAttributeType.mtu
        XCTAssertEqual(data[1], 0x04) // Length = 4 (type + length + 2-byte value)
        XCTAssertEqual(data[2], 0x05) // MTU high byte (1500 = 0x05DC)
        XCTAssertEqual(data[3], 0xDC) // MTU low byte
    }
    
    // MARK: - TLVAttribute Deserialization Tests
    
    func testTLVAttributeDeserialization() throws {
        // Create test data for username attribute
        let username = "testuser"
        let usernameData = username.data(using: .utf8)!
        var data = Data()
        data.append(0x01) // TLVAttributeType.username
        data.append(UInt8(usernameData.count + 2)) // Length
        data.append(usernameData)
        
        let attribute = try TLVAttribute(from: data)
        
        XCTAssertEqual(attribute.type, .username)
        XCTAssertEqual(attribute.length, UInt8(usernameData.count + 2))
        XCTAssertEqual(attribute.value, usernameData)
    }
    
    func testTLVAttributeDeserializationMTU() throws {
        // Create test data for MTU attribute
        let data = Data([
            0x03, // TLVAttributeType.mtu
            0x04, // Length = 4
            0x05, 0xDC // MTU = 1500 in Big Endian
        ])
        
        let attribute = try TLVAttribute(from: data)
        
        XCTAssertEqual(attribute.type, .mtu)
        XCTAssertEqual(attribute.length, 4)
        
        // Verify MTU value
        let mtuValue = attribute.value.withUnsafeBytes { $0.load(as: UInt16.self).bigEndian }
        XCTAssertEqual(mtuValue, 1500)
    }
    
    func testTLVAttributeDeserializationInvalidLength() {
        // Test with insufficient data
        let data = Data([0x01, 0x05, 0x74, 0x65]) // Claims length 5 but only has 2 value bytes
        
        XCTAssertThrowsError(try TLVAttribute(from: data)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidTLVLength)
        }
    }
    
    func testTLVAttributeDeserializationInvalidType() {
        // Test with invalid attribute type
        let data = Data([0xFF, 0x03, 0x01]) // Invalid type 0xFF
        
        XCTAssertThrowsError(try TLVAttribute(from: data)) { error in
            XCTAssertEqual(error as? ProtocolError, ProtocolError.invalidTLVType)
        }
    }
    
    // MARK: - TLVAttribute Round Trip Tests
    
    func testTLVAttributeRoundTrip() throws {
        let originalAttribute = TLVAttribute(username: "testuser123")
        let data = originalAttribute.toData()
        let deserializedAttribute = try TLVAttribute(from: data)
        
        XCTAssertEqual(originalAttribute.type, deserializedAttribute.type)
        XCTAssertEqual(originalAttribute.length, deserializedAttribute.length)
        XCTAssertEqual(originalAttribute.value, deserializedAttribute.value)
    }
    
    func testTLVAttributeRoundTripMTU() throws {
        let originalAttribute = TLVAttribute(mtu: 1420)
        let data = originalAttribute.toData()
        let deserializedAttribute = try TLVAttribute(from: data)
        
        XCTAssertEqual(originalAttribute.type, deserializedAttribute.type)
        XCTAssertEqual(originalAttribute.length, deserializedAttribute.length)
        XCTAssertEqual(originalAttribute.value, deserializedAttribute.value)
    }
    
    // MARK: - Edge Cases
    
    func testTLVAttributeEmptyUsername() {
        let attribute = TLVAttribute(username: "")
        
        XCTAssertEqual(attribute.type, .username)
        XCTAssertEqual(attribute.length, 2) // Type(1) + Length(1) + empty value
        XCTAssertEqual(attribute.value, Data())
    }
    
    func testTLVAttributeLongUsername() {
        let longUsername = String(repeating: "a", count: 200)
        let attribute = TLVAttribute(username: longUsername)
        
        XCTAssertEqual(attribute.type, .username)
        XCTAssertEqual(attribute.length, UInt8(202)) // Type(1) + Length(1) + 200 chars
        XCTAssertEqual(attribute.value.count, 200)
    }
    
    func testTLVAttributeZeroMTU() {
        let attribute = TLVAttribute(mtu: 0)
        
        XCTAssertEqual(attribute.type, .mtu)
        XCTAssertEqual(attribute.length, 4)
        
        let expectedData = Data([0x00, 0x00]) // 0 in Big Endian
        XCTAssertEqual(attribute.value, expectedData)
    }
    
    func testTLVAttributeMaxMTU() {
        let attribute = TLVAttribute(mtu: UInt16.max)
        
        XCTAssertEqual(attribute.type, .mtu)
        XCTAssertEqual(attribute.length, 4)
        
        let expectedData = Data([0xFF, 0xFF]) // UInt16.max in Big Endian
        XCTAssertEqual(attribute.value, expectedData)
    }
}
