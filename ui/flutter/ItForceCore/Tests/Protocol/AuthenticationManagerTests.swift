/**
 * FILE: AuthenticationManagerTests.swift
 *
 * DESCRIPTION:
 *     Unit tests for AuthenticationManager and authentication flow.
 *     Verifies authentication process compatibility with Go backend.
 *
 * AUTHOR: wei
 * HISTORY: 26/06/2025 create
 */

import XCTest
import Network
@testable import ItForceCore

class AuthenticationManagerTests: XCTestCase {
    
    var authManager: AuthenticationManager!
    
    override func setUp() {
        super.setUp()
        authManager = AuthenticationManager()
    }
    
    override func tearDown() {
        authManager = nil
        super.tearDown()
    }
    
    // MARK: - Authentication State Tests
    
    func testInitialState() async {
        let state = await authManager.getState()
        XCTAssertEqual(state, .idle)
    }
    
    func testStateTransitions() async {
        // Initial state should be idle
        var state = await authManager.getState()
        XCTAssertEqual(state, .idle)
        
        // After starting authentication, state should change
        // Note: This test would require a mock server for full testing
        // For now, we test the state management logic
    }
    
    func testSessionInfoInitiallyNil() async {
        let sessionInfo = await authManager.getSessionInfo()
        XCTAssertNil(sessionInfo)
    }
    
    // MARK: - Authentication Request Building Tests
    
    func testAuthenticationRequestCreation() async throws {
        // Test that authentication manager can create proper OPEN packets
        let username = "testuser"
        let password = "testpass"
        let serverAddress = "***********"
        let serverPort = 8080
        let mtu = 1420
        let encryptionMethod = EncryptionMethod.aes
        
        // We can't test the full authentication without a server,
        // but we can test that the packet building logic works
        let packetBuilder = PacketBuilder()
        let openPacket = try packetBuilder.buildOpenPacket(
            username: username,
            password: password,
            mtu: UInt16(mtu),
            encryptionMethod: encryptionMethod
        )
        
        // Verify the packet is properly formed
        XCTAssertGreaterThan(openPacket.count, 8 + 16) // Header + signature + attributes
        
        // Parse and verify the packet
        let packetParser = PacketParser()
        let parsed = try packetParser.parsePacket(from: openPacket)
        
        XCTAssertEqual(parsed.header.type, .open)
        XCTAssertEqual(parsed.header.encrypt, encryptionMethod)
        XCTAssertEqual(parsed.header.sessionID, 0)
        XCTAssertEqual(parsed.header.token, 0)
        XCTAssertNotNil(parsed.signature)
        XCTAssertGreaterThan(parsed.attributes.count, 0)
    }
    
    // MARK: - Authentication Result Processing Tests
    
    func testAuthenticationResultSuccess() {
        let result = AuthenticationResult(
            success: true,
            sessionID: 1234,
            token: 0x56789ABC,
            serverConfig: [:],
            errorCode: nil,
            errorMessage: nil
        )
        
        XCTAssertTrue(result.success)
        XCTAssertEqual(result.sessionID, 1234)
        XCTAssertEqual(result.token, 0x56789ABC)
        XCTAssertNil(result.errorCode)
        XCTAssertNil(result.errorMessage)
    }
    
    func testAuthenticationResultFailure() {
        let result = AuthenticationResult(
            success: false,
            sessionID: nil,
            token: nil,
            serverConfig: [:],
            errorCode: 2, // Authentication failed
            errorMessage: "Invalid credentials"
        )
        
        XCTAssertFalse(result.success)
        XCTAssertNil(result.sessionID)
        XCTAssertNil(result.token)
        XCTAssertEqual(result.errorCode, 2)
        XCTAssertEqual(result.errorMessage, "Invalid credentials")
    }
    
    // MARK: - Mock Authentication Tests
    
    func testMockAuthenticationFlow() async throws {
        // Create a mock authentication scenario
        let username = "testuser"
        let password = "testpass"
        
        // Test packet creation
        let packetBuilder = PacketBuilder()
        let openPacket = try packetBuilder.buildOpenPacket(
            username: username,
            password: password,
            mtu: 1420,
            encryptionMethod: .aes
        )
        
        // Simulate server response (OpenAck packet)
        let responseHeader = PacketHeader(
            type: .openAck,
            encrypt: .aes,
            sessionID: 1234,
            token: 0x56789ABC
        )
        
        // Create mock server configuration attributes
        let serverAttributes = [
            TLVAttribute(ip: IPv4Address("**********")!),
            TLVAttribute(gateway: IPv4Address("********")!),
            TLVAttribute(dns: [IPv4Address("*******")!, IPv4Address("*******")!]),
            TLVAttribute(mtu: 1420)
        ]
        
        let responsePacket = try packetBuilder.buildPacketWithSignature(
            header: responseHeader,
            attributes: serverAttributes
        )
        
        // Parse the response
        let packetParser = PacketParser()
        let parsedResponse = try packetParser.parsePacket(from: responsePacket)
        
        // Verify response structure
        XCTAssertEqual(parsedResponse.header.type, .openAck)
        XCTAssertEqual(parsedResponse.header.sessionID, 1234)
        XCTAssertEqual(parsedResponse.header.token, 0x56789ABC)
        XCTAssertNotNil(parsedResponse.signature)
        XCTAssertGreaterThan(parsedResponse.attributes.count, 0)
        
        // Verify server configuration attributes
        let ipAttr = parsedResponse.attributes.first { $0.type == .ip }
        XCTAssertNotNil(ipAttr)
        
        let gatewayAttr = parsedResponse.attributes.first { $0.type == .gateway }
        XCTAssertNotNil(gatewayAttr)
        
        let dnsAttr = parsedResponse.attributes.first { $0.type == .dns }
        XCTAssertNotNil(dnsAttr)
        
        let mtuAttr = parsedResponse.attributes.first { $0.type == .mtu }
        XCTAssertNotNil(mtuAttr)
    }
    
    // MARK: - Authentication Error Handling Tests
    
    func testAuthenticationRejectionHandling() async throws {
        // Test handling of OpenReject packets
        let rejectHeader = PacketHeader(
            type: .openReject,
            encrypt: .none,
            sessionID: 0,
            token: 0
        )
        
        // Create rejection packet with reason
        let rejectAttributes = [
            TLVAttribute(type: .rejectReason, length: 3, value: Data([0x02])) // Invalid credentials
        ]
        
        let packetBuilder = PacketBuilder()
        let rejectPacket = try packetBuilder.buildPacketWithSignature(
            header: rejectHeader,
            attributes: rejectAttributes
        )
        
        // Parse the rejection
        let packetParser = PacketParser()
        let parsedReject = try packetParser.parsePacket(from: rejectPacket)
        
        XCTAssertEqual(parsedReject.header.type, .openReject)
        XCTAssertEqual(parsedReject.header.sessionID, 0)
        XCTAssertEqual(parsedReject.header.token, 0)
        
        // Verify rejection reason
        let reasonAttr = parsedReject.attributes.first { $0.type == .rejectReason }
        XCTAssertNotNil(reasonAttr)
        XCTAssertEqual(reasonAttr?.value.first, 0x02)
    }
    
    // MARK: - Timeout and Retry Logic Tests
    
    func testAuthenticationTimeout() async {
        // Test timeout behavior
        let startTime = Date()
        
        // Simulate timeout scenario
        // In a real implementation, this would test the actual timeout logic
        let timeoutDuration: TimeInterval = 0.1 // Short timeout for testing
        
        try? await Task.sleep(nanoseconds: UInt64(timeoutDuration * 1_000_000_000))
        
        let elapsedTime = Date().timeIntervalSince(startTime)
        XCTAssertGreaterThanOrEqual(elapsedTime, timeoutDuration)
    }
    
    // MARK: - Session Management Tests
    
    func testSessionInvalidation() async {
        // Test session invalidation
        // This would be more meaningful with actual session state
        
        // Simulate having a session
        let mockSessionID: UInt16 = 1234
        let mockToken: UInt32 = 0x56789ABC
        
        // In a real implementation, we would:
        // 1. Set the session info
        // 2. Verify it's set
        // 3. Invalidate the session
        // 4. Verify it's cleared
        
        // For now, we just test the data structures
        let sessionInfo = (sessionID: mockSessionID, token: mockToken)
        XCTAssertEqual(sessionInfo.sessionID, mockSessionID)
        XCTAssertEqual(sessionInfo.token, mockToken)
    }
    
    // MARK: - Network Configuration Tests
    
    func testNetworkConfigurationParsing() throws {
        // Test parsing of network configuration from server response
        let configAttributes = [
            TLVAttribute(ip: IPv4Address("**********")!),
            TLVAttribute(gateway: IPv4Address("********")!),
            TLVAttribute(netmask: IPv4Address("*************")!),
            TLVAttribute(dns: [IPv4Address("*******")!, IPv4Address("*******")!]),
            TLVAttribute(mtu: 1420),
            TLVAttribute(keepAlive: 30)
        ]
        
        // Verify each attribute can be parsed correctly
        for attribute in configAttributes {
            let serialized = attribute.toData()
            let deserialized = try TLVAttribute(from: serialized)
            
            XCTAssertEqual(deserialized.type, attribute.type)
            XCTAssertEqual(deserialized.value, attribute.value)
        }
        
        // Test configuration extraction
        var config: [String: String] = [:]
        
        for attribute in configAttributes {
            switch attribute.type {
            case .ip:
                if attribute.value.count == 4 {
                    let ip = IPv4Address(attribute.value)
                    config["ip"] = ip?.debugDescription
                }
            case .gateway:
                if attribute.value.count == 4 {
                    let gateway = IPv4Address(attribute.value)
                    config["gateway"] = gateway?.debugDescription
                }
            case .mtu:
                if attribute.value.count == 2 {
                    let mtu = attribute.value.withUnsafeBytes { $0.load(as: UInt16.self).bigEndian }
                    config["mtu"] = String(mtu)
                }
            case .keepAlive:
                if attribute.value.count == 2 {
                    let keepAlive = attribute.value.withUnsafeBytes { $0.load(as: UInt16.self).bigEndian }
                    config["keepAlive"] = String(keepAlive)
                }
            default:
                break
            }
        }
        
        XCTAssertNotNil(config["ip"])
        XCTAssertNotNil(config["gateway"])
        XCTAssertNotNil(config["mtu"])
        XCTAssertNotNil(config["keepAlive"])
        XCTAssertEqual(config["mtu"], "1420")
        XCTAssertEqual(config["keepAlive"], "30")
    }
    
    // MARK: - Error Code Mapping Tests
    
    func testErrorCodeMapping() {
        // Test error code constants match Go backend
        let errorCodes = [
            (code: 0, description: "No error"),
            (code: 1, description: "Invalid packet"),
            (code: 2, description: "Authentication failed"),
            (code: 3, description: "Connection failed"),
            (code: 4, description: "Timeout"),
            (code: 5, description: "Server rejected"),
        ]
        
        for errorCode in errorCodes {
            // In a real implementation, we would have an error code mapping function
            // For now, we just verify the structure
            XCTAssertGreaterThanOrEqual(errorCode.code, 0)
            XCTAssertFalse(errorCode.description.isEmpty)
        }
    }
    
    // MARK: - Performance Tests
    
    func testAuthenticationPacketPerformance() throws {
        let username = "testuser"
        let password = "testpass"
        let packetBuilder = PacketBuilder()
        
        measure {
            for _ in 0..<1000 {
                do {
                    _ = try packetBuilder.buildOpenPacket(
                        username: username,
                        password: password,
                        mtu: 1420,
                        encryptionMethod: .aes
                    )
                } catch {
                    XCTFail("Packet building failed: \(error)")
                }
            }
        }
    }
}
