/**
 * FILE: PlatformChannelExtensionsTests.swift
 *
 * DESCRIPTION:
 *     Unit tests for Platform Channel data conversion extensions.
 *     Validates compatibility with Flutter frontend data models.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create Platform Channel extensions tests
 */

import XCTest
import Foundation
@testable import ItForceCore

class PlatformChannelExtensionsTests: XCTestCase {
    
    // MARK: - ServerInfo Tests
    
    func testServerInfoToDictionary() {
        // Create test server info
        let serverInfo = ServerInfo(
            id: "test_server_001",
            name: "测试服务器",
            nameEn: "Test Server",
            serverName: "test.example.com",
            serverPort: 8080,
            isAuto: true,
            ping: 50,
            status: .online,
            lastCheck: Date(timeIntervalSince1970: 1640995200) // 2022-01-01 00:00:00 UTC
        )
        
        // Convert to dictionary
        let dict = serverInfo.toDictionary()
        
        // Validate Flutter Server.fromJson() compatibility
        XCTAssertEqual(dict["id"] as? String, "test_server_001")
        XCTAssertEqual(dict["name"] as? String, "测试服务器")
        XCTAssertEqual(dict["name_en"] as? String, "Test Server")
        XCTAssertEqual(dict["server_name"] as? String, "test.example.com")
        XCTAssertEqual(dict["server_port"] as? Int, 8080)
        XCTAssertEqual(dict["ping"] as? Int, 50)
        XCTAssertEqual(dict["isauto"] as? Bool, true)
        XCTAssertEqual(dict["status"] as? String, "online")
        XCTAssertEqual(dict["isdefault"] as? Bool, false)
        
        print("✅ ServerInfo toDictionary test passed")
    }
    
    // MARK: - ConnectionState Tests
    
    func testConnectionStateFlutterMapping() {
        // Test all connection states
        XCTAssertEqual(ConnectionState.initializing.flutterStatusString, "connecting")
        XCTAssertEqual(ConnectionState.serverResolved.flutterStatusString, "connecting")
        XCTAssertEqual(ConnectionState.authenticating.flutterStatusString, "connecting")
        XCTAssertEqual(ConnectionState.connected.flutterStatusString, "connected")
        XCTAssertEqual(ConnectionState.disconnected.flutterStatusString, "disconnected")
        XCTAssertEqual(ConnectionState.authenticationFailed.flutterStatusString, "error")
        
        print("✅ ConnectionState Flutter mapping test passed")
    }
    
    func testConnectionStateDisconnectingSupport() {
        // Test disconnecting state support
        XCTAssertEqual(
            ConnectionState.connected.flutterStatusString(isDisconnecting: true),
            "disconnecting"
        )
        XCTAssertEqual(
            ConnectionState.disconnected.flutterStatusString(isDisconnecting: true),
            "disconnecting"
        )
        XCTAssertEqual(
            ConnectionState.authenticating.flutterStatusString(isDisconnecting: true),
            "connecting" // Should not change to disconnecting
        )
        
        print("✅ ConnectionState disconnecting support test passed")
    }
    
    // MARK: - TrafficStatistics Tests
    
    func testTrafficStatisticsToDictionary() {
        // Create test traffic stats
        let stats = TrafficStatistics(
            totalUpload: 1024000,
            totalDownload: 2048000,
            uploadSpeed: 512,
            downloadSpeed: 1024,
            lastUpdate: Date(timeIntervalSince1970: 1640995200)
        )
        
        // Convert to dictionary
        let dict = stats.toDictionary()
        
        // Validate Flutter TrafficStats.fromJson() compatibility
        XCTAssertEqual(dict["upload_speed"] as? Int, 512)
        XCTAssertEqual(dict["download_speed"] as? Int, 1024)
        XCTAssertEqual(dict["total_upload"] as? Int, 1024000)
        XCTAssertEqual(dict["total_download"] as? Int, 2048000)
        XCTAssertEqual(dict["timestamp"] as? Int64, 1640995200000) // Milliseconds
        
        print("✅ TrafficStatistics toDictionary test passed")
    }
    
    // MARK: - VPNConnectionInfo Tests
    
    func testVPNConnectionInfoToDictionary() {
        // Create test server
        let server = ServerInfo(
            id: "test_server",
            name: "测试服务器",
            nameEn: "Test Server",
            serverName: "test.example.com",
            serverPort: 8080
        )
        
        // Create test traffic stats
        let trafficStats = TrafficStatistics(
            totalUpload: 1024,
            totalDownload: 2048,
            uploadSpeed: 100,
            downloadSpeed: 200,
            lastUpdate: Date()
        )
        
        // Create test connection info
        let connectionInfo = VPNConnectionInfo(
            state: .connected,
            connectedServer: server,
            connectionTime: Date(timeIntervalSince1970: 1640995200),
            trafficStats: trafficStats,
            lastError: nil
        )
        
        // Convert to dictionary
        let dict = connectionInfo.toDictionary()
        
        // Validate structure
        XCTAssertEqual(dict["status"] as? String, "connected")
        XCTAssertEqual(dict["message"] as? String, "connected")
        XCTAssertEqual(dict["connected_time"] as? Double, 1640995200.0)
        XCTAssertNotNil(dict["traffic_stats"])
        XCTAssertNotNil(dict["server"])
        
        print("✅ VPNConnectionInfo toDictionary test passed")
    }
    
    func testVPNConnectionInfoDisconnectingSupport() {
        let connectionInfo = VPNConnectionInfo(
            state: .connected,
            connectedServer: nil,
            connectionTime: nil,
            trafficStats: TrafficStatistics(),
            lastError: nil
        )
        
        // Test disconnecting support
        let dictDisconnecting = connectionInfo.toDictionary(isDisconnecting: true)
        XCTAssertEqual(dictDisconnecting["status"] as? String, "disconnecting")
        XCTAssertEqual(dictDisconnecting["message"] as? String, "disconnecting")
        
        // Test normal state
        let dictNormal = connectionInfo.toDictionary(isDisconnecting: false)
        XCTAssertEqual(dictNormal["status"] as? String, "connected")
        XCTAssertEqual(dictNormal["message"] as? String, "connected")
        
        print("✅ VPNConnectionInfo disconnecting support test passed")
    }
    
    // MARK: - VPNServiceError Tests
    
    func testVPNServiceErrorToDictionary() {
        let error = VPNServiceError.connectionServiceError(
            ConnectionServiceError.connectionInProgress
        )
        
        let dict = error.toDictionary()
        
        XCTAssertEqual(dict["code"] as? Int, 1003)
        XCTAssertEqual(dict["type"] as? String, "connection_service_error")
        XCTAssertNotNil(dict["message"])
        
        print("✅ VPNServiceError toDictionary test passed")
    }
    
    // MARK: - InterfaceInfo Tests
    
    func testInterfaceInfoToDictionary() {
        let interfaceInfo = InterfaceInfo(
            localIP: "*************",
            tunIP: "********",
            gateway: "********",
            dns: ["*******", "*******"],
            mtu: 1400
        )
        
        let dict = interfaceInfo.toDictionary()
        
        XCTAssertEqual(dict["local_ip"] as? String, "*************")
        XCTAssertEqual(dict["tun_ip"] as? String, "********")
        XCTAssertEqual(dict["gateway"] as? String, "********")
        XCTAssertEqual(dict["dns"] as? [String], ["*******", "*******"])
        XCTAssertEqual(dict["mtu"] as? Int, 1400)
        
        print("✅ InterfaceInfo toDictionary test passed")
    }
    
    // MARK: - Integration Tests
    
    func testFlutterCompatibilityIntegration() {
        print("\n🧪 Running Flutter compatibility integration test...")
        
        // Simulate complete VPN status data that would be sent to Flutter
        let server = ServerInfo(
            id: "auto_server_001",
            name: "自动服务器",
            nameEn: "Auto Server",
            serverName: "auto.vpn.com",
            serverPort: 443,
            isAuto: true,
            ping: 25,
            status: .online
        )
        
        let trafficStats = TrafficStatistics(
            totalUpload: 5242880,    // 5MB
            totalDownload: 10485760, // 10MB
            uploadSpeed: 1024,       // 1KB/s
            downloadSpeed: 2048,     // 2KB/s
            lastUpdate: Date()
        )
        
        let connectionInfo = VPNConnectionInfo(
            state: .connected,
            connectedServer: server,
            connectionTime: Date(timeIntervalSince1970: Date().timeIntervalSince1970 - 3600), // 1 hour ago
            trafficStats: trafficStats,
            lastError: nil
        )
        
        // Convert to Flutter-compatible format
        let statusData = connectionInfo.toDictionary()
        
        // Validate complete structure matches Flutter expectations
        XCTAssertEqual(statusData["status"] as? String, "connected")
        XCTAssertNotNil(statusData["server"])
        XCTAssertNotNil(statusData["traffic_stats"])
        XCTAssertGreaterThan(statusData["connected_time"] as? Double ?? 0, 0)
        
        // Validate nested server data
        let serverData = statusData["server"] as? [String: Any]
        XCTAssertEqual(serverData?["id"] as? String, "auto_server_001")
        XCTAssertEqual(serverData?["isauto"] as? Bool, true)
        
        // Validate nested traffic data
        let trafficData = statusData["traffic_stats"] as? [String: Any]
        XCTAssertEqual(trafficData?["total_upload"] as? Int, 5242880)
        XCTAssertEqual(trafficData?["upload_speed"] as? Int, 1024)
        
        print("✅ Flutter compatibility integration test passed")
        print("📊 Complete status data structure validated")
    }
}
