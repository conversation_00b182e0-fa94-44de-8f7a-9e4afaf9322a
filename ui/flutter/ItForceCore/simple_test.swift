/**
 * FILE: simple_test.swift
 *
 * DESCRIPTION:
 *     Simple test to verify Infrastructure layer compilation and basic functionality
 *
 * AUTHOR: wei
 * HISTORY: 01/07/2025 create
 */

import Foundation

// Simple test to verify that the Infrastructure layer compiles correctly
func testInfrastructureCompilation() {
    print("🧪 Testing Infrastructure Layer Compilation")
    
    // Test 1: Basic Foundation types work
    print("✅ Foundation types available")
    
    // Test 2: Data structures work
    let testData = Data([0x01, 0x02, 0x03, 0x04])
    assert(testData.count == 4, "Data creation should work")
    print("✅ Data structures work")
    
    // Test 3: Async/await support
    Task {
        await testAsyncSupport()
    }
    
    // Test 4: Actor support
    let testActor = TestActor()
    Task {
        await testActor.testMethod()
        print("✅ Actor support works")
    }
    
    print("✅ Basic Swift features work correctly")
    print("📊 Infrastructure layer compilation test completed successfully!")
}

// Test async support
func testAsyncSupport() async {
    try? await Task.sleep(nanoseconds: 1_000_000) // 1ms
    print("✅ Async/await support works")
}

// Test actor
actor TestActor {
    private var value = 0
    
    func testMethod() {
        value += 1
    }
}

// Main execution
testInfrastructureCompilation()

// Keep running for async tasks
DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
    print("🎉 All tests completed!")
    exit(0)
}

RunLoop.main.run()
