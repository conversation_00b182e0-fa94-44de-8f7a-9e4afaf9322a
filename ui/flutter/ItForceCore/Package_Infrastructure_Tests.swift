// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "InfrastructureTests",
    platforms: [
        .iOS(.v13),
        .macOS(.v10_15)
    ],
    products: [
        .executable(name: "InfrastructureTests", targets: ["InfrastructureTests"])
    ],
    dependencies: [],
    targets: [
        .target(
            name: "ItForceCore",
            path: "Sources"
        ),
        .executableTarget(
            name: "InfrastructureTests",
            dependencies: ["ItForceCore"],
            path: "InfrastructureTestsOnly",
            sources: [
                "main.swift",
                "LoggingTests.swift",
                "CallerInfoTest.swift", 
                "ConfigurationTests.swift",
                "DataBufferPoolTests.swift",
                "ErrorHandlingTests.swift",
                "PerformanceMonitorTests.swift",
                "InfrastructureIntegrationTests.swift"
            ]
        )
    ]
)
