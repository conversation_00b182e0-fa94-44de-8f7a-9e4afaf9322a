Pod::Spec.new do |spec|
  spec.name          = 'ItForceCore'
  spec.version       = '1.0.0'
  spec.license       = { :type => 'MIT' }
  spec.homepage      = 'https://github.com/itforce/core'
  spec.authors       = { 'wei' => '<EMAIL>' }
  spec.summary       = 'ItForce VPN Core library for iOS and macOS'
  spec.description   = 'Universal VPN core library providing connection management, protocol handling, and platform integration for iOS and macOS applications.'
  spec.source        = { :path => '.' }
  spec.module_name   = 'ItForceCore'

  spec.ios.deployment_target = '13.0'
  spec.osx.deployment_target = '11.0'

  spec.swift_version = '5.0'

  spec.source_files = 'Sources/**/*.swift'

  spec.frameworks = 'Foundation', 'NetworkExtension', 'OSLog', 'CryptoKit'

  spec.dependency 'Flutter'
end
