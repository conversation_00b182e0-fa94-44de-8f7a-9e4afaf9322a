// swift-tools-version: 5.7
/**
 * FILE: Package.swift
 *
 * DESCRIPTION:
 *     Swift Package Manager configuration for ItForce VPN Core (Shared iOS/macOS)
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import PackageDescription

let package = Package(
    name: "ItForceCore",
    platforms: [
        .iOS(.v14),
        .macOS(.v11)
    ],
    products: [
        .library(
            name: "ItForceCore",
            targets: ["ItForceCore"]
        ),
    ],
    dependencies: [
        // No external dependencies - using system frameworks only
        // NetworkExtension, CryptoKit, OSLog are system frameworks
    ],
    targets: [
        .target(
            name: "ItForceCore",
            dependencies: [],
            path: "Sources",
            exclude: [
                "Infrastructure/Logging/README.md"
            ]
        ),
        .testTarget(
            name: "ItForceCoreTests",
            dependencies: ["ItForceCore"],
            path: "Tests"
        ),
    ]
)
