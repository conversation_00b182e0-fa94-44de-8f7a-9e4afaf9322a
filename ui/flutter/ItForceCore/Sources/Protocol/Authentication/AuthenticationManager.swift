/**
 * FILE: AuthenticationManager.swift
 *
 * DESCRIPTION:
 *     Manages user authentication process for SDWAN ZZVPN protocol.
 *     Handles authentication packet creation, response processing, and session management.
 *     Compatible with Go backend authentication flow.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import Network

/**
 * NAME: AuthenticationState
 *
 * DESCRIPTION:
 *     Represents the current state of authentication process.
 *     Maps to Go backend connection states for consistency.
 */
public enum AuthenticationState: String, CaseIterable {
    case idle = "idle"
    case authenticating = "authenticating"
    case authenticated = "authenticated"
    case failed = "failed"
    case timeout = "timeout"
    case rejected = "rejected"
}

/**
 * NAME: AuthenticationResult
 *
 * DESCRIPTION:
 *     Contains the result of an authentication attempt.
 *     Includes success status, session information, and error details.
 *
 * PROPERTIES:
 *     success - Whether authentication was successful
 *     sessionID - Session ID assigned by server (if successful)
 *     token - Authentication token (if successful)
 *     errorMessage - Error description (if failed)
 *     rejectReason - Specific rejection reason code (if rejected)
 *     serverConfig - Server configuration received (if successful)
 */
public struct AuthenticationResult {
    public let success: Bool
    public let sessionID: UInt16?
    public let token: UInt32?
    public let errorMessage: String?
    public let rejectReason: UInt8?
    public let serverConfig: [String: Any]?
    
    public init(success: Bool, sessionID: UInt16? = nil, token: UInt32? = nil, 
                errorMessage: String? = nil, rejectReason: UInt8? = nil, 
                serverConfig: [String: Any]? = nil) {
        self.success = success
        self.sessionID = sessionID
        self.token = token
        self.errorMessage = errorMessage
        self.rejectReason = rejectReason
        self.serverConfig = serverConfig
    }
}

/**
 * NAME: AuthenticationManagerProtocol
 *
 * DESCRIPTION:
 *     Protocol defining authentication management interface.
 *     Provides methods for authentication, session management, and state tracking.
 *
 * METHODS:
 *     authenticate - Perform user authentication
 *     getState - Get current authentication state
 *     getSessionInfo - Get current session information
 *     invalidateSession - Invalidate current session
 */
public protocol AuthenticationManagerProtocol {
    func authenticate(username: String, password: String, serverAddress: String,
                     serverPort: Int, mtu: Int, encryptionMethod: EncryptionMethod) async throws -> AuthenticationResult
    func getState() async -> AuthenticationState
    func getSessionInfo() async -> (sessionID: UInt16?, token: UInt32?)?
    func invalidateSession() async
}

/**
 * NAME: AuthenticationManager
 *
 * DESCRIPTION:
 *     Manages the complete authentication process for SDWAN ZZVPN protocol.
 *     Handles packet creation, network communication, response processing, and session management.
 *     Implements timeout, retry logic, and error handling compatible with Go backend.
 *
 * PROPERTIES:
 *     keyManager - Key management service for encryption
 *     packetBuilder - Packet builder for creating authentication packets
 *     packetParser - Packet parser for processing responses
 *     state - Current authentication state
 *     sessionID - Current session ID (if authenticated)
 *     token - Current authentication token (if authenticated)
 *     singleAttemptTimeout - Single attempt timeout duration (2 seconds)
 *     retryCount - Maximum number of retry attempts (3 times)
 */
public actor AuthenticationManager: AuthenticationManagerProtocol {
    private let keyManager: KeyManager
    private let packetBuilder: PacketBuilder
    private let packetParser: PacketParser
    private let logger: LoggerProtocol

    private var state: AuthenticationState = .idle
    private var sessionID: UInt16?
    private var token: UInt32?

    // Configuration
    private let singleAttemptTimeout: TimeInterval  // 单次尝试超时时间：2秒
    private let retryCount: Int                      // 最大重试次数：3次
    private let retryInterval: TimeInterval          // 重试间隔：很短即可

    public init(keyManager: KeyManager, packetBuilder: PacketBuilder,
                packetParser: PacketParser, singleAttemptTimeout: TimeInterval = 2.0, retryCount: Int = 3, retryInterval: TimeInterval = 0.1) {
        self.keyManager = keyManager
        self.packetBuilder = packetBuilder
        self.packetParser = packetParser
        self.logger = LoggingSystem.shared.logger(for: "AuthenticationManager")
        self.singleAttemptTimeout = singleAttemptTimeout
        self.retryCount = retryCount
        self.retryInterval = retryInterval
    }

    /**
     * NAME: init()
     *
     * DESCRIPTION:
     *     Convenience initializer with default dependencies.
     *     Note: In Swift 6, actor convenience initializers are handled differently.
     */
    public init() {
        self.init(
            keyManager: KeyManager(),
            packetBuilder: PacketBuilder(),
            packetParser: PacketParser()
        )
    }



    /**
     * NAME: authenticateWithConnection
     *
     * DESCRIPTION:
     *     Performs authentication using an existing UDP connection (like Go backend).
     *     This is the preferred method for VPN connections to avoid connection conflicts.
     *
     * PARAMETERS:
     *     connection - Existing UDP connection to use
     *     username - User login name
     *     password - User password
     *     mtu - Maximum transmission unit
     *     encryptionMethod - Encryption method to use
     *
     * RETURNS:
     *     AuthenticationResult - Authentication result with server configuration
     *
     * THROWS:
     *     ProtocolError - If authentication fails
     */
    public func authenticateWithConnection(
        connection: NWConnection,
        username: String,
        password: String,
        mtu: Int,
        encryptionMethod: EncryptionMethod
    ) async throws -> AuthenticationResult {
        logger.info("Starting authentication with existing connection", metadata: [
            "username": username,
            "mtu": "\(mtu)",
            "encryption": "\(encryptionMethod.rawValue)"
        ])

        // Update state to authenticating
        state = .authenticating

        do {
            // Create authentication packet
            let openPacket = try packetBuilder.buildOpenPacket(
                username: username,
                password: password,
                mtu: UInt16(mtu),
                encryptionMethod: encryptionMethod
            )

            let packetData = openPacket.count
            logger.info("Authentication packet created", metadata: [
                "packet_size": "\(packetData)",
                "username": username,
                "mtu": "\(mtu)",
                "encryption": "\(encryptionMethod.rawValue)"
            ])

            // Send authentication packet using existing connection
            try await sendPacket(connection: connection, packet: openPacket)
            logger.info("Authentication packet sent successfully")

            // Wait for authentication response and parse it
            let result = try await waitForAuthenticationResponse(connection: connection)

            // Update state to authenticated
            state = .authenticated
            sessionID = result.sessionID
            token = result.token

            logger.info("Authentication completed successfully", metadata: [
                "session_id": String(format: "0x%04X", result.sessionID ?? 0),
                "token": String(format: "0x%08X", result.token ?? 0)
            ])

            return result

        } catch {
            logger.error("Authentication failed", metadata: [
                "error": error.localizedDescription
            ])
            state = .failed
            throw error
        }
    }

    /**
     * NAME: authenticate
     *
     * DESCRIPTION:
     *     Performs user authentication with the SDWAN server.
     *     Creates OPEN packet, sends to server, waits for OPENACK response.
     *     Handles timeout, retry logic, and error conditions.
     *
     * PARAMETERS:
     *     username - User login name
     *     password - User password
     *     serverAddress - Server IP address or hostname
     *     serverPort - Server port number
     *     mtu - Maximum transmission unit
     *     encryptionMethod - Encryption method to use
     *
     * RETURNS:
     *     AuthenticationResult - Result of authentication attempt
     *
     * THROWS:
     *     ProtocolError.authenticationFailed - If authentication fails
     *     ProtocolError.networkError - If network communication fails
     *     ProtocolError.timeout - If authentication times out
     */
    public func authenticate(username: String, password: String, serverAddress: String,
                           serverPort: Int, mtu: Int, encryptionMethod: EncryptionMethod) async throws -> AuthenticationResult {

        // Update state to authenticating
        state = .authenticating

        do {
            // Note: Password encryption is now handled inside buildOpenPacket
            // to match Go backend CreateOpenPacket implementation exactly

            // Create authentication packet
            let openPacket = try packetBuilder.buildOpenPacket(
                username: username,
                password: password,
                mtu: UInt16(mtu),
                encryptionMethod: encryptionMethod
            )

            // Perform authentication with retry logic - create new connection for each attempt
            let result: AuthenticationResult = try await performAuthenticationWithRetry(
                serverAddress: serverAddress,
                serverPort: serverPort,
                packet: openPacket,
                username: username
            )

            // Update state based on result
            if result.success {
                state = .authenticated
                sessionID = result.sessionID
                token = result.token
            } else {
                state = result.rejectReason != nil ? .rejected : .failed
                sessionID = nil
                token = nil
            }

            return result

        } catch {
            state = .failed
            sessionID = nil
            token = nil
            throw error
        }
    }
    
    /**
     * NAME: getState
     *
     * DESCRIPTION:
     *     Returns the current authentication state.
     *
     * RETURNS:
     *     AuthenticationState - Current state
     */
    public func getState() async -> AuthenticationState {
        return state
    }

    /**
     * NAME: getSessionInfo
     *
     * DESCRIPTION:
     *     Returns current session information if authenticated.
     *
     * RETURNS:
     *     Optional tuple with sessionID and token, nil if not authenticated
     */
    public func getSessionInfo() async -> (sessionID: UInt16?, token: UInt32?)? {
        guard state == .authenticated else { return nil }
        return (sessionID: sessionID, token: token)
    }

    /**
     * NAME: invalidateSession
     *
     * DESCRIPTION:
     *     Invalidates the current session and resets authentication state.
     */
    public func invalidateSession() async {
        state = .idle
        sessionID = nil
        token = nil
    }

    // MARK: - Private Methods

    /**
     * NAME: createUDPConnection
     *
     * DESCRIPTION:
     *     Creates a UDP connection to the specified server.
     *     Always creates a new NWConnection instance to avoid state conflicts.
     *     Uses simple continuation handling - each call creates fresh connection.
     *
     * PARAMETERS:
     *     serverAddress - Server IP address or hostname
     *     serverPort - Server port number
     *
     * RETURNS:
     *     NWConnection - Established UDP connection
     *
     * THROWS:
     *     ProtocolError.networkError - If connection fails
     */
    private func createUDPConnection(serverAddress: String, serverPort: Int) async throws -> NWConnection {
        logger.info("Creating UDP connection", metadata: [
            "server_address": serverAddress,
            "server_port": "\(serverPort)"
        ])

        let host = NWEndpoint.Host(serverAddress)
        let port = NWEndpoint.Port(integerLiteral: UInt16(serverPort))
        let endpoint = NWEndpoint.hostPort(host: host, port: port)

        // Always create a new connection instance to avoid state conflicts
        let connection = NWConnection(to: endpoint, using: .udp)

        logger.info("UDP connection created, starting connection process")

        return try await withCheckedThrowingContinuation { continuation in
            // Note: Using nonisolated(unsafe) to suppress Swift 6 concurrency warnings
            // This is safe because we only access hasResumed within the state handler
            nonisolated(unsafe) var hasResumed = false

            connection.stateUpdateHandler = { state in
                guard !hasResumed else {
                    // Skip logging to avoid concurrency issues
                    return
                }

                // Skip detailed logging to avoid concurrency issues
                switch state {
                case .ready:
                    hasResumed = true
                    continuation.resume(returning: connection)
                case .failed(let error):
                    hasResumed = true
                    continuation.resume(throwing: ProtocolError.networkError(error.localizedDescription))
                case .cancelled:
                    hasResumed = true
                    continuation.resume(throwing: ProtocolError.networkError("Connection cancelled"))
                case .waiting(_), .preparing, .setup:
                    // Non-terminal states, continue waiting
                    break
                @unknown default:
                    // Unknown state, continue waiting
                    break
                }
            }

            logger.info("Starting UDP connection")
            connection.start(queue: .global())
        }
    }

    /**
     * NAME: performAuthenticationWithRetry
     *
     * DESCRIPTION:
     *     Performs authentication with retry logic.
     *     Creates a new UDP connection for each attempt to avoid state conflicts.
     *     Sends OPEN packet and waits for OPENACK response with timeout handling.
     *
     * PARAMETERS:
     *     serverAddress - Server IP address or hostname
     *     serverPort - Server port number
     *     packet - Authentication packet to send
     *     username - Username for logging
     *
     * RETURNS:
     *     AuthenticationResult - Result of authentication
     *
     * THROWS:
     *     ProtocolError.timeout - If authentication times out
     *     ProtocolError.authenticationFailed - If authentication fails
     */
    private func performAuthenticationWithRetry(serverAddress: String, serverPort: Int, packet: Data, username: String) async throws -> AuthenticationResult {

        for attempt in 1...retryCount {
            do {
                logger.info("Starting authentication attempt", metadata: [
                    "attempt": "\(attempt)",
                    "max_attempts": "\(retryCount)",
                    "server_address": serverAddress,
                    "server_port": "\(serverPort)"
                ])

                // Create a new connection for each attempt to avoid state conflicts
                logger.info("Creating new UDP connection for attempt \(attempt)")
                let connection = try await createUDPConnection(serverAddress: serverAddress, serverPort: serverPort)

                // Send authentication packet
                logger.info("Sending authentication packet", metadata: [
                    "attempt": "\(attempt)",
                    "packet_size": "\(packet.count)"
                ])
                try await sendPacket(connection: connection, packet: packet)
                logger.info("Authentication packet sent successfully")

                // Wait for response with single attempt timeout (2 seconds)
                logger.info("Waiting for authentication response", metadata: [
                    "single_attempt_timeout": "\(singleAttemptTimeout)",
                    "attempt": "\(attempt)"
                ])
                let result = try await withTimeout(singleAttemptTimeout) {
                    try await self.waitForAuthenticationResponse(connection: connection)
                }

                // Close connection after successful authentication
                logger.info("Authentication successful, closing connection", metadata: [
                    "attempt": "\(attempt)",
                    "success": "\(result.success)"
                ])
                connection.cancel()
                return result

            } catch ProtocolError.timeout {
                logger.warning("Authentication attempt timed out after \(singleAttemptTimeout) seconds", metadata: [
                    "attempt": "\(attempt)",
                    "max_attempts": "\(retryCount)",
                    "single_attempt_timeout": "\(singleAttemptTimeout)"
                ])

                if attempt == retryCount {
                    logger.error("All authentication attempts failed due to timeout", metadata: [
                        "total_attempts": "\(retryCount)",
                        "single_attempt_timeout": "\(singleAttemptTimeout)",
                        "total_time_spent": "\(Double(retryCount) * singleAttemptTimeout)"
                    ])
                    state = .timeout
                    throw ProtocolError.timeout
                }

                // Brief wait before next attempt to avoid overwhelming the server
                logger.info("Authentication timeout, retrying in \(retryInterval) seconds", metadata: [
                    "attempt": "\(attempt)",
                    "max_attempts": "\(retryCount)",
                    "retry_interval": "\(retryInterval)"
                ])

                try await Task.sleep(nanoseconds: UInt64(retryInterval * 1_000_000_000))
                continue
            } catch ProtocolError.networkError(let errorMessage) {
                logger.warning("Authentication attempt failed due to network error: \(errorMessage)", metadata: [
                    "attempt": "\(attempt)",
                    "max_attempts": "\(retryCount)",
                    "network_error": errorMessage
                ])

                if attempt == retryCount {
                    logger.error("All authentication attempts failed due to network error", metadata: [
                        "total_attempts": "\(retryCount)",
                        "network_error": errorMessage,
                        "single_attempt_timeout": "\(singleAttemptTimeout)"
                    ])
                    state = .timeout
                    throw ProtocolError.networkError(errorMessage)
                }

                // Brief wait before next attempt to allow network recovery
                logger.info("Network error, retrying in \(retryInterval) seconds", metadata: [
                    "attempt": "\(attempt)",
                    "max_attempts": "\(retryCount)",
                    "retry_interval": "\(retryInterval)",
                    "network_error": errorMessage
                ])

                try await Task.sleep(nanoseconds: UInt64(retryInterval * 1_000_000_000))
                continue
            } catch {
                // Non-retryable errors (authentication failures, protocol errors, etc.)
                logger.error("Authentication attempt failed with non-retryable error", metadata: [
                    "attempt": "\(attempt)",
                    "error": error.localizedDescription,
                    "error_type": "\(type(of: error))"
                ])
                throw error
            }
        }

        // Should not reach here, but handle gracefully
        state = .timeout
        throw ProtocolError.timeout
    }

    /**
     * NAME: sendPacket
     *
     * DESCRIPTION:
     *     Sends a packet over the UDP connection.
     *
     * PARAMETERS:
     *     connection - UDP connection
     *     packet - Packet data to send
     *
     * THROWS:
     *     ProtocolError.networkError - If send fails
     */
    private func sendPacket(connection: NWConnection, packet: Data) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            connection.send(content: packet, completion: .contentProcessed { error in
                if let error = error {
                    continuation.resume(throwing: ProtocolError.networkError(error.localizedDescription))
                } else {
                    continuation.resume()
                }
            })
        }
    }

    /**
     * NAME: waitForAuthenticationResponse
     *
     * DESCRIPTION:
     *     Waits for and processes authentication response from server.
     *     Handles OPENACK (success) and OPENREJECT (failure) packets.
     *     Ignores non-authentication packets (like DATAENC) that may arrive after authentication.
     *
     * PARAMETERS:
     *     connection - UDP connection to receive from
     *
     * RETURNS:
     *     AuthenticationResult - Parsed authentication result
     *
     * THROWS:
     *     ProtocolError.authenticationFailed - If authentication fails
     *     ProtocolError.invalidPacket - If response packet is invalid
     */
    private func waitForAuthenticationResponse(connection: NWConnection) async throws -> AuthenticationResult {
        // Keep receiving packets until we get an authentication response
        while true {
            let responseData = try await receivePacket(connection: connection)

            // logger.logDebug("Received packet during authentication",
            //     fields: LogField.int("data_length", responseData.count),
            //     LogField.string("data_hex", responseData.map { String(format: "%02X", $0) }.joined(separator: " "))
            // ) // Debug log commented for production

            // Parse the response packet
            let parsedPacket = try packetParser.parsePacket(from: responseData)

            // logger.logDebug("Parsed packet during authentication",
            //     fields: LogField.string("packet_type", parsedPacket.header.type.description),
            //     LogField.string("session_id", String(format: "0x%04X", parsedPacket.header.sessionID)),
            //     LogField.string("token", String(format: "0x%08X", parsedPacket.header.token))
            // ) // Debug log commented for production

            // Handle different packet types
            switch parsedPacket.header.type {
            case .openAck:
                logger.info("Received OPENACK packet - authentication successful")
                return try parseOpenAckPacket(parsedPacket)

            case .openReject:
                logger.warning("Received OPENREJECT packet - authentication failed")
                // Use PacketParser's parseOpenRejectPacket to avoid TLV parsing errors
                let (errorCode, errorMessage) = try packetParser.parseOpenRejectPacket(from: responseData)

                logger.logError("Authentication rejected by server",
                    fields: LogField.int("error_code", errorCode),
                    LogField.string("error_message", errorMessage)
                )

                return AuthenticationResult(
                    success: false,
                    sessionID: nil,
                    token: nil,
                    errorMessage: errorMessage,
                    rejectReason: UInt8(errorCode),
                    serverConfig: nil
                )

            case .dataEncrypt, .data, .dataDup, .dataEncDup, .ipFrag, .data6, .ipFrag6:
                // These are data packets that may arrive after authentication
                // Ignore them and continue waiting for authentication response
                logger.info("Ignoring data packet during authentication", metadata: [
                    "packet_type": parsedPacket.header.type.description,
                    "packet_size": "\(responseData.count)"
                ])
                continue

            case .echoRequest, .echoResponse:
                // These are keepalive packets, ignore them during authentication
                logger.info("Ignoring keepalive packet during authentication", metadata: [
                    "packet_type": parsedPacket.header.type.description
                ])
                continue

            default:
                // For other unexpected packet types, log and continue waiting
                logger.warning("Ignoring unexpected packet type during authentication", metadata: [
                    "packet_type": parsedPacket.header.type.description,
                    "packet_size": "\(responseData.count)"
                ])
                continue
            }
        }
    }

    /**
     * NAME: receivePacket
     *
     * DESCRIPTION:
     *     Receives a packet from the UDP connection.
     *
     * PARAMETERS:
     *     connection - UDP connection to receive from
     *
     * RETURNS:
     *     Data - Received packet data
     *
     * THROWS:
     *     ProtocolError.networkError - If receive fails
     */
    private func receivePacket(connection: NWConnection) async throws -> Data {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Data, Error>) in
            connection.receive(minimumIncompleteLength: 1, maximumLength: 65536) { data, _, isComplete, error in
                if let error = error {
                    continuation.resume(throwing: ProtocolError.networkError(error.localizedDescription))
                } else if let data = data {
                    continuation.resume(returning: data)
                } else {
                    continuation.resume(throwing: ProtocolError.networkError("No data received"))
                }
            }
        }
    }

    /**
     * NAME: parseOpenAckPacket
     *
     * DESCRIPTION:
     *     Parses OPENACK packet to extract session information.
     *     Compatible with Go backend OPENACK packet format.
     *
     * PARAMETERS:
     *     packet - Parsed packet containing OPENACK data
     *
     * RETURNS:
     *     AuthenticationResult - Success result with session info
     *
     * THROWS:
     *     ProtocolError.invalidPacket - If packet format is invalid
     */
    private func parseOpenAckPacket(_ packet: ParsedPacket) throws -> AuthenticationResult {
        let sessionID = packet.header.sessionID
        let token = packet.header.token

        // Parse server configuration from TLV attributes
        var serverConfig: [String: Any] = [:]

        for attribute in packet.attributes {
            switch attribute.type {
            case .ip:
                // Parse IP address (4 bytes)
                if attribute.value.count >= 4 {
                    let ip = attribute.value.withUnsafeBytes { bytes in
                        let addr = bytes.bindMemory(to: UInt8.self)
                        return "\(addr[0]).\(addr[1]).\(addr[2]).\(addr[3])"
                    }
                    serverConfig["ip"] = ip
                }
            case .dns:
                // Parse DNS servers (4 or 8 bytes for primary and optional secondary DNS)
                if attribute.value.count >= 4 {
                    let dns1 = attribute.value.withUnsafeBytes { bytes in
                        let addr = bytes.bindMemory(to: UInt8.self)
                        return "\(addr[0]).\(addr[1]).\(addr[2]).\(addr[3])"
                    }
                    serverConfig["dns"] = dns1

                    // Check for secondary DNS (if attribute contains 8 bytes)
                    if attribute.value.count >= 8 {
                        let dns2 = attribute.value.withUnsafeBytes { bytes in
                            let addr = bytes.bindMemory(to: UInt8.self)
                            return "\(addr[4]).\(addr[5]).\(addr[6]).\(addr[7])"
                        }
                        serverConfig["dns2"] = dns2
                    }
                }
            case .gateway:
                // Parse gateway address (4 bytes)
                if attribute.value.count >= 4 {
                    let gateway = attribute.value.withUnsafeBytes { bytes in
                        let addr = bytes.bindMemory(to: UInt8.self)
                        return "\(addr[0]).\(addr[1]).\(addr[2]).\(addr[3])"
                    }
                    serverConfig["gateway"] = gateway
                }
            case .netmask:
                // Parse netmask (4 bytes)
                if attribute.value.count >= 4 {
                    let netmask = attribute.value.withUnsafeBytes { bytes in
                        let addr = bytes.bindMemory(to: UInt8.self)
                        return "\(addr[0]).\(addr[1]).\(addr[2]).\(addr[3])"
                    }
                    serverConfig["netmask"] = netmask
                }
            case .mtu:
                // Parse MTU (2 bytes, big endian)
                if attribute.value.count >= 2 {
                    let mtu = UInt16(bigEndian: attribute.value.withUnsafeBytes { $0.load(as: UInt16.self) })
                    serverConfig["mtu"] = String(mtu)
                }
            case .encrypt:
                // Parse encryption method
                if attribute.value.count >= 1 {
                    let encryptMethod = attribute.value[0]
                    serverConfig["encrypt"] = String(encryptMethod)
                }
            case .serverConfig:
                // Parse server configuration data
                if let configData = String(data: attribute.value, encoding: .utf8) {
                    serverConfig["config"] = configData
                }
            case .keepAlive:
                if attribute.value.count >= 2 {
                    let keepAlive = UInt16(bigEndian: attribute.value.withUnsafeBytes { $0.load(as: UInt16.self) })
                    serverConfig["keepAlive"] = String(keepAlive)
                }
            default:
                // Store other attributes as raw data for analysis
                serverConfig["attr_\(attribute.type.rawValue)"] = attribute.value.map { String(format: "%02x", $0) }.joined()
            }
        }

        return AuthenticationResult(
            success: true,
            sessionID: sessionID,
            token: token,
            errorMessage: nil,
            rejectReason: nil,
            serverConfig: serverConfig
        )
    }



    /**
     * NAME: getRejectReasonMessage
     *
     * DESCRIPTION:
     *     Converts rejection reason code to human-readable message.
     *     Maps to Go backend rejection reason constants.
     *
     * PARAMETERS:
     *     reason - Rejection reason code
     *
     * RETURNS:
     *     String - Human-readable error message
     */
    private func getRejectReasonMessage(_ reason: UInt8) -> String {
        switch reason {
        case 0: return "Unknown reason"
        case 1: return "Invalid username"
        case 2: return "Invalid password"
        case 3: return "Server is full"
        case 4: return "Server error"
        case 5: return "Unsupported feature"
        case 6: return "Account expired"
        case 7: return "Account disabled"
        case 8: return "Maximum sessions reached"
        case 9: return "Invalid token"
        default: return "Unknown rejection reason (\(reason))"
        }
    }

    /**
     * NAME: withTimeout
     *
     * DESCRIPTION:
     *     Executes an async operation with a timeout.
     *     Throws ProtocolError.timeout if operation exceeds timeout duration.
     *
     * PARAMETERS:
     *     timeout - Maximum duration to wait
     *     operation - Async operation to execute
     *
     * RETURNS:
     *     T - Result of the operation
     *
     * THROWS:
     *     ProtocolError.timeout - If operation times out
     */
    private func withTimeout<T>(_ timeout: TimeInterval, operation: @escaping () async throws -> T) async throws -> T {
        try await withThrowingTaskGroup(of: T.self) { group in
            // Add the main operation
            group.addTask {
                try await operation()
            }

            // Add timeout task
            group.addTask {
                try await Task.sleep(nanoseconds: UInt64(timeout * 1_000_000_000))
                throw ProtocolError.timeout
            }

            // Return the first completed result
            let result = try await group.next()!
            group.cancelAll()
            return result
        }
    }
}
