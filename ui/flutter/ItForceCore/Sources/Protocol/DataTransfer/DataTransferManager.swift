/**
 * FILE: DataTransferManager.swift
 *
 * DESCRIPTION:
 *     Manages data transfer operations for SDWAN ZZVPN protocol.
 *     Handles packet sending, receiving, and TUN device data processing.
 *     Compatible with Go backend data transfer implementation.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import OSLog

/**
 * NAME: DataTransferManagerProtocol
 *
 * DESCRIPTION:
 *     Protocol defining data transfer management interface.
 *     Provides methods for sending and receiving data packets.
 *
 * METHODS:
 *     sendData - Send data through the tunnel
 *     handleIncomingPacket - Process incoming data packet
 *     startDataTransfer - Start data transfer operations
 *     stopDataTransfer - Stop data transfer operations
 */
public protocol DataTransferManagerProtocol {
    func sendData(_ data: Data) async throws
    func handleIncomingPacket(_ packet: ParsedPacket) async throws
    func startDataTransfer() async throws
    func stopDataTransfer() async
}

/**
 * NAME: DataTransferConfiguration
 *
 * DESCRIPTION:
 *     Configuration parameters for data transfer operations.
 *     Defines buffer sizes and processing parameters.
 *
 * PROPERTIES:
 *     bufferSize - Size of data buffer for TUN device operations
 *     maxPacketSize - Maximum packet size for data transfer
 *     enableFragmentation - Whether to enable packet fragmentation
 *     fragmentTimeout - Timeout for fragment reassembly
 */
public struct DataTransferConfiguration {
    public let bufferSize: Int
    public let maxPacketSize: Int
    public let enableFragmentation: Bool
    public let fragmentTimeout: TimeInterval
    
    public init(
        bufferSize: Int = 65536,
        maxPacketSize: Int = 1500,
        enableFragmentation: Bool = true,
        fragmentTimeout: TimeInterval = 30.0
    ) {
        self.bufferSize = bufferSize
        self.maxPacketSize = maxPacketSize
        self.enableFragmentation = enableFragmentation
        self.fragmentTimeout = fragmentTimeout
    }
}

/**
 * NAME: DataTransferDelegate
 *
 * DESCRIPTION:
 *     Delegate protocol for data transfer events and operations.
 *     Allows external components to handle data transfer events.
 *
 * METHODS:
 *     sendPacketToNetwork - Send packet to network connection
 *     writeDataToTUN - Write data to TUN device
 *     readDataFromTUN - Read data from TUN device
 *     updateTrafficStats - Update traffic statistics
 */
public protocol DataTransferDelegate: AnyObject {
    func sendPacketToNetwork(_ packet: ParsedPacket) async throws
    func writeDataToTUN(_ data: Data) async throws
    func readDataFromTUN() async throws -> Data?
    func updateTrafficStats(bytesSent: Int64, bytesReceived: Int64) async
}

/**
 * NAME: DataTransferManager
 *
 * DESCRIPTION:
 *     Actor-based data transfer manager for thread-safe operation.
 *     Manages data packet sending, receiving, and TUN device operations.
 *     Compatible with Go backend data transfer mechanism.
 *
 * PROPERTIES:
 *     configuration - Data transfer configuration parameters
 *     delegate - Delegate for data transfer operations
 *     isActive - Whether data transfer is active
 *     transferTask - Task for handling data transfer operations
 *     packetsSent - Counter for sent packets
 *     packetsReceived - Counter for received packets
 *     bytesSent - Counter for sent bytes
 *     bytesReceived - Counter for received bytes
 */
public actor DataTransferManager: DataTransferManagerProtocol {
    private let configuration: DataTransferConfiguration
    private weak var delegate: DataTransferDelegate?
    private let logger: Logger
    
    private var isActive: Bool = false
    private var transferTask: Task<Void, Never>?
    
    // Traffic statistics
    private var packetsSent: UInt64 = 0
    private var packetsReceived: UInt64 = 0
    private var bytesSent: UInt64 = 0
    private var bytesReceived: UInt64 = 0
    
    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Initialize data transfer manager with configuration and delegate.
     *
     * PARAMETERS:
     *     configuration - Data transfer configuration parameters
     *     delegate - Delegate for data transfer operations
     *     logger - Logger for data transfer events
     */
    public init(
        configuration: DataTransferConfiguration = DataTransferConfiguration(),
        delegate: DataTransferDelegate? = nil,
        logger: Logger = LoggingSystem.shared.logger(for: "DataTransfer")
    ) {
        self.configuration = configuration
        self.delegate = delegate
        self.logger = logger
    }
    
    /**
     * NAME: setDelegate
     *
     * DESCRIPTION:
     *     Set the data transfer delegate for operation callbacks.
     *
     * PARAMETERS:
     *     delegate - Delegate for data transfer operations
     */
    public func setDelegate(_ delegate: DataTransferDelegate?) {
        self.delegate = delegate
    }
    
    /**
     * NAME: getTrafficStats
     *
     * DESCRIPTION:
     *     Get current traffic statistics.
     *
     * RETURNS:
     *     Tuple containing packets and bytes sent/received
     */
    public func getTrafficStats() -> (packetsSent: UInt64, packetsReceived: UInt64, bytesSent: UInt64, bytesReceived: UInt64) {
        return (packetsSent, packetsReceived, bytesSent, bytesReceived)
    }

    /**
     * NAME: startDataTransfer
     *
     * DESCRIPTION:
     *     Start data transfer operations.
     *     Begins TUN device reading and data processing.
     *
     * THROWS:
     *     ProtocolError.dataTransferStartFailed - If data transfer is already active
     */
    public func startDataTransfer() async throws {
        guard !isActive else {
            logger.warning("Data transfer already active, ignoring start request", file: #file, line: #line, function: #function)
            return
        }

        isActive = true

        logger.logInfo("Starting data transfer operations",
            fields: LogField.int("buffer_size", configuration.bufferSize),
            LogField.int("max_packet_size", configuration.maxPacketSize),
            LogField.bool("fragmentation_enabled", configuration.enableFragmentation)
        )

        // Start TUN device reading task
        transferTask = Task {
            await tunReaderLoop()
        }

        logger.info("Data transfer operations started successfully", file: #file, line: #line, function: #function)
    }

    /**
     * NAME: stopDataTransfer
     *
     * DESCRIPTION:
     *     Stop data transfer operations.
     *     Cancels all data transfer tasks and cleans up resources.
     */
    public func stopDataTransfer() async {
        guard isActive else {
            return
        }

        logger.info("Stopping data transfer operations", file: #file, line: #line, function: #function)

        isActive = false

        // Cancel transfer task
        transferTask?.cancel()

        // Wait for task to complete
        _ = await transferTask?.result
        transferTask = nil

        logger.info("Data transfer operations stopped", file: #file, line: #line, function: #function)
    }

    /**
     * NAME: sendData
     *
     * DESCRIPTION:
     *     Send data through the tunnel connection.
     *     Creates data packet and sends it to network.
     *     Compatible with Go backend SendData implementation.
     *
     * PARAMETERS:
     *     data - Data to send through tunnel
     *
     * THROWS:
     *     ProtocolError.dataTransferFailed - If sending fails
     */
    public func sendData(_ data: Data) async throws {
        guard isActive else {
            throw ProtocolError.dataTransferFailed
        }

        guard !data.isEmpty else {
            return
        }

        // Create data packet (will be handled by connection layer for session info)
        let packet = ParsedPacket(
            header: PacketHeader(
                type: .data,
                encrypt: .none,
                sessionID: 0, // Will be set by connection layer
                token: 0      // Will be set by connection layer
            ),
            signature: Data(),
            attributes: [],
            payload: data
        )

        // Send packet through delegate
        try await delegate?.sendPacketToNetwork(packet)

        // Update statistics
        packetsSent += 1
        bytesSent += UInt64(data.count)

        // Update delegate statistics
        await delegate?.updateTrafficStats(bytesSent: Int64(bytesSent), bytesReceived: Int64(bytesReceived))

        // logger.logDebug("Data packet sent",
        //     fields: LogField.int("packet_size", data.count),
        //     LogField.int("total_packets", Int(packetsSent)),
        //     LogField.int("total_bytes", Int(bytesSent))
        // ) // Debug log commented for production
    }

    /**
     * NAME: handleIncomingPacket
     *
     * DESCRIPTION:
     *     Handle incoming data packet from network.
     *     Processes packet and writes data to TUN device.
     *     Compatible with Go backend handleDataPacket implementation.
     *
     * PARAMETERS:
     *     packet - Incoming data packet to process
     *
     * THROWS:
     *     ProtocolError.dataTransferFailed - If packet processing fails
     */
    public func handleIncomingPacket(_ packet: ParsedPacket) async throws {
        guard isActive else {
            throw ProtocolError.dataTransferFailed
        }

        // Only handle data packets
        guard packet.header.type.isDataPacket else {
            return
        }

        // Write data to TUN device through delegate
        try await delegate?.writeDataToTUN(packet.payload)

        // Update statistics
        packetsReceived += 1
        bytesReceived += UInt64(packet.payload.count)

        // Update delegate statistics
        await delegate?.updateTrafficStats(bytesSent: Int64(bytesSent), bytesReceived: Int64(bytesReceived))

        // logger.logDebug("Data packet received and written to TUN",
        //     fields: LogField.int("packet_size", packet.payload.count),
        //     LogField.string("packet_type", "\(packet.header.type)"),
        //     LogField.int("total_packets", Int(packetsReceived)),
        //     LogField.int("total_bytes", Int(bytesReceived))
        // ) // Debug log commented for production
    }

    // MARK: - Private Methods

    /**
     * NAME: tunReaderLoop
     *
     * DESCRIPTION:
     *     Background loop for reading data from TUN device.
     *     Continuously reads data and sends it through tunnel.
     *     Compatible with Go backend runTunReader implementation.
     */
    private func tunReaderLoop() async {
        // logger.debug("TUN reader loop started", file: #file, line: #line, function: #function)

        while isActive && !Task.isCancelled {
            do {
                // Read data from TUN device through delegate
                if let data = try await delegate?.readDataFromTUN() {
                    // Send data through tunnel
                    try await sendData(data)
                } else {
                    // No data available, short sleep to avoid CPU spinning
                    try await Task.sleep(nanoseconds: 1_000_000) // 1ms
                }

            } catch is CancellationError {
                // logger.debug("TUN reader loop cancelled", file: #file, line: #line, function: #function)
                break
            } catch {
                logger.error("Error in TUN reader loop: \(error)", file: #file, line: #line, function: #function)

                // Short sleep on error to avoid CPU spinning
                do {
                    try await Task.sleep(nanoseconds: 10_000_000) // 10ms
                } catch {
                    break
                }
            }
        }

        // logger.debug("TUN reader loop ended", file: #file, line: #line, function: #function)
    }
}
