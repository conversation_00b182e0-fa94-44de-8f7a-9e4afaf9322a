/**
 * FILE: TLVAttribute.swift
 *
 * DESCRIPTION:
 *     Defines TLV (Type-Length-Value) attribute structure for SDWAN protocol.
 *     Handles attribute encoding and decoding for packet construction.
 *     Ensures 100% compatibility with Go backend TLV implementation.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation

/**
 * NAME: TLVAttributeType
 *
 * DESCRIPTION:
 *     Enumeration of all supported TLV attribute types.
 *     Values must match exactly with Go backend constants for compatibility.
 *
 * METHODS:
 *     rawValue - UInt8 value matching Go backend constants
 *     expectedLength - Expected length for fixed-size attributes
 *     description - Human-readable attribute type description
 */
public enum TLVAttributeType: UInt8, CaseIterable {
    case username = 0x01        // Username for authentication
    case password = 0x02        // Encrypted password
    case mtu = 0x03             // Maximum Transmission Unit
    case ip = 0x04              // Assigned IP address
    case dns = 0x05             // DNS server address
    case gateway = 0x06         // Gateway IP address
    case netmask = 0x07         // Subnet mask
    case encrypt = 0x08         // Encryption method
    case dupPkt = 0x09          // Packet duplication
    case link = 0x0A            // Link
    case ip6 = 0x0B             // IPv6 address
    case dns6 = 0x0C            // IPv6 DNS server
    case gateway6 = 0x0D        // IPv6 gateway
    case serverConfig = 0x0E    // Server configuration data
    case keepAlive = 0x0F       // Keep alive interval
    case rejectReason = 0x10    // Authentication rejection reason
    
    /**
     * NAME: expectedLength
     *
     * DESCRIPTION:
     *     Returns expected total length (including type and length bytes) for fixed-size attributes.
     *     Used for packet parsing validation and error recovery.
     *
     * RETURNS:
     *     Int - Expected length in bytes, 0 if variable length
     */
    public var expectedLength: Int {
        switch self {
        case .ip, .gateway, .netmask:
            return 6    // Type(1) + Length(1) + IPv4(4)
        case .dns:
            return 10   // Type(1) + Length(1) + IPv4(4) * 2
        case .mtu, .keepAlive:
            return 4    // Type(1) + Length(1) + Value(2) - consistent with Go backend
        case .ip6, .dns6, .gateway6:
            return 18   // Type(1) + Length(1) + IPv6(16)
        case .encrypt, .dupPkt, .rejectReason:
            return 3    // Type(1) + Length(1) + Value(1)
        case .username, .password, .link, .serverConfig:
            return 0    // Variable length
        }
    }
    
    /**
     * NAME: description
     *
     * DESCRIPTION:
     *     Provides human-readable description of attribute type.
     *
     * RETURNS:
     *     String - Attribute type description
     */
    public var description: String {
        switch self {
        case .username: return "USERNAME"
        case .password: return "PASSWORD"
        case .mtu: return "MTU"
        case .ip: return "IP"
        case .dns: return "DNS"
        case .gateway: return "GATEWAY"
        case .netmask: return "NETMASK"
        case .encrypt: return "ENCRYPT"
        case .dupPkt: return "DUPPKT"
        case .link: return "LINK"
        case .ip6: return "IP6"
        case .dns6: return "DNS6"
        case .gateway6: return "GATEWAY6"
        case .serverConfig: return "SERVER_CONFIG"
        case .keepAlive: return "KEEP_ALIVE"
        case .rejectReason: return "REJECT_REASON"
        }
    }
}

/**
 * NAME: TLVAttribute
 *
 * DESCRIPTION:
 *     Represents a single TLV attribute with type, length, and value.
 *     Provides encoding and decoding functionality compatible with Go backend.
 *
 * PROPERTIES:
 *     type - Attribute type identifier
 *     value - Attribute value data
 */
public struct TLVAttribute {
    public let type: TLVAttributeType
    public let value: Data
    
    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Creates a TLV attribute with specified type and value.
     *
     * PARAMETERS:
     *     type - Attribute type identifier
     *     value - Attribute value data
     */
    public init(type: TLVAttributeType, value: Data) {
        self.type = type
        self.value = value
    }
    
    /**
     * NAME: length
     *
     * DESCRIPTION:
     *     Computed property that returns the total length including type and length bytes.
     *     This matches Go backend behavior where length field includes header bytes.
     *
     * RETURNS:
     *     UInt8 - Total length including type(1) + length(1) + value bytes
     */
    public var length: UInt8 {
        return UInt8(value.count + 2)  // Include type and length bytes (Go backend behavior)
    }

    /**
     * NAME: valueLength
     *
     * DESCRIPTION:
     *     Computed property that returns only the value data length.
     *
     * RETURNS:
     *     UInt8 - Length of value data only
     */
    public var valueLength: UInt8 {
        return UInt8(value.count)
    }
}

// MARK: - Convenience Initializers
extension TLVAttribute {
    /**
     * NAME: init(mtu:)
     *
     * DESCRIPTION:
     *     Creates MTU attribute with 2-byte integer value (consistent with Go backend).
     *
     * PARAMETERS:
     *     mtu - MTU value as UInt16
     */
    public init(mtu: UInt16) {
        let data = withUnsafeBytes(of: mtu.bigEndian) { Data($0) }
        self.init(type: .mtu, value: data)
    }
    
    /**
     * NAME: init(username:)
     *
     * DESCRIPTION:
     *     Creates username attribute with UTF-8 encoded string value.
     *
     * PARAMETERS:
     *     username - Username string
     */
    public init(username: String) {
        let data = username.data(using: .utf8) ?? Data()
        self.init(type: .username, value: data)
    }
    
    /**
     * NAME: init(encryptedPassword:)
     *
     * DESCRIPTION:
     *     Creates password attribute with encrypted password data.
     *     Password should be AES encrypted using MD5("mw" + username) as key.
     *
     * PARAMETERS:
     *     encryptedPassword - AES encrypted password data (16 bytes)
     */
    public init(encryptedPassword: Data) {
        self.init(type: .password, value: encryptedPassword)
    }
    
    /**
     * NAME: init(ipAddress:)
     *
     * DESCRIPTION:
     *     Creates IP address attribute with IPv4 address.
     *
     * PARAMETERS:
     *     ipAddress - IPv4 address string (e.g., "***********")
     *
     * THROWS:
     *     ProtocolError.invalidIPAddress - If IP address format is invalid
     */
    public init(ipAddress: String) throws {
        let components = ipAddress.split(separator: ".").compactMap { UInt8($0) }
        guard components.count == 4 else {
            throw ProtocolError.invalidIPAddress
        }
        let data = Data(components)
        self.init(type: .ip, value: data)
    }
    
    /**
     * NAME: init(gateway:)
     *
     * DESCRIPTION:
     *     Creates gateway attribute with IPv4 address.
     *
     * PARAMETERS:
     *     gateway - Gateway IPv4 address string
     *
     * THROWS:
     *     ProtocolError.invalidIPAddress - If IP address format is invalid
     */
    public init(gateway: String) throws {
        let components = gateway.split(separator: ".").compactMap { UInt8($0) }
        guard components.count == 4 else {
            throw ProtocolError.invalidIPAddress
        }
        let data = Data(components)
        self.init(type: .gateway, value: data)
    }
    
    /**
     * NAME: init(dns:)
     *
     * DESCRIPTION:
     *     Creates DNS attribute with IPv4 address(es).
     *     Supports single DNS server (4 bytes) or dual DNS servers (8 bytes).
     *
     * PARAMETERS:
     *     dns - DNS server IPv4 address string
     *
     * THROWS:
     *     ProtocolError.invalidIPAddress - If IP address format is invalid
     */
    public init(dns: String) throws {
        let components = dns.split(separator: ".").compactMap { UInt8($0) }
        guard components.count == 4 else {
            throw ProtocolError.invalidIPAddress
        }
        let data = Data(components)
        self.init(type: .dns, value: data)
    }
    
    /**
     * NAME: init(encryptionMethod:)
     *
     * DESCRIPTION:
     *     Creates encryption method attribute.
     *
     * PARAMETERS:
     *     encryptionMethod - Encryption method to use
     */
    public init(encryptionMethod: EncryptionMethod) {
        let data = Data([encryptionMethod.rawValue])
        self.init(type: .encrypt, value: data)
    }

    /**
     * NAME: init(ip:)
     *
     * DESCRIPTION:
     *     Creates TLV attribute for IPv4 address.
     *
     * PARAMETERS:
     *     ip - IPv4 address
     */
    public init(ip: IPv4Address) {
        let data = Data(ip.rawValue)
        self.init(type: .ip, value: data)
    }

    /**
     * NAME: init(gateway:)
     *
     * DESCRIPTION:
     *     Creates TLV attribute for gateway IPv4 address.
     *
     * PARAMETERS:
     *     gateway - Gateway IPv4 address
     */
    public init(gateway: IPv4Address) {
        let data = Data(gateway.rawValue)
        self.init(type: .gateway, value: data)
    }

    /**
     * NAME: init(netmask:)
     *
     * DESCRIPTION:
     *     Creates TLV attribute for netmask IPv4 address.
     *
     * PARAMETERS:
     *     netmask - Netmask IPv4 address
     */
    public init(netmask: IPv4Address) {
        let data = Data(netmask.rawValue)
        self.init(type: .netmask, value: data)
    }

    /**
     * NAME: init(dns:)
     *
     * DESCRIPTION:
     *     Creates TLV attribute for DNS servers.
     *
     * PARAMETERS:
     *     dns - Array of DNS server IPv4 addresses
     */
    public init(dns: [IPv4Address]) {
        var data = Data()
        for dnsServer in dns {
            data.append(Data(dnsServer.rawValue))
        }
        self.init(type: .dns, value: data)
    }

    /**
     * NAME: init(keepAlive:)
     *
     * DESCRIPTION:
     *     Creates TLV attribute for keep alive interval.
     *
     * PARAMETERS:
     *     keepAlive - Keep alive interval in seconds
     */
    public init(keepAlive: UInt16) {
        let data = withUnsafeBytes(of: keepAlive.bigEndian) { Data($0) }
        self.init(type: .keepAlive, value: data)
    }

    /**
     * NAME: init(from:)
     *
     * DESCRIPTION:
     *     Creates TLV attribute from binary data.
     *
     * PARAMETERS:
     *     data - Binary data containing TLV attribute
     *
     * THROWS:
     *     ProtocolError.invalidTLVLength - If data is too short
     *     ProtocolError.invalidTLVType - If attribute type is unknown
     */
    public init(from data: Data) throws {
        guard data.count >= 2 else {
            throw ProtocolError.invalidTLVLength
        }

        guard let attributeType = TLVAttributeType(rawValue: data[0]) else {
            throw ProtocolError.invalidTLVType
        }

        let length = Int(data[1])
        guard data.count >= length else {
            throw ProtocolError.invalidTLVLength
        }

        let valueLength = length - 2
        let value = data.subdata(in: 2..<2 + valueLength)

        self.init(type: attributeType, value: value)
    }
}

// MARK: - Value Extraction
extension TLVAttribute {
    /**
     * NAME: mtuValue
     *
     * DESCRIPTION:
     *     Extracts MTU value from attribute data.
     *
     * RETURNS:
     *     UInt16? - MTU value if valid, nil otherwise
     */
    public var mtuValue: UInt16? {
        guard type == .mtu, value.count >= 2 else { return nil }
        return value.withUnsafeBytes { $0.load(as: UInt16.self).bigEndian }
    }
    
    /**
     * NAME: stringValue
     *
     * DESCRIPTION:
     *     Extracts string value from attribute data.
     *
     * RETURNS:
     *     String? - UTF-8 decoded string if valid, nil otherwise
     */
    public var stringValue: String? {
        return String(data: value, encoding: .utf8)
    }
    
    /**
     * NAME: ipAddressValue
     *
     * DESCRIPTION:
     *     Extracts IPv4 address from attribute data.
     *
     * RETURNS:
     *     String? - IPv4 address string if valid, nil otherwise
     */
    public var ipAddressValue: String? {
        guard value.count >= 4 else { return nil }
        return "\(value[0]).\(value[1]).\(value[2]).\(value[3])"
    }
    
    /**
     * NAME: encryptionMethodValue
     *
     * DESCRIPTION:
     *     Extracts encryption method from attribute data.
     *
     * RETURNS:
     *     EncryptionMethod? - Encryption method if valid, nil otherwise
     */
    public var encryptionMethodValue: EncryptionMethod? {
        guard type == .encrypt, value.count >= 1 else { return nil }
        return EncryptionMethod(rawValue: value[0])
    }
}

// MARK: - Serialization
extension TLVAttribute {
    /**
     * NAME: toData
     *
     * DESCRIPTION:
     *     Converts TLV attribute to binary format.
     *     Format: [Type:1][Length:1][Value:Length-2]
     *     Length includes type and length bytes (consistent with Go backend).
     *
     * RETURNS:
     *     Data - Binary representation of TLV attribute
     */
    public func toData() -> Data {
        var data = Data(capacity: Int(length))
        data.append(type.rawValue)
        data.append(length)
        data.append(value)
        return data
    }
    
    /**
     * NAME: parse(from:offset:)
     *
     * DESCRIPTION:
     *     Parses TLV attribute from binary data at specified offset.
     *     Handles error recovery using expected lengths for known attribute types.
     *
     * PARAMETERS:
     *     data - Binary data containing TLV attributes
     *     offset - Starting offset for parsing
     *
     * RETURNS:
     *     (TLVAttribute, Int) - Parsed attribute and next offset
     *
     * THROWS:
     *     ProtocolError.invalidTLVFormat - If TLV format is invalid
     *     ProtocolError.invalidTLVType - If attribute type is unknown
     *     ProtocolError.invalidTLVLength - If attribute length is invalid
     */
    public static func parse(from data: Data, offset: Int) throws -> (TLVAttribute, Int) {
        guard offset + 2 <= data.count else {
            let logger = LoggingSystem.shared.logger(for: "TLV")
            logger.error("TLV Parse Error: Not enough data for header at offset \(offset), data.count=\(data.count)")
            throw ProtocolError.invalidTLVFormat
        }

        let rawType = data[offset]
        let rawLength = data[offset + 1]

        let logger = LoggingSystem.shared.logger(for: "TLV")
        // logger.info("TLV Parse: offset=\(offset), type=0x\(String(format: "%02X", rawType)), length=\(rawLength), remaining=\(data.count - offset)") // Debug log commented for production

        guard let attributeType = TLVAttributeType(rawValue: rawType) else {
            logger.error("TLV Parse Error: Unknown attribute type 0x\(String(format: "%02X", rawType))")
            throw ProtocolError.invalidTLVType
        }

        var attributeLength = Int(rawLength)

        // Validate length (must include at least type and length bytes)
        guard attributeLength >= 2 else {
            logger.error("TLV Parse Error: Invalid length \(attributeLength), must be >= 2")
            throw ProtocolError.invalidTLVLength
        }
        
        // Check if declared length exceeds remaining data
        if offset + attributeLength > data.count {
            logger.error("TLV Parse Error: Length exceeds remaining data. offset=\(offset), attributeLength=\(attributeLength), data.count=\(data.count)")
            // Try to use expected length for error recovery
            let expectedLength = attributeType.expectedLength
            if expectedLength > 0 && offset + expectedLength <= data.count {
                // logger.info("TLV Parse: Using expected length \(expectedLength) instead of declared \(attributeLength)") // Debug log commented for production
                attributeLength = expectedLength
            } else {
                logger.error("TLV Parse Error: Cannot recover, expected=\(expectedLength)")
                throw ProtocolError.invalidTLVLength
            }
        }
        
        let valueLength = attributeLength - 2
        let value = data.subdata(in: offset + 2..<offset + 2 + valueLength)

        let attribute = TLVAttribute(type: attributeType, value: value)
        return (attribute, offset + attributeLength)
    }
    
    /**
     * NAME: parseAll(from:)
     *
     * DESCRIPTION:
     *     Parses all TLV attributes from binary data.
     *
     * PARAMETERS:
     *     data - Binary data containing TLV attributes
     *
     * RETURNS:
     *     [TLVAttribute] - Array of parsed attributes
     *
     * THROWS:
     *     ProtocolError - If parsing fails
     */
    public static func parseAll(from data: Data) throws -> [TLVAttribute] {
        let logger = LoggingSystem.shared.logger(for: "TLV")
        // logger.info("TLV ParseAll: Starting with data.count=\(data.count)") // Debug log commented for production
        // logger.info("TLV ParseAll: Data hex=\(data.map { String(format: "%02X", $0) }.joined(separator: " "))") // Debug log commented for production

        var attributes: [TLVAttribute] = []
        var offset = 0

        while offset < data.count {
            do {
                let (attribute, nextOffset) = try parse(from: data, offset: offset)
                attributes.append(attribute)
                offset = nextOffset
            } catch ProtocolError.invalidTLVType {
                // Skip unknown TLV attribute types
                // Try to skip this attribute by reading length and advancing
                if offset + 2 <= data.count {
                    let attributeLength = Int(data[offset + 1])
                    if attributeLength >= 2 && offset + attributeLength <= data.count {
                        offset += attributeLength
                        continue
                    }
                }
                // If we can't determine length, skip one byte and try again
                offset += 1
            } catch {
                // For other errors, re-throw
                throw error
            }
        }

        return attributes
    }
}

// MARK: - CustomStringConvertible
extension TLVAttribute: CustomStringConvertible {
    /**
     * NAME: description
     *
     * DESCRIPTION:
     *     Provides string representation of TLV attribute for debugging.
     *
     * RETURNS:
     *     String - Attribute description with type, length, and value summary
     */
    public var description: String {
        let valueDesc: String
        switch type {
        case .mtu:
            valueDesc = mtuValue.map { "MTU: \($0)" } ?? "Invalid MTU"
        case .username:
            valueDesc = stringValue ?? "Invalid Username"
        case .password:
            valueDesc = "Encrypted Password (\(value.count) bytes)"
        case .ip, .gateway, .netmask:
            valueDesc = ipAddressValue ?? "Invalid IP"
        case .dns:
            valueDesc = ipAddressValue ?? "Invalid DNS"
        case .encrypt:
            valueDesc = encryptionMethodValue?.description ?? "Invalid Encryption"
        default:
            valueDesc = "\(value.count) bytes"
        }
        
        return "TLVAttribute(type: \(type.description), length: \(length), value: \(valueDesc))"
    }
}
