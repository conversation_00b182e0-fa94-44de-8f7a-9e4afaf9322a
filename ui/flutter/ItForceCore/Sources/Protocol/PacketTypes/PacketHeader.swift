/**
 * FILE: PacketHeader.swift
 *
 * DESCRIPTION:
 *     Defines SDWAN protocol packet header structure and related types.
 *     Provides complete packet type definitions, encryption methods, and serialization.
 *     Ensures 100% compatibility with Go backend implementation.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation

/**
 * NAME: PacketType
 *
 * DESCRIPTION:
 *     Enumeration of all supported packet types in SDWAN protocol.
 *     Values must match exactly with Go backend constants for compatibility.
 *
 * METHODS:
 *     rawValue - UInt8 value matching Go backend constants
 *     isDataPacket - Checks if packet type carries IP data
 *     description - Human-readable packet type description
 */
public enum PacketType: UInt8, CaseIterable {
    case openReject = 0x11      // Server rejects client connection
    case openAck = 0x12         // Server acknowledges client connection
    case open = 0x13            // Client requests connection
    case data = 0x14            // IPv4 data packet
    case echoRequest = 0x15     // Echo request (keepalive)
    case echoResponse = 0x16    // Echo response (keepalive)
    case close = 0x17           // Connection close
    case dataEncrypt = 0x18     // Encrypted IPv4 data packet
    case dataDup = 0x19         // Duplicate IPv4 data packet
    case dataEncDup = 0x20      // Encrypted duplicate IPv4 data packet
    case ipFrag = 0x22          // IPv4 fragmented data packet
    case data6 = 0x23           // IPv6 data packet
    case ipFrag6 = 0x24         // IPv6 fragmented data packet
    case segRT = 0x28           // Segment routing data packet
    case pingRequest = 0x29     // Ping request
    case pingResponse = 0x2A    // Ping response
    
    /**
     * NAME: isDataPacket
     *
     * DESCRIPTION:
     *     Determines if this packet type carries IP data traffic.
     *
     * RETURNS:
     *     Bool - True if packet carries IP data
     */
    public var isDataPacket: Bool {
        switch self {
        case .data, .dataEncrypt, .dataDup, .dataEncDup, .ipFrag, .data6, .ipFrag6, .segRT:
            return true
        default:
            return false
        }
    }
    
    /**
     * NAME: description
     *
     * DESCRIPTION:
     *     Provides human-readable description of packet type.
     *
     * RETURNS:
     *     String - Packet type description
     */
    public var description: String {
        switch self {
        case .openReject: return "OPENREJ"
        case .openAck: return "OPENACK"
        case .open: return "OPEN"
        case .data: return "DATA"
        case .echoRequest: return "ECHOREQ"
        case .echoResponse: return "ECHORES"
        case .close: return "CLOSE"
        case .dataEncrypt: return "DATAENC"
        case .dataDup: return "DATADUP"
        case .dataEncDup: return "DATAENCDUP"
        case .ipFrag: return "IPFRAG"
        case .data6: return "DATA6"
        case .ipFrag6: return "IPFRAG6"
        case .segRT: return "SEGRT"
        case .pingRequest: return "PINGREQ"
        case .pingResponse: return "PINGRES"
        }
    }
}

/**
 * NAME: EncryptionMethod
 *
 * DESCRIPTION:
 *     Enumeration of supported encryption methods.
 *     Must be compatible with Go backend encryption constants.
 *
 * METHODS:
 *     rawValue - UInt8 value matching Go backend constants
 *     description - Human-readable encryption method description
 */
public enum EncryptionMethod: UInt8, CaseIterable, Sendable {
    case none = 0x00            // No encryption
    case xor = 0x01             // XOR encryption
    case aes = 0x02             // AES encryption
    
    /**
     * NAME: description
     *
     * DESCRIPTION:
     *     Provides human-readable description of encryption method.
     *
     * RETURNS:
     *     String - Encryption method description
     */
    public var description: String {
        switch self {
        case .none: return "NONE"
        case .xor: return "XOR"
        case .aes: return "AES"
        }
    }
}

/**
 * NAME: PacketHeader
 *
 * DESCRIPTION:
 *     Represents SDWAN protocol packet header structure.
 *     8-byte header consistent with protocol specification and Go backend.
 *
 * PROPERTIES:
 *     type - Packet type identifier
 *     encrypt - Encryption method used
 *     sessionID - Session ID for connection tracking (SID in Go)
 *     token - Authentication token
 */
public struct PacketHeader {
    public let type: PacketType
    public let encrypt: EncryptionMethod
    public let sessionID: UInt16        // Maps to SID in Go backend
    public let token: UInt32
    
    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Creates a new packet header with specified parameters.
     *
     * PARAMETERS:
     *     type - Packet type identifier
     *     encrypt - Encryption method to use
     *     sessionID - Session ID for connection tracking
     *     token - Authentication token
     */
    public init(type: PacketType, encrypt: EncryptionMethod, sessionID: UInt16, token: UInt32) {
        self.type = type
        self.encrypt = encrypt
        self.sessionID = sessionID
        self.token = token
    }
}

// MARK: - Constants
extension PacketHeader {
    /**
     * NAME: headerSize
     *
     * DESCRIPTION:
     *     Size of packet header in bytes (consistent with Go backend).
     *
     * RETURNS:
     *     Int - Header size (8 bytes)
     */
    public static let headerSize: Int = 8
}

// MARK: - Serialization
extension PacketHeader {
    /**
     * NAME: toData
     *
     * DESCRIPTION:
     *     Converts packet header to binary data format.
     *     Uses big-endian byte order for network transmission (consistent with Go backend).
     *
     * RETURNS:
     *     Data - 8-byte binary representation of header
     */
    public func toData() -> Data {
        var data = Data(capacity: PacketHeader.headerSize)
        data.append(type.rawValue)
        data.append(encrypt.rawValue)
        data.append(contentsOf: withUnsafeBytes(of: sessionID.bigEndian) { Data($0) })
        data.append(contentsOf: withUnsafeBytes(of: token.bigEndian) { Data($0) })
        return data
    }
    
    /**
     * NAME: init(from:)
     *
     * DESCRIPTION:
     *     Creates packet header from binary data.
     *     Validates data length and parses header fields using big-endian byte order.
     *
     * PARAMETERS:
     *     data - Binary data containing header (minimum 8 bytes)
     *
     * THROWS:
     *     ProtocolError.invalidHeaderSize - If data length is less than 8 bytes
     *     ProtocolError.invalidPacketType - If packet type is unknown
     *     ProtocolError.invalidEncryptionMethod - If encryption method is unknown
     */
    public init(from data: Data) throws {
        guard data.count >= PacketHeader.headerSize else {
            throw ProtocolError.invalidHeaderSize
        }
        
        guard let packetType = PacketType(rawValue: data[0]) else {
            throw ProtocolError.invalidPacketType
        }
        
        guard let encryptMethod = EncryptionMethod(rawValue: data[1]) else {
            throw ProtocolError.invalidEncryptionMethod
        }
        
        self.type = packetType
        self.encrypt = encryptMethod
        self.sessionID = data.subdata(in: 2..<4).withUnsafeBytes { $0.load(as: UInt16.self).bigEndian }
        self.token = data.subdata(in: 4..<8).withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }
    }
}

// MARK: - CustomStringConvertible
extension PacketHeader: CustomStringConvertible {
    /**
     * NAME: description
     *
     * DESCRIPTION:
     *     Provides string representation of packet header for debugging.
     *
     * RETURNS:
     *     String - Header description with type, encryption, session ID, and token
     */
    public var description: String {
        return "PacketHeader(type: \(type.description), encrypt: \(encrypt.description), sessionID: \(sessionID), token: 0x\(String(token, radix: 16)))"
    }
}


