/**
 * FILE: SDWANPacket.swift
 *
 * DESCRIPTION:
 *     Defines SDWAN packet structure for protocol communication.
 *     Represents complete packet with header and data payload.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation

/**
 * NAME: SDWANPacket
 *
 * DESCRIPTION:
 *     Represents a complete SDWAN protocol packet.
 *     Contains packet header and data payload for network transmission.
 *
 * PROPERTIES:
 *     header - Packet header with type, encryption, session info
 *     data - Packet data payload
 */
public struct SDWANPacket {
    public let header: PacketHeader
    public let data: Data
    
    public init(header: PacketHeader, data: Data) {
        self.header = header
        self.data = data
    }
    
    /**
     * NAME: init(from:)
     *
     * DESCRIPTION:
     *     Creates packet from binary data.
     *     Parses header and extracts data payload.
     *
     * PARAMETERS:
     *     data - Binary packet data
     *
     * THROWS:
     *     ProtocolError.invalidHeaderSize - If data too small for header
     */
    public init(from data: Data) throws {
        guard data.count >= 8 else {
            throw ProtocolError.invalidHeaderSize
        }
        
        self.header = try PacketHeader(from: data.prefix(8))
        self.data = data.dropFirst(8)
    }
    
    /**
     * NAME: toData
     *
     * DESCRIPTION:
     *     Converts packet to binary data for transmission.
     *
     * RETURNS:
     *     Data - Complete packet as binary data
     */
    public func toData() -> Data {
        var result = header.toData()
        result.append(data)
        return result
    }
    
    /**
     * NAME: totalSize
     *
     * DESCRIPTION:
     *     Returns total packet size including header.
     *
     * RETURNS:
     *     Int - Total packet size in bytes
     */
    public var totalSize: Int {
        return 8 + data.count
    }
}
