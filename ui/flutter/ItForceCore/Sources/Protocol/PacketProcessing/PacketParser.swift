/**
 * FILE: PacketParser.swift
 *
 * DESCRIPTION:
 *     Parses SDWAN protocol packets from raw binary data.
 *     Handles packet validation, signature verification, and TLV attribute extraction.
 *     Ensures 100% compatibility with Go backend packet parsing logic.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import CryptoKit

/**
 * NAME: ParsedPacket
 *
 * DESCRIPTION:
 *     Represents a parsed SDWAN protocol packet with extracted components.
 *     Contains header, signature (if present), attributes, and payload data.
 *
 * PROPERTIES:
 *     header - Parsed packet header
 *     signature - MD5 signature (for control packets only)
 *     attributes - TLV attributes (for control packets only)
 *     payload - IP packet data (for data packets only)
 */
public struct ParsedPacket {
    public let header: PacketHeader
    public let signature: Data?
    public let attributes: [TLVAttribute]
    public let payload: Data
    
    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Creates a parsed packet with specified components.
     *
     * PARAMETERS:
     *     header - Packet header
     *     signature - MD5 signature (optional)
     *     attributes - TLV attributes array
     *     payload - Payload data
     */
    public init(header: PacketHeader, signature: Data? = nil, attributes: [TLVAttribute] = [], payload: Data = Data()) {
        self.header = header
        self.signature = signature
        self.attributes = attributes
        self.payload = payload
    }
}

/**
 * NAME: PacketParser
 *
 * DESCRIPTION:
 *     Main packet parser class for processing SDWAN protocol packets.
 *     Provides methods for parsing different packet types with proper validation.
 *
 * METHODS:
 *     parsePacket - Parses complete packet from raw data
 *     parseHeader - Parses packet header only
 *     verifySignature - Verifies packet signature
 *     extractAttributes - Extracts TLV attributes from control packets
 */
public class PacketParser {
    
    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Creates a new packet parser instance.
     */
    public init() {}
    
    /**
     * NAME: parsePacket
     *
     * DESCRIPTION:
     *     Parses complete SDWAN protocol packet from raw binary data.
     *     Automatically handles different packet formats based on packet type.
     *
     * PARAMETERS:
     *     data - Raw packet data bytes
     *
     * RETURNS:
     *     ParsedPacket - Parsed packet with all components
     *
     * THROWS:
     *     ProtocolError.invalidHeaderSize - If packet is too short
     *     ProtocolError.invalidPacketType - If packet type is unknown
     *     ProtocolError.invalidSignature - If signature verification fails
     */
    public func parsePacket(from data: Data) throws -> ParsedPacket {
        // Validate minimum packet size
        guard data.count >= PacketHeader.headerSize else {
            throw ProtocolError.invalidHeaderSize
        }

        // Parse packet header
        let header = try parseHeader(from: data)

        // Debug: Print header information
        // let logger = LoggingSystem.shared.logger(for: "PacketParser")
        // logger.info("Parsed packet header", metadata: [
        //     "type": "0x\(String(format: "%02X", header.type.rawValue))",
        //     "type_name": "\(header.type.description)",
        //     "encrypt": "0x\(String(format: "%02X", header.encrypt.rawValue))",
        //     "sessionID": "0x\(String(format: "%04X", header.sessionID))",
        //     "token": "0x\(String(format: "%08X", header.token))",
        //     "isDataPacket": "\(header.type.isDataPacket)"
        // ])

        // Extract payload data (everything after header)
        let payloadData = data.subdata(in: PacketHeader.headerSize..<data.count)

        // Handle different packet types
        if header.type.isDataPacket {
            // Data packets: Header + IP payload (no signature, no TLV attributes)
            //logger.info("Processing as data packet", metadata: [
            //    "payload_size": "\(payloadData.count)"
            //])
            return ParsedPacket(header: header, payload: payloadData)
        } else if header.type == .openReject {
            // OPENREJECT packets: Header + Signature + Reject reason + Optional message
            // Special handling - no TLV attributes
            //logger.info("Processing as OPENREJECT packet")
            guard payloadData.count >= 16 else {
                throw ProtocolError.invalidSignature
            }

            // Extract signature (first 16 bytes of payload)
            let signature = payloadData.prefix(16)

            // Verify signature
            try verifySignature(header: header, signature: Data(signature))

            // Extract remaining data (reject reason + optional message)
            let remainingData = payloadData.subdata(in: 16..<payloadData.count)

            return ParsedPacket(header: header, signature: Data(signature), payload: remainingData)
        } else if header.type == .echoRequest || header.type == .echoResponse {
            // ECHO packets: Header + Signature + Timestamp + Delay info + SDRT tag
            // Special handling - no TLV attributes, fixed format
            //logger.info("Processing as ECHO packet", metadata: [
            //    "type": "\(header.type.description)",
            //    "payload_size": "\(payloadData.count)"
            //])

            // ECHO packets should be exactly 40 bytes payload (16+8+12+4)
            // But we'll be flexible and just require minimum signature
            guard payloadData.count >= 16 else {
                throw ProtocolError.invalidSignature
            }

            // Extract signature (first 16 bytes of payload)
            let signature = payloadData.prefix(16)

            // Verify signature
            try verifySignature(header: header, signature: Data(signature))

            // Extract remaining data (timestamp + delay info + SDRT tag)
            let remainingData = payloadData.subdata(in: 16..<payloadData.count)

            return ParsedPacket(header: header, signature: Data(signature), payload: remainingData)
        } else {
            // Other control packets: Header + Signature + TLV attributes
            //logger.info("Processing as control packet", metadata: [
            //   "payload_size": "\(payloadData.count)"
            //])
            return try parseControlPacket(header: header, payloadData: payloadData)
        }
    }
    
    /**
     * NAME: parseHeader
     *
     * DESCRIPTION:
     *     Parses packet header from raw binary data.
     *     Validates header format and extracts all header fields.
     *
     * PARAMETERS:
     *     data - Raw packet data (minimum 8 bytes)
     *
     * RETURNS:
     *     PacketHeader - Parsed packet header
     *
     * THROWS:
     *     ProtocolError.invalidHeaderSize - If data is too short
     *     ProtocolError.invalidPacketType - If packet type is unknown
     *     ProtocolError.invalidEncryptionMethod - If encryption method is unknown
     */
    public func parseHeader(from data: Data) throws -> PacketHeader {
        guard data.count >= PacketHeader.headerSize else {
            throw ProtocolError.invalidHeaderSize
        }
        
        return try PacketHeader(from: data.prefix(PacketHeader.headerSize))
    }
    
    /**
     * NAME: parseControlPacket
     *
     * DESCRIPTION:
     *     Parses control packet with signature and TLV attributes.
     *     Handles signature verification and attribute extraction.
     *
     * PARAMETERS:
     *     header - Parsed packet header
     *     payloadData - Payload data after header
     *
     * RETURNS:
     *     ParsedPacket - Parsed control packet
     *
     * THROWS:
     *     ProtocolError.invalidSignature - If signature is missing or invalid
     *     ProtocolError.invalidTLVFormat - If TLV parsing fails
     */
    private func parseControlPacket(header: PacketHeader, payloadData: Data) throws -> ParsedPacket {
        // Control packets must have at least 16 bytes for MD5 signature
        guard payloadData.count >= 16 else {
            throw ProtocolError.invalidSignature
        }
        
        // Extract signature (first 16 bytes of payload)
        let signature = payloadData.prefix(16)
        
        // Verify signature
        try verifySignature(header: header, signature: Data(signature))
        
        // Extract TLV attributes (remaining data after signature)
        let attributesData = payloadData.subdata(in: 16..<payloadData.count)
        let attributes = try extractAttributes(from: attributesData)
        
        return ParsedPacket(header: header, signature: Data(signature), attributes: attributes)
    }
    
    /**
     * NAME: verifySignature
     *
     * DESCRIPTION:
     *     Verifies packet signature using MD5 hash calculation.
     *     Uses same algorithm as Go backend: MD5(header + "mw").
     *
     * PARAMETERS:
     *     header - Packet header to verify
     *     signature - Provided signature to check
     *
     * THROWS:
     *     ProtocolError.invalidSignature - If signature verification fails
     */
    public func verifySignature(header: PacketHeader, signature: Data) throws {
        // Calculate expected signature
        let headerData = header.toData()
        let salt = Data([109, 119]) // ASCII values for 'm' and 'w'
        let signatureData = headerData + salt
        let expectedSignature = Insecure.MD5.hash(data: signatureData)
        
        // Compare signatures
        guard signature == Data(expectedSignature) else {
            throw ProtocolError.invalidSignature
        }
    }
    
    /**
     * NAME: extractAttributes
     *
     * DESCRIPTION:
     *     Extracts TLV attributes from control packet data.
     *     Handles parsing errors and provides error recovery.
     *
     * PARAMETERS:
     *     data - TLV attributes data
     *
     * RETURNS:
     *     [TLVAttribute] - Array of parsed attributes
     *
     * THROWS:
     *     ProtocolError.invalidTLVFormat - If TLV parsing fails
     */
    public func extractAttributes(from data: Data) throws -> [TLVAttribute] {
        guard !data.isEmpty else {
            return []
        }
        
        return try TLVAttribute.parseAll(from: data)
    }
}

// MARK: - Specialized Parsing Methods
extension PacketParser {
    /**
     * NAME: parseOpenAckPacket
     *
     * DESCRIPTION:
     *     Parses OPENACK packet and extracts authentication result.
     *     Extracts session info and configuration parameters from TLV attributes.
     *
     * PARAMETERS:
     *     data - OPENACK packet data
     *
     * RETURNS:
     *     (sessionID: UInt16, token: UInt32, config: [String: String]) - Authentication result
     *
     * THROWS:
     *     ProtocolError.invalidPacketType - If packet is not OPENACK
     *     ProtocolError.invalidSignature - If signature verification fails
     */
    public func parseOpenAckPacket(from data: Data) throws -> (sessionID: UInt16, token: UInt32, config: [String: String]) {
        let packet = try parsePacket(from: data)
        
        // Validate packet type
        guard packet.header.type == .openAck else {
            throw ProtocolError.invalidPacketType
        }
        
        // Extract configuration from TLV attributes
        var config: [String: String] = [:]
        
        for attribute in packet.attributes {
            switch attribute.type {
            case .mtu:
                if let mtu = attribute.mtuValue {
                    config["mtu"] = String(mtu)
                }
            case .ip:
                if let ip = attribute.ipAddressValue {
                    config["ip"] = ip
                }
            case .gateway:
                if let gateway = attribute.ipAddressValue {
                    config["gateway"] = gateway
                }
            case .dns:
                if let dns = attribute.ipAddressValue {
                    config["dns"] = dns
                }
            case .netmask:
                if let netmask = attribute.ipAddressValue {
                    config["netmask"] = netmask
                }
            case .encrypt:
                if let encryptMethod = attribute.encryptionMethodValue {
                    config["encrypt"] = encryptMethod.description
                }
            default:
                // Skip unknown attributes
                break
            }
        }
        
        return (sessionID: packet.header.sessionID, token: packet.header.token, config: config)
    }
    
    /**
     * NAME: parseOpenRejectPacket
     *
     * DESCRIPTION:
     *     Parses OPENREJ packet and extracts rejection reason.
     *
     * PARAMETERS:
     *     data - OPENREJ packet data
     *
     * RETURNS:
     *     (errorCode: Int, errorMessage: String) - Rejection information
     *
     * THROWS:
     *     ProtocolError.invalidPacketType - If packet is not OPENREJ
     *     ProtocolError.invalidSignature - If signature verification fails
     */
    public func parseOpenRejectPacket(from data: Data) throws -> (errorCode: Int, errorMessage: String) {
        // Parse packet using the general parser (which now handles OPENREJECT specially)
        let packet = try parsePacket(from: data)

        // Validate packet type
        guard packet.header.type == .openReject else {
            throw ProtocolError.invalidPacketType
        }

        // Extract reject reason and message from payload
        // payload now contains: Reject reason(1 byte) + Optional message
        let payloadData = packet.payload ?? Data()

        let errorCode: Int
        let errorMessage: String

        if !payloadData.isEmpty {
            // First byte is the error code
            errorCode = Int(payloadData[0])

            // Rest of the data (if any) is the error message string
            if payloadData.count > 1 {
                let messageData = payloadData.subdata(in: 1..<payloadData.count)
                if let serverMessage = String(data: messageData, encoding: .utf8) {
                    errorMessage = "\(getRejectReasonString(errorCode)) (\(serverMessage))"
                } else {
                    errorMessage = getRejectReasonString(errorCode)
                }
            } else {
                errorMessage = getRejectReasonString(errorCode)
            }
        } else {
            errorCode = 0
            errorMessage = "Unknown rejection reason"
        }

        return (errorCode: errorCode, errorMessage: errorMessage)
    }
    
    /**
     * NAME: parseEchoResponsePacket
     *
     * DESCRIPTION:
     *     Parses ECHO response packet and extracts timing information.
     *
     * PARAMETERS:
     *     data - ECHO response packet data
     *
     * RETURNS:
     *     (timestamp: UInt64, currentDelay: UInt32, minDelay: UInt32, maxDelay: UInt32) - Timing info
     *
     * THROWS:
     *     ProtocolError.invalidPacketType - If packet is not ECHO response
     *     ProtocolError.packetTooShort - If packet data is insufficient
     */
    public func parseEchoResponsePacket(from data: Data) throws -> (timestamp: UInt64, currentDelay: UInt32, minDelay: UInt32, maxDelay: UInt32) {
        let packet = try parsePacket(from: data)
        
        // Validate packet type
        guard packet.header.type == .echoResponse else {
            throw ProtocolError.invalidPacketType
        }
        
        // Check data length (signature + timestamp + delays = 16 + 8 + 4 + 4 + 4 = 36 bytes minimum)
        let payloadData = data.subdata(in: PacketHeader.headerSize..<data.count)
        guard payloadData.count >= 36 else {
            throw ProtocolError.packetTooShort
        }
        
        // Parse timing data (skip 16-byte signature)
        let timestamp = payloadData.subdata(in: 16..<24).withUnsafeBytes { $0.load(as: UInt64.self).bigEndian }
        let currentDelay = payloadData.subdata(in: 24..<28).withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }
        let minDelay = payloadData.subdata(in: 28..<32).withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }
        let maxDelay = payloadData.subdata(in: 32..<36).withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }
        
        return (timestamp: timestamp, currentDelay: currentDelay, minDelay: minDelay, maxDelay: maxDelay)
    }
    
    /**
     * NAME: getRejectReasonString
     *
     * DESCRIPTION:
     *     Converts rejection error code to human-readable message.
     *
     * PARAMETERS:
     *     code - Error code from OPENREJ packet
     *
     * RETURNS:
     *     String - Human-readable error message
     */
    private func getRejectReasonString(_ code: Int) -> String {
        switch code {
        case 0: return "Unknown rejection reason"
        case 1: return "Invalid username"
        case 2: return "Invalid password"
        case 3: return "Server is full"
        case 4: return "Server error"
        case 5: return "Unsupported feature"
        case 6: return "Account expired"
        case 7: return "Account disabled"
        case 8: return "Maximum sessions reached"
        case 9: return "Invalid token"
        default: return "Unknown error"
        }
    }
}
