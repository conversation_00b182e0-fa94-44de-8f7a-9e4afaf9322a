/**
 * FILE: PacketBuilder.swift
 *
 * DESCRIPTION:
 *     Builds SDWAN protocol packets with proper formatting and signatures.
 *     Handles MD5 signature calculation, TLV attribute encoding, and packet assembly.
 *     Ensures 100% compatibility with Go backend packet construction.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import CryptoKit

/**
 * NAME: PacketBuilder
 *
 * DESCRIPTION:
 *     Main packet builder class for constructing SDWAN protocol packets.
 *     Provides methods for building different packet types with proper signatures.
 *
 * METHODS:
 *     buildPacketWithSignature - Builds packet with MD5 signature
 *     buildDataPacket - Builds data packet without signature
 *     buildOpenPacket - Builds OPEN authentication packet
 *     buildEchoRequestPacket - Builds ECHO request packet
 *     calculateSignature - Calculates MD5 signature for packet
 */
public class PacketBuilder {
    
    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Creates a new packet builder instance.
     */
    public init() {}
    
    /**
     * NAME: buildPacketWithSignature
     *
     * DESCRIPTION:
     *     Builds packet with MD5 signature for control packets.
     *     Signature calculated as MD5(header + "mw") consistent with Go backend.
     *
     * PARAMETERS:
     *     header - Packet header
     *     attributes - TLV attributes array (optional)
     *
     * RETURNS:
     *     Data - Complete packet with header, signature, and attributes
     *
     * THROWS:
     *     ProtocolError.invalidPacketFormat - If packet construction fails
     */
    public func buildPacketWithSignature(header: PacketHeader, attributes: [TLVAttribute] = []) throws -> Data {
        // Calculate MD5 signature
        let signature = try calculateSignature(for: header)
        
        // Calculate total packet size
        let attributesSize = attributes.reduce(0) { $0 + Int($1.length) }
        let totalSize = PacketHeader.headerSize + signature.count + attributesSize
        
        // Build packet data
        var packetData = Data(capacity: totalSize)
        
        // Add header
        packetData.append(header.toData())
        
        // Add signature
        packetData.append(signature)
        
        // Add TLV attributes
        for attribute in attributes {
            packetData.append(attribute.toData())
        }
        
        return packetData
    }
    
    /**
     * NAME: buildDataPacket
     *
     * DESCRIPTION:
     *     Builds data packet without signature (data packets don't need signatures).
     *     Used for unencrypted data transmission.
     *
     * PARAMETERS:
     *     header - Packet header
     *     payload - IP packet data
     *
     * RETURNS:
     *     Data - Complete data packet with header and payload
     */
    public func buildDataPacket(header: PacketHeader, payload: Data) -> Data {
        var packetData = Data(capacity: PacketHeader.headerSize + payload.count)
        packetData.append(header.toData())
        packetData.append(payload)
        return packetData
    }

    /**
     * NAME: buildDataEncryptPacket
     *
     * DESCRIPTION:
     *     Builds encrypted data packet for secure data transmission.
     *     Compatible with Go backend CreateDataEncryptPacket function.
     *     Uses DATAENCRYPT packet type (0x18) and encrypts payload data.
     *
     * PARAMETERS:
     *     sessionID - Session ID for the connection
     *     token - Authentication token
     *     payload - IP packet data to encrypt
     *     encryptionMethod - Encryption method to use (XOR or AES)
     *     encryptor - Encryption service for data encryption
     *
     * RETURNS:
     *     Data - Complete encrypted data packet
     *
     * THROWS:
     *     ProtocolError.encryptionFailed - If encryption fails
     */
    public func buildDataEncryptPacket(
        sessionID: UInt16,
        token: UInt32,
        payload: Data,
        encryptionMethod: EncryptionMethod,
        encryptor: any EncryptionService
    ) throws -> Data {
        // Create packet header with DATAENCRYPT type
        let header = PacketHeader(
            type: .dataEncrypt,
            encrypt: encryptionMethod,
            sessionID: sessionID,
            token: token
        )

        // Encrypt the payload data
        let encryptedPayload = try encryptor.encrypt(payload)

        // Build packet: Header + Encrypted Payload
        var packetData = Data(capacity: PacketHeader.headerSize + encryptedPayload.count)
        packetData.append(header.toData())
        packetData.append(encryptedPayload)

        #if DEBUG
        print("DEBUG buildDataEncryptPacket:")
        print("  Header size: \(PacketHeader.headerSize) bytes")
        print("  Original payload size: \(payload.count) bytes")
        print("  Encrypted payload size: \(encryptedPayload.count) bytes")
        print("  Total packet size: \(packetData.count) bytes")
        print("  Encryption method: \(encryptionMethod)")
        #endif

        return packetData
    }
    
    /**
     * NAME: buildOpenPacket
     *
     * DESCRIPTION:
     *     Builds OPEN authentication packet with required TLV attributes.
     *     Follows exact ordering: MTU (first), Username (second), Password (third).
     *     Encrypts password internally using MD5("mw" + username) as key, consistent with Go backend.
     *
     * PARAMETERS:
     *     username - Username for authentication
     *     password - Raw password string (will be encrypted internally)
     *     mtu - Maximum transmission unit
     *     encryptionMethod - Encryption method to use
     *
     * RETURNS:
     *     Data - Complete OPEN packet
     *
     * THROWS:
     *     ProtocolError.invalidPacketFormat - If packet construction fails
     *     ProtocolError.encryptionFailed - If password encryption fails
     */
    public func buildOpenPacket(
        username: String,
        password: String,
        mtu: UInt16,
        encryptionMethod: EncryptionMethod
    ) throws -> Data {
        // Encrypt password using AES-ECB with MD5("mw" + username) as key
        // This matches exactly with Go backend encryptPasswordAES function
        let encryptedPassword = try PasswordEncryption.encryptPassword(password, username: username)

        // Create packet header (sessionID and token are 0 for OPEN packets)
        let header = PacketHeader(
            type: .open,
            encrypt: encryptionMethod,
            sessionID: 0,
            token: 0
        )

        // Create TLV attributes in exact order (consistent with Go backend CreateOpenPacket)
        // Go backend order: MTU (first), Username (second), Password (third), Encrypt (optional fourth)
        var attributes: [TLVAttribute] = [
            TLVAttribute(mtu: mtu),                                    // Must be first (AttributeMTU = 0x03)
            TLVAttribute(username: username),                          // Must be second (AttributeUsername = 0x01)
            TLVAttribute(encryptedPassword: encryptedPassword)         // Must be third (AttributePassword = 0x02)
        ]
        
        // Always add encryption attribute (including NONE)
        attributes.append(TLVAttribute(encryptionMethod: encryptionMethod))
        
        // Build packet with signature
        return try buildPacketWithSignature(header: header, attributes: attributes)
    }
    
    /**
     * NAME: buildEchoRequestPacket
     *
     * DESCRIPTION:
     *     Builds ECHO request packet for keepalive functionality.
     *     This is a simplified version that only includes timestamp.
     *     For full compatibility with Go backend, use buildEchoPacket instead.
     *
     * PARAMETERS:
     *     sessionID - Session ID from authentication
     *     token - Authentication token
     *     encryptionMethod - Encryption method to use
     *
     * RETURNS:
     *     Data - Complete ECHO request packet (32 bytes)
     *
     * THROWS:
     *     ProtocolError.invalidPacketFormat - If packet construction fails
     */
    public func buildEchoRequestPacket(
        sessionID: UInt16,
        token: UInt32,
        encryptionMethod: EncryptionMethod
    ) throws -> Data {
        // Create packet header
        let header = PacketHeader(
            type: .echoRequest,
            encrypt: encryptionMethod,
            sessionID: sessionID,
            token: token
        )

        // Calculate signature
        let signature = try calculateSignature(for: header)

        // Create timestamp (microseconds since Unix epoch)
        let timestamp = UInt64(Date().timeIntervalSince1970 * 1_000_000)
        let timestampData = withUnsafeBytes(of: timestamp.bigEndian) { Data($0) }

        // Build packet data
        var packetData = Data(capacity: PacketHeader.headerSize + signature.count + timestampData.count)
        packetData.append(header.toData())
        packetData.append(signature)
        packetData.append(timestampData)

        return packetData
    }

    /**
     * NAME: buildEchoPacket
     *
     * DESCRIPTION:
     *     Builds complete ECHO request packet with full Go backend compatibility.
     *     Includes timestamp, delay information, and SDRT tag.
     *     Total packet size: 48 bytes (Header 8 + Signature 16 + Timestamp 8 + Delays 12 + SDRT 4)
     *
     * PARAMETERS:
     *     header - Packet header with session info
     *     currentDelay - Current network delay in milliseconds
     *     minDelay - Minimum recorded delay in milliseconds
     *     maxDelay - Maximum recorded delay in milliseconds
     *
     * RETURNS:
     *     Data - Complete ECHO packet (48 bytes)
     *
     * THROWS:
     *     ProtocolError.invalidPacketFormat - If packet construction fails
     */
    public func buildEchoPacket(
        header: PacketHeader,
        currentDelay: UInt32,
        minDelay: UInt32,
        maxDelay: UInt32
    ) throws -> Data {
        // Calculate signature (header + "mw")
        let headerData = header.toData()
        let salt = Data([109, 119]) // ASCII values for 'm' and 'w'
        let signatureData = headerData + salt
        let signature = Insecure.MD5.hash(data: signatureData)

        // Build echo data payload
        var echoData = Data()

        // Add MD5 signature (16 bytes)
        echoData.append(Data(signature))

        // Add timestamp in microseconds (8 bytes) - matching Go backend format
        let timestamp = UInt64(Date().timeIntervalSince1970 * 1_000_000)
        echoData.append(contentsOf: withUnsafeBytes(of: timestamp.bigEndian) { Data($0) })

        // Add delay information (12 bytes total)
        echoData.append(contentsOf: withUnsafeBytes(of: currentDelay.bigEndian) { Data($0) })  // 4 bytes
        echoData.append(contentsOf: withUnsafeBytes(of: minDelay.bigEndian) { Data($0) })      // 4 bytes
        echoData.append(contentsOf: withUnsafeBytes(of: maxDelay.bigEndian) { Data($0) })      // 4 bytes

        // Add SDRT tag (4 bytes)
        echoData.append(contentsOf: [0x53, 0x44, 0x52, 0x54]) // "SDRT"

        // Combine header and echo data
        var completePacket = headerData  // 8 bytes
        completePacket.append(echoData)  // 40 bytes (16+8+12+4)

        // Debug logging to verify packet structure
        #if DEBUG
        print("DEBUG buildEchoPacket:")
        print("  Header size: \(headerData.count) bytes")
        print("  Signature size: 16 bytes")
        print("  Timestamp size: 8 bytes")
        print("  Delays size: 12 bytes")
        print("  SDRT size: 4 bytes")
        print("  Total size: \(completePacket.count) bytes (expected: 48)")
        print("  Packet bytes: \(completePacket.map { String(format: "%02x", $0) }.joined(separator: " "))")
        #endif

        return completePacket
    }

    /**
     * NAME: buildEchoResponsePacket
     *
     * DESCRIPTION:
     *     Builds ECHO response packet by combining header with echo data.
     *     Used to respond to ECHO requests from server.
     *
     * PARAMETERS:
     *     header - Packet header for the response
     *     echoData - Echo data from the original request
     *
     * RETURNS:
     *     Data - Complete ECHO response packet
     *
     * THROWS:
     *     ProtocolError.invalidPacketFormat - If packet construction fails
     */
    public func buildEchoResponsePacket(
        header: PacketHeader,
        echoData: Data
    ) throws -> Data {
        // Calculate signature (header + "mw")
        let headerData = header.toData()
        let salt = Data([109, 119]) // ASCII values for 'm' and 'w'
        let signatureData = headerData + salt
        let signature = Insecure.MD5.hash(data: signatureData)

        // Build response packet
        var completePacket = headerData  // 8 bytes
        completePacket.append(Data(signature))  // 16 bytes signature
        completePacket.append(echoData)  // Original echo data

        return completePacket
    }

    /**
     * NAME: buildClosePacket
     *
     * DESCRIPTION:
     *     Builds CLOSE packet to terminate connection gracefully.
     *     CLOSE packets only contain header with no additional data.
     *     Compatible with Go backend CreateClosePacket function.
     *
     * PARAMETERS:
     *     sessionID - Session ID for the connection
     *     token - Authentication token
     *     encryptionMethod - Encryption method to use
     *
     * RETURNS:
     *     Data - Complete CLOSE packet (8 bytes header only)
     *
     * THROWS:
     *     ProtocolError.invalidPacketFormat - If packet construction fails
     */
    public func buildClosePacket(
        sessionID: UInt16,
        token: UInt32,
        encryptionMethod: EncryptionMethod
    ) throws -> Data {
        // Create packet header
        let header = PacketHeader(
            type: .close,
            encrypt: encryptionMethod,
            sessionID: sessionID,
            token: token
        )

        // CLOSE packets only contain header, no signature or data
        // This matches Go backend CreateClosePacket implementation
        let headerData = header.toData()

        #if DEBUG
        print("DEBUG buildClosePacket:")
        print("  Header size: \(headerData.count) bytes")
        print("  Total size: \(headerData.count) bytes (expected: 8)")
        print("  Packet bytes: \(headerData.map { String(format: "%02x", $0) }.joined(separator: " "))")
        #endif

        return headerData
    }

    /**
     * NAME: calculateSignature
     *
     * DESCRIPTION:
     *     Calculates MD5 signature for packet header.
     *     Signature = MD5(header + "mw") consistent with Go backend.
     *
     * PARAMETERS:
     *     header - Packet header to sign
     *
     * RETURNS:
     *     Data - 16-byte MD5 signature
     *
     * THROWS:
     *     ProtocolError.invalidSignature - If signature calculation fails
     */
    public func calculateSignature(for header: PacketHeader) throws -> Data {
        // Get header data
        let headerData = header.toData()

        // Add salt "mw" (consistent with Go backend)
        let salt = Data([109, 119]) // ASCII values for 'm' and 'w'

        // Calculate MD5 hash
        let signatureData = headerData + salt
        let hash = Insecure.MD5.hash(data: signatureData)

        // Debug logging to verify signature calculation matches Go backend
        #if DEBUG
        print("DEBUG MD5 Signature Calculation:")
        print("  Header bytes: \(headerData.map { String(format: "%02x", $0) }.joined(separator: " "))")
        print("  Salt bytes: \(salt.map { String(format: "%02x", $0) }.joined(separator: " "))")
        print("  Combined: \(signatureData.map { String(format: "%02x", $0) }.joined(separator: " "))")
        print("  MD5 result: \(Data(hash).map { String(format: "%02x", $0) }.joined(separator: " "))")
        print("  Expected Windows: 36 01 68 0d e3 a6 b3 dd 33 6a c5 57 c8 7b 50 1b")
        #endif

        return Data(hash)
    }
    
    /**
     * NAME: verifySignature
     *
     * DESCRIPTION:
     *     Verifies packet signature against expected signature.
     *
     * PARAMETERS:
     *     header - Packet header
     *     signature - Provided signature to verify
     *
     * RETURNS:
     *     Bool - True if signature is valid
     */
    public func verifySignature(header: PacketHeader, signature: Data) -> Bool {
        do {
            let calculatedSignature = try calculateSignature(for: header)
            return calculatedSignature == signature
        } catch {
            return false
        }
    }
}

// MARK: - Packet Assembly Utilities
extension PacketBuilder {
    /**
     * NAME: assembleCompletePacket
     *
     * DESCRIPTION:
     *     Assembles complete packet from header and payload data.
     *     Determines whether signature is needed based on packet type.
     *     Data packets: Header + Payload (no signature)
     *     Control packets: Header + Signature + TLV attributes
     *
     * PARAMETERS:
     *     header - Packet header
     *     payload - Packet payload data
     *
     * RETURNS:
     *     Data - Complete assembled packet
     *
     * THROWS:
     *     ProtocolError.invalidPacketFormat - If assembly fails
     */
    public func assembleCompletePacket(header: PacketHeader, payload: Data) throws -> Data {
        // Data packets don't need signatures - just header + payload
        if header.type.isDataPacket {
            return buildDataPacket(header: header, payload: payload)
        }

        // Control packets need signatures - header + signature + TLV attributes
        // Parse payload as TLV attributes if present
        let attributes: [TLVAttribute]
        if !payload.isEmpty {
            // For control packets, payload should be TLV attributes (no signature in payload)
            attributes = try TLVAttribute.parseAll(from: payload)
        } else {
            attributes = []
        }

        return try buildPacketWithSignature(header: header, attributes: attributes)
    }

    /**
     * NAME: needsSignature
     *
     * DESCRIPTION:
     *     Determines if a packet type requires MD5 signature.
     *     Based on Go backend NeedsSignatureVerification() logic.
     *
     * PARAMETERS:
     *     packetType - Packet type to check
     *
     * RETURNS:
     *     Bool - True if packet needs signature
     */
    public func needsSignature(for packetType: PacketType) -> Bool {
        // Data packets and fragment packets do not need signature verification
        return !packetType.isDataPacket
    }
    
    /**
     * NAME: extractSignatureFromPacket
     *
     * DESCRIPTION:
     *     Extracts MD5 signature from control packet data.
     *     Only control packets have signatures, data packets don't.
     *
     * PARAMETERS:
     *     packetData - Complete packet data
     *     packetType - Packet type to determine if signature exists
     *
     * RETURNS:
     *     Data? - Extracted signature if present, nil otherwise
     */
    public func extractSignatureFromPacket(_ packetData: Data, packetType: PacketType) -> Data? {
        // Data packets don't have signatures
        guard needsSignature(for: packetType) else {
            return nil
        }

        // Signature is 16 bytes after header (8 bytes) for control packets
        let signatureStart = PacketHeader.headerSize
        let signatureEnd = signatureStart + 16

        guard packetData.count >= signatureEnd else {
            return nil
        }

        return packetData.subdata(in: signatureStart..<signatureEnd)
    }

    /**
     * NAME: extractAttributesFromPacket
     *
     * DESCRIPTION:
     *     Extracts TLV attributes from control packet data.
     *     Data packets don't have TLV attributes.
     *
     * PARAMETERS:
     *     packetData - Complete packet data
     *     packetType - Packet type to determine packet format
     *
     * RETURNS:
     *     [TLVAttribute] - Array of extracted attributes
     *
     * THROWS:
     *     ProtocolError - If attribute parsing fails
     */
    public func extractAttributesFromPacket(_ packetData: Data, packetType: PacketType) throws -> [TLVAttribute] {
        // Data packets don't have TLV attributes
        guard needsSignature(for: packetType) else {
            return []
        }

        // Attributes start after signature (16 bytes) for control packets
        // Go backend skips only MD5 signature, not header + signature
        let attributesStart = 16

        guard packetData.count > attributesStart else {
            return []
        }

        let attributesData = packetData.subdata(in: attributesStart..<packetData.count)
        return try TLVAttribute.parseAll(from: attributesData)
    }

    /**
     * NAME: extractPayloadFromPacket
     *
     * DESCRIPTION:
     *     Extracts payload from data packet.
     *     For data packets: returns IP packet data after header
     *     For control packets: returns empty data (payload is in TLV attributes)
     *
     * PARAMETERS:
     *     packetData - Complete packet data
     *     packetType - Packet type to determine packet format
     *
     * RETURNS:
     *     Data - Extracted payload data
     */
    public func extractPayloadFromPacket(_ packetData: Data, packetType: PacketType) -> Data {
        // For data packets, payload starts after header (8 bytes)
        if packetType.isDataPacket {
            let payloadStart = PacketHeader.headerSize
            guard packetData.count > payloadStart else {
                return Data()
            }
            return packetData.subdata(in: payloadStart..<packetData.count)
        }

        // Control packets don't have direct payload (data is in TLV attributes)
        return Data()
    }
}
