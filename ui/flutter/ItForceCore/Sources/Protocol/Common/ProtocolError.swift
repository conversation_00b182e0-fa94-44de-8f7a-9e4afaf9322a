/**
 * FILE: ProtocolError.swift
 *
 * DESCRIPTION:
 *     Protocol-specific error definitions for SDWAN ZZVPN protocol.
 *     Provides detailed error types for packet processing, encryption, and authentication.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation

/**
 * NAME: ProtocolError
 *
 * DESCRIPTION:
 *     Enumeration of all protocol-related errors.
 *     Provides localized error descriptions for debugging and user feedback.
 */
public enum ProtocolError: Error, LocalizedError, Equatable {
    // Packet format errors
    case invalidHeaderSize
    case invalidPacketType
    case invalidEncryptionMethod
    case invalidSignature
    
    // TLV attribute errors
    case invalidTLVFormat
    case invalidTLVType
    case invalidTLVLength
    
    // Authentication errors
    case authenticationFailed
    case sessionExpired
    case invalidCredentials
    
    // Encryption errors
    case encryptionFailed
    case decryptionFailed
    case keyGenerationFailed
    case invalidKey
    
    // Data processing errors
    case packetTooLarge
    case packetTooSmall
    case packetTooShort
    case checksumMismatch
    case invalidIPAddress
    case invalidPacketFormat

    // Network errors
    case networkError(String)
    case timeout
    case invalidPacket(String)

    // Data transfer errors
    case dataTransferFailed
    case heartbeatStartFailed
    
    public var errorDescription: String? {
        switch self {
        case .invalidHeaderSize:
            return "Invalid packet header size"
        case .invalidPacketType:
            return "Unknown packet type"
        case .invalidEncryptionMethod:
            return "Unsupported encryption method"
        case .invalidSignature:
            return "Packet signature verification failed"
        case .invalidTLVFormat:
            return "Invalid TLV attribute format"
        case .invalidTLVType:
            return "Unknown TLV attribute type"
        case .invalidTLVLength:
            return "Invalid TLV attribute length"
        case .authenticationFailed:
            return "Authentication failed"
        case .sessionExpired:
            return "Session has expired"
        case .invalidCredentials:
            return "Invalid username or password"
        case .encryptionFailed:
            return "Data encryption failed"
        case .decryptionFailed:
            return "Data decryption failed"
        case .keyGenerationFailed:
            return "Encryption key generation failed"
        case .invalidKey:
            return "Invalid encryption key"
        case .packetTooLarge:
            return "Packet size exceeds maximum allowed"
        case .packetTooSmall:
            return "Packet size below minimum required"
        case .packetTooShort:
            return "Packet data is too short"
        case .checksumMismatch:
            return "Packet checksum verification failed"
        case .invalidIPAddress:
            return "Invalid IP address format"
        case .invalidPacketFormat:
            return "Invalid packet format"
        case .networkError(let message):
            return "Network error: \(message)"
        case .timeout:
            return "Operation timed out"
        case .invalidPacket(let message):
            return "Invalid packet: \(message)"
        case .dataTransferFailed:
            return "Data transfer operation failed"
        case .heartbeatStartFailed:
            return "Failed to start heartbeat mechanism"
        }
    }
}
