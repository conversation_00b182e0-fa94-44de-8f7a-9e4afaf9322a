/**
 * FILE: EncryptionProtocol.swift
 *
 * DESCRIPTION:
 *     Defines encryption service protocol and key management for SDWAN protocol.
 *     Provides unified interface for XOR and AES encryption methods.
 *     Ensures 100% compatibility with Go backend encryption implementation.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import CryptoKit
import CommonCrypto

/**
 * NAME: EncryptionService
 *
 * DESCRIPTION:
 *     Protocol defining encryption and decryption operations for SDWAN protocol.
 *     Supports session key management, password encryption, and packet encryption.
 *
 * METHODS:
 *     generateSessionKey - Creates session key from username and password
 *     generatePasswordKey - Creates password encryption key from username
 *     encrypt - Encrypts raw data
 *     decrypt - Decrypts raw data
 *     encryptPacket - Encrypts packet data
 *     decryptPacket - Decrypts packet data
 */
public protocol EncryptionService {
    /**
     * NAME: generateSessionKey
     *
     * DESCRIPTION:
     *     Generates session key using MD5(username + password).
     *     Used for data packet encryption/decryption.
     *
     * PARAMETERS:
     *     username - Username string
     *     password - Password string
     *
     * RETURNS:
     *     Data - 16-byte session key
     *
     * THROWS:
     *     ProtocolError.encryptionFailed - If key generation fails
     */
    func generateSessionKey(username: String, password: String) throws -> Data
    
    /**
     * NAME: generatePasswordKey
     *
     * DESCRIPTION:
     *     Generates password encryption key using MD5("mw" + username).
     *     Used for password encryption in OPEN packets.
     *
     * PARAMETERS:
     *     username - Username string
     *
     * RETURNS:
     *     Data - 16-byte password encryption key
     *
     * THROWS:
     *     ProtocolError.encryptionFailed - If key generation fails
     */
    func generatePasswordKey(username: String) throws -> Data
    
    /**
     * NAME: encrypt
     *
     * DESCRIPTION:
     *     Encrypts raw data using the configured encryption method.
     *
     * PARAMETERS:
     *     data - Raw data to encrypt
     *
     * RETURNS:
     *     Data - Encrypted data
     *
     * THROWS:
     *     ProtocolError.encryptionFailed - If encryption fails
     */
    func encrypt(_ data: Data) throws -> Data
    
    /**
     * NAME: decrypt
     *
     * DESCRIPTION:
     *     Decrypts raw data using the configured encryption method.
     *
     * PARAMETERS:
     *     data - Encrypted data to decrypt
     *
     * RETURNS:
     *     Data - Decrypted data
     *
     * THROWS:
     *     ProtocolError.decryptionFailed - If decryption fails
     */
    func decrypt(_ data: Data) throws -> Data
    
    /**
     * NAME: encryptPacket
     *
     * DESCRIPTION:
     *     Encrypts packet data (only the data portion, not the header).
     *
     * PARAMETERS:
     *     packet - Original packet to encrypt
     *
     * RETURNS:
     *     ParsedPacket - Packet with encrypted data
     *
     * THROWS:
     *     ProtocolError.encryptionFailed - If packet encryption fails
     */
    func encryptPacket(_ packet: ParsedPacket) throws -> ParsedPacket
    
    /**
     * NAME: decryptPacket
     *
     * DESCRIPTION:
     *     Decrypts packet data and validates encryption method.
     *
     * PARAMETERS:
     *     packet - Encrypted packet to decrypt
     *
     * RETURNS:
     *     ParsedPacket - Packet with decrypted data
     *
     * THROWS:
     *     ProtocolError.decryptionFailed - If packet decryption fails
     */
    func decryptPacket(_ packet: ParsedPacket) throws -> ParsedPacket
    
    /**
     * NAME: getEncryptionMethod
     *
     * DESCRIPTION:
     *     Returns the encryption method identifier.
     *
     * RETURNS:
     *     EncryptionMethod - Encryption method used by this service
     */
    func getEncryptionMethod() -> EncryptionMethod
}

/**
 * NAME: BaseEncryptionService
 *
 * DESCRIPTION:
 *     Base implementation of EncryptionService providing common packet encryption logic.
 *     Eliminates duplicate encryptPacket/decryptPacket implementations across XOR and AES.
 *     Subclasses only need to implement raw data encrypt/decrypt methods.
 *
 * PROPERTIES:
 *     keyManager - Key management service for session and password keys
 */
public class BaseEncryptionService: EncryptionService {
    internal let keyManager: KeyManager

    public init(keyManager: KeyManager) {
        self.keyManager = keyManager
    }

    // MARK: - Key Management (Common Implementation)

    public func generateSessionKey(username: String, password: String) throws -> Data {
        let result = try keyManager.generateSessionKey(username: username, password: password)
        // Notify subclasses to invalidate their caches
        onSessionKeyGenerated()
        return result
    }

    /**
     * NAME: onSessionKeyGenerated
     *
     * DESCRIPTION:
     *     Called when session key is generated/regenerated.
     *     Subclasses should override to invalidate their caches.
     */
    internal func onSessionKeyGenerated() {
        // Default implementation does nothing
        // Subclasses override to invalidate caches
    }

    public func generatePasswordKey(username: String) throws -> Data {
        return try keyManager.generatePasswordKey(username: username)
    }

    // MARK: - Raw Data Encryption (Subclasses Override)

    /**
     * NAME: encrypt
     *
     * DESCRIPTION:
     *     Encrypts raw data. Subclasses must override this method.
     *
     * PARAMETERS:
     *     data - Data to encrypt
     *
     * RETURNS:
     *     Data - Encrypted data
     *
     * THROWS:
     *     ProtocolError.encryptionFailed - If encryption fails
     */
    public func encrypt(_ data: Data) throws -> Data {
        fatalError("Subclasses must implement encrypt(_:)")
    }

    /**
     * NAME: decrypt
     *
     * DESCRIPTION:
     *     Decrypts raw data. Subclasses must override this method.
     *
     * PARAMETERS:
     *     data - Data to decrypt
     *
     * RETURNS:
     *     Data - Decrypted data
     *
     * THROWS:
     *     ProtocolError.decryptionFailed - If decryption fails
     */
    public func decrypt(_ data: Data) throws -> Data {
        fatalError("Subclasses must implement decrypt(_:)")
    }

    /**
     * NAME: getEncryptionMethod
     *
     * DESCRIPTION:
     *     Returns encryption method. Subclasses must override this method.
     *
     * RETURNS:
     *     EncryptionMethod - Encryption method identifier
     */
    public func getEncryptionMethod() -> EncryptionMethod {
        fatalError("Subclasses must implement getEncryptionMethod()")
    }

    // MARK: - Packet Encryption (Common Implementation)

    /**
     * NAME: encryptPacket
     *
     * DESCRIPTION:
     *     Universal packet encryption implementation.
     *     Only encrypts data packets, control packets remain unchanged.
     *     Eliminates duplicate logic across XOR and AES implementations.
     *
     * PARAMETERS:
     *     packet - Original packet to encrypt
     *
     * RETURNS:
     *     ParsedPacket - Packet with encrypted payload
     *
     * THROWS:
     *     ProtocolError.encryptionFailed - If packet encryption fails
     */
    public func encryptPacket(_ packet: ParsedPacket) throws -> ParsedPacket {
        // Only encrypt data packets, control packets are not encrypted
        guard packet.header.type.isDataPacket else {
            return packet
        }

        // Encrypt payload using subclass implementation
        let encryptedPayload = try encrypt(packet.payload)

        // Create new packet with encrypted payload
        return ParsedPacket(
            header: packet.header,
            signature: packet.signature,
            attributes: packet.attributes,
            payload: encryptedPayload
        )
    }

    /**
     * NAME: decryptPacket
     *
     * DESCRIPTION:
     *     Universal packet decryption implementation.
     *     Validates encryption method and decrypts data packets only.
     *     Eliminates duplicate logic across XOR and AES implementations.
     *
     * PARAMETERS:
     *     packet - Encrypted packet to decrypt
     *
     * RETURNS:
     *     ParsedPacket - Packet with decrypted payload
     *
     * THROWS:
     *     ProtocolError.encryptionMethodMismatch - If encryption methods don't match
     *     ProtocolError.decryptionFailed - If packet decryption fails
     */
    public func decryptPacket(_ packet: ParsedPacket) throws -> ParsedPacket {
        // Only decrypt data packets
        guard packet.header.type.isDataPacket else {
            return packet
        }

        // Validate encryption method
        let expectedMethod = getEncryptionMethod()
        guard packet.header.encrypt == expectedMethod else {
            throw ProtocolError.encryptionMethodMismatch(expected: expectedMethod, actual: packet.header.encrypt)
        }

        // Decrypt payload using subclass implementation
        let decryptedPayload = try decrypt(packet.payload)

        // Create new packet with decrypted payload
        return ParsedPacket(
            header: packet.header,
            signature: packet.signature,
            attributes: packet.attributes,
            payload: decryptedPayload
        )
    }
}

/**
 * NAME: KeyManager
 *
 * DESCRIPTION:
 *     Manages encryption keys for SDWAN protocol.
 *     Handles session key and password key generation using MD5 hashing.
 *
 * PROPERTIES:
 *     sessionKey - Current session encryption key
 *     passwordKey - Current password encryption key
 */
public class KeyManager {
    private var sessionKey: Data?
    private var passwordKey: Data?
    
    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Creates a new key manager instance.
     */
    public init() {}
    
    /**
     * NAME: generateSessionKey
     *
     * DESCRIPTION:
     *     Generates session key using MD5(username + password).
     *     Consistent with Go backend key generation algorithm.
     *
     * PARAMETERS:
     *     username - Username string
     *     password - Password string
     *
     * RETURNS:
     *     Data - 16-byte session key
     *
     * THROWS:
     *     ProtocolError.encryptionFailed - If key generation fails
     */
    public func generateSessionKey(username: String, password: String) throws -> Data {
        let combined = username + password
        guard let data = combined.data(using: .utf8) else {
            throw ProtocolError.encryptionFailed
        }

        let key = Insecure.MD5.hash(data: data)
        self.sessionKey = Data(key)

        return Data(key)
    }
    
    /**
     * NAME: generatePasswordKey
     *
     * DESCRIPTION:
     *     Generates password encryption key using MD5("mw" + username).
     *     Used for password encryption in OPEN packets, consistent with Go backend.
     *
     * PARAMETERS:
     *     username - Username string
     *
     * RETURNS:
     *     Data - 16-byte password encryption key
     *
     * THROWS:
     *     ProtocolError.encryptionFailed - If key generation fails
     */
    public func generatePasswordKey(username: String) throws -> Data {
        let combined = "mw" + username
        guard let data = combined.data(using: .utf8) else {
            throw ProtocolError.encryptionFailed
        }
        
        let key = Insecure.MD5.hash(data: data)
        self.passwordKey = Data(key)
        return Data(key)
    }
    
    /**
     * NAME: getSessionKey
     *
     * DESCRIPTION:
     *     Returns the current session key.
     *
     * RETURNS:
     *     Data? - Current session key, nil if not generated
     */
    public func getSessionKey() -> Data? {
        return sessionKey
    }
    
    /**
     * NAME: getPasswordKey
     *
     * DESCRIPTION:
     *     Returns the current password key.
     *
     * RETURNS:
     *     Data? - Current password key, nil if not generated
     */
    public func getPasswordKey() -> Data? {
        return passwordKey
    }
    
    /**
     * NAME: clearKeys
     *
     * DESCRIPTION:
     *     Clears all stored keys for security.
     */
    public func clearKeys() {
        sessionKey = nil
        passwordKey = nil
    }
}

/**
 * NAME: PasswordEncryption
 *
 * DESCRIPTION:
 *     Utility class for password encryption using AES-ECB mode.
 *     Provides password encryption for OPEN packets, consistent with Go backend.
 */
public class PasswordEncryption {
    
    /**
     * NAME: generatePasswordKey
     *
     * DESCRIPTION:
     *     Generates password encryption key using MD5("mw" + username).
     *     Implementation consistent with Go backend key generation.
     *
     * PARAMETERS:
     *     username - Username for key generation
     *
     * RETURNS:
     *     Data - 16-byte MD5 hash key
     *
     * THROWS:
     *     ProtocolError.keyGenerationFailed - If key generation fails
     */
    public static func generatePasswordKey(username: String) throws -> Data {
        let combined = "mw" + username
        guard let keyData = combined.data(using: .utf8) else {
            throw ProtocolError.keyGenerationFailed
        }

        let key = Insecure.MD5.hash(data: keyData)
        return Data(key)
    }

    /**
     * NAME: encryptPassword
     *
     * DESCRIPTION:
     *     Encrypts password using AES-ECB mode with MD5("mw" + username) as key.
     *     Implementation consistent with Go backend encryptPasswordAES function.
     *
     * PARAMETERS:
     *     password - Password to encrypt
     *     username - Username for key generation
     *
     * RETURNS:
     *     Data - 16-byte encrypted password (first block only)
     *
     * THROWS:
     *     ProtocolError.encryptionFailed - If encryption fails
     */
    public static func encryptPassword(_ password: String, username: String) throws -> Data {
        // Generate password encryption key using MD5("mw" + username)
        let combined = "mw" + username
        guard let keyData = combined.data(using: .utf8) else {
            throw ProtocolError.encryptionFailed
        }

        let key = Insecure.MD5.hash(data: keyData)

        // Pad password to 32 bytes (consistent with Go backend)
        guard let passwordData = password.data(using: .utf8) else {
            throw ProtocolError.encryptionFailed
        }

        var paddedPassword = Data(count: 32)
        let copyLength = min(passwordData.count, 32)
        paddedPassword.replaceSubrange(0..<copyLength, with: passwordData.prefix(copyLength))

        // Debug logging to verify password encryption matches Go backend
        #if DEBUG
        print("DEBUG Password Encryption:")
        print("  Username: '\(username)'")
        print("  Password: '\(password)'")
        print("  Key input: '\(combined)'")
        print("  MD5 key: \(Data(key).map { String(format: "%02x", $0) }.joined(separator: " "))")
        print("  Password bytes: \(passwordData.map { String(format: "%02x", $0) }.joined(separator: " "))")
        print("  Padded password (32 bytes): \(paddedPassword.map { String(format: "%02x", $0) }.joined(separator: " "))")
        #endif

        // Encrypt using AES-ECB mode
        let encryptedPassword = try encryptAESECB(data: paddedPassword, key: Data(key))

        // Return only first 16 bytes (consistent with Go backend)
        let result = encryptedPassword.prefix(16)

        #if DEBUG
        print("  Encrypted result: \(result.map { String(format: "%02x", $0) }.joined(separator: " "))")
        if username == "tmptest" && password == "itforce" {
            print("  Expected for tmptest/itforce: cb 7d 34 35 27 31 73 aa 7a 12 06 28 c9 bc ca aa")
            let expected = "cb7d3435273173aa7a120628c9bccaaa"
            let actual = result.map { String(format: "%02x", $0) }.joined()
            if actual == expected {
                print("  ✅ PERFECT MATCH with Go backend!")
            } else {
                print("  ❌ Mismatch with Go backend")
            }
        }
        #endif

        return Data(result)
    }
    
    /**
     * NAME: encryptAESECB
     *
     * DESCRIPTION:
     *     Encrypts data using AES-ECB mode.
     *     ECB mode encrypts each block independently, consistent with Go backend.
     *
     * PARAMETERS:
     *     data - Data to encrypt (must be multiple of 16 bytes)
     *     key - AES encryption key (16 bytes)
     *
     * RETURNS:
     *     Data - Encrypted data
     *
     * THROWS:
     *     ProtocolError.encryptionFailed - If encryption fails
     */
    private static func encryptAESECB(data: Data, key: Data) throws -> Data {
        guard data.count % 16 == 0 else {
            throw ProtocolError.encryptionFailed
        }

        guard key.count == 16 else {
            throw ProtocolError.encryptionFailed
        }

        // Use CommonCrypto for AES-ECB encryption (consistent with Go backend)
        var result = Data(capacity: data.count)

        // Process each 16-byte block using raw AES encryption
        for i in stride(from: 0, to: data.count, by: 16) {
            let block = data.subdata(in: i..<i+16)
            let encryptedBlock = try encryptAESBlock(block: block, key: key)
            result.append(encryptedBlock)
        }

        return result
    }

    /**
     * NAME: encryptAESBlock
     *
     * DESCRIPTION:
     *     Encrypts a single 16-byte AES block using ECB mode.
     *     Note: This is a simplified implementation for password encryption only.
     *     For production use, consider using CommonCrypto or a proper AES-ECB library.
     *
     * PARAMETERS:
     *     block - 16-byte data block to encrypt
     *     key - AES encryption key (16 bytes)
     *
     * RETURNS:
     *     Data - Encrypted 16-byte block
     *
     * THROWS:
     *     ProtocolError.encryptionFailed - If encryption fails
     */
    private static func encryptAESBlock(block: Data, key: Data) throws -> Data {
        // Use CommonCrypto for proper AES-ECB implementation
        var encryptedBlock = Data(count: block.count)
        var numBytesEncrypted = 0

        let status = encryptedBlock.withUnsafeMutableBytes { encryptedBytes in
            block.withUnsafeBytes { blockBytes in
                key.withUnsafeBytes { keyBytes in
                    CCCrypt(
                        CCOperation(kCCEncrypt),
                        CCAlgorithm(kCCAlgorithmAES),
                        CCOptions(kCCOptionECBMode),
                        keyBytes.bindMemory(to: UInt8.self).baseAddress,
                        key.count,
                        nil, // No IV for ECB mode
                        blockBytes.bindMemory(to: UInt8.self).baseAddress,
                        block.count,
                        encryptedBytes.bindMemory(to: UInt8.self).baseAddress,
                        block.count,
                        &numBytesEncrypted
                    )
                }
            }
        }

        guard status == kCCSuccess else {
            throw ProtocolError.encryptionFailed
        }

        return encryptedBlock
    }
}

// MARK: - Error Extensions
extension ProtocolError {
    /**
     * NAME: encryptionMethodMismatch
     *
     * DESCRIPTION:
     *     Creates error for encryption method mismatch.
     *
     * PARAMETERS:
     *     expected - Expected encryption method
     *     actual - Actual encryption method
     *
     * RETURNS:
     *     ProtocolError - Encryption method mismatch error
     */
    public static func encryptionMethodMismatch(expected: EncryptionMethod, actual: EncryptionMethod) -> ProtocolError {
        return .encryptionFailed
    }
}
