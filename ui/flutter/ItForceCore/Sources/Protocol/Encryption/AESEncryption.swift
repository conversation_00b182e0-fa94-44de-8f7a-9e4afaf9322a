/**
 * FILE: AESEncryption.swift
 *
 * DESCRIPTION:
 *     Implements AES-ECB encryption service compatible with Go backend.
 *     Uses ECB mode without PKCS#7 padding for consistency with Android and Go implementations.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import CommonCrypto

/**
 * NAME: AESEncryptionService
 *
 * DESCRIPTION:
 *     AES encryption implementation using ECB mode.
 *     Compatible with Android and Go backend implementations.
 *     Uses zero padding instead of PKCS#7 for consistency.
 *
 * PROPERTIES:
 *     keyManager - Key management service for session and password keys
 */
public class AESEncryptionService: BaseEncryptionService {

    // MARK: - Performance Optimization
    private var cachedSessionKey: Data?
    private let cacheQueue = DispatchQueue(label: "aes.encryption.cache", attributes: .concurrent)

    public override init(keyManager: KeyManager) {
        super.init(keyManager: keyManager)
    }

    // MARK: - Key Management (Inherited from BaseEncryptionService)
    // generateSessionKey and generatePass<PERSON>Key are inherited from base class

    /**
     * NAME: getCachedSessionKey
     *
     * DESCRIPTION:
     *     Gets session key with performance optimization.
     *     Uses cached key to avoid repeated KeyManager calls during packet processing.
     *
     * RETURNS:
     *     Data? - Cached session key, nil if not available
     */
    private func getCachedSessionKey() -> Data? {
        return cacheQueue.sync {
            if let cached = cachedSessionKey {
                return cached
            }

            // Cache miss - get from KeyManager and cache it
            if let key = keyManager.getSessionKey() {
                cachedSessionKey = key
                return key
            }

            return nil
        }
    }

    /**
     * NAME: invalidateKeyCache
     *
     * DESCRIPTION:
     *     Invalidates cached session key when key is regenerated.
     *     Called automatically when session key is generated.
     */
    private func invalidateKeyCache() {
        cacheQueue.async(flags: .barrier) {
            self.cachedSessionKey = nil
        }
    }

    /**
     * NAME: onSessionKeyGenerated
     *
     * DESCRIPTION:
     *     Called when session key is generated/regenerated.
     *     Invalidates the cached session key.
     */
    internal override func onSessionKeyGenerated() {
        invalidateKeyCache()
    }
    
    /**
     * NAME: encrypt
     *
     * DESCRIPTION:
     *     Encrypts data using AES-ECB mode without PKCS#7 padding.
     *     Pads data to block size with zeros for consistency with Go backend.
     *     Uses ECB mode where each block is encrypted independently.
     *
     * PARAMETERS:
     *     data - Data to encrypt
     *
     * RETURNS:
     *     Data - Encrypted data
     *
     * THROWS:
     *     ProtocolError.encryptionFailed - If session key not available or encryption fails
     */
    public override func encrypt(_ data: Data) throws -> Data {
        guard let sessionKey = getCachedSessionKey() else {
            // NSLog("ERROR: Session key not available for encryption") // Debug NSLog commented for production
            throw ProtocolError.encryptionFailed
        }

        // NSLog("Encrypting \(data.count) bytes with \(sessionKey.count)-byte session key") // Debug NSLog commented for production
        // print("DEBUG: AESEncryption.encrypt() - Data size: \(data.count), Key size: \(sessionKey.count)") // Debug print commented for production
        // print("DEBUG: AESEncryption.encrypt() - Session key: \(sessionKey.map { String(format: "%02x", $0) }.joined(separator: " "))") // Debug print commented for production
        // print("DEBUG: AESEncryption.encrypt() - Input data: \(data.prefix(32).map { String(format: "%02x", $0) }.joined(separator: " "))\(data.count > 32 ? "..." : "")") // Debug print commented for production
        
        guard !data.isEmpty else {
            return data
        }
        
        // Ensure data length is multiple of block size (16 bytes for AES)
        let blockSize = kCCBlockSizeAES128
        let padding = blockSize - (data.count % blockSize)
        let paddingToAdd = (padding == blockSize) ? 0 : padding
        
        // Pad data with zeros (consistent with Go backend, not PKCS#7)
        var paddedData = Data(data)
        if paddingToAdd > 0 {
            paddedData.append(Data(count: paddingToAdd))
        }
        
        // Create result array
        var encryptedData = Data(count: paddedData.count)
        var numBytesEncrypted = 0
        
        // Perform AES-ECB encryption using CommonCrypto
        let status = encryptedData.withUnsafeMutableBytes { encryptedBytes in
            paddedData.withUnsafeBytes { dataBytes in
                sessionKey.withUnsafeBytes { keyBytes in
                    CCCrypt(
                        CCOperation(kCCEncrypt),
                        CCAlgorithm(kCCAlgorithmAES),
                        CCOptions(kCCOptionECBMode),
                        keyBytes.bindMemory(to: UInt8.self).baseAddress,
                        sessionKey.count,
                        nil, // No IV for ECB mode
                        dataBytes.bindMemory(to: UInt8.self).baseAddress,
                        paddedData.count,
                        encryptedBytes.bindMemory(to: UInt8.self).baseAddress,
                        paddedData.count,
                        &numBytesEncrypted
                    )
                }
            }
        }
        
        guard status == kCCSuccess else {
            // NSLog("ERROR: Encryption failed with status: \(status)") // Debug NSLog commented for production
            // print("DEBUG: AESEncryption.encrypt() - CCCrypt failed with status: \(status)") // Debug print commented for production
            // print("DEBUG: AESEncryption.encrypt() - Expected kCCSuccess (\(kCCSuccess))") // Debug print commented for production
            throw ProtocolError.encryptionFailed
        }

        // NSLog("Encryption successful: \(data.count) -> \(numBytesEncrypted) bytes") // Debug NSLog commented for production
        // print("DEBUG: AESEncryption.encrypt() - Encryption successful, output size: \(numBytesEncrypted)") // Debug print commented for production
        // print("DEBUG: AESEncryption.encrypt() - Output data: \(encryptedData.prefix(32).map { String(format: "%02x", $0) }.joined(separator: " "))\(encryptedData.count > 32 ? "..." : "")") // Debug print commented for production

        return encryptedData
    }
    
    /**
     * NAME: decrypt
     *
     * DESCRIPTION:
     *     Decrypts AES-ECB encrypted data.
     *     Does not handle padding removal for consistency with Go backend.
     *     Caller must handle padding if needed.
     *
     * PARAMETERS:
     *     data - Encrypted data
     *
     * RETURNS:
     *     Data - Decrypted data (with padding intact)
     *
     * THROWS:
     *     ProtocolError.decryptionFailed - If session key not available or decryption fails
     */
    public override func decrypt(_ data: Data) throws -> Data {
        guard let sessionKey = getCachedSessionKey() else {
            throw ProtocolError.decryptionFailed
        }
        
        guard !data.isEmpty else {
            return data
        }
        
        // Check if data length is multiple of block size
        guard data.count % kCCBlockSizeAES128 == 0 else {
            throw ProtocolError.decryptionFailed
        }
        
        // Create result array
        var decryptedData = Data(count: data.count)
        var numBytesDecrypted = 0
        
        // Perform AES-ECB decryption using CommonCrypto
        let status = decryptedData.withUnsafeMutableBytes { decryptedBytes in
            data.withUnsafeBytes { dataBytes in
                sessionKey.withUnsafeBytes { keyBytes in
                    CCCrypt(
                        CCOperation(kCCDecrypt),
                        CCAlgorithm(kCCAlgorithmAES),
                        CCOptions(kCCOptionECBMode),
                        keyBytes.bindMemory(to: UInt8.self).baseAddress,
                        sessionKey.count,
                        nil, // No IV for ECB mode
                        dataBytes.bindMemory(to: UInt8.self).baseAddress,
                        data.count,
                        decryptedBytes.bindMemory(to: UInt8.self).baseAddress,
                        data.count,
                        &numBytesDecrypted
                    )
                }
            }
        }
        
        guard status == kCCSuccess else {
            throw ProtocolError.decryptionFailed
        }
        
        // Do not handle padding removal, consistent with Go backend
        // Return complete decrypted data, caller handles padding
        return decryptedData
    }
    
    // MARK: - Packet Encryption (Inherited from BaseEncryptionService)
    // encryptPacket and decryptPacket are inherited from base class

    /**
     * NAME: getEncryptionMethod
     *
     * DESCRIPTION:
     *     Returns the encryption method identifier for AES encryption.
     *
     * RETURNS:
     *     EncryptionMethod - AES encryption method
     */
    public override func getEncryptionMethod() -> EncryptionMethod {
        return .aes
    }
}
