/**
 * FILE: XOREncryption.swift
 *
 * DESCRIPTION:
 *     XOR encryption implementation for SDWAN protocol.
 *     Provides XOR-based encryption and decryption operations.
 *     Ensures 100% compatibility with Go backend XOR encryption algorithm.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation

/**
 * NAME: XOREncryptionService
 *
 * DESCRIPTION:
 *     XOR encryption implementation using session key.
 *     Processes data in 8-byte blocks using uint32 operations for efficiency.
 *     Consistent with Go backend pa_mobile XOR encryption method.
 *
 * PROPERTIES:
 *     keyManager - Key manager for session key access
 *     sessionKey - Current session encryption key (16 bytes)
 */
public class XOREncryptionService: BaseEncryptionService {

    // MARK: - Performance Optimization
    private var cachedSessionKey: Data?
    private let cacheQueue = DispatchQueue(label: "xor.encryption.cache", attributes: .concurrent)

    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Creates XOR encryption service with key manager.
     *     Inherits common packet encryption logic from BaseEncryptionService.
     *
     * PARAMETERS:
     *     keyManager - Key manager instance for key access
     */
    public override init(keyManager: KeyManager) {
        super.init(keyManager: keyManager)
    }

    // MARK: - Key Management (Inherited from BaseEncryptionService)
    // generateSessionKey and generatePasswordKey are inherited from base class

    /**
     * NAME: getCachedSessionKey
     *
     * DESCRIPTION:
     *     Gets session key with performance optimization.
     *     Uses cached key to avoid repeated KeyManager calls during packet processing.
     *
     * RETURNS:
     *     Data? - Cached session key, nil if not available
     */
    private func getCachedSessionKey() -> Data? {
        return cacheQueue.sync {
            if let cached = cachedSessionKey {
                return cached
            }

            // Cache miss - get from KeyManager and cache it
            if let key = keyManager.getSessionKey() {
                cachedSessionKey = key
                return key
            }

            return nil
        }
    }

    /**
     * NAME: invalidateKeyCache
     *
     * DESCRIPTION:
     *     Invalidates cached session key when key is regenerated.
     *     Called automatically when session key is generated.
     */
    private func invalidateKeyCache() {
        cacheQueue.async(flags: .barrier) {
            self.cachedSessionKey = nil
        }
    }

    /**
     * NAME: onSessionKeyGenerated
     *
     * DESCRIPTION:
     *     Called when session key is generated/regenerated.
     *     Invalidates the cached session key.
     */
    internal override func onSessionKeyGenerated() {
        invalidateKeyCache()
    }
    
    /**
     * NAME: encrypt
     *
     * DESCRIPTION:
     *     Encrypts data using XOR algorithm with session key.
     *     Uses same XOR encryption method as Go backend pa_mobile implementation.
     *     Processes 8 bytes at a time using uint32 operations for efficiency.
     *
     * PARAMETERS:
     *     data - Raw data to encrypt
     *
     * RETURNS:
     *     Data - Encrypted data
     *
     * THROWS:
     *     ProtocolError.encryptionFailed - If session key not available
     */
    public override func encrypt(_ data: Data) throws -> Data {
        guard let sessionKey = getCachedSessionKey() else {
            // NSLog("ERROR: Session key not available for encryption") // Debug NSLog commented for production
            throw ProtocolError.encryptionFailed
        }
        
        guard !data.isEmpty else {
            return data
        }
        
        // Create result array (copy of original data)
        var result = Data(data)
        
        // Process 8 bytes at a time using uint32 operations (consistent with Go backend)
        let blockCount = result.count / 8
        
        result.withUnsafeMutableBytes { resultBytes in
            sessionKey.withUnsafeBytes { keyBytes in
                let resultPtr = resultBytes.bindMemory(to: UInt32.self)
                let keyPtr = keyBytes.bindMemory(to: UInt32.self)
                
                // Process each 8-byte block as two uint32 values
                for i in 0..<blockCount {
                    let blockOffset = i * 2 // Each block contains 2 uint32 values
                    
                    // XOR operation on uint32 pairs (consistent with Go backend unsafe.Pointer usage)
                    resultPtr[blockOffset] ^= keyPtr[0]
                    resultPtr[blockOffset + 1] ^= keyPtr[1]
                }
            }
        }
        
        // Process remaining bytes (less than 8 bytes)
        // Use same logic as Go backend: process from end of data
        let remainder = result.count % 8
        if remainder > 0 {
            for i in 0..<remainder {
                result[result.count - remainder + i] ^= sessionKey[i]
            }
        }
        
        return result
    }
    
    /**
     * NAME: decrypt
     *
     * DESCRIPTION:
     *     Decrypts XOR encrypted data.
     *     XOR encryption is symmetric, so uses same algorithm as encrypt.
     *
     * PARAMETERS:
     *     data - Encrypted data to decrypt
     *
     * RETURNS:
     *     Data - Decrypted data
     *
     * THROWS:
     *     ProtocolError.decryptionFailed - If decryption fails
     */
    public override func decrypt(_ data: Data) throws -> Data {
        // XOR encryption and decryption operations are identical
        do {
            return try encrypt(data)
        } catch {
            throw ProtocolError.decryptionFailed
        }
    }
    
    // MARK: - Packet Encryption (Inherited from BaseEncryptionService)
    // encryptPacket and decryptPacket are inherited from base class
    
    /**
     * NAME: getEncryptionMethod
     *
     * DESCRIPTION:
     *     Returns the XOR encryption method identifier.
     *
     * RETURNS:
     *     EncryptionMethod - XOR encryption method
     */
    public override func getEncryptionMethod() -> EncryptionMethod {
        return .xor
    }
}

// MARK: - XOR Utility Functions
extension XOREncryptionService {
    /**
     * NAME: encryptWithKey
     *
     * DESCRIPTION:
     *     Encrypts data using provided XOR key.
     *     Utility method for custom key encryption (e.g., password encryption).
     *
     * PARAMETERS:
     *     data - Data to encrypt
     *     key - XOR encryption key (16 bytes)
     *
     * RETURNS:
     *     Data - Encrypted data
     *
     * THROWS:
     *     ProtocolError.encryptionFailed - If encryption fails
     */
    public static func encryptWithKey(_ data: Data, key: Data) throws -> Data {
        guard key.count >= 16 else {
            throw ProtocolError.encryptionFailed
        }
        
        guard !data.isEmpty else {
            return data
        }
        
        // Create result array
        var result = Data(data)
        
        // Process 8 bytes at a time using uint32 operations
        let blockCount = result.count / 8
        
        result.withUnsafeMutableBytes { resultBytes in
            key.withUnsafeBytes { keyBytes in
                let resultPtr = resultBytes.bindMemory(to: UInt32.self)
                let keyPtr = keyBytes.bindMemory(to: UInt32.self)
                
                // Process each 8-byte block as two uint32 values
                for i in 0..<blockCount {
                    let blockOffset = i * 2
                    
                    // XOR operation on uint32 pairs
                    resultPtr[blockOffset] ^= keyPtr[0]
                    resultPtr[blockOffset + 1] ^= keyPtr[1]
                }
            }
        }
        
        // Process remaining bytes
        // Use same logic as Go backend: process from end of data
        let remainder = result.count % 8
        if remainder > 0 {
            for i in 0..<remainder {
                result[result.count - remainder + i] ^= key[i]
            }
        }
        
        return result
    }
    
    /**
     * NAME: validateKey
     *
     * DESCRIPTION:
     *     Validates XOR encryption key format and length.
     *
     * PARAMETERS:
     *     key - Key to validate
     *
     * RETURNS:
     *     Bool - True if key is valid
     */
    public static func validateKey(_ key: Data) -> Bool {
        return key.count >= 16
    }
}
