/**
 * FILE: NetworkInterfaceService.swift
 *
 * DESCRIPTION:
 *     iOS/macOS network interface information service for getting real physical
 *     interface names and IP addresses. Provides accurate network interface
 *     information for statistics display.
 *
 * AUTHOR: wei
 * HISTORY: 05/07/2025 create
 */

import Foundation
#if os(macOS)
import SystemConfiguration
#endif
import Network

/**
 * NAME: NetworkInterfaceService
 *
 * DESCRIPTION:
 *     Service for retrieving real network interface information on iOS/macOS.
 *     Gets physical interface names (WiFi: en0, Cellular: pdp_ip0) and their
 *     corresponding IP addresses using system APIs.
 *
 * FEATURES:
 *     - Real physical interface name detection
 *     - Local IP address retrieval for active interfaces
 *     - WiFi and cellular network interface identification
 *     - Default route interface detection
 *     - Thread-safe implementation
 */
public class NetworkInterfaceService {
    
    // MARK: - Private Properties
    
    private static let logger = OSLogLogger.createDevelopmentLogger(category: "NetworkInterface")
    
    // MARK: - Public Interface
    
    /**
     * NAME: getPhysicalInterfaceInfo
     *
     * DESCRIPTION:
     *     Gets the current active physical network interface information.
     *     Returns the interface name and local IP address of the default route interface.
     *
     * RETURNS:
     *     (interfaceName: String, localIP: String) - Interface name and IP address
     */
    public static func getPhysicalInterfaceInfo() -> (interfaceName: String, localIP: String) {
        // logger.debug("Getting physical interface information") // Debug log commented for production

        // Get all available interfaces
        let allInterfaces = getAllNetworkInterfaces()
        logger.info("Available network interfaces", metadata: [
            "total_count": "\(allInterfaces.count)"
        ])

        // Detailed interface enumeration commented for production
        // for (index, interface) in allInterfaces.enumerated() {
        //     logger.debug("Interface[\(index)]", metadata: [
        //         "name": interface.interfaceName,
        //         "ip": interface.ipAddress
        //     ]) // Debug log commented for production
        // }

        // Get default route interface name
        guard let interfaceName = getDefaultRouteInterface() else {
            logger.warning("Failed to get default route interface")
            logger.warning("Available interfaces: \(allInterfaces.map { "\($0.interfaceName):\($0.ipAddress)" }.joined(separator: ", "))")
            return ("", "")
        }

        logger.info("Found default route interface", metadata: [
            "interface_name": interfaceName,
            "interface_type": getInterfaceType(interfaceName)
        ])

        // Get IP address for the interface
        guard let localIP = getInterfaceIPAddress(interfaceName: interfaceName) else {
            logger.warning("Failed to get IP address for interface", metadata: [
                "interface_name": interfaceName
            ])
            return (interfaceName, "")
        }

        logger.info("Successfully retrieved interface information", metadata: [
            "interface_name": interfaceName,
            "interface_type": getInterfaceType(interfaceName),
            "local_ip": localIP,
            "ip_class": getIPAddressClass(localIP)
        ])

        return (interfaceName, localIP)
    }
    
    /**
     * NAME: getAllNetworkInterfaces
     *
     * DESCRIPTION:
     *     Gets information for all active network interfaces.
     *     Useful for comprehensive network status.
     *
     * RETURNS:
     *     [(interfaceName: String, ipAddress: String)] - Array of interface info
     */
    public static func getAllNetworkInterfaces() -> [(interfaceName: String, ipAddress: String)] {
        // logger.debug("Getting all network interfaces") // Debug log commented for production

        var interfaces: [(String, String)] = []
        var ifaddr: UnsafeMutablePointer<ifaddrs>?

        guard getifaddrs(&ifaddr) == 0 else {
            logger.error("Failed to get interface addresses")
            return interfaces
        }
        
        defer { freeifaddrs(ifaddr) }
        
        var ptr = ifaddr
        while ptr != nil {
            defer { ptr = ptr?.pointee.ifa_next }
            
            guard let interface = ptr?.pointee else { continue }
            
            // Get interface name
            let interfaceName = String(cString: interface.ifa_name)
            
            // Skip loopback and inactive interfaces
            let flags = interface.ifa_flags
            if (flags & UInt32(IFF_LOOPBACK)) != 0 || (flags & UInt32(IFF_UP)) == 0 {
                continue
            }
            
            // Check for IPv4 address
            guard let addr = interface.ifa_addr,
                  addr.pointee.sa_family == UInt8(AF_INET) else {
                continue
            }
            
            // Convert address to string
            var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
            if getnameinfo(addr, socklen_t(addr.pointee.sa_len),
                          &hostname, socklen_t(hostname.count),
                          nil, socklen_t(0), NI_NUMERICHOST) == 0 {
                let ipAddress = String(cString: hostname)
                interfaces.append((interfaceName, ipAddress))

                // logger.debug("Found interface", metadata: [
                //     "name": interfaceName,
                //     "ip": ipAddress
                // ]) // Debug log commented for production
            }
        }

        logger.info("Retrieved all interfaces", metadata: [
            "count": "\(interfaces.count)"
        ])
        
        return interfaces
    }

    /**
     * NAME: testNetworkInterfaceDetection
     *
     * DESCRIPTION:
     *     Test method to verify network interface detection functionality.
     *     Logs comprehensive information about network interfaces for testing.
     *
     * RETURNS:
     *     Bool - Whether the test passed successfully
     */
    public static func testNetworkInterfaceDetection() -> Bool {
        logger.info("Starting network interface detection test")

        var testPassed = true

        // Test 1: Get all interfaces
        let allInterfaces = getAllNetworkInterfaces()
        logger.info("Test 1 - All interfaces", metadata: [
            "count": "\(allInterfaces.count)"
        ])

        if allInterfaces.isEmpty {
            logger.error("Test 1 FAILED - No interfaces found")
            testPassed = false
        } else {
            logger.info("Test 1 PASSED - Found \(allInterfaces.count) interfaces")
        }

        // Test 2: Get physical interface info
        let physicalInterface = getPhysicalInterfaceInfo()
        logger.info("Test 2 - Physical interface", metadata: [
            "name": physicalInterface.interfaceName,
            "ip": physicalInterface.localIP
        ])

        if physicalInterface.interfaceName.isEmpty {
            logger.error("Test 2 FAILED - No physical interface name")
            testPassed = false
        } else if physicalInterface.localIP.isEmpty {
            logger.warning("Test 2 WARNING - No IP address for physical interface")
        } else {
            logger.info("Test 2 PASSED - Physical interface detected")
        }

        // Test 3: Validate interface types
        for interface in allInterfaces {
            let interfaceType = getInterfaceType(interface.interfaceName)
            let ipClass = getIPAddressClass(interface.ipAddress)

            // logger.debug("Interface analysis", metadata: [
            //     "name": interface.interfaceName,
            //     "type": interfaceType,
            //     "ip": interface.ipAddress,
            //     "ip_class": ipClass
            // ]) // Debug log commented for production
        }

        let testResult = testPassed ? "PASSED" : "FAILED"
        logger.info("Network interface detection test \(testResult)")

        return testPassed
    }

    // MARK: - Private Implementation
    
    /**
     * NAME: getDefaultRouteInterface
     *
     * DESCRIPTION:
     *     Gets the network interface name that handles the default route.
     *     SystemConfiguration APIs are not available in iOS.
     *
     * RETURNS:
     *     String? - Interface name or nil if not found
     */
    private static func getDefaultRouteInterface() -> String? {
        // logger.debug("Getting default route interface") // Debug log commented for production

        #if os(iOS)
        // On iOS, we can't use SystemConfiguration APIs, so we analyze available interfaces
        return getDefaultRouteInterfaceIOS()
        #else
        // On macOS, we can use SystemConfiguration
        // Create dynamic store
        guard let dynamicStore = SCDynamicStoreCreate(nil, "NetworkInterfaceService" as CFString, nil, nil) else {
            logger.error("Failed to create SCDynamicStore")
            return nil
        }

        // Get global IPv4 state
        guard let globalIPv4 = SCDynamicStoreCopyValue(dynamicStore, "State:/Network/Global/IPv4" as CFString) as? [String: Any] else {
            logger.warning("Failed to get global IPv4 state")
            return nil
        }

        // Extract primary interface
        guard let primaryInterface = globalIPv4["PrimaryInterface"] as? String else {
            logger.warning("No primary interface found in global IPv4 state")
            return nil
        }

        logger.info("Found primary interface via SystemConfiguration", metadata: [
            "interface": primaryInterface
        ])

        return primaryInterface
        #endif
    }
    
    /**
     * NAME: getInterfaceIPAddress
     *
     * DESCRIPTION:
     *     Gets the IPv4 address for a specific network interface.
     *     Uses getifaddrs to enumerate interface addresses.
     *
     * PARAMETERS:
     *     interfaceName - Name of the interface to query
     *
     * RETURNS:
     *     String? - IP address or nil if not found
     */
    private static func getInterfaceIPAddress(interfaceName: String) -> String? {
        // logger.debug("Getting IP address for interface", metadata: [
        //     "interface_name": interfaceName
        // ]) // Debug log commented for production

        var ifaddr: UnsafeMutablePointer<ifaddrs>?

        guard getifaddrs(&ifaddr) == 0 else {
            logger.error("Failed to get interface addresses")
            return nil
        }
        
        defer { freeifaddrs(ifaddr) }
        
        var ptr = ifaddr
        while ptr != nil {
            defer { ptr = ptr?.pointee.ifa_next }
            
            guard let interface = ptr?.pointee else { continue }
            
            // Check if this is the interface we're looking for
            let currentInterfaceName = String(cString: interface.ifa_name)
            guard currentInterfaceName == interfaceName else { continue }
            
            // Check for IPv4 address
            guard let addr = interface.ifa_addr,
                  addr.pointee.sa_family == UInt8(AF_INET) else {
                continue
            }
            
            // Convert address to string
            var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
            if getnameinfo(addr, socklen_t(addr.pointee.sa_len),
                          &hostname, socklen_t(hostname.count),
                          nil, socklen_t(0), NI_NUMERICHOST) == 0 {
                let ipAddress = String(cString: hostname)

                logger.info("Found IP address for interface", metadata: [
                    "interface_name": interfaceName,
                    "ip_address": ipAddress
                ])

                return ipAddress
            }
        }

        logger.warning("No IP address found for interface", metadata: [
            "interface_name": interfaceName
        ])
        
        return nil
    }

    // MARK: - Helper Methods

    /**
     * NAME: getInterfaceType
     *
     * DESCRIPTION:
     *     Determines the type of network interface based on its name.
     *     Helps with understanding interface types.
     *
     * PARAMETERS:
     *     interfaceName - Name of the interface
     *
     * RETURNS:
     *     String - Interface type description
     */
    public static func getInterfaceType(_ interfaceName: String) -> String {
        switch interfaceName {
        case let name where name.hasPrefix("en"):
            return "WiFi/Ethernet"
        case let name where name.hasPrefix("pdp_ip"):
            return "Cellular"
        case let name where name.hasPrefix("utun"):
            return "VPN/Tunnel"
        case let name where name.hasPrefix("lo"):
            return "Loopback"
        case let name where name.hasPrefix("awdl"):
            return "AirDrop"
        case let name where name.hasPrefix("bridge"):
            return "Bridge"
        default:
            return "Unknown"
        }
    }

    /**
     * NAME: getIPAddressClass
     *
     * DESCRIPTION:
     *     Determines the class/type of IP address for classification purposes.
     *
     * PARAMETERS:
     *     ipAddress - IP address string
     *
     * RETURNS:
     *     String - IP address class description
     */
    private static func getIPAddressClass(_ ipAddress: String) -> String {
        let components = ipAddress.split(separator: ".").compactMap { Int($0) }
        guard components.count == 4 else { return "Invalid" }

        let firstOctet = components[0]
        let secondOctet = components[1]

        switch firstOctet {
        case 10:
            return "Private (Class A)"
        case 172:
            if secondOctet >= 16 && secondOctet <= 31 {
                return "Private (Class B)"
            }
            return "Public (Class B)"
        case 192:
            if secondOctet == 168 {
                return "Private (Class C)"
            }
            return "Public (Class C)"
        case 169:
            if secondOctet == 254 {
                return "Link-Local"
            }
            return "Public"
        case 127:
            return "Loopback"
        case 224...239:
            return "Multicast"
        case 240...255:
            return "Reserved"
        default:
            return "Public"
        }
    }

    /**
     * NAME: getDefaultRouteInterfaceIOS
     *
     * DESCRIPTION:
     *     Gets the default route interface on iOS by analyzing available interfaces.
     *     Since SystemConfiguration APIs are limited on iOS, we use heuristics.
     *
     * RETURNS:
     *     String? - Interface name or nil if not found
     */
    private static func getDefaultRouteInterfaceIOS() -> String? {
        // logger.debug("Getting default route interface using interface analysis (iOS)") // Debug log commented for production

        let allInterfaces = getAllNetworkInterfaces()



        // Priority order for iOS interfaces:
        // 1. WiFi interfaces (en0, en1, etc.) with valid IP
        // 2. Cellular interfaces (pdp_ip0, etc.) with valid IP
        // 3. Other active interfaces

        // First, try to find WiFi interfaces
        for interface in allInterfaces {
            if interface.interfaceName.hasPrefix("en") &&
               !interface.ipAddress.isEmpty &&
               !interface.ipAddress.hasPrefix("169.254") { // Exclude link-local
                logger.info("Found WiFi interface as default route", metadata: [
                    "interface": interface.interfaceName,
                    "ip": interface.ipAddress
                ])
                return interface.interfaceName
            }
        }

        // Then, try cellular interfaces
        for interface in allInterfaces {
            if interface.interfaceName.hasPrefix("pdp_ip") &&
               !interface.ipAddress.isEmpty &&
               !interface.ipAddress.hasPrefix("169.254") { // Exclude link-local
                logger.info("Found cellular interface as default route", metadata: [
                    "interface": interface.interfaceName,
                    "ip": interface.ipAddress
                ])
                return interface.interfaceName
            }
        }

        // Finally, use any active interface (excluding loopback, VPN, and special interfaces)
        for interface in allInterfaces {
            if !interface.interfaceName.hasPrefix("lo") &&
               !interface.interfaceName.hasPrefix("utun") &&
               !interface.interfaceName.hasPrefix("awdl") &&
               !interface.interfaceName.hasPrefix("bridge") &&
               !interface.ipAddress.isEmpty &&
               !interface.ipAddress.hasPrefix("169.254") { // Exclude link-local
                logger.info("Found active interface as default route", metadata: [
                    "interface": interface.interfaceName,
                    "ip": interface.ipAddress
                ])
                return interface.interfaceName
            }
        }

        // If no suitable interface found, fall back to en0 (common WiFi interface)
        logger.warning("No suitable default route interface found, falling back to en0")
        return "en0"
    }
}
