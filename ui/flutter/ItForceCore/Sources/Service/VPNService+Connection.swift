/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      VPNService+Connection.swift
 *
 * DESCRIPTION :    VPN connection management extension for VPNService.
 *                  Contains connection, disconnection, and reconnection logic
 *                  using unified state management to eliminate state inconsistency.
 *
 * AUTHOR :         wei
 *
 * HISTORY :        03/07/2025 create - Refactored from VPNService.swift
 ******************************************************************************/

import Foundation
import NetworkExtension
import OSLog

// MARK: - VPNService Connection Management Extension

extension VPNService {
    
    // MARK: - Connection Operations
    
    /**
     * NAME: connect
     *
     * DESCRIPTION:
     *     Establishes VPN connection using unified state management.
     *     Replaces the original connect() method with simplified state transitions.
     *     Handles NetworkExtension authorization and tunnel establishment.
     *
     * THROWS:
     *     VPNServiceError - If connection fails
     */
    public func connect() async throws {
        logger.info("VPN connection requested")
        try await ensureServiceStarted()



        // Enhanced state checking with proactive cleanup mechanism
        let canConnect = await checkAndPrepareForConnection()

        guard canConnect else {
            let error = VPNServiceError.operationInProgress
            logger.error("Connection blocked - operation in progress", metadata: [
                "current_state": "\(currentState)",
                "is_operation_in_progress": "\(currentState.isOperationInProgress)",
                "can_connect": "\(canConnect)",
                "allowed_states": "disconnected, error",
                "error": error.localizedDescription
            ])
            delegate?.vpnService(self, didEncounterError: error)
            throw error
        }

        logger.info("Starting VPN connection with unified state management")

        do {
            // Get target server
            let targetServer = try await getTargetServer()
            
            // Update to connecting state
            updateVPNState(.connecting(server: targetServer, progress: .initializing))

            // Add connection timeout protection
            try await withTimeout(seconds: 25.0) {
                // Get current credentials
                // self.logger.debug("Getting current credentials for authentication") // Debug log commented for production
                let credentials = try await self.getCurrentCredentials()
                self.logger.info("Credentials retrieved", metadata: [
                    "username": credentials.username,
                    "has_password": "\(!credentials.password.isEmpty)"
                ])

                // Ensure server IPs are pre-resolved before creating VPN configuration
                self.logger.info("Ensuring server IPs are pre-resolved for route exclusion")
                do {
                    let allServers = try await self.serverService.getServerList()
                    var resolvedCount = 0

                    for server in allServers {
                        if let _ = await self.serverService.resolveServerIP(server.serverName) {
                            resolvedCount += 1
                        }
                    }

                    self.logger.info("Server IP pre-resolution completed", metadata: [
                        "total_servers": "\(allServers.count)",
                        "resolved_count": "\(resolvedCount)"
                    ])
                } catch {
                    self.logger.warning("Failed to pre-resolve server IPs", metadata: [
                        "error": error.localizedDescription
                    ])
                }

                // Create VPN configuration for authorization
                // CRITICAL: Add ALL server IP addresses to excludedIPs to prevent routing loops
                var excludedIPs: [String] = []

                // Get all cached server IPs from ServerManager (includes target server if resolved)
                let cachedServerIPs = await self.serverService.getAllCachedServerIPs()
                excludedIPs.append(contentsOf: cachedServerIPs)

                // Ensure target server is included (resolve if not already cached)
                if let targetServerIP = await self.serverService.resolveServerIP(targetServer.serverName) {
                    if !excludedIPs.contains(targetServerIP) {
                        excludedIPs.append(targetServerIP)
                    }
                    self.logger.info("Target server IP ensured in exclusions", metadata: [
                        "hostname": targetServer.serverName,
                        "resolved_ip": targetServerIP,
                        "was_already_cached": "\(cachedServerIPs.contains(targetServerIP))"
                    ])
                } else {
                    // Fallback to hostname if resolution fails
                    if !excludedIPs.contains(targetServer.serverName) {
                        excludedIPs.append(targetServer.serverName)
                    }
                    self.logger.warning("Failed to resolve target server IP, using hostname", metadata: [
                        "hostname": targetServer.serverName
                    ])
                }

                self.logger.info("Server IP exclusion list prepared", metadata: [
                    "cached_server_ips": "\(cachedServerIPs.count)",
                    "total_excluded_ips": "\(excludedIPs.count)",
                    "target_server": targetServer.serverName
                ])

                // Get stored routing configuration
                let routingConfig = await self.getStoredRoutingConfiguration()

                let vpnConfiguration = VPNConfiguration(
                    serverAddress: targetServer.serverName,
                    serverPort: targetServer.serverPort,
                    username: credentials.username,
                    password: credentials.password,
                    mtu: 1400,
                    encryptionMethod: 1,
                    dnsServers: ["*******", "*******"],
                    excludedIPs: excludedIPs,
                    tunnelIP: "********",
                    routingMode: routingConfig.mode,
                    customRoutes: routingConfig.customRoutes
                )

                self.logger.info("VPN configuration created with excluded IPs", metadata: [
                    "server": targetServer.serverName,
                    "excluded_ips": "\(excludedIPs)"
                ])

                // Update progress to authentication
                self.updateVPNState(.connecting(server: targetServer, progress: .authenticating))

                // Check VPN permission status first
                let permissionStatus = await self.checkNetworkExtensionPermissionStatus()
                self.logger.info("Current VPN permission status", metadata: [
                    "status": "\(permissionStatus)",
                    "is_configured": "\(self.isNetworkExtensionConfigured)"
                ])

                // Only request permission if not already authorized
                if permissionStatus != .authorized {
                    let permissionGranted = try await self.requestNetworkExtensionPermission(with: vpnConfiguration)

                    if !permissionGranted {
                        self.logger.error("VPN permission denied by user, performing immediate cleanup")

                        // Perform immediate cleanup after permission denial
                        await self.handlePermissionDenialCleanup()

                        // Set to error state after cleanup (user can retry from error state)
                        let serviceError = VPNServiceError.vpnPermissionDenied
                        self.updateVPNState(.error(serviceError))
                        self.delegate?.vpnService(self, didEncounterError: serviceError)
                        throw serviceError
                    }
                } else {
                    // Permission already granted, ensure configuration is loaded
                    if !self.isNetworkExtensionConfigured {
                        let configLoaded = await self.loadExistingVPNConfiguration()
                        if configLoaded {
                            self.logger.info("Existing VPN configuration loaded successfully")
                        } else {
                            self.logger.warning("Failed to load existing VPN configuration, will create new one")
                            let permissionGranted = try await self.requestNetworkExtensionPermission(with: vpnConfiguration)
                            if !permissionGranted {
                                self.logger.error("VPN permission denied during configuration reload, performing cleanup")

                                // Perform immediate cleanup after permission denial
                                await self.handlePermissionDenialCleanup()

                                let serviceError = VPNServiceError.vpnPermissionDenied
                                self.updateVPNState(.error(serviceError))
                                self.delegate?.vpnService(self, didEncounterError: serviceError)
                                throw serviceError
                            }
                        }
                    }

                    // Update current configuration for this connection
                    self.currentVPNConfiguration = vpnConfiguration
                }

                // Update progress to tunnel establishment
                self.updateVPNState(.connecting(server: targetServer, progress: .establishingTunnel))

                // Start VPN tunnel using integrated NetworkExtension management
                self.logger.info("Starting VPN tunnel", metadata: [
                    "server": targetServer.serverName,
                    "authorization": "granted"
                ])

                try await self.startNetworkExtensionTunnel()

                // Connection will be marked as connected via NetworkExtension status callback
                self.logger.info("VPN connection initiated successfully", metadata: [
                    "server_id": targetServer.id,
                    "server_name": targetServer.name,
                    "server_address": targetServer.serverName,
                    "platform_note": "VPN authorization completed"
                ])
            }

        } catch is TimeoutError {
            // Handle connection timeout - should go to error state like other failures
            logger.warning("VPN connection timed out after 25 seconds, performing cleanup and setting error state")

            // Perform immediate comprehensive cleanup
            await performUnifiedDisconnect(
                reason: .connectionLost,
                source: .errorHandler
            )

            let timeoutError = VPNServiceError.connectionTimeout
            // Set to error state - user needs to see timeout error and decide whether to retry
            await handleConnectionFailure(timeoutError, shouldAllowRetry: false)
            throw timeoutError

        } catch let error as VPNServiceError {
            // All connection failures should go to error state - user needs to see what happened
            await handleConnectionFailure(error, shouldAllowRetry: false)
            throw error
        } catch let error as ConnectionManagerError {
            // Map ConnectionManagerError to generic configuration error
            let serviceError = VPNServiceError.configurationInvalid("Connection error: \(error.localizedDescription)")
            await handleConnectionFailure(serviceError, shouldAllowRetry: false)
            throw serviceError
        } catch let error as ServerServiceError {
            // Use unified error handling for ServerServiceError
            let serviceError = VPNServiceError.serverServiceError(error)
            await handleConnectionFailure(serviceError, shouldAllowRetry: false)
            throw serviceError
        } catch {
            // Use unified error handling for unexpected errors
            logger.error("VPN connection failed with unexpected error", metadata: [
                "error": error.localizedDescription,
                "current_state": "\(currentState)",
                "error_type": "\(type(of: error))"
            ])
            let serviceError = VPNServiceError.configurationInvalid(error.localizedDescription)
            await handleConnectionFailure(serviceError, shouldAllowRetry: false)
            throw serviceError
        }
    }



    /**
     * NAME: disconnect
     *
     * DESCRIPTION:
     *     Disconnects VPN connection using unified state management.
     *     Handles graceful disconnection and state cleanup.
     *     Waits for actual disconnection to complete before returning.
     *
     * THROWS:
     *     VPNServiceError - If service not started
     */
    public func disconnect() async throws {
        try await ensureServiceStarted()

        // Check current state
        guard currentState.isConnected || currentState.isOperationInProgress else {
            logger.info("VPN already disconnected")
            return
        }

        logger.info("Disconnecting VPN connection")

        // Use unified disconnect coordination for user-initiated disconnect
        await performUnifiedDisconnect(
            reason: .userInitiated,
            source: .userAction
        )

        // Wait for actual disconnection to complete
        await waitForDisconnectionComplete()

        // Note: State is already updated by performUnifiedDisconnect, no need to update again

        logger.info("VPN connection disconnected successfully")
    }
    
    // Note: reconnect() method removed - reconnection is handled by VPN Extension
    


    /**
     * NAME: waitForDisconnectionComplete
     *
     * DESCRIPTION:
     *     Waits for VPN disconnection to complete by monitoring NetworkExtension status.
     *     Ensures that the disconnect operation is fully finished before returning.
     */
    private func waitForDisconnectionComplete() async {
        logger.info("Waiting for VPN disconnection to complete")

        let maxWaitTime: TimeInterval = 10.0 // Maximum wait time in seconds
        let checkInterval: TimeInterval = 0.5 // Check every 500ms
        let startTime = Date()

        while Date().timeIntervalSince(startTime) < maxWaitTime {
            let status = await getVPNConnectionStatus()

            // logger.debug("Checking VPN status during disconnection", metadata: [
            //     "status": "\(status.rawValue)",
            //     "elapsed_time": "\(Date().timeIntervalSince(startTime))"
            // ]) // Debug log commented for production

            // Check if disconnection is complete
            if status == .disconnected || status == .invalid {
                logger.info("VPN disconnection confirmed by NetworkExtension", metadata: [
                    "final_status": "\(status.rawValue)",
                    "elapsed_time": "\(Date().timeIntervalSince(startTime))"
                ])
                return
            }

            // Wait before next check
            try? await Task.sleep(nanoseconds: UInt64(checkInterval * 1_000_000_000))
        }

        // If we reach here, disconnection took longer than expected
        logger.warning("VPN disconnection wait timeout", metadata: [
            "max_wait_time": "\(maxWaitTime)",
            "final_status": "\(await getVPNConnectionStatus().rawValue)"
        ])
    }

    // MARK: - Helper Methods

    /**
     * NAME: getTargetServer
     *
     * DESCRIPTION:
     *     Gets target server for connection (current or best available).
     *     Migrated from VPNService.swift - belongs to connection logic.
     *
     *     This method implements the server selection strategy for connections:
     *     1. Use current server if available
     *     2. Select best server if none selected
     *
     * RETURNS:
     *     ServerInfo - Target server for connection
     *
     * THROWS:
     *     VPNServiceError - If no server available
     */
    internal func getTargetServer() async throws -> ServerInfo {
        logger.info("Getting target server for connection")

        // Try to get current server first
        if let currentServer = await serverService.getCurrentServer() {
            logger.info("Using current server", metadata: [
                "server_id": currentServer.id,
                "server_name": currentServer.name,
                "status": currentServer.status.rawValue
            ])
            return currentServer
        }

        logger.info("No current server, selecting best server")

        // Select best server if none selected
        do {
            guard let bestServer = try await serverService.selectBestServer() else {
                logger.error("Best server selection returned nil")
                throw VPNServiceError.serverNotSelected
            }

            logger.info("Best server selected", metadata: [
                "server_id": bestServer.id,
                "server_name": bestServer.name,
                "status": bestServer.status.rawValue
            ])
            return bestServer
        } catch let serverError as ServerServiceError {
            logger.error("Server selection failed", metadata: ["error": serverError.localizedDescription])
            throw VPNServiceError.serverServiceError(serverError)
        } catch {
            logger.error("Server selection failed with unknown error", metadata: ["error": error.localizedDescription])
            throw VPNServiceError.serverNotSelected
        }
    }

    /**
     * NAME: getCurrentConnectedServer
     *
     * DESCRIPTION:
     *     Gets currently connected server from unified state.
     *     Extracts server information from current VPN state.
     */
    private func getCurrentConnectedServer() -> ServerInfo? {
        return currentState.connectedServer
    }

    // MARK: - Proactive State Management

    /**
     * NAME: checkAndPrepareForConnection
     *
     * DESCRIPTION:
     *     Proactively checks VPN state and performs cleanup if needed before allowing connection.
     *     This method implements the "immediate cleanup on operation in progress" strategy
     *     instead of relying on timeout-based recovery mechanisms.
     *
     * RETURNS:
     *     Bool - Whether connection can proceed after cleanup attempts
     */
    private func checkAndPrepareForConnection() async -> Bool {
        logger.info("Proactive connection state check", metadata: [
            "current_state": "\(currentState)",
            "is_operation_in_progress": "\(currentState.isOperationInProgress)",
            "is_connected": "\(currentState.isConnected)"
        ])

        // Fast path: already in valid state for connection
        switch currentState {
        case .disconnected, .error:
            return true
        case .connected:
            logger.info("VPN already connected, cannot start new connection")
            return false
        case .connecting, .reconnecting, .disconnecting:
            // Operation in progress - perform immediate cleanup and recovery
            logger.warning("VPN operation in progress detected, performing immediate cleanup", metadata: [
                "current_state": "\(currentState)",
                "cleanup_strategy": "proactive_immediate"
            ])

            // Step 1: Check if this is a stale state (common after permission denial)
            let stateAge = Date().timeIntervalSince(stateChangedTime ?? Date())
            let isStaleState = stateAge > 5.0 // Consider state stale after 5 seconds

            logger.info("State age analysis", metadata: [
                "state_age_seconds": "\(stateAge)",
                "is_stale_state": "\(isStaleState)",
                "state_changed_time": "\(stateChangedTime?.description ?? "unknown")"
            ])

            // Step 2: Perform comprehensive emergency cleanup
            await performEmergencyCleanup()

            // Step 3: Force reset state to disconnected
            forceResetState()

            // Step 4: Additional cleanup for permission-related issues
            if isStaleState {
                logger.info("Performing additional cleanup for stale state")
                // Ensure NetworkExtension is completely stopped
                await forceStopNetworkExtension()
                // Clear any cached configurations that might be causing issues
                isNetworkExtensionConfigured = false
            }

            // Step 5: Validate state consistency across all components
            await ensureComponentStateConsistency(for: .disconnected)

            // Step 6: Final state verification
            let finalState = currentState
            let canProceed = finalState.isDisconnected  // After forceResetState(), should always be disconnected

            logger.info("Proactive cleanup completed", metadata: [
                "final_state": "\(finalState)",
                "can_proceed": "\(canProceed)",
                "cleanup_successful": "\(canProceed)",
                "was_stale_state": "\(isStaleState)"
            ])

            return canProceed
        }
    }

    /**
     * NAME: handlePermissionDenialCleanup
     *
     * DESCRIPTION:
     *     Specialized cleanup method for permission denial scenarios.
     *     This method is called immediately after permission is denied to ensure
     *     no resources are left in an inconsistent state.
     */
    private func handlePermissionDenialCleanup() async {
        logger.info("Performing specialized cleanup for permission denial")

        // 1. Note: Connection attempts are handled by VPN Extension

        // 2. Ensure NetworkExtension is completely stopped
        await forceStopNetworkExtension()

        // 3. Clear configuration flags
        isNetworkExtensionConfigured = false
        currentVPNConfiguration = nil

        // 4. Reset any pending operation flags
        isPerformingUnifiedDisconnect = false

        // 5. Clear connection timing
        connectionStartTime = nil

        logger.info("Permission denial cleanup completed")
    }

    // MARK: - Debug and Testing Support

    /**
     * NAME: debugCurrentState
     *
     * DESCRIPTION:
     *     Debug method to log comprehensive state information.
     *     Useful for troubleshooting "operation in progress" issues.
     */
    public func debugCurrentState() {
        logger.info("VPN State Debug Information", metadata: [
            "current_state": "\(currentState)",
            "is_operation_in_progress": "\(currentState.isOperationInProgress)",
            "is_connected": "\(currentState.isConnected)",
            "is_disconnected": "\(currentState.isDisconnected)",
            "is_error": "\(currentState.isError)",
            "state_changed_time": "\(stateChangedTime?.description ?? "unknown")",
            "connection_start_time": "\(connectionStartTime?.description ?? "unknown")",
            "is_network_extension_configured": "\(isNetworkExtensionConfigured)",
            "is_performing_unified_disconnect": "\(isPerformingUnifiedDisconnect)",
            "current_vpn_configuration": "\(currentVPNConfiguration != nil ? "configured" : "nil")"
        ])
    }
}
