/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      VPNService+Monitoring.swift
 *
 * DESCRIPTION :    Monitoring and statistics management extension for VPNService.
 *                  Contains traffic monitoring, performance data collection, and
 *                  heartbeat handling functionality. Migrated from VPNService.swift
 *                  to improve code organization and maintainability.
 *
 * ARCHITECTURE :   - Unified monitoring lifecycle management
 *                  - Optimized timer implementation using DispatchSourceTimer
 *                  - Integrated heartbeat and traffic statistics handling
 *                  - Consistent error handling and logging patterns
 *                  - Thread-safe delegate notifications
 *
 * AUTHOR :         wei
 *
 * HISTORY :        03/07/2025 create - Migrated and optimized from VPNService.swift
 ******************************************************************************/

import Foundation
import NetworkExtension
import OSLog

// MARK: - VPN Extension Event Types

/**
 * NAME: VPNExtensionEvent
 *
 * DESCRIPTION:
 *     VPN扩展事件枚举，表示从VPN扩展传递给主应用的各种事件
 */
enum VPNExtensionEvent {
    case reconnectionSuccess(tunnelIP: String, serverAddress: String, timestamp: TimeInterval)
    case connectionFailure(errorMessage: String, serverAddress: String, timestamp: TimeInterval)
    case reconnectionFailure(errorMessage: String, serverAddress: String, timestamp: TimeInterval)
}

// MARK: - VPNService Monitoring Extension

extension VPNService {
    
    // MARK: - Monitoring State Properties
    
    /// Connection start timestamp for duration calculations
    internal var connectionStartTime: Date? {
        get {
            return _connectionStartTime
        }
        set {
            _connectionStartTime = newValue
        }
    }
    
    /// Last traffic update timestamp
    internal var lastTrafficUpdate: Date {
        get {
            return _lastTrafficUpdate
        }
        set {
            _lastTrafficUpdate = newValue
        }
    }
    
    // MARK: - Private Storage Properties
    
    private var _connectionStartTime: Date? {
        get {
            return objc_getAssociatedObject(self, &AssociatedKeys.connectionStartTime) as? Date
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.connectionStartTime, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    private var _lastTrafficUpdate: Date {
        get {
            return (objc_getAssociatedObject(self, &AssociatedKeys.lastTrafficUpdate) as? Date) ?? Date()
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.lastTrafficUpdate, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    private var trafficUpdateTimer: DispatchSourceTimer? {
        get {
            return objc_getAssociatedObject(self, &AssociatedKeys.trafficUpdateTimer) as? DispatchSourceTimer
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.trafficUpdateTimer, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }

    private var stateValidationTimer: DispatchSourceTimer? {
        get {
            return objc_getAssociatedObject(self, &AssociatedKeys.stateValidationTimer) as? DispatchSourceTimer
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.stateValidationTimer, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    


    // MARK: - Associated Object Keys

    private struct AssociatedKeys {
        static var connectionStartTime: UInt8 = 0
        static var lastTrafficUpdate: UInt8 = 0
        static var trafficUpdateTimer: UInt8 = 0
        static var stateValidationTimer: UInt8 = 0
    }
    
    // MARK: - Monitoring Lifecycle Management
    
    /**
     * NAME: startMonitoring
     *
     * DESCRIPTION:
     *     Starts traffic monitoring for UI updates.
     *     Heartbeat monitoring is handled by VPN Extension.
     */
    internal func startMonitoring() async {
        guard configuration.monitoring.enabled else {
            // logger.debug("Monitoring disabled in configuration") // Debug log commented for production
            return
        }

        logger.info("Starting VPN traffic monitoring for UI updates", metadata: [
            "traffic_interval": "\(configuration.trafficUpdateInterval)"
        ])

        // Start traffic monitoring for periodic traffic events
        startTrafficMonitoring()

        // logger.debug("VPN traffic monitoring started successfully") // Debug log commented for production
    }
    
    /**
     * NAME: stopMonitoring
     *
     * DESCRIPTION:
     *     Stops traffic monitoring and cleans up resources.
     *     Heartbeat monitoring is handled by VPN Extension.
     */
    internal func stopMonitoring() {
        // logger.debug("Stopping VPN traffic monitoring") // Debug log commented for production

        // Reset monitoring state
        connectionStartTime = nil

        // logger.debug("VPN traffic monitoring stopped successfully") // Debug log commented for production
    }


    
    // MARK: - Traffic Statistics Management
    // MARK: - Private Traffic Monitoring Implementation
    
    /**
     * NAME: startTrafficMonitoring
     *
     * DESCRIPTION:
     *     Starts background traffic monitoring with optimized DispatchSourceTimer.
     *     Replaces Timer.scheduledTimer for better performance and control.
     */
    private func startTrafficMonitoring() {
        guard configuration.monitoring.enabled else {
            return
        }
        
        // logger.debug("Starting traffic monitoring", metadata: [
        //     "update_interval": "\(configuration.trafficUpdateInterval)"
        // ]) // Debug log commented for production
        
        // Create dispatch timer for better performance
        let timer = DispatchSource.makeTimerSource(queue: DispatchQueue.global(qos: .utility))
        timer.schedule(deadline: .now() + configuration.trafficUpdateInterval, 
                      repeating: configuration.trafficUpdateInterval)
        
        timer.setEventHandler { [weak self] in
            Task {
                await self?.updateTrafficStatistics()
            }
        }
        
        trafficUpdateTimer = timer
        timer.resume()
    }
    
    /**
     * NAME: stopTrafficMonitoring
     *
     * DESCRIPTION:
     *     Stops background traffic monitoring and cleans up timer resources.
     */
    private func stopTrafficMonitoring() {
        trafficUpdateTimer?.cancel()
        trafficUpdateTimer = nil
        // logger.debug("Traffic monitoring stopped") // Debug log commented for production
    }
    
    /**
     * NAME: updateTrafficStatistics
     *
     * DESCRIPTION:
     *     统一检查和更新所有状态信息，包括：
     *     - 流量统计
     *     - tunnel IP、本地IP、当前接口
     *     - 连接状态（成功、失败、重连失败等）
     *     - VPN扩展事件（连接失败、重连事件等）
     *     确保UI能及时获取所有状态变化
     */
    private func updateTrafficStatistics() async {
        guard await lifecycleManager.isStarted() else {
            return
        }

        // 1. 获取当前连接状态和接口信息（无论是否连接都要检查）
        let currentConnectionState = currentState
        let physicalInterface = NetworkInterfaceService.getPhysicalInterfaceInfo()
        let tunnelIP = getTunnelIPAddress() ?? ""

        // 2. 检查VPN扩展状态事件（包括连接失败、重连失败、重连成功等）
        let vpnExtensionEvents = await checkVPNExtensionEvents()

        // 3. 获取流量统计（仅在连接时有效）
        var stats = TrafficStatistics()
        if currentConnectionState.isConnected {
            stats = getSharedTrafficStatisticsForMonitoring()
        }

        lastTrafficUpdate = Date()

        logger.info("Comprehensive status update", metadata: [
            "connection_state": "\(currentConnectionState)",
            "interface_name": physicalInterface.interfaceName,
            "local_ip": physicalInterface.localIP,
            "tunnel_ip": tunnelIP,
            "total_upload": "\(stats.totalUpload)",
            "total_download": "\(stats.totalDownload)",
            "events_detected": "\(vpnExtensionEvents.count)"
        ])

        // 4. 处理VPN扩展事件（连接失败、重连失败、重连成功等）
        for event in vpnExtensionEvents {
            await handleVPNExtensionEvent(event)
        }

        // 5. 发送流量更新（即使是零值也发送，保持UI一致性）
        notifyDelegate { delegate in
            delegate.vpnService(self, didUpdateTraffic: stats)
        }

        // 6. 检查是否需要发送接口信息更新
        if shouldNotifyInterfaceUpdate(physicalInterface: physicalInterface, tunnelIP: tunnelIP) {
            logger.info("Sending interface info update", metadata: [
                "interface_name": physicalInterface.interfaceName,
                "local_ip": physicalInterface.localIP,
                "tunnel_ip": tunnelIP
            ])

            notifyDelegate { delegate in
                delegate.vpnService(self, didUpdateNetworkInterface: physicalInterface.interfaceName,
                                  ipAddress: physicalInterface.localIP)
            }
        }

        // 7. 存储当前状态到App Group供UI查询
        await storeCurrentStatusToAppGroup(
            connectionState: currentConnectionState,
            physicalInterface: physicalInterface,
            tunnelIP: tunnelIP,
            stats: stats
        )
    }
    
    // MARK: - Heartbeat Monitoring
    
    // Note: setupHeartbeatMonitoring() removed - heartbeat is handled by VPN Extension

    // MARK: - Shared Statistics Helper

    /**
     * NAME: getSharedTrafficStatisticsForMonitoring
     *
     * DESCRIPTION:
     *     Gets traffic statistics from App Group shared data for monitoring.
     *     This allows main app to access VPN Extension's traffic statistics.
     *
     * RETURNS:
     *     TrafficStatistics - Current traffic statistics from VPN Extension
     */
    private func getSharedTrafficStatisticsForMonitoring() -> TrafficStatistics {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient"),
              let sharedStats = userDefaults.object(forKey: "vpn_traffic_statistics") as? [String: Any] else {
            // logger.debug("No shared traffic statistics available, returning empty stats") // Debug log commented for production
            return TrafficStatistics()
        }

        // Check if data is recent (within last 30 seconds)
        let timestamp = sharedStats["timestamp"] as? TimeInterval ?? 0
        let age = Date().timeIntervalSince1970 - timestamp

        if age > 30 {
            // logger.debug("Shared traffic statistics are stale", metadata: [
            //     "age_seconds": "\(age)"
            // ]) // Debug log commented for production
            return TrafficStatistics()
        }

        let stats = TrafficStatistics(
            totalUpload: sharedStats["totalUpload"] as? Int64 ?? 0,
            totalDownload: sharedStats["totalDownload"] as? Int64 ?? 0,
            uploadSpeed: sharedStats["uploadSpeed"] as? Int64 ?? 0,
            downloadSpeed: sharedStats["downloadSpeed"] as? Int64 ?? 0,
            lastUpdate: Date(timeIntervalSince1970: sharedStats["lastUpdate"] as? TimeInterval ?? 0)
        )

        // logger.debug("Retrieved shared traffic statistics", metadata: [
        //     "total_upload": "\(stats.totalUpload)",
        //     "total_download": "\(stats.totalDownload)",
        //     "upload_speed": "\(stats.uploadSpeed)",
        //     "download_speed": "\(stats.downloadSpeed)",
        //     "age_seconds": "\(age)"
        // ]) // Debug log commented for production

        return stats
    }
    

    
    // MARK: - Error Handling and Utilities
    
    /**
     * NAME: handleMonitoringError
     *
     * DESCRIPTION:
     *     Unified error handling for monitoring operations with consistent logging.
     *
     * PARAMETERS:
     *     error - Error that occurred
     *     operation - Name of the operation that failed
     */
    private func handleMonitoringError(_ error: Error, operation: String) {
        logger.warning("Monitoring operation failed", metadata: [
            "operation": operation,
            "error": error.localizedDescription,
            "connection_state": currentState.description
        ])
    }



    // MARK: - VPN Extension Event Handling

    /**
     * NAME: checkVPNExtensionEvents
     *
     * DESCRIPTION:
     *     检查App Group中VPN扩展写入的各种事件，包括：
     *     - 重连成功事件
     *     - 连接失败事件
     *     - 重连失败事件
     *     - 状态变化事件
     *
     * RETURNS:
     *     [VPNExtensionEvent] - 检测到的事件列表
     */
    private func checkVPNExtensionEvents() async -> [VPNExtensionEvent] {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            return []
        }

        var events: [VPNExtensionEvent] = []

        // 检查重连成功事件
        if let reconnectionEvent = userDefaults.object(forKey: "vpn_reconnection_event") as? [String: Any],
           let eventType = reconnectionEvent["event_type"] as? String,
           eventType == "reconnection_success",
           let timestamp = reconnectionEvent["timestamp"] as? TimeInterval {

            // 检查事件是否新鲜且未处理
            let eventAge = Date().timeIntervalSince1970 - timestamp
            let lastProcessedTimestamp = userDefaults.double(forKey: "last_processed_reconnection_timestamp")

            if eventAge < 30 && timestamp > lastProcessedTimestamp {
                let tunnelIP = reconnectionEvent["tunnel_ip"] as? String ?? ""
                let serverAddress = reconnectionEvent["server_address"] as? String ?? ""

                events.append(.reconnectionSuccess(tunnelIP: tunnelIP, serverAddress: serverAddress, timestamp: timestamp))

                // 标记为已处理
                userDefaults.set(timestamp, forKey: "last_processed_reconnection_timestamp")
            }
        }

        // 检查连接失败事件
        if let connectionFailureEvent = userDefaults.object(forKey: "vpn_connection_failure_event") as? [String: Any],
           let eventType = connectionFailureEvent["event_type"] as? String,
           eventType == "connection_failure",
           let timestamp = connectionFailureEvent["timestamp"] as? TimeInterval {

            let eventAge = Date().timeIntervalSince1970 - timestamp
            let lastProcessedTimestamp = userDefaults.double(forKey: "last_processed_connection_failure_timestamp")

            if eventAge < 30 && timestamp > lastProcessedTimestamp {
                let errorMessage = connectionFailureEvent["error_message"] as? String ?? ""
                let serverAddress = connectionFailureEvent["server_address"] as? String ?? ""

                events.append(.connectionFailure(errorMessage: errorMessage, serverAddress: serverAddress, timestamp: timestamp))

                // 标记为已处理
                userDefaults.set(timestamp, forKey: "last_processed_connection_failure_timestamp")
            }
        }

        // 检查重连失败事件
        if let reconnectionFailureEvent = userDefaults.object(forKey: "vpn_reconnection_failure_event") as? [String: Any],
           let eventType = reconnectionFailureEvent["event_type"] as? String,
           eventType == "reconnection_failure",
           let timestamp = reconnectionFailureEvent["timestamp"] as? TimeInterval {

            let eventAge = Date().timeIntervalSince1970 - timestamp
            let lastProcessedTimestamp = userDefaults.double(forKey: "last_processed_reconnection_failure_timestamp")

            if eventAge < 30 && timestamp > lastProcessedTimestamp {
                let errorMessage = reconnectionFailureEvent["error_message"] as? String ?? ""
                let serverAddress = reconnectionFailureEvent["server_address"] as? String ?? ""

                events.append(.reconnectionFailure(errorMessage: errorMessage, serverAddress: serverAddress, timestamp: timestamp))

                // 标记为已处理
                userDefaults.set(timestamp, forKey: "last_processed_reconnection_failure_timestamp")
            }
        }

        // 同步UserDefaults
        if !events.isEmpty {
            userDefaults.synchronize()
        }

        return events
    }

    /**
     * NAME: handleVPNExtensionEvent
     *
     * DESCRIPTION:
     *     处理VPN扩展事件并通知delegate
     *
     * PARAMETERS:
     *     event - VPN扩展事件
     */
    private func handleVPNExtensionEvent(_ event: VPNExtensionEvent) async {
        switch event {
        case .reconnectionSuccess(let tunnelIP, let serverAddress, let timestamp):
            logger.info("Handling reconnection success event", metadata: [
                "tunnel_ip": tunnelIP,
                "server_address": serverAddress,
                "timestamp": "\(timestamp)"
            ])

            // 通知delegate重连成功
            notifyDelegate { delegate in
                delegate.vpnService(self, didReconnectSuccessfully: tunnelIP, serverAddress: serverAddress)
            }

            // 清理事件
            await cleanupProcessedEvent("vpn_reconnection_event")

        case .connectionFailure(let errorMessage, let serverAddress, let timestamp):
            logger.warning("Handling connection failure event", metadata: [
                "error_message": errorMessage,
                "server_address": serverAddress,
                "timestamp": "\(timestamp)"
            ])

            // 通知delegate连接失败
            notifyDelegate { delegate in
                delegate.vpnService(self, didFailToConnect: errorMessage, serverAddress: serverAddress)
            }

            // 清理事件
            await cleanupProcessedEvent("vpn_connection_failure_event")

        case .reconnectionFailure(let errorMessage, let serverAddress, let timestamp):
            logger.warning("Handling reconnection failure event", metadata: [
                "error_message": errorMessage,
                "server_address": serverAddress,
                "timestamp": "\(timestamp)"
            ])

            // 通知delegate重连失败
            notifyDelegate { delegate in
                delegate.vpnService(self, didFailToReconnect: errorMessage, serverAddress: serverAddress)
            }

            // 清理事件
            await cleanupProcessedEvent("vpn_reconnection_failure_event")
        }
    }

    /**
     * NAME: cleanupProcessedEvent
     *
     * DESCRIPTION:
     *     清理已处理的事件
     *
     * PARAMETERS:
     *     eventKey - 事件在App Group中的key
     */
    private func cleanupProcessedEvent(_ eventKey: String) async {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            return
        }

        userDefaults.removeObject(forKey: eventKey)
        userDefaults.synchronize()
    }

    /**
     * NAME: shouldNotifyInterfaceUpdate
     *
     * DESCRIPTION:
     *     检查是否需要通知接口信息更新
     *
     * PARAMETERS:
     *     physicalInterface - 当前物理接口信息 (interfaceName, localIP)
     *     tunnelIP - 当前tunnel IP
     *
     * RETURNS:
     *     Bool - 是否需要通知更新
     */
    private func shouldNotifyInterfaceUpdate(physicalInterface: (interfaceName: String, localIP: String), tunnelIP: String) -> Bool {
        // 简单实现：总是通知更新以确保UI同步
        // 可以根据需要添加更复杂的变化检测逻辑
        return true
    }

    /**
     * NAME: storeCurrentStatusToAppGroup
     *
     * DESCRIPTION:
     *     将当前状态存储到App Group供UI查询
     *
     * PARAMETERS:
     *     connectionState - 连接状态
     *     physicalInterface - 物理接口信息 (interfaceName, localIP)
     *     tunnelIP - tunnel IP
     *     stats - 流量统计
     */
    private func storeCurrentStatusToAppGroup(
        connectionState: VPNState,
        physicalInterface: (interfaceName: String, localIP: String),
        tunnelIP: String,
        stats: TrafficStatistics
    ) async {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            return
        }

        let statusInfo: [String: Any] = [
            "connection_state": connectionState.flutterStatusString,
            "interface_name": physicalInterface.interfaceName,
            "local_ip": physicalInterface.localIP,
            "tunnel_ip": tunnelIP,
            "total_upload": stats.totalUpload,
            "total_download": stats.totalDownload,
            "upload_speed": stats.uploadSpeed,
            "download_speed": stats.downloadSpeed,
            "timestamp": Date().timeIntervalSince1970,
            "is_connected": connectionState.isConnected
        ]

        userDefaults.set(statusInfo, forKey: "vpn_current_status")
        userDefaults.synchronize()
    }

    // Note: notifyDelegate method is defined in main VPNService.swift file
}
