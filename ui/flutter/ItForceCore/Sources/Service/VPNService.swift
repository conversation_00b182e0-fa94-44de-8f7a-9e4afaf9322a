/**
 * FILE: VPNService.swift
 *
 * DESCRIPTION:
 *     Unified VPN service providing complete VPN management interface.
 *     Directly manages ConnectionManager, ServerService, and VPN authorization,
 *     eliminating the redundant ConnectionService layer for simplified architecture.
 *     Handles complex business logic, state coordination, and iOS/macOS NetworkExtension integration.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import Network
import NetworkExtension
import OSLog

// MARK: - Timeout Error
struct TimeoutError: Error {
    let message: String

    init(_ message: String = "Operation timed out") {
        self.message = message
    }
}

// MARK: - Timeout Utility
func withTimeout<T>(seconds: TimeInterval, operation: @escaping () async throws -> T) async throws -> T {
    return try await withThrowingTaskGroup(of: T.self) { group in
        group.addTask {
            try await operation()
        }

        group.addTask {
            try await Task.sleep(nanoseconds: UInt64(seconds * 1_000_000_000))
            throw TimeoutError("Operation timed out after \(seconds) seconds")
        }

        let result = try await group.next()!
        group.cancelAll()
        return result
    }
}

// Note: AutoReconnectConfiguration removed - reconnection is handled by VPN Extension

/**
 * NAME: VPNServiceConfiguration
 *
 * DESCRIPTION:
 *     Unified configuration structure for VPN service.
 *     Combines high-level service settings with connection-specific preferences.
 *     Replaces both VPNServiceConfiguration and ConnectionServiceConfiguration.
 *
 * PROPERTIES:
 *     autoConnect - Whether to auto-connect on service start
 *     autoSelectBestServer - Whether to automatically select best server
 *     trafficUpdateInterval - Traffic statistics update interval
 *     heartbeatInterval - Connection heartbeat interval (from ConnectionService)
 *     autoReconnect - Whether to enable auto-reconnection (from ConnectionService)
 */
public struct VPNServiceConfiguration: BaseServiceConfiguration {
    // MARK: - BaseServiceConfiguration Implementation
    public let retry: RetryConfiguration
    public let timeout: TimeoutConfiguration
    public let monitoring: MonitoringConfiguration

    // MARK: - VPN-Specific Configuration
    public let autoConnect: Bool
    public let autoSelectBestServer: Bool
    public let trafficUpdateInterval: TimeInterval

    // Note: heartbeatInterval and autoReconnect removed - handled by VPN Extension

    public init(
        retry: RetryConfiguration = .default,
        timeout: TimeoutConfiguration = .default,
        monitoring: MonitoringConfiguration = .default,
        autoConnect: Bool = false,
        autoSelectBestServer: Bool = true,
        trafficUpdateInterval: TimeInterval = 2.0
    ) {
        self.retry = retry
        self.timeout = timeout
        self.monitoring = monitoring
        self.autoConnect = autoConnect
        self.autoSelectBestServer = autoSelectBestServer
        self.trafficUpdateInterval = trafficUpdateInterval
    }

    public static let `default` = VPNServiceConfiguration()
}

/**
 * NAME: VPNServiceEvent
 *
 * DESCRIPTION:
 *     VPN service event types for notification system.
 *     Used to notify Platform Channel and UI about VPN-related changes.
 */
public enum VPNServiceEvent: String, CaseIterable, Sendable {
    case serviceStarted = "service_started"
    case serviceStopped = "service_stopped"
    case vpnConnecting = "vpn_connecting"
    case vpnConnected = "vpn_connected"
    case vpnDisconnected = "vpn_disconnected"
    case vpnReconnecting = "vpn_reconnecting"
    case serverChanged = "server_changed"
    case trafficUpdated = "traffic_updated"
    case errorOccurred = "error_occurred"
    case networkExtensionRebuildRequired = "network_extension_rebuild_required"
}

/**
 * NAME: VPNState
 *
 * DESCRIPTION:
 *     Unified VPN state enum that combines operation state, connection state, and server context.
 *     Replaces the three-layer state management (VPNOperationState, ConnectionState, connectedServer)
 *     with a single, comprehensive state machine that eliminates state inconsistency issues.
 *
 * DESIGN PRINCIPLES:
 *     - Single source of truth for all VPN state information
 *     - Self-contained state with associated values for context
 *     - Platform-independent design with compatibility layers
 *     - Clear state transitions without manual synchronization
 */
public enum VPNState: Sendable {
    case disconnected
    case connecting(server: ServerInfo, progress: ConnectionProgress)
    case connected(server: ServerInfo, since: Date)
    case disconnecting(reason: DisconnectReason)
    case reconnecting(server: ServerInfo, attempt: Int)
    case error(VPNServiceError)
}

// MARK: - VPNState Equatable Implementation
extension VPNState: Equatable {
    public static func == (lhs: VPNState, rhs: VPNState) -> Bool {
        switch (lhs, rhs) {
        case (.disconnected, .disconnected):
            return true
        case (.connecting(let lhsServer, let lhsProgress), .connecting(let rhsServer, let rhsProgress)):
            return lhsServer.id == rhsServer.id && lhsProgress == rhsProgress
        case (.connected(let lhsServer, let lhsSince), .connected(let rhsServer, let rhsSince)):
            return lhsServer.id == rhsServer.id && lhsSince == rhsSince
        case (.disconnecting(let lhsReason), .disconnecting(let rhsReason)):
            return lhsReason == rhsReason
        case (.reconnecting(let lhsServer, let lhsAttempt), .reconnecting(let rhsServer, let rhsAttempt)):
            return lhsServer.id == rhsServer.id && lhsAttempt == rhsAttempt
        case (.error(let lhsError), .error(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        default:
            return false
        }
    }
}

/**
 * NAME: ConnectionProgress
 *
 * DESCRIPTION:
 *     Detailed connection progress states for fine-grained UI feedback.
 *     Maps to the original ConnectionState granularity while being part of unified state.
 */
public enum ConnectionProgress: Equatable, Sendable {
    case initializing        // NetworkExtension initialization
    case resolvingServer     // DNS resolution
    case authenticating      // VPN authentication in progress
    case establishingTunnel  // Tunnel establishment
}

/**
 * NAME: DisconnectReason
 *
 * DESCRIPTION:
 *     Reasons for VPN disconnection to provide better user feedback and debugging.
 */
public enum DisconnectReason: Equatable, Sendable {
    case userInitiated       // User manually disconnected
    case networkLost         // Network connectivity lost
    case authenticationFailed // Authentication failure
    case error(String)       // Error-induced disconnection
}

// MARK: - VPNState Extensions

extension VPNState {
    /**
     * NAME: Flutter Compatibility
     *
     * DESCRIPTION:
     *     Provides Flutter-compatible status strings for Platform Channel communication.
     *     Maps unified VPNState to Flutter ConnectionStatus enum values.
     */
    public var flutterStatusString: String {
        switch self {
        case .disconnected:
            return "disconnected"
        case .connecting, .reconnecting:
            return "connecting"
        case .connected:
            return "connected"
        case .disconnecting:
            return "disconnecting"
        case .error:
            return "error"
        }
    }

    /**
     * NAME: Connected Server
     *
     * DESCRIPTION:
     *     Extracts connected server information from state.
     */
    public var connectedServer: ServerInfo? {
        switch self {
        case .connected(let server, _),
             .reconnecting(let server, _):
            return server
        case .connecting(let server, _):
            return server
        default:
            return nil
        }
    }

    /**
     * NAME: Connection Time
     *
     * DESCRIPTION:
     *     Extracts connection time from state.
     */
    public var connectionTime: Date? {
        switch self {
        case .connected(_, let since):
            return since
        default:
            return nil
        }
    }

    /**
     * NAME: Is Connected
     *
     * DESCRIPTION:
     *     Returns whether VPN is in connected state.
     */
    public var isConnected: Bool {
        if case .connected = self { return true }
        return false
    }

    /**
     * NAME: Is Idle
     *
     * DESCRIPTION:
     *     Returns whether VPN is in idle state (disconnected and not performing operations).
     */
    public var isDisconnected: Bool {
        if case .disconnected = self { return true }
        return false
    }

    /**
     * NAME: Is Operation In Progress
     *
     * DESCRIPTION:
     *     Returns whether any VPN operation is in progress.
     */
    public var isOperationInProgress: Bool {
        switch self {
        case .disconnected, .connected, .error:
            return false
        default:
            return true
        }
    }

    /**
     * NAME: Is Error
     *
     * DESCRIPTION:
     *     Returns whether VPN is in error state.
     */
    public var isError: Bool {
        if case .error = self { return true }
        return false
    }

    /**
     * NAME: Description
     *
     * DESCRIPTION:
     *     Provides human-readable description of the VPN state for logging and debugging.
     */
    public var description: String {
        switch self {
        case .disconnected:
            return "disconnected"
        case .connecting(let server, let progress):
            return "connecting(server: \(server.name), progress: \(progress))"
        case .connected(let server, let since):
            return "connected(server: \(server.name), since: \(since))"
        case .disconnecting(let reason):
            return "disconnecting(reason: \(reason))"
        case .reconnecting(let server, let attempt):
            return "reconnecting(server: \(server.name), attempt: \(attempt))"
        case .error(let error):
            return "error(\(error.localizedDescription))"
        }
    }

    /**
     * NAME: Connection State
     *
     * DESCRIPTION:
     *     Maps VPNState to ConnectionState for delegate compatibility.
     *     Error states are mapped to authenticationFailed to preserve error information.
     */
    public var connectionState: ConnectionState {
        switch self {
        case .disconnected:
            return .disconnected
        case .error:
            // Map error states to authenticationFailed to preserve error information
            return .authenticationFailed
        case .connecting(_, let progress):
            switch progress {
            case .initializing:
                return .initializing
            case .resolvingServer:
                return .serverResolved
            case .authenticating, .establishingTunnel:
                return .authenticating
            }
        case .connected:
            return .connected
        case .disconnecting, .reconnecting:
            return .disconnected
        }
    }

    /**
     * NAME: Protocol Compatibility
     *
     * DESCRIPTION:
     *     Maps VPNState to Go backend protocol state values for SDWAN communication.
     */
    public var protocolValue: UInt8 {
        switch self {
        case .disconnected, .error:
            return 6  // StateClosed
        case .connecting(_, let progress):
            switch progress {
            case .initializing:
                return 2  // StateInit
            case .resolvingServer:
                return 3  // StateInit1
            case .authenticating, .establishingTunnel:
                return 4  // StateAuth
            }
        case .connected:
            return 5  // StateData
        case .disconnecting, .reconnecting:
            return 6  // StateClosed
        }
    }
}

/**
 * NAME: VPNDisconnectReason
 *
 * DESCRIPTION:
 *     Enumeration of possible disconnect reasons for unified disconnect handling.
 *     Separate from VPNState.DisconnectReason to avoid naming conflicts.
 */
public enum VPNDisconnectReason: String, CaseIterable, Sendable {
    case userInitiated = "user_initiated"
    case systemDisconnected = "system_disconnected"
    case connectionLost = "connection_lost"
    case heartbeatTimeout = "heartbeat_timeout"
    case networkChange = "network_change"
    case authenticationFailed = "authentication_failed"
    case configurationError = "configuration_error"

    public var description: String {
        switch self {
        case .userInitiated:
            return "User initiated disconnect"
        case .systemDisconnected:
            return "System disconnected VPN"
        case .connectionLost:
            return "Connection lost"
        case .heartbeatTimeout:
            return "Heartbeat timeout"
        case .networkChange:
            return "Network change detected"
        case .authenticationFailed:
            return "Authentication failed"
        case .configurationError:
            return "Configuration error"
        }
    }
}

/**
 * NAME: DisconnectSource
 *
 * DESCRIPTION:
 *     Enumeration of disconnect detection sources for unified disconnect handling.
 */
public enum DisconnectSource: String, CaseIterable, Sendable {
    case networkExtension = "network_extension"
    case connectionManager = "connection_manager"
    case userAction = "user_action"
    case autoReconnection = "auto_reconnection"
    case errorHandler = "error_handler"
}

/**
 * NAME: VPNOperationState (Legacy - Deprecated)
 *
 * DESCRIPTION:
 *     Legacy operation state enum - kept for backward compatibility during transition.
 *     Will be removed after migration to VPNState is complete.
 */
@available(*, deprecated, message: "Use VPNState instead")
public enum VPNOperationState: String, CaseIterable, Sendable {
    case idle = "idle"
    case connecting = "connecting"
    case connected = "connected"
    case disconnecting = "disconnecting"
    case reconnecting = "reconnecting"
}

/**
 * NAME: VPNServiceError
 *
 * DESCRIPTION:
 *     Unified VPN service error types with user-friendly messages.
 *     Combines VPNServiceError and ConnectionServiceError for simplified error handling.
 *     Provides business-level error information for UI display.
 */
public enum VPNServiceError: Error, LocalizedError, Sendable {
    case serviceNotStarted
    case serviceAlreadyStarted
    case serverServiceError(ServerServiceError)
    case configurationInvalid(String)
    case vpnPermissionDenied
    case networkExtensionError(String)
    case operationInProgress
    case noCredentialsConfigured

    // MARK: - Connection-Specific Errors (migrated from ConnectionService)
    case connectionInProgress
    case disconnectionInProgress
    case networkExtensionNotAvailable
    case serverNotSelected
    case authenticationFailed
    case authenticationTimeout
    case connectionTimeout

    // MARK: - Platform Channel Errors
    case platformChannelError(String)

    // MARK: - Auto-Reconnection Errors
    case autoReconnectFailed(String)

    public var errorDescription: String? {
        switch self {
        case .serviceNotStarted:
            return "VPN service is not started. Please start the service first."
        case .serviceAlreadyStarted:
            return "VPN service is already running."
        case .serverServiceError(let error):
            return "Server error: \(error.localizedDescription)"
        case .configurationInvalid(let reason):
            return "Invalid VPN configuration: \(reason)"
        case .vpnPermissionDenied:
            return "VPN permission denied. Please enable VPN in system settings."
        case .networkExtensionError(let reason):
            return "NetworkExtension error: \(reason)"
        case .operationInProgress:
            return "Another VPN operation is in progress. Please wait for completion."
        case .noCredentialsConfigured:
            return "No VPN credentials configured. Please configure credentials first."
        case .connectionInProgress:
            return "Connection is already in progress. Please wait for completion."
        case .disconnectionInProgress:
            return "Disconnection is in progress. Please wait for completion."
        case .networkExtensionNotAvailable:
            return "NetworkExtension is not available. Please check VPN permissions."
        case .serverNotSelected:
            return "No server selected. Please select a server before connecting."
        case .authenticationFailed:
            return "Authentication failed. Please check your credentials in NetworkExtension configuration."
        case .authenticationTimeout:
            return "Authentication timed out. Please check your network connection and try again."
        case .connectionTimeout:
            return "Connection timed out. Please check your network connection and try again."
        case .platformChannelError(let reason):
            return "Platform channel error: \(reason)"
        case .autoReconnectFailed(let reason):
            return "Auto-reconnection failed: \(reason)"
        }
    }
}

/**
 * NAME: ConnectionCredentials
 *
 * DESCRIPTION:
 *     User credentials for VPN authentication.
 *     Contains username and password for server authentication.
 */
public struct ConnectionCredentials: Sendable {
    public let username: String
    public let password: String

    public init(username: String, password: String) {
        self.username = username
        self.password = password
    }
}

/**
 * NAME: TrafficStatistics
 *
 * DESCRIPTION:
 *     Traffic statistics structure matching Go backend TrafficStats.
 *     Contains upload/download speeds and cumulative data transfer amounts.
 *
 * PROPERTIES:
 *     totalUpload - Total uploaded bytes
 *     totalDownload - Total downloaded bytes
 *     uploadSpeed - Upload speed in bytes per second
 *     downloadSpeed - Download speed in bytes per second
 *     lastUpdate - Last update timestamp
 */
public struct TrafficStatistics: Sendable, Codable {
    public let totalUpload: Int64
    public let totalDownload: Int64
    public let uploadSpeed: Int64
    public let downloadSpeed: Int64
    public let lastUpdate: Date

    public init(
        totalUpload: Int64 = 0,
        totalDownload: Int64 = 0,
        uploadSpeed: Int64 = 0,
        downloadSpeed: Int64 = 0,
        lastUpdate: Date = Date()
    ) {
        self.totalUpload = totalUpload
        self.totalDownload = totalDownload
        self.uploadSpeed = uploadSpeed
        self.downloadSpeed = downloadSpeed
        self.lastUpdate = lastUpdate
    }
}

/**
 * NAME: VPNServiceDelegate
 *
 * DESCRIPTION:
 *     Delegate protocol for VPN service events.
 *     Allows Platform Channel to receive comprehensive VPN notifications.
 */
public protocol VPNServiceDelegate: AnyObject {
    func vpnService(_ service: VPNService, didChangeState state: ConnectionState)
    func vpnService(_ service: VPNService, didConnectToServer server: ServerInfo)
    func vpnService(_ service: VPNService, didDisconnectWithReason reason: String?)
    func vpnService(_ service: VPNService, didUpdateTraffic stats: TrafficStatistics)
    func vpnService(_ service: VPNService, didEncounterError error: VPNServiceError)
    func vpnService(_ service: VPNService, didRequireNetworkExtensionRebuild server: ServerInfo)
    func vpnService(_ service: VPNService, didUpdateServerList servers: [ServerInfo])
    func vpnService(_ service: VPNService, didCompletePing results: [String: PingResult])

    // 网络接口更新通知 - 复用interface_info事件
    func vpnService(_ service: VPNService, didUpdateNetworkInterface interfaceName: String, ipAddress: String)

    // VPN扩展事件通知 - 复用status事件，通过message区分事件类型
    func vpnService(_ service: VPNService, didReconnectSuccessfully tunnelIP: String, serverAddress: String)
    func vpnService(_ service: VPNService, didFailToConnect errorMessage: String, serverAddress: String)
    func vpnService(_ service: VPNService, didFailToReconnect errorMessage: String, serverAddress: String)

    // didReceiveHeartbeat removed - heartbeat is handled entirely by VPN extension
    // Main app should not depend on heartbeat events to prevent extension blocking
}

/**
 * NAME: VPNConnectionInfo
 *
 * DESCRIPTION:
 *     Comprehensive VPN connection information for UI display.
 *     Combines connection status, server info, and traffic statistics.
 */
public struct VPNConnectionInfo: Sendable {
    public let state: ConnectionState
    public let connectedServer: ServerInfo?
    public let connectionTime: Date?
    public let trafficStats: TrafficStatistics
    public let lastError: String?

    public init(
        state: ConnectionState,
        connectedServer: ServerInfo? = nil,
        connectionTime: Date? = nil,
        trafficStats: TrafficStatistics = TrafficStatistics(),
        lastError: String? = nil
    ) {
        self.state = state
        self.connectedServer = connectedServer
        self.connectionTime = connectionTime
        self.trafficStats = trafficStats
        self.lastError = lastError
    }
}

/**
 * NAME: VPNService
 *
 * DESCRIPTION:
 *     Simplified VPN service providing NetworkExtension lifecycle management.
 *     Manages only high-level VPN operations: server selection, NetworkExtension
 *     authorization, and state monitoring. Network protocol processing is handled
 *     exclusively by VPN Extension (PacketTunnelProvider).
 *
 *     IMPORTANT: This service is designed for iOS/macOS NetworkExtension integration
 *     and handles platform-specific requirements like NE rebuild for server switches.
 *
 * PROPERTIES:
 *     serverService - Server management service
 *     vpnManager - VPN manager for authorization and system integration
 *     vpnPermissionHandler - VPN permission handler for authorization flow
 *     configuration - Unified service configuration
 *     delegate - Service delegate for event notifications
 *     logger - Logger instance
 *     isOperationInProgress - Operation in progress flag
 *     connectionStartTime - Connection start timestamp
 *     connectionState - Current connection state
 *     connectedServer - Currently connected server
 *     lastTrafficUpdate - Last traffic update time
 *     trafficUpdateTimer - Traffic update timer
 */
public class VPNService: ServiceLifecycle {
    // MARK: - Core Dependencies (simplified architecture - no ConnectionManager)

    public let serverService: ServerService
    internal let configuration: VPNServiceConfiguration
    internal let logger: LoggerProtocol
    internal let lifecycleManager: ServiceLifecycleManager

    // MARK: - NetworkExtension Management (integrated from VPNManager)

    internal var tunnelManager: NETunnelProviderManager?
    internal var isNetworkExtensionConfigured: Bool = false
    internal var currentVPNConfiguration: VPNConfiguration?

    // MARK: - Credentials Storage

    /// Stored user credentials for VPN authentication
    private var storedCredentials: ConnectionCredentials?

    // MARK: - Constants for NetworkExtension

    internal let bundleIdentifier = "com.panabit.PanabitClient.Extension"
    internal let localizedDescription = "Panabit Client"

    // MARK: - Unified State Management (refactored architecture)

    /// Unified VPN state - replaces operationState, connectionState, and connectedServer
    public private(set) var currentState: VPNState = .disconnected

    /// Timestamp when state was last changed (for stuck state detection)
    internal var stateChangedTime: Date?

    // MARK: - Unified Disconnect Coordination

    /// Flag to prevent concurrent disconnect operations
    internal var isPerformingUnifiedDisconnect = false

    /// Flag to prevent concurrent state cleanup operations
    private var isPerformingStateCleanup = false



    // MARK: - Traffic Monitoring State (moved to VPNService+Monitoring.swift)
    // Monitoring state has been moved to VPNService+Monitoring.swift for better code organization

    // MARK: - State Transition Helpers

    /**
     * NAME: isValidStateTransition
     *
     * DESCRIPTION:
     *     Validates whether a state transition is allowed.
     *     Prevents invalid transitions like connecting -> disconnected during connection establishment.
     *
     * PARAMETERS:
     *     from - Current state
     *     to - Target state
     *
     * RETURNS:
     *     Bool - True if transition is valid, false otherwise
     */
    private func isValidStateTransition(from oldState: VPNState, to newState: VPNState) -> Bool {
        switch (oldState, newState) {
        case (.connecting, .disconnected):
            // Block transition from connecting to disconnected (transient system states)
            // Exception: Allow if this is a force reset operation
            return false

        case (.reconnecting, .disconnected):
            // Block transition from reconnecting to disconnected
            return false

        case (.error, .disconnected):
            // Allow error -> disconnected transition for user retry
            return true

        default:
            // Allow all other transitions
            return true
        }
    }

    /**
     * NAME: updateVPNState
     *
     * DESCRIPTION:
     *     Updates the unified VPN state and synchronizes legacy state properties.
     *     Provides a single point for state transitions to ensure consistency.
     *     Includes state deduplication to prevent unnecessary notifications.
     *
     * PARAMETERS:
     *     newState - New VPN state to transition to
     */
    internal func updateVPNState(_ newState: VPNState) {
        let oldState = currentState

        // State deduplication: Skip if state hasn't actually changed
        if oldState == newState {
            // logger.debug("State update skipped - no change", metadata: [
            //     "current_state": "\(oldState)",
            //     "requested_state": "\(newState)"
            // ]) // Debug log commented for production
            return
        }

        // State transition validation: prevent invalid transitions
        if !isValidStateTransition(from: oldState, to: newState) {
            // logger.info("Blocking invalid state transition", metadata: [
            //     "from_state": "\(oldState)",
            //     "to_state": "\(newState)",
            //     "reason": "Invalid transition during operation"
            // ]) // Debug log commented for production
            return
        }

        currentState = newState
        stateChangedTime = Date()

        // Update connectionStartTime based on state
        if case .connected(_, let since) = newState {
            connectionStartTime = since
        } else if case .disconnected = newState {
            connectionStartTime = nil
        }

        // Ensure component state consistency for terminal states
        Task {
            await ensureComponentStateConsistency(for: newState)
        }

        // Enhanced state transition logging commented for production
        // logger.info("VPN state transition", metadata: [
        //     "old_state": "\(oldState)",
        //     "new_state": "\(newState)",
        //     "old_is_operation_in_progress": "\(oldState.isOperationInProgress)",
        //     "new_is_operation_in_progress": "\(newState.isOperationInProgress)",
        //     "old_is_connected": "\(oldState.isConnected)",
        //     "new_is_connected": "\(newState.isConnected)",
        //     "flutter_status": newState.flutterStatusString,
        //     "thread": Thread.isMainThread ? "main" : "background"
        // ]) // Debug log commented for production

        // Notify delegate with Flutter-compatible state
        logger.info("VPN state changed to: \(newState.connectionState)")

        delegate?.vpnService(self, didChangeState: newState.connectionState)
    }

    // MARK: - Unified Disconnect Coordination

    /**
     * NAME: performUnifiedDisconnect
     *
     * DESCRIPTION:
     *     Unified disconnect coordinator that ensures all layers (NetworkExtension,
     *     ConnectionManager, VPNState) are properly synchronized during disconnect.
     *     Prevents state inconsistency and duplicate event notifications.
     *
     * PARAMETERS:
     *     reason - Reason for disconnection
     *     source - Source that detected the disconnect
     */
    internal func performUnifiedDisconnect(
        reason: VPNDisconnectReason,
        source: DisconnectSource
    ) async {
        // Prevent concurrent disconnect operations (simplified without locks)
        guard !isPerformingUnifiedDisconnect else {
            // logger.debug("Unified disconnect already in progress, skipping", metadata: [
            //     "reason": reason.rawValue,
            //     "source": source.rawValue
            // ]) // Debug log commented for production
            return
        }

        isPerformingUnifiedDisconnect = true
        defer { isPerformingUnifiedDisconnect = false }

        logger.info("Performing unified disconnect", metadata: [
            "reason": reason.rawValue,
            "source": source.rawValue,
            "current_state": "\(currentState)"
        ])

        // Simplified component stopping - only NetworkExtension needs to be managed
        switch source {
        case .networkExtension:
            // NetworkExtension already disconnected, no additional action needed
            break
        case .connectionManager:
            // ConnectionManager disconnect detected, stop NetworkExtension
            await forceStopNetworkExtension()
        case .userAction, .autoReconnection, .errorHandler:
            // External trigger, stop NetworkExtension
            await forceStopNetworkExtension()
        }

        // Update unified state to disconnected (single state transition)
        updateVPNState(.disconnected)

        // Single delegate notification for disconnection
        notifyDelegate { delegate in
            delegate.vpnService(self, didDisconnectWithReason: reason.description)
        }

        logger.info("Unified disconnect coordination completed", metadata: [
            "reason": reason.rawValue,
            "source": source.rawValue,
            "final_state": "\(currentState)"
        ])
    }

    // Note: forceStopConnectionManager removed - ConnectionManager is handled by VPN Extension

    /**
     * NAME: forceStopNetworkExtension
     *
     * DESCRIPTION:
     *     Forces NetworkExtension to stop if it's still running.
     *     Ensures NetworkExtension is synchronized with the disconnect operation.
     */
    internal func forceStopNetworkExtension() async {
        logger.info("Force stopping NetworkExtension")

        // Check current NetworkExtension status
        let currentStatus = await getVPNConnectionStatus()

        if currentStatus != .disconnected && currentStatus != .invalid {
            // logger.debug("NetworkExtension still active, forcing stop", metadata: [
            //     "current_status": "\(currentStatus.rawValue)"
            // ]) // Debug log commented for production

            stopNetworkExtensionTunnel()
            // logger.debug("NetworkExtension force stop completed successfully") // Debug log commented for production
        } else {
            // logger.debug("NetworkExtension already stopped", metadata: [
            //     "current_status": "\(currentStatus.rawValue)"
            // ]) // Debug log commented for production
        }
    }

    /**
     * NAME: notifyDelegate
     *
     * DESCRIPTION:
     *     Unified delegate notification method with thread safety.
     *     Ensures all delegate calls are made on the main thread.
     *     Made internal to be accessible from extension files.
     *
     * PARAMETERS:
     *     action - Delegate action to execute
     */
    internal func notifyDelegate(_ action: @escaping (VPNServiceDelegate) -> Void) {
        Task { @MainActor in
            guard let delegate = self.delegate else { return }
            action(delegate)
        }
    }

    // MARK: - Auto-Reconnection Management

    // MARK: - ServiceLifecycle Protocol Conformance

    public var serviceName: String { return "VPNService" }

    // MARK: - Delegate and Callbacks

    public weak var delegate: VPNServiceDelegate?

    // MARK: - NetworkExtension Management Methods (integrated from VPNManager)

    // checkNetworkExtensionPermissionStatus method migrated to VPNService+NetworkExtension.swift

    // requestNetworkExtensionPermission method migrated to VPNService+NetworkExtension.swift

    // createTunnelManager method migrated to VPNService+NetworkExtension.swift

    // saveManagerToPreferences, loadManagerFromPreferences, getVPNStatus methods migrated to VPNService+NetworkExtension.swift

    // startNetworkExtensionTunnel and stopNetworkExtensionTunnel methods migrated to VPNService+NetworkExtension.swift

    /**
     * NAME: setupNetworkExtensionNotifications
     *
     * DESCRIPTION:
     *     Sets up VPN status change notifications directly in VPNService.
     *     No longer relies on VPNManager - handles notifications directly.
     */
    private func setupNetworkExtensionNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(vpnStatusDidChange),
            name: .NEVPNStatusDidChange,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(vpnConfigurationDidChange),
            name: .NEVPNConfigurationChange,
            object: nil
        )

        logger.info("NetworkExtension notifications setup completed - direct monitoring")
    }

    /**
     * NAME: setupNetworkInterfaceChangeCallback
     *
     * DESCRIPTION:
     *     DEPRECATED: Network interface change callbacks removed to prevent extension blocking.
     *     ConnectionManager now operates independently without notifying main app.
     */
    private func setupNetworkInterfaceChangeCallback() async {
        // Network interface change callback removed to prevent extension blocking
        // ConnectionManager operates independently when main app is backgrounded
        logger.info("Network interface change callback disabled (prevents extension blocking)")
    }

    /**
     * NAME: ensureNetworkInterfaceCallbackSetup
     *
     * DESCRIPTION:
     *     Ensures network interface change callback is properly set up.
     *     Called during critical operations to guarantee callback availability.
     *     Only re-sets if callback is nil to avoid unnecessary re-setting.
     */
    private func ensureNetworkInterfaceCallbackSetup() async {
        // Re-setup the callback to ensure it's not lost
        // Since setNetworkInterfaceChangeCallback() is idempotent, this is safe
        await setupNetworkInterfaceChangeCallback()
        logger.debug("Network interface callback setup ensured")
    }

    /**
     * NAME: vpnStatusDidChange
     *
     * DESCRIPTION:
     *     Handles VPN status change notifications from NetworkExtension.
     *     Directly processes NEVPNStatusDidChange notifications.
     */
    @objc private func vpnStatusDidChange(_ notification: Notification) {
        guard let connection = notification.object as? NEVPNConnection else { return }

        logger.info("VPN status changed", metadata: [
            "status": "\(connection.status.rawValue)",
            "status_description": statusDescription(connection.status)
        ])

        Task {
            await handleVPNStatusChange(notification)
        }
    }

    /**
     * NAME: vpnConfigurationDidChange
     *
     * DESCRIPTION:
     *     Handles VPN configuration change notifications.
     */
    @objc private func vpnConfigurationDidChange(_ notification: Notification) {
        logger.info("VPN configuration changed")

        Task {
            // Reload tunnel manager to reflect changes
            try? await loadManagerFromPreferences()
        }
    }

    /**
     * NAME: statusDescription
     *
     * DESCRIPTION:
     *     Returns human-readable description for VPN status.
     */
    private func statusDescription(_ status: NEVPNStatus) -> String {
        switch status {
        case .invalid: return "invalid"
        case .disconnected: return "disconnected"
        case .connecting: return "connecting"
        case .connected: return "connected"
        case .reasserting: return "reasserting"
        case .disconnecting: return "disconnecting"
        @unknown default: return "unknown"
        }
    }

    /**
     * NAME: handleVPNStatusChange
     *
     * DESCRIPTION:
     *     Handles VPN status change in async context.
     *     Integrated from VPNManager to simplify architecture.
     */
    private func handleVPNStatusChange(_ notification: Notification) async {
        guard let connection = notification.object as? NEVPNConnection else {
            return
        }

        logger.info("VPN status changed", metadata: [
            "status": "\(connection.status.rawValue)"
        ])

        // Update unified VPN state based on NetworkExtension status
        switch connection.status {
        case .invalid, .disconnected:
            // Check if this was an unexpected disconnection
            let wasConnected = currentState.isConnected
            let wasConnecting = currentState.isOperationInProgress &&
                               (currentState.description.contains("connecting"))
            let wasDisconnecting = currentState.isOperationInProgress &&
                                   (currentState.description.contains("disconnecting"))
            let wasPermissionError: Bool
            if case .error(let error) = currentState,
               case .vpnPermissionDenied = error {
                wasPermissionError = true
            } else {
                wasPermissionError = false
            }

            logger.info("NetworkExtension status changed to disconnected/invalid", metadata: [
                "previous_state": "\(currentState)",
                "was_connected": "\(wasConnected)",
                "was_connecting": "\(wasConnecting)",
                "was_disconnecting": "\(wasDisconnecting)",
                "was_permission_error": "\(wasPermissionError)",
                "ne_status": "\(connection.status)"
            ])

            // Use unified disconnect coordination (state validation handled in updateVPNState)
            if !currentState.isDisconnected {
                let reason: VPNDisconnectReason = wasDisconnecting ? .userInitiated : .systemDisconnected
                logger.info("NetworkExtension unified disconnect coordination")
                await performUnifiedDisconnect(reason: reason, source: .networkExtension)
            } else {
                logger.info("NetworkExtension disconnect skipped - already disconnected")
            }

        case .connecting:
            // Only update if not already in a connecting state
            if !currentState.isOperationInProgress {
                if let server = currentState.connectedServer {
                    updateVPNState(.connecting(server: server, progress: .establishingTunnel))
                }
            }

        case .reasserting:
            // Handle iOS system's automatic network interface switching
            logger.info("NetworkExtension entered reasserting state - iOS handling network change", metadata: [
                "current_state": "\(currentState)",
                "connected_server": currentState.connectedServer?.name ?? "unknown"
            ])

            // Trust iOS system to handle network switching automatically
            // If iOS fails, our other mechanisms will detect and handle:
            // 1. NWPathMonitor will detect interface changes
            // 2. Heartbeat timeout will detect connection issues
            // No additional timeout monitoring needed - keep current state
            // logger.debug("Trusting iOS system to handle reasserting state automatically")
        case .connected:
            // Update to connected state with current server and timestamp
            var serverToUse: ServerInfo? = currentState.connectedServer

            // If no server in current state (e.g., due to error state), try to get from last selected server
            if serverToUse == nil {
                if let lastServer = await serverService.getCurrentServer() {
                    serverToUse = lastServer
                    logger.info("Recovered server info from ServerService", metadata: [
                        "server_id": lastServer.id,
                        "server_name": lastServer.name
                    ])
                }
            }

            if let server = serverToUse {
                updateVPNState(.connected(server: server, since: Date()))
                logger.info("VPN connected successfully", metadata: [
                    "server_id": server.id,
                    "server_name": server.name
                ])

                // Notify delegate of successful connection (separate from state change)
                notifyDelegate { delegate in
                    delegate.vpnService(self, didConnectToServer: server)
                }
            } else {
                logger.warning("VPN connected but no server information available")
            }
        case .disconnecting:
            // Only update if not already in disconnecting state
            if !currentState.isOperationInProgress {
                updateVPNState(.disconnecting(reason: .userInitiated))
            }
        @unknown default:
            logger.warning("Unknown VPN status: \(connection.status.rawValue)")
        }
    }
    private var serverServiceDelegate: ServerServiceDelegate?

    // MARK: - Closure Callbacks (Alternative to delegate for ping completion)

    private var onPingCompletion: (([String: PingResult]) -> Void)?



    // MARK: - Initialization

    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Initializes simplified VPN service for NetworkExtension management.
     *     Eliminates ConnectionManager dependency - network operations are handled
     *     exclusively by VPN Extension (PacketTunnelProvider).
     *
     * PARAMETERS:
     *     serverService - Server management service
     *     configuration - Unified service configuration
     *     logger - Logger instance
     */
    public init(
        serverService: ServerService,
        configuration: VPNServiceConfiguration = .default,
        logger: LoggerProtocol
    ) {
        self.serverService = serverService
        self.configuration = configuration
        self.logger = logger
        self.lifecycleManager = ServiceLifecycleManager(serviceName: "VPNService", logger: logger)

        // Setup NetworkExtension notifications
        Task {
            setupNetworkExtensionNotifications()

            // Setup network interface change callback for UI updates
            await setupNetworkInterfaceChangeCallback()

            // Reconnection is now handled internally by ConnectionManager
            // No need to register callbacks
        }

        logger.info("VPN service initialized with simplified architecture", metadata: [
            "auto_connect": "\(configuration.autoConnect)",
            "auto_select_server": "\(configuration.autoSelectBestServer)",
            "background_monitoring": "\(configuration.monitoring.enabled)",
            "traffic_update_interval": "\(configuration.trafficUpdateInterval)",
            "platform_note": "iOS/macOS NetworkExtension integration - ConnectionManager eliminated",
            "initial_state": "\(currentState)",
            "is_operation_in_progress": "\(currentState.isOperationInProgress)"
        ])
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - User Credentials Management

    /**
     * NAME: saveUserCredentials
     *
     * DESCRIPTION:
     *     Saves user credentials after successful login.
     *     These credentials will be used for VPN authentication.
     *
     * PARAMETERS:
     *     username - User's login username
     *     password - User's login password
     *
     * THROWS:
     *     VPNServiceError - If credentials cannot be saved
     */
    public func saveUserCredentials(username: String, password: String) async throws {
        logger.info("Saving user credentials for VPN authentication", metadata: [
            "username": username,
            "has_password": "\(!password.isEmpty)"
        ])

        // Store credentials in memory for VPN connection
        storedCredentials = ConnectionCredentials(username: username, password: password)

        logger.info("User credentials saved successfully")
    }

    /**
     * Test delegate functionality
     */
    public func testPingDelegate(with results: [String: PingResult]) async {
        logger.info("Testing ping delegate with test data")
        await handlePingCompletion(results)
        logger.info("Ping delegate test completed")
    }

    // MARK: - ServiceLifecycle Protocol Implementation

    public func getCurrentState() async -> ServiceState {
        return await lifecycleManager.getCurrentState()
    }

    public func isStarted() async -> Bool {
        return await lifecycleManager.isStarted()
    }

    /**
     * NAME: start
     *
     * DESCRIPTION:
     *     Starts VPN service and underlying components.
     *     Sets up callbacks, initializes services, and optionally auto-connects.
     *
     * THROWS:
     *     VPNServiceError - If start operation fails
     */
    public func start() async throws {
        try await lifecycleManager.startTransition()

        // Initial state logging commented for production
        // logger.info("VPN service starting", metadata: [
        //     "initial_state": "\(currentState)",
        //     "is_operation_in_progress": "\(currentState.isOperationInProgress)",
        //     "is_connected": "\(currentState.isConnected)"
        // ]) // Debug log commented for production

        do {
            // Start underlying services
            try await serverService.start()
            // Note: ConnectionManager removed - network operations handled by VPN Extension

            // State logging after services start commented for production
            // logger.info("Underlying services started", metadata: [
            //     "current_state": "\(currentState)",
            //     "is_operation_in_progress": "\(currentState.isOperationInProgress)"
            // ]) // Debug log commented for production

            // Setup callbacks
            await setupServiceCallbacks()

            // State logging after callbacks setup commented for production
            // logger.info("Service callbacks setup completed", metadata: [
            //     "current_state": "\(currentState)",
            //     "is_operation_in_progress": "\(currentState.isOperationInProgress)"
            // ]) // Debug log commented for production

            // Load existing VPN configuration if available (Apple's best practice)
            // This prevents creating duplicate configurations and properly restores VPN state
            let existingConfigLoaded = await loadExistingVPNConfiguration()
            logger.info("VPN configuration loading completed", metadata: [
                "existing_config_found": "\(existingConfigLoaded)",
                "is_network_extension_configured": "\(isNetworkExtensionConfigured)"
            ])

            // Check for stuck states on startup and clean them up
            if case .connecting = currentState {
                logger.warning("Found stuck connecting state on startup, resetting to idle")
                forceResetState()
            }

            // Check if VPN Extension is already connected and disconnect it
            await checkAndDisconnectExistingVPN()

            // Start background monitoring if enabled
            await startMonitoring()

            // Note: Network monitoring for auto-reconnection is handled by ServerService

            await lifecycleManager.completeStart()

            // State logging before delegate notification commented for production
            // logger.info("VPN service start completed", metadata: [
            //     "current_state": "\(currentState)",
            //     "is_operation_in_progress": "\(currentState.isOperationInProgress)",
            //     "notifying_delegate_with": "disconnected"
            // ]) // Debug log commented for production

            // Notify delegate
            delegate?.vpnService(self, didChangeState: .disconnected)

            // Auto-connect if enabled
            if configuration.autoConnect {
                logger.info("Auto-connect enabled, attempting connection")
                try await connect()
            }

        } catch let error as ServerServiceError {
            await lifecycleManager.failStart(error)
            throw VPNServiceError.serverServiceError(error)
        } catch {
            await lifecycleManager.failStart(error)
            throw VPNServiceError.configurationInvalid(error.localizedDescription)
        }
    }

    /**
     * NAME: stop
     *
     * DESCRIPTION:
     *     Stops VPN service and cleans up resources.
     */
    public func stop() async throws {
        try await lifecycleManager.stopTransition()

        // Stop monitoring
        stopMonitoring()

        // Note: Network monitoring is handled by ServerService, no need to stop here

        // Disconnect if connected
        if currentState.isConnected {
            try? await disconnect()
        }

        // Stop underlying services
        // Note: ConnectionManager removed - network operations handled by VPN Extension
        try? await serverService.stop()

        // Clear delegates
        serverServiceDelegate = nil

        // Reset unified state
        updateVPNState(.disconnected)
        connectionStartTime = nil



        await lifecycleManager.completeStop()
    }

    /**
     * NAME: restart
     *
     * DESCRIPTION:
     *     Restarts the VPN service.
     */
    public func restart() async throws {
        if await isStarted() {
            try await stop()
        }
        try await start()
    }

    // MARK: - Delegate Management

    /**
     * NAME: setDelegate
     *
     * DESCRIPTION:
     *     Sets service delegate for event notifications.
     *
     * PARAMETERS:
     *     delegate - Service delegate
     */
    public func setDelegate(_ delegate: VPNServiceDelegate?) {
        self.delegate = delegate
        // logger.debug("VPN service delegate set")
    }

    // MARK: - VPN Connection Management

    // MARK: - Connection Methods (moved to VPNService+Connection.swift)
    // connect(), disconnect(), reconnect(), switchToServer() methods are now in VPNService+Connection.swift


    // MARK: - Status and Information

    /**
     * NAME: getCurrentVPNState
     *
     * DESCRIPTION:
     *     Returns current unified VPN state for debugging purposes.
     *
     * RETURNS:
     *     VPNState - Current unified VPN state
     */
    public func getCurrentVPNState() async -> VPNState {
        return currentState
    }

    /**
     * NAME: getConnectionInfo
     *
     * DESCRIPTION:
     *     Returns comprehensive VPN connection information for UI display.
     *     Combines connection status, server info, and traffic statistics.
     *
     * RETURNS:
     *     VPNConnectionInfo - Complete connection information
     *
     * THROWS:
     *     VPNServiceError - If service not started
     */
    public func getConnectionInfo() async throws -> VPNConnectionInfo {
        try await ensureServiceStarted()

        // Use unified state management
        // Get traffic statistics from shared App Group data (for VPN Extension compatibility)
        let trafficStats = getSharedTrafficStatistics()

        // Convert unified state to legacy ConnectionState for compatibility
        let legacyConnectionState: ConnectionState
        switch currentState {
        case .disconnected, .error:
            legacyConnectionState = .disconnected
        case .connecting(_, let progress):
            switch progress {
            case .initializing:
                legacyConnectionState = .initializing
            case .resolvingServer:
                legacyConnectionState = .serverResolved
            case .authenticating, .establishingTunnel:
                legacyConnectionState = .authenticating
            }
        case .connected:
            legacyConnectionState = .connected
        case .disconnecting, .reconnecting:
            legacyConnectionState = .disconnected
        }

        return VPNConnectionInfo(
            state: legacyConnectionState,
            connectedServer: currentState.connectedServer,
            connectionTime: currentState.connectionTime,
            trafficStats: trafficStats,
            lastError: nil
        )
    }

    /**
     * NAME: RoutingConfiguration
     *
     * DESCRIPTION:
     *     Structure to hold routing configuration.
     */
    public struct RoutingConfiguration {
        public let mode: RoutingMode
        public let customRoutes: [String]

        public init(mode: RoutingMode = .all, customRoutes: [String] = []) {
            self.mode = mode
            self.customRoutes = customRoutes
        }
    }

    /**
     * NAME: getStoredRoutingConfiguration
     *
     * DESCRIPTION:
     *     Retrieves stored routing configuration from UserDefaults.
     *
     * RETURNS:
     *     RoutingConfiguration - Current routing settings
     */
    public func getStoredRoutingConfiguration() async -> RoutingConfiguration {
        let userDefaults = UserDefaults.standard
        let modeString = userDefaults.string(forKey: "vpn_routing_mode") ?? "all"
        let customRoutesString = userDefaults.string(forKey: "vpn_custom_routes") ?? ""

        let mode: RoutingMode = (modeString == "custom") ? .custom : .all
        let customRoutes = customRoutesString.isEmpty ? [] :
            customRoutesString.split(separator: ",").map { $0.trimmingCharacters(in: .whitespaces) }

        logger.info("Retrieved stored routing configuration", metadata: [
            "mode": modeString,
            "custom_routes_count": "\(customRoutes.count)"
        ])

        return RoutingConfiguration(mode: mode, customRoutes: customRoutes)
    }

    /**
     * NAME: getTunnelIPAddress
     *
     * DESCRIPTION:
     *     Gets the current VPN tunnel IP address from App Group shared storage.
     *     This IP is set by the NetworkExtension when the tunnel is established.
     *
     * RETURNS:
     *     String? - Tunnel IP address or nil if not available
     */
    public func getTunnelIPAddress() -> String? {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            logger.warning("Failed to access App Group UserDefaults for tunnel IP")
            return nil
        }

        guard let tunnelInfo = userDefaults.object(forKey: "vpn_tunnel_info") as? [String: Any] else {
            return nil
        }

        guard let tunnelIP = tunnelInfo["tunnel_ip"] as? String else {
            logger.warning("tunnel_ip key missing or invalid in App Group data")
            return nil
        }

        // Check if the data is recent (within last 5 minutes)
        if let timestamp = tunnelInfo["timestamp"] as? TimeInterval {
            let age = Date().timeIntervalSince1970 - timestamp
            let _ = age / 60.0 // Age in minutes for debugging reference

            if age > 300 { // 5 minutes
                //logger.warning("Tunnel IP data is stale", metadata: [
                //    "age_minutes": String(format: "%.1f", ageMinutes),
                //    "tunnel_ip": tunnelIP
                //])
                return nil
            }
        } else {
            logger.warning("No timestamp found in tunnel info")
        }

        logger.info("Successfully retrieved tunnel IP from App Group", metadata: [
            "tunnel_ip": tunnelIP
        ])

        return tunnelIP
    }

    /**
     * NAME: isConnected
     *
     * DESCRIPTION:
     *     Returns whether VPN is currently connected.
     *
     * RETURNS:
     *     Bool - True if connected, false otherwise
     */
    public func isConnected() async -> Bool {
        guard await lifecycleManager.isStarted() else {
            return false
        }

        // Use unified state management
        return currentState.isConnected
    }

    /**
     * NAME: getConnectionState
     *
     * DESCRIPTION:
     *     Returns current VPN connection state.
     *
     * RETURNS:
     *     ConnectionState - Current connection state
     */
    public func getConnectionState() async -> ConnectionState {
        guard await lifecycleManager.isStarted() else {
            return .disconnected
        }

        // Convert unified state to legacy ConnectionState for compatibility
        switch currentState {
        case .disconnected, .error:
            return .disconnected
        case .connecting(_, let progress):
            switch progress {
            case .initializing:
                return .initializing
            case .resolvingServer:
                return .serverResolved
            case .authenticating, .establishingTunnel:
                return .authenticating
            }
        case .connected:
            return .connected
        case .disconnecting, .reconnecting:
            return .disconnected
        }
    }

    /**
     * NAME: forceResetState
     *
     * DESCRIPTION:
     *     Forces VPN state reset to disconnected. Use this as a recovery mechanism
     *     when state gets stuck due to failed operations, or when user wants to retry after error.
     *     This allows transition from any state (including error) to disconnected.
     *
     * WARNING:
     *     This is a recovery method and should only be used when normal
     *     state transitions fail to clean up properly, or for user-initiated retry.
     */
    public func forceResetState() {
        let oldState = currentState

        logger.warning("Force resetting VPN state to disconnected", metadata: [
            "previous_state": "\(oldState)",
            "is_operation_in_progress": "\(oldState.isOperationInProgress)"
        ])

        // Force state reset to disconnected - this allows retry from any state
        // Use direct assignment to bypass state transition validation
        currentState = .disconnected
        stateChangedTime = Date()
        connectionStartTime = nil

        // Clear any pending operation flags
        isPerformingUnifiedDisconnect = false



        // Ensure component state consistency
        Task {
            await ensureComponentStateConsistency(for: .disconnected)
        }

        // Notify delegates of state change using correct state mapping
        notifyDelegate { delegate in
            delegate.vpnService(self, didChangeState: self.currentState.connectionState)
        }

        logger.info("VPN state force reset completed", metadata: [
            "previous_state": "\(oldState)",
            "new_state": "\(currentState)",
            "is_operation_in_progress": "\(currentState.isOperationInProgress)"
        ])
    }

    /**
     * NAME: validateAndRecoverFromStuckState
     *
     * DESCRIPTION:
     *     Validates current state consistency and recovers from stuck states.
     *     Checks if state has been in operation for too long and forces reset if needed.
     *     IMPORTANT: Does not reset error states - they are terminal states that user needs to see.
     */
    internal func validateAndRecoverFromStuckState() async {
        // IMPORTANT: Do not reset error states - they are terminal states with important user information
        switch currentState {
        case .error(let error):
            logger.info("Skipping stuck state validation for error state - user needs to see error", metadata: [
                "error": error.localizedDescription
            ])
            return
        case .disconnected, .connected:
            // Terminal states that are not stuck
            return
        case .connecting, .reconnecting, .disconnecting:
            // These are operation states that can get stuck
            break
        }

        // Check if current operation state is stuck for too long
        if currentState.isOperationInProgress {
            // Use connectionStartTime if available, otherwise estimate based on state change time
            let referenceTime: Date
            if let startTime = connectionStartTime {
                referenceTime = startTime
            } else if let stateTime = stateChangedTime {
                referenceTime = stateTime
            } else {
                // Fallback: estimate state age (this is a rough estimate)
                referenceTime = Date().addingTimeInterval(-60) // Assume max 60 seconds ago
            }

            let stateAge = Date().timeIntervalSince(referenceTime)
            let maxStateAge: TimeInterval = 27 // 27 seconds maximum for any operation (slightly more than connect timeout)

            if stateAge > maxStateAge {
                logger.warning("Operation state stuck for too long, forcing reset", metadata: [
                    "current_state": "\(currentState)",
                    "state_age": "\(stateAge)s",
                    "max_allowed_age": "\(maxStateAge)s",
                    "reference_time": "\(referenceTime)"
                ])

                // Force cleanup of all resources
                await performEmergencyCleanup()

                // Reset stuck operation state to disconnected (allows retry)
                forceResetState()
                return
            }
        }

        // Check state consistency between layers
        await validateStateConsistency()
    }

    /**
     * NAME: validateStateConsistency
     *
     * DESCRIPTION:
     *     Validates VPN state consistency by checking NetworkExtension status.
     *     Forces reset if inconsistency is detected.
     *
     *     NOTE: State validation now relies on NetworkExtension status and App Group data
     *     since ConnectionManager is handled by VPN Extension.
     */
    private func validateStateConsistency() async {
        let vpnServiceState = currentState
        let networkExtensionStatus = await getVPNStatus()

        // Check for state inconsistency between VPNService and NetworkExtension
        let isInconsistent = (networkExtensionStatus == .disconnected || networkExtensionStatus == .invalid)
                            && vpnServiceState.isOperationInProgress

        if isInconsistent {
            logger.warning("State inconsistency detected, forcing reset", metadata: [
                "network_extension_status": "\(networkExtensionStatus)",
                "vpn_service_state": "\(vpnServiceState)",
                "vpn_service_is_operation_in_progress": "\(vpnServiceState.isOperationInProgress)"
            ])

            // Force reset to align states
            forceResetState()
        }
    }

    /**
     * NAME: performEmergencyCleanup
     *
     * DESCRIPTION:
     *     Performs emergency cleanup of all VPN resources when state is stuck.
     *     This is a comprehensive cleanup that ensures all resources are properly released.
     */
    internal func performEmergencyCleanup() async {
        logger.warning("Performing emergency cleanup of VPN resources")

        // Note: ConnectionManager is handled by VPN Extension, no need to stop it here

        // Force stop NetworkExtension
        await forceStopNetworkExtension()

        // Stop all monitoring
        stopMonitoring()

        // Clear any pending operations
        isPerformingUnifiedDisconnect = false

        logger.info("Emergency cleanup completed")
    }

    /**
     * NAME: handleConnectionFailure
     *
     * DESCRIPTION:
     *     Unified error handling for all connection failures.
     *     Ensures consistent resource cleanup and state reset across all failure scenarios.
     *
     * PARAMETERS:
     *     error - The error that caused the failure
     *     shouldAllowRetry - Whether to set state to disconnected (allows retry) or error
     */
    internal func handleConnectionFailure(_ error: Error, shouldAllowRetry: Bool = true) async {
        logger.error("Handling connection failure with unified cleanup", metadata: [
            "error": error.localizedDescription,
            "error_type": "\(type(of: error))",
            "current_state": "\(currentState)",
            "should_allow_retry": "\(shouldAllowRetry)"
        ])

        // 1. Note: ConnectionManager is handled by VPN Extension

        // 2. Force stop NetworkExtension
        await forceStopNetworkExtension()

        // 3. Stop all monitoring and timers
        stopMonitoring()

        // 4. Clear any pending operations
        isPerformingUnifiedDisconnect = false

        // 5. Map error to VPNServiceError
        let vpnError: VPNServiceError
        if let vpnServiceError = error as? VPNServiceError {
            vpnError = vpnServiceError
        } else {
            vpnError = VPNServiceError.configurationInvalid(error.localizedDescription)
        }

        // 6. Set appropriate final state
        if shouldAllowRetry {
            // Set to disconnected to allow immediate retry
            updateVPNState(.disconnected)

            // Notify error but don't block retry
            delegate?.vpnService(self, didEncounterError: vpnError)
        } else {
            // Set to error state
            updateVPNState(.error(vpnError))
            delegate?.vpnService(self, didEncounterError: vpnError)
        }

        logger.info("Connection failure handling completed", metadata: [
            "final_state": "\(currentState)",
            "can_retry": "\(!currentState.isOperationInProgress)"
        ])
    }

    /**
     * NAME: checkAndDisconnectExistingVPN
     *
     * DESCRIPTION:
     *     Checks if VPN Extension is already connected on startup and disconnects it.
     *     This ensures clean state when app restarts.
     */
    internal func checkAndDisconnectExistingVPN() async {
        guard isNetworkExtensionConfigured else {
            return
        }

        let vpnStatus = await getVPNConnectionStatus()

        if vpnStatus == .connected {
            logger.warning("VPN Extension already connected on startup - disconnecting for clean state")
            stopNetworkExtensionTunnel()
            logger.info("Existing VPN connection disconnected")
        }
    }

    /**
     * NAME: ensureComponentStateConsistency
     *
     * DESCRIPTION:
     *     Ensures all components are in consistent state after VPN state change.
     *     This is the key method to prevent state inconsistencies.
     *
     * PARAMETERS:
     *     vpnState - The new VPN state to ensure consistency for
     */
    internal func ensureComponentStateConsistency(for vpnState: VPNState) async {
        switch vpnState {
        case .disconnected:
            // Ensure NetworkExtension is properly disconnected
            // Note: ConnectionManager state is handled by VPN Extension
            let networkStatus = await getVPNStatus()
            if networkStatus != .disconnected && networkStatus != .invalid {
                logger.info("Syncing NetworkExtension to disconnected state")
                await forceStopNetworkExtension()
            }

        case .error:
            // For error states, ensure clean disconnection
            await forceStopNetworkExtension()

        case .connecting, .reconnecting:
            // For operation states, ensure components are active
            break

        case .connected:
            // For connected state, validate NetworkExtension is actually connected
            let networkStatus = await getVPNStatus()
            if networkStatus != .connected {
                logger.warning("VPN state is connected but NetworkExtension is not - state inconsistency detected")
                // Don't force disconnect here, let the connection establish
            }

        case .disconnecting:
            // Ensure disconnection is in progress
            break
        }
    }

    /**
     * NAME: performPeriodicStateValidation
     *
     * DESCRIPTION:
     *     Performs periodic validation of state consistency.
     *     Should be called periodically to detect and fix state inconsistencies.
     */
    internal func performPeriodicStateValidation() async {
        // Only validate if service is started
        guard await lifecycleManager.isStarted() else { return }

        // Validate state consistency
        await validateStateConsistency()

        // Check for stuck states
        if currentState.isOperationInProgress {
            await validateAndRecoverFromStuckState()
        }
    }

    // MARK: - Direct Component Access (eliminates transparent pass-through methods)

    // MARK: - Direct Component Access
    // Note: serverService and connectionManager are already public properties
    // This eliminates the need for transparent pass-through methods like:
    // - getServerList() -> use serverService.getServerList() directly
    // - updateServerList() -> use serverService.updateServerList() directly
    // - pingAllServers() -> use serverService.pingAllServers() directly
    // - getCurrentServer() -> use serverService.getCurrentServer() directly


    // selectBestServer() transparent pass-through method removed
    // Use serverService.selectBestServer() directly for better efficiency
    // Example: try await vpnService.serverService.selectBestServer()

    // MARK: - VPN Authorization Management (migrated from ConnectionService)

    // checkVPNPermissionStatus, loadExistingVPNConfiguration, getVPNConnectionStatus methods migrated to VPNService+NetworkExtension.swift

    // MARK: - Connection State and Status (migrated from ConnectionService)



    /**
     * NAME: isConnected
     *
     * DESCRIPTION:
     *     Returns whether VPN is currently connected.
     *
     * RETURNS:
     *     Bool - True if connected, false otherwise
     */
    public func isConnected() -> Bool {
        return currentState.isConnected
    }

    /**
     * NAME: getConnectedServer
     *
     * DESCRIPTION:
     *     Returns currently connected server information.
     *
     * RETURNS:
     *     ServerInfo? - Connected server or nil
     */
    public func getConnectedServer() -> ServerInfo? {
        return currentState.connectedServer
    }





    // MARK: - Private Helper Methods

    /**
     * NAME: getSharedTrafficStatistics
     *
     * DESCRIPTION:
     *     Gets traffic statistics from App Group shared data.
     *     This allows main app to access VPN Extension's traffic statistics.
     *
     * RETURNS:
     *     TrafficStatistics - Current traffic statistics from VPN Extension
     */
    private func getSharedTrafficStatistics() -> TrafficStatistics {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient"),
              let sharedStats = userDefaults.object(forKey: "vpn_traffic_statistics") as? [String: Any] else {
            return TrafficStatistics()
        }

        // Check if data is recent (within last 30 seconds)
        let timestamp = sharedStats["timestamp"] as? TimeInterval ?? 0
        let age = Date().timeIntervalSince1970 - timestamp

        if age > 30 {
            return TrafficStatistics()
        }

        let stats = TrafficStatistics(
            totalUpload: sharedStats["totalUpload"] as? Int64 ?? 0,
            totalDownload: sharedStats["totalDownload"] as? Int64 ?? 0,
            uploadSpeed: sharedStats["uploadSpeed"] as? Int64 ?? 0,
            downloadSpeed: sharedStats["downloadSpeed"] as? Int64 ?? 0,
            lastUpdate: Date(timeIntervalSince1970: sharedStats["lastUpdate"] as? TimeInterval ?? 0)
        )

        return stats
    }

    /**
     * NAME: ensureServiceStarted
     *
     * DESCRIPTION:
     *     Ensures service is started before operations.
     *
     * THROWS:
     *     VPNServiceError.serviceNotStarted - If service not started
     */
    internal func ensureServiceStarted() async throws {
        guard await lifecycleManager.isStarted() else {
            throw VPNServiceError.serviceNotStarted
        }
    }

    // getTargetServer() method migrated to VPNService+Connection.swift
    // This method belongs to connection logic, not core service management

    /**
     * NAME: getCurrentCredentials
     *
     * DESCRIPTION:
     *     Gets current user credentials for VPN connection.
     *     Migrated from ConnectionService.
     *     Retrieves credentials from storage or configuration.
     *
     * RETURNS:
     *     ConnectionCredentials - Current user credentials
     *
     * THROWS:
     *     VPNServiceError - If credentials are not available
     */
    internal func getCurrentCredentials() async throws -> ConnectionCredentials {
        // Get credentials from secure storage or configuration
        // Note: Credentials are now managed independently since ConnectionManager is in VPN Extension
        guard let credentials = await getStoredCredentials() else {
            throw VPNServiceError.configurationInvalid("User credentials not configured")
        }

        return credentials
    }

    private func getStoredCredentials() async -> ConnectionCredentials? {
        return storedCredentials
    }

    /**
     * NAME: setupServiceCallbacks
     *
     * DESCRIPTION:
     *     Sets up callbacks from underlying services to coordinate events.
     *     Simplified for NetworkExtension-only architecture.
     */
    private func setupServiceCallbacks() async {
        logger.info("Setting up service callbacks")

        // Setup server service callbacks
        logger.info("Setting up server service delegate")
        serverServiceDelegate = createServerServiceDelegate()
        logger.info("Created ServerServiceDelegate: \(String(describing: serverServiceDelegate))")
        await serverService.setDelegate(serverServiceDelegate)
        logger.info("Server service delegate set successfully")

        logger.info("Service callbacks setup completed")
    }











    // MARK: - Traffic Statistics Update (moved to VPNService+Monitoring.swift)
    // updateTrafficStatistics() method has been moved to VPNService+Monitoring.swift for better code organization

    // MARK: - Event Handlers (moved to VPNService+EventHandling.swift)
    // Event handling methods have been moved to VPNService+EventHandling.swift for better code organization
}

// MARK: - Delegate Implementations (moved to VPNService+EventHandling.swift)
// ServerServiceDelegateImpl has been moved to VPNService+EventHandling.swift for better organization