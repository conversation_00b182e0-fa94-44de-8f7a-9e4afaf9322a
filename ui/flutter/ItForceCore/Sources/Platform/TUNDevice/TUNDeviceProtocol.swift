/**
 * FILE: TUNDeviceProtocol.swift
 *
 * DESCRIPTION:
 *     TUN device protocol definitions for iOS/macOS NetworkExtension integration.
 *     Provides unified interface for virtual network device operations.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import Network

/**
 * NAME: TUNDeviceProtocol
 *
 * DESCRIPTION:
 *     Protocol defining TUN device operations for virtual network interface management.
 *     Abstracts platform-specific TUN device implementations.
 *
 * METHODS:
 *     configure - Configure device with network settings
 *     start - Start device operations
 *     stop - Stop device operations
 *     readPacket - Read packet from device
 *     writePacket - Write packet to device
 *     setMTU - Set maximum transmission unit
 *     setAddresses - Set IP addresses
 *     addRoute - Add network route
 *     deleteRoute - Delete network route
 *     setDNS - Set DNS servers
 */
public protocol TUNDeviceProtocol: AnyObject {
    // Device Properties
    var name: String { get }
    var mtu: Int { get }
    var isUp: Bool { get }
    var addresses: [IPAddress] { get }
    
    // Device Lifecycle
    func configure(with config: TUNDeviceConfiguration) async throws
    func start() async throws
    func stop() async throws
    
    // Packet I/O
    func readPacket() async throws -> Data
    func writePacket(_ data: Data) async throws
    func readPackets(maxCount: Int) async throws -> [Data]
    func writePackets(_ packets: [Data]) async throws
    
    // Network Configuration
    func setMTU(_ mtu: Int) async throws
    func setAddresses(_ addresses: [IPAddress]) async throws
    func addRoute(destination: IPNetwork, gateway: IPAddress?, metric: Int) async throws
    func deleteRoute(destination: IPNetwork, gateway: IPAddress?) async throws
    func setDNS(servers: [IPAddress]) async throws
    func setDefaultGateway(_ gateway: IPAddress) async throws
    
    // Status and Monitoring
    func getStatistics() async -> TUNDeviceStatistics
    func flushConfiguration() async throws
}

/**
 * NAME: TUNDeviceConfiguration
 *
 * DESCRIPTION:
 *     Configuration structure for TUN device setup.
 *     Contains all necessary parameters for device initialization.
 *
 * PROPERTIES:
 *     name - Device name (may be ignored on some platforms)
 *     mtu - Maximum transmission unit
 *     addresses - IP addresses to assign
 *     routes - Routes to configure
 *     dnsServers - DNS servers to set
 *     defaultGateway - Default gateway address
 */
public struct TUNDeviceConfiguration {
    public let name: String
    public let mtu: Int
    public let addresses: [IPAddress]
    public let routes: [RouteEntry]
    public let dnsServers: [IPAddress]
    public let defaultGateway: IPAddress?
    
    public init(
        name: String = "tun0",
        mtu: Int = 1400,
        addresses: [IPAddress] = [],
        routes: [RouteEntry] = [],
        dnsServers: [IPAddress] = [],
        defaultGateway: IPAddress? = nil
    ) {
        self.name = name
        self.mtu = mtu
        self.addresses = addresses
        self.routes = routes
        self.dnsServers = dnsServers
        self.defaultGateway = defaultGateway
    }
}

/**
 * NAME: TUNDeviceStatistics
 *
 * DESCRIPTION:
 *     Statistics structure for TUN device monitoring.
 *     Provides metrics for performance monitoring and debugging.
 *
 * PROPERTIES:
 *     bytesReceived - Total bytes received
 *     bytesSent - Total bytes sent
 *     packetsReceived - Total packets received
 *     packetsSent - Total packets sent
 *     errors - Total error count
 *     dropped - Total dropped packets
 *     lastActivity - Last activity timestamp
 */
public struct TUNDeviceStatistics {
    public let bytesReceived: UInt64
    public let bytesSent: UInt64
    public let packetsReceived: UInt64
    public let packetsSent: UInt64
    public let errors: UInt64
    public let dropped: UInt64
    public let lastActivity: Date
    
    public init(
        bytesReceived: UInt64 = 0,
        bytesSent: UInt64 = 0,
        packetsReceived: UInt64 = 0,
        packetsSent: UInt64 = 0,
        errors: UInt64 = 0,
        dropped: UInt64 = 0,
        lastActivity: Date = Date()
    ) {
        self.bytesReceived = bytesReceived
        self.bytesSent = bytesSent
        self.packetsReceived = packetsReceived
        self.packetsSent = packetsSent
        self.errors = errors
        self.dropped = dropped
        self.lastActivity = lastActivity
    }
}

/**
 * NAME: IPAddress
 *
 * DESCRIPTION:
 *     IP address representation supporting both IPv4 and IPv6.
 *     Provides unified interface for IP address operations.
 *
 * PROPERTIES:
 *     rawValue - String representation of IP address
 *     isIPv4 - Whether address is IPv4
 *     isIPv6 - Whether address is IPv6
 */
public struct IPAddress: Hashable, CustomStringConvertible {
    public let rawValue: String
    
    public init(_ address: String) throws {
        guard Self.isValidIPAddress(address) else {
            throw PlatformError.configurationInvalid
        }
        self.rawValue = address
    }
    
    public var isIPv4: Bool {
        return rawValue.contains(".") && !rawValue.contains(":")
    }
    
    public var isIPv6: Bool {
        return rawValue.contains(":")
    }
    
    public var description: String {
        return rawValue
    }
    
    private static func isValidIPAddress(_ address: String) -> Bool {
        // Simple validation - could be enhanced
        return !address.isEmpty && (address.contains(".") || address.contains(":"))
    }
    
    // Common IP addresses
    public static let localhost = try! IPAddress("127.0.0.1")
    public static let any = try! IPAddress("0.0.0.0")
    public static let googleDNS = try! IPAddress("*******")
    public static let cloudflareDNS = try! IPAddress("*******")
}

/**
 * NAME: IPNetwork
 *
 * DESCRIPTION:
 *     IP network representation with address and prefix length.
 *     Used for route configuration and network operations.
 *
 * PROPERTIES:
 *     address - Network address
 *     prefixLength - Network prefix length (CIDR notation)
 */
public struct IPNetwork: Hashable, CustomStringConvertible {
    public let address: IPAddress
    public let prefixLength: Int
    
    public init(address: IPAddress, prefixLength: Int) throws {
        guard prefixLength >= 0 && prefixLength <= (address.isIPv4 ? 32 : 128) else {
            throw PlatformError.configurationInvalid
        }
        self.address = address
        self.prefixLength = prefixLength
    }
    
    public init(_ cidr: String) throws {
        let components = cidr.split(separator: "/")
        guard components.count == 2,
              let prefixLength = Int(components[1]) else {
            throw PlatformError.configurationInvalid
        }
        
        let address = try IPAddress(String(components[0]))
        try self.init(address: address, prefixLength: prefixLength)
    }
    
    public var description: String {
        return "\(address)/\(prefixLength)"
    }
    
    // Common networks
    public static let defaultRoute = try! IPNetwork("0.0.0.0/0")
    public static let localhost = try! IPNetwork("*********/8")
    public static let privateA = try! IPNetwork("10.0.0.0/8")
    public static let privateB = try! IPNetwork("**********/12")
    public static let privateC = try! IPNetwork("***********/16")
    public static let linkLocal = try! IPNetwork("***********/16")
}

/**
 * NAME: RouteEntry
 *
 * DESCRIPTION:
 *     Route entry for network routing table configuration.
 *     Defines destination network, gateway, and routing metrics.
 *
 * PROPERTIES:
 *     destination - Destination network
 *     gateway - Gateway address (nil for direct routes)
 *     metric - Route metric (priority)
 *     interface - Interface name (optional)
 */
public struct RouteEntry: Hashable, CustomStringConvertible {
    public let destination: IPNetwork
    public let gateway: IPAddress?
    public let metric: Int
    public let interface: String?
    
    public init(
        destination: IPNetwork,
        gateway: IPAddress? = nil,
        metric: Int = 0,
        interface: String? = nil
    ) {
        self.destination = destination
        self.gateway = gateway
        self.metric = metric
        self.interface = interface
    }
    
    public var description: String {
        var desc = "\(destination)"
        if let gateway = gateway {
            desc += " via \(gateway)"
        }
        if metric > 0 {
            desc += " metric \(metric)"
        }
        if let interface = interface {
            desc += " dev \(interface)"
        }
        return desc
    }
    
    public var isDefaultRoute: Bool {
        return destination.address.rawValue == "0.0.0.0" && destination.prefixLength == 0
    }
}

/**
 * NAME: TUNDeviceDelegate
 *
 * DESCRIPTION:
 *     Delegate protocol for TUN device events and notifications.
 *     Allows monitoring of device state changes and packet events.
 *
 * METHODS:
 *     deviceDidStart - Called when device starts
 *     deviceDidStop - Called when device stops
 *     deviceDidReceivePacket - Called when packet is received
 *     deviceDidEncounterError - Called when error occurs
 */
public protocol TUNDeviceDelegate: AnyObject {
    func deviceDidStart(_ device: TUNDeviceProtocol)
    func deviceDidStop(_ device: TUNDeviceProtocol)
    func deviceDidReceivePacket(_ device: TUNDeviceProtocol, packet: Data)
    func deviceDidEncounterError(_ device: TUNDeviceProtocol, error: Error)
}

// Default implementations for optional delegate methods
public extension TUNDeviceDelegate {
    func deviceDidStart(_ device: TUNDeviceProtocol) {}
    func deviceDidStop(_ device: TUNDeviceProtocol) {}
    func deviceDidReceivePacket(_ device: TUNDeviceProtocol, packet: Data) {}
    func deviceDidEncounterError(_ device: TUNDeviceProtocol, error: Error) {}
}
