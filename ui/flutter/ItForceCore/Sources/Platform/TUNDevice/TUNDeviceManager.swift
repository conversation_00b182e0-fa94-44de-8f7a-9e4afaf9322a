/**
 * FILE: TUNDeviceManager.swift
 *
 * DESCRIPTION:
 *     TUN device manager implementation for iOS/macOS NetworkExtension.
 *     Manages virtual network interface operations and packet processing.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import NetworkExtension

/**
 * NAME: TUNDeviceManager
 *
 * DESCRIPTION:
 *     Concrete implementation of TUN device management for NetworkExtension.
 *     Handles packet flow operations and network configuration.
 *
 * PROPERTIES:
 *     packetFlow - NetworkExtension packet flow
 *     configuration - Current device configuration
 *     statistics - Device statistics
 *     isRunning - Whether device is currently running
 *     delegate - Device event delegate
 *     logger - Logger instance
 */
public class TUNDeviceManager: TUNDeviceProtocol {
    private let packetFlow: NEPacketTunnelFlow
    private var configuration: TUNDeviceConfiguration?
    private var statistics: TUNDeviceStatistics
    private var isRunning: Bool = false
    private let logger: LoggerProtocol

    // MARK: - High-Performance Atomic Statistics
    // Dedicated atomic statistics manager for optimal performance
    private let atomicStats = AtomicStatisticsManager()

    public weak var delegate: TUNDeviceDelegate?
    
    // MARK: - TUNDeviceProtocol Properties
    
    public var name: String {
        return configuration?.name ?? "tun0"
    }
    
    public var mtu: Int {
        return configuration?.mtu ?? 1400
    }
    
    public var isUp: Bool {
        return isRunning
    }
    
    public var addresses: [IPAddress] {
        return configuration?.addresses ?? []
    }
    
    // MARK: - Initialization
    
    public init(packetFlow: NEPacketTunnelFlow, logger: LoggerProtocol) {
        self.packetFlow = packetFlow
        self.logger = logger
        self.statistics = TUNDeviceStatistics()
    }
    
    // MARK: - Device Lifecycle
    
    /**
     * NAME: configure
     *
     * DESCRIPTION:
     *     Configures TUN device with provided settings.
     *     Stores configuration for later use during start operation.
     *
     * PARAMETERS:
     *     config - Device configuration
     *
     * THROWS:
     *     PlatformError.tunDeviceConfigurationFailed - If configuration is invalid
     */
    public func configure(with config: TUNDeviceConfiguration) async throws {
        logger.info("Configuring TUN device", metadata: [
            "name": config.name,
            "mtu": "\(config.mtu)",
            "addresses": "\(config.addresses.count)"
        ])
        
        // Validate configuration
        guard config.mtu > 0 && config.mtu <= 9000 else {
            throw PlatformError.tunDeviceConfigurationFailed
        }
        
        guard !config.addresses.isEmpty else {
            throw PlatformError.tunDeviceConfigurationFailed
        }
        
        self.configuration = config
        logger.info("TUN device configured successfully")
    }
    
    /**
     * NAME: start
     *
     * DESCRIPTION:
     *     Starts TUN device operations.
     *     Begins packet processing and enables network interface.
     *
     * THROWS:
     *     PlatformError.tunDeviceStartFailed - If device start fails
     *     PlatformError.tunDeviceConfigurationFailed - If device not configured
     */
    public func start() async throws {
        guard configuration != nil else {
            throw PlatformError.tunDeviceConfigurationFailed
        }
        
        guard !isRunning else {
            logger.warning("TUN device already running")
            return
        }
        
        logger.info("Starting TUN device")
        
        // Reset statistics
        statistics = TUNDeviceStatistics()
        atomicStats.resetStatistics()

        // Start periodic statistics sync
        atomicStats.startPeriodicSync()

        // Mark as running
        isRunning = true

        // Notify delegate
        delegate?.deviceDidStart(self)

        logger.info("TUN device started successfully")
    }
    
    /**
     * NAME: stop
     *
     * DESCRIPTION:
     *     Stops TUN device operations.
     *     Halts packet processing and disables network interface.
     */
    public func stop() async throws {
        guard isRunning else {
            logger.warning("TUN device not running")
            return
        }
        
        logger.info("Stopping TUN device")

        // Stop periodic statistics sync
        atomicStats.stopPeriodicSync()

        isRunning = false

        // Notify delegate
        delegate?.deviceDidStop(self)

        logger.info("TUN device stopped successfully")
    }
    
    // MARK: - Packet I/O
    
    /**
     * NAME: readPacket
     *
     * DESCRIPTION:
     *     Reads a single packet from the TUN device.
     *     Blocks until packet is available or error occurs.
     *
     * RETURNS:
     *     Data - Packet data
     *
     * THROWS:
     *     PlatformError.packetReadFailed - If packet reading fails
     *     PlatformError.tunDeviceNotAvailable - If device not running
     */
    public func readPacket() async throws -> Data {
        guard isRunning else {
            throw PlatformError.tunDeviceNotAvailable
        }
        
        let packets = try await readPackets(maxCount: 1)
        guard let packet = packets.first else {
            throw PlatformError.packetReadFailed
        }
        
        return packet
    }
    
    /**
     * NAME: writePacket
     *
     * DESCRIPTION:
     *     Writes a single packet to the TUN device.
     *     Sends packet through NetworkExtension packet flow.
     *
     * PARAMETERS:
     *     data - Packet data to write
     *
     * THROWS:
     *     PlatformError.packetWriteFailed - If packet writing fails
     *     PlatformError.tunDeviceNotAvailable - If device not running
     */
    public func writePacket(_ data: Data) async throws {
        try await writePackets([data])
    }
    
    /**
     * NAME: readPackets
     *
     * DESCRIPTION:
     *     Reads multiple packets from the TUN device.
     *     Returns up to maxCount packets or all available packets.
     *
     * PARAMETERS:
     *     maxCount - Maximum number of packets to read
     *
     * RETURNS:
     *     [Data] - Array of packet data
     *
     * THROWS:
     *     PlatformError.packetReadFailed - If packet reading fails
     *     PlatformError.tunDeviceNotAvailable - If device not running
     */
    public func readPackets(maxCount: Int) async throws -> [Data] {
        guard isRunning else {
            throw PlatformError.tunDeviceNotAvailable
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            packetFlow.readPackets { [weak self] packets, protocols in
                guard let self = self else {
                    continuation.resume(throwing: PlatformError.tunDeviceNotAvailable)
                    return
                }
                
                // Update statistics with atomic operations (high performance)
                let packetCount = UInt64(packets.count)
                let totalBytes = packets.reduce(0) { $0 + UInt64($1.count) }

                self.atomicStats.updateTUNReceiveStatistics(
                    packets: packetCount,
                    bytes: totalBytes
                )
                
                // Notify delegate for each packet
                for packet in packets {
                    self.delegate?.deviceDidReceivePacket(self, packet: packet)
                }
                
                // Return requested number of packets
                let resultPackets = Array(packets.prefix(maxCount))
                continuation.resume(returning: resultPackets)
            }
        }
    }
    
    /**
     * NAME: writePackets
     *
     * DESCRIPTION:
     *     Writes multiple packets to the TUN device.
     *     Sends packets through NetworkExtension packet flow.
     *
     * PARAMETERS:
     *     packets - Array of packet data to write
     *
     * THROWS:
     *     PlatformError.packetWriteFailed - If packet writing fails
     *     PlatformError.tunDeviceNotAvailable - If device not running
     */
    public func writePackets(_ packets: [Data]) async throws {
        guard isRunning else {
            throw PlatformError.tunDeviceNotAvailable
        }
        
        guard !packets.isEmpty else {
            return
        }
        
        // Create protocol numbers for packets (IPv4 = 2, IPv6 = 30)
        let protocols = packets.map { packet -> NSNumber in
            // Simple IPv4/IPv6 detection based on first nibble
            guard let firstByte = packet.first else { return NSNumber(value: AF_INET) }
            let version = (firstByte >> 4) & 0x0F
            return NSNumber(value: version == 6 ? AF_INET6 : AF_INET)
        }
        
        let success = packetFlow.writePackets(packets, withProtocols: protocols)
        if !success {
            delegate?.deviceDidEncounterError(self, error: PlatformError.packetWriteFailed)
            throw PlatformError.packetWriteFailed
        }
        
        // Update statistics with atomic operations (high performance)
        let packetCount = UInt64(packets.count)
        let totalBytes = packets.reduce(0) { $0 + UInt64($1.count) }

        atomicStats.updateTUNSendStatistics(
            packets: packetCount,
            bytes: totalBytes
        )
    }
    
    // MARK: - Network Configuration
    
    /**
     * NAME: setMTU
     *
     * DESCRIPTION:
     *     Sets maximum transmission unit for the device.
     *     Updates configuration and applies to active device.
     *
     * PARAMETERS:
     *     mtu - Maximum transmission unit
     *
     * THROWS:
     *     PlatformError.tunDeviceConfigurationFailed - If MTU is invalid
     */
    public func setMTU(_ mtu: Int) async throws {
        guard mtu > 0 && mtu <= 9000 else {
            throw PlatformError.tunDeviceConfigurationFailed
        }
        
        guard let config = configuration else {
            throw PlatformError.tunDeviceConfigurationFailed
        }

        let updatedConfig = TUNDeviceConfiguration(
            name: config.name,
            mtu: mtu,
            addresses: config.addresses,
            routes: config.routes,
            dnsServers: config.dnsServers,
            defaultGateway: config.defaultGateway
        )
        
        self.configuration = updatedConfig
        logger.info("MTU updated", metadata: ["mtu": "\(mtu)"])

        // Note: Actual MTU change requires NetworkExtension settings update
        if isRunning {
            logger.info("MTU updated during runtime - NetworkExtension settings update required")
        }
    }
    
    /**
     * NAME: setAddresses
     *
     * DESCRIPTION:
     *     Sets IP addresses for the device.
     *     Updates configuration with new address list.
     *
     * PARAMETERS:
     *     addresses - Array of IP addresses
     *
     * THROWS:
     *     PlatformError.tunDeviceConfigurationFailed - If addresses are invalid
     */
    public func setAddresses(_ addresses: [IPAddress]) async throws {
        guard !addresses.isEmpty else {
            throw PlatformError.tunDeviceConfigurationFailed
        }
        
        guard let config = configuration else {
            throw PlatformError.tunDeviceConfigurationFailed
        }

        let updatedConfig = TUNDeviceConfiguration(
            name: config.name,
            mtu: config.mtu,
            addresses: addresses,
            routes: config.routes,
            dnsServers: config.dnsServers,
            defaultGateway: config.defaultGateway
        )
        
        self.configuration = updatedConfig
        logger.info("Addresses updated", metadata: ["count": "\(addresses.count)"])
    }

    /**
     * NAME: addRoute
     *
     * DESCRIPTION:
     *     Adds a network route to the routing table.
     *     Updates configuration for next NetworkExtension settings application.
     *
     * PARAMETERS:
     *     destination - Destination network
     *     gateway - Gateway address (optional)
     *     metric - Route metric
     *
     * THROWS:
     *     PlatformError.routeAddFailed - If route addition fails
     */
    public func addRoute(destination: IPNetwork, gateway: IPAddress?, metric: Int) async throws {
        logger.info("Adding route", metadata: [
            "destination": destination.description,
            "gateway": gateway?.description ?? "direct",
            "metric": "\(metric)"
        ])

        guard let config = configuration else {
            throw PlatformError.routeAddFailed("Device not configured")
        }

        // Create new route entry
        let newRoute = RouteEntry(
            destination: destination,
            gateway: gateway,
            metric: metric
        )

        // Add to configuration routes
        var updatedRoutes = config.routes
        updatedRoutes.append(newRoute)

        // Update configuration
        let updatedConfig = TUNDeviceConfiguration(
            name: config.name,
            mtu: config.mtu,
            addresses: config.addresses,
            routes: updatedRoutes,
            dnsServers: config.dnsServers,
            defaultGateway: config.defaultGateway
        )

        self.configuration = updatedConfig
        logger.info("Route added to configuration successfully")
    }

    /**
     * NAME: deleteRoute
     *
     * DESCRIPTION:
     *     Deletes a network route from the routing table.
     *     Updates configuration for next NetworkExtension settings application.
     *
     * PARAMETERS:
     *     destination - Destination network
     *     gateway - Gateway address (optional)
     *
     * THROWS:
     *     PlatformError.routeDeleteFailed - If route deletion fails
     */
    public func deleteRoute(destination: IPNetwork, gateway: IPAddress?) async throws {
        logger.info("Deleting route", metadata: [
            "destination": destination.description,
            "gateway": gateway?.description ?? "direct"
        ])

        guard let config = configuration else {
            throw PlatformError.routeDeleteFailed("Device not configured")
        }

        // Find and remove matching route
        let originalCount = config.routes.count
        let updatedRoutes = config.routes.filter { route in
            // Match by destination and gateway
            if route.destination.description != destination.description {
                return true // Keep this route
            }

            // Check gateway match
            if let routeGateway = route.gateway, let targetGateway = gateway {
                return routeGateway.description != targetGateway.description // Keep if different gateway
            } else if route.gateway == nil && gateway == nil {
                return false // Remove this route (both gateways are nil)
            } else {
                return true // Keep if one has gateway and other doesn't
            }
        }

        guard updatedRoutes.count < originalCount else {
            throw PlatformError.routeDeleteFailed("Route not found")
        }

        // Update configuration
        let updatedConfig = TUNDeviceConfiguration(
            name: config.name,
            mtu: config.mtu,
            addresses: config.addresses,
            routes: updatedRoutes,
            dnsServers: config.dnsServers,
            defaultGateway: config.defaultGateway
        )

        self.configuration = updatedConfig
        logger.info("Route deleted from configuration successfully")
    }

    /**
     * NAME: setDNS
     *
     * DESCRIPTION:
     *     Sets DNS servers for the device.
     *     Updates configuration with new DNS server list.
     *
     * PARAMETERS:
     *     servers - Array of DNS server addresses
     *
     * THROWS:
     *     PlatformError.dnsConfigurationFailed - If DNS configuration fails
     */
    public func setDNS(servers: [IPAddress]) async throws {
        guard !servers.isEmpty else {
            throw PlatformError.dnsConfigurationFailed
        }

        guard let config = configuration else {
            throw PlatformError.tunDeviceConfigurationFailed
        }

        let updatedConfig = TUNDeviceConfiguration(
            name: config.name,
            mtu: config.mtu,
            addresses: config.addresses,
            routes: config.routes,
            dnsServers: servers,
            defaultGateway: config.defaultGateway
        )

        self.configuration = updatedConfig
        logger.info("DNS servers updated", metadata: ["count": "\(servers.count)"])
    }

    /**
     * NAME: setDefaultGateway
     *
     * DESCRIPTION:
     *     Sets default gateway for the device.
     *     Updates configuration with new gateway address.
     *
     * PARAMETERS:
     *     gateway - Default gateway address
     *
     * THROWS:
     *     PlatformError.routeConfigurationFailed - If gateway configuration fails
     */
    public func setDefaultGateway(_ gateway: IPAddress) async throws {
        guard let config = configuration else {
            throw PlatformError.tunDeviceConfigurationFailed
        }

        let updatedConfig = TUNDeviceConfiguration(
            name: config.name,
            mtu: config.mtu,
            addresses: config.addresses,
            routes: config.routes,
            dnsServers: config.dnsServers,
            defaultGateway: gateway
        )

        self.configuration = updatedConfig
        logger.info("Default gateway updated", metadata: ["gateway": gateway.description])
    }

    // MARK: - Status and Monitoring

    /**
     * NAME: getStatistics
     *
     * DESCRIPTION:
     *     Returns current device statistics from atomic counters.
     *     Provides real-time metrics for monitoring and debugging.
     *
     * RETURNS:
     *     TUNDeviceStatistics - Current device statistics
     */
    public func getStatistics() async -> TUNDeviceStatistics {
        let atomicSnapshot = atomicStats.getCurrentStatistics()
        return TUNDeviceStatistics(
            bytesReceived: atomicSnapshot.tunBytesReceived,
            bytesSent: atomicSnapshot.tunBytesSent,
            packetsReceived: atomicSnapshot.tunPacketsReceived,
            packetsSent: atomicSnapshot.tunPacketsSent,
            errors: atomicSnapshot.tunErrors,
            dropped: atomicSnapshot.tunDropped,
            lastActivity: atomicSnapshot.timestamp
        )
    }

    /**
     * NAME: flushConfiguration
     *
     * DESCRIPTION:
     *     Flushes all device configuration.
     *     Resets device to unconfigured state.
     */
    public func flushConfiguration() async throws {
        logger.info("Flushing TUN device configuration")

        configuration = nil
        statistics = TUNDeviceStatistics()

        if isRunning {
            try await stop()
        }

        logger.info("TUN device configuration flushed")
    }

    // MARK: - Private Methods

    /**
     * NAME: updateStatistics (Legacy)
     *
     * DESCRIPTION:
     *     Legacy statistics update method - kept for compatibility.
     *     New code should use atomic update methods for better performance.
     *
     * PARAMETERS:
     *     packetsReceived - Packets received count (optional)
     *     packetsSent - Packets sent count (optional)
     *     bytesReceived - Bytes received count (optional)
     *     bytesSent - Bytes sent count (optional)
     *     errors - Error count (optional)
     *     dropped - Dropped packets count (optional)
     */
    private func updateStatistics(
        packetsReceived: UInt64 = 0,
        packetsSent: UInt64 = 0,
        bytesReceived: UInt64 = 0,
        bytesSent: UInt64 = 0,
        errors: UInt64 = 0,
        dropped: UInt64 = 0
    ) {
        statistics = TUNDeviceStatistics(
            bytesReceived: statistics.bytesReceived + bytesReceived,
            bytesSent: statistics.bytesSent + bytesSent,
            packetsReceived: statistics.packetsReceived + packetsReceived,
            packetsSent: statistics.packetsSent + packetsSent,
            errors: statistics.errors + errors,
            dropped: statistics.dropped + dropped,
            lastActivity: Date()
        )
    }
}
