/**
 * FILE: PacketTunnelProvider.swift
 *
 * DESCRIPTION:
 *     Core NetworkExtension PacketTunnelProvider implementation for iOS/macOS VPN functionality.
 *     Integrates SDWAN protocol with system VPN framework, handles tunnel lifecycle management,
 *     and provides seamless integration with ConnectionManager and TUN device management.
 *
 *     Key Features:
 *     - NetworkExtension integration with iOS/macOS system VPN
 *     - SDWAN protocol connection management
 *     - Automatic packet processing via ConnectionManager
 *     - Runtime configuration updates (DNS, MTU)
 *     - Server switching support (requires tunnel restart)
 *     - Comprehensive error handling and recovery
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create NetworkExtension PacketTunnelProvider with SDWAN protocol integration
 */

import NetworkExtension
import Foundation
import CoreFoundation

/**
 * NAME: VPNPacketTunnelProvider
 *
 * DESCRIPTION:
 *     Main PacketTunnelProvider implementation that integrates with iOS/macOS system VPN.
 *     Manages tunnel establishment, SDWAN protocol processing, and system integration.
 *     Provides bridge between NetworkExtension framework and ItForceCore VPN services.
 *
 * PROPERTIES:
 *     connectionManager - SDWAN protocol connection management
 *     logger - Structured logging system
 *     isStarted - Tunnel running state
 *     configuration - Current tunnel configuration
 *
 * NOTE: Simplified architecture - removed RouteManager and DNSManager
 *       NetworkExtension APIs (NEIPv4Settings, NEDNSSettings) handle all routing and DNS configuration
 */
open class VPNPacketTunnelProvider: NEPacketTunnelProvider {
    
    // MARK: - Properties

    private var connectionManager: ConnectionManager?
    private let logger: LoggerProtocol

    private var isStarted: Bool = false
    private var configuration: VPNTunnelConfiguration?
    private var packetProcessingTask: Task<Void, Never>?
    private var isReconnecting: Bool = false

    // 重连重试机制配置
    private var reconnectionRetryCount: Int = 0
    private let maxReconnectionRetries: Int = 2
    private let reconnectionRetryDelay: TimeInterval = 1.0 // 1 second

    // 防止多个并发重连任务
    private var pendingReconnectionTask: Task<Void, Never>?



    // MARK: - Debug Logging

    /**
     * NAME: debugLog
     *
     * DESCRIPTION:
     *     Outputs debug messages that are visible in system logs and Console.app.
     *     Uses debugLog for VPN extension environment compatibility.
     *
     * PARAMETERS:
     *     message - Debug message to log
     */
    private func debugLog(_ message: String) {
        NSLog("%@", message)
        //logger.info("🔍 [Reconnect_DEBUG] \(message)")
    }
    
    // MARK: - Initialization
    
    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Initializes PacketTunnelProvider with required dependencies.
     *     Sets up logging and prepares for tunnel operations.
     */
    public override init() {
        // Initialize logger for NetworkExtension environment
        self.logger = OSLogLogger.createNetworkExtensionLogger(category: "PacketTunnelProvider")

        super.init()

        //debugLog("VPNPacketTunnelProvider initialized")
        logger.info("VPNPacketTunnelProvider initialized")
    }
    
    // MARK: - NetworkExtension Lifecycle
    
    /**
     * NAME: startTunnel
     *
     * DESCRIPTION:
     *     Starts the VPN tunnel with provided configuration options.
     *     Configures network settings, establishes SDWAN connection, and begins packet processing.
     *     Equivalent to Go backend's main connection establishment flow.
     *
     * PARAMETERS:
     *     options - Tunnel configuration options from system/app
     *     completionHandler - Completion callback with result
     */
    public override func startTunnel(options: [String : NSObject]?,
                                   completionHandler: @escaping (Error?) -> Void) {
        // Use debugLog for immediate visibility in system logs
        //debugLog("startTunnel called with options: \(options?.description ?? "nil")")
        logger.info("Starting VPN tunnel", metadata: ["options_count": "\(options?.count ?? 0)"])

        Task {
            do {
                debugLog("🔍 [w] Step 1: Parsing configuration from options")
                logger.info("Step 1: Parsing configuration from options")
                // Parse configuration from options
                let config = try parseConfiguration(from: options)
                self.configuration = config
                debugLog("🔍 [Reconnect_DEBUG] Step 1 completed: server=\(config.serverAddress), port=\(config.serverPort), username=\(config.username)")
                logger.info("Step 1 completed: Configuration parsed successfully", metadata: [
                    "server": config.serverAddress,
                    "port": "\(config.serverPort)",
                    "username": config.username
                ])

                debugLog("🔍 [Reconnect_DEBUG] Step 2: Initializing core components")
                logger.info("Step 2: Initializing core components")
                // Initialize core components
                try await initializeComponents(config: config)
                debugLog("🔍 [Reconnect_DEBUG] Step 2 completed: Core components initialized")
                logger.info("Step 2 completed: Core components initialized")

                debugLog("🔍 [Reconnect_DEBUG] Step 3: Starting SDWAN connection and authentication")
                logger.info("Step 3: Starting SDWAN connection and authentication")
                // Start SDWAN connection (includes authentication and data connection)
                let serverConfig = try await startSDWANConnection(config: config)
                debugLog("🔍 [Reconnect_DEBUG] Step 3 completed: SDWAN connection established with authentication")
                logger.info("Step 3 completed: SDWAN connection established with authentication")

                // //debugLog("Step 4: Configuring VPN network settings with server parameters") // Debug debugLog commented for production
                logger.info("Step 4: Configuring VPN network settings with server parameters")
                // Configure network settings using server configuration from OpenAck response
                let networkSettings = try createNetworkSettings(for: config, serverConfig: serverConfig)
                try await setTunnelNetworkSettings(networkSettings)
                // //debugLog("Step 4 completed: VPN network settings configured") // Debug debugLog commented for production
                logger.info("Step 4 completed: VPN network settings configured")

                isStarted = true
                // //debugLog("VPN tunnel started successfully - all steps completed") // Debug debugLog commented for production
                logger.info("VPN tunnel started successfully - all steps completed")

                completionHandler(nil)

            } catch {
                // //debugLog("Failed to start VPN tunnel: \(error.localizedDescription)") // Debug debugLog commented for production
                logger.error("Failed to start VPN tunnel", metadata: [
                    "error": "\(error)",
                    "error_type": "\(type(of: error))",
                    "localized_description": error.localizedDescription
                ])

                // 通知主应用连接失败
                let serverAddress = configuration?.serverAddress ?? "unknown"
                sendConnectionFailureToMainApp(errorMessage: error.localizedDescription, serverAddress: serverAddress)

                await cleanupOnError()
                completionHandler(error)
            }
        }
    }
    
    /**
     * NAME: stopTunnel
     *
     * DESCRIPTION:
     *     Stops the VPN tunnel and cleans up resources.
     *     Disconnects SDWAN connection and restores network configuration.
     *
     * PARAMETERS:
     *     reason - Reason for tunnel stop
     *     completionHandler - Completion callback
     */
    public override func stopTunnel(with reason: NEProviderStopReason,
                                  completionHandler: @escaping () -> Void) {
        logger.info("Stopping VPN tunnel", metadata: ["reason": "\(reason.rawValue)"])

        Task {
            await cleanupTunnel(reason: reason)
            completionHandler()
        }
    }

    // MARK: - Sleep/Wake Lifecycle Management

    /**
     * NAME: sleep
     *
     * DESCRIPTION:
     *     Handles device entering sleep mode.
     *     Actively cancels tunnel to ensure proper sleep behavior and resource cleanup.
     *     Called by iOS when device is about to sleep.
     *
     * PARAMETERS:
     *     completionHandler - Completion callback to signal sleep preparation is complete
     */
    public override func sleep(completionHandler: @escaping () -> Void) {
        logger.info("VPN Extension entering sleep mode - cancelling tunnel")

        Task {
            // Notify connection manager about sleep state
            await connectionManager?.handleDeviceSleep()

            // Actively cancel tunnel to ensure proper sleep behavior
            // This works in conjunction with disconnectOnSleep = true in configuration
            let sleepError = NEVPNError(.connectionFailed)
            logger.info("Cancelling tunnel for device sleep")
            cancelTunnelWithError(sleepError)

            // Signal that sleep preparation is complete
            completionHandler()

            logger.info("VPN Extension sleep preparation completed - tunnel cancelled")
        }
    }

    /**
     * NAME: wake
     *
     * DESCRIPTION:
     *     Handles device waking up from sleep mode.
     *     Triggers immediate reconnection for faster recovery without heartbeat verification.
     *     Called by iOS when device wakes up.
     */
    public override func wake() {
        // //debugLog("Device waking up - triggering direct reconnection") // Debug debugLog commented for production
        logger.info("VPN Extension waking up from sleep")

        Task {
            // Notify connection manager about wake state - triggers direct reconnection
            await connectionManager?.handleDeviceWake()

            logger.info("VPN Extension wake restoration completed")
        }
    }

    /**
     * NAME: handleAppMessage
     *
     * DESCRIPTION:
     *     Handles messages from the main app (server switching, configuration updates).
     *     Supports runtime configuration changes without tunnel restart.
     *
     * PARAMETERS:
     *     messageData - Message data from app
     *     completionHandler - Response callback
     */
    public override func handleAppMessage(_ messageData: Data, 
                                        completionHandler: ((Data?) -> Void)?) {
        // logger.debug("Received app message", metadata: ["size": "\(messageData.count)"]) // Debug log commented for production
        
        Task {
            do {
                let response = try await processAppMessage(messageData)
                completionHandler?(response)
            } catch {
                logger.error("Failed to process app message", metadata: ["error": "\(error)"])
                completionHandler?(nil)
            }
        }
    }
    
    // MARK: - Reconnection Handling

    /**
     * NAME: setupReconnectionCallback
     *
     * DESCRIPTION:
     *     Sets up the reconnection callback for ConnectionManager.
     *     This method handles Swift 6 async closure requirements properly.
     */
    private func setupReconnectionCallback() async {
        guard let connectionManager = connectionManager else {
            debugLog("🔍 [Reconnect_DEBUG] No ConnectionManager available for callback setup")
            return
        }

        // Create the async callback function
        let reconnectionCallback: (ReconnectReason) async -> Void = { [weak self] reason in
            await self?.handleReconnectRequest(reason: reason)
        }

        await connectionManager.setReconnectionRequestCallback(reconnectionCallback)
        debugLog("🔍 [Reconnect_DEBUG] Reconnection callback successfully set")
    }

    /**
     * NAME: handleReconnectRequest
     *
     * DESCRIPTION:
     *     Handles reconnection requests directly within NetworkExtension.
     *     Performs disconnect+connect cycle with retry mechanism.
     *     Fixed concurrent reconnection handling and state management.
     *     Enhanced with proper tunnel cancellation and timeout handling for all steps.
     *
     * PARAMETERS:
     *     reason - Reason for reconnection request
     */
    private func handleReconnectRequest(reason: ReconnectReason) async {
        debugLog("🔍 [Reconnect_DEBUG] NetworkExtension handling reconnection request")
        debugLog("🔍 [Reconnect_DEBUG] - Reason: \(reason.rawValue)")
        debugLog("🔍 [Reconnect_DEBUG] - Tunnel started: \(isStarted)")
        debugLog("🔍 [Reconnect_DEBUG] - Is reconnecting: \(isReconnecting)")

        logger.info("NetworkExtension handling reconnection request", metadata: [
            "reason": reason.rawValue,
            "tunnel_started": "\(isStarted)",
            "is_reconnecting": "\(isReconnecting)"
        ])

        guard isStarted, let connectionManager = connectionManager, let config = configuration else {
            debugLog("🔍 [Reconnect_DEBUG] Cannot reconnect - tunnel not properly initialized")
            logger.warning("Cannot reconnect - tunnel not properly initialized")
            return
        }

        // Prevent concurrent reconnections with atomic check-and-set
        guard !isReconnecting else {
            debugLog("🔍 [Reconnect_DEBUG] Reconnection already in progress, ignoring request")
            logger.info("Reconnection already in progress, ignoring request", metadata: [
                "reason": reason.rawValue
            ])
            return
        }

        // Cancel any pending reconnection task
        pendingReconnectionTask?.cancel()

        // Set reconnecting flag with proper cleanup guarantee
        debugLog("🔍 [Reconnect_DEBUG] Setting reconnecting flag to true")
        isReconnecting = true

        // Ensure reconnecting flag is always reset, even on early returns or exceptions
        defer {
            debugLog("🔍 [Reconnect_DEBUG] Resetting reconnecting flag to false (defer)")
            isReconnecting = false
            pendingReconnectionTask = nil
        }

        // Reset retry count for new reconnection attempt
        reconnectionRetryCount = 0

        // Step 0: Cancel current tunnel and update state (only once before retry loop)
        debugLog("🔍 [Reconnect_DEBUG] Step 0: Cancelling current tunnel and updating state")
        logger.info("Step 0: Cancelling current tunnel and updating state")

        // 0.1: Cancel current tunnel with error to properly restore user network
        let reconnectError = NEVPNError(.connectionFailed)
        logger.info("Cancelling tunnel for reconnection to restore user network")
        //cancelTunnelWithError(reconnectError)

        // 0.2: Update connection state to connecting in app group (reconnection in progress)
        updateConnectionStateToAppGroup(state: "connecting", reason: "reconnecting")

        // Retry loop with exponential backoff
        while reconnectionRetryCount < maxReconnectionRetries {
            do {
                debugLog("🔍 [Reconnect_DEBUG] Starting NetworkExtension reconnection process (attempt \(reconnectionRetryCount + 1)/\(maxReconnectionRetries))")
                logger.info("Starting NetworkExtension reconnection process", metadata: [
                    "attempt": "\(reconnectionRetryCount + 1)",
                    "max_attempts": "\(maxReconnectionRetries)"
                ])

                // Step 1: Disconnect current connection with timeout
                debugLog("🔍 [Reconnect_DEBUG] Step 1: Disconnecting current connection")
                logger.info("Step 1: Disconnecting current connection")

                // 1.1: Disconnect with timeout to prevent hanging
                try await withThrowingTaskGroup(of: Void.self) { group in
                    // Add disconnect task
                    group.addTask { [weak self] in
                        //self?.debugLog("🔍 [Reconnect_DEBUG] Step 1.1: Starting disconnect operation")
                        try await connectionManager.disconnect()
                        //self?.debugLog("🔍 [Reconnect_DEBUG] Step 1.1: Disconnect operation completed")
                    }

                    // Add timeout task
                    group.addTask { [weak self] in
                        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second timeout
                        //self?.debugLog("🔍 [Reconnect_DEBUG] Step 1.1: Disconnect timeout reached")
                        throw NSError(domain: "VPNReconnection", code: -1, userInfo: [NSLocalizedDescriptionKey: "Disconnect timeout"])
                    }

                    // Wait for first task to complete (either disconnect or timeout)
                    try await group.next()
                    group.cancelAll()
                }

                debugLog("🔍 [Reconnect_DEBUG] Step 1: Disconnect completed successfully")

                // Step 2: Wait briefly for cleanup and ensure clean state
                debugLog("🔍 [Reconnect_DEBUG] Step 2: Waiting for cleanup (1 second)")
                //try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

                // Note: setTunnelNetworkSettings(nil) is not needed here because:
                // 1. Apple's NetworkExtension automatically replaces old settings when new ones are applied
                // 2. Calling setTunnelNetworkSettings(nil) would completely stop the tunnel
                // 3. Our disconnect() already cleaned up the UDP connection and internal state

                // Step 3: Re-establish SDWAN connection (since tunnel was cancelled)
                debugLog("🔍 [Reconnect_DEBUG] Step 3: Re-establishing SDWAN connection to \(config.serverAddress):\(config.serverPort)")
                logger.info("Step 3: Re-establishing SDWAN connection with timeout")

                // Step 3: Re-establish SDWAN connection with timeout (since tunnel was cancelled)
                let serverConfig = try await withThrowingTaskGroup(of: [String: Any].self) { group in
                    // Add SDWAN connection task
                    group.addTask { [weak self] in
                        //self?.debugLog("🔍 [Reconnect_DEBUG] Step 3.1: Starting SDWAN connection")
                        let config = try await self?.startSDWANConnection(config: config)
                        //self?.debugLog("🔍 [Reconnect_DEBUG] Step 3.1: SDWAN connection completed")
                        return config ?? [:]
                    }

                    // Add timeout task
                    group.addTask { [weak self] in
                        try await Task.sleep(nanoseconds: 5_000_000_000) // 6 second timeout for connection
                        //self?.debugLog("🔍 [Reconnect_DEBUG] Step 3.1: SDWAN connection timeout reached")
                        throw NSError(domain: "VPNReconnection", code: -2, userInfo: [NSLocalizedDescriptionKey: "SDWAN connection timeout"])
                    }

                    // Wait for first task to complete (either connect or timeout)
                    let result = try await group.next()!
                    group.cancelAll()
                    return result
                }

                // Step 4: Re-establish tunnel network settings with timeout (since tunnel was cancelled)
                debugLog("🔍 [Reconnect_DEBUG] Step 4: Re-establishing tunnel network settings")
                logger.info("Step 4: Re-establishing tunnel network settings with timeout")

                // Create new network settings for the re-established tunnel
                let networkSettings: NEPacketTunnelNetworkSettings = try createNetworkSettings(for: config, serverConfig: serverConfig)

                try await withThrowingTaskGroup(of: Void.self) { group in
                    // Add tunnel network settings task
                    group.addTask { [weak self] in
                        //self?.debugLog("🔍 [Reconnect_DEBUG] Step 4.1: Starting tunnel network settings")
                        try await self?.setTunnelNetworkSettings(networkSettings)
                        //self?.debugLog("🔍 [Reconnect_DEBUG] Step 4.1: Tunnel network settings completed")
                    }

                    // Add timeout task
                    group.addTask { [weak self] in
                        try await Task.sleep(nanoseconds: 3_000_000_000) // 5 second timeout for tunnel settings
                        //self?.debugLog("🔍 [Reconnect_DEBUG] Step 4.1: Tunnel settings timeout reached")
                        throw NSError(domain: "VPNReconnection", code: -3, userInfo: [NSLocalizedDescriptionKey: "Tunnel settings timeout"])
                    }

                    // Wait for first task to complete (either configuration or timeout)
                    try await group.next()
                    group.cancelAll()
                }

                debugLog("🔍 [Reconnect_DEBUG] Step 4 completed: Tunnel network settings re-established")
                logger.info("Step 4 completed: Tunnel network settings re-established")

                // Step 5: Update connection state to connected and notify main app
                debugLog("🔍 [Reconnect_DEBUG] Step 5: Updating connection state and notifying main app")
                updateConnectionStateToAppGroup(state: "connected", reason: "reconnection_successful")
                sendReconnectionSuccessToMainApp()
                debugLog("🔍 [Reconnect_DEBUG] Step 5 completed: Connection state updated and main app notified")

                debugLog("🔍 [Reconnect_DEBUG] NetworkExtension reconnection completed successfully")
                logger.info("NetworkExtension reconnection completed successfully")

                // Success - break out of retry loop
                break

            } catch {
                reconnectionRetryCount += 1

                debugLog("🔍 [Reconnect_DEBUG] NetworkExtension reconnection failed (attempt \(reconnectionRetryCount)/\(maxReconnectionRetries))")
                debugLog("🔍 [Reconnect_DEBUG] - Reason: \(reason.rawValue)")
                debugLog("🔍 [Reconnect_DEBUG] - Error: \(error.localizedDescription)")

                logger.error("NetworkExtension reconnection failed", metadata: [
                    "reason": reason.rawValue,
                    "error": error.localizedDescription,
                    "attempt": "\(reconnectionRetryCount)",
                    "max_attempts": "\(maxReconnectionRetries)"
                ])

                // Check if we should retry
                if reconnectionRetryCount < maxReconnectionRetries {
                    let delay = reconnectionRetryDelay * Double(reconnectionRetryCount) // Exponential backoff
                    debugLog("🔍 [Reconnect_DEBUG] Retrying in \(delay) seconds...")
                    logger.info("Retrying reconnection", metadata: [
                        "delay_seconds": "\(delay)",
                        "next_attempt": "\(reconnectionRetryCount + 1)"
                    ])

                    try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                } else {
                    // All retries exhausted
                    debugLog("🔍 [Reconnect_DEBUG] All reconnection attempts exhausted, stopping tunnel")
                    logger.error("All reconnection attempts exhausted, stopping tunnel")

                    // 通知主应用重连失败
                    let serverAddress = config.serverAddress
                    sendReconnectionFailureToMainApp(errorMessage: "All reconnection attempts exhausted", serverAddress: serverAddress)
                    cancelTunnelWithError(reconnectError)
                    break
                }
            }
        }

        debugLog("🔍 [Reconnect_DEBUG] Reconnection process completed")
    }

    // MARK: - Component Initialization
    
    /**
     * NAME: initializeComponents
     *
     * DESCRIPTION:
     *     Initializes core VPN components with configuration.
     *     Simplified to only setup ConnectionManager - NetworkExtension handles routing/DNS directly.
     *
     * PARAMETERS:
     *     config - Tunnel configuration
     *
     * THROWS:
     *     PlatformError - If component initialization fails
     */
    private func initializeComponents(config: VPNTunnelConfiguration) async throws {
        logger.info("Initializing VPN components (simplified architecture)")

        // Initialize ServerManager for ConnectionManager
        let serverManager = ServerManager(logger: logger)

        // Initialize ConnectionManager with SDWAN protocol
        let connectionConfig = ConnectionConfiguration(
            serverAddress: config.serverAddress,
            serverPort: config.serverPort,
            username: config.username,
            password: config.password,
            mtu: config.mtu,
            encryption: EncryptionMethod(rawValue: config.encryptionMethod) ?? .none,
            timeout: 10.0,
            retryCount: 3,
            retryInterval: 1.0,
            heartbeatInterval: 15.0  // 统一使用15秒心跳间隔
        )

        debugLog("🔍 [Reconnect_DEBUG] Creating ConnectionManager with unified reconnection handling")
        connectionManager = ConnectionManager(
            configuration: connectionConfig,
            packetFlow: self.packetFlow,
            serverManager: serverManager,
            logger: logger
        )

        // Set PacketTunnelProvider as the reconnection handler
        // This provides unified reconnection logic that includes both UDP connection and TUN interface management
        await setupReconnectionCallback()

        debugLog("🔍 [Reconnect_DEBUG] VPN components initialized successfully with unified reconnection handling")
        logger.info("VPN components initialized successfully (unified reconnection)", metadata: [
            "unified_reconnection_enabled": "true"
        ])
    }

    /**
     * NAME: startSDWANConnection
     *
     * DESCRIPTION:
     *     Establishes SDWAN connection with authentication and data processing.
     *     This combines connection, authentication, and packet processing in one step.
     *
     * PARAMETERS:
     *     config - Tunnel configuration
     *
     * RETURNS:
     *     [String: Any] - Server configuration from OpenAck response
     *
     * THROWS:
     *     PlatformError.connectionFailed - If connection or authentication fails
     */
    private func startSDWANConnection(config: VPNTunnelConfiguration) async throws -> [String: Any] {
        guard let connectionManager = connectionManager else {
            logger.error("ConnectionManager not initialized")
            throw PlatformError.tunnelNotStarted
        }

        logger.info("Starting SDWAN authentication", metadata: [
            "server": "\(config.serverAddress):\(config.serverPort)",
            "username": config.username,
            "encryption_method": "\(config.encryptionMethod)"
        ])

        // Create server info for authentication
        let serverInfo = ServerInfo(
            id: "tunnel-server",
            name: "Tunnel Server",
            nameEn: "Tunnel Server",
            serverName: config.serverAddress,
            serverPort: config.serverPort,
            isAuto: false,
            ping: 0,
            status: .online
        )

        debugLog("🔍 [Reconnect_DEBUG] Step 3a: Starting ConnectionManager for authentication")
        logger.info("Step 3a: Starting ConnectionManager for authentication")
        // Start ConnectionManager first
        try await connectionManager.start()
        debugLog("🔍 [Reconnect_DEBUG] Step 3a completed: ConnectionManager started with monitoring enabled")
        logger.info("Step 3a completed: ConnectionManager started")

        // //debugLog("Step 3b: Authenticating with server \(serverInfo.serverName):\(serverInfo.serverPort)") // Debug debugLog commented for production
        logger.info("Step 3b: Authenticating with server")

        // Connect to server (this includes authentication and starts packet processing)
        let serverConfig = try await connectionManager.connect(to: serverInfo)

        // //debugLog("Step 3b completed: Connection and authentication successful") // Debug debugLog commented for production
        logger.info("Step 3b completed: Connection and authentication successful")

        // Return network configuration from OpenAck response
        // //debugLog("Received server configuration with \(serverConfig.count) parameters") // Debug debugLog commented for production
        logger.info("Received server configuration from OpenAck", metadata: [
            "config_count": "\(serverConfig.count)"
        ])

        return serverConfig
    }




    
    // MARK: - Network Configuration
    
    /**
     * NAME: createNetworkSettings
     *
     * DESCRIPTION:
     *     Creates NetworkExtension network settings for tunnel configuration.
     *     Configures IP addresses, routes, DNS, and MTU based on SDWAN requirements.
     *     Uses server-provided configuration from OpenAck response when available.
     *
     * PARAMETERS:
     *     config - Tunnel configuration
     *     serverConfig - Optional server configuration from OpenAck response
     *
     * RETURNS:
     *     NEPacketTunnelNetworkSettings - Configured network settings
     *
     * THROWS:
     *     PlatformError.networkConfigurationFailed - If settings creation fails
     */
    private func createNetworkSettings(for config: VPNTunnelConfiguration, serverConfig: [String: Any]? = nil) throws -> NEPacketTunnelNetworkSettings {
        // //debugLog("createNetworkSettings called") // Debug debugLog commented for production
        // //debugLog("serverConfig: \(serverConfig ?? [:])") // Debug debugLog commented for production
        // //debugLog("config.tunnelIP: \(config.tunnelIP)") // Debug debugLog commented for production

        // Use server-provided configuration when available, fallback to static config
        let tunnelIP = (serverConfig?["ip"] as? String) ?? config.tunnelIP
        let mtu = (serverConfig?["mtu"] as? String).flatMap { Int($0) } ?? config.mtu

        // //debugLog("Final tunnelIP: \(tunnelIP) (from \(serverConfig?["ip"] != nil ? "server" : "config"))") // Debug debugLog commented for production
        // Extract DNS servers from server config
        var dnsServers = config.dnsServers
        if let serverConfig = serverConfig {
            var serverDNS: [String] = []
            if let dns1 = serverConfig["dns"] as? String {
                serverDNS.append(dns1)
            }
            if let dns2 = serverConfig["dns2"] as? String {
                serverDNS.append(dns2)
            }
            if !serverDNS.isEmpty {
                dnsServers = serverDNS
            }
        }

        // Use /32 mask for tunnel IP as recommended
        let netmask = "***************"

        logger.info("Creating network settings", metadata: [
            "server": config.serverAddress,
            "tunnel_ip": tunnelIP,
            "mtu": "\(mtu)",
            "dns_servers": "\(dnsServers.count)",
            "source": serverConfig != nil ? "server_config" : "static_config"
        ])

        // //debugLog("Network configuration:") // Debug debugLog commented for production
        // //debugLog("- Tunnel IP: \(tunnelIP) (from \(serverConfig?["ip"] != nil ? "server OpenAck" : "static config"))") // Debug debugLog commented for production
        // //debugLog("- MTU: \(mtu) (from \(serverConfig?["mtu"] != nil ? "server OpenAck" : "static config"))") // Debug debugLog commented for production
        // //debugLog("- DNS: \(dnsServers) (from \(serverConfig != nil ? "server OpenAck" : "static config"))") // Debug debugLog commented for production
        // //debugLog("- Netmask: \(netmask) (/32 for point-to-point)") // Debug debugLog commented for production
        // if let serverConfig = serverConfig {
        //     //debugLog("- Server config received: \(serverConfig)") // Debug debugLog commented for production
        // } else {
        //     //debugLog("- No server config received, using static values") // Debug debugLog commented for production
        // }

        // Resolve server address to IP for tunnelRemoteAddress
        // iOS requires tunnelRemoteAddress to be a valid IP address, not hostname
        let resolvedServerIP = resolveServerAddressToIP(config.serverAddress)

        // Create base network settings with resolved server IP
        let settings = NEPacketTunnelNetworkSettings(tunnelRemoteAddress: resolvedServerIP)

        // Configure IPv4 settings with server-provided or static tunnel IP
        let ipv4Settings = NEIPv4Settings(
            addresses: [tunnelIP],
            subnetMasks: [netmask]
        )
        
        // Configure routing based on routing mode
        if config.routingMode == "custom" && !config.customRoutes.isEmpty {
            // Custom routing mode - only route specified networks through VPN
            var customRoutes: [NEIPv4Route] = []

            // //debugLog("Using custom routing mode with \(config.customRoutes.count) routes") // Debug debugLog commented for production

            for routeString in config.customRoutes {
                let trimmedRoute = routeString.trimmingCharacters(in: .whitespaces)
                if trimmedRoute.isEmpty { continue }

                // Parse CIDR notation (e.g., "***********/24")
                let components = trimmedRoute.split(separator: "/")
                if components.count == 2,
                   let ipString = components.first,
                   let prefixString = components.last,
                   let prefixLength = Int(prefixString),
                   prefixLength >= 0 && prefixLength <= 32 {

                    // Create subnet mask from prefix length
                    let mask = (0xFFFFFFFF << (32 - prefixLength)) & 0xFFFFFFFF
                    let subnetMask = String(format: "%d.%d.%d.%d",
                                           (mask >> 24) & 0xFF,
                                           (mask >> 16) & 0xFF,
                                           (mask >> 8) & 0xFF,
                                           mask & 0xFF)

                    let route = NEIPv4Route(destinationAddress: String(ipString), subnetMask: subnetMask)
                    customRoutes.append(route)
                    // //debugLog("Added custom route: \(trimmedRoute)") // Debug debugLog commented for production
                } else {
                    // //debugLog("Invalid CIDR format: \(trimmedRoute)") // Debug debugLog commented for production
                }
            }

            // Use custom routes if any are valid, otherwise fall back to default
            ipv4Settings.includedRoutes = customRoutes.isEmpty ? [NEIPv4Route.default()] : customRoutes
            // //debugLog("Applied \(customRoutes.count) custom routes") // Debug debugLog commented for production
        } else {
            // All traffic routing mode (default)
            ipv4Settings.includedRoutes = [NEIPv4Route.default()]
            // //debugLog("Using all-traffic routing (default route)") // Debug debugLog commented for production
        }
        
        // Configure excluded routes for server accessibility and local networks
        var excludedRoutes: [NEIPv4Route] = []

        // //debugLog("Configuring excluded routes") // Debug debugLog commented for production
        // //debugLog("- Server excludedIPs from config: \(config.excludedIPs)") // Debug debugLog commented for production

        // Exclude server IPs (provided by app layer after resolution)
        for excludedIP in config.excludedIPs {
            // //debugLog("- Processing excluded IP: \(excludedIP)") // Debug debugLog commented for production
            if let route = createExcludedRoute(for: excludedIP) {
                excludedRoutes.append(route)
                // //debugLog("- Added excluded route for: \(excludedIP)") // Debug debugLog commented for production
            } else {
                // //debugLog("- ERROR: Failed to create route for: \(excludedIP)") // Debug debugLog commented for production
            }
        }

        // Exclude local networks (equivalent to Go backend local network protection)
        let localExclusions = createLocalNetworkExclusions()
        excludedRoutes.append(contentsOf: localExclusions)
        // //debugLog("- Added \(localExclusions.count) local network exclusions") // Debug debugLog commented for production
        
        ipv4Settings.excludedRoutes = excludedRoutes
        settings.ipv4Settings = ipv4Settings
        
        // Configure DNS settings using server-provided or static DNS
        let dnsSettings = NEDNSSettings(servers: dnsServers)
        settings.dnsSettings = dnsSettings

        // Set MTU using server-provided or static MTU
        settings.mtu = NSNumber(value: mtu)
        
        // //debugLog("Final network configuration:") // Debug debugLog commented for production
        // //debugLog("- Tunnel IP: \(tunnelIP)") // Debug debugLog commented for production
        // //debugLog("- MTU: \(mtu)") // Debug debugLog commented for production
        // //debugLog("- DNS servers: \(dnsServers)") // Debug debugLog commented for production
        // //debugLog("- Total excluded routes: \(excludedRoutes.count)") // Debug debugLog commented for production
        // for (index, route) in excludedRoutes.enumerated() {
        //     //debugLog("- Excluded route \(index + 1): \(route.destinationAddress)/\(route.destinationSubnetMask)") // Debug debugLog commented for production
        // }

        // Store tunnel IP in App Group for main app access
        storeTunnelIPInAppGroup(tunnelIP)

        logger.info("Network settings created successfully", metadata: [
            "included_routes": "1", // Default route
            "excluded_routes": "\(excludedRoutes.count)",
            "dns_servers": "\(config.dnsServers.count)",
            "tunnel_ip_stored": tunnelIP
        ])

        return settings
    }

    /**
     * NAME: createExcludedRoute
     *
     * DESCRIPTION:
     *     Creates excluded route for specific IP address or hostname.
     *     Ensures server and critical IPs bypass VPN tunnel.
     *     Attempts to resolve hostname to IP if needed.
     *
     * PARAMETERS:
     *     ipAddress - IP address or hostname to exclude
     *
     * RETURNS:
     *     NEIPv4Route? - Excluded route or nil if invalid
     */
    private func createExcludedRoute(for ipAddress: String) -> NEIPv4Route? {
        // First check if it's already a valid IP address
        if let _ = IPv4Address(ipAddress) {
            return NEIPv4Route(destinationAddress: ipAddress, subnetMask: "***************")
        }

        // If not an IP, try to get cached IP from App Group
        if let cachedIP = getCachedServerIPFromAppGroup(ipAddress) {
            logger.info("Using cached IP for exclusion", metadata: [
                "hostname": ipAddress,
                "cached_ip": cachedIP
            ])
            return NEIPv4Route(destinationAddress: cachedIP, subnetMask: "***************")
        }

        // If no cached IP, try synchronous DNS resolution as last resort
        if let resolvedIP = performSyncDNSResolution(ipAddress) {
            logger.info("Resolved hostname for exclusion", metadata: [
                "hostname": ipAddress,
                "resolved_ip": resolvedIP
            ])
            return NEIPv4Route(destinationAddress: resolvedIP, subnetMask: "***************")
        }

        logger.warning("Failed to resolve hostname for exclusion", metadata: [
            "hostname": ipAddress,
            "note": "Server may be routed through VPN tunnel"
        ])
        return nil
    }

    /**
     * NAME: createIncludedRoutes
     *
     * DESCRIPTION:
     *     Creates included routes based on routing configuration.
     *     Supports both all-traffic and custom routing modes.
     *
     * PARAMETERS:
     *     config - VPN tunnel configuration containing routing settings
     *
     * RETURNS:
     *     [NEIPv4Route] - Array of routes to include in VPN tunnel
     */
    private func createIncludedRoutes(config: VPNTunnelConfiguration) -> [NEIPv4Route] {
        // //debugLog("Creating included routes - mode: \(config.routingMode)") // Debug debugLog commented for production

        switch config.routingMode {
        case "custom":
            return createCustomIncludedRoutes(customRoutes: config.customRoutes)
        case "all":
            fallthrough
        default:
            // Default to all traffic routing
            // //debugLog("Using all-traffic routing (default route)") // Debug debugLog commented for production
            return [NEIPv4Route.default()]
        }
    }

    /**
     * NAME: createCustomIncludedRoutes
     *
     * DESCRIPTION:
     *     Creates included routes for custom routing mode.
     *     Parses CIDR notation routes and creates NEIPv4Route objects.
     *
     * PARAMETERS:
     *     customRoutes - Array of custom routes in CIDR notation
     *
     * RETURNS:
     *     [NEIPv4Route] - Array of custom routes
     */
    private func createCustomIncludedRoutes(customRoutes: [String]) -> [NEIPv4Route] {
        var routes: [NEIPv4Route] = []

        // //debugLog("Processing \(customRoutes.count) custom routes") // Debug debugLog commented for production

        for routeString in customRoutes {
            let trimmedRoute = routeString.trimmingCharacters(in: .whitespaces)
            if trimmedRoute.isEmpty {
                continue
            }

            // //debugLog("Processing custom route: \(trimmedRoute)") // Debug debugLog commented for production

            if let route = parseCustomRoute(routeString: trimmedRoute) {
                routes.append(route)
                // //debugLog("Added custom route: \(trimmedRoute)") // Debug debugLog commented for production
            } else {
                // //debugLog("ERROR: Failed to parse custom route: \(trimmedRoute)") // Debug debugLog commented for production
            }
        }

        // If no valid custom routes, fall back to default route
        if routes.isEmpty {
            // //debugLog("No valid custom routes, falling back to default route") // Debug debugLog commented for production
            routes.append(NEIPv4Route.default())
        }

        return routes
    }

    /**
     * NAME: parseCustomRoute
     *
     * DESCRIPTION:
     *     Parses a custom route string in CIDR notation and creates NEIPv4Route.
     *
     * PARAMETERS:
     *     routeString - Route in CIDR notation (e.g., "***********/24")
     *
     * RETURNS:
     *     NEIPv4Route? - Parsed route or nil if invalid
     */
    private func parseCustomRoute(routeString: String) -> NEIPv4Route? {
        let components = routeString.split(separator: "/")
        guard components.count == 2,
              let ipString = components.first,
              let prefixString = components.last,
              let prefixLength = Int(prefixString),
              prefixLength >= 0 && prefixLength <= 32 else {
            // //debugLog("Invalid CIDR format: \(routeString)") // Debug debugLog commented for production
            return nil
        }

        // Create subnet mask from prefix length
        let mask = (0xFFFFFFFF << (32 - prefixLength)) & 0xFFFFFFFF
        let subnetMask = String(format: "%d.%d.%d.%d",
                               (mask >> 24) & 0xFF,
                               (mask >> 16) & 0xFF,
                               (mask >> 8) & 0xFF,
                               mask & 0xFF)

        return NEIPv4Route(destinationAddress: String(ipString), subnetMask: subnetMask)
    }

    /**
     * NAME: createLocalNetworkExclusions
     *
     * DESCRIPTION:
     *     Creates excluded routes for local networks.
     *     Equivalent to Go backend's local network protection.
     *
     * RETURNS:
     *     [NEIPv4Route] - Array of local network exclusions
     */
    private func createLocalNetworkExclusions() -> [NEIPv4Route] {
        return [
            // Link-local addresses (RFC 3927)
            NEIPv4Route(destinationAddress: "***********", subnetMask: "***********"),

            // Multicast addresses (RFC 3171)
            NEIPv4Route(destinationAddress: "*********", subnetMask: "240.0.0.0")
        ]
    }

    // MARK: - Packet Processing

    /**
     * NOTE: Packet Processing Architecture
     *
     * DESCRIPTION:
     *     In iOS/macOS NetworkExtension architecture, packet processing is handled automatically
     *     by ConnectionManager's internal packet sender/receiver loops. Unlike Go backend where
     *     we manually read from TUN device, NetworkExtension provides packetFlow which is
     *     automatically processed by ConnectionManager.runPacketSender() and runPacketReceiver().
     *
     *     Flow:
     *     1. System → NEPacketTunnelFlow → ConnectionManager.runPacketSender() → SDWAN Server
     *     2. SDWAN Server → ConnectionManager.runPacketReceiver() → NEPacketTunnelFlow → System
     *
     *     This eliminates the need for manual packet processing in PacketTunnelProvider.
     */

    // MARK: - Configuration Management

    /**
     * NAME: parseConfiguration
     *
     * DESCRIPTION:
     *     Parses tunnel configuration from NetworkExtension options.
     *     Extracts server details, credentials, and network parameters.
     *
     * PARAMETERS:
     *     options - Configuration options from system/app
     *
     * RETURNS:
     *     TunnelConfiguration - Parsed configuration
     *
     * THROWS:
     *     PlatformError.configurationInvalid - If required parameters missing
     */
    private func parseConfiguration(from options: [String: NSObject]?) throws -> VPNTunnelConfiguration {
        guard let options = options else {
            throw PlatformError.configurationInvalid
        }

        // Extract required parameters
        guard let serverAddress = options["serverAddress"] as? String,
              let serverPort = options["serverPort"] as? NSNumber,
              let username = options["username"] as? String,
              let password = options["password"] as? String else {
            throw PlatformError.configurationInvalid
        }

        // Extract optional parameters with defaults
        let mtu = (options["mtu"] as? NSNumber)?.intValue ?? 1400
        let encryptionMethod = (options["encryptionMethod"] as? NSNumber)?.uint8Value ?? 1  // Default to XOR (0x01) to match Windows
        let dnsServers = (options["dnsServers"] as? [String]) ?? ["*******", "*******"]
        let excludedIPs = (options["excludedIPs"] as? [String]) ?? []
        let tunnelIP = (options["tunnelIP"] as? String) ?? "********"
        let routingMode = (options["routingMode"] as? String) ?? "all"
        let customRoutes = (options["customRoutes"] as? [String]) ?? []

        // //debugLog("Parsed routing configuration: mode=\(routingMode), routes=\(customRoutes)") // Debug debugLog commented for production

        return VPNTunnelConfiguration(
            serverAddress: serverAddress,
            serverPort: serverPort.intValue,
            username: username,
            password: password,
            mtu: mtu,
            encryptionMethod: encryptionMethod,
            dnsServers: dnsServers,
            excludedIPs: excludedIPs,
            tunnelIP: tunnelIP,
            routingMode: routingMode,
            customRoutes: customRoutes
        )
    }

    /**
     * NAME: processAppMessage
     *
     * DESCRIPTION:
     *     Processes messages from main app for runtime configuration changes.
     *     Supports server switching and configuration updates without tunnel restart.
     *
     * PARAMETERS:
     *     messageData - Message data from app
     *
     * RETURNS:
     *     Data? - Response data
     *
     * THROWS:
     *     PlatformError - If message processing fails
     */
    private func processAppMessage(_ messageData: Data) async throws -> Data? {
        // Parse message (simplified JSON format)
        guard let message = try? JSONSerialization.jsonObject(with: messageData) as? [String: Any],
              let action = message["action"] as? String else {
            throw PlatformError.configurationInvalid
        }

        // logger.debug("Processing app message", metadata: ["action": action]) // Debug log commented for production

        switch action {
        case "switch_server":
            return try await handleServerSwitch(message)
        case "update_config":
            return try await handleConfigUpdate(message)
        case "get_status":
            return try await handleStatusRequest()
        default:
            logger.warning("Unknown app message action", metadata: ["action": action])
            return nil
        }
    }

    /**
     * NAME: handleServerSwitch
     *
     * DESCRIPTION:
     *     Handles server switching request from app.
     *     Updates connection to new server without full tunnel restart.
     *
     * PARAMETERS:
     *     message - Server switch message data
     *
     * RETURNS:
     *     Data? - Response data
     */
    private func handleServerSwitch(_ message: [String: Any]) async throws -> Data? {
        guard let serverAddress = message["serverAddress"] as? String,
              let serverPort = message["serverPort"] as? NSNumber else {
            throw PlatformError.configurationInvalid
        }

        logger.info("Switching to new server", metadata: [
            "server": "\(serverAddress):\(serverPort.intValue)"
        ])

        // Note: In iOS/macOS, server switching requires NetworkExtension rebuild
        // This is different from Go backend which can switch UDP connections
        let response: [String: Any] = [
            "success": true,
            "message": "Server switch requires tunnel restart",
            "requires_restart": true
        ]

        return try JSONSerialization.data(withJSONObject: response)
    }

    /**
     * NAME: handleConfigUpdate
     *
     * DESCRIPTION:
     *     Handles configuration update request from app.
     *
     * PARAMETERS:
     *     message - Configuration update message
     *
     * RETURNS:
     *     Data? - Response data
     */
    private func handleConfigUpdate(_ message: [String: Any]) async throws -> Data? {
        logger.info("Updating configuration")

        // Update supported runtime configurations
        if let newDNS = message["dnsServers"] as? [String] {
            try await updateDNSConfiguration(newDNS)
        }

        if let newMTU = message["mtu"] as? NSNumber {
            try await updateMTUConfiguration(newMTU.intValue)
        }

        let response: [String: Any] = [
            "success": true,
            "message": "Configuration updated successfully"
        ]

        return try JSONSerialization.data(withJSONObject: response)
    }

    /**
     * NAME: handleStatusRequest
     *
     * DESCRIPTION:
     *     Handles status request from app.
     *
     * RETURNS:
     *     Data? - Status response data
     */
    private func handleStatusRequest() async throws -> Data? {
        let connectionState = await connectionManager?.getConnectionState() ?? .disconnected

        let status: [String: Any] = [
            "connected": isStarted,
            "state": connectionState.rawValue,
            "upload_speed": 0,
            "download_speed": 0,
            "total_upload": 0,
            "total_download": 0
        ]

        return try JSONSerialization.data(withJSONObject: status)
    }

    // MARK: - Runtime Configuration Updates

    /**
     * NAME: updateDNSConfiguration
     *
     * DESCRIPTION:
     *     Updates DNS configuration during runtime.
     *
     * PARAMETERS:
     *     dnsServers - New DNS servers
     */
    private func updateDNSConfiguration(_ dnsServers: [String]) async throws {
        guard let config = configuration else {
            throw PlatformError.configurationInvalid
        }

        // Update configuration
        var updatedConfig = config
        updatedConfig.dnsServers = dnsServers
        self.configuration = updatedConfig

        // Create updated network settings
        let networkSettings = try createNetworkSettings(for: updatedConfig)
        try await setTunnelNetworkSettings(networkSettings)

        logger.info("DNS configuration updated", metadata: ["servers": "\(dnsServers.count)"])
    }

    /**
     * NAME: updateMTUConfiguration
     *
     * DESCRIPTION:
     *     Updates MTU configuration during runtime.
     *     Note: MTU changes require tunnel restart in NetworkExtension.
     *
     * PARAMETERS:
     *     mtu - New MTU value
     */
    private func updateMTUConfiguration(_ mtu: Int) async throws {
        guard let config = configuration else {
            throw PlatformError.configurationInvalid
        }

        // Update configuration
        var updatedConfig = config
        updatedConfig.mtu = mtu
        self.configuration = updatedConfig

        // Note: MTU changes in NetworkExtension require tunnel restart
        // The new MTU will be applied on next tunnel start
        logger.info("MTU configuration updated (requires tunnel restart)", metadata: ["mtu": "\(mtu)"])
    }

    // MARK: - Cleanup and Error Handling

    /**
     * NAME: cleanupTunnel
     *
     * DESCRIPTION:
     *     Cleans up tunnel resources and disconnects SDWAN connection.
     *     Simplified cleanup for streamlined architecture.
     *
     * PARAMETERS:
     *     reason - Reason for tunnel cleanup
     */
    private func cleanupTunnel(reason: NEProviderStopReason) async {
        logger.info("Cleaning up tunnel (simplified)", metadata: ["reason": "\(reason.rawValue)"])

        isStarted = false

        // Stop and disconnect SDWAN connection
        if let connectionManager = connectionManager {
            try? await connectionManager.disconnect()
            await connectionManager.stop()
        }

        // Clear tunnel IP from App Group
        clearTunnelIPFromAppGroup()

        // Clean up components
        connectionManager = nil
        configuration = nil

        // 重要：如果是由于连接失败导致的清理，需要调用cancelTunnelWithError()
        // 这样可以确保系统正式取消VPN隧道，恢复用户的正常网络连接
        if reason == .connectionFailed {
            let error = NEVPNError(.connectionFailed)
            logger.warning("Cancelling tunnel due to connection failure to restore user network")
            cancelTunnelWithError(error)
        }

        logger.info("Tunnel cleanup completed (simplified)")
    }

    /**
     * NAME: cleanupOnError
     *
     * DESCRIPTION:
     *     Cleans up resources when tunnel start fails.
     *     Simplified cleanup for streamlined architecture.
     */
    private func cleanupOnError() async {
        logger.warning("Cleaning up due to error (simplified)")

        isStarted = false

        // Clean up partially initialized components
        if let connectionManager = connectionManager {
            try? await connectionManager.disconnect()
            await connectionManager.stop()
        }

        // Reset state
        connectionManager = nil
        configuration = nil
    }

    // MARK: - App Group Data Sharing

    /**
     * NAME: storeTunnelIPInAppGroup
     *
     * DESCRIPTION:
     *     Stores the current tunnel IP address in App Group shared storage
     *     for access by the main application.
     *
     * PARAMETERS:
     *     tunnelIP - The tunnel IP address to store
     */
    private func storeTunnelIPInAppGroup(_ tunnelIP: String) {
        // //debugLog("storeTunnelIPInAppGroup called with tunnelIP: \(tunnelIP)") // Debug debugLog commented for production

        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            logger.error("Failed to access App Group UserDefaults")
            // //debugLog("Failed to access App Group UserDefaults") // Debug debugLog commented for production
            // //debugLog("ERROR: Failed to access App Group UserDefaults") // Debug debugLog commented for production
            return
        }

        let tunnelInfo: [String: Any] = [
            "tunnel_ip": tunnelIP,
            "timestamp": Date().timeIntervalSince1970,
            "server_address": configuration?.serverAddress ?? "",
            "mtu": configuration?.mtu ?? 1400
        ]

        // //debugLog("About to store tunnel info: \(tunnelInfo)") // Debug debugLog commented for production

        userDefaults.set(tunnelInfo, forKey: "vpn_tunnel_info")
        userDefaults.synchronize()

        // Verify storage
        // if let storedData = userDefaults.object(forKey: "vpn_tunnel_info") {
        //     //debugLog("Successfully stored and verified tunnel info: \(storedData)") // Debug debugLog commented for production
        // } else {
        //     //debugLog("Failed to verify stored tunnel info") // Debug debugLog commented for production
        // }

        logger.info("Stored tunnel IP in App Group", metadata: [
            "tunnel_ip": tunnelIP,
            "server": configuration?.serverAddress ?? ""
        ])
        // //debugLog("Stored tunnel IP in App Group: \(tunnelIP)") // Debug debugLog commented for production
    }

    /**
     * NAME: clearTunnelIPFromAppGroup
     *
     * DESCRIPTION:
     *     Clears tunnel IP information from App Group when tunnel stops.
     */
    private func clearTunnelIPFromAppGroup() {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            logger.error("Failed to access App Group UserDefaults for cleanup")
            return
        }

        userDefaults.removeObject(forKey: "vpn_tunnel_info")
        userDefaults.synchronize()

        logger.info("Cleared tunnel IP from App Group")
        // //debugLog("Cleared tunnel IP from App Group") // Debug debugLog commented for production
    }

    /**
     * NAME: resolveServerAddressToIP
     *
     * DESCRIPTION:
     *     Resolves server address to IP address for tunnelRemoteAddress.
     *     First tries to get cached IP from App Group (shared with main app ServerManager),
     *     then performs synchronous DNS resolution if needed.
     *     iOS requires tunnelRemoteAddress to be a valid IP address.
     *
     * PARAMETERS:
     *     serverAddress - Server hostname or IP address to resolve
     *
     * RETURNS:
     *     String - Resolved IP address or original address if already IP
     */
    private func resolveServerAddressToIP(_ serverAddress: String) -> String {
        // Check if serverAddress is already an IP address
        if isValidIPAddress(serverAddress) {
            logger.info("Server address is already an IP", metadata: ["ip": serverAddress])
            return serverAddress
        }

        // Try to get cached IP from App Group (shared with main app ServerManager)
        if let cachedIP = getCachedServerIPFromAppGroup(serverAddress) {
            logger.info("Using cached server IP from App Group", metadata: [
                "hostname": serverAddress,
                "cached_ip": cachedIP
            ])
            return cachedIP
        }

        // Perform synchronous DNS resolution as fallback
        logger.info("Performing DNS resolution for server address", metadata: ["hostname": serverAddress])

        if let resolvedIP = performSyncDNSResolution(serverAddress) {
            logger.info("Successfully resolved server IP", metadata: [
                "hostname": serverAddress,
                "resolved_ip": resolvedIP
            ])

            // Cache the resolved IP in App Group for future use
            storeCachedServerIPInAppGroup(serverAddress, resolvedIP)

            return resolvedIP
        } else {
            logger.error("Failed to resolve server IP, using hostname as fallback", metadata: [
                "hostname": serverAddress
            ])

            // Return hostname as last resort - this may cause tunnel startup failure
            // but it's better than crashing
            return serverAddress
        }
    }

    /**
     * NAME: isValidIPAddress
     *
     * DESCRIPTION:
     *     Checks if a string is a valid IPv4 address.
     *
     * PARAMETERS:
     *     address - String to check
     *
     * RETURNS:
     *     Bool - true if valid IPv4 address, false otherwise
     */
    private func isValidIPAddress(_ address: String) -> Bool {
        var sin = sockaddr_in()
        return address.withCString { cstring in
            inet_pton(AF_INET, cstring, &sin.sin_addr) == 1
        }
    }

    /**
     * NAME: getCachedServerIPFromAppGroup
     *
     * DESCRIPTION:
     *     Gets cached server IP from App Group shared storage.
     *     This allows VPN extension to use IPs cached by main app's ServerManager.
     *
     * PARAMETERS:
     *     hostname - Server hostname to lookup
     *
     * RETURNS:
     *     String? - Cached IP address or nil if not found
     */
    private func getCachedServerIPFromAppGroup(_ hostname: String) -> String? {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            logger.warning("Failed to access App Group UserDefaults for server IP cache")
            return nil
        }

        guard let serverIPCache = userDefaults.object(forKey: "server_ip_cache") as? [String: Any] else {
            return nil
        }

        guard let cachedIP = serverIPCache[hostname] as? String else {
            return nil
        }

        // Check if cached data is recent (within last 30 minutes)
        if let cacheData = serverIPCache["\(hostname)_metadata"] as? [String: Any],
           let timestamp = cacheData["timestamp"] as? TimeInterval {
            let age = Date().timeIntervalSince1970 - timestamp
            if age > 1800 { // 30 minutes
                logger.warning("Cached server IP is stale", metadata: [
                    "hostname": hostname,
                    "age_minutes": String(format: "%.1f", age / 60.0)
                ])
                return nil
            }
        }

        return cachedIP
    }

    /**
     * NAME: storeCachedServerIPInAppGroup
     *
     * DESCRIPTION:
     *     Stores resolved server IP in App Group for sharing with main app.
     *
     * PARAMETERS:
     *     hostname - Server hostname
     *     ip - Resolved IP address
     */
    private func storeCachedServerIPInAppGroup(_ hostname: String, _ ip: String) {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            logger.warning("Failed to access App Group UserDefaults for storing server IP cache")
            return
        }

        var serverIPCache = userDefaults.object(forKey: "server_ip_cache") as? [String: Any] ?? [:]

        // Store IP and metadata
        serverIPCache[hostname] = ip
        serverIPCache["\(hostname)_metadata"] = [
            "timestamp": Date().timeIntervalSince1970,
            "source": "vpn_extension"
        ]

        userDefaults.set(serverIPCache, forKey: "server_ip_cache")
        userDefaults.synchronize()

        logger.info("Stored server IP in App Group cache", metadata: [
            "hostname": hostname,
            "ip": ip
        ])
    }

    /**
     * NAME: sendReconnectionSuccessToMainApp
     *
     * DESCRIPTION:
     *     Notifies main app that reconnection was successful by storing
     *     reconnection event in App Group. Main app can monitor this to
     *     update interface info and UI state.
     */
    private func sendReconnectionSuccessToMainApp() {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            logger.warning("Failed to access App Group UserDefaults for reconnection notification")
            return
        }

        // Get current tunnel IP and interface info
        let tunnelIP = getTunnelIPFromAppGroup() ?? ""

        // Create reconnection success event
        let reconnectionEvent: [String: Any] = [
            "event_type": "reconnection_success",
            "timestamp": Date().timeIntervalSince1970,
            "tunnel_ip": tunnelIP,
            "server_address": configuration?.serverAddress ?? "",
            "server_port": configuration?.serverPort ?? 0,
            "mtu": configuration?.mtu ?? 1400
        ]

        // Store event for main app to detect
        userDefaults.set(reconnectionEvent, forKey: "vpn_reconnection_event")
        userDefaults.synchronize()

        logger.info("Stored reconnection success event in App Group", metadata: [
            "tunnel_ip": tunnelIP,
            "server": configuration?.serverAddress ?? ""
        ])
    }

    /**
     * NAME: getTunnelIPFromAppGroup
     *
     * DESCRIPTION:
     *     Gets current tunnel IP from App Group storage.
     *
     * RETURNS:
     *     String? - Current tunnel IP or nil if not available
     */
    private func getTunnelIPFromAppGroup() -> String? {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            return nil
        }

        guard let tunnelInfo = userDefaults.object(forKey: "vpn_tunnel_info") as? [String: Any] else {
            return nil
        }

        return tunnelInfo["tunnel_ip"] as? String
    }

    /**
     * NAME: sendConnectionFailureToMainApp
     *
     * DESCRIPTION:
     *     通知主应用连接失败事件
     *
     * PARAMETERS:
     *     errorMessage - 错误信息
     *     serverAddress - 服务器地址
     */
    private func sendConnectionFailureToMainApp(errorMessage: String, serverAddress: String) {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            logger.warning("Failed to access App Group UserDefaults for connection failure notification")
            return
        }

        let failureEvent: [String: Any] = [
            "event_type": "connection_failure",
            "timestamp": Date().timeIntervalSince1970,
            "error_message": errorMessage,
            "server_address": serverAddress,
            "server_port": configuration?.serverPort ?? 0
        ]

        userDefaults.set(failureEvent, forKey: "vpn_connection_failure_event")
        userDefaults.synchronize()

        logger.warning("Stored connection failure event in App Group", metadata: [
            "error_message": errorMessage,
            "server": serverAddress
        ])
    }

    /**
     * NAME: sendReconnectionFailureToMainApp
     *
     * DESCRIPTION:
     *     通知主应用重连失败事件
     *
     * PARAMETERS:
     *     errorMessage - 错误信息
     *     serverAddress - 服务器地址
     */
    private func sendReconnectionFailureToMainApp(errorMessage: String, serverAddress: String) {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            logger.warning("Failed to access App Group UserDefaults for reconnection failure notification")
            return
        }

        let failureEvent: [String: Any] = [
            "event_type": "reconnection_failure",
            "timestamp": Date().timeIntervalSince1970,
            "error_message": errorMessage,
            "server_address": serverAddress,
            "server_port": configuration?.serverPort ?? 0
        ]

        userDefaults.set(failureEvent, forKey: "vpn_reconnection_failure_event")
        userDefaults.synchronize()

        logger.warning("Stored reconnection failure event in App Group", metadata: [
            "error_message": errorMessage,
            "server": serverAddress
        ])
    }

    /**
     * NAME: updateConnectionStateToAppGroup
     *
     * DESCRIPTION:
     *     更新连接状态到App Group供主应用读取
     *
     * PARAMETERS:
     *     state - 连接状态字符串 (disconnected, connecting, connected, etc.)
     *     reason - 状态变更原因 (可选)
     */
    private func updateConnectionStateToAppGroup(state: String, reason: String? = nil) {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            logger.warning("Failed to access App Group UserDefaults for connection state update")
            return
        }

        var stateEvent: [String: Any] = [
            "event_type": "connection_state_change",
            "timestamp": Date().timeIntervalSince1970,
            "connection_state": state,
            "server_address": configuration?.serverAddress ?? "",
            "server_port": configuration?.serverPort ?? 0
        ]

        if let reason = reason {
            stateEvent["reason"] = reason
        }

        userDefaults.set(stateEvent, forKey: "vpn_connection_state_event")
        userDefaults.synchronize()

        logger.info("Updated connection state in App Group", metadata: [
            "state": state,
            "reason": reason ?? "none",
            "server": configuration?.serverAddress ?? ""
        ])
    }

    /**
     * NAME: performSyncDNSResolution
     *
     * DESCRIPTION:
     *     Performs synchronous DNS resolution for hostname to get IPv4 address.
     *     Uses CFHost API for immediate resolution.
     *
     * PARAMETERS:
     *     hostname - Hostname to resolve
     *
     * RETURNS:
     *     String? - Resolved IPv4 address or nil if resolution fails
     */
    private func performSyncDNSResolution(_ hostname: String) -> String? {
        let host = CFHostCreateWithName(nil, hostname as CFString).takeRetainedValue()

        var success: DarwinBoolean = false
        CFHostStartInfoResolution(host, .addresses, nil)

        guard let addresses = CFHostGetAddressing(host, &success)?.takeUnretainedValue() as NSArray?,
              success.boolValue else {
            logger.warning("CFHost DNS resolution failed", metadata: ["hostname": hostname])
            return nil
        }

        // Find first IPv4 address
        for case let addressData as NSData in addresses {
            var storage = sockaddr_storage()
            addressData.getBytes(&storage, length: MemoryLayout<sockaddr_storage>.size)

            if storage.ss_family == sa_family_t(AF_INET) {
                let addr4 = withUnsafePointer(to: &storage) {
                    $0.withMemoryRebound(to: sockaddr_in.self, capacity: 1) { $0.pointee }
                }

                let ipString = String(cString: inet_ntoa(addr4.sin_addr))
                return ipString
            }
        }

        logger.warning("No IPv4 address found for hostname", metadata: ["hostname": hostname])
        return nil
    }

    // MARK: - Keep-Alive Server List Fetching




}

// MARK: - Simplified Architecture Notes

/**
 * ARCHITECTURE SIMPLIFICATION NOTES:
 *
 * Removed Components:
 * - TUNDeviceManager: NetworkExtension's packetFlow handles packet processing automatically
 * - RouteManager: NetworkExtension's NEIPv4Settings handles all routing configuration
 * - DNSManager: NetworkExtension's NEDNSSettings handles all DNS configuration
 * - TUNDeviceDelegate: No longer needed without TUNDeviceManager
 *
 * Benefits:
 * - Reduced code complexity by ~1400 lines
 * - Eliminated redundant abstractions
 * - Direct use of NetworkExtension APIs
 * - Simplified error handling and state management
 */

// MARK: - Configuration Structure

/**
 * NAME: VPNTunnelConfiguration
 *
 * DESCRIPTION:
 *     Configuration structure for VPN tunnel parameters.
 *     Contains all necessary settings for SDWAN connection and network configuration.
 *     Renamed to avoid conflict with NetworkExtensionProvider's TunnelConfiguration.
 *     Enhanced with routing configuration support.
 */
public struct VPNTunnelConfiguration {
    public let serverAddress: String
    public let serverPort: Int
    public let username: String
    public let password: String
    public var mtu: Int
    public let encryptionMethod: UInt8
    public var dnsServers: [String]
    public let excludedIPs: [String]
    public let tunnelIP: String
    public let routingMode: String
    public let customRoutes: [String]

    public init(
        serverAddress: String,
        serverPort: Int,
        username: String,
        password: String,
        mtu: Int = 1400,
        encryptionMethod: UInt8 = 0,
        dnsServers: [String] = ["*******", "*******"],
        excludedIPs: [String] = [],
        tunnelIP: String = "********",
        routingMode: String = "all",
        customRoutes: [String] = []
    ) {
        self.serverAddress = serverAddress
        self.serverPort = serverPort
        self.username = username
        self.password = password
        self.mtu = mtu
        self.encryptionMethod = encryptionMethod
        self.dnsServers = dnsServers
        self.excludedIPs = excludedIPs
        self.tunnelIP = tunnelIP
        self.routingMode = routingMode
        self.customRoutes = customRoutes
    }
}