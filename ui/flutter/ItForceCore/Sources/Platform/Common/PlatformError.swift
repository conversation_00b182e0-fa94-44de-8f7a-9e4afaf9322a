/**
 * FILE: PlatformError.swift
 *
 * DESCRIPTION:
 *     Platform layer error definitions for iOS/macOS NetworkExtension operations.
 *     Provides comprehensive error handling for VPN tunnel and network operations.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation

/**
 * NAME: PlatformError
 *
 * DESCRIPTION:
 *     Enumeration of platform-specific errors for NetworkExtension operations.
 *     Covers tunnel management, network configuration, and packet processing errors.
 */
public enum PlatformError: Error, LocalizedError, CustomStringConvertible {
    // Configuration Errors
    case configurationInvalid
    case configurationMissing
    case configurationParsingFailed(String)
    
    // Tunnel Errors
    case tunnelStartFailed
    case tunnelStopFailed
    case tunnelNotStarted
    case tunnelAlreadyStarted
    
    // Network Configuration Errors
    case networkConfigurationFailed
    case routeConfigurationFailed
    case dnsConfigurationFailed
    case interfaceConfigurationFailed
    
    // Packet Processing Errors
    case packetReadFailed
    case packetWriteFailed
    case packetProcessingFailed
    case invalidPacketFormat
    
    // TUN Device Errors
    case tunDeviceCreationFailed
    case tunDeviceConfigurationFailed
    case tunDeviceStartFailed
    case tunDeviceStopFailed
    case tunDeviceNotAvailable
    
    // Route Management Errors
    case routeAddFailed(String)
    case routeDeleteFailed(String)
    case routeConflictDetected(String)
    case routeTableAccessFailed
    
    // DNS Management Errors
    case dnsServerConfigurationFailed
    case dnsResolutionFailed
    case dnsServerNotReachable
    
    // Platform Specific Errors
    case platformUnsupported
    case permissionDenied
    case resourceUnavailable
    case systemCallFailed(String)
    
    // Message Handling Errors
    case messageHandlingFailed
    case invalidMessageFormat
    case messageProcessingTimeout
    
    // Memory and Resource Errors
    case memoryAllocationFailed
    case resourceLimitExceeded
    case bufferOverflow
    
    /**
     * NAME: errorDescription
     *
     * DESCRIPTION:
     *     Provides localized error descriptions for user-facing error messages.
     *
     * RETURNS:
     *     String? - Localized error description
     */
    public var errorDescription: String? {
        switch self {
        // Configuration Errors
        case .configurationInvalid:
            return "VPN configuration is invalid"
        case .configurationMissing:
            return "VPN configuration is missing"
        case .configurationParsingFailed(let details):
            return "Failed to parse VPN configuration: \(details)"
            
        // Tunnel Errors
        case .tunnelStartFailed:
            return "Failed to start VPN tunnel"
        case .tunnelStopFailed:
            return "Failed to stop VPN tunnel"
        case .tunnelNotStarted:
            return "VPN tunnel is not started"
        case .tunnelAlreadyStarted:
            return "VPN tunnel is already started"
            
        // Network Configuration Errors
        case .networkConfigurationFailed:
            return "Failed to configure network settings"
        case .routeConfigurationFailed:
            return "Failed to configure network routes"
        case .dnsConfigurationFailed:
            return "Failed to configure DNS settings"
        case .interfaceConfigurationFailed:
            return "Failed to configure network interface"
            
        // Packet Processing Errors
        case .packetReadFailed:
            return "Failed to read network packets"
        case .packetWriteFailed:
            return "Failed to write network packets"
        case .packetProcessingFailed:
            return "Failed to process network packets"
        case .invalidPacketFormat:
            return "Invalid network packet format"
            
        // TUN Device Errors
        case .tunDeviceCreationFailed:
            return "Failed to create TUN device"
        case .tunDeviceConfigurationFailed:
            return "Failed to configure TUN device"
        case .tunDeviceStartFailed:
            return "Failed to start TUN device"
        case .tunDeviceStopFailed:
            return "Failed to stop TUN device"
        case .tunDeviceNotAvailable:
            return "TUN device is not available"
            
        // Route Management Errors
        case .routeAddFailed(let details):
            return "Failed to add network route: \(details)"
        case .routeDeleteFailed(let details):
            return "Failed to delete network route: \(details)"
        case .routeConflictDetected(let details):
            return "Network route conflict detected: \(details)"
        case .routeTableAccessFailed:
            return "Failed to access system route table"
            
        // DNS Management Errors
        case .dnsServerConfigurationFailed:
            return "Failed to configure DNS servers"
        case .dnsResolutionFailed:
            return "DNS resolution failed"
        case .dnsServerNotReachable:
            return "DNS server is not reachable"
            
        // Platform Specific Errors
        case .platformUnsupported:
            return "Platform is not supported"
        case .permissionDenied:
            return "Permission denied for network operation"
        case .resourceUnavailable:
            return "Required system resource is unavailable"
        case .systemCallFailed(let details):
            return "System call failed: \(details)"
            
        // Message Handling Errors
        case .messageHandlingFailed:
            return "Failed to handle application message"
        case .invalidMessageFormat:
            return "Invalid message format"
        case .messageProcessingTimeout:
            return "Message processing timeout"
            
        // Memory and Resource Errors
        case .memoryAllocationFailed:
            return "Memory allocation failed"
        case .resourceLimitExceeded:
            return "System resource limit exceeded"
        case .bufferOverflow:
            return "Buffer overflow detected"
        }
    }
    
    /**
     * NAME: description
     *
     * DESCRIPTION:
     *     Provides detailed error descriptions for debugging purposes.
     *
     * RETURNS:
     *     String - Detailed error description
     */
    public var description: String {
        switch self {
        case .configurationParsingFailed(let details):
            return "PlatformError.configurationParsingFailed(\(details))"
        case .routeAddFailed(let details):
            return "PlatformError.routeAddFailed(\(details))"
        case .routeDeleteFailed(let details):
            return "PlatformError.routeDeleteFailed(\(details))"
        case .routeConflictDetected(let details):
            return "PlatformError.routeConflictDetected(\(details))"
        case .systemCallFailed(let details):
            return "PlatformError.systemCallFailed(\(details))"
        default:
            return "PlatformError.\(String(describing: self).components(separatedBy: "(").first ?? "unknown")"
        }
    }
    
    /**
     * NAME: isRecoverable
     *
     * DESCRIPTION:
     *     Determines if the error is recoverable and operation can be retried.
     *
     * RETURNS:
     *     Bool - True if error is recoverable
     */
    public var isRecoverable: Bool {
        switch self {
        // Recoverable errors (can retry)
        case .networkConfigurationFailed,
             .routeConfigurationFailed,
             .dnsConfigurationFailed,
             .packetReadFailed,
             .packetWriteFailed,
             .tunDeviceStartFailed,
             .routeAddFailed,
             .routeDeleteFailed,
             .dnsServerConfigurationFailed,
             .resourceUnavailable,
             .messageProcessingTimeout,
             .memoryAllocationFailed:
            return true
            
        // Non-recoverable errors
        case .configurationInvalid,
             .configurationMissing,
             .platformUnsupported,
             .permissionDenied,
             .invalidPacketFormat,
             .invalidMessageFormat,
             .bufferOverflow:
            return false
            
        // Context-dependent errors
        default:
            return false
        }
    }
    
    /**
     * NAME: errorCode
     *
     * DESCRIPTION:
     *     Provides numeric error codes for programmatic error handling.
     *
     * RETURNS:
     *     Int - Numeric error code
     */
    public var errorCode: Int {
        switch self {
        // Configuration Errors (1000-1099)
        case .configurationInvalid: return 1001
        case .configurationMissing: return 1002
        case .configurationParsingFailed: return 1003
            
        // Tunnel Errors (1100-1199)
        case .tunnelStartFailed: return 1101
        case .tunnelStopFailed: return 1102
        case .tunnelNotStarted: return 1103
        case .tunnelAlreadyStarted: return 1104
            
        // Network Configuration Errors (1200-1299)
        case .networkConfigurationFailed: return 1201
        case .routeConfigurationFailed: return 1202
        case .dnsConfigurationFailed: return 1203
        case .interfaceConfigurationFailed: return 1204
            
        // Packet Processing Errors (1300-1399)
        case .packetReadFailed: return 1301
        case .packetWriteFailed: return 1302
        case .packetProcessingFailed: return 1303
        case .invalidPacketFormat: return 1304
            
        // TUN Device Errors (1400-1499)
        case .tunDeviceCreationFailed: return 1401
        case .tunDeviceConfigurationFailed: return 1402
        case .tunDeviceStartFailed: return 1403
        case .tunDeviceStopFailed: return 1404
        case .tunDeviceNotAvailable: return 1405
            
        // Route Management Errors (1500-1599)
        case .routeAddFailed: return 1501
        case .routeDeleteFailed: return 1502
        case .routeConflictDetected: return 1503
        case .routeTableAccessFailed: return 1504
            
        // DNS Management Errors (1600-1699)
        case .dnsServerConfigurationFailed: return 1601
        case .dnsResolutionFailed: return 1602
        case .dnsServerNotReachable: return 1603
            
        // Platform Specific Errors (1700-1799)
        case .platformUnsupported: return 1701
        case .permissionDenied: return 1702
        case .resourceUnavailable: return 1703
        case .systemCallFailed: return 1704
            
        // Message Handling Errors (1800-1899)
        case .messageHandlingFailed: return 1801
        case .invalidMessageFormat: return 1802
        case .messageProcessingTimeout: return 1803
            
        // Memory and Resource Errors (1900-1999)
        case .memoryAllocationFailed: return 1901
        case .resourceLimitExceeded: return 1902
        case .bufferOverflow: return 1903
        }
    }
}
