/**
 * FILE: PerformanceManager.swift
 *
 * DESCRIPTION:
 *     Global performance monitoring manager and convenience utilities for ItForce VPN client.
 *     Provides centralized access to performance monitoring and built-in metric collectors.
 *     Includes memory pressure detection and automatic threshold monitoring.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation

// MARK: - Performance Manager

/**
 * NAME: PerformanceManager
 *
 * DESCRIPTION:
 *     Global manager for performance monitoring system
 *     Provides centralized configuration and access to performance metrics
 */
public final class PerformanceManager {
    public static let shared = PerformanceManager()
    
    private var monitor: (any PerformanceMonitorProtocol)?

    /**
     * NAME: performanceMonitor
     *
     * DESCRIPTION:
     *     Gets the current performance monitor instance
     *
     * RETURNS:
     *     PerformanceMonitorProtocol? - Current monitor or nil if not configured
     */
    public var performanceMonitor: (any PerformanceMonitorProtocol)? {
        return monitor
    }
    private var thresholds: PerformanceThresholds
    private var isConfigured = false
    
    private init() {
        self.thresholds = PerformanceThresholds()
    }
    
    /**
     * NAME: configure
     *
     * DESCRIPTION:
     *     Configures the performance monitoring system
     *
     * PARAMETERS:
     *     logger - Logger instance
     *     interval - Collection interval in seconds
     *     thresholds - Performance thresholds for alerts
     */
    public func configure(
        logger: LoggerProtocol,
        interval: TimeInterval = 30.0,
        thresholds: PerformanceThresholds? = nil
    ) async {
        guard !isConfigured else { return }
        
        self.monitor = DefaultPerformanceMonitor(logger: logger, collectionInterval: interval)
        if let thresholds = thresholds {
            self.thresholds = thresholds
        }
        
        // Register built-in collectors
        await registerBuiltInCollectors()
        
        isConfigured = true
    }
    
    /**
     * NAME: start
     *
     * DESCRIPTION:
     *     Starts performance monitoring
     */
    public func start() async throws {
        guard let monitor = monitor else {
            throw ItForceError.invalidState(current: "unconfigured", expected: "configured")
        }
        
        try await monitor.start()
    }
    
    /**
     * NAME: stop
     *
     * DESCRIPTION:
     *     Stops performance monitoring
     */
    public func stop() async {
        await monitor?.stop()
    }
    
    /**
     * NAME: getCurrentMetrics
     *
     * DESCRIPTION:
     *     Gets current performance metrics
     *
     * RETURNS:
     *     PerformanceMetrics? - Current metrics or nil if not configured
     */
    public func getCurrentMetrics() async -> PerformanceMetrics? {
        return await monitor?.getCurrentMetrics()
    }
    
    /**
     * NAME: registerCollector
     *
     * DESCRIPTION:
     *     Registers a custom metric collector
     *
     * PARAMETERS:
     *     collector - Metric collector to register
     */
    public func registerCollector(_ collector: MetricCollectorProtocol) async {
        await monitor?.registerCollector(collector)
    }
    
    /**
     * NAME: trackOperation
     *
     * DESCRIPTION:
     *     Tracks the performance of an operation
     *
     * PARAMETERS:
     *     name - Operation name
     *     operation - Operation to track
     *
     * RETURNS:
     *     T - Result of the operation
     */
    public func trackOperation<T>(_ name: String, operation: () async throws -> T) async rethrows -> T {
        guard let monitor = monitor else {
            return try await operation()
        }
        
        return try await monitor.trackOperation(name, operation: operation)
    }
    
    /**
     * NAME: incrementCounter
     *
     * DESCRIPTION:
     *     Increments a performance counter
     *
     * PARAMETERS:
     *     name - Counter name
     *     value - Value to add
     */
    public func incrementCounter(_ name: String, by value: UInt64 = 1) async {
        await monitor?.incrementCounter(name, by: value)
    }
    
    /**
     * NAME: setGauge
     *
     * DESCRIPTION:
     *     Sets a gauge metric value
     *
     * PARAMETERS:
     *     name - Gauge name
     *     value - Value to set
     */
    public func setGauge(_ name: String, value: Any) async {
        await monitor?.setGauge(name, value: value)
    }
    
    /**
     * NAME: checkThresholds
     *
     * DESCRIPTION:
     *     Checks if current metrics exceed configured thresholds
     *
     * RETURNS:
     *     [String] - List of threshold violations
     */
    public func checkThresholds() async -> [String] {
        guard let metrics = await getCurrentMetrics() else { return [] }
        
        var violations: [String] = []
        
        // Check memory threshold
        if metrics.memory.usagePercentage > thresholds.memoryUsagePercent {
            violations.append("Memory usage (\(String(format: "%.1f", metrics.memory.usagePercentage))%) exceeds threshold (\(thresholds.memoryUsagePercent)%)")
        }
        
        // Check error rate threshold
        if metrics.application.errorRate > thresholds.errorRatePercent {
            violations.append("Error rate (\(String(format: "%.1f", metrics.application.errorRate))%) exceeds threshold (\(thresholds.errorRatePercent)%)")
        }
        
        // Check response time threshold
        if metrics.application.averageResponseTime > thresholds.responseTimeSeconds {
            violations.append("Response time (\(String(format: "%.2f", metrics.application.averageResponseTime))s) exceeds threshold (\(thresholds.responseTimeSeconds)s)")
        }
        
        return violations
    }
    
    // MARK: - Private Methods
    
    private func registerBuiltInCollectors() async {
        // Register system resource collector
        await monitor?.registerCollector(SystemResourceCollector())
        
        // Register network statistics collector
        await monitor?.registerCollector(NetworkStatsCollector())
    }
}

// MARK: - Performance Thresholds

/**
 * NAME: PerformanceThresholds
 *
 * DESCRIPTION:
 *     Configuration for performance threshold monitoring
 */
public struct PerformanceThresholds {
    public let memoryUsagePercent: Double
    public let errorRatePercent: Double
    public let responseTimeSeconds: TimeInterval
    
    public init(
        memoryUsagePercent: Double = 80.0,
        errorRatePercent: Double = 5.0,
        responseTimeSeconds: TimeInterval = 5.0
    ) {
        self.memoryUsagePercent = memoryUsagePercent
        self.errorRatePercent = errorRatePercent
        self.responseTimeSeconds = responseTimeSeconds
    }
    
    // Predefined threshold configurations
    public static let strict = PerformanceThresholds(
        memoryUsagePercent: 60.0,
        errorRatePercent: 2.0,
        responseTimeSeconds: 2.0
    )
    
    public static let relaxed = PerformanceThresholds(
        memoryUsagePercent: 90.0,
        errorRatePercent: 10.0,
        responseTimeSeconds: 10.0
    )
    
    public static let networkExtension = PerformanceThresholds(
        memoryUsagePercent: 70.0,  // NetworkExtension has strict memory limits
        errorRatePercent: 3.0,
        responseTimeSeconds: 3.0
    )
}

// MARK: - Built-in Metric Collectors

/**
 * NAME: SystemResourceCollector
 *
 * DESCRIPTION:
 *     Collects system resource metrics
 */
public struct SystemResourceCollector: MetricCollectorProtocol {
    public let name = "system"
    
    public func collect() async -> [String: Any] {
        var metrics: [String: Any] = [:]
        
        // Collect process info
        let processInfo = ProcessInfo.processInfo
        metrics["system_uptime"] = processInfo.systemUptime
        metrics["process_id"] = processInfo.processIdentifier
        
        // Collect thermal state (iOS only)
        #if os(iOS)
        metrics["thermal_state"] = processInfo.thermalState.rawValue
        #endif
        
        // Collect low power mode (iOS only)
        #if os(iOS)
        metrics["low_power_mode"] = processInfo.isLowPowerModeEnabled
        #endif
        
        return metrics
    }
}

/**
 * NAME: NetworkStatsCollector
 *
 * DESCRIPTION:
 *     Collects network-related statistics
 */
public struct NetworkStatsCollector: MetricCollectorProtocol {
    public let name = "network"
    
    public func collect() async -> [String: Any] {
        var metrics: [String: Any] = [:]
        
        // These would be populated by the actual network layer
        // This is a placeholder implementation
        metrics["active_connections"] = 0
        metrics["connection_attempts"] = 0
        metrics["connection_failures"] = 0
        metrics["dns_resolution_time"] = 0.0
        
        return metrics
    }
}

// MARK: - Global Convenience Functions

/**
 * Global convenience functions for performance tracking
 */

public func trackPerformance<T>(
    _ operationName: String,
    operation: () async throws -> T
) async rethrows -> T {
    return try await PerformanceManager.shared.trackOperation(operationName, operation: operation)
}

public func incrementPerformanceCounter(
    _ name: String,
    by value: UInt64 = 1
) async {
    await PerformanceManager.shared.incrementCounter(name, by: value)
}

public func setPerformanceGauge(
    _ name: String,
    value: Any
) async {
    await PerformanceManager.shared.setGauge(name, value: value)
}

public func getCurrentPerformanceMetrics() async -> PerformanceMetrics? {
    return await PerformanceManager.shared.getCurrentMetrics()
}

// MARK: - Performance Monitoring Extensions

extension PerformanceMetrics {
    /**
     * NAME: summary
     *
     * DESCRIPTION:
     *     Gets a human-readable summary of key metrics
     *
     * RETURNS:
     *     String - Formatted summary
     */
    public var summary: String {
        let memoryMB = memory.usedBytes / 1024 / 1024
        let uptimeFormatted = String(format: "%.1f", uptime / 60) // minutes
        
        return """
        Performance Summary:
        - Uptime: \(uptimeFormatted) minutes
        - Memory: \(memoryMB) MB (\(String(format: "%.1f", memory.usagePercentage))%)
        - Network: ↓\(formatBytes(network.bytesReceived)) ↑\(formatBytes(network.bytesSent))
        - Requests: \(application.requestCount) (errors: \(application.errorCount))
        - Error Rate: \(String(format: "%.1f", application.errorRate))%
        - Avg Response: \(String(format: "%.2f", application.averageResponseTime))s
        """
    }
    
    private func formatBytes(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .binary
        return formatter.string(fromByteCount: Int64(bytes))
    }
}

// MARK: - Notification Names

extension Notification.Name {
    public static let performanceThresholdExceeded = Notification.Name("PerformanceThresholdExceeded")
    public static let performanceMetricsUpdated = Notification.Name("PerformanceMetricsUpdated")
}
