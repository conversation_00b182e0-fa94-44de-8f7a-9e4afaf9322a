/**
 * FILE: PerformanceMonitor.swift
 *
 * DESCRIPTION:
 *     Performance monitoring system for iOS/macOS ItForce VPN client.
 *     Optimized for NetworkExtension environment with minimal overhead.
 *     Monitors memory usage, CPU, network metrics, and custom application metrics.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import OSLog

// MARK: - Performance Metrics

/**
 * NAME: PerformanceMetrics
 *
 * DESCRIPTION:
 *     Container for all performance metrics data
 *
 * PROPERTIES:
 *     timestamp - When metrics were collected
 *     uptime - Application uptime
 *     memory - Memory usage metrics
 *     network - Network traffic metrics
 *     application - Application-specific metrics
 *     custom - Custom metrics from collectors
 */
public struct PerformanceMetrics {
    public let timestamp: Date
    public let uptime: TimeInterval
    public let memory: MemoryMetrics
    public let network: NetworkMetrics
    public let application: ApplicationMetrics
    public let custom: [String: Any]
    
    public init(
        timestamp: Date = Date(),
        uptime: TimeInterval,
        memory: MemoryMetrics,
        network: NetworkMetrics,
        application: ApplicationMetrics,
        custom: [String: Any] = [:]
    ) {
        self.timestamp = timestamp
        self.uptime = uptime
        self.memory = memory
        self.network = network
        self.application = application
        self.custom = custom
    }
}

/**
 * NAME: MemoryMetrics
 *
 * DESCRIPTION:
 *     Memory usage metrics optimized for NetworkExtension monitoring
 */
public struct MemoryMetrics {
    public let usedBytes: UInt64
    public let availableBytes: UInt64
    public let peakUsageBytes: UInt64
    public let usagePercentage: Double
    
    public init(usedBytes: UInt64, availableBytes: UInt64, peakUsageBytes: UInt64) {
        self.usedBytes = usedBytes
        self.availableBytes = availableBytes
        self.peakUsageBytes = peakUsageBytes
        self.usagePercentage = Double(usedBytes) / Double(usedBytes + availableBytes) * 100.0
    }
}

/**
 * NAME: NetworkMetrics
 *
 * DESCRIPTION:
 *     Network traffic and performance metrics
 */
public struct NetworkMetrics {
    public let bytesReceived: UInt64
    public let bytesSent: UInt64
    public let packetsReceived: UInt64
    public let packetsSent: UInt64
    public let connectionCount: Int
    public let averageLatency: TimeInterval
    
    public init(
        bytesReceived: UInt64 = 0,
        bytesSent: UInt64 = 0,
        packetsReceived: UInt64 = 0,
        packetsSent: UInt64 = 0,
        connectionCount: Int = 0,
        averageLatency: TimeInterval = 0
    ) {
        self.bytesReceived = bytesReceived
        self.bytesSent = bytesSent
        self.packetsReceived = packetsReceived
        self.packetsSent = packetsSent
        self.connectionCount = connectionCount
        self.averageLatency = averageLatency
    }
}

/**
 * NAME: ApplicationMetrics
 *
 * DESCRIPTION:
 *     Application-specific performance metrics
 */
public struct ApplicationMetrics {
    public let requestCount: UInt64
    public let errorCount: UInt64
    public let averageResponseTime: TimeInterval
    public let activeOperations: Int
    
    public var errorRate: Double {
        guard requestCount > 0 else { return 0.0 }
        return Double(errorCount) / Double(requestCount) * 100.0
    }
    
    public init(
        requestCount: UInt64 = 0,
        errorCount: UInt64 = 0,
        averageResponseTime: TimeInterval = 0,
        activeOperations: Int = 0
    ) {
        self.requestCount = requestCount
        self.errorCount = errorCount
        self.averageResponseTime = averageResponseTime
        self.activeOperations = activeOperations
    }
}

// MARK: - Metric Collector Protocol

/**
 * NAME: MetricCollectorProtocol
 *
 * DESCRIPTION:
 *     Protocol for custom metric collectors
 */
public protocol MetricCollectorProtocol {
    var name: String { get }
    func collect() async -> [String: Any]
}

// MARK: - Performance Monitor Protocol

/**
 * NAME: PerformanceMonitorProtocol
 *
 * DESCRIPTION:
 *     Protocol for performance monitoring system
 */
public protocol PerformanceMonitorProtocol: Actor {
    func start() async throws
    func stop() async
    func getCurrentMetrics() async -> PerformanceMetrics
    func registerCollector(_ collector: MetricCollectorProtocol) async
    func removeCollector(name: String) async
    func incrementCounter(_ name: String, by value: UInt64) async
    func setGauge(_ name: String, value: Any) async
    func trackOperation<T>(_ name: String, operation: () async throws -> T) async rethrows -> T
}

// MARK: - Default Performance Monitor

/**
 * NAME: DefaultPerformanceMonitor
 *
 * DESCRIPTION:
 *     Default implementation of performance monitoring using Swift Actor for thread safety
 *     Optimized for NetworkExtension environment with minimal memory footprint
 */
public actor DefaultPerformanceMonitor: PerformanceMonitorProtocol {
    private let logger: LoggerProtocol
    private let collectionInterval: TimeInterval
    private let startTime: Date
    
    private var isRunning = false
    private var collectionTask: Task<Void, Never>?
    private var collectors: [String: MetricCollectorProtocol] = [:]
    
    // Metrics storage
    private var currentMetrics: PerformanceMetrics
    private var customCounters: [String: UInt64] = [:]
    private var customGauges: [String: Any] = [:]
    private var peakMemoryUsage: UInt64 = 0
    
    // Operation tracking
    private var activeOperations: Set<String> = []
    private var operationStats: [String: OperationStats] = [:]
    
    public init(logger: LoggerProtocol, collectionInterval: TimeInterval = 30.0) {
        self.logger = logger
        self.collectionInterval = collectionInterval
        self.startTime = Date()
        
        // Initialize with empty metrics
        self.currentMetrics = PerformanceMetrics(
            uptime: 0,
            memory: MemoryMetrics(usedBytes: 0, availableBytes: 0, peakUsageBytes: 0),
            network: NetworkMetrics(),
            application: ApplicationMetrics()
        )
    }
    
    /**
     * NAME: start
     *
     * DESCRIPTION:
     *     Starts the performance monitoring system
     */
    public func start() async throws {
        guard !isRunning else { return }
        
        logger.logInfo("Starting performance monitor",
            fields: LogField.double("interval_seconds", collectionInterval)
        )
        
        isRunning = true
        
        // Start collection task
        collectionTask = Task { [weak self] in
            await self?.collectionLoop()
        }
    }
    
    /**
     * NAME: stop
     *
     * DESCRIPTION:
     *     Stops the performance monitoring system
     */
    public func stop() async {
        guard isRunning else { return }
        
        logger.logInfo("Stopping performance monitor")
        
        isRunning = false
        collectionTask?.cancel()
        collectionTask = nil
    }
    
    /**
     * NAME: getCurrentMetrics
     *
     * DESCRIPTION:
     *     Gets the current performance metrics
     *
     * RETURNS:
     *     PerformanceMetrics - Current metrics snapshot
     */
    public func getCurrentMetrics() async -> PerformanceMetrics {
        return currentMetrics
    }
    
    /**
     * NAME: registerCollector
     *
     * DESCRIPTION:
     *     Registers a custom metric collector
     *
     * PARAMETERS:
     *     collector - Metric collector to register
     */
    public func registerCollector(_ collector: MetricCollectorProtocol) async {
        collectors[collector.name] = collector
        logger.logDebug("Registered metric collector",
            fields: LogField.string("name", collector.name)
        )
    }
    
    /**
     * NAME: removeCollector
     *
     * DESCRIPTION:
     *     Removes a metric collector
     *
     * PARAMETERS:
     *     name - Name of collector to remove
     */
    public func removeCollector(name: String) async {
        collectors.removeValue(forKey: name)
        logger.logDebug("Removed metric collector",
            fields: LogField.string("name", name)
        )
    }
    
    /**
     * NAME: incrementCounter
     *
     * DESCRIPTION:
     *     Increments a custom counter metric
     *
     * PARAMETERS:
     *     name - Counter name
     *     value - Value to add
     */
    public func incrementCounter(_ name: String, by value: UInt64 = 1) async {
        customCounters[name, default: 0] += value
    }
    
    /**
     * NAME: setGauge
     *
     * DESCRIPTION:
     *     Sets a custom gauge metric
     *
     * PARAMETERS:
     *     name - Gauge name
     *     value - Value to set
     */
    public func setGauge(_ name: String, value: Any) async {
        customGauges[name] = value
    }
    
    /**
     * NAME: trackOperation
     *
     * DESCRIPTION:
     *     Tracks the performance of an operation
     *
     * PARAMETERS:
     *     name - Operation name
     *     operation - Operation to track
     *
     * RETURNS:
     *     T - Result of the operation
     */
    public func trackOperation<T>(_ name: String, operation: () async throws -> T) async rethrows -> T {
        let startTime = Date()
        activeOperations.insert(name)
        
        defer {
            let duration = Date().timeIntervalSince(startTime)
            Task { [weak self] in
                await self?.recordOperationCompletion(name: name, duration: duration, success: true)
            }
        }
        
        do {
            let result = try await operation()
            return result
        } catch {
            let duration = Date().timeIntervalSince(startTime)
            await recordOperationCompletion(name: name, duration: duration, success: false)
            throw error
        }
    }
    
    // MARK: - Private Methods
    
    private func collectionLoop() async {
        while isRunning {
            await collectMetrics()
            
            // Sleep for collection interval
            try? await Task.sleep(nanoseconds: UInt64(collectionInterval * 1_000_000_000))
        }
    }
    
    private func collectMetrics() async {
        let memoryMetrics = collectMemoryMetrics()
        let networkMetrics = collectNetworkMetrics()
        let applicationMetrics = collectApplicationMetrics()
        let customMetrics = await collectCustomMetrics()
        
        currentMetrics = PerformanceMetrics(
            uptime: Date().timeIntervalSince(startTime),
            memory: memoryMetrics,
            network: networkMetrics,
            application: applicationMetrics,
            custom: customMetrics
        )
        
        logger.logDebug("Collected performance metrics",
            fields: LogField.int("memory_mb", Int(memoryMetrics.usedBytes / 1024 / 1024)),
            LogField.double("memory_percent", memoryMetrics.usagePercentage),
            LogField.int("active_operations", activeOperations.count)
        )
    }
    
    private func collectMemoryMetrics() -> MemoryMetrics {
        // Use mach task info for accurate memory usage in NetworkExtension
        let usedBytes = getMemoryUsage()
        peakMemoryUsage = max(peakMemoryUsage, usedBytes)
        
        // Estimate available memory (simplified for NetworkExtension)
        let availableBytes: UInt64 = 50 * 1024 * 1024 // 50MB typical limit
        
        return MemoryMetrics(
            usedBytes: usedBytes,
            availableBytes: availableBytes,
            peakUsageBytes: peakMemoryUsage
        )
    }
    
    private func collectNetworkMetrics() -> NetworkMetrics {
        // Network metrics would be collected from connection managers
        // This is a placeholder - actual implementation would integrate with network layer
        return NetworkMetrics(
            bytesReceived: customCounters["network_bytes_received"] ?? 0,
            bytesSent: customCounters["network_bytes_sent"] ?? 0,
            packetsReceived: customCounters["network_packets_received"] ?? 0,
            packetsSent: customCounters["network_packets_sent"] ?? 0,
            connectionCount: customGauges["connection_count"] as? Int ?? 0,
            averageLatency: customGauges["average_latency"] as? TimeInterval ?? 0
        )
    }
    
    private func collectApplicationMetrics() -> ApplicationMetrics {
        let totalRequests = operationStats.values.reduce(0) { $0 + $1.count }
        let totalErrors = operationStats.values.reduce(0) { $0 + $1.errorCount }
        let avgResponseTime = operationStats.values.isEmpty ? 0 :
            operationStats.values.reduce(0) { $0 + $1.averageDuration } / Double(operationStats.count)
        
        return ApplicationMetrics(
            requestCount: totalRequests,
            errorCount: totalErrors,
            averageResponseTime: avgResponseTime,
            activeOperations: activeOperations.count
        )
    }
    
    private func collectCustomMetrics() async -> [String: Any] {
        var allCustomMetrics: [String: Any] = [:]
        
        // Add custom counters and gauges
        for (key, value) in customCounters {
            allCustomMetrics[key] = value
        }
        for (key, value) in customGauges {
            allCustomMetrics[key] = value
        }
        
        // Collect from registered collectors
        for collector in collectors.values {
            let collectorMetrics = await collector.collect()
            for (key, value) in collectorMetrics {
                allCustomMetrics["\(collector.name)_\(key)"] = value
            }
        }
        
        return allCustomMetrics
    }
    
    private func recordOperationCompletion(name: String, duration: TimeInterval, success: Bool) async {
        activeOperations.remove(name)
        
        var stats = operationStats[name] ?? OperationStats()
        stats.count += 1
        if !success {
            stats.errorCount += 1
        }
        stats.totalDuration += duration
        stats.averageDuration = stats.totalDuration / Double(stats.count)
        
        operationStats[name] = stats
    }
    
    private func getMemoryUsage() -> UInt64 {
        // Simplified memory usage calculation
        // In a real implementation, you'd use mach task info
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        return result == KERN_SUCCESS ? UInt64(info.resident_size) : 0
    }
}

// MARK: - Supporting Types

private struct OperationStats {
    var count: UInt64 = 0
    var errorCount: UInt64 = 0
    var totalDuration: TimeInterval = 0
    var averageDuration: TimeInterval = 0
}
