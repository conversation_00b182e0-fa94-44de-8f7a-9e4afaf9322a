/**
 * FILE: ErrorSystem.swift
 *
 * DESCRIPTION:
 *     Core error handling system for iOS/macOS ItForce VPN client.
 *     Provides structured error types, error codes, severity levels, and context management.
 *     Designed to be compatible with Go backend error system while leveraging Swift Error protocol.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation

// MARK: - Error Code Definition

/**
 * NAME: ErrorCode
 *
 * DESCRIPTION:
 *     Defines standardized error codes organized by category
 *     Compatible with Go backend error code system
 */
public enum ErrorCode: Int, CaseIterable {
    // General errors (0-99)
    case unknown = 0
    case `internal` = 1
    case invalidArgument = 2
    case notImplemented = 3
    case unavailable = 4
    case timeout = 5
    case canceled = 6
    case alreadyExists = 7
    case notFound = 8
    case permissionDenied = 9
    case resourceExhausted = 10
    case failedPrecondition = 11
    case aborted = 12
    case outOfRange = 13
    case invalidState = 14
    
    // Network errors (1000-1999)
    case networkUnreachable = 1000
    case networkTimeout = 1001
    case networkDNSFailure = 1002
    case networkConnectionReset = 1003
    case networkConnectionClosed = 1004
    case networkProxyError = 1005
    case networkTLSError = 1006
    
    // Authentication errors (2000-2999)
    case authInvalidCredentials = 2000
    case authExpiredCredentials = 2001
    case authRateLimited = 2002
    case authAccountLocked = 2003
    case authTokenInvalid = 2004
    case authTokenExpired = 2005
    case authMissingCredentials = 2006
    
    // Tunnel errors (3000-3999)
    case tunnelInitFailed = 3000
    case tunnelClosedUnexpected = 3001
    case tunnelPacketDropped = 3002
    case tunnelDeviceError = 3003
    case tunnelRouteError = 3004
    case tunnelDNSError = 3005
    case tunnelEncryptionError = 3006
    
    // Configuration errors (4000-4999)
    case configInvalid = 4000
    case configMissing = 4001
    case configPermissionDenied = 4002
    case configReadError = 4003
    case configWriteError = 4004
    case configParseError = 4005
    case configValidationError = 4006
    
    // Platform errors (5000-5999)
    case platformUnsupported = 5000
    case platformPermissionDenied = 5001
    case platformDriverError = 5002
    case platformSystemError = 5003
    case platformResourceError = 5004
    case platformNetworkError = 5005
    case platformFirewallError = 5006
    
    // Protocol errors (6000-6999)
    case protocolVersionMismatch = 6000
    case protocolInvalidFormat = 6001
    case protocolUnsupported = 6002
    case protocolHandshakeFailed = 6003
    case protocolEncryptionError = 6004
    case protocolDecryptionError = 6005
    case protocolAuthError = 6006
}

// MARK: - Error Category Definition

/**
 * NAME: ErrorCategory
 *
 * DESCRIPTION:
 *     Defines error categories for grouping related error codes
 */
public enum ErrorCategory: String, CaseIterable {
    case general = "General"
    case network = "Network"
    case authentication = "Authentication"
    case tunnel = "Tunnel"
    case configuration = "Configuration"
    case platform = "Platform"
    case protocolLayer = "Protocol"
}

// MARK: - Error Severity Definition

/**
 * NAME: ErrorSeverity
 *
 * DESCRIPTION:
 *     Defines the severity level of an error for appropriate handling
 */
public enum ErrorSeverity: Int, CaseIterable, Comparable {
    case info = 0
    case warning = 1
    case error = 2
    case critical = 3
    case fatal = 4
    
    public static func < (lhs: ErrorSeverity, rhs: ErrorSeverity) -> Bool {
        return lhs.rawValue < rhs.rawValue
    }
}

// MARK: - Error Code Extensions

extension ErrorCode {
    /**
     * NAME: category
     *
     * DESCRIPTION:
     *     Gets the category for this error code
     *
     * RETURNS:
     *     ErrorCategory - The category this error belongs to
     */
    public var category: ErrorCategory {
        switch self.rawValue {
        case 1000..<2000:
            return .network
        case 2000..<3000:
            return .authentication
        case 3000..<4000:
            return .tunnel
        case 4000..<5000:
            return .configuration
        case 5000..<6000:
            return .platform
        case 6000..<7000:
            return .protocolLayer
        default:
            return .general
        }
    }
    
    /**
     * NAME: defaultSeverity
     *
     * DESCRIPTION:
     *     Gets the default severity level for this error code
     *
     * RETURNS:
     *     ErrorSeverity - Default severity level
     */
    public var defaultSeverity: ErrorSeverity {
        switch self {
        case .unknown, .notImplemented:
            return .warning
        case .timeout, .canceled, .unavailable:
            return .warning
        case .networkTimeout, .networkDNSFailure:
            return .warning
        case .authRateLimited, .authTokenExpired:
            return .warning
        case .tunnelPacketDropped:
            return .info
        case .configMissing, .configInvalid:
            return .error
        case .platformUnsupported, .platformPermissionDenied:
            return .critical
        case .tunnelInitFailed, .tunnelDeviceError:
            return .critical
        case .authInvalidCredentials, .authAccountLocked:
            return .error
        case .protocolVersionMismatch, .protocolHandshakeFailed:
            return .error
        default:
            return .error
        }
    }
    
    /**
     * NAME: localizedDescription
     *
     * DESCRIPTION:
     *     Gets a localized description for this error code
     *
     * RETURNS:
     *     String - Localized error description
     */
    public var localizedDescription: String {
        switch self {
        // General errors
        case .unknown: return "Unknown error"
        case .internal: return "Internal error"
        case .invalidArgument: return "Invalid argument"
        case .notImplemented: return "Feature not implemented"
        case .unavailable: return "Service unavailable"
        case .timeout: return "Operation timed out"
        case .canceled: return "Operation canceled"
        case .alreadyExists: return "Resource already exists"
        case .notFound: return "Resource not found"
        case .permissionDenied: return "Permission denied"
        case .resourceExhausted: return "Resource exhausted"
        case .failedPrecondition: return "Failed precondition"
        case .aborted: return "Operation aborted"
        case .outOfRange: return "Value out of range"
        case .invalidState: return "Invalid state"
        
        // Network errors
        case .networkUnreachable: return "Network unreachable"
        case .networkTimeout: return "Network timeout"
        case .networkDNSFailure: return "DNS resolution failed"
        case .networkConnectionReset: return "Connection reset"
        case .networkConnectionClosed: return "Connection closed"
        case .networkProxyError: return "Proxy error"
        case .networkTLSError: return "TLS/SSL error"
        
        // Authentication errors
        case .authInvalidCredentials: return "Invalid credentials"
        case .authExpiredCredentials: return "Credentials expired"
        case .authRateLimited: return "Authentication rate limited"
        case .authAccountLocked: return "Account locked"
        case .authTokenInvalid: return "Invalid token"
        case .authTokenExpired: return "Token expired"
        case .authMissingCredentials: return "Missing credentials"
        
        // Tunnel errors
        case .tunnelInitFailed: return "Tunnel initialization failed"
        case .tunnelClosedUnexpected: return "Tunnel closed unexpectedly"
        case .tunnelPacketDropped: return "Packet dropped"
        case .tunnelDeviceError: return "Tunnel device error"
        case .tunnelRouteError: return "Routing error"
        case .tunnelDNSError: return "Tunnel DNS error"
        case .tunnelEncryptionError: return "Encryption error"
        
        // Configuration errors
        case .configInvalid: return "Invalid configuration"
        case .configMissing: return "Configuration missing"
        case .configPermissionDenied: return "Configuration access denied"
        case .configReadError: return "Configuration read error"
        case .configWriteError: return "Configuration write error"
        case .configParseError: return "Configuration parse error"
        case .configValidationError: return "Configuration validation error"
        
        // Platform errors
        case .platformUnsupported: return "Platform not supported"
        case .platformPermissionDenied: return "Platform permission denied"
        case .platformDriverError: return "Platform driver error"
        case .platformSystemError: return "Platform system error"
        case .platformResourceError: return "Platform resource error"
        case .platformNetworkError: return "Platform network error"
        case .platformFirewallError: return "Platform firewall error"
        
        // Protocol errors
        case .protocolVersionMismatch: return "Protocol version mismatch"
        case .protocolInvalidFormat: return "Invalid protocol format"
        case .protocolUnsupported: return "Protocol not supported"
        case .protocolHandshakeFailed: return "Protocol handshake failed"
        case .protocolEncryptionError: return "Protocol encryption error"
        case .protocolDecryptionError: return "Protocol decryption error"
        case .protocolAuthError: return "Protocol authentication error"
        }
    }
}
