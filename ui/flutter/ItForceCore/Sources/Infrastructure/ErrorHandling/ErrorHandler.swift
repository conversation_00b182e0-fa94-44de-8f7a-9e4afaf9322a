/**
 * FILE: <PERSON>rrorHandler.swift
 *
 * DESCRIPTION:
 *     Centralized error handling utilities for ItForce VPN client.
 *     Focuses on error logging, classification, notification, and monitoring.
 *     Business recovery logic should be handled by respective business layers.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation

// MARK: - Error Handler Protocol

/**
 * NAME: ErrorHandlerProtocol
 *
 * DESCRIPTION:
 *     Protocol for centralized error handling without business recovery logic
 */
public protocol ErrorHandlerProtocol {
    func handle(_ error: Error, context: String, file: String, line: Int, function: String)
    func handleAsync(_ error: Error, context: String, file: String, line: Int, function: String) async
}

// MARK: - Default Error Handler

/**
 * NAME: DefaultErrorHandler
 *
 * DESCRIPTION:
 *     Default implementation focusing on error logging, classification, and notification
 *
 * PROPERTIES:
 *     logger - Logger instance for error reporting
 *     errorStatistics - Error occurrence statistics
 */
public final class DefaultErrorHandler: ErrorHandlerProtocol {
    private let logger: LoggerProtocol
    private var errorStatistics: [ErrorCategory: Int] = [:]
    private let statisticsQueue = DispatchQueue(label: "com.panabit.error.statistics", qos: .utility)

    public init(logger: LoggerProtocol) {
        self.logger = logger
        initializeStatistics()
    }
    
    /**
     * NAME: handle
     *
     * DESCRIPTION:
     *     Handles an error synchronously with logging, classification, and notification
     *
     * PARAMETERS:
     *     error - Error to handle
     *     context - Context description
     *     file - Source file
     *     line - Source line
     *     function - Source function
     */
    public func handle(
        _ error: Error,
        context: String,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        let itForceError = convertToItForceError(error, context: context)

        // Update error statistics
        updateErrorStatistics(itForceError)

        // Log the error with appropriate level
        logError(itForceError, context: context, file: file, line: line, function: function)

        // Notify error observers
        notifyErrorObservers(itForceError, context: context)
    }
    
    /**
     * NAME: handleAsync
     *
     * DESCRIPTION:
     *     Handles an error asynchronously with logging, classification, and notification
     *
     * PARAMETERS:
     *     error - Error to handle
     *     context - Context description
     *     file - Source file
     *     line - Source line
     *     function - Source function
     */
    public func handleAsync(
        _ error: Error,
        context: String,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) async {
        let itForceError = convertToItForceError(error, context: context)

        // Update error statistics
        updateErrorStatistics(itForceError)

        // Log the error
        logError(itForceError, context: context, file: file, line: line, function: function)

        // Notify error observers
        notifyErrorObservers(itForceError, context: context)
    }
    
    /**
     * NAME: getErrorStatistics
     *
     * DESCRIPTION:
     *     Gets current error statistics by category
     *
     * RETURNS:
     *     [ErrorCategory: Int] - Error count by category
     */
    public func getErrorStatistics() -> [ErrorCategory: Int] {
        return statisticsQueue.sync {
            return errorStatistics
        }
    }

    /**
     * NAME: resetErrorStatistics
     *
     * DESCRIPTION:
     *     Resets error statistics counters
     */
    public func resetErrorStatistics() {
        statisticsQueue.sync {
            errorStatistics = [:]
            initializeStatistics()
        }
    }
    
    // MARK: - Private Methods

    private func convertToItForceError(_ error: Error, context: String) -> ItForceError {
        if let itForceError = error as? ItForceError {
            return itForceError
        }
        return ItForceError.wrap(error, context: context)
    }

    private func updateErrorStatistics(_ error: ItForceError) {
        statisticsQueue.sync {
            errorStatistics[error.category, default: 0] += 1
        }
    }

    private func initializeStatistics() {
        for category in ErrorCategory.allCases {
            errorStatistics[category] = 0
        }
    }

    private func logError(
        _ error: ItForceError,
        context: String,
        file: String,
        line: Int,
        function: String
    ) {
        let fields = [
            LogField.string("context", context),
            LogField.string("category", error.category.rawValue),
            LogField.string("severity", String(describing: error.severity)),
            LogField.bool("recoverable", error.isRecoverable),
            LogField.string("error_description", error.description)
        ]

        switch error.severity {
        case .info:
            logger.log(level: .info, message: "Error occurred", fields: fields, file: file, line: line, function: function)
        case .warning:
            logger.log(level: .warning, message: "Warning occurred", fields: fields, file: file, line: line, function: function)
        case .error:
            logger.log(level: .error, message: "Error occurred", fields: fields, file: file, line: line, function: function)
        case .critical, .fatal:
            logger.log(level: .critical, message: "Critical error occurred", fields: fields, file: file, line: line, function: function)
        }
    }

    private func notifyErrorObservers(_ error: ItForceError, context: String) {
        // Post notification for error observers
        NotificationCenter.default.post(
            name: .itForceErrorOccurred,
            object: nil,
            userInfo: [
                "error": error,
                "context": context,
                "timestamp": Date(),
                "category": error.category.rawValue,
                "severity": error.severity.rawValue
            ]
        )
    }
}

// MARK: - Global Error Handler

/**
 * NAME: ErrorManager
 *
 * DESCRIPTION:
 *     Global error manager singleton for centralized error handling
 */
public final class ErrorManager {
    public static let shared = ErrorManager()
    
    private var errorHandler: ErrorHandlerProtocol?
    
    private init() {}
    
    public func configure(with handler: ErrorHandlerProtocol) {
        self.errorHandler = handler
    }
    
    public func handle(
        _ error: Error,
        context: String,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        errorHandler?.handle(error, context: context, file: file, line: line, function: function)
    }
    
    public func handleAsync(
        _ error: Error,
        context: String,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) async {
        await errorHandler?.handleAsync(error, context: context, file: file, line: line, function: function)
    }
}

// MARK: - Notification Names

extension Notification.Name {
    public static let itForceErrorOccurred = Notification.Name("ItForceErrorOccurred")
}

// MARK: - Global Convenience Functions

public func handleError(
    _ error: Error,
    context: String,
    file: String = #file,
    line: Int = #line,
    function: String = #function
) {
    ErrorManager.shared.handle(error, context: context, file: file, line: line, function: function)
}

public func handleErrorAsync(
    _ error: Error,
    context: String,
    file: String = #file,
    line: Int = #line,
    function: String = #function
) async {
    await ErrorManager.shared.handleAsync(error, context: context, file: file, line: line, function: function)
}
