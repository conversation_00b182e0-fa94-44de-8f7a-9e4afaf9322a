/**
 * FILE: ItForceError.swift
 *
 * DESCRIPTION:
 *     Swift-native error handling system for ItForce VPN client.
 *     Leverages Swift Error protocol and enum-based error types for type safety and performance.
 *     Provides structured error information with context and localized descriptions.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation

// MARK: - Core Error Types

/**
 * NAME: ItForceError
 *
 * DESCRIPTION:
 *     Main error enum for ItForce VPN client using <PERSON>'s native Error protocol
 *     Organized by functional domains for clear error categorization
 */
public enum ItForceError: Error, LocalizedError, CustomStringConvertible {
    // Network errors
    case networkTimeout
    case networkUnreachable
    case dnsResolutionFailed
    case connectionFailed(reason: String)
    case connectionLost
    
    // Authentication errors
    case invalidCredentials
    case authenticationFailed(message: String)
    case tokenExpired
    case unauthorized
    
    // Tunnel errors
    case tunnelInitializationFailed(reason: String)
    case tunnelDeviceError(details: String)
    case routingError(description: String)
    case encryptionFailed
    case packetProcessingError
    
    // Configuration errors
    case invalidConfiguration(field: String)
    case configurationMissing
    case configurationParseError(details: String)
    
    // Platform errors
    case platformNotSupported
    case permissionDenied(permission: String)
    case systemError(code: Int, message: String)
    case networkExtensionError(underlying: Error)
    
    // Protocol errors
    case protocolVersionMismatch(expected: String, received: String)
    case invalidPacketFormat
    case handshakeFailed
    
    // General errors
    case internalError(description: String)
    case operationCanceled
    case timeout(operation: String)
    case invalidState(current: String, expected: String)
    
    // MARK: - LocalizedError Implementation
    
    public var errorDescription: String? {
        switch self {
        // Network errors
        case .networkTimeout:
            return "Network operation timed out"
        case .networkUnreachable:
            return "Network is unreachable"
        case .dnsResolutionFailed:
            return "DNS resolution failed"
        case .connectionFailed(let reason):
            return "Connection failed: \(reason)"
        case .connectionLost:
            return "Connection lost"
            
        // Authentication errors
        case .invalidCredentials:
            return "Invalid username or password"
        case .authenticationFailed(let message):
            return "Authentication failed: \(message)"
        case .tokenExpired:
            return "Authentication token has expired"
        case .unauthorized:
            return "Access denied"
            
        // Tunnel errors
        case .tunnelInitializationFailed(let reason):
            return "Tunnel initialization failed: \(reason)"
        case .tunnelDeviceError(let details):
            return "Tunnel device error: \(details)"
        case .routingError(let description):
            return "Routing error: \(description)"
        case .encryptionFailed:
            return "Data encryption failed"
        case .packetProcessingError:
            return "Packet processing error"
            
        // Configuration errors
        case .invalidConfiguration(let field):
            return "Invalid configuration: \(field)"
        case .configurationMissing:
            return "Configuration is missing"
        case .configurationParseError(let details):
            return "Configuration parse error: \(details)"
            
        // Platform errors
        case .platformNotSupported:
            return "Platform is not supported"
        case .permissionDenied(let permission):
            return "Permission denied: \(permission)"
        case .systemError(let code, let message):
            return "System error (\(code)): \(message)"
        case .networkExtensionError(let underlying):
            return "NetworkExtension error: \(underlying.localizedDescription)"
            
        // Protocol errors
        case .protocolVersionMismatch(let expected, let received):
            return "Protocol version mismatch: expected \(expected), received \(received)"
        case .invalidPacketFormat:
            return "Invalid packet format"
        case .handshakeFailed:
            return "Protocol handshake failed"
            
        // General errors
        case .internalError(let description):
            return "Internal error: \(description)"
        case .operationCanceled:
            return "Operation was canceled"
        case .timeout(let operation):
            return "Operation timed out: \(operation)"
        case .invalidState(let current, let expected):
            return "Invalid state: current=\(current), expected=\(expected)"
        }
    }
    
    public var failureReason: String? {
        return errorDescription
    }
    
    public var recoverySuggestion: String? {
        switch self {
        case .networkTimeout, .networkUnreachable:
            return "Check your internet connection and try again"
        case .dnsResolutionFailed:
            return "Check DNS settings or try a different server"
        case .invalidCredentials:
            return "Verify your username and password"
        case .tokenExpired:
            return "Please log in again"
        case .permissionDenied:
            return "Grant the required permissions in system settings"
        case .platformNotSupported:
            return "Update to a supported iOS/macOS version"
        case .configurationMissing, .invalidConfiguration:
            return "Check your VPN configuration settings"
        default:
            return "Please try again or contact support if the problem persists"
        }
    }
    
    // MARK: - CustomStringConvertible Implementation
    
    public var description: String {
        return errorDescription ?? "Unknown error"
    }
}

// MARK: - Error Severity and Category

extension ItForceError {
    /**
     * NAME: severity
     *
     * DESCRIPTION:
     *     Gets the severity level of the error for appropriate handling
     */
    public var severity: ErrorSeverity {
        switch self {
        case .operationCanceled, .timeout:
            return .warning
        case .networkTimeout, .connectionLost, .tokenExpired:
            return .warning
        case .invalidCredentials, .authenticationFailed, .unauthorized:
            return .error
        case .configurationMissing, .invalidConfiguration:
            return .error
        case .tunnelInitializationFailed, .tunnelDeviceError:
            return .critical
        case .platformNotSupported, .permissionDenied:
            return .critical
        case .internalError, .systemError:
            return .critical
        default:
            return .error
        }
    }
    
    /**
     * NAME: category
     *
     * DESCRIPTION:
     *     Gets the functional category of the error
     */
    public var category: ErrorCategory {
        switch self {
        case .networkTimeout, .networkUnreachable, .dnsResolutionFailed, .connectionFailed, .connectionLost:
            return .network
        case .invalidCredentials, .authenticationFailed, .tokenExpired, .unauthorized:
            return .authentication
        case .tunnelInitializationFailed, .tunnelDeviceError, .routingError, .encryptionFailed, .packetProcessingError:
            return .tunnel
        case .invalidConfiguration, .configurationMissing, .configurationParseError:
            return .configuration
        case .platformNotSupported, .permissionDenied, .systemError, .networkExtensionError:
            return .platform
        case .protocolVersionMismatch, .invalidPacketFormat, .handshakeFailed:
            return .protocolLayer
        case .internalError, .operationCanceled, .timeout, .invalidState:
            return .general
        }
    }
    
    /**
     * NAME: isRecoverable
     *
     * DESCRIPTION:
     *     Indicates whether the error condition might be recoverable
     */
    public var isRecoverable: Bool {
        switch self {
        case .networkTimeout, .networkUnreachable, .connectionLost:
            return true
        case .tokenExpired:
            return true
        case .operationCanceled, .timeout:
            return true
        case .platformNotSupported, .permissionDenied:
            return false
        case .invalidCredentials:
            return false
        case .internalError, .systemError:
            return false
        default:
            return true
        }
    }
}

// MARK: - Supporting Types



// MARK: - Error Creation Helpers

extension ItForceError {
    /**
     * NAME: wrap
     *
     * DESCRIPTION:
     *     Wraps a system error with ItForce context
     */
    public static func wrap(_ error: Error, context: String) -> ItForceError {
        if let itForceError = error as? ItForceError {
            return itForceError
        }
        
        // Handle common system errors
        if let nsError = error as NSError? {
            switch nsError.domain {
            case NSURLErrorDomain:
                return .networkUnreachable
            case "NEVPNErrorDomain":
                return .networkExtensionError(underlying: error)
            default:
                return .systemError(code: nsError.code, message: "\(context): \(nsError.localizedDescription)")
            }
        }
        
        return .internalError(description: "\(context): \(error.localizedDescription)")
    }
}
