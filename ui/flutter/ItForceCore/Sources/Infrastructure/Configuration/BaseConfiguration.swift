/**
 * FILE: BaseConfiguration.swift
 *
 * DESCRIPTION:
 *     Universal configuration system eliminating duplicate configuration patterns.
 *     Provides base configuration protocols and common configuration components.
 *     Supports composition-based configuration design with type safety.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create universal configuration system
 */

import Foundation

/**
 * NAME: RetryConfiguration
 *
 * DESCRIPTION:
 *     Common retry configuration used across all services.
 *     Eliminates duplicate retry settings in service configurations.
 */
public struct RetryConfiguration: Sendable, Codable, Equatable {
    public let maxAttempts: Int
    public let delay: TimeInterval
    public let backoffMultiplier: Double
    public let maxDelay: TimeInterval
    
    public init(
        maxAttempts: Int = 3,
        delay: TimeInterval = 5.0,
        backoffMultiplier: Double = 1.5,
        maxDelay: TimeInterval = 60.0
    ) {
        self.maxAttempts = maxAttempts
        self.delay = delay
        self.backoffMultiplier = backoffMultiplier
        self.maxDelay = maxDelay
    }
    
    public static let `default` = RetryConfiguration()
    public static let aggressive = RetryConfiguration(maxAttempts: 5, delay: 2.0)
    public static let conservative = RetryConfiguration(maxAttempts: 2, delay: 10.0)
    public static let authentication = RetryConfiguration(maxAttempts: 3, delay: 2.0, backoffMultiplier: 1.0, maxDelay: 2.0)
}

/**
 * NAME: TimeoutConfiguration
 *
 * DESCRIPTION:
 *     Common timeout configuration used across all services.
 *     Provides standardized timeout settings with validation.
 */
public struct TimeoutConfiguration: Sendable, Codable, Equatable {
    public let connection: TimeInterval
    public let authentication: TimeInterval
    public let operation: TimeInterval
    public let heartbeat: TimeInterval
    
    public init(
        connection: TimeInterval = 25.0,
        authentication: TimeInterval = 8.0,
        operation: TimeInterval = 60.0,
        heartbeat: TimeInterval = 15.0
    ) {
        self.connection = max(1.0, connection)
        self.authentication = max(1.0, authentication)
        self.operation = max(1.0, operation)
        self.heartbeat = max(1.0, heartbeat)
    }
    
    public static let `default` = TimeoutConfiguration()
    public static let fast = TimeoutConfiguration(connection: 15.0, authentication: 10.0, operation: 30.0)
    public static let slow = TimeoutConfiguration(connection: 60.0, authentication: 30.0, operation: 120.0)
}

/**
 * NAME: MonitoringConfiguration
 *
 * DESCRIPTION:
 *     Common monitoring configuration for background tasks.
 *     Standardizes monitoring intervals and behavior.
 */
public struct MonitoringConfiguration: Sendable, Codable, Equatable {
    public let enabled: Bool
    public let interval: TimeInterval
    public let backgroundEnabled: Bool
    public let statisticsInterval: TimeInterval
    
    public init(
        enabled: Bool = true,
        interval: TimeInterval = 30.0,
        backgroundEnabled: Bool = true,
        statisticsInterval: TimeInterval = 2.0
    ) {
        self.enabled = enabled
        self.interval = max(1.0, interval)
        self.backgroundEnabled = backgroundEnabled
        self.statisticsInterval = max(0.5, statisticsInterval)
    }
    
    public static let `default` = MonitoringConfiguration()
    public static let disabled = MonitoringConfiguration(enabled: false, backgroundEnabled: false)
    public static let frequent = MonitoringConfiguration(interval: 10.0, statisticsInterval: 1.0)
}

/**
 * NAME: BaseServiceConfiguration
 *
 * DESCRIPTION:
 *     Base configuration protocol providing common configuration components.
 *     All service configurations should implement this protocol.
 */
public protocol BaseServiceConfiguration: Sendable, Codable {
    var retry: RetryConfiguration { get }
    var timeout: TimeoutConfiguration { get }
    var monitoring: MonitoringConfiguration { get }
}

/**
 * NAME: ConfigurationValidator
 *
 * DESCRIPTION:
 *     Universal configuration validation utility.
 *     Provides common validation rules and error reporting.
 */
public struct ConfigurationValidator {
    
    public enum ValidationError: Error, LocalizedError {
        case invalidTimeout(String, TimeInterval)
        case invalidRetryCount(String, Int)
        case invalidInterval(String, TimeInterval)
        case missingRequiredField(String)
        
        public var errorDescription: String? {
            switch self {
            case .invalidTimeout(let field, let value):
                return "Invalid timeout for \(field): \(value). Must be > 0"
            case .invalidRetryCount(let field, let value):
                return "Invalid retry count for \(field): \(value). Must be >= 0"
            case .invalidInterval(let field, let value):
                return "Invalid interval for \(field): \(value). Must be > 0"
            case .missingRequiredField(let field):
                return "Missing required field: \(field)"
            }
        }
    }
    
    public static func validateTimeout(_ value: TimeInterval, field: String) throws {
        guard value > 0 else {
            throw ValidationError.invalidTimeout(field, value)
        }
    }
    
    public static func validateRetryCount(_ value: Int, field: String) throws {
        guard value >= 0 else {
            throw ValidationError.invalidRetryCount(field, value)
        }
    }
    
    public static func validateInterval(_ value: TimeInterval, field: String) throws {
        guard value > 0 else {
            throw ValidationError.invalidInterval(field, value)
        }
    }
    
    public static func validateRequired<T>(_ value: T?, field: String) throws -> T {
        guard let value = value else {
            throw ValidationError.missingRequiredField(field)
        }
        return value
    }
}

/**
 * NAME: ConfigurationBuilder
 *
 * DESCRIPTION:
 *     Builder pattern for creating configurations with validation.
 *     Provides fluent API for configuration construction.
 */
public class ConfigurationBuilder<T: BaseServiceConfiguration> {
    private var retry: RetryConfiguration = .default
    private var timeout: TimeoutConfiguration = .default
    private var monitoring: MonitoringConfiguration = .default
    
    public init() {}
    
    @discardableResult
    public func withRetry(_ retry: RetryConfiguration) -> Self {
        self.retry = retry
        return self
    }
    
    @discardableResult
    public func withTimeout(_ timeout: TimeoutConfiguration) -> Self {
        self.timeout = timeout
        return self
    }
    
    @discardableResult
    public func withMonitoring(_ monitoring: MonitoringConfiguration) -> Self {
        self.monitoring = monitoring
        return self
    }
    
    @discardableResult
    public func withQuickRetry() -> Self {
        self.retry = .aggressive
        return self
    }
    
    @discardableResult
    public func withSlowRetry() -> Self {
        self.retry = .conservative
        return self
    }
    
    @discardableResult
    public func withFastTimeouts() -> Self {
        self.timeout = .fast
        return self
    }
    
    @discardableResult
    public func withSlowTimeouts() -> Self {
        self.timeout = .slow
        return self
    }
    
    @discardableResult
    public func withoutMonitoring() -> Self {
        self.monitoring = .disabled
        return self
    }
    
    @discardableResult
    public func withFrequentMonitoring() -> Self {
        self.monitoring = .frequent
        return self
    }
    
    // Subclasses override this method to build specific configuration types
    public func build() throws -> T {
        fatalError("Subclasses must implement build()")
    }
    
    // Internal access to common configurations
    internal var commonRetry: RetryConfiguration { retry }
    internal var commonTimeout: TimeoutConfiguration { timeout }
    internal var commonMonitoring: MonitoringConfiguration { monitoring }
}

/**
 * NAME: ConfigurationManager
 *
 * DESCRIPTION:
 *     Centralized configuration management system.
 *     Handles configuration loading, validation, and caching.
 */
public class ConfigurationManager {
    private var configurations: [String: Any] = [:]
    private let lock = NSLock()
    private let logger: LoggerProtocol
    
    public init(logger: LoggerProtocol) {
        self.logger = logger
    }
    
    public func register<T: BaseServiceConfiguration>(_ configuration: T, for key: String) {
        lock.lock()
        defer { lock.unlock() }
        
        configurations[key] = configuration
        // logger.debug("Configuration registered", metadata: ["key": key, "type": "\(T.self)"]) // Debug log commented for production
    }
    
    public func get<T: BaseServiceConfiguration>(_ type: T.Type, for key: String) -> T? {
        lock.lock()
        defer { lock.unlock() }
        
        return configurations[key] as? T
    }
    
    public func update<T: BaseServiceConfiguration>(_ configuration: T, for key: String) {
        lock.lock()
        defer { lock.unlock() }
        
        configurations[key] = configuration
        logger.info("Configuration updated", metadata: ["key": key, "type": "\(T.self)"])
    }
    
    public func remove(for key: String) {
        lock.lock()
        defer { lock.unlock() }
        
        configurations.removeValue(forKey: key)
        // logger.debug("Configuration removed", metadata: ["key": key]) // Debug log commented for production
    }
    
    public func getAllKeys() -> [String] {
        lock.lock()
        defer { lock.unlock() }
        
        return Array(configurations.keys)
    }
}
