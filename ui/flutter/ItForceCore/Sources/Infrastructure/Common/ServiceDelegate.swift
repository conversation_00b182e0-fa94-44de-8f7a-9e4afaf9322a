/**
 * FILE: ServiceDelegate.swift
 *
 * DESCRIPTION:
 *     Universal service delegate system eliminating duplicate delegate patterns.
 *     Provides type-safe event notification with consistent naming and behavior.
 *     Supports both specific service delegates and universal event broadcasting.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create universal service delegate system
 */

import Foundation

/**
 * NAME: ServiceEvent
 *
 * DESCRIPTION:
 *     Universal service event enumeration covering all service types.
 *     Provides type-safe event handling with associated data.
 */
public enum ServiceEvent: Sendable {
    // Lifecycle Events
    case serviceStarted(serviceName: String)
    case serviceStopped(serviceName: String)
    case serviceError(serviceName: String, error: Error)
    
    // Connection Events
    case connectionStateChanged(state: ConnectionState)
    case connectionEstablished(server: ServerInfo)
    case connectionFailed(error: Error)
    case connectionDisconnected(reason: String?)
    case trafficUpdated(stats: TrafficStatistics)
    case networkExtensionRebuildRequired(server: ServerInfo)
    
    // Server Events
    case serverListUpdated(servers: [ServerInfo])
    case serverSelected(server: ServerInfo?)
    case serverStatusChanged(serverID: String, status: ServerStatus)
    case pingCompleted(results: [String: PingResult])
    
    // Configuration Events
    case configurationChanged(key: String, value: String)
    case settingsUpdated(settings: [String: String])
    
    public var eventType: String {
        switch self {
        case .serviceStarted: return "service_started"
        case .serviceStopped: return "service_stopped"
        case .serviceError: return "service_error"
        case .connectionStateChanged: return "connection_state_changed"
        case .connectionEstablished: return "connection_established"
        case .connectionFailed: return "connection_failed"
        case .connectionDisconnected: return "connection_disconnected"
        case .trafficUpdated: return "traffic_updated"
        case .networkExtensionRebuildRequired: return "network_extension_rebuild_required"
        case .serverListUpdated: return "server_list_updated"
        case .serverSelected: return "server_selected"
        case .serverStatusChanged: return "server_status_changed"
        case .pingCompleted: return "ping_completed"
        case .configurationChanged: return "configuration_changed"
        case .settingsUpdated: return "settings_updated"
        }
    }
}

/**
 * NAME: ServiceEventDelegate
 *
 * DESCRIPTION:
 *     Universal delegate protocol for all service events.
 *     Replaces individual service delegate protocols.
 */
public protocol ServiceEventDelegate: AnyObject, Sendable {
    func serviceDidEmitEvent(_ event: ServiceEvent, from service: any ServiceLifecycle)
}

/**
 * NAME: ServiceEventBroadcaster
 *
 * DESCRIPTION:
 *     Thread-safe event broadcasting system for services.
 *     Manages multiple delegates and provides event filtering.
 *
 * PROPERTIES:
 *     delegates - Registered event delegates
 *     eventFilters - Event type filters per delegate
 *     logger - Event logging
 */
public class ServiceEventBroadcaster {
    // MARK: - Delegate Management
    
    private struct DelegateEntry {
        weak var delegate: ServiceEventDelegate?
        let eventFilters: Set<String>?  // nil means all events
        let identifier: String
    }
    
    private var delegates: [DelegateEntry] = []
    private let delegatesLock = NSLock()
    private let logger: LoggerProtocol
    
    // MARK: - Initialization
    
    public init(logger: LoggerProtocol) {
        self.logger = logger
    }
    
    // MARK: - Delegate Registration
    
    /**
     * NAME: addDelegate
     *
     * DESCRIPTION:
     *     Registers a delegate for service events.
     *     Supports optional event filtering for performance.
     *
     * PARAMETERS:
     *     delegate - Event delegate to register
     *     eventTypes - Optional event type filter (nil = all events)
     *     identifier - Unique identifier for the delegate
     */
    public func addDelegate(
        _ delegate: ServiceEventDelegate,
        eventTypes: Set<String>? = nil,
        identifier: String = UUID().uuidString
    ) {
        delegatesLock.lock()
        defer { delegatesLock.unlock() }
        
        // Remove existing delegate with same identifier
        delegates.removeAll { $0.identifier == identifier }
        
        // Add new delegate entry
        let entry = DelegateEntry(
            delegate: delegate,
            eventFilters: eventTypes,
            identifier: identifier
        )
        delegates.append(entry)
        
        logger.debug("Service delegate registered", metadata: [
            "identifier": identifier,
            "event_filters": eventTypes?.joined(separator: ",") ?? "all"
        ])
    }
    
    /**
     * NAME: removeDelegate
     *
     * DESCRIPTION:
     *     Removes a delegate by identifier.
     *
     * PARAMETERS:
     *     identifier - Delegate identifier to remove
     */
    public func removeDelegate(identifier: String) {
        delegatesLock.lock()
        defer { delegatesLock.unlock() }
        
        delegates.removeAll { $0.identifier == identifier }
        
        logger.debug("Service delegate removed", metadata: ["identifier": identifier])
    }
    
    /**
     * NAME: removeAllDelegates
     *
     * DESCRIPTION:
     *     Removes all registered delegates.
     */
    public func removeAllDelegates() {
        delegatesLock.lock()
        defer { delegatesLock.unlock() }
        
        delegates.removeAll()
        logger.debug("All service delegates removed")
    }
    
    // MARK: - Event Broadcasting
    
    /**
     * NAME: broadcastEvent
     *
     * DESCRIPTION:
     *     Broadcasts an event to all registered delegates.
     *     Applies event filtering and handles delegate cleanup.
     *
     * PARAMETERS:
     *     event - Event to broadcast
     *     service - Service that emitted the event
     */
    public func broadcastEvent(_ event: ServiceEvent, from service: any ServiceLifecycle) {
        delegatesLock.lock()
        let currentDelegates = delegates
        delegatesLock.unlock()
        
        let eventType = event.eventType
        var validDelegates: [DelegateEntry] = []
        
        // Notify delegates and collect valid ones
        for entry in currentDelegates {
            guard let delegate = entry.delegate else {
                continue  // Delegate was deallocated
            }
            
            // Apply event filtering
            if let filters = entry.eventFilters, !filters.contains(eventType) {
                validDelegates.append(entry)
                continue  // Event filtered out
            }
            
            // Notify delegate
            delegate.serviceDidEmitEvent(event, from: service)
            validDelegates.append(entry)
        }
        
        // Clean up deallocated delegates
        if validDelegates.count != currentDelegates.count {
            delegatesLock.lock()
            delegates = validDelegates
            delegatesLock.unlock()
            
            logger.debug("Cleaned up deallocated service delegates", metadata: [
                "removed": "\(currentDelegates.count - validDelegates.count)"
            ])
        }
        
        logger.debug("Service event broadcasted", metadata: [
            "event_type": eventType,
            "service": service.serviceName,
            "delegates_notified": "\(validDelegates.count)"
        ])
    }
}

/**
 * NAME: ServiceEventEmitter
 *
 * DESCRIPTION:
 *     Mixin protocol for services that emit events.
 *     Provides convenient event emission methods.
 */
public protocol ServiceEventEmitter: ServiceLifecycle {
    var eventBroadcaster: ServiceEventBroadcaster { get }
}

extension ServiceEventEmitter {
    /**
     * NAME: emitEvent
     *
     * DESCRIPTION:
     *     Convenience method for emitting service events.
     */
    public func emitEvent(_ event: ServiceEvent) {
        eventBroadcaster.broadcastEvent(event, from: self)
    }
    
    // MARK: - Convenience Event Emission Methods
    
    public func emitConnectionStateChanged(_ state: ConnectionState) {
        emitEvent(.connectionStateChanged(state: state))
    }
    
    public func emitConnectionEstablished(_ server: ServerInfo) {
        emitEvent(.connectionEstablished(server: server))
    }
    
    public func emitConnectionFailed(_ error: Error) {
        emitEvent(.connectionFailed(error: error))
    }
    
    public func emitConnectionDisconnected(reason: String?) {
        emitEvent(.connectionDisconnected(reason: reason))
    }
    
    public func emitTrafficUpdated(_ stats: TrafficStatistics) {
        emitEvent(.trafficUpdated(stats: stats))
    }
    
    public func emitServerListUpdated(_ servers: [ServerInfo]) {
        emitEvent(.serverListUpdated(servers: servers))
    }
    
    public func emitServerSelected(_ server: ServerInfo?) {
        emitEvent(.serverSelected(server: server))
    }
    
    public func emitServiceError(_ error: Error) {
        emitEvent(.serviceError(serviceName: serviceName, error: error))
    }
}

/**
 * NAME: EventFilterBuilder
 *
 * DESCRIPTION:
 *     Builder for creating event type filters.
 *     Provides convenient methods for common filter combinations.
 */
public struct EventFilterBuilder {
    private var eventTypes: Set<String> = []
    
    public init() {}
    
    public mutating func includeConnectionEvents() -> EventFilterBuilder {
        eventTypes.insert("connection_state_changed")
        eventTypes.insert("connection_established")
        eventTypes.insert("connection_failed")
        eventTypes.insert("connection_disconnected")
        eventTypes.insert("traffic_updated")
        return self
    }
    
    public mutating func includeServerEvents() -> EventFilterBuilder {
        eventTypes.insert("server_list_updated")
        eventTypes.insert("server_selected")
        eventTypes.insert("server_status_changed")
        eventTypes.insert("ping_completed")
        return self
    }
    
    public mutating func includeServiceEvents() -> EventFilterBuilder {
        eventTypes.insert("service_started")
        eventTypes.insert("service_stopped")
        eventTypes.insert("service_error")
        return self
    }
    
    public func build() -> Set<String> {
        return eventTypes
    }
}
