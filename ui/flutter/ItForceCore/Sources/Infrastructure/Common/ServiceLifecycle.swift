/**
 * AUTHOR: wei
 * HISTORY: 23/06/2025
 *
 * NAME: ServiceLifecycle
 *
 * DESCRIPTION:
 *     Actor-based service lifecycle management using composition pattern.
 *     Thread-safe, simple, and functional design without over-engineering.
 */

import Foundation

/**
 * NAME: ServiceState
 *
 * DESCRIPTION:
 *     Service state enumeration with clear transitions.
 */
public enum ServiceState: String, CaseIterable, Sendable {
    case stopped = "stopped"
    case starting = "starting"
    case started = "started"
    case stopping = "stopping"
    case error = "error"

    public var isTransitioning: Bool {
        return self == .starting || self == .stopping
    }
}

/**
 * NAME: ServiceLifecycleManager
 *
 * DESCRIPTION:
 *     Actor-based lifecycle state manager for thread-safe operations.
 *     Used via composition in services.
 */
public actor ServiceLifecycleManager {
    private var state: ServiceState = .stopped
    private let serviceName: String
    private let logger: LoggerProtocol

    public init(serviceName: String, logger: LoggerProtocol) {
        self.serviceName = serviceName
        self.logger = logger
    }

    public func getCurrentState() -> ServiceState {
        return state
    }

    public func isStarted() -> Bool {
        return state == .started
    }

    public func startTransition() throws {
        guard state == .stopped || state == .error else {
            if state == .started {
                throw ServiceLifecycleError.serviceAlreadyStarted(serviceName)
            } else {
                throw ServiceLifecycleError.serviceInTransition(serviceName, state)
            }
        }
        state = .starting
        logger.info("Starting \(serviceName)")
    }

    public func completeStart() {
        state = .started
        logger.info("\(serviceName) started successfully")
    }

    public func failStart(_ error: Error) {
        state = .error
        logger.error("\(serviceName) startup failed", metadata: ["error": "\(error)"])
    }

    public func stopTransition() throws {
        guard state == .started || state == .error else {
            if state == .stopped {
                throw ServiceLifecycleError.serviceAlreadyStopped(serviceName)
            } else {
                throw ServiceLifecycleError.serviceInTransition(serviceName, state)
            }
        }
        state = .stopping
        logger.info("Stopping \(serviceName)")
    }

    public func completeStop() {
        state = .stopped
        logger.info("\(serviceName) stopped successfully")
    }

    public func failStop(_ error: Error) {
        state = .error
        logger.error("\(serviceName) shutdown failed", metadata: ["error": "\(error)"])
    }
}

/**
 * NAME: ServiceLifecycleError
 *
 * DESCRIPTION:
 *     Standard errors for service lifecycle operations.
 */
public enum ServiceLifecycleError: Error, LocalizedError {
    case serviceAlreadyStarted(String)
    case serviceAlreadyStopped(String)
    case serviceInTransition(String, ServiceState)
    case startupFailed(String, Error)
    case shutdownFailed(String, Error)

    public var errorDescription: String? {
        switch self {
        case .serviceAlreadyStarted(let name):
            return "\(name) service is already started"
        case .serviceAlreadyStopped(let name):
            return "\(name) service is already stopped"
        case .serviceInTransition(let name, let state):
            return "\(name) service is currently \(state.rawValue)"
        case .startupFailed(let name, let error):
            return "\(name) service startup failed: \(error.localizedDescription)"
        case .shutdownFailed(let name, let error):
            return "\(name) service shutdown failed: \(error.localizedDescription)"
        }
    }
}

/**
 * NAME: ServiceLifecycle
 *
 * DESCRIPTION:
 *     Simplified service lifecycle protocol.
 *     Services use composition with ServiceLifecycleManager.
 */
public protocol ServiceLifecycle: AnyObject {
    /// Service name for logging and identification
    var serviceName: String { get }

    /// Current service state
    func getCurrentState() async -> ServiceState

    /// Check if service is started
    func isStarted() async -> Bool

    /// Starts the service
    func start() async throws

    /// Stops the service
    func stop() async throws

    /// Restarts the service (stop + start)
    func restart() async throws
}

/**
 * NAME: ServiceLifecycleComposer
 *
 * DESCRIPTION:
 *     Helper class for services to easily integrate lifecycle management.
 *     Uses composition pattern with ServiceLifecycleManager.
 */
public class ServiceLifecycleComposer: ServiceLifecycle {
    private let lifecycleManager: ServiceLifecycleManager
    private let performStartHandler: () async throws -> Void
    private let performStopHandler: () async throws -> Void

    public let serviceName: String

    public init(
        serviceName: String,
        logger: LoggerProtocol,
        performStart: @escaping () async throws -> Void = {},
        performStop: @escaping () async throws -> Void = {}
    ) {
        self.serviceName = serviceName
        self.lifecycleManager = ServiceLifecycleManager(serviceName: serviceName, logger: logger)
        self.performStartHandler = performStart
        self.performStopHandler = performStop
    }

    public func getCurrentState() async -> ServiceState {
        return await lifecycleManager.getCurrentState()
    }

    public func isStarted() async -> Bool {
        return await lifecycleManager.isStarted()
    }

    public func start() async throws {
        try await lifecycleManager.startTransition()

        do {
            try await performStartHandler()
            await lifecycleManager.completeStart()
        } catch {
            await lifecycleManager.failStart(error)
            throw ServiceLifecycleError.startupFailed(serviceName, error)
        }
    }

    public func stop() async throws {
        try await lifecycleManager.stopTransition()

        do {
            try await performStopHandler()
            await lifecycleManager.completeStop()
        } catch {
            await lifecycleManager.failStop(error)
            throw ServiceLifecycleError.shutdownFailed(serviceName, error)
        }
    }

    public func restart() async throws {
        if await isStarted() {
            try await stop()
        }
        try await start()
    }
}

/**
 * NAME: ServiceManager
 *
 * DESCRIPTION:
 *     Manages multiple services with dependency ordering.
 *     Simplified batch operations for service management.
 */
public actor ServiceManager {
    private var services: [ServiceLifecycle] = []
    private let logger: LoggerProtocol

    public init(logger: LoggerProtocol) {
        self.logger = logger
    }

    public func addService(_ service: ServiceLifecycle) {
        services.append(service)
    }

    public func startAll() async throws {
        logger.info("Starting all services", metadata: ["count": "\(services.count)"])

        for service in services {
            try await service.start()
        }

        logger.info("All services started successfully")
    }

    public func stopAll() async throws {
        logger.info("Stopping all services", metadata: ["count": "\(services.count)"])

        // Stop services in reverse order
        for service in services.reversed() {
            try await service.stop()
        }

        logger.info("All services stopped successfully")
    }

    public func getServiceStates() async -> [String: ServiceState] {
        var states: [String: ServiceState] = [:]
        for service in services {
            states[service.serviceName] = await service.getCurrentState()
        }
        return states
    }
}
