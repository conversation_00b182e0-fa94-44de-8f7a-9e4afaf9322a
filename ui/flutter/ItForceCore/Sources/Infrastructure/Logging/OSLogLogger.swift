/**
 * FILE: OSLogLogger.swift
 *
 * DESCRIPTION:
 *     OSLog-based logger implementation providing high-performance structured logging
 *     for iOS/macOS ItForce VPN client. Optimized for NetworkExtension environment
 *     with minimal memory footprint and efficient log formatting.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import OSLog

/**
 * NAME: OSLogLogger
 *
 * DESCRIPTION:
 *     OSLog-based implementation of LoggerProtocol providing structured logging
 *     with performance optimization for NetworkExtension environment
 *
 * PROPERTIES:
 *     osLog - OSLog instance for actual logging
 *     configuration - Logger configuration
 *     contextFields - Additional context fields
 *     currentLevel - Current log level
 */
public final class OSLogLogger: LoggerProtocol {
    private let osLog: OSLog
    private let configuration: LoggingConfiguration
    private let contextFields: [LogField]
    private var currentLevel: LogLevel
    
    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Initializes OSLogLogger with configuration and optional context fields
     *
     * PARAMETERS:
     *     configuration - Logging configuration
     *     contextFields - Additional context fields to include in all log messages
     */
    public init(configuration: LoggingConfiguration, contextFields: [LogField] = []) {
        self.configuration = configuration
        self.contextFields = contextFields
        self.currentLevel = configuration.level

        // Safe OSLog creation with validation
        let safeSubsystem = configuration.subsystem.isEmpty ? "com.panabit.client" : configuration.subsystem
        let safeCategory = configuration.category.isEmpty ? "default" : configuration.category

        // Create OSLog with error handling
        self.osLog = OSLog(subsystem: safeSubsystem, category: safeCategory)
    }
    
    // MARK: - Basic Logging Methods
    
    /**
     * NAME: log
     *
     * DESCRIPTION:
     *     Core logging method that handles all log output with caller information
     *
     * PARAMETERS:
     *     level - Log level
     *     message - Log message
     *     fields - Additional structured fields
     *     file - Source file
     *     line - Source line
     *     function - Source function
     */
    public func log(level: LogLevel, message: String, fields: [LogField], file: String, line: Int, function: String) {
        guard level >= currentLevel else { return }

        var allFields = contextFields + fields

        if configuration.includeCaller {
            let fileName = (file as NSString).lastPathComponent
            allFields.append(LogField.string("file", fileName))
            allFields.append(LogField.int("line", line))
            allFields.append(LogField.string("function", function))
        }

        let formattedMessage = formatMessage(message: message, fields: allFields)
        os_log("%{public}@", log: osLog, type: level.osLogType, formattedMessage)
    }

    // MARK: - Protocol Implementation Methods

    public func logDebugInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .debug, message: message, fields: fields, file: file, line: line, function: function)
    }

    public func logInfoInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .info, message: message, fields: fields, file: file, line: line, function: function)
    }

    public func logWarningInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .warning, message: message, fields: fields, file: file, line: line, function: function)
    }

    public func logErrorInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .error, message: message, fields: fields, file: file, line: line, function: function)
    }

    public func logCriticalInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String) {
        log(level: .critical, message: message, fields: fields, file: file, line: line, function: function)
    }
    
    // MARK: - Context-Aware Logging Methods
    
    /**
     * NAME: withModule
     *
     * DESCRIPTION:
     *     Creates a new logger instance with module context
     *
     * PARAMETERS:
     *     module - Module name
     *
     * RETURNS:
     *     LoggerProtocol - New logger with module context
     */
    public func withModule(_ module: String) -> LoggerProtocol {
        let moduleField = LogField.string("module", module)
        return OSLogLogger(
            configuration: configuration,
            contextFields: contextFields + [moduleField]
        )
    }
    
    /**
     * NAME: withFields
     *
     * DESCRIPTION:
     *     Creates a new logger instance with additional context fields
     *
     * PARAMETERS:
     *     fields - Additional context fields
     *
     * RETURNS:
     *     LoggerProtocol - New logger with additional context
     */
    public func withFields(_ fields: [LogField]) -> LoggerProtocol {
        return OSLogLogger(
            configuration: configuration,
            contextFields: contextFields + fields
        )
    }
    
    // MARK: - Performance Tracking Methods
    
    /**
     * NAME: traceMethod
     *
     * DESCRIPTION:
     *     Traces method execution time and returns a completion closure.
     *     Uses Swift compile-time constants for automatic caller information capture.
     *
     * PARAMETERS:
     *     methodName - Name of the method being traced
     *     file - Source file (automatically captured via #file)
     *     line - Source line (automatically captured via #line)
     *     function - Source function (automatically captured via #function)
     *
     * RETURNS:
     *     () -> Void - Completion closure to call when method finishes
     */
    public func traceMethodInternal(_ methodName: String, file: String, line: Int, function: String) -> () -> Void {
        guard configuration.enablePerformanceTracking else {
            return {}
        }

        let startTime = DispatchTime.now()
        logDebugInternal("Method started", fields: [LogField.string("method", methodName)], file: file, line: line, function: function)

        return { [weak self] in
            let endTime = DispatchTime.now()
            let duration = Double(endTime.uptimeNanoseconds - startTime.uptimeNanoseconds) / 1_000_000.0

            self?.logDebugInternal("Method completed", fields: [
                LogField.string("method", methodName),
                LogField.double("duration_ms", duration)
            ], file: file, line: line, function: function)
        }
    }
    
    /**
     * NAME: traceError
     *
     * DESCRIPTION:
     *     Traces an error with additional context information.
     *     Uses Swift compile-time constants for automatic caller information capture.
     *
     * PARAMETERS:
     *     error - Error to trace
     *     message - Additional error message
     *     fields - Additional context fields
     *     file - Source file (automatically captured via #file)
     *     line - Source line (automatically captured via #line)
     *     function - Source function (automatically captured via #function)
     */
    public func traceErrorInternal(_ error: Error, message: String, fields: [LogField], file: String, line: Int, function: String) {
        var errorFields = fields
        errorFields.append(LogField.error("error", error))

        if configuration.includeCaller {
            let fileName = (file as NSString).lastPathComponent
            errorFields.append(LogField.string("caller_file", fileName))
            errorFields.append(LogField.int("caller_line", line))
            errorFields.append(LogField.string("caller_function", function))
        }

        self.log(level: .error, message: message, fields: errorFields, file: file, line: line, function: function)
    }
    
    // MARK: - Configuration Methods
    
    /**
     * NAME: setLevel
     *
     * DESCRIPTION:
     *     Sets the current log level
     *
     * PARAMETERS:
     *     level - New log level
     */
    public func setLevel(_ level: LogLevel) {
        currentLevel = level
    }
    
    /**
     * NAME: getLevel
     *
     * DESCRIPTION:
     *     Gets the current log level
     *
     * RETURNS:
     *     LogLevel - Current log level
     */
    public func getLevel() -> LogLevel {
        return currentLevel
    }
    
    // MARK: - Private Helper Methods
    
    /**
     * NAME: formatMessage
     *
     * DESCRIPTION:
     *     Formats log message with structured fields for optimal readability
     *
     * PARAMETERS:
     *     message - Base log message
     *     fields - Structured fields to include
     *
     * RETURNS:
     *     String - Formatted log message
     */
    private func formatMessage(message: String, fields: [LogField]) -> String {
        guard !fields.isEmpty else { return message }
        
        let fieldStrings = fields.map { field in
            "\(field.key)=\(formatValue(field.value))"
        }
        
        return "\(message) [\(fieldStrings.joined(separator: ", "))]"
    }
    
    /**
     * NAME: formatValue
     *
     * DESCRIPTION:
     *     Formats field value for log output with type-specific formatting
     *
     * PARAMETERS:
     *     value - Value to format
     *
     * RETURNS:
     *     String - Formatted value string
     */
    private func formatValue(_ value: Any) -> String {
        switch value {
        case let string as String:
            return "\"\(string)\""
        case let number as NSNumber:
            return number.stringValue
        case let bool as Bool:
            return bool ? "true" : "false"
        case let date as Date:
            return ISO8601DateFormatter().string(from: date)
        default:
            return "\(value)"
        }
    }
    

}

// MARK: - OSLogLogger Extensions

extension OSLogLogger {
    /**
     * NAME: createNetworkExtensionLogger
     *
     * DESCRIPTION:
     *     Creates a logger optimized for NetworkExtension environment
     *
     * PARAMETERS:
     *     category - Logger category
     *
     * RETURNS:
     *     OSLogLogger - NetworkExtension-optimized logger
     */
    public static func createNetworkExtensionLogger(category: String = "network") -> OSLogLogger {
        let config = LoggingConfiguration(
            level: .info,
            subsystem: "com.panabit.client.extension",
            category: category,
            includeCaller: false,
            enablePerformanceTracking: true
        )
        return OSLogLogger(configuration: config)
    }

    /**
     * NAME: createDevelopmentLogger
     *
     * DESCRIPTION:
     *     Creates a logger optimized for development environment with safe initialization
     *
     * PARAMETERS:
     *     category - Logger category
     *
     * RETURNS:
     *     OSLogLogger - Development-optimized logger
     */
    public static func createDevelopmentLogger(category: String = "development") -> OSLogLogger {
        // Validate category string to prevent OSLog issues
        let safeCategory = category.isEmpty ? "development" : category

        let config = LoggingConfiguration(
            level: .debug,
            subsystem: "com.panabit.client.dev",
            category: safeCategory,
            includeCaller: true,
            enablePerformanceTracking: true
        )

        // Create logger with error handling
        do {
            return OSLogLogger(configuration: config)
        } catch {
            // If OSLog creation fails, create a minimal fallback
            // print("⚠️ [OSLogLogger] Failed to create development logger: \(error)")
            let fallbackConfig = LoggingConfiguration(
                level: .debug,
                subsystem: "com.panabit.client",
                category: "fallback",
                includeCaller: false,
                enablePerformanceTracking: false
            )
            return OSLogLogger(configuration: fallbackConfig)
        }
    }
}

// MARK: - Simplified Logging Interface

/**
 * NAME: OSLogLogger Simplified Interface Extension
 *
 * DESCRIPTION:
 *     Provides ultra-simplified logging methods that require no parameters beyond the message.
 *     These methods automatically capture caller information using Swift compile-time constants.
 *     Perfect for quick logging without any boilerplate code.
 *
 * USAGE EXAMPLES:
 *     logger.logDebug("Connection started")
 *     logger.logInfo("Data received")
 *     logger.logWarning("Network unstable")
 *     logger.logError("Connection failed")
 *     logger.logCritical("System error")
 */
extension OSLogLogger {
    /**
     * NAME: logDebug
     *
     * DESCRIPTION:
     *     Ultra-simplified debug logging with automatic caller information
     *
     * PARAMETERS:
     *     message - Debug message
     */
    public func logDebug(
        _ message: String,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        debug(message, file: file, line: line, function: function)
    }

    /**
     * NAME: logInfo
     *
     * DESCRIPTION:
     *     Ultra-simplified info logging with automatic caller information
     *
     * PARAMETERS:
     *     message - Info message
     */
    public func logInfo(
        _ message: String,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        info(message, file: file, line: line, function: function)
    }

    /**
     * NAME: logWarning
     *
     * DESCRIPTION:
     *     Ultra-simplified warning logging with automatic caller information
     *
     * PARAMETERS:
     *     message - Warning message
     */
    public func logWarning(
        _ message: String,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        warning(message, file: file, line: line, function: function)
    }

    /**
     * NAME: logError
     *
     * DESCRIPTION:
     *     Ultra-simplified error logging with automatic caller information
     *
     * PARAMETERS:
     *     message - Error message
     */
    public func logError(
        _ message: String,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        error(message, file: file, line: line, function: function)
    }

    /**
     * NAME: logCritical
     *
     * DESCRIPTION:
     *     Ultra-simplified critical logging with automatic caller information
     *
     * PARAMETERS:
     *     message - Critical message
     */
    public func logCritical(
        _ message: String,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        critical(message, file: file, line: line, function: function)
    }
}
