# iOS/macOS 日志系统使用指南

## 📋 概述

本文档介绍优化后的iOS/macOS日志系统的使用方法。日志系统基于OSLog，提供了极简的调用接口，自动捕获调用者信息，无需手动传入文件、行号、函数名等参数。

## 🎯 优化成果

### 修复前的问题
```swift
// 调用者信息显示错误，显示LoggingSystem.swift而不是实际调用者
logger.debug("Connection started")  // 显示: file="LoggingSystem.swift", line=203
logger.info("Data received")        // 显示: file="LoggingSystem.swift", line=198
```

### 修复后的效果
```swift
// 正确显示实际调用者信息
logger.debug("Connection started")  // 显示: file="MyClass.swift", line=42, function="myMethod()"
logger.info("Data received", LogField.int("bytes", 1024))  // 显示正确的调用者位置
```

### 支持的调用方式
```swift
// 1. 简单消息（自动捕获调用者信息）
logger.debug("Connection started")
logger.info("Data received")

// 2. 带结构化字段（自动捕获调用者信息）
logger.info("Data received", fields: LogField.int("bytes", 1024))

// 3. 带metadata字典（兼容性方法，自动捕获调用者信息）
logger.info("Data received", metadata: ["bytes": "1024"])

// 4. 手动指定调用者信息（高级用法）
logger.debug("Connection started", file: #file, line: #line, function: #function)
```

## 🚀 快速开始

### 1. 获取Logger实例

```swift
import ItForceCore

// 使用默认logger
let logger = LoggingSystem.shared.defaultLogger

// 创建特定类别的logger
let networkLogger = LoggingSystem.shared.logger(for: "network")

// 创建模块特定的logger
let moduleLogger = logger.withModule("ConnectionManager")
```

### 2. 基础日志记录

```swift
// 简单消息记录
logger.debug("Debug message")
logger.info("Info message")
logger.warning("Warning message")
logger.error("Error message")
logger.critical("Critical message")
```

### 3. 结构化日志记录

```swift
// 带结构化字段的日志
logger.info("Connection established", 
    LogField.string("server", "192.168.1.1"),
    LogField.int("port", 8080),
    LogField.double("latency", 15.5)
)

// 错误日志
logger.error("Connection failed",
    LogField.string("reason", "timeout"),
    LogField.int("retry_count", 3)
)
```

### 4. 性能追踪

```swift
// 方法执行时间追踪
func connectToServer() {
    let trace = logger.traceMethod("connectToServer")
    defer { trace() }
    
    // 方法实现...
}

// 错误追踪
do {
    try someOperation()
} catch {
    logger.traceError(error, message: "Operation failed")
}
```

### 5. 上下文日志

```swift
// 创建带模块上下文的logger
let connectionLogger = logger.withModule("Connection")
connectionLogger.info("Starting connection")

// 创建带字段上下文的logger
let sessionLogger = logger.withFields([
    LogField.string("session_id", "abc123"),
    LogField.string("user_id", "user456")
])
sessionLogger.info("Session activity")
```

## ⚙️ 配置

### 1. 环境配置

```swift
// 开发环境配置
LoggingSystem.shared.configure(with: .development)

// 生产环境配置
LoggingSystem.shared.configure(with: .production)

// NetworkExtension环境配置
LoggingSystem.shared.configure(with: .networkExtension)
```

### 2. 自定义配置

```swift
let customConfig = LoggingConfiguration(
    level: .info,
    subsystem: "com.myapp.vpn",
    category: "custom",
    includeCaller: true,
    enablePerformanceTracking: true
)

LoggingSystem.shared.configure(with: customConfig)
```

## 🔧 高级特性

### 1. 日志级别控制

```swift
// 设置日志级别
logger.setLevel(.warning)

// 获取当前级别
let currentLevel = logger.getLevel()
```

### 2. 便利函数

```swift
// 全局便利函数（自动使用默认logger）
logDebug("Debug message")
logInfo("Info message")
logWarning("Warning message")
logError("Error message")
logCritical("Critical message")
```

### 3. 专用Logger创建

```swift
// NetworkExtension优化的logger
let networkLogger = OSLogLogger.createNetworkExtensionLogger()

// 开发环境优化的logger
let devLogger = OSLogLogger.createDevelopmentLogger()
```

## 📊 性能特性

### 1. 编译时优化
- 使用Swift编译时常量（#file, #line, #function）
- 零运行时开销的调用者信息捕获
- 自动日志级别过滤

### 2. NetworkExtension优化
- 最小内存占用
- 高效的OSLog集成
- 智能性能追踪

### 3. 结构化日志
- JSON格式输出支持
- 类型安全的字段定义
- 自动值格式化

## 🎯 最佳实践

### 1. 日志级别使用
- **Debug**: 开发调试信息
- **Info**: 正常操作信息
- **Warning**: 潜在问题警告
- **Error**: 错误但可继续运行
- **Critical**: 严重错误需要立即关注

### 2. 结构化字段
```swift
// 推荐：使用类型安全的字段
logger.info("Data transfer", 
    LogField.int("bytes", dataSize),
    LogField.double("duration", transferTime)
)

// 避免：直接字符串拼接
logger.info("Data transfer: \(dataSize) bytes in \(transferTime)s")
```

### 3. 性能追踪
```swift
// 推荐：使用defer确保追踪完成
func performOperation() {
    let trace = logger.traceMethod("performOperation")
    defer { trace() }
    
    // 操作实现...
}
```

## 🔍 故障排除

### 1. 日志不显示
- 检查日志级别设置
- 确认OSLog配置正确
- 验证subsystem和category设置

### 2. 性能问题
- 使用适当的日志级别
- 避免在高频路径中使用debug日志
- 利用日志级别过滤

### 3. NetworkExtension环境
- 使用networkExtension配置
- 注意内存使用限制
- 避免过度详细的日志

---

**文档版本**: v1.0  
**创建日期**: 2025-06-25  
**作者**: wei  
**更新历史**: 23/06/2025 create
