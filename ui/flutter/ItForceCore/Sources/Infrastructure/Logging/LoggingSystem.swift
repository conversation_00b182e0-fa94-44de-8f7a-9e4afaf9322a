/**
 * FILE: LoggingSystem.swift
 *
 * DESCRIPTION:
 *     Core logging system interface and implementation for iOS/macOS ItForce VPN client.
 *     Provides structured logging with OSLog integration, optimized for NetworkExtension environment.
 *     Supports multiple log levels, module-specific loggers, and performance tracking.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import OSLog

// MARK: - Log Level Definition

/**
 * NAME: LogLevel
 *
 * DESCRIPTION:
 *     Defines the log level hierarchy for filtering log messages
 *
 * CASES:
 *     debug - Debug information for development and troubleshooting
 *     info - General information indicating normal operation
 *     warning - Warning information indicating potential issues
 *     error - Error information indicating problems that allow continued operation
 *     critical - Critical error information requiring immediate attention
 */
public enum LogLevel: Int, CaseIterable, Comparable {
    case debug = 0
    case info = 1
    case warning = 2
    case error = 3
    case critical = 4

    public static func < (lhs: LogLevel, rhs: LogLevel) -> Bool {
        return lhs.rawValue < rhs.rawValue
    }

    var stringValue: String {
        switch self {
        case .debug: return "DEBUG"
        case .info: return "INFO"
        case .warning: return "WARNING"
        case .error: return "ERROR"
        case .critical: return "CRITICAL"
        }
    }

    var osLogType: OSLogType {
        switch self {
        case .debug: return .debug
        case .info: return .info
        case .warning: return .default
        case .error: return .error
        case .critical: return .fault
        }
    }
}

// MARK: - Log Field Definition

/**
 * NAME: LogField
 *
 * DESCRIPTION:
 *     Represents a structured log field with key-value pair
 *
 * PROPERTIES:
 *     key - Field identifier
 *     value - Field value (supports multiple types)
 */
public struct LogField {
    public let key: String
    public let value: Any
    
    public init(key: String, value: Any) {
        self.key = key
        self.value = value
    }
    
    // Convenience initializers for common types
    public static func string(_ key: String, _ value: String) -> LogField {
        return LogField(key: key, value: value)
    }
    
    public static func int(_ key: String, _ value: Int) -> LogField {
        return LogField(key: key, value: value)
    }
    
    public static func double(_ key: String, _ value: Double) -> LogField {
        return LogField(key: key, value: value)
    }
    
    public static func bool(_ key: String, _ value: Bool) -> LogField {
        return LogField(key: key, value: value)
    }
    
    public static func error(_ key: String, _ value: Error) -> LogField {
        return LogField(key: key, value: value.localizedDescription)
    }
}

// MARK: - Logger Protocol

/**
 * NAME: LoggerProtocol
 *
 * DESCRIPTION:
 *     Core logging interface providing structured logging capabilities with simplified calling interface.
 *     Uses Swift compile-time constants (#file, #line, #function) as default parameters for optimal usability.
 *
 * METHODS:
 *     log - Log a message with specified level and fields
 *     debug - Log debug message
 *     info - Log info message
 *     warning - Log warning message
 *     error - Log error message
 *     critical - Log critical message
 *     withModule - Create module-specific logger
 *     withFields - Create logger with additional fields
 *     traceMethod - Trace method execution time
 *     traceError - Trace error with context
 */
public protocol LoggerProtocol {
    // Basic logging methods with automatic caller information
    func log(level: LogLevel, message: String, fields: [LogField], file: String, line: Int, function: String)
    func logDebugInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String)
    func logInfoInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String)
    func logWarningInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String)
    func logErrorInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String)
    func logCriticalInternal(_ message: String, fields: [LogField], file: String, line: Int, function: String)

    // Context-aware logging methods
    func withModule(_ module: String) -> LoggerProtocol
    func withFields(_ fields: [LogField]) -> LoggerProtocol

    // Performance tracking methods
    func traceMethodInternal(_ methodName: String, file: String, line: Int, function: String) -> () -> Void
    func traceErrorInternal(_ error: Error, message: String, fields: [LogField], file: String, line: Int, function: String)

    // Configuration methods
    func setLevel(_ level: LogLevel)
    func getLevel() -> LogLevel
}

// MARK: - LoggerProtocol Simplified Interface Extension

/**
 * NAME: LoggerProtocol Simplified Interface Extension
 *
 * DESCRIPTION:
 *     Provides simplified logging methods with automatic caller information capture.
 *     These methods use default parameters to eliminate the need for manual parameter passing.
 *
 * USAGE EXAMPLES:
 *     logger.logDebug("Connection started")
 *     logger.logInfo("Data received", LogField.int("bytes", 1024))
 *     logger.logWarning("Network unstable")
 *     logger.logError("Connection failed")
 *     logger.logCritical("System error")
 */
extension LoggerProtocol {
    /**
     * NAME: logDebug
     *
     * DESCRIPTION:
     *     Simplified debug logging with automatic caller information capture
     *
     * PARAMETERS:
     *     message - Debug message
     *     fields - Optional structured fields
     */
    public func logDebug(
        _ message: String,
        fields: LogField...,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        logDebugInternal(message, fields: Array(fields), file: file, line: line, function: function)
    }

    public func debug(
        _ message: String,
        fields: LogField...,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        logDebugInternal(message, fields: Array(fields), file: file, line: line, function: function)
    }

    public func info(
        _ message: String,
        fields: LogField...,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        logInfoInternal(message, fields: Array(fields), file: file, line: line, function: function)
    }

    public func warning(
        _ message: String,
        fields: LogField...,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        logWarningInternal(message, fields: Array(fields), file: file, line: line, function: function)
    }

    public func error(
        _ message: String,
        fields: LogField...,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        logErrorInternal(message, fields: Array(fields), file: file, line: line, function: function)
    }

    public func critical(
        _ message: String,
        fields: LogField...,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        logCriticalInternal(message, fields: Array(fields), file: file, line: line, function: function)
    }

    public func traceMethod(
        _ methodName: String,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) -> () -> Void {
        return traceMethodInternal(methodName, file: file, line: line, function: function)
    }

    public func traceError(
        _ error: Error,
        message: String,
        fields: LogField...,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        traceErrorInternal(error, message: message, fields: Array(fields), file: file, line: line, function: function)
    }

    // MARK: - Temporary Compatibility Methods

    /**
     * NAME: Temporary compatibility methods for metadata-style logging
     *
     * DESCRIPTION:
     *     These methods provide temporary compatibility for existing code
     *     that uses metadata dictionaries instead of LogField arrays.
     */

    public func info(_ message: String, metadata: [String: String] = [:],
                    file: String = #file, line: Int = #line, function: String = #function) {
        let fields = metadata.map { LogField(key: $0.key, value: $0.value) }
        log(level: .info, message: message, fields: fields, file: file, line: line, function: function)
    }

    public func debug(_ message: String, metadata: [String: String] = [:],
                     file: String = #file, line: Int = #line, function: String = #function) {
        let fields = metadata.map { LogField(key: $0.key, value: $0.value) }
        log(level: .debug, message: message, fields: fields, file: file, line: line, function: function)
    }

    public func warning(_ message: String, metadata: [String: String] = [:],
                       file: String = #file, line: Int = #line, function: String = #function) {
        let fields = metadata.map { LogField(key: $0.key, value: $0.value) }
        log(level: .warning, message: message, fields: fields, file: file, line: line, function: function)
    }

    public func error(_ message: String, metadata: [String: String] = [:],
                     file: String = #file, line: Int = #line, function: String = #function) {
        let fields = metadata.map { LogField(key: $0.key, value: $0.value) }
        log(level: .error, message: message, fields: fields, file: file, line: line, function: function)
    }

    /**
     * NAME: logInfo
     *
     * DESCRIPTION:
     *     Simplified info logging with automatic caller information capture
     *
     * PARAMETERS:
     *     message - Info message
     *     fields - Optional structured fields
     */
    public func logInfo(
        _ message: String,
        fields: LogField...,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        log(level: .info, message: message, fields: Array(fields), file: file, line: line, function: function)
    }

    /**
     * NAME: logWarning
     *
     * DESCRIPTION:
     *     Simplified warning logging with automatic caller information capture
     *
     * PARAMETERS:
     *     message - Warning message
     *     fields - Optional structured fields
     */
    public func logWarning(
        _ message: String,
        fields: LogField...,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        log(level: .warning, message: message, fields: Array(fields), file: file, line: line, function: function)
    }

    /**
     * NAME: logError
     *
     * DESCRIPTION:
     *     Simplified error logging with automatic caller information capture
     *
     * PARAMETERS:
     *     message - Error message
     *     fields - Optional structured fields
     */
    public func logError(
        _ message: String,
        fields: LogField...,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        log(level: .error, message: message, fields: Array(fields), file: file, line: line, function: function)
    }

    /**
     * NAME: logCritical
     *
     * DESCRIPTION:
     *     Simplified critical logging with automatic caller information capture
     *
     * PARAMETERS:
     *     message - Critical message
     *     fields - Optional structured fields
     */
    public func logCritical(
        _ message: String,
        fields: LogField...,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        log(level: .critical, message: message, fields: Array(fields), file: file, line: line, function: function)
    }

    /**
     * NAME: trace
     *
     * DESCRIPTION:
     *     Simplified method tracing with automatic caller information capture
     *
     * PARAMETERS:
     *     methodName - Name of the method being traced
     */
    public func trace(
        _ methodName: String,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) -> () -> Void {
        return traceMethod(methodName, file: file, line: line, function: function)
    }

    /**
     * NAME: trace (error)
     *
     * DESCRIPTION:
     *     Simplified error tracing with automatic caller information capture
     *
     * PARAMETERS:
     *     error - Error to trace
     *     message - Additional error message
     *     fields - Optional structured fields
     */
    public func trace(
        _ error: Error,
        message: String,
        fields: LogField...,
        file: String = #file,
        line: Int = #line,
        function: String = #function
    ) {
        var errorFields = Array(fields)
        errorFields.append(LogField.error("error", error))
        log(level: .error, message: message, fields: errorFields, file: file, line: line, function: function)
    }
}

// MARK: - Logging Configuration

/**
 * NAME: LoggingConfiguration
 *
 * DESCRIPTION:
 *     Configuration for the logging system
 *
 * PROPERTIES:
 *     level - Minimum log level to output
 *     subsystem - OSLog subsystem identifier
 *     category - OSLog category for grouping
 *     includeCaller - Whether to include caller information
 *     enablePerformanceTracking - Whether to enable method performance tracking
 */
public struct LoggingConfiguration {
    public let level: LogLevel
    public let subsystem: String
    public let category: String
    public let includeCaller: Bool
    public let enablePerformanceTracking: Bool
    
    public init(
        level: LogLevel = .info,
        subsystem: String = "com.panabit.client",
        category: String = "default",
        includeCaller: Bool = true,
        enablePerformanceTracking: Bool = true
    ) {
        self.level = level
        self.subsystem = subsystem
        self.category = category
        self.includeCaller = includeCaller
        self.enablePerformanceTracking = enablePerformanceTracking
    }
    
    // Predefined configurations for different environments
    public static let development = LoggingConfiguration(
        level: .debug,
        subsystem: "com.panabit.client.dev",
        category: "development",
        includeCaller: true,
        enablePerformanceTracking: true
    )

    public static let production = LoggingConfiguration(
        level: .info,
        subsystem: "com.panabit.client",
        category: "production",
        includeCaller: false,
        enablePerformanceTracking: false
    )
    
    public static let networkExtension = LoggingConfiguration(
        level: .info,
        subsystem: "com.panabit.client.extension",
        category: "network",
        includeCaller: false,
        enablePerformanceTracking: true
    )
}

// MARK: - Logging System Manager

/**
 * NAME: LoggingSystem
 *
 * DESCRIPTION:
 *     Central logging system manager providing logger instances and configuration
 *
 * PROPERTIES:
 *     shared - Singleton instance
 *     defaultLogger - Default logger instance
 */
public final class LoggingSystem {
    public static let shared = LoggingSystem()
    
    private var configuration: LoggingConfiguration
    private var loggers: [String: LoggerProtocol] = [:]
    private let queue = DispatchQueue(label: "com.panabit.logging", qos: .utility)
    
    private init() {
        // 设置为生产环境配置，默认info级别
        self.configuration = .production
    }
    
    /**
     * NAME: configure
     *
     * DESCRIPTION:
     *     Configures the logging system with specified configuration
     *
     * PARAMETERS:
     *     configuration - Logging configuration
     */
    public func configure(with configuration: LoggingConfiguration) {
        queue.sync {
            self.configuration = configuration
            self.loggers.removeAll()
        }
    }
    
    /**
     * NAME: logger
     *
     * DESCRIPTION:
     *     Gets or creates a logger for the specified category
     *
     * PARAMETERS:
     *     category - Logger category (defaults to configuration category)
     *
     * RETURNS:
     *     LoggerProtocol - Logger instance
     */
    public func logger(for category: String? = nil) -> LoggerProtocol {
        let categoryKey = category ?? configuration.category
        
        return queue.sync {
            if let existingLogger = loggers[categoryKey] {
                return existingLogger
            }
            
            let newLogger = OSLogLogger(
                configuration: LoggingConfiguration(
                    level: configuration.level,
                    subsystem: configuration.subsystem,
                    category: categoryKey,
                    includeCaller: configuration.includeCaller,
                    enablePerformanceTracking: configuration.enablePerformanceTracking
                )
            )
            
            loggers[categoryKey] = newLogger
            return newLogger
        }
    }
    
    /**
     * NAME: defaultLogger
     *
     * DESCRIPTION:
     *     Gets the default logger instance
     *
     * RETURNS:
     *     LoggerProtocol - Default logger
     */
    public var defaultLogger: LoggerProtocol {
        return logger()
    }
}

// MARK: - Global Convenience Functions

/**
 * Global logging convenience functions for easy access with automatic caller information
 */
public func logDebug(
    _ message: String,
    fields: LogField...,
    file: String = #file,
    line: Int = #line,
    function: String = #function
) {
    LoggingSystem.shared.defaultLogger.log(level: .debug, message: message, fields: Array(fields), file: file, line: line, function: function)
}

public func logInfo(
    _ message: String,
    fields: LogField...,
    file: String = #file,
    line: Int = #line,
    function: String = #function
) {
    LoggingSystem.shared.defaultLogger.log(level: .info, message: message, fields: Array(fields), file: file, line: line, function: function)
}

public func logWarning(
    _ message: String,
    fields: LogField...,
    file: String = #file,
    line: Int = #line,
    function: String = #function
) {
    LoggingSystem.shared.defaultLogger.log(level: .warning, message: message, fields: Array(fields), file: file, line: line, function: function)
}

public func logError(
    _ message: String,
    fields: LogField...,
    file: String = #file,
    line: Int = #line,
    function: String = #function
) {
    LoggingSystem.shared.defaultLogger.log(level: .error, message: message, fields: Array(fields), file: file, line: line, function: function)
}

public func logCritical(
    _ message: String,
    fields: LogField...,
    file: String = #file,
    line: Int = #line,
    function: String = #function
) {
    LoggingSystem.shared.defaultLogger.log(level: .critical, message: message, fields: Array(fields), file: file, line: line, function: function)
}


