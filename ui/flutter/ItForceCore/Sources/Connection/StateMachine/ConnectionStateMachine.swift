/**
 * FILE: ConnectionStateMachine.swift
 *
 * DESCRIPTION:
 *     Connection state machine implementation for SDWAN ZZVPN protocol.
 *     Manages VPN connection state transitions and provides thread-safe state management.
 *     Compatible with Go backend state machine implementation.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import OSLog

/**
 * NAME: ConnectionState
 *
 * DESCRIPTION:
 *     iOS/macOS VPN connection state enumeration with platform-independent design.
 *     Represents all possible VPN connection states for NetworkExtension integration.
 *     Uses iOS-specific state values for platform independence.
 *
 * CASES:
 *     disconnected - VPN is disconnected and idle
 *     initializing - NetworkExtension initialization and DNS resolution
 *     serverResolved - Server IP obtained, ready for authentication
 *     authenticating - VPN authentication in progress
 *     connected - VPN tunnel active and data transmission ready
 *     authenticationFailed - Authentication failed, connection terminated
 */
public enum ConnectionState: UInt8, CaseIterable, Sendable {
    case disconnected = 0
    case initializing = 1
    case serverResolved = 2
    case authenticating = 3
    case connected = 4
    case authenticationFailed = 5
    
    /**
     * NAME: description
     *
     * DESCRIPTION:
     *     Returns human-readable state description.
     *
     * RETURNS:
     *     String - State description
     */
    public var description: String {
        switch self {
        case .initializing:
            return "INIT"
        case .serverResolved:
            return "INIT1"
        case .authenticating:
            return "AUTH"
        case .connected:
            return "DATA"
        case .disconnected:
            return "CLOSED"
        case .authenticationFailed:
            return "AUTHFAIL"
        }
    }
    
    /**
     * NAME: isConnected
     *
     * DESCRIPTION:
     *     Returns whether the state represents an active connection.
     *
     * RETURNS:
     *     Bool - True if connected
     */
    public var isConnected: Bool {
        return self == .connected
    }
    
    /**
     * NAME: isConnecting
     *
     * DESCRIPTION:
     *     Returns whether the state represents a connection in progress.
     *
     * RETURNS:
     *     Bool - True if connecting
     */
    public var isConnecting: Bool {
        switch self {
        case .initializing, .serverResolved, .authenticating:
            return true
        default:
            return false
        }
    }
}

// MARK: - Protocol Compatibility Extension

extension ConnectionState {
    /**
     * NAME: protocolValue
     *
     * DESCRIPTION:
     *     Maps iOS ConnectionState to Go backend protocol state values.
     *     Used only for SDWAN protocol communication, maintaining backward compatibility.
     *     iOS state management remains independent while protocol communication works correctly.
     *
     * RETURNS:
     *     UInt8 - Go backend compatible state value for protocol communication
     */
    public var protocolValue: UInt8 {
        switch self {
        case .disconnected:
            return 6        // StateClosed
        case .initializing:
            return 2        // StateInit
        case .serverResolved:
            return 3        // StateInit1
        case .authenticating:
            return 4        // StateAuth
        case .connected:
            return 5        // StateData
        case .authenticationFailed:
            return 7        // StateAuthFail
        }
    }

    /**
     * NAME: init(protocolValue:)
     *
     * DESCRIPTION:
     *     Creates ConnectionState from Go backend protocol state value.
     *     Used for parsing protocol responses while maintaining iOS state independence.
     *
     * PARAMETERS:
     *     protocolValue - Go backend state value from protocol communication
     */
    public init?(protocolValue: UInt8) {
        switch protocolValue {
        case 2:
            self = .initializing    // StateInit
        case 3:
            self = .serverResolved  // StateInit1
        case 4:
            self = .authenticating  // StateAuth
        case 5:
            self = .connected       // StateData
        case 6:
            self = .disconnected    // StateClosed
        case 7:
            self = .authenticationFailed // StateAuthFail
        default:
            return nil
        }
    }
}

/**
 * NAME: ConnectionError
 *
 * DESCRIPTION:
 *     Connection error information structure matching Go backend.
 *     Contains error code and message for debugging and user feedback.
 *
 * PROPERTIES:
 *     code - Error code
 *     message - Error message
 *     timestamp - Error occurrence time
 */
public struct ConnectionError: Error, LocalizedError, Sendable {
    public let code: Int
    public let message: String
    public let timestamp: Date

    public init(code: Int, message: String) {
        self.code = code
        self.message = message
        self.timestamp = Date()
    }

    public var errorDescription: String? {
        return message
    }

    public var localizedDescription: String {
        return message
    }
}

/**
 * NAME: SessionInfo
 *
 * DESCRIPTION:
 *     Session information structure.
 *     Contains session ID and authentication token.
 *
 * PROPERTIES:
 *     sessionID - Session identifier
 *     authToken - Authentication token
 */
public struct SessionInfo: Sendable {
    public let sessionID: UInt16
    public let authToken: UInt32
    
    public init(sessionID: UInt16, authToken: UInt32) {
        self.sessionID = sessionID
        self.authToken = authToken
    }
}

/**
 * NAME: StateChangeCallback
 *
 * DESCRIPTION:
 *     Type alias for state change callback functions.
 *     Called when connection state transitions occur.
 */
public typealias StateChangeCallback = @Sendable (ConnectionState, ConnectionState) -> Void

/**
 * NAME: ErrorCallback
 *
 * DESCRIPTION:
 *     Type alias for error callback functions.
 *     Called when connection errors occur.
 */
public typealias ErrorCallback = @Sendable (ConnectionError) -> Void

/**
 * NAME: PacketHandler
 *
 * DESCRIPTION:
 *     Type alias for packet handler functions.
 *     Called to process incoming packets based on packet type.
 */
public typealias PacketHandler = @Sendable (ParsedPacket) async throws -> Void

/**
 * NAME: ConnectionStateMachine
 *
 * DESCRIPTION:
 *     Thread-safe connection state machine using Swift Actor model.
 *     Manages VPN connection state transitions, session information, and callbacks.
 *     Provides compatibility with Go backend state machine functionality.
 *
 * PROPERTIES:
 *     currentState - Current connection state
 *     sessionInfo - Session information (ID and token)
 *     lastHeartbeat - Last heartbeat timestamp
 *     stateChangedTime - Last state change timestamp
 *     lastError - Last error information
 *     configuration - Configuration key-value pairs
 *     stateCallbacks - State change callback functions
 *     errorCallbacks - Error callback functions
 *     packetHandlers - Packet handler functions by packet type
 *     logger - Logger instance
 */
public actor ConnectionStateMachine {
    // MARK: - State Properties

    private var currentState: ConnectionState = .disconnected
    private var sessionInfo: SessionInfo?
    private var lastHeartbeat: Date = Date()
    private var stateChangedTime: Date = Date()
    private var configuration: [String: String] = [:]

    // Error tracking (matching Go backend)
    private var errorCode: Int = 0
    private var errorMessage: String = ""
    
    // MARK: - Callback Properties
    
    private var stateCallbacks: [StateChangeCallback] = []
    private var errorCallbacks: [ErrorCallback] = []
    private var packetHandlers: [UInt8: PacketHandler] = [:]
    
    // MARK: - Dependencies
    
    private let logger: LoggerProtocol
    
    // MARK: - Initialization
    
    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Initializes connection state machine with logger.
     *
     * PARAMETERS:
     *     logger - Logger instance for debugging
     */
    public init(logger: LoggerProtocol) {
        self.logger = logger
        self.currentState = .initializing
        self.lastHeartbeat = Date()
        self.stateChangedTime = Date()

        logger.info("Connection state machine initialized", metadata: [
            "initial_state": currentState.description
        ])
    }
    
    // MARK: - State Management
    
    /**
     * NAME: getState
     *
     * DESCRIPTION:
     *     Returns current connection state.
     *
     * RETURNS:
     *     ConnectionState - Current state
     */
    public func getState() -> ConnectionState {
        return currentState
    }
    
    /**
     * NAME: getStateDescription
     *
     * DESCRIPTION:
     *     Returns current state description with error message if applicable.
     *     Matches Go backend GetStateString functionality.
     *
     * RETURNS:
     *     String - State description
     */
    public func getStateDescription() -> String {
        if currentState == .authenticationFailed && !errorMessage.isEmpty {
            return "AUTHFAIL(\(errorMessage))"
        }
        return currentState.description
    }

    /**
     * NAME: setState
     *
     * DESCRIPTION:
     *     Sets new connection state and triggers callbacks.
     *     Matches Go backend SetState functionality.
     *
     * PARAMETERS:
     *     newState - New connection state
     */
    public func setState(_ newState: ConnectionState) {
        let oldState = currentState

        // Skip if state hasn't changed
        guard oldState != newState else {
            return
        }

        // Update state and timestamp
        currentState = newState
        stateChangedTime = Date()

        logger.info("State changed", metadata: [
            "old_state": "\(oldState.rawValue)",
            "new_state": "\(newState.rawValue)",
            "old_state_str": oldState.description,
            "new_state_str": newState.description
        ])

        // Trigger state change callbacks
        let callbacks = stateCallbacks
        Task {
            for callback in callbacks {
                callback(oldState, newState)
            }
        }
    }

    /**
     * NAME: setError
     *
     * DESCRIPTION:
     *     Sets error information and triggers error callbacks.
     *     Matches Go backend SetError functionality.
     *
     * PARAMETERS:
     *     code - Error code
     *     message - Error message
     */
    public func setError(code: Int, message: String) {
        errorCode = code
        errorMessage = message

        logger.error("Connection error", metadata: [
            "code": "\(code)",
            "message": message
        ])

        // Create error for callbacks
        let error = ConnectionError(code: code, message: message)

        // Trigger error callbacks
        let callbacks = errorCallbacks
        Task {
            for callback in callbacks {
                callback(error)
            }
        }
    }

    /**
     * NAME: getLastError
     *
     * DESCRIPTION:
     *     Returns last error information.
     *
     * RETURNS:
     *     ConnectionError? - Last error or nil
     */
    public func getLastError() -> ConnectionError? {
        if errorCode != 0 || !errorMessage.isEmpty {
            return ConnectionError(code: errorCode, message: errorMessage)
        }
        return nil
    }

    /**
     * NAME: clearError
     *
     * DESCRIPTION:
     *     Clears last error information.
     */
    public func clearError() {
        errorCode = 0
        errorMessage = ""
    }

    // MARK: - Session Management

    /**
     * NAME: setSessionInfo
     *
     * DESCRIPTION:
     *     Sets session information (ID and authentication token).
     *
     * PARAMETERS:
     *     sessionID - Session identifier
     *     authToken - Authentication token
     */
    public func setSessionInfo(sessionID: UInt16, authToken: UInt32) {
        sessionInfo = SessionInfo(sessionID: sessionID, authToken: authToken)

        logger.info("Session info updated", metadata: [
            "session_id": "\(sessionID)",
            "auth_token": "\(authToken)"
        ])
    }

    /**
     * NAME: getSessionInfo
     *
     * DESCRIPTION:
     *     Returns current session information.
     *
     * RETURNS:
     *     SessionInfo? - Session information or nil
     */
    public func getSessionInfo() -> SessionInfo? {
        return sessionInfo
    }

    /**
     * NAME: clearSessionInfo
     *
     * DESCRIPTION:
     *     Clears session information.
     */
    public func clearSessionInfo() {
        sessionInfo = nil
        logger.info("Session info cleared")
    }

    // MARK: - Heartbeat Management

    /**
     * NAME: updateHeartbeat
     *
     * DESCRIPTION:
     *     Updates last heartbeat timestamp to current time.
     */
    public func updateHeartbeat() {
        lastHeartbeat = Date()
    }

    /**
     * NAME: getLastHeartbeat
     *
     * DESCRIPTION:
     *     Returns last heartbeat timestamp.
     *
     * RETURNS:
     *     Date - Last heartbeat time
     */
    public func getLastHeartbeat() -> Date {
        return lastHeartbeat
    }

    /**
     * NAME: getTimeSinceLastHeartbeat
     *
     * DESCRIPTION:
     *     Returns time elapsed since last heartbeat.
     *
     * RETURNS:
     *     TimeInterval - Time since last heartbeat in seconds
     */
    public func getTimeSinceLastHeartbeat() -> TimeInterval {
        return Date().timeIntervalSince(lastHeartbeat)
    }

    /**
     * NAME: getStateChangedTime
     *
     * DESCRIPTION:
     *     Returns timestamp of last state change.
     *
     * RETURNS:
     *     Date - State change time
     */
    public func getStateChangedTime() -> Date {
        return stateChangedTime
    }

    // MARK: - Configuration Management

    /**
     * NAME: setConfiguration
     *
     * DESCRIPTION:
     *     Sets configuration key-value pairs.
     *
     * PARAMETERS:
     *     config - Configuration dictionary
     */
    public func setConfiguration(_ config: [String: String]) {
        for (key, value) in config {
            configuration[key] = value
        }

        // logger.debug("Configuration updated", metadata: [
        //     "keys": config.keys.joined(separator: ", ")
        // ]) // Debug log commented for production
    }

    /**
     * NAME: getConfiguration
     *
     * DESCRIPTION:
     *     Returns copy of current configuration.
     *
     * RETURNS:
     *     [String: String] - Configuration dictionary
     */
    public func getConfiguration() -> [String: String] {
        return configuration
    }

    /**
     * NAME: getConfigurationValue
     *
     * DESCRIPTION:
     *     Returns configuration value for given key.
     *
     * PARAMETERS:
     *     key - Configuration key
     *     defaultValue - Default value if key not found
     *
     * RETURNS:
     *     String - Configuration value or default
     */
    public func getConfigurationValue(key: String, defaultValue: String = "") -> String {
        return configuration[key] ?? defaultValue
    }

    /**
     * NAME: clearConfiguration
     *
     * DESCRIPTION:
     *     Clears all configuration data.
     */
    public func clearConfiguration() {
        configuration.removeAll()
        // logger.debug("Configuration cleared") // Debug log commented for production
    }

    // MARK: - Callback Management

    /**
     * NAME: registerStateChangeCallback
     *
     * DESCRIPTION:
     *     Registers callback function for state changes.
     *
     * PARAMETERS:
     *     callback - State change callback function
     */
    public func registerStateChangeCallback(_ callback: @escaping StateChangeCallback) {
        stateCallbacks.append(callback)
        // logger.debug("State change callback registered", metadata: [
        //     "total_callbacks": "\(stateCallbacks.count)"
        // ]) // Debug log commented for production
    }

    /**
     * NAME: registerErrorCallback
     *
     * DESCRIPTION:
     *     Registers callback function for errors.
     *
     * PARAMETERS:
     *     callback - Error callback function
     */
    public func registerErrorCallback(_ callback: @escaping ErrorCallback) {
        errorCallbacks.append(callback)
        // logger.debug("Error callback registered", metadata: [
        //     "total_callbacks": "\(errorCallbacks.count)"
        // ]) // Debug log commented for production
    }

    /**
     * NAME: clearCallbacks
     *
     * DESCRIPTION:
     *     Clears all registered callbacks.
     */
    public func clearCallbacks() {
        stateCallbacks.removeAll()
        errorCallbacks.removeAll()
        packetHandlers.removeAll()
        // logger.debug("All callbacks cleared") // Debug log commented for production
    }

    // MARK: - Packet Handling

    /**
     * NAME: registerPacketHandler
     *
     * DESCRIPTION:
     *     Registers packet handler for specific packet type.
     *
     * PARAMETERS:
     *     packetType - Packet type to handle
     *     handler - Packet handler function
     */
    public func registerPacketHandler(packetType: UInt8, handler: @escaping PacketHandler) {
        packetHandlers[packetType] = handler
        // logger.debug("Packet handler registered", metadata: [
        //     "packet_type": String(format: "0x%02X", packetType),
        //     "total_handlers": "\(packetHandlers.count)"
        // ]) // Debug log commented for production
    }

    /**
     * NAME: handlePacket
     *
     * DESCRIPTION:
     *     Handles incoming packet using registered handler.
     *
     * PARAMETERS:
     *     packet - Parsed packet to handle
     *
     * THROWS:
     *     ConnectionError - If no handler found or handler fails
     */
    public func handlePacket(_ packet: ParsedPacket) async throws {
        let packetType = packet.header.type.rawValue

        guard let handler = packetHandlers[packetType] else {
            let error = ConnectionError(
                code: 1002,
                message: "No handler for packet type: 0x\(String(format: "%02X", packetType))"
            )
            logger.warning("No packet handler found", metadata: [
                "packet_type": String(format: "0x%02X", packetType)
            ])
            throw error
        }

        do {
            try await handler(packet)
        } catch {
            let connectionError = ConnectionError(
                code: 1003,
                message: "Packet handler failed: \(error.localizedDescription)"
            )
            logger.error("Packet handler failed", metadata: [
                "packet_type": String(format: "0x%02X", packetType),
                "error": error.localizedDescription
            ])
            throw connectionError
        }
    }



    // MARK: - Reset and Cleanup

    /**
     * NAME: reset
     *
     * DESCRIPTION:
     *     Resets state machine to initial state.
     *     Clears session info, errors, and configuration.
     */
    public func reset() {
        currentState = .initializing
        sessionInfo = nil
        errorCode = 0
        errorMessage = ""
        configuration.removeAll()
        lastHeartbeat = Date()
        stateChangedTime = Date()

        logger.info("State machine reset to initial state")
    }

    /**
     * NAME: getStateSummary
     *
     * DESCRIPTION:
     *     Returns comprehensive state summary for debugging.
     *
     * RETURNS:
     *     [String: Any] - State summary dictionary
     */
    public func getStateSummary() -> [String: Any] {
        var summary: [String: Any] = [
            "state": currentState.description,
            "state_raw": currentState.rawValue,
            "state_changed_time": stateChangedTime,
            "last_heartbeat": lastHeartbeat,
            "time_since_heartbeat": getTimeSinceLastHeartbeat(),
            "is_connected": currentState.isConnected,
            "is_connecting": currentState.isConnecting
        ]

        if let session = sessionInfo {
            summary["session_id"] = session.sessionID
            summary["auth_token"] = session.authToken
        }

        if errorCode != 0 || !errorMessage.isEmpty {
            summary["last_error_code"] = errorCode
            summary["last_error_message"] = errorMessage
        }

        summary["configuration_keys"] = Array(configuration.keys)
        summary["callback_counts"] = [
            "state_callbacks": stateCallbacks.count,
            "error_callbacks": errorCallbacks.count,
            "packet_handlers": packetHandlers.count
        ]

        return summary
    }
}
