/**
 * FILE: NetworkStatistics.swift
 *
 * DESCRIPTION:
 *     Network statistics and delay measurement for SDWAN connections.
 *     Provides delay tracking for heartbeat packets and network quality monitoring.
 *     Compatible with Go backend delay measurement functionality.
 *
 * AUTHOR: wei
 * HISTORY: 30/06/2025 create network statistics for heartbeat delay tracking
 */

import Foundation
import OSLog

/**
 * NAME: NetworkStatistics
 *
 * DESCRIPTION:
 *     Thread-safe network statistics tracker using Swift Actor model.
 *     Tracks network delay, packet counts, and connection quality metrics.
 *     Provides delay statistics for heartbeat packets.
 *
 * PROPERTIES:
 *     currentDelay - Current round-trip delay in milliseconds
 *     minDelay - Minimum recorded delay in milliseconds
 *     maxDelay - Maximum recorded delay in milliseconds
 *     averageDelay - Average delay over recent measurements
 *     packetsSent - Total packets sent
 *     packetsReceived - Total packets received
 *     bytesTransferred - Total bytes transferred
 */
public actor NetworkStatistics {
    
    // MARK: - Properties
    
    private var currentDelay: UInt32 = 0
    private var minDelay: UInt32 = UInt32.max
    private var maxDelay: UInt32 = 0
    private var averageDelay: Double = 0.0
    
    private var packetsSent: UInt64 = 0
    private var packetsReceived: UInt64 = 0
    private var bytesTransferred: UInt64 = 0
    
    private var delayMeasurements: [UInt32] = []
    private let maxMeasurements = 100  // Keep last 100 measurements for average
    
    private let logger = OSLog(subsystem: "ItForceCore", category: "NetworkStatistics")

    // MARK: - Initialization

    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Creates a new network statistics tracker.
     */
    public init() {
        os_log("Network statistics tracker initialized", log: logger, type: .info)
    }
    
    // MARK: - Delay Measurement
    
    /**
     * NAME: recordDelay
     *
     * DESCRIPTION:
     *     Records a new delay measurement and updates statistics.
     *     Updates current, min, max, and average delay values.
     *
     * PARAMETERS:
     *     delay - Delay measurement in milliseconds
     */
    public func recordDelay(_ delay: UInt32) {
        currentDelay = delay
        
        // Update min/max delays
        if delay < minDelay {
            minDelay = delay
        }
        if delay > maxDelay {
            maxDelay = delay
        }
        
        // Add to measurements for average calculation
        delayMeasurements.append(delay)
        if delayMeasurements.count > maxMeasurements {
            delayMeasurements.removeFirst()
        }
        
        // Calculate average
        if !delayMeasurements.isEmpty {
            let sum = delayMeasurements.reduce(0, +)
            averageDelay = Double(sum) / Double(delayMeasurements.count)
        }
        
        // os_log("Delay recorded: %{public}dms (min: %{public}d, max: %{public}d, avg: %.1f)", log: logger, type: .debug, delay, minDelay, maxDelay, averageDelay) // Debug log commented for production
    }
    
    /**
     * NAME: getCurrentDelay
     *
     * DESCRIPTION:
     *     Returns current delay measurement.
     *
     * RETURNS:
     *     UInt32 - Current delay in milliseconds
     */
    public func getCurrentDelay() -> UInt32 {
        return currentDelay
    }
    
    /**
     * NAME: getMinDelay
     *
     * DESCRIPTION:
     *     Returns minimum recorded delay.
     *
     * RETURNS:
     *     UInt32 - Minimum delay in milliseconds (0 if no measurements)
     */
    public func getMinDelay() -> UInt32 {
        return minDelay == UInt32.max ? 0 : minDelay
    }
    
    /**
     * NAME: getMaxDelay
     *
     * DESCRIPTION:
     *     Returns maximum recorded delay.
     *
     * RETURNS:
     *     UInt32 - Maximum delay in milliseconds
     */
    public func getMaxDelay() -> UInt32 {
        return maxDelay
    }
    
    /**
     * NAME: getAverageDelay
     *
     * DESCRIPTION:
     *     Returns average delay over recent measurements.
     *
     * RETURNS:
     *     Double - Average delay in milliseconds
     */
    public func getAverageDelay() -> Double {
        return averageDelay
    }
    
    /**
     * NAME: getDelayStatistics
     *
     * DESCRIPTION:
     *     Returns complete delay statistics for heartbeat packets.
     *     Compatible with Go backend delay parameters.
     *
     * RETURNS:
     *     (current: UInt32, min: UInt32, max: UInt32) - Delay statistics
     */
    public func getDelayStatistics() -> (current: UInt32, min: UInt32, max: UInt32) {
        return (
            current: currentDelay,
            min: minDelay == UInt32.max ? 0 : minDelay,
            max: maxDelay
        )
    }
    
    // MARK: - Packet Statistics
    
    /**
     * NAME: recordPacketSent
     *
     * DESCRIPTION:
     *     Records a sent packet and updates statistics.
     *
     * PARAMETERS:
     *     bytes - Number of bytes sent
     */
    public func recordPacketSent(bytes: Int) {
        packetsSent += 1
        bytesTransferred += UInt64(bytes)
    }
    
    /**
     * NAME: recordPacketReceived
     *
     * DESCRIPTION:
     *     Records a received packet and updates statistics.
     *
     * PARAMETERS:
     *     bytes - Number of bytes received
     */
    public func recordPacketReceived(bytes: Int) {
        packetsReceived += 1
        bytesTransferred += UInt64(bytes)
    }
    
    /**
     * NAME: getPacketStatistics
     *
     * DESCRIPTION:
     *     Returns packet transmission statistics.
     *
     * RETURNS:
     *     (sent: UInt64, received: UInt64, bytes: UInt64) - Packet statistics
     */
    public func getPacketStatistics() -> (sent: UInt64, received: UInt64, bytes: UInt64) {
        return (sent: packetsSent, received: packetsReceived, bytes: bytesTransferred)
    }
    
    // MARK: - Reset and Maintenance
    
    /**
     * NAME: resetStatistics
     *
     * DESCRIPTION:
     *     Resets all statistics to initial values.
     */
    public func resetStatistics() {
        currentDelay = 0
        minDelay = UInt32.max
        maxDelay = 0
        averageDelay = 0.0
        packetsSent = 0
        packetsReceived = 0
        bytesTransferred = 0
        delayMeasurements.removeAll()
        
        os_log("Network statistics reset", log: logger, type: .info)
    }
    
    /**
     * NAME: getStatisticsSummary
     *
     * DESCRIPTION:
     *     Returns comprehensive statistics summary for debugging.
     *
     * RETURNS:
     *     [String: Any] - Statistics summary dictionary
     */
    public func getStatisticsSummary() -> [String: Any] {
        return [
            "current_delay_ms": currentDelay,
            "min_delay_ms": minDelay == UInt32.max ? 0 : minDelay,
            "max_delay_ms": maxDelay,
            "average_delay_ms": averageDelay,
            "packets_sent": packetsSent,
            "packets_received": packetsReceived,
            "bytes_transferred": bytesTransferred,
            "measurement_count": delayMeasurements.count
        ]
    }
}

/**
 * NAME: DelayMeasurement
 *
 * DESCRIPTION:
 *     Helper class for measuring round-trip delay.
 *     Tracks start time and calculates elapsed time for delay measurement.
 */
public class DelayMeasurement {
    private let startTime: Date
    
    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Creates a new delay measurement starting now.
     */
    public init() {
        startTime = Date()
    }
    
    /**
     * NAME: getElapsedMilliseconds
     *
     * DESCRIPTION:
     *     Returns elapsed time since measurement started.
     *
     * RETURNS:
     *     UInt32 - Elapsed time in milliseconds
     */
    public func getElapsedMilliseconds() -> UInt32 {
        let elapsed = Date().timeIntervalSince(startTime)
        return UInt32(elapsed * 1000) // Convert to milliseconds
    }
}
