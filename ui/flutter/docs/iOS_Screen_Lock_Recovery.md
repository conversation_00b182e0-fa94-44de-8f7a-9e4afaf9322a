# iOS 锁屏/唤醒 VPN 重连功能

## 概述

本文档描述了iOS平台锁屏/唤醒VPN重连功能的实现方案。该功能解决了iOS设备在锁屏后VPN连接失效的问题，通过监听设备锁屏/唤醒事件，自动触发VPN重连流程。

## 问题背景

### 主要问题
- iOS设备锁屏后，VPN连接可能因为系统资源管理而失效
- 网络接口在锁屏/唤醒过程中可能发生切换
- 心跳包在锁屏期间无法正常发送，导致服务器端断开连接
- 用户解锁设备后需要手动重新连接VPN

### 影响范围
- 所有iOS设备（iPhone、iPad）
- 设置了密码/生物识别的设备效果最佳
- 无密码设备通过应用生命周期事件提供基础支持

## 技术方案

### 架构设计

```
┌─────────────────┐    ┌──────────────────────┐    ┌─────────────────┐
│   AppDelegate   │───▶│ PlatformChannelHandler │───▶│   VPNService    │
│                 │    │                      │    │                 │
│ 事件监听         │    │ 状态检查 & 重连触发    │    │ 断开 & 重连     │
│ - 锁屏/唤醒     │    │ - isVPNConnected     │    │ - disconnect    │
│ - 应用生命周期   │    │ - getVPNState        │    │ - connect       │
└─────────────────┘    └──────────────────────┘    └─────────────────┘
```

### 核心组件

#### 1. AppDelegate 事件监听
- **protectedDataDidBecomeAvailableNotification**: 设备解锁事件
- **protectedDataWillBecomeUnavailableNotification**: 设备即将锁定事件
- **applicationWillEnterForeground**: 应用回到前台（辅助检测）
- **applicationDidEnterBackground**: 应用进入后台

#### 2. PlatformChannelHandler 状态管理
- **isVPNConnected()**: 检查VPN连接状态
- **getVPNConnectionState()**: 获取详细状态信息
- **triggerScreenLockRecoveryReconnection()**: 触发重连流程

#### 3. VPNService 重连逻辑
- **performUnifiedDisconnect()**: 统一断开连接
- **connect()**: 重新建立连接

## 实现细节

### 事件监听机制

```swift
// 设置锁屏/唤醒事件监听
private func setupScreenLockUnlockNotifications() {
    let notificationCenter = NotificationCenter.default
    
    // 监听设备解锁事件
    notificationCenter.addObserver(
        self,
        selector: #selector(deviceDidUnlock),
        name: UIApplication.protectedDataDidBecomeAvailableNotification,
        object: nil
    )
    
    // 监听设备锁定事件
    notificationCenter.addObserver(
        self,
        selector: #selector(deviceWillLock),
        name: UIApplication.protectedDataWillBecomeUnavailableNotification,
        object: nil
    )
}
```

### 智能重连判断

重连触发需要满足以下条件：
1. **必要条件**：
   - VPN在锁屏前处于连接状态
   - 当前VPN连接异常或失效
   - 不在最小重连间隔内（防止频繁重连）

2. **排除条件**：
   - VPN正在连接过程中
   - 锁屏时间过短（< 5秒）
   - 用户主动断开连接

### 重连流程

```swift
public func triggerScreenLockRecoveryReconnection(reason: String, lockDuration: TimeInterval) async -> Bool {
    do {
        // 1. 执行统一断开连接
        await vpnService.performUnifiedDisconnect(
            reason: .systemDisconnected,
            source: .userAction
        )
        
        // 2. 等待断开完成
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // 3. 重新连接
        try await vpnService.connect()
        
        return true
    } catch {
        return false
    }
}
```

## Apple 官方指导原则

### 使用的官方API
- ✅ `UIApplication.protectedDataDidBecomeAvailableNotification`
- ✅ `UIApplication.protectedDataWillBecomeUnavailableNotification`
- ✅ `UIApplication.willEnterForegroundNotification`
- ✅ `UIApplication.didEnterBackgroundNotification`

### 避免的私有API
- ❌ `com.apple.springboard.lockcomplete`
- ❌ `com.apple.springboard.lockstate`
- ❌ 其他SpringBoard私有通知

## 使用限制

### API限制
1. **protectedData通知**仅在设备设置了密码/生物识别时有效
2. 无密码设备依赖应用生命周期事件（精度较低）
3. Face ID解锁可能触发多次通知

### 系统限制
1. iOS系统可能在长时间锁屏后终止VPN扩展
2. 网络环境变化可能影响重连成功率
3. 企业策略或家长控制可能限制VPN功能

## 测试验证

### 测试场景
1. **基础功能测试**
   - 设备锁屏 → 解锁 → 验证VPN自动重连
   - 应用后台 → 前台 → 验证VPN状态检查

2. **边界条件测试**
   - 短时间锁屏（< 5秒）→ 不应触发重连
   - 频繁锁屏/解锁 → 防止重连风暴
   - VPN未连接状态 → 不应触发重连

3. **错误处理测试**
   - 重连失败场景
   - 网络异常场景
   - VPN服务不可用场景

### 测试文件
- `ItForceCore/Tests/ScreenLockRecoveryTests.swift`

## 日志监控

### 关键日志
```
🔒 [AppDelegate] Device will lock - VPN was connected: true
🔓 [AppDelegate] Device unlocked - lock duration: 15.2s
🔄 [AppDelegate] Triggering VPN reconnection due to screen_unlock
✅ [AppDelegate] VPN reconnection triggered successfully
```

### 调试信息
- 锁屏时间记录
- VPN状态变化
- 重连触发条件判断
- 重连成功/失败状态

## 性能考虑

### 优化措施
1. **防抖机制**：最小重连间隔（10秒）
2. **状态缓存**：避免重复状态查询
3. **异步处理**：所有操作使用async/await
4. **资源清理**：deinit时移除通知监听

### 内存管理
- 使用弱引用避免循环引用
- 及时清理通知监听器
- 避免长时间持有大对象

## 兼容性

### iOS版本支持
- iOS 14.0+ （protectedData通知）
- iOS 13.0+ （应用生命周期事件）

### 设备支持
- iPhone（所有型号）
- iPad（所有型号）
- 有密码/生物识别设备（最佳体验）
- 无密码设备（基础支持）

## 故障排除

### 常见问题
1. **重连不触发**
   - 检查设备是否设置密码
   - 确认VPN在锁屏前已连接
   - 查看日志中的条件判断

2. **重连失败**
   - 检查网络连接
   - 确认VPN服务可用
   - 查看错误日志

3. **频繁重连**
   - 检查防抖机制
   - 确认锁屏时间阈值
   - 调整重连间隔

### 调试步骤
1. 启用详细日志
2. 监控事件触发
3. 检查状态判断逻辑
4. 验证重连流程
