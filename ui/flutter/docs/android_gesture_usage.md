# Android手势功能使用指南

## 功能概述

Android版本的Flutter应用现已实现以下手势功能，确保与iOS版本保持一致的用户体验：

1. **侧边栏手势**
   - 向右滑动：从屏幕左边缘向右滑动打开侧边栏
   - 向左滑动：当侧边栏打开时，向左滑动关闭侧边栏

2. **应用最小化手势**
   - 当没有侧边栏时，向左或向右滑动将应用最小化到后台
   - 替代了Android系统默认的返回手势，避免直接退出应用

## 技术实现

### 核心组件

1. **AndroidGestureService**
   - 处理手势识别和响应
   - 管理侧边栏打开/关闭
   - 通过原生方法实现应用最小化

2. **AndroidGestureWrapper**
   - 包装子Widget提供手势支持
   - 使用PopScope禁用系统返回手势
   - 确保只在Android平台生效

3. **原生集成**
   - 通过MethodChannel实现应用最小化
   - 使用moveTaskToBack将应用移到后台

### 平台兼容性

- **Android平台**：启用全部手势功能
- **iOS/macOS/Windows/Linux**：完全不受影响，保持原有行为

## 使用方法

### 基本用法

在MainScreen中，已经集成了AndroidGestureBuilder.wrap()方法：

```dart
// 使用Android手势包装器包装Scaffold内容
final gestureWrappedContent = AndroidGestureBuilder.wrap(
  scaffoldKey: _scaffoldKey,
  onDrawerToggle: () {
    // 侧边栏切换回调
  },
  child: scaffoldContent,
);
```

### 手动集成

如果需要在其他页面添加手势支持：

```dart
import '../widgets/android_gesture_wrapper.dart';

// 在Widget树中包装
return AndroidGestureBuilder.wrap(
  scaffoldKey: yourScaffoldKey,
  child: yourWidget,
);
```

### 条件启用

可以根据条件启用或禁用手势：

```dart
AndroidGestureBuilder.conditional(
  condition: shouldEnableGestures,
  scaffoldKey: scaffoldKey,
  child: childWidget,
);
```

## 手势阈值配置

当前配置的手势阈值：

```dart
static const double _horizontalSwipeThreshold = 50.0; // 水平滑动最小距离
static const double _verticalSwipeThreshold = 100.0;  // 垂直滑动最大允许距离
static const double _velocityThreshold = 500.0;       // 滑动速度阈值
static const double _edgeSwipeZone = 50.0;            // 边缘滑动区域宽度
```

这些阈值经过测试，能够提供良好的用户体验，避免误触发。

## 用户体验说明

### 侧边栏手势

- **打开侧边栏**：从屏幕左边缘向右滑动
- **关闭侧边栏**：当侧边栏打开时，向左滑动

### 应用最小化手势

- **无侧边栏时**：向左或向右滑动将应用最小化到后台
- **有侧边栏时**：向右滑动打开侧边栏，向左滑动关闭侧边栏

### 系统返回手势

- Android系统默认的返回手势被禁用
- 用户仍可以使用系统返回按钮，此时会先关闭侧边栏（如果打开），否则最小化应用

## 测试与验证

已进行以下测试确保功能正常：

1. **单元测试**：验证手势逻辑的正确性
2. **平台兼容性测试**：确保只在Android平台生效
3. **边界条件测试**：验证各种边界情况的处理

## 故障排除

### 常见问题

1. **手势不响应**
   - 检查是否在Android平台
   - 确认AndroidGestureBuilder.wrap()正确使用
   - 验证Scaffold键是否正确传递

2. **应用仍然退出而不是最小化**
   - 确认原生MethodChannel正确配置
   - 检查moveTaskToBack方法是否正常工作

3. **侧边栏手势与其他手势冲突**
   - 调整手势阈值
   - 检查是否有其他GestureDetector拦截了事件

### 日志调试

AndroidGestureService会记录详细的日志信息：

```
AndroidGestureService: Android gesture service initialized successfully
AndroidGestureService: Drawer opened via gesture
AndroidGestureService: App minimized to background via gesture
```

可以通过这些日志追踪手势处理流程。

## 未来改进

1. **可配置阈值**：允许通过配置文件调整手势阈值
2. **更多手势支持**：添加更多Android平台特定的手势
3. **性能优化**：减少手势处理的计算开销

## 注意事项

- 此功能仅在Android平台生效，不会影响其他平台
- 手势功能可以通过AndroidGestureBuilder.wrap()的enableGestures参数禁用
- 如果遇到与其他手势冲突，可以调整手势阈值或使用条件启用
