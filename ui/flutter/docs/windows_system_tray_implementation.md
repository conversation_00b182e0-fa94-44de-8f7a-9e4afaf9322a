# Windows 系统托盘功能实现文档

## 概述

本文档描述了为 Windows 客户端实现的系统托盘功能，包括窗口关闭行为修改、托盘图标管理、右键菜单和焦点管理等核心功能。

## 功能特性

### 1. 窗口关闭行为
- **原有行为**：点击窗口右上角关闭按钮直接退出应用
- **新行为**：在 Windows 平台点击关闭按钮时隐藏到系统托盘，而不是退出应用
- **其他平台**：保持原有的完全退出行为

### 2. 系统托盘图标
- 使用应用主图标（`assets/icons/app_icon.ico`）
- 显示应用名称作为提示文本
- 支持左键和右键交互

### 3. 托盘交互功能
- **左键点击**：切换窗口显示/隐藏状态
- **右键点击**：显示上下文菜单
- **菜单选项**：
  - "显示窗口" / "隐藏窗口"（根据当前状态动态切换）
  - "退出应用"

### 4. 完整退出流程
通过托盘菜单选择"退出"时，执行与原有退出逻辑一致的完整流程：
1. 断开 VPN 连接（如果已连接）
2. 停止后端服务（通过 API 和 BackendService）
3. 清理托盘资源
4. 关闭应用窗口
5. 退出应用进程

## 核心实现

### 1. TrayService 类
位置：`lib/services/tray_service.dart`

主要方法：
- `initialize()`: 初始化托盘服务
- `hideWindow()` / `showWindow()`: 窗口显示/隐藏控制
- `dispose()`: 清理托盘资源
- `_exitApplication()`: 完整的应用退出流程

### 2. 窗口关闭行为修改
修改的文件：
- `lib/services/window_close_handler.dart`
- `lib/widgets/custom_window_frame.dart`

实现逻辑：
```dart
// 在 Windows 平台且托盘服务可用时，隐藏到托盘
if (Platform.isWindows && _trayService != null) {
  await _trayService!.hideWindow();
  return;
}
// 其他情况执行完整退出流程
```

### 3. 依赖注入集成
在 `lib/core/dependency_injection.dart` 中注册 TrayService：
```dart
// 系统托盘服务 - 仅Windows平台需要
serviceLocator.registerLazySingleton<TrayService>(
  () => TrayService(logService: serviceLocator<LogService>()),
);
```

### 4. 应用初始化集成
在 `lib/services/app_initialization_service.dart` 中初始化托盘服务：
```dart
// 初始化系统托盘服务（仅Windows平台需要）
if (Platform.isWindows) {
  await _initializeTrayService(logService);
}
```

## 菜单焦点管理问题解决方案

### 问题描述
在某些情况下，Windows 系统托盘的右键菜单可能不会自动关闭，导致菜单残留在屏幕上。

### 解决方案

#### 1. 事件处理时机优化
- 将菜单显示从 `onTrayIconRightMouseDown` 移动到 `onTrayIconRightMouseUp`
- 这符合 Windows 系统托盘的标准行为模式

#### 2. 焦点管理增强
实现 `_ensureMenuFocusManagement()` 方法：
```dart
Future<void> _ensureMenuFocusManagement() async {
  if (Platform.isWindows) {
    final isVisible = await windowManager.isVisible();
    if (isVisible) {
      await windowManager.focus();
    } else {
      // 临时显示/隐藏窗口来重置焦点状态
      await windowManager.show();
      await Future.delayed(const Duration(milliseconds: 10));
      await windowManager.hide();
    }
  }
}
```

#### 3. 异步菜单显示
实现 `_showContextMenuWithFocusManagement()` 方法，确保菜单显示前进行适当的焦点管理。

#### 4. 多点焦点管理
在以下事件中调用焦点管理：
- 右键菜单显示前
- 左键点击托盘图标时
- 菜单项点击后

## 使用说明

### 用户体验
1. **隐藏到托盘**：点击窗口右上角的关闭按钮，应用隐藏到系统托盘
2. **恢复窗口**：左键点击托盘图标或右键选择"显示窗口"
3. **完全退出**：右键点击托盘图标，选择"退出应用"

### 开发者注意事项
1. **平台限制**：托盘功能仅在 Windows 平台启用
2. **资源管理**：应用退出时会自动清理托盘资源
3. **错误处理**：托盘服务初始化失败不会阻止应用启动
4. **日志记录**：所有托盘操作都有详细的日志记录

## 依赖包

- `tray_manager: ^0.5.0`: 跨平台系统托盘管理
- `window_manager: ^0.3.9`: 窗口管理功能

## 测试建议

### 功能测试
1. 验证窗口关闭按钮行为（隐藏而非退出）
2. 测试托盘图标左键点击（窗口显示/隐藏切换）
3. 测试托盘图标右键菜单显示和选项功能
4. 验证完整退出流程的正确性

### 焦点管理测试
1. 右键点击托盘图标显示菜单
2. 鼠标移开菜单区域，验证菜单是否自动关闭
3. 点击桌面其他位置，验证菜单是否消失
4. 测试菜单项点击后的正确响应

## 已知问题和限制

1. **平台限制**：仅支持 Windows 平台
2. **焦点管理**：在某些 Windows 版本或系统配置下，菜单焦点管理可能仍需要进一步优化
3. **图标资源**：需要确保 `assets/icons/app_icon.ico` 文件存在且格式正确

## 未来改进方向

1. **多语言支持**：完善托盘菜单的国际化支持
2. **更多菜单选项**：根据需要添加更多快捷操作
3. **通知集成**：结合系统通知功能提供更好的用户体验
4. **主题适配**：支持系统深色/浅色主题的托盘图标切换
