# iOS VPN应用代码审查规则文档

## 概述
本文档定义了iOS VPN应用全面模块功能审查和代码重构的详细规则和标准，确保代码质量、架构合规性和Apple平台最佳实践。

## 审查规则（按优先级排序）

### 1. 功能正确性验证 (Priority 1)

#### 1.1 VPN核心功能
- **连接功能**
  - [ ] VPN连接流程完整且正确（open packet → openack response）
  - [ ] 8秒总超时，2秒单次尝试超时，最多3次重试
  - [ ] UDP连接复用而非每次操作创建新连接
  - [ ] 连接失败时正确进入错误状态

- **断开功能**
  - [ ] 断开方法幂等性，包含状态检查
  - [ ] 一致的断开流程跨所有组件
  - [ ] 错误状态到断开状态的显式转换

- **状态管理**
  - [ ] VPNState统一状态管理实现
  - [ ] 状态转换逻辑正确且一致
  - [ ] 避免复杂的统一断开协调模式

#### 1.2 网络接口变化处理
- **重连逻辑**
  - [ ] 仅在VPN连接状态时监控网络接口变化
  - [ ] 不同活跃接口间变化触发重连
  - [ ] 空接口到可用接口变化不触发重连
  - [ ] WiFi到蜂窝网络切换正常工作

- **网络配置**
  - [ ] 服务器列表中所有服务器添加到排除路由
  - [ ] 隧道IP使用OpenAck响应的IP，/32掩码
  - [ ] DNS配置正确

#### 1.3 Flutter通信
- **Method Channel**
  - [ ] 所有方法调用正确实现
  - [ ] 参数类型转换正确
  - [ ] 错误处理和返回值正确

- **Event Channel**
  - [ ] 状态变化事件正确发送
  - [ ] 事件数据格式正确
  - [ ] 事件监听器生命周期管理

### 2. 架构合规性检查 (Priority 2)

#### 2.1 简化架构原则
- **VPNService统一管理**
  - [ ] VPNService管理所有功能，避免过度抽象
  - [ ] 避免不必要的抽象层和平台通道处理器
  - [ ] 避免多个服务实例，使用统一组件

- **层次职责分离**
  - [ ] Infrastructure层：基础设施和工具
  - [ ] Protocol层：协议实现和加密
  - [ ] Platform层：平台特定功能
  - [ ] Service层：业务逻辑服务
  - [ ] Connection层：连接管理

#### 2.2 异步模式
- **后台线程执行**
  - [ ] 长时间操作在后台线程执行
  - [ ] 状态标志用于结果报告而非阻塞操作
  - [ ] 避免阻塞主线程

- **状态管理**
  - [ ] 使用VPNState统一状态管理
  - [ ] 避免在不同组件中实现单独的状态过滤逻辑

#### 2.3 组件简化
- **移除重复功能**
  - [ ] ServerManager保留ping更新功能
  - [ ] 移除ServerService的重连功能
  - [ ] 移除重复的网络监控机制
  - [ ] AppLifecycleService简化实现

### 3. Apple平台最佳实践合规 (Priority 3)

#### 3.1 Swift Concurrency
- **Actor隔离**
  - [ ] NSObject delegate方法在主线程调用
  - [ ] 使用Task { @MainActor in }处理delegate回调
  - [ ] Sendable协议正确实现

#### 3.2 NetworkExtension
- **VPN配置**
  - [ ] NetworkExtension VPN配置正确
  - [ ] 生命周期管理正确
  - [ ] 不检查VPN Extension状态再发起新连接

- **PacketTunnelProvider**
  - [ ] 包处理逻辑正确
  - [ ] 异常包丢弃而非停止接收循环
  - [ ] 状态管理正确

#### 3.3 日志系统
- **参数使用**
  - [ ] 使用#file, #line, #function作为默认参数
  - [ ] 使用createDevelopmentLogger进行配置
  - [ ] iOS调试日志在flutter run控制台可见

#### 3.4 系统集成
- **语言初始化**
  - [ ] 基于iOS系统语言设置初始化
  - [ ] 用户修改保存到SharedPreferences优先
  - [ ] SharedPreferences内容格式与原mobile_old版本完全相同

### 4. 代码质量和错误排查 (Priority 4)

#### 4.1 Bug排查
- **内存管理**
  - [ ] 无内存泄漏
  - [ ] 正确的对象生命周期管理
  - [ ] 循环引用检查

- **并发问题**
  - [ ] 无竞态条件
  - [ ] 无永久阻塞
  - [ ] 线程安全检查

#### 4.2 异常处理
- **UDP连接**
  - [ ] UDP连接生命周期管理正确
  - [ ] 异常处理完善
  - [ ] 连接复用正确实现

- **协议处理**
  - [ ] 协议字段和加密/解密方法与Go后端完全匹配
  - [ ] 心跳超时基于丢失响应计数而非5秒轮询
  - [ ] 包接收器丢弃异常包而非停止

#### 4.3 编译质量
- **零错误要求**
  - [ ] 编译零错误
  - [ ] 编译零警告
  - [ ] 零信息级代码分析问题

### 5. 代码简化和清理 (Priority 5)

#### 5.1 重复功能清理
- **功能去重**
  - [ ] 每个功能只保留一个版本
  - [ ] 删除重复的实现
  - [ ] 统一接口和方法

#### 5.2 向前兼容代码清理
- **清理项目**
  - [ ] 移除所有向前兼容代码
  - [ ] 删除未使用的文件
  - [ ] 删除未使用的接口和方法

#### 5.3 平台检测简化
- **检测方法统一**
  - [ ] 只保留硬件信息检测方法
  - [ ] 移除重复的平台检测方法
  - [ ] 移除Platform API检测方法
  - [ ] 初始化时缓存检测结果

## 审查流程

### 阶段1：文件清单和优先级
1. 列出所有Swift文件
2. 按架构层次分组
3. 确定审查优先级顺序

### 阶段2：逐文件审查
1. 从Infrastructure层开始
2. 按优先级规则逐项检查
3. 记录发现的问题
4. 制定修复计划

### 阶段3：重构实施
1. 按优先级修复问题
2. 删除重复和未使用代码
3. 优化架构和性能
4. 确保编译无错误

### 阶段4：验证测试
1. 功能测试
2. 性能测试
3. 集成测试
4. 最终验证

## 检查清单模板

每个文件审查使用以下模板：

```
文件：[文件路径]
层次：[Infrastructure/Protocol/Platform/Service/Connection/Application]

功能正确性：
- [ ] 核心功能实现正确
- [ ] 错误处理完善
- [ ] 状态管理正确

架构合规性：
- [ ] 层次职责正确
- [ ] 依赖关系合理
- [ ] 接口设计清晰

Apple最佳实践：
- [ ] Swift Concurrency正确使用
- [ ] 系统API正确调用
- [ ] 生命周期管理正确

代码质量：
- [ ] 无明显bug
- [ ] 性能合理
- [ ] 可维护性好

简化清理：
- [ ] 无重复代码
- [ ] 无未使用代码
- [ ] 接口简洁

问题记录：
[记录发现的具体问题]

修复建议：
[提出具体的修复建议]
```

## Swift文件清单和审查优先级

### 文件分组（按架构层次）

#### 1. Infrastructure层 (基础设施层) - 优先级1
```
ItForceCore/Sources/Infrastructure/Common/ServiceDelegate.swift
ItForceCore/Sources/Infrastructure/Common/ServiceLifecycle.swift
ItForceCore/Sources/Infrastructure/Configuration/BaseConfiguration.swift
ItForceCore/Sources/Infrastructure/Configuration/Configurations.swift
ItForceCore/Sources/Infrastructure/DataBufferPool/BufferPoolManager.swift
ItForceCore/Sources/Infrastructure/DataBufferPool/DataBufferPool.swift
ItForceCore/Sources/Infrastructure/ErrorHandling/ErrorHandler.swift
ItForceCore/Sources/Infrastructure/ErrorHandling/ErrorSystem.swift
ItForceCore/Sources/Infrastructure/ErrorHandling/ItForceError.swift
ItForceCore/Sources/Infrastructure/Logging/LoggingSystem.swift
ItForceCore/Sources/Infrastructure/Logging/OSLogLogger.swift
ItForceCore/Sources/Infrastructure/PerformanceMonitor/PerformanceManager.swift
ItForceCore/Sources/Infrastructure/PerformanceMonitor/PerformanceMonitor.swift
```

#### 2. Protocol层 (协议层) - 优先级2
```
ItForceCore/Sources/Protocol/Common/ProtocolError.swift
ItForceCore/Sources/Protocol/Encryption/EncryptionProtocol.swift
ItForceCore/Sources/Protocol/Encryption/AESEncryption.swift
ItForceCore/Sources/Protocol/Encryption/XOREncryption.swift
ItForceCore/Sources/Protocol/PacketTypes/PacketHeader.swift
ItForceCore/Sources/Protocol/PacketTypes/SDWANPacket.swift
ItForceCore/Sources/Protocol/PacketTypes/TLVAttribute.swift
ItForceCore/Sources/Protocol/PacketProcessing/PacketParser.swift
ItForceCore/Sources/Protocol/PacketProcessing/PacketBuilder.swift
ItForceCore/Sources/Protocol/Authentication/AuthenticationManager.swift
ItForceCore/Sources/Protocol/DataTransfer/DataTransferManager.swift
```

#### 3. Platform层 (平台层) - 优先级3
```
ItForceCore/Sources/Platform/Common/PlatformError.swift
ItForceCore/Sources/Platform/VPN/VPNPermissionHandler.swift
ItForceCore/Sources/Platform/TUNDevice/TUNDeviceProtocol.swift
ItForceCore/Sources/Platform/TUNDevice/TUNDeviceManager.swift
ItForceCore/Sources/Platform/NetworkExtension/PacketTunnelProvider.swift
```

#### 4. Service层 (服务层) - 优先级4
```
ItForceCore/Sources/Service/VPNService.swift
ItForceCore/Sources/Service/VPNService+Connection.swift
ItForceCore/Sources/Service/VPNService+EventHandling.swift
ItForceCore/Sources/Service/VPNService+Monitoring.swift
ItForceCore/Sources/Service/VPNService+NetworkExtension.swift
ItForceCore/Sources/Service/ServerService.swift
ItForceCore/Sources/Service/NetworkInterfaceService.swift
```

#### 5. Connection层 (连接层) - 优先级5
```
ItForceCore/Sources/Connection/StateMachine/ConnectionStateMachine.swift
ItForceCore/Sources/Connection/ConnectionManager/ConnectionManager.swift
ItForceCore/Sources/Connection/ServerManager/ServerManager.swift
ItForceCore/Sources/Connection/NetworkStatistics/NetworkStatistics.swift
```

#### 6. Application层 (应用层) - 优先级6
```
ItForceCore/Sources/ItForceCore.swift
ItForceCore/Sources/Common/PlatformChannelExtensions.swift
ios/Runner/AppDelegate.swift
ios/Runner/PlatformChannelHandler.swift
ios/ItForceVPNExtension/PacketTunnelProvider.swift
```

#### 7. Test层 (测试层) - 优先级7
```
ios/RunnerTests/RunnerTests.swift
```

### 审查执行顺序

1. **第一轮：Infrastructure层** (13个文件)
   - 基础设施组件是其他层的依赖基础
   - 日志、错误处理、性能监控等核心功能
   - 确保基础功能稳定可靠

2. **第二轮：Protocol层** (11个文件)
   - VPN协议实现的核心
   - 加密、包处理、认证等关键功能
   - 与Go后端的协议兼容性验证

3. **第三轮：Platform层** (5个文件)
   - Apple平台特定功能
   - NetworkExtension集成
   - 系统权限和设备管理

4. **第四轮：Service层** (7个文件)
   - 业务逻辑服务
   - VPNService统一管理验证
   - 服务间协调和状态管理

5. **第五轮：Connection层** (4个文件)
   - 连接管理和状态机
   - 网络统计和服务器管理
   - 重连逻辑验证

6. **第六轮：Application层** (5个文件)
   - 应用入口和Flutter集成
   - Platform Channel通信
   - NetworkExtension Provider

7. **第七轮：Test层** (1个文件)
   - 测试代码审查
   - 测试覆盖率验证

### 关键审查重点

#### 高优先级问题（必须修复）
- VPN连接/断开功能正确性
- 状态管理一致性
- Swift Concurrency使用规范
- 内存泄漏和循环引用

#### 中优先级问题（建议修复）
- 代码重复和冗余
- 架构层次违规
- 性能优化机会

#### 低优先级问题（可选修复）
- 代码风格统一
- 注释完善
- 接口简化

## 总结

本审查规则确保iOS VPN应用达到生产级别的代码质量，符合Apple平台最佳实践，并保持简洁高效的架构设计。总计需要审查42个ItForceCore Swift文件和4个iOS应用层Swift文件，共46个文件。