#!/usr/bin/env kotlin

/**
 * FILE: verify_interop.kts
 *
 * DESCRIPTION:
 *     <PERSON><PERSON><PERSON> script to verify Android-Go protocol interoperability.
 *     This script can be run independently to validate the test packets.
 *
 * USAGE: kotlin verify_interop.kts
 */

import java.io.File
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.security.MessageDigest

// Packet types (matching protocol constants)
object PacketTypes {
    const val OPEN: Byte = 0x13
    const val OPEN_ACK: Byte = 0x12
    const val DATA: Byte = 0x14
    const val DATA_ENCRYPTED: Byte = 0x17
    const val ECHO_REQUEST: Byte = 0x15
}

// Encryption methods
object EncryptionMethods {
    const val NONE: Byte = 0x00
    const val XOR: Byte = 0x01
    const val AES: Byte = 0x02
}

data class PacketHeader(
    val type: Byte,
    val encrypt: Byte,
    val sessionID: Short,
    val token: Int
)

fun main() {
    println("=== Android-Go Protocol Interoperability Verification ===")
    println()
    
    val testResourcesDir = File("app/src/test/resources")
    val goToAndroidDir = File(testResourcesDir, "go_to_android")
    
    if (!goToAndroidDir.exists()) {
        println("❌ Test data directory not found: ${goToAndroidDir.absolutePath}")
        println("   Please run the Go packet generator first")
        return
    }
    
    // Test 1: Verify Go-generated packets
    println("🔍 Test 1: Verifying Go-generated packets...")
    verifyGoPackets(goToAndroidDir)
    
    println()
    
    // Test 2: Generate Android packets for Go validation
    println("🔧 Test 2: Generating Android packets for Go validation...")
    val androidToGoDir = File(testResourcesDir, "android_to_go")
    androidToGoDir.mkdirs()
    generateAndroidPackets(androidToGoDir)
    
    println()
    println("✅ Verification completed!")
    println("📁 Check generated Android packets in: ${androidToGoDir.absolutePath}")
}

fun verifyGoPackets(goToAndroidDir: File) {
    val testFiles = listOf(
        "go_open_xor.bin" to "OPEN packet with XOR encryption",
        "go_openack_xor.bin" to "OPEN_ACK packet with session info",
        "go_data_encrypted.bin" to "Encrypted data packet",
        "go_data_plain.bin" to "Plain data packet",
        "go_echo_request.bin" to "Echo request packet"
    )
    
    var successCount = 0
    
    for ((filename, description) in testFiles) {
        val file = File(goToAndroidDir, filename)
        
        if (!file.exists()) {
            println("⚠️  Skipping $filename - file not found")
            continue
        }
        
        try {
            val packetData = file.readBytes()
            val isValid = validatePacket(packetData, description)
            
            if (isValid) {
                println("✅ $filename: $description - VALID")
                successCount++
            } else {
                println("❌ $filename: $description - INVALID")
            }
            
        } catch (e: Exception) {
            println("❌ $filename: $description - ERROR: ${e.message}")
        }
    }
    
    println("📊 Go→Android Validation: $successCount/${testFiles.size} packets valid")
}

fun validatePacket(packetData: ByteArray, description: String): Boolean {
    if (packetData.size < 8) {
        println("   ERROR: Packet too short (${packetData.size} bytes)")
        return false
    }
    
    try {
        // Parse header (8 bytes, big-endian)
        val buffer = ByteBuffer.wrap(packetData).order(ByteOrder.BIG_ENDIAN)
        val header = PacketHeader(
            type = buffer.get(),
            encrypt = buffer.get(),
            sessionID = buffer.getShort(),
            token = buffer.getInt()
        )
        
        println("   Parsed: Type=0x${header.type.toString(16).padStart(2, '0')}, " +
                "Encrypt=0x${header.encrypt.toString(16).padStart(2, '0')}, " +
                "SessionID=0x${header.sessionID.toString(16).padStart(4, '0')}, " +
                "Token=0x${header.token.toString(16).padStart(8, '0')}")
        println("   Payload size: ${packetData.size - 8} bytes")
        
        // Validate packet type
        val validTypes = listOf(PacketTypes.OPEN, PacketTypes.OPEN_ACK, PacketTypes.DATA, 
                               PacketTypes.DATA_ENCRYPTED, PacketTypes.ECHO_REQUEST)
        if (header.type !in validTypes) {
            println("   ERROR: Invalid packet type: 0x${header.type.toString(16)}")
            return false
        }
        
        // Validate encryption method
        val validEncryption = listOf(EncryptionMethods.NONE, EncryptionMethods.XOR, EncryptionMethods.AES)
        if (header.encrypt !in validEncryption) {
            println("   ERROR: Invalid encryption method: 0x${header.encrypt.toString(16)}")
            return false
        }
        
        // For control packets, verify signature if present
        if (header.type != PacketTypes.DATA && header.type != PacketTypes.DATA_ENCRYPTED && packetData.size >= 24) {
            val headerBytes = packetData.sliceArray(0..7)
            val signature = packetData.sliceArray(8..23)
            val expectedSignature = calculateMD5Signature(headerBytes)
            
            if (!signature.contentEquals(expectedSignature)) {
                println("   ERROR: Invalid MD5 signature")
                return false
            }
            println("   MD5 signature verified ✓")
        }
        
        return true
        
    } catch (e: Exception) {
        println("   ERROR: Failed to parse packet: ${e.message}")
        return false
    }
}

fun generateAndroidPackets(androidToGoDir: File) {
    val testCases = listOf(
        Triple("android_open_xor.bin", "OPEN packet with XOR encryption") {
            createOpenPacket("testuser", "testpass", 1420, EncryptionMethods.XOR)
        },
        Triple("android_openack_xor.bin", "OPEN_ACK packet with session info") {
            createOpenAckPacket(0x1234, 0xDEADBEEF.toInt(), EncryptionMethods.XOR)
        },
        Triple("android_data_encrypted.bin", "Encrypted data packet") {
            createDataPacket(PacketTypes.DATA_ENCRYPTED, 0x1234, 0xDEADBEEF.toInt(), EncryptionMethods.XOR)
        },
        Triple("android_data_plain.bin", "Plain data packet") {
            createDataPacket(PacketTypes.DATA, 0x5678, 0x12345678, EncryptionMethods.NONE)
        },
        Triple("android_echo_request.bin", "Echo request packet") {
            createEchoRequestPacket(0x1234, 0xDEADBEEF.toInt(), EncryptionMethods.XOR)
        }
    )
    
    var successCount = 0
    
    for ((filename, description, generator) in testCases) {
        try {
            val packetData = generator()
            val file = File(androidToGoDir, filename)
            file.writeBytes(packetData)
            
            println("✅ Generated: $filename - $description (${packetData.size} bytes)")
            successCount++
            
        } catch (e: Exception) {
            println("❌ Failed to generate $filename: ${e.message}")
        }
    }
    
    println("📊 Android→Go Generation: $successCount/${testCases.size} packets generated")
}

fun createOpenPacket(username: String, password: String, mtu: Int, encryptMethod: Byte): ByteArray {
    val header = createHeader(PacketTypes.OPEN, encryptMethod, 0, 0)
    val signature = calculateMD5Signature(header)
    val attributes = createOpenAttributes(username, password, mtu, encryptMethod)
    
    return header + signature + attributes
}

fun createOpenAckPacket(sessionID: Int, token: Int, encryptMethod: Byte): ByteArray {
    val header = createHeader(PacketTypes.OPEN_ACK, encryptMethod, sessionID.toShort(), token)
    val signature = calculateMD5Signature(header)
    val attributes = byteArrayOf(0x10, 0x04, 192.toByte(), 168.toByte(), 1, 1) // Server IP
    
    return header + signature + attributes
}

fun createDataPacket(packetType: Byte, sessionID: Int, token: Int, encryptMethod: Byte): ByteArray {
    val header = createHeader(packetType, encryptMethod, sessionID.toShort(), token)
    val ipPacket = byteArrayOf(
        0x45, 0x00, 0x00, 0x3C, // IP header
        0x1C, 0x46, 0x40, 0x00,
        0x40, 0x06, 0x00, 0x00,
        0xC0.toByte(), 0xA8.toByte(), 0x01, 0x64, // Source IP
        0xC0.toByte(), 0xA8.toByte(), 0x01, 0x01  // Dest IP
    )
    
    return header + ipPacket
}

fun createEchoRequestPacket(sessionID: Int, token: Int, encryptMethod: Byte): ByteArray {
    val header = createHeader(PacketTypes.ECHO_REQUEST, encryptMethod, sessionID.toShort(), token)
    val signature = calculateMD5Signature(header)
    val timestamp = ByteArray(8) // 8-byte timestamp
    
    return header + signature + timestamp
}

fun createHeader(type: Byte, encrypt: Byte, sessionID: Short, token: Int): ByteArray {
    val buffer = ByteBuffer.allocate(8).order(ByteOrder.BIG_ENDIAN)
    buffer.put(type)
    buffer.put(encrypt)
    buffer.putShort(sessionID)
    buffer.putInt(token)
    return buffer.array()
}

fun createOpenAttributes(username: String, password: String, mtu: Int, encryptMethod: Byte): ByteArray {
    var attributes = byteArrayOf()
    
    // MTU attribute
    val mtuBytes = ByteBuffer.allocate(4).order(ByteOrder.BIG_ENDIAN).putShort(mtu.toShort()).array()
    attributes += createTLVAttribute(0x03, mtuBytes)
    
    // Username attribute
    attributes += createTLVAttribute(0x01, username.toByteArray())
    
    // Password attribute (simplified)
    val encryptedPassword = ByteArray(16) { (it + password.hashCode()).toByte() }
    attributes += createTLVAttribute(0x02, encryptedPassword)
    
    // Encryption method attribute
    attributes += createTLVAttribute(0x04, byteArrayOf(encryptMethod))
    
    return attributes
}

fun createTLVAttribute(type: Byte, value: ByteArray): ByteArray {
    return byteArrayOf(type, value.size.toByte()) + value
}

fun calculateMD5Signature(headerBytes: ByteArray): ByteArray {
    val md5 = MessageDigest.getInstance("MD5")
    md5.update(headerBytes)
    md5.update(byteArrayOf(109, 119)) // ASCII 'm', 'w'
    return md5.digest()
}

// Run the main function
main()
