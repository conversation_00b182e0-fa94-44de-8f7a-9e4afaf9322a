# Android认证错误处理修复

## 问题描述

Android版本的VPN客户端在处理认证错误时存在以下问题：

1. **认证超时被误判为用户名密码错误**：当网络超时导致认证失败时，系统显示"用户名或密码错误"，而不是"认证超时"
2. **OPENREJECT包处理不完整**：`processOpenAckPacketForAuth`方法只处理`OPEN_ACK`包，不处理`OPEN_REJECT`包
3. **错误分类逻辑不准确**：`isCredentialError`方法无法正确区分超时错误和凭证错误

## 修复方案

### 1. 修复OPENREJECT包处理

**文件**: `ui/flutter/android/app/src/main/kotlin/com/panabit/client/protocol/SDWANProtocol.kt`

**修改内容**:
- 更新`processOpenAckPacketForAuth`方法，支持处理`OPEN_REJECT`包
- 添加`processOpenRejectPacket`方法，解析OPENREJECT包中的错误码和错误消息
- 添加`getRejectReasonString`方法，将错误码转换为可读消息

**核心逻辑**:
```kotlin
return when (packet.header.type) {
    PacketType.OPEN_ACK -> {
        // 处理认证成功
    }
    PacketType.OPEN_REJECT -> {
        // 处理认证被拒绝（用户名密码错误）
        val errorInfo = processOpenRejectPacket(packet)
        AuthenticationResult(success = false, errorMessage = errorInfo.errorMessage)
    }
    else -> {
        // 无效的包类型
    }
}
```

### 2. 优化错误分类逻辑

**文件**: `ui/flutter/android/app/src/main/kotlin/com/panabit/client/connection/ConnectionManager.kt`

**修改内容**:
- 更新`isCredentialError`方法，明确区分超时错误和凭证错误
- 改进超时错误的日志记录

**核心逻辑**:
```kotlin
private fun isCredentialError(errorMessage: String?): Boolean {
    // 明确的非凭据错误（超时、网络问题）
    val nonCredentialErrors = listOf("timeout", "timed out", "connection failed", "network")
    
    // 如果是超时或网络错误，返回false
    if (nonCredentialErrors.any { error -> lowerMessage.contains(error) }) {
        return false
    }
    
    // 来自OPENREJECT包的所有错误都视为凭证错误
    return credentialKeywords.any { keyword -> lowerMessage.contains(keyword) }
}
```

## 错误处理流程

### 场景1：用户名或密码错误
1. 客户端发送OPEN包
2. 服务器返回OPENREJECT包（错误码1或2）
3. `processOpenRejectPacket`解析错误信息
4. `isCredentialIssue = true`
5. 显示："身份验证失败，请检查用户名和密码"

### 场景2：认证超时
1. 客户端发送OPEN包
2. 服务器无响应，触发超时异常
3. `TimeoutCancellationException`被捕获
4. 创建`TimeoutError`，`isCredentialIssue = false`
5. 显示："服务器认证失败，请稍后重试"

## 支持的OPENREJECT错误码

根据Go后端定义的错误码：

```kotlin
private fun getRejectReasonString(reasonCode: Int): String {
    return when (reasonCode) {
        1 -> "Invalid username"        // 用户名无效
        2 -> "Invalid password"        // 密码无效
        3 -> "Server is full"          // 服务器已满
        4 -> "Server error"            // 服务器错误
        5 -> "Unsupported feature"     // 不支持的功能
        6 -> "Account expired"         // 账户已过期
        7 -> "Account disabled"        // 账户已禁用
        8 -> "Maximum sessions reached" // 达到最大会话数
        9 -> "Invalid token"           // 无效令牌
        else -> "Unknown rejection reason"
    }
}
```

## 测试验证

### 测试用例1：用户名密码错误
- **输入**: 错误的用户名或密码
- **预期**: 收到OPENREJECT包，显示"身份验证失败，请检查用户名和密码"
- **验证**: `isCredentialIssue = true`

### 测试用例2：网络超时
- **输入**: 网络不稳定或服务器无响应
- **预期**: 触发超时异常，显示"服务器认证失败，请稍后重试"
- **验证**: `isCredentialIssue = false`

### 测试用例3：服务器错误
- **输入**: 服务器返回OPENREJECT包（错误码3-9）
- **预期**: 根据具体错误码显示相应消息
- **验证**: `isCredentialIssue`根据错误类型确定

## 兼容性

此修复与以下组件兼容：
- Go后端的OPENREJECT包格式
- iOS版本的错误处理逻辑
- 现有的ErrorHandler和VPNServiceError体系
- Flutter层的错误显示机制

## 日志改进

增强了认证过程的日志记录：
- 区分超时和凭证错误的日志级别
- 记录OPENREJECT包的详细错误信息
- 提供更准确的错误上下文信息

## 修复验证

### 修复前的问题
根据提供的日志：
```
E/UndispatchedCoroutine(20493): [null:invoke:8] Authentication failed after 3 attempts
E/ErrorHandler(20493): [ErrorHandler.kt:handleVPNError$default:86] VPN Error occurred [error_type=AuthenticationFailed, error_message=Authentication failed after 3 attempts, is_recoverable=false, context={server_id=1, server_address=kphz.unisase.cn, socket_local_port=45288, target_ip=*************}]
W/ErrorHandler(20493): [ErrorHandler.kt:handleVPNError:109] Authentication failed due to credential issue [error_code=Authentication failed after 3 attempts, vpn_error_code=2000, error_category=Authentication]
I/ErrorHandler(20493): [ErrorHandler.kt:handleVPNError:132] Error notification displayed [error_type=AuthenticationFailed, user_message=用户名或密码错误]
```

**问题分析**：
- 日志显示"Authentication failed after 3 attempts"，这表明是超时导致的失败
- 但是`is_recoverable=false`和`vpn_error_code=2000`表明被错误地识别为凭证问题
- 最终显示给用户的消息是"用户名或密码错误"，这是不正确的

### 修复后的预期行为

**场景1：真正的用户名密码错误**
- 服务器返回OPENREJECT包
- `processOpenRejectPacket`解析出具体错误（如"Invalid password"）
- `isCredentialIssue = true`
- 用户看到："身份验证失败，请检查用户名和密码"

**场景2：网络超时**
- 客户端发送认证请求后无响应
- 触发`TimeoutCancellationException`
- 创建`TimeoutError`而不是`AuthenticationFailed`
- 用户看到："连接超时，请检查网络连接后重试"

**场景3：认证超时（修复后）**
- 如果仍然创建`AuthenticationFailed`错误
- `isCredentialError`方法识别"timeout"关键词
- `isCredentialIssue = false`
- 用户看到："服务器认证失败，请稍后重试"

## 关键修复点总结

1. **OPENREJECT包处理**：现在能正确解析服务器拒绝的具体原因
2. **超时错误分类**：超时不再被误判为凭证错误
3. **用户消息准确性**：根据真实错误原因显示相应消息
4. **错误恢复策略**：超时错误可重试，凭证错误需用户干预

这个修复确保了用户能够获得准确的错误信息，从而采取正确的解决措施。
