# VPN权限请求测试指南

## 修复内容总结

### 1. 修复的问题
- **权限回调链断裂**：MainActivity 缺少 onActivityResult 方法
- **权限请求方式不正确**：PlatformChannelHandler 使用错误的权限请求方式
- **Activity上下文问题**：确保使用正确的Activity上下文进行权限请求

### 2. 修复的文件
- `MainActivity.kt`：添加了 onActivityResult 方法处理权限回调
- `PlatformChannelHandler.kt`：修复了权限请求逻辑，直接使用 VpnService.prepare()
- `PermissionManager.kt`：移除了冗余的 hasVPNPermission 方法

### 3. 权限请求流程
1. Flutter 调用 `requestVPNPermission` 方法
2. PlatformChannelHandler 检查当前权限状态
3. 如果需要权限，调用 `VpnService.prepare()` 获取权限请求Intent
4. 使用 `activity.startActivityForResult()` 启动系统权限对话框
5. 用户在系统对话框中选择"允许"或"拒绝"
6. MainActivity 的 `onActivityResult` 接收权限结果
7. 通过 Event Channel 将结果发送给 Flutter

## 测试步骤

### 前置条件
1. 确保应用已安装但未授权VPN权限
2. 可以通过以下方式清除权限：
   ```bash
   adb shell pm reset-permissions com.itforce.wan
   ```

### 测试用例1：首次权限请求
1. 启动应用
2. 在Flutter界面中触发VPN连接或权限请求
3. **预期结果**：
   - 应该弹出Android系统的VPN权限对话框
   - 对话框标题类似"允许ITforce WAN创建VPN连接吗？"
   - 有"拒绝"和"确定"两个按钮

### 测试用例2：用户授权权限
1. 在权限对话框中点击"确定"
2. **预期结果**：
   - MainActivity.onActivityResult 被调用，resultCode = RESULT_OK
   - Flutter收到权限授权成功的事件
   - 后续VPN连接可以正常进行

### 测试用例3：用户拒绝权限
1. 在权限对话框中点击"拒绝"
2. **预期结果**：
   - MainActivity.onActivityResult 被调用，resultCode = RESULT_CANCELED
   - Flutter收到权限被拒绝的事件
   - 应用显示相应的错误提示

### 测试用例4：权限已授权的情况
1. 在已授权VPN权限的设备上测试
2. 调用权限请求方法
3. **预期结果**：
   - 不弹出权限对话框
   - 直接返回权限已授权的状态

## 调试信息

### 日志标签
- `MainActivity`：Activity相关日志
- `PlatformChannelHandler`：权限请求处理日志
- `PermissionManager`：权限管理日志

### 关键日志
```
MainActivity: VPN permission result received: granted=true
PlatformChannelHandler: VPN permission required, launching system dialog
PlatformChannelHandler: VPN permission dialog launched successfully
```

### 常见问题排查

#### 1. 权限对话框不弹出
- 检查Activity上下文是否正确
- 确认VpnService.prepare()返回非null Intent
- 查看是否有异常阻止startActivityForResult调用

#### 2. 权限回调不触发
- 确认MainActivity的onActivityResult方法被正确调用
- 检查requestCode是否匹配(1001)
- 验证PlatformChannelHandler是否正确初始化

#### 3. Flutter未收到权限结果
- 检查Event Channel是否正确配置
- 确认sendEvent方法被调用
- 验证Flutter端的事件监听器

## 代码关键点

### MainActivity.onActivityResult
```kotlin
if (requestCode == 1001) { // VPN_PERMISSION_REQUEST_CODE
    val granted = resultCode == RESULT_OK
    // 发送事件给Flutter
    handler.sendEvent("vpn_permission_result", eventData)
}
```

### PlatformChannelHandler权限请求
```kotlin
val prepareIntent = android.net.VpnService.prepare(activity)
if (prepareIntent != null) {
    activity.startActivityForResult(prepareIntent, 1001)
}
```

这个修复确保了VPN权限请求能够正确弹出Android系统对话框，并且权限结果能够正确传递回Flutter层。
