# Android认证超时修复验证

## 问题根因分析

### 原始问题
从日志可以看出：
```
E/ErrorHandler(10076): [ErrorHandler.kt:handleVPNError$default:86] VPN Error occurred [error_type=AuthenticationFailed, error_message=Authentication failed after 3 attempts, is_recoverable=false, context={server_id=1, server_address=kphz.unisase.cn, socket_local_port=43792, target_ip=*************}]
```

**根因**：SDWANProtocol的认证重试逻辑有缺陷：
1. 每次重试都因为超时失败（`TimeoutCancellationException`）
2. 重试循环结束后，返回通用消息"Authentication failed after 3 attempts"
3. 这个消息不包含"timeout"关键词，被`isCredentialError`误判为凭证错误

## 修复方案

### 1. 增强认证重试逻辑

**文件**: `ui/flutter/android/app/src/main/kotlin/com/panabit/client/protocol/SDWANProtocol.kt`

**修复内容**:
```kotlin
// 跟踪失败原因
var lastError: String? = null
var lastErrorWasTimeout = false
var lastErrorWasReject = false

while (attempt < MAX_AUTH_RETRIES) {
    try {
        // 发送认证请求
        val response = withTimeout(AUTH_RETRY_TIMEOUT_MS) {
            receiveAuthResponseWithSocket(socket)
        }
        
        if (response != null) {
            val result = processOpenAckPacketForAuth(response)
            if (result.success) {
                return result
            } else {
                // 收到OPENREJECT响应
                lastError = result.errorMessage
                lastErrorWasReject = true
                lastErrorWasTimeout = false
                
                // 如果是凭证错误，立即停止重试
                if (isCredentialRelatedError(result.errorMessage)) {
                    break
                }
            }
        } else {
            // 没有收到响应
            lastError = "No response received from server"
            lastErrorWasTimeout = true
            lastErrorWasReject = false
        }
    } catch (e: TimeoutCancellationException) {
        // 超时异常
        lastError = "Authentication request timed out"
        lastErrorWasTimeout = true
        lastErrorWasReject = false
    }
}

// 根据失败原因生成相应的错误消息
val finalErrorMessage = when {
    lastErrorWasReject && lastError != null -> {
        // 收到了OPENREJECT响应 - 凭证错误
        lastError
    }
    lastErrorWasTimeout -> {
        // 所有重试都是超时 - 网络问题
        "Authentication timed out after $MAX_AUTH_RETRIES attempts - no response from server"
    }
    else -> {
        // 其他未知错误
        "Authentication failed after $MAX_AUTH_RETRIES attempts: ${lastError ?: "unknown error"}"
    }
}
```

### 2. 添加凭证错误识别

**新增方法**:
```kotlin
private fun isCredentialRelatedError(errorMessage: String?): Boolean {
    if (errorMessage == null) return false
    
    val lowerMessage = errorMessage.lowercase()
    val credentialErrors = listOf(
        "invalid username",
        "invalid password", 
        "account expired",
        "account disabled",
        "invalid token"
    )
    
    return credentialErrors.any { error -> lowerMessage.contains(error) }
}
```

### 3. 更新错误分类逻辑

**文件**: `ui/flutter/android/app/src/main/kotlin/com/panabit/client/connection/ConnectionManager.kt`

**修复内容**:
```kotlin
// 在isCredentialError方法中添加新的超时关键词
val nonCredentialErrors = listOf(
    "timeout", "timed out", "超时",
    "no response from server", "服务器无响应",  // 新增
    "connection failed", "连接失败", 
    "network", "网络",
    "unreachable", "不可达",
    "server error", "服务器错误",
    "server is full", "服务器已满"
)
```

## 修复后的预期行为

### 场景1：真正的用户名密码错误
```
1. 客户端发送OPEN包
2. 服务器立即返回OPENREJECT包（错误码1或2）
3. processOpenRejectPacket解析出"Invalid password"
4. isCredentialRelatedError返回true，停止重试
5. 最终错误消息："Invalid password"
6. isCredentialError返回true
7. 用户看到："身份验证失败，请检查用户名和密码"
```

### 场景2：网络超时（修复后）
```
1. 客户端发送OPEN包
2. 服务器无响应，触发TimeoutCancellationException
3. 重试3次，每次都超时
4. 最终错误消息："Authentication timed out after 3 attempts - no response from server"
5. isCredentialError识别"timed out"和"no response"关键词，返回false
6. 用户看到："服务器认证失败，请稍后重试"
```

### 场景3：服务器错误
```
1. 客户端发送OPEN包
2. 服务器返回OPENREJECT包（错误码3-9）
3. processOpenRejectPacket解析出"Server is full"
4. isCredentialRelatedError返回false，可能重试
5. 最终错误消息："Server is full"
6. isCredentialError识别"server is full"关键词，返回false
7. 用户看到："服务器认证失败，请稍后重试"
```

## 日志对比

### 修复前（问题日志）
```
E/ErrorHandler: VPN Error occurred [error_type=AuthenticationFailed, error_message=Authentication failed after 3 attempts, is_recoverable=false]
W/ErrorHandler: Authentication failed due to credential issue [vpn_error_code=2000]
I/ErrorHandler: Error notification displayed [user_message=用户名或密码错误]
```

### 修复后（预期日志）
```
E/ErrorHandler: VPN Error occurred [error_type=AuthenticationFailed, error_message=Authentication timed out after 3 attempts - no response from server, is_recoverable=true]
I/ErrorHandler: Authentication failed due to network issue [vpn_error_code=2000]
I/ErrorHandler: Error notification displayed [user_message=服务器认证失败，请稍后重试]
```

## 关键改进点

1. **精确的失败原因跟踪**：区分超时、OPENREJECT和其他错误
2. **智能重试策略**：凭证错误立即停止，网络错误继续重试
3. **准确的错误消息**：包含明确的失败原因关键词
4. **正确的错误分类**：超时不再被误判为凭证错误

## 测试验证

### 测试用例1：网络超时
- **模拟条件**：服务器不响应认证请求
- **预期结果**：显示"服务器认证失败，请稍后重试"
- **验证点**：`isCredentialError`返回false

### 测试用例2：用户名错误
- **模拟条件**：服务器返回OPENREJECT（错误码1）
- **预期结果**：显示"身份验证失败，请检查用户名和密码"
- **验证点**：`isCredentialError`返回true，不重试

### 测试用例3：服务器满载
- **模拟条件**：服务器返回OPENREJECT（错误码3）
- **预期结果**：显示"服务器认证失败，请稍后重试"
- **验证点**：`isCredentialError`返回false，可重试

这个修复确保了用户能够获得准确的错误信息，从而采取正确的解决措施。
