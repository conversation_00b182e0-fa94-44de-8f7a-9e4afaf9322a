# Android VPN服务器切换功能实现

## 概述

本文档描述了Android版本中VPN服务器切换功能的实现，确保disconnect操作完全成功后再执行connect操作，避免并发连接问题。

## 实现目标

1. **顺序执行**: 确保disconnect操作完全完成后再开始connect操作
2. **状态同步**: 实现可靠的状态检查机制，防止并发操作
3. **超时处理**: 添加超时机制防止无限等待
4. **错误处理**: 提供完善的错误处理和日志记录

## 核心修改

### 1. VPNState类增强 (`VPNState.kt`)

添加了缺失的状态查询属性：

```kotlin
/**
 * 检查VPN是否已断开连接
 */
val isDisconnected: Boolean
    get() = this is Disconnected

/**
 * 检查VPN是否正在断开连接
 */
val isDisconnecting: Boolean
    get() = this is Disconnecting
```

### 2. VPNServiceAdapter增强 (`VPNServiceAdapter.kt`)

#### 2.1 添加状态等待机制

```kotlin
/**
 * 等待断开连接操作完成
 * 实现基于超时的等待，防止无限阻塞
 */
private suspend fun waitForDisconnectionComplete(): Boolean {
    val maxWaitTime = 10_000L // 10秒超时
    val checkInterval = 200L // 每200ms检查一次
    
    while (System.currentTimeMillis() - startTime < maxWaitTime) {
        val currentState = vpnService.getCurrentState()
        if (!currentState.isDisconnecting) {
            return true // 断开完成
        }
        delay(checkInterval)
    }
    return false // 超时
}
```

#### 2.2 增强connect方法

```kotlin
override suspend fun connect(serverId: String): Result<Map<String, Any>> {
    // 1. 检查当前状态，防止并发操作
    val currentState = vpnService.getCurrentState()
    if (currentState.isOperationInProgress) {
        return Result.failure(Exception("Another operation is in progress"))
    }

    // 2. 等待正在进行的断开操作完成
    if (currentState.isDisconnecting) {
        val disconnectCompleted = waitForDisconnectionComplete()
        if (!disconnectCompleted) {
            return Result.failure(Exception("Timeout waiting for disconnection"))
        }
    }

    // 3. 执行连接操作
    // ... 原有连接逻辑
}
```

#### 2.3 增强disconnect方法

```kotlin
override suspend fun disconnect(): Result<Unit> {
    // 1. 检查当前状态
    val currentState = vpnService.getCurrentState()
    if (currentState.isDisconnected) {
        return Result.success(Unit) // 已断开，直接返回
    }

    // 2. 如果正在断开，等待完成
    if (currentState.isDisconnecting) {
        val completed = waitForConnectionStateChange(VPNState.Disconnected)
        return if (completed) Result.success(Unit) else Result.failure(...)
    }

    // 3. 执行断开操作并等待完成
    context.startService(disconnectIntent)
    val disconnectCompleted = waitForConnectionStateChange(VPNState.Disconnected)
    
    return if (disconnectCompleted) {
        Result.success(Unit)
    } else {
        Result.failure(Exception("Disconnect operation timed out"))
    }
}
```

## 工作流程

### 服务器切换流程

1. **Flutter UI层调用**: 用户选择新服务器时，Flutter调用`disconnect()`
2. **Android断开处理**: 
   - 检查当前状态
   - 发送断开Intent给VPN服务
   - 等待VPN状态变为`Disconnected`
   - 返回成功结果给Flutter
3. **Flutter延迟等待**: Flutter等待1秒确保断开完成
4. **Flutter连接调用**: Flutter调用`connect(newServerId)`
5. **Android连接处理**:
   - 检查是否有操作正在进行
   - 等待任何残留的断开操作完成
   - 执行新服务器连接

### 状态同步机制

```
当前状态检查 → 等待操作完成 → 执行新操作 → 等待操作完成 → 返回结果
     ↓              ↓              ↓              ↓
  防止并发      确保清理完成    执行目标操作    确保操作完成
```

## 关键特性

### 1. 并发操作防护
- 在connect前检查`isOperationInProgress`
- 阻止在连接/断开过程中启动新操作

### 2. 状态等待机制
- 使用轮询方式检查状态变化
- 200ms检查间隔，平衡响应性和性能
- 10秒超时防止无限等待

### 3. 幂等性支持
- disconnect方法支持重复调用
- 已断开状态直接返回成功
- 正在断开时等待完成而非报错

### 4. 错误处理
- 详细的错误日志记录
- 超时错误的明确提示
- 操作失败时的状态恢复

## 测试验证

创建了`server_switch_test.kt`测试脚本，验证：

1. **基本切换流程**: disconnect-then-connect模式
2. **并发操作防护**: 连接过程中阻止新连接
3. **状态同步**: 等待机制的正确性
4. **超时处理**: 超时情况的处理

## 与iOS实现的一致性

| 特性 | iOS实现 | Android实现 | 一致性 |
|------|---------|-------------|--------|
| 切换模式 | disconnect-then-connect | disconnect-then-connect | ✅ |
| 状态检查 | operationState检查 | isOperationInProgress检查 | ✅ |
| 等待机制 | Task.sleep轮询 | delay轮询 | ✅ |
| 超时处理 | 有超时机制 | 10秒超时 | ✅ |
| 并发防护 | 状态检查阻止 | 状态检查阻止 | ✅ |

## 性能考虑

1. **轮询间隔**: 200ms平衡响应性和CPU使用
2. **超时设置**: 10秒超时适合网络操作
3. **状态缓存**: 避免重复的状态查询
4. **协程使用**: 非阻塞的异步等待

## 使用示例

```kotlin
// Flutter UI层的服务器切换逻辑（已存在）
suspend fun switchServer(newServerId: String) {
    // 1. 断开当前连接
    val disconnectResult = apiService.disconnect()
    
    // 2. 等待断开完成
    delay(1000) // Flutter层等待
    
    // 3. 连接新服务器
    val connectResult = apiService.connect(newServerId)
}
```

Android平台层会自动处理状态同步和并发防护，确保操作的安全性和可靠性。

## 总结

通过在Android VPN实现中添加状态检查机制和等待逻辑，我们成功实现了：

1. ✅ **顺序执行**: disconnect完全完成后再connect
2. ✅ **并发防护**: 防止操作冲突和状态不一致
3. ✅ **可靠性**: 超时处理和错误恢复
4. ✅ **一致性**: 与iOS实现保持功能一致

这确保了Android版本的服务器切换功能稳定可靠，避免了并发连接问题。
