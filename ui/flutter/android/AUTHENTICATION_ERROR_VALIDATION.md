# Android认证错误处理验证

## 原始问题分析

根据提供的日志：
```
E/UndispatchedCoroutine(20493): [null:invoke:8] Authentication failed after 3 attempts
I/ErrorHandler(20493): [ErrorHandler.kt:handleVPNError:132] Error notification displayed [error_type=AuthenticationFailed, user_message=用户名或密码错误]
```

**问题**：认证重试3次后失败，但被错误地识别为"用户名或密码错误"

## 修复后的预期行为

### 场景1：真正的用户名密码错误
```kotlin
// 服务器返回OPENREJECT包（错误码1或2）
val errorMessage = "Invalid password"  // 来自processOpenRejectPacket
val isCredentialIssue = isCredentialError(errorMessage)  // true
// 用户看到："身份验证失败，请检查用户名和密码"
```

### 场景2：认证超时（修复后）
```kotlin
// 发生TimeoutCancellationException
val timeoutError = TimeoutError(...)  // 不是AuthenticationFailed
// 用户看到："连接超时，请检查网络连接后重试"
```

### 场景3：重试失败（可能的情况）
```kotlin
// 如果仍然创建AuthenticationFailed错误
val errorMessage = "Authentication failed after 3 attempts"
val isCredentialIssue = isCredentialError(errorMessage)  // false（包含"failed"但不是明确的凭证错误）
// 用户看到："服务器认证失败，请稍后重试"
```

## 关键修复验证

### 1. OPENREJECT包处理
```kotlin
// SDWANProtocol.processOpenAckPacketForAuth
when (packet.header.type) {
    PacketType.OPEN_REJECT -> {
        val errorInfo = processOpenRejectPacket(packet)
        // errorInfo.errorMessage = "Invalid password" (具体错误)
        AuthenticationResult(success = false, errorMessage = errorInfo.errorMessage)
    }
}
```

### 2. 错误分类逻辑
```kotlin
// ConnectionManager.isCredentialError
private fun isCredentialError(errorMessage: String?): Boolean {
    val lowerMessage = errorMessage?.lowercase() ?: return false
    
    // 超时错误 -> false
    if (lowerMessage.contains("timeout") || lowerMessage.contains("timed out")) {
        return false
    }
    
    // OPENREJECT错误 -> true
    if (lowerMessage.contains("invalid username") || lowerMessage.contains("invalid password")) {
        return true
    }
    
    // 模糊的"authentication failed"消息 -> 需要进一步判断
    // 如果包含"after X attempts"，可能是超时导致的
    return credentialKeywords.any { keyword -> lowerMessage.contains(keyword) }
}
```

### 3. 超时处理
```kotlin
// ConnectionManager.authenticateSDWAN
try {
    val authResult = withTimeout(authTimeout) {
        currentProtocolHandler.authenticateWithSocket(...)
    }
    // 处理authResult
} catch (e: TimeoutCancellationException) {
    // 创建TimeoutError，不是AuthenticationFailed
    val timeoutError = timeoutManager.createTimeoutError(...)
    return Result.failure(timeoutError)
}
```

## 测试用例

### 测试1：用户名错误
- **输入**：错误的用户名
- **服务器响应**：OPENREJECT包，错误码1
- **解析结果**："Invalid username"
- **isCredentialError**：true
- **用户消息**："身份验证失败，请检查用户名和密码"

### 测试2：密码错误
- **输入**：错误的密码
- **服务器响应**：OPENREJECT包，错误码2
- **解析结果**："Invalid password"
- **isCredentialError**：true
- **用户消息**："身份验证失败，请检查用户名和密码"

### 测试3：网络超时
- **输入**：正确的凭证，但网络不稳定
- **服务器响应**：无响应
- **异常**：TimeoutCancellationException
- **错误类型**：TimeoutError
- **用户消息**："连接超时，请检查网络连接后重试"

### 测试4：服务器满载
- **输入**：正确的凭证
- **服务器响应**：OPENREJECT包，错误码3
- **解析结果**："Server is full"
- **isCredentialError**：false（包含"server"关键词）
- **用户消息**："服务器认证失败，请稍后重试"

## 边界情况处理

### 1. 解析OPENREJECT包失败
```kotlin
// 如果processOpenRejectPacket抛出异常
catch (e: Exception) {
    AuthenticationResult(
        success = false,
        errorMessage = "Authentication rejected by server"  // 通用消息
    )
}
```

### 2. 空错误消息
```kotlin
// isCredentialError处理null或空消息
if (errorMessage == null) return false  // 不默认为凭证错误
```

### 3. 未知错误码
```kotlin
// getRejectReasonString处理未知错误码
else -> "Unknown rejection reason"
```

## 兼容性确认

1. **与Go后端兼容**：错误码映射与Go后端定义一致
2. **与iOS版本兼容**：错误处理逻辑类似
3. **向后兼容**：不影响现有的错误处理流程
4. **Flutter集成**：错误消息通过现有的通道传递给Flutter层

## 结论

修复后的实现能够：
1. ✅ 正确区分OPENREJECT（凭证错误）和超时（网络错误）
2. ✅ 提供准确的用户错误消息
3. ✅ 保持与其他平台的一致性
4. ✅ 处理各种边界情况

原始日志中的问题应该得到解决：认证超时不再被误判为"用户名或密码错误"。
