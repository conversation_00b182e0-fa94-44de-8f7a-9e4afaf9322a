<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24"
    android:tint="@android:color/white">
    
    <!-- Simplified Panabit icon for notifications -->
    <!-- This is a white silhouette version suitable for status bar -->
    <path
        android:fillColor="@android:color/white"
        android:pathData="M12,2C6.48,2 2,6.48 2,12s4.48,10 10,10 10,-4.48 10,-10S17.52,2 12,2zM12,20c-4.41,0 -8,-3.59 -8,-8s3.59,-8 8,-8 8,3.59 8,8 -3.59,8 -8,8z"/>
    
    <!-- Inner design element representing connectivity/network -->
    <path
        android:fillColor="@android:color/white"
        android:pathData="M8,12l2,-2 2,2 4,-4v3h3V8h-3v3l-4,4 -2,-2 -2,2z"/>
</vector>
