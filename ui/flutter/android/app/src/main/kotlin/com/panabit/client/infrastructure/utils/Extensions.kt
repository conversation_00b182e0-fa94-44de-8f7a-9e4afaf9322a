/**
 * FILE: Extensions.kt
 *
 * DESCRIPTION:
 *     Panabit Client Android应用程序扩展函数定义。
 *     包含Context、String、网络工具等常用扩展函数。
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.infrastructure.utils

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.InetAddress

/**
 * NAME: isNetworkAvailable
 *
 * DESCRIPTION:
 *     检查网络连接是否可用
 *
 * RECEIVER:
 *     Context - Android上下文
 *
 * RETURNS:
 *     Boolean - true表示网络可用，false表示网络不可用
 */
fun Context.isNetworkAvailable(): Boolean {
    val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
    } else {
        @Suppress("DEPRECATION")
        val networkInfo = connectivityManager.activeNetworkInfo
        @Suppress("DEPRECATION")
        networkInfo?.isConnected == true
    }
}

/**
 * NAME: getNetworkType
 *
 * DESCRIPTION:
 *     获取当前网络类型
 *
 * RECEIVER:
 *     Context - Android上下文
 *
 * RETURNS:
 *     String - 网络类型描述（WiFi、Mobile、Unknown）
 */
fun Context.getNetworkType(): String {
    val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        val network = connectivityManager.activeNetwork ?: return "Unknown"
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return "Unknown"
        when {
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "WiFi"
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> "Mobile"
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> "Ethernet"
            else -> "Unknown"
        }
    } else {
        @Suppress("DEPRECATION")
        val networkInfo = connectivityManager.activeNetworkInfo
        @Suppress("DEPRECATION")
        networkInfo?.typeName ?: "Unknown"
    }
}

/**
 * NAME: isValidIPAddress
 *
 * DESCRIPTION:
 *     验证字符串是否为有效的IP地址
 *
 * RECEIVER:
 *     String - 待验证的IP地址字符串
 *
 * RETURNS:
 *     Boolean - true表示有效IP地址，false表示无效
 */
fun String.isValidIPAddress(): Boolean {
    return try {
        InetAddress.getByName(this)
        true
    } catch (e: Exception) {
        false
    }
}

/**
 * NAME: resolveHostname
 *
 * DESCRIPTION:
 *     异步解析主机名为IP地址
 *
 * RECEIVER:
 *     String - 主机名
 *
 * RETURNS:
 *     String? - 解析得到的IP地址，失败时返回null
 */
suspend fun String.resolveHostname(): String? = withContext(Dispatchers.IO) {
    try {
        InetAddress.getByName(this@resolveHostname).hostAddress
    } catch (e: Exception) {
        null
    }
}

/**
 * NAME: toHexString
 *
 * DESCRIPTION:
 *     将字节数组转换为十六进制字符串
 *
 * RECEIVER:
 *     ByteArray - 字节数组
 *
 * RETURNS:
 *     String - 十六进制字符串表示
 */
fun ByteArray.toHexString(): String {
    return joinToString("") { "%02x".format(it) }
}

/**
 * NAME: fromHexString
 *
 * DESCRIPTION:
 *     将十六进制字符串转换为字节数组
 *
 * RECEIVER:
 *     String - 十六进制字符串
 *
 * RETURNS:
 *     ByteArray - 转换得到的字节数组
 *
 * THROWS:
 *     IllegalArgumentException - 如果字符串格式无效
 */
fun String.fromHexString(): ByteArray {
    require(length % 2 == 0) { "Hex string must have even length" }
    return chunked(2)
        .map { it.toInt(16).toByte() }
        .toByteArray()
}
