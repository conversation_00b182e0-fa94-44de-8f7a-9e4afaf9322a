/**
 * FILE: AESEncryption.kt
 *
 * DESCRIPTION:
 *     AES encryption implementation for SDWAN protocol.
 *     Uses ECB mode with zero padding for compatibility with Go backend.
 *     Session key generated from MD5(username + password).
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.protocol

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import com.panabit.client.protocol.models.EncryptionMethod
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

/**
 * NAME: AESEncryption
 *
 * DESCRIPTION:
 *     AES encryption implementation using ECB mode.
 *     Compatible with Go backend AES implementation.
 *     Uses zero padding instead of PKCS#7 for consistency.
 *
 * PROPERTIES:
 *     keyManager - Key management service for session key
 *     cipher - AES cipher instance
 *     secretKey - AES secret key specification
 */
class AESEncryption(private val keyManager: KeyManager) : EncryptionService {
    private var cachedSessionKey: ByteArray? = null
    private var secretKey: SecretKeySpec? = null

    companion object {
        private const val AES_BLOCK_SIZE = 16
        private const val AES_ALGORITHM = "AES"
        private const val AES_TRANSFORMATION = "AES/ECB/NoPadding"
    }

    /**
     * NAME: getCachedSessionKey
     *
     * DESCRIPTION:
     *     Gets session key with performance optimization.
     *     Uses cached key to avoid repeated KeyManager calls.
     *
     * RETURNS:
     *     ByteArray? - Cached session key, null if not available
     */
    private fun getCachedSessionKey(): ByteArray? {
        if (cachedSessionKey == null) {
            cachedSessionKey = keyManager.getSessionKey()
            if (cachedSessionKey != null) {
                secretKey = SecretKeySpec(cachedSessionKey, AES_ALGORITHM)
            }
        }
        return cachedSessionKey
    }

    /**
     * NAME: encrypt
     *
     * DESCRIPTION:
     *     Encrypts data using AES-ECB mode without PKCS#7 padding.
     *     Pads data to block size with zeros for consistency with Go backend.
     *     Uses ECB mode where each block is encrypted independently.
     *
     * PARAMETERS:
     *     data - Data to encrypt
     *
     * RETURNS:
     *     ByteArray - Encrypted data
     *
     * THROWS:
     *     IllegalStateException - If session key not available
     *     Exception - If encryption fails
     */
    override fun encrypt(data: ByteArray): ByteArray {
        val sessionKey = getCachedSessionKey()
            ?: throw IllegalStateException("Session key not available for encryption")

        if (data.isEmpty()) return data

        // Pad data to AES block size with zeros (consistent with Go backend)
        val paddedSize = ((data.size + AES_BLOCK_SIZE - 1) / AES_BLOCK_SIZE) * AES_BLOCK_SIZE
        val paddedData = ByteArray(paddedSize)
        System.arraycopy(data, 0, paddedData, 0, data.size)
        // Remaining bytes are already zero-filled

        // Perform AES-ECB encryption
        val cipher = Cipher.getInstance(AES_TRANSFORMATION)
        cipher.init(Cipher.ENCRYPT_MODE, secretKey)
        return cipher.doFinal(paddedData)
    }

    /**
     * NAME: decrypt
     *
     * DESCRIPTION:
     *     Decrypts AES-ECB encrypted data.
     *     Does not handle padding removal for consistency with Go backend.
     *     Caller must handle padding if needed.
     *
     * PARAMETERS:
     *     data - Encrypted data
     *
     * RETURNS:
     *     ByteArray - Decrypted data (with padding intact)
     *
     * THROWS:
     *     IllegalStateException - If session key not available
     *     IllegalArgumentException - If data length is not multiple of block size
     *     Exception - If decryption fails
     */
    override fun decrypt(data: ByteArray): ByteArray {
        val sessionKey = getCachedSessionKey()
            ?: throw IllegalStateException("Session key not available for decryption")

        if (data.isEmpty()) return data

        // Check if data length is multiple of block size
        require(data.size % AES_BLOCK_SIZE == 0) {
            "AES encrypted data length must be multiple of $AES_BLOCK_SIZE bytes"
        }

        // Perform AES-ECB decryption
        val cipher = Cipher.getInstance(AES_TRANSFORMATION)
        cipher.init(Cipher.DECRYPT_MODE, secretKey)
        return cipher.doFinal(data)
    }

    /**
     * NAME: getMethod
     *
     * DESCRIPTION:
     *     Returns the encryption method identifier.
     *
     * RETURNS:
     *     EncryptionMethod - AES encryption method
     */
    override fun getMethod(): EncryptionMethod = EncryptionMethod.AES

    /**
     * NAME: clearCache
     *
     * DESCRIPTION:
     *     Clears cached session key and secret key for security.
     */
    fun clearCache() {
        cachedSessionKey?.fill(0)
        cachedSessionKey = null
        secretKey = null
    }



}
