/**
 * FILE: NetworkKeepAliveJobService.kt
 *
 * DESCRIPTION:
 *     Panabit Client Android network keep-alive job service for maintaining
 *     VPN connection stability in background. Uses JobScheduler for
 *     system-managed periodic network connectivity checks.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.services

import android.app.job.JobParameters
import android.app.job.JobService
import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import androidx.annotation.RequiresApi
import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logError
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.vpn.ITforceVPNService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

/**
 * NAME: NetworkKeepAliveJobService
 *
 * DESCRIPTION:
 *     Background job service for network keep-alive functionality.
 *     Performs periodic network connectivity checks and VPN health monitoring
 *     to ensure stable VPN operation. Uses Android JobScheduler for
 *     system-managed background execution with proper battery optimization.
 *
 * PROPERTIES:
 *     connectivityManager - ConnectivityManager for network status checking
 *     jobScope - Coroutine scope for background job execution
 *     currentJob - Current running job for cancellation support
 */
@RequiresApi(Build.VERSION_CODES.LOLLIPOP)
class NetworkKeepAliveJobService : JobService() {

    private val connectivityManager: ConnectivityManager by lazy {
        getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    private val jobScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var currentJob: Job? = null

    companion object {
        const val JOB_ID = 1000
        private const val MAX_EXECUTION_TIME = 30_000L // 30 seconds
        private const val NETWORK_CHECK_TIMEOUT = 10_000L // 10 seconds
    }

    /**
     * NAME: onStartJob
     *
     * DESCRIPTION:
     *     Called when the job is started by the system.
     *     Performs network connectivity check and VPN health monitoring
     *     in background coroutine with proper timeout handling.
     *
     * PARAMETERS:
     *     params - Job parameters from JobScheduler
     *
     * RETURNS:
     *     Boolean - true if job is running asynchronously, false if completed
     */
    override fun onStartJob(params: JobParameters?): Boolean {
        logInfo("Network keep-alive job started", mapOf(
            "job_id" to JOB_ID,
            "network_type" to getNetworkTypeDescription()
        ))

        currentJob = jobScope.launch {
            try {
                performNetworkKeepAlive(params)
                jobFinished(params, false) // Job completed successfully
            } catch (e: Exception) {
                logError("Network keep-alive job failed", e)
                jobFinished(params, true) // Reschedule job due to failure
            }
        }

        return true // Job is running asynchronously
    }

    /**
     * NAME: onStopJob
     *
     * DESCRIPTION:
     *     Called when the job is stopped by the system before completion.
     *     Cancels ongoing network operations and cleans up resources.
     *
     * PARAMETERS:
     *     params - Job parameters from JobScheduler
     *
     * RETURNS:
     *     Boolean - true to reschedule job, false to drop it
     */
    override fun onStopJob(params: JobParameters?): Boolean {
        logWarn("Network keep-alive job stopped by system")
        
        currentJob?.cancel()
        currentJob = null
        
        return true // Reschedule the job
    }

    /**
     * NAME: performNetworkKeepAlive
     *
     * DESCRIPTION:
     *     Performs the actual network keep-alive operations.
     *     Checks network connectivity, VPN service health, and triggers
     *     reconnection if necessary.
     *
     * PARAMETERS:
     *     params - Job parameters for completion notification
     */
    private suspend fun performNetworkKeepAlive(params: JobParameters?) {
        logDebug("Starting network keep-alive operations")

        // Check network connectivity
        val isNetworkAvailable = checkNetworkConnectivity()
        if (!isNetworkAvailable) {
            logWarn("Network not available during keep-alive check")
            return
        }

        // Check VPN service status
        val vpnService = ITforceVPNService.getInstance()
        if (vpnService == null) {
            logDebug("VPN service not running - no keep-alive action needed")
            return
        }

        // Perform VPN health check
        performVPNHealthCheck(vpnService)
    }

    /**
     * NAME: checkNetworkConnectivity
     *
     * DESCRIPTION:
     *     Checks current network connectivity status.
     *     Verifies that device has active internet connection.
     *
     * RETURNS:
     *     Boolean - true if network is available and connected
     */
    private fun checkNetworkConnectivity(): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val activeNetwork = connectivityManager.activeNetwork
                val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)

                val hasInternet = networkCapabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
                val isValidated = networkCapabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED) == true

                logDebug("Network connectivity check", mapOf(
                    "has_internet" to hasInternet,
                    "is_validated" to isValidated,
                    "network_type" to getNetworkTypeDescription()
                ))

                hasInternet && isValidated
            } else {
                // Fallback for older Android versions
                @Suppress("DEPRECATION")
                val activeNetworkInfo = connectivityManager.activeNetworkInfo
                @Suppress("DEPRECATION")
                val isConnected = activeNetworkInfo?.isConnectedOrConnecting == true

                logDebug("Network connectivity check (legacy)", mapOf(
                    "is_connected" to isConnected,
                    "network_type" to (activeNetworkInfo?.let {
                        @Suppress("DEPRECATION")
                        it.typeName
                    } ?: "unknown")
                ))

                isConnected
            }
        } catch (e: Exception) {
            logError("Error checking network connectivity", e)
            false
        }
    }

    /**
     * NAME: performVPNHealthCheck
     *
     * DESCRIPTION:
     *     Performs VPN service health check and triggers reconnection if needed.
     *     Checks VPN connection status and initiates recovery if connection is unhealthy.
     *
     * PARAMETERS:
     *     vpnService - VPN service instance for health checking
     */
    private suspend fun performVPNHealthCheck(vpnService: ITforceVPNService) {
        try {
            logDebug("Performing VPN health check")

            // Get current VPN state
            val currentState = vpnService.vpnState.value
            
            when {
                currentState.isConnected -> {
                    logDebug("VPN is connected - checking connection health")
                    
                    // 禁用后台健康检查重连，避免前后台切换时的误触发
                    // 健康检查应该由HeartbeatManager负责，而不是后台服务
                    logDebug("VPN connection health check disabled to prevent unwanted reconnections")
                }
                currentState.isOperationInProgress -> {
                    logDebug("VPN operation in progress - skipping health check")
                }
                else -> {
                    logDebug("VPN not connected - no health check needed")
                }
            }
        } catch (e: Exception) {
            logError("Error during VPN health check", e)
        }
    }

    /**
     * NAME: checkVPNConnectionHealth
     *
     * DESCRIPTION:
     *     Checks if VPN connection is healthy by examining connection metrics.
     *     This is a simplified health check - in a full implementation,
     *     this would involve network connectivity tests.
     *
     * PARAMETERS:
     *     vpnService - VPN service instance
     *
     * RETURNS:
     *     Boolean - true if connection appears healthy
     */
    private fun checkVPNConnectionHealth(vpnService: ITforceVPNService): Boolean {
        return try {
            // Simplified health check - in practice, this would involve:
            // 1. Checking last heartbeat response time
            // 2. Verifying data flow through VPN tunnel
            // 3. Testing connectivity to VPN server
            
            val currentState = vpnService.vpnState.value
            val isConnected = currentState.isConnected
            
            logDebug("VPN health check result: healthy=$isConnected")
            isConnected
        } catch (e: Exception) {
            logError("Error checking VPN connection health", e)
            false
        }
    }

    /**
     * NAME: triggerVPNReconnection
     *
     * DESCRIPTION:
     *     Triggers VPN reconnection when connection health issues are detected.
     *     Initiates internal reconnection process to restore VPN connectivity.
     *
     * PARAMETERS:
     *     vpnService - VPN service instance
     *     reason - Reason for reconnection trigger
     */
    private suspend fun triggerVPNReconnection(vpnService: ITforceVPNService, reason: String) {
        try {
            logInfo("Triggering VPN reconnection", mapOf("reason" to reason))
            
            // Trigger internal reconnection through VPN service
            // This would typically call the connection manager's reconnection method
            // vpnService.performInternalReconnection(reason)
            
            logInfo("VPN reconnection triggered successfully")
        } catch (e: Exception) {
            logError("Failed to trigger VPN reconnection", e)
        }
    }

    /**
     * NAME: getNetworkTypeDescription
     *
     * DESCRIPTION:
     *     Gets human-readable description of current network type.
     *
     * RETURNS:
     *     String - Network type description
     */
    private fun getNetworkTypeDescription(): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val activeNetwork = connectivityManager.activeNetwork
                val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
                
                when {
                    networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true -> "WiFi"
                    networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true -> "Cellular"
                    networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) == true -> "Ethernet"
                    else -> "Unknown"
                }
            } else {
                @Suppress("DEPRECATION")
                val activeNetworkInfo = connectivityManager.activeNetworkInfo
                @Suppress("DEPRECATION")
                activeNetworkInfo?.typeName ?: "Unknown"
            }
        } catch (e: Exception) {
            "Error"
        }
    }

    /**
     * NAME: onDestroy
     *
     * DESCRIPTION:
     *     Called when service is destroyed. Cleans up resources and cancels
     *     any ongoing operations.
     */
    override fun onDestroy() {
        super.onDestroy()
        logDebug("Network keep-alive job service destroyed")
        
        currentJob?.cancel()
        jobScope.cancel()
    }
}
