/**
 * FILE: BatteryOptimizationManager.kt
 *
 * DESCRIPTION:
 *     ITforce WAN Android battery optimization manager for comprehensive
 *     battery management and power-related optimizations. Handles battery
 *     status monitoring, power state changes, and optimization strategies.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.infrastructure.performance

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Build
import android.os.PowerManager
import androidx.annotation.RequiresApi
import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logError
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * NAME: BatteryStatus
 *
 * DESCRIPTION:
 *     Battery status information for VPN optimization decisions.
 *     Provides comprehensive battery state for power management.
 *
 * PROPERTIES:
 *     level - Battery level percentage (0-100)
 *     isCharging - Whether device is currently charging
 *     isLowBattery - Whether battery is in low power state
 *     temperature - Battery temperature in Celsius
 *     powerSaveMode - Whether device is in power save mode
 */
data class BatteryStatus(
    val level: Int,
    val isCharging: Boolean,
    val isLowBattery: Boolean,
    val temperature: Float,
    val powerSaveMode: Boolean
)

/**
 * NAME: PowerOptimizationStrategy
 *
 * DESCRIPTION:
 *     Power optimization strategy based on battery status.
 *     Defines how VPN service should adapt to power conditions.
 */
enum class PowerOptimizationStrategy {
    NORMAL,           // Normal operation
    CONSERVATIVE,     // Reduce background activity
    AGGRESSIVE,       // Minimize power consumption
    CRITICAL          // Emergency power saving
}

/**
 * NAME: BatteryOptimizationManager
 *
 * DESCRIPTION:
 *     Battery optimization manager for VPN service power management.
 *     Monitors battery status, handles power state changes, and provides
 *     optimization strategies for maintaining VPN stability while
 *     conserving battery life.
 *
 * PROPERTIES:
 *     context - Application context for system service access
 *     powerManager - PowerManager for power state monitoring
 *     batteryManager - BatteryManager for battery information
 *     batteryReceiver - BroadcastReceiver for battery change events
 *     currentBatteryStatus - Current battery status state
 *     optimizationStrategy - Current power optimization strategy
 */
class BatteryOptimizationManager(private val context: Context) {

    private val powerManager: PowerManager by lazy {
        context.getSystemService(Context.POWER_SERVICE) as PowerManager
    }
    
    private val batteryManager: BatteryManager? by lazy {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
        } else null
    }

    // Battery status state management
    private val _batteryStatus = MutableStateFlow(
        BatteryStatus(
            level = 100,
            isCharging = false,
            isLowBattery = false,
            temperature = 25.0f,
            powerSaveMode = false
        )
    )
    val batteryStatus: StateFlow<BatteryStatus> = _batteryStatus.asStateFlow()

    // Optimization strategy state
    private val _optimizationStrategy = MutableStateFlow(PowerOptimizationStrategy.NORMAL)
    val optimizationStrategy: StateFlow<PowerOptimizationStrategy> = _optimizationStrategy.asStateFlow()

    // Battery change receiver
    private val batteryReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                Intent.ACTION_BATTERY_CHANGED -> handleBatteryChanged(intent)
                PowerManager.ACTION_POWER_SAVE_MODE_CHANGED -> handlePowerSaveModeChanged()
                PowerManager.ACTION_DEVICE_IDLE_MODE_CHANGED -> handleDozeStateChanged()
            }
        }
    }

    private var isMonitoring = false

    companion object {
        private const val LOW_BATTERY_THRESHOLD = 15 // 15%
        private const val CRITICAL_BATTERY_THRESHOLD = 5 // 5%
        private const val HIGH_TEMPERATURE_THRESHOLD = 40.0f // 40°C
    }

    /**
     * NAME: startMonitoring
     *
     * DESCRIPTION:
     *     Starts battery status monitoring and optimization.
     *     Registers broadcast receivers for battery and power state changes.
     */
    fun startMonitoring() {
        if (isMonitoring) {
            logDebug("Battery optimization monitoring already started")
            return
        }

        logInfo("Starting battery optimization monitoring")

        // Register battery change receiver
        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_BATTERY_CHANGED)
            addAction(PowerManager.ACTION_POWER_SAVE_MODE_CHANGED)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                addAction(PowerManager.ACTION_DEVICE_IDLE_MODE_CHANGED)
            }
        }

        try {
            context.registerReceiver(batteryReceiver, filter)
            isMonitoring = true

            // Get initial battery status
            updateBatteryStatus()
            updateOptimizationStrategy()

            logInfo("Battery optimization monitoring started successfully")
        } catch (e: Exception) {
            logError("Failed to start battery optimization monitoring", e)
        }
    }

    /**
     * NAME: stopMonitoring
     *
     * DESCRIPTION:
     *     Stops battery status monitoring and cleans up resources.
     *     Unregisters broadcast receivers.
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            logDebug("Battery optimization monitoring not started")
            return
        }

        logInfo("Stopping battery optimization monitoring")

        try {
            context.unregisterReceiver(batteryReceiver)
            isMonitoring = false
            logInfo("Battery optimization monitoring stopped successfully")
        } catch (e: Exception) {
            logError("Failed to stop battery optimization monitoring", e)
        }
    }

    /**
     * NAME: handleBatteryChanged
     *
     * DESCRIPTION:
     *     Handles battery status change events.
     *     Updates battery status and optimization strategy.
     *
     * PARAMETERS:
     *     intent - Battery change intent with status information
     */
    private fun handleBatteryChanged(intent: Intent) {
        try {
            val level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
            val scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
            val status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1)
            val temperature = intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0) / 10.0f

            val batteryPct = if (level >= 0 && scale > 0) {
                (level * 100 / scale)
            } else {
                _batteryStatus.value.level
            }

            val isCharging = status == BatteryManager.BATTERY_STATUS_CHARGING ||
                           status == BatteryManager.BATTERY_STATUS_FULL

            val isLowBattery = batteryPct <= LOW_BATTERY_THRESHOLD
            val powerSaveMode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                powerManager.isPowerSaveMode
            } else {
                false
            }

            val newStatus = BatteryStatus(
                level = batteryPct,
                isCharging = isCharging,
                isLowBattery = isLowBattery,
                temperature = temperature,
                powerSaveMode = powerSaveMode
            )

            _batteryStatus.value = newStatus
            updateOptimizationStrategy()

            logDebug("Battery status updated", mapOf(
                "level" to batteryPct,
                "charging" to isCharging,
                "low_battery" to isLowBattery,
                "temperature" to temperature,
                "power_save" to powerSaveMode
            ))

        } catch (e: Exception) {
            logError("Error handling battery change", e)
        }
    }

    /**
     * NAME: handlePowerSaveModeChanged
     *
     * DESCRIPTION:
     *     Handles power save mode state changes.
     *     Updates optimization strategy when power save mode is toggled.
     */
    private fun handlePowerSaveModeChanged() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val powerSaveMode = powerManager.isPowerSaveMode
            logInfo("Power save mode changed: enabled=$powerSaveMode")

            // Update battery status with new power save mode
            _batteryStatus.value = _batteryStatus.value.copy(powerSaveMode = powerSaveMode)
            updateOptimizationStrategy()
        }
    }

    /**
     * NAME: handleDozeStateChanged
     *
     * DESCRIPTION:
     *     Handles Doze mode state changes (Android 6.0+).
     *     Adjusts optimization strategy when device enters/exits Doze mode.
     */
    @RequiresApi(Build.VERSION_CODES.M)
    private fun handleDozeStateChanged() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val isDozeMode = powerManager.isDeviceIdleMode
            logInfo("Doze mode changed: enabled=$isDozeMode")

            // Update optimization strategy for Doze mode
            updateOptimizationStrategy()
        }
    }

    /**
     * NAME: updateBatteryStatus
     *
     * DESCRIPTION:
     *     Updates current battery status by querying system services.
     *     Used for initial status and manual refresh.
     */
    private fun updateBatteryStatus() {
        try {
            val batteryIntent = context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
            if (batteryIntent != null) {
                handleBatteryChanged(batteryIntent)
            }
        } catch (e: Exception) {
            logError("Failed to update battery status", e)
        }
    }

    /**
     * NAME: updateOptimizationStrategy
     *
     * DESCRIPTION:
     *     Updates power optimization strategy based on current battery status.
     *     Determines appropriate power management approach for VPN service.
     */
    private fun updateOptimizationStrategy() {
        val status = _batteryStatus.value
        val isDozeMode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            powerManager.isDeviceIdleMode
        } else {
            false
        }

        val newStrategy = when {
            status.level <= CRITICAL_BATTERY_THRESHOLD -> {
                logWarn("Critical battery level detected: ${status.level}%")
                PowerOptimizationStrategy.CRITICAL
            }
            status.isLowBattery || status.powerSaveMode || isDozeMode -> {
                logInfo("Low power conditions detected - using conservative strategy")
                PowerOptimizationStrategy.AGGRESSIVE
            }
            status.temperature > HIGH_TEMPERATURE_THRESHOLD -> {
                logWarn("High battery temperature detected: ${status.temperature}°C")
                PowerOptimizationStrategy.CONSERVATIVE
            }
            status.isCharging -> {
                logDebug("Device charging - using normal strategy")
                PowerOptimizationStrategy.NORMAL
            }
            else -> {
                PowerOptimizationStrategy.NORMAL
            }
        }

        if (newStrategy != _optimizationStrategy.value) {
            logInfo("Optimization strategy changed: ${_optimizationStrategy.value} -> $newStrategy")
            _optimizationStrategy.value = newStrategy
        }
    }

    /**
     * NAME: getRecommendedHeartbeatInterval
     *
     * DESCRIPTION:
     *     Gets recommended heartbeat interval based on current optimization strategy.
     *     Adjusts VPN heartbeat frequency to balance connectivity and battery life.
     *
     * RETURNS:
     *     Long - Recommended heartbeat interval in milliseconds
     */
    fun getRecommendedHeartbeatInterval(): Long {
        return when (_optimizationStrategy.value) {
            PowerOptimizationStrategy.NORMAL -> 30_000L      // 30 seconds
            PowerOptimizationStrategy.CONSERVATIVE -> 60_000L // 1 minute
            PowerOptimizationStrategy.AGGRESSIVE -> 120_000L  // 2 minutes
            PowerOptimizationStrategy.CRITICAL -> 300_000L    // 5 minutes
        }
    }

    /**
     * NAME: shouldReduceBackgroundActivity
     *
     * DESCRIPTION:
     *     Determines if background activity should be reduced based on power conditions.
     *
     * RETURNS:
     *     Boolean - true if background activity should be reduced
     */
    fun shouldReduceBackgroundActivity(): Boolean {
        return _optimizationStrategy.value in listOf(
            PowerOptimizationStrategy.AGGRESSIVE,
            PowerOptimizationStrategy.CRITICAL
        )
    }

    /**
     * NAME: getBatteryOptimizationInfo
     *
     * DESCRIPTION:
     *     Gets comprehensive battery optimization information for monitoring.
     *
     * RETURNS:
     *     Map<String, Any> - Battery optimization status and recommendations
     */
    fun getBatteryOptimizationInfo(): Map<String, Any> {
        val status = _batteryStatus.value
        val strategy = _optimizationStrategy.value

        return mapOf(
            "battery_level" to status.level,
            "is_charging" to status.isCharging,
            "is_low_battery" to status.isLowBattery,
            "temperature" to status.temperature,
            "power_save_mode" to status.powerSaveMode,
            "optimization_strategy" to strategy.name,
            "recommended_heartbeat_interval" to getRecommendedHeartbeatInterval(),
            "should_reduce_background_activity" to shouldReduceBackgroundActivity(),
            "is_monitoring" to isMonitoring
        )
    }
}
