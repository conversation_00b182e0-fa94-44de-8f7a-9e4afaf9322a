/**
 * FILE: SystemOptimizationManager.kt
 *
 * DESCRIPTION:
 *     ITforce WAN Android system optimization manager for handling Doze mode,
 *     background keep-alive mechanisms, and vendor-specific optimizations.
 *     Ensures VPN service stability under Android power management restrictions.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.platform

import android.app.AlarmManager
import android.app.PendingIntent
import android.app.job.JobInfo
import android.app.job.JobScheduler
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.PowerManager
import androidx.annotation.RequiresApi
import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logError
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.services.NetworkKeepAliveJobService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import com.panabit.client.connection.models.VPNState
import com.panabit.client.connection.state.VPNStateManager
import com.panabit.client.connection.state.StateTransitionHelper
import com.panabit.client.infrastructure.error.VPNServiceError

/**
 * NAME: SystemOptimizationManager
 *
 * DESCRIPTION:
 *     Android system optimization manager handling Doze mode, app standby,
 *     and other system restrictions that could affect VPN service stability.
 *     Provides comprehensive background keep-alive mechanisms and vendor-specific
 *     optimization handling for stable VPN operation.
 *
 * PROPERTIES:
 *     context - Application context for system service access
 *     powerManager - PowerManager for Doze mode detection
 *     jobScheduler - JobScheduler for background task scheduling
 *     alarmManager - AlarmManager for heartbeat scheduling
 *     permissionManager - PermissionManager for battery optimization handling
 */
class SystemOptimizationManager(private val context: Context) {

    private val powerManager: PowerManager by lazy {
        context.getSystemService(Context.POWER_SERVICE) as PowerManager
    }
    
    private val jobScheduler: JobScheduler? by lazy {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
        } else null
    }
    
    private val alarmManager: AlarmManager by lazy {
        context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
    }
    
    private val permissionManager: PermissionManager by lazy {
        PermissionManager(context)
    }

    companion object {
        private const val NETWORK_CHECK_JOB_ID = 1000
        // HEARTBEAT_ALARM_ID和HEARTBEAT_INTERVAL已移除，heartbeat功能已集中在ConnectionManager
        private const val NETWORK_CHECK_INTERVAL = 15 * 60 * 1000L // 15 minutes
    }

    /**
     * NAME: initializeOptimizations
     *
     * DESCRIPTION:
     *     Initializes all system optimizations for VPN service stability.
     *     Sets up battery optimization, network keep-alive, and background tasks.
     *     Should be called when VPN service starts.
     *
     * RETURNS:
     *     Result<Unit> - Success or failure of initialization
     */
    suspend fun initializeOptimizations(): Result<Unit> = withContext(Dispatchers.IO) {
        return@withContext try {
            logInfo("Initializing system optimizations for VPN stability")
            
            // Check and handle battery optimization
            checkBatteryOptimization()
            
            // Setup network keep-alive mechanisms
            setupNetworkKeepAlive()

            // VPN协议级别的heartbeat已在ConnectionManager的HeartbeatManager中处理
            // 移除重复的系统级heartbeat任务以避免冲突

            // Handle vendor-specific optimizations
            handleVendorOptimizations()
            
            logInfo("System optimizations initialized successfully")
            Result.success(Unit)
        } catch (e: Exception) {
            logError("Failed to initialize system optimizations", mapOf("error" to (e.message ?: "unknown")), e)
            Result.failure(e)
        }
    }

    /**
     * NAME: checkBatteryOptimization
     *
     * DESCRIPTION:
     *     Checks battery optimization status and logs recommendations.
     *     Handles Android 6.0+ Doze mode restrictions by checking whitelist status.
     *     Provides guidance for requesting exemption when needed.
     */
    private fun checkBatteryOptimization() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val isIgnored = permissionManager.isBatteryOptimizationIgnored()

            if (isIgnored) {
                logInfo("App is in battery optimization whitelist - VPN service protected")
            } else {
                logWarn("App not in battery optimization whitelist - VPN may be affected by Doze mode")
                logInfo("CRITICAL: Battery optimization not disabled - this may cause VPN service termination")
                logInfo("Recommendation: Request battery optimization exemption immediately for stable VPN operation")

                // Log specific guidance for different scenarios
                logInfo("To fix this issue:")
                logInfo("1. Call requestBatteryOptimizationExemption() from UI")
                logInfo("2. Or guide user to Settings > Battery > Battery Optimization > [App] > Don't optimize")
            }

            // Check if device is currently in Doze mode
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val isDeviceIdle = powerManager.isDeviceIdleMode
                logDebug("Device Doze mode status", mapOf("idle" to isDeviceIdle))

                if (isDeviceIdle && !isIgnored) {
                    logWarn("CRITICAL: Device in Doze mode AND app not in battery whitelist - VPN likely to be killed")
                }
            }
        } else {
            logDebug("Battery optimization not applicable for Android version < 6.0")
        }
    }

    /**
     * NAME: requestBatteryOptimizationExemption
     *
     * DESCRIPTION:
     *     Requests battery optimization exemption for the VPN service.
     *     Should be called from UI context when user initiates VPN connection.
     *
     * PARAMETERS:
     *     activity - Activity context for launching system settings
     *
     * RETURNS:
     *     Boolean - true if request was initiated, false if not needed or failed
     */
    fun requestBatteryOptimizationExemption(activity: android.app.Activity): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!permissionManager.isBatteryOptimizationIgnored()) {
                    logInfo("Requesting battery optimization exemption for VPN stability")
                    permissionManager.requestBatteryOptimizationExemption(activity)
                    true
                } else {
                    logInfo("Battery optimization already ignored, no request needed")
                    false
                }
            } else {
                logDebug("Battery optimization not applicable for Android version < 6.0")
                false
            }
        } catch (e: Exception) {
            logError("Failed to request battery optimization exemption", mapOf("error" to (e.message ?: "unknown")), e)
            false
        }
    }

    /**
     * NAME: setupNetworkKeepAlive
     *
     * DESCRIPTION:
     *     Sets up network keep-alive mechanism using JobScheduler for Android 5.0+.
     *     Schedules periodic network connectivity checks to maintain VPN connection.
     *     Uses system-managed job scheduling for better battery efficiency.
     */
    private fun setupNetworkKeepAlive() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP && jobScheduler != null) {
            logInfo("Setting up network keep-alive using JobScheduler")
            
            val jobInfo = JobInfo.Builder(
                NETWORK_CHECK_JOB_ID,
                ComponentName(context, NetworkKeepAliveJobService::class.java)
            )
                .setRequiredNetworkType(JobInfo.NETWORK_TYPE_ANY)
                .setPersisted(true)
                .setPeriodic(NETWORK_CHECK_INTERVAL)
                .setRequiresCharging(false)
                .setRequiresDeviceIdle(false)
                .build()

            val result = jobScheduler!!.schedule(jobInfo)
            if (result == JobScheduler.RESULT_SUCCESS) {
                logInfo("Network keep-alive job scheduled successfully")
            } else {
                logWarn("Failed to schedule network keep-alive job")
            }
        } else {
            logDebug("JobScheduler not available, using alternative keep-alive mechanism")
        }
    }

    // setupBackgroundTasks方法已移除
    // VPN协议级别的heartbeat功能已集中在ConnectionManager的HeartbeatManager中
    // 避免重复实现和潜在冲突

    /**
     * NAME: handleVendorOptimizations
     *
     * DESCRIPTION:
     *     Handles vendor-specific power management optimizations.
     *     Detects device manufacturer and logs recommendations for
     *     vendor-specific battery optimization settings.
     */
    private fun handleVendorOptimizations() {
        val manufacturer = Build.MANUFACTURER.lowercase()
        val model = Build.MODEL
        
        logInfo("Checking vendor-specific optimizations")
        
        when {
            manufacturer.contains("xiaomi") -> {
                logInfo("Xiaomi device detected - recommend disabling MIUI optimization for VPN")
                logDebug("MIUI optimization may affect background VPN service operation")
            }
            manufacturer.contains("huawei") || manufacturer.contains("honor") -> {
                logInfo("Huawei/Honor device detected - recommend adding app to protected apps")
                logDebug("EMUI power management may terminate VPN service in background")
            }
            manufacturer.contains("oppo") -> {
                logInfo("OPPO device detected - recommend disabling ColorOS battery optimization")
                logDebug("ColorOS may aggressively manage background apps including VPN")
            }
            manufacturer.contains("vivo") -> {
                logInfo("Vivo device detected - recommend adding app to high background app consumption")
                logDebug("FunTouch OS may limit background VPN service operation")
            }
            manufacturer.contains("oneplus") -> {
                logInfo("OnePlus device detected - recommend disabling advanced optimization")
                logDebug("OxygenOS battery optimization may affect VPN stability")
            }
            manufacturer.contains("samsung") -> {
                logInfo("Samsung device detected - recommend adding app to never sleeping apps")
                logDebug("Samsung Device Care may optimize VPN service in background")
            }
            else -> {
                logDebug("Standard Android device - no vendor-specific optimizations needed")
            }
        }
    }

    /**
     * NAME: isDozeMode
     *
     * DESCRIPTION:
     *     Checks if device is currently in Doze mode (Android 6.0+).
     *     Provides information about current power management state.
     *
     * RETURNS:
     *     Boolean - true if device is in Doze mode, false otherwise
     */
    @RequiresApi(Build.VERSION_CODES.M)
    fun isDozeMode(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val isIdle = powerManager.isDeviceIdleMode
            logDebug("Doze mode check: isDeviceIdle=$isIdle")
            isIdle
        } else {
            false
        }
    }

    /**
     * NAME: cancelOptimizations
     *
     * DESCRIPTION:
     *     Cancels all scheduled optimization tasks.
     *     Should be called when VPN service stops to clean up resources.
     */
    fun cancelOptimizations() {
        logInfo("Cancelling system optimization tasks")
        
        // Cancel JobScheduler tasks
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP && jobScheduler != null) {
            jobScheduler!!.cancel(NETWORK_CHECK_JOB_ID)
            logDebug("Network keep-alive job cancelled")
        }

        // AlarmManager heartbeat任务已移除，VPN heartbeat由ConnectionManager的HeartbeatManager处理
        
        logInfo("System optimization tasks cancelled successfully")
    }

    /**
     * NAME: getOptimizationStatus
     *
     * DESCRIPTION:
     *     Gets current system optimization status for monitoring and debugging.
     *     Provides comprehensive information about power management settings.
     *
     * RETURNS:
     *     Map<String, Any> - Current optimization status information
     */
    fun getOptimizationStatus(): Map<String, Any> {
        val status = mutableMapOf<String, Any>()
        
        // Battery optimization status
        status["battery_optimization_ignored"] = permissionManager.isBatteryOptimizationIgnored()
        
        // Doze mode status (Android 6.0+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            status["doze_mode"] = powerManager.isDeviceIdleMode
        }
        
        // Device information
        status["manufacturer"] = Build.MANUFACTURER
        status["model"] = Build.MODEL
        status["android_version"] = Build.VERSION.SDK_INT
        
        // Job scheduler availability
        status["job_scheduler_available"] = (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP)
        
        logDebug("System optimization status retrieved", status)
        return status
    }

    // MARK: - Constraint Assessment and Handling (Merged from AndroidConstraintsHandler)

    /**
     * NAME: assessConstraintImpact
     *
     * DESCRIPTION:
     *     Assesses the impact of current system constraints on VPN operation.
     *     Provides comprehensive analysis of battery optimization, background restrictions,
     *     and other system limitations that may affect VPN stability.
     *
     * RETURNS:
     *     ConstraintImpact - Assessment of constraint impact with severity and issues
     */
    fun assessConstraintImpact(): ConstraintImpact {
        val issues = mutableListOf<String>()
        var severity = ConstraintSeverity.NONE

        // Check battery optimization
        if (!permissionManager.isBatteryOptimizationIgnored()) {
            issues.add("Battery optimization enabled - may affect background connectivity")
            severity = maxOf(severity, ConstraintSeverity.MEDIUM)
        }

        // Check background restrictions (Android 9.0+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE)
                as android.app.ActivityManager
            if (activityManager.isBackgroundRestricted) {
                issues.add("Background app restrictions enabled - may prevent VPN operation")
                severity = maxOf(severity, ConstraintSeverity.HIGH)
            }
        }

        // Check Doze mode (Android 6.0+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && powerManager.isDeviceIdleMode) {
            issues.add("Device in Doze mode - network activity may be limited")
            severity = maxOf(severity, ConstraintSeverity.MEDIUM)
        }

        // Check power save mode
        if (powerManager.isPowerSaveMode) {
            issues.add("Power save mode active - performance may be reduced")
            severity = maxOf(severity, ConstraintSeverity.LOW)
        }

        return ConstraintImpact(severity, issues)
    }

    /**
     * NAME: handleConstraintViolation
     *
     * DESCRIPTION:
     *     Handles constraint violations that affect VPN operation.
     *     Provides appropriate responses based on violation type and current VPN state.
     *
     * PARAMETERS:
     *     violation - Type of constraint violation
     *     stateManager - VPN state manager for state transitions (optional)
     */
    suspend fun handleConstraintViolation(
        violation: ConstraintViolation,
        stateManager: VPNStateManager? = null
    ) {
        logWarn("Handling constraint violation: $violation")

        when (violation) {
            ConstraintViolation.BATTERY_OPTIMIZATION -> {
                handleBatteryOptimizationImpact(stateManager)
            }
            ConstraintViolation.BACKGROUND_RESTRICTION -> {
                handleBackgroundRestrictionImpact(stateManager)
            }
            ConstraintViolation.DOZE_MODE -> {
                handleDozeModeImpact()
            }
            ConstraintViolation.POWER_SAVE_MODE -> {
                handlePowerSaveModeImpact()
            }
        }
    }

    /**
     * NAME: getConstraintGuidance
     *
     * DESCRIPTION:
     *     Gets user guidance for resolving system constraints.
     *     Provides localized, actionable recommendations for users.
     *
     * RETURNS:
     *     String - User-friendly guidance message in Chinese
     */
    fun getConstraintGuidance(): String {
        val impact = assessConstraintImpact()

        return when (impact.severity) {
            ConstraintSeverity.HIGH -> {
                "检测到系统限制可能影响VPN正常运行。建议在设置中允许应用后台运行并关闭电池优化。"
            }
            ConstraintSeverity.MEDIUM -> {
                "检测到一些系统限制可能影响VPN稳定性。建议将应用加入电池优化白名单。"
            }
            ConstraintSeverity.LOW -> {
                "检测到轻微的系统限制，VPN运行基本正常。"
            }
            ConstraintSeverity.NONE -> {
                "系统设置良好，VPN可以正常运行。"
            }
        }
    }

    // MARK: - Private Constraint Handlers

    /**
     * NAME: handleBatteryOptimizationImpact
     *
     * DESCRIPTION:
     *     Handles impact of battery optimization on VPN operation.
     */
    private suspend fun handleBatteryOptimizationImpact(stateManager: VPNStateManager?) {
        logInfo("Battery optimization may affect VPN connection stability")

        // Log guidance but don't change VPN state
        logInfo("Recommendation: Add app to battery optimization whitelist")
    }

    /**
     * NAME: handleBackgroundRestrictionImpact
     *
     * DESCRIPTION:
     *     Handles impact of background restrictions on VPN operation.
     */
    private suspend fun handleBackgroundRestrictionImpact(stateManager: VPNStateManager?) {
        logWarn("Background restrictions may cause VPN disconnection")

        if (stateManager != null) {
            val currentState = stateManager.currentStateValue
            if (currentState.isConnected) {
                // Transition to error state with guidance
                val error = VPNServiceError.PermissionDenied(
                    errorMessage = "后台应用限制可能影响VPN连接，请在设置中允许后台运行",
                    permissionType = "BACKGROUND_RESTRICTION",
                    canRetry = true
                )
                StateTransitionHelper.handleConnectionError(stateManager, error)
            }
        }
    }

    /**
     * NAME: handleDozeModeImpact
     *
     * DESCRIPTION:
     *     Handles impact of Doze mode on VPN operation.
     */
    private fun handleDozeModeImpact() {
        logInfo("Device in Doze mode - VPN may experience reduced activity")
        // Don't take action, let VPN handle Doze mode naturally
    }

    /**
     * NAME: handlePowerSaveModeImpact
     *
     * DESCRIPTION:
     *     Handles impact of power save mode on VPN operation.
     */
    private fun handlePowerSaveModeImpact() {
        logInfo("Power save mode active - VPN performance may be reduced")
        // Don't take action, just log for awareness
    }
}

// MARK: - Data Classes for Constraint Management

/**
 * NAME: ConstraintImpact
 *
 * DESCRIPTION:
 *     Represents the impact of system constraints on VPN operation.
 */
data class ConstraintImpact(
    val severity: ConstraintSeverity,
    val issues: List<String>
)

/**
 * NAME: ConstraintSeverity
 *
 * DESCRIPTION:
 *     Severity levels for constraint impact assessment.
 */
enum class ConstraintSeverity {
    NONE, LOW, MEDIUM, HIGH
}

/**
 * NAME: ConstraintViolation
 *
 * DESCRIPTION:
 *     Types of system constraint violations that affect VPN operation.
 */
enum class ConstraintViolation {
    BATTERY_OPTIMIZATION,
    BACKGROUND_RESTRICTION,
    DOZE_MODE,
    POWER_SAVE_MODE
}
