/**
 * FILE: LazyVPNServiceAdapter.kt
 *
 * DESCRIPTION:
 *     Lazy VPN service adapter that follows iOS initialization pattern.
 *     VPN service is started only when first method is called, not during adapter creation.
 *     This matches iOS PlatformChannelHandler behavior for consistent cross-platform initialization.
 *
 * AUTHOR: wei
 * HISTORY: 14/07/2025 create for iOS initialization pattern consistency
 */

package com.panabit.client.domain.adapters

import android.content.Context
import android.content.Intent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import com.panabit.client.domain.interfaces.VPNServiceInterface
import com.panabit.client.domain.interfaces.InterfaceInfo
import com.panabit.client.vpn.ITforceVPNService
import com.panabit.client.connection.models.VPNState
import com.panabit.client.connection.models.ServerInfo
import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

/**
 * NAME: LazyVPNServiceAdapter
 *
 * DESCRIPTION:
 *     Lazy VPN service adapter that starts VPN service on-demand.
 *     Follows iOS PlatformChannelHandler initialization pattern where VPN service
 *     is created and started only when first method call is made.
 *
 * PROPERTIES:
 *     context - Android context for service operations
 *     actualAdapter - Real VPN service adapter (created lazily)
 */
class LazyVPNServiceAdapter(
    private val context: Context
) : VPNServiceInterface {

    private var actualAdapter: VPNServiceAdapter? = null
    private var isInitializing = false

    // MARK: - Lifecycle Management

    override suspend fun initialize(): Result<Unit> = withContext(Dispatchers.IO) {
        return@withContext try {
            ensureVPNServiceStarted()
            actualAdapter?.initialize() ?: Result.failure(Exception("Failed to create VPN service adapter"))
        } catch (e: Exception) {
            logError("Lazy VPN service adapter initialization failed", emptyMap(), e)
            Result.failure(e)
        }
    }

    override fun isInitialized(): Boolean {
        return actualAdapter?.isInitialized() ?: false
    }

    override suspend fun isHealthy(): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            ensureVPNServiceStarted()
            actualAdapter?.isHealthy() ?: false
        } catch (e: Exception) {
            logError("Health check failed", emptyMap(), e)
            false
        }
    }

    // MARK: - Authentication

    override suspend fun login(username: String, password: String): Result<Map<String, Any>> =
        withContext(Dispatchers.IO) {
            return@withContext try {
                ensureVPNServiceStarted()
                actualAdapter?.login(username, password) 
                    ?: Result.failure(Exception("VPN service adapter not available"))
            } catch (e: Exception) {
                logError("Login operation failed", emptyMap(), e)
                Result.failure(e)
            }
        }

    // MARK: - Connection Management

    override suspend fun connect(serverId: String): Result<Map<String, Any>> = withContext(Dispatchers.IO) {
        return@withContext try {
            ensureVPNServiceStarted()
            actualAdapter?.connect(serverId) 
                ?: Result.failure(Exception("VPN service adapter not available"))
        } catch (e: Exception) {
            logError("Connect operation failed", emptyMap(), e)
            Result.failure(e)
        }
    }

    override suspend fun disconnect(): Result<Unit> = withContext(Dispatchers.IO) {
        return@withContext try {
            ensureVPNServiceStarted()
            actualAdapter?.disconnect() ?: Result.failure(Exception("VPN service adapter not available"))
        } catch (e: Exception) {
            logError("Disconnect operation failed", emptyMap(), e)
            Result.failure(e)
        }
    }

    // MARK: - Status Monitoring

    override suspend fun getStatus(): VPNState = withContext(Dispatchers.IO) {
        return@withContext try {
            ensureVPNServiceStarted()
            actualAdapter?.getStatus() ?: VPNState.Disconnected
        } catch (e: Exception) {
            logError("Failed to get VPN status", emptyMap(), e)
            VPNState.Disconnected
        }
    }

    override suspend fun getInterfaceInfo(): InterfaceInfo = withContext(Dispatchers.IO) {
        return@withContext try {
            ensureVPNServiceStarted()
            actualAdapter?.getInterfaceInfo() ?: InterfaceInfo.createDefault()
        } catch (e: Exception) {
            logError("Failed to get interface info", emptyMap(), e)
            InterfaceInfo.createDefault()
        }
    }

    // MARK: - Server Management

    override suspend fun getServers(): List<ServerInfo> = withContext(Dispatchers.IO) {
        return@withContext try {
            ensureVPNServiceStarted()
            actualAdapter?.getServers() ?: emptyList()
        } catch (e: Exception) {
            logError("Failed to get servers", emptyMap(), e)
            emptyList()
        }
    }

    override suspend fun pingServer(serverId: String): Int = withContext(Dispatchers.IO) {
        return@withContext try {
            ensureVPNServiceStarted()
            actualAdapter?.pingServer(serverId) ?: -1
        } catch (e: Exception) {
            logError("Failed to ping server", mapOf("server_id" to serverId), e)
            -1
        }
    }

    override suspend fun pingServers(): Result<Unit> = withContext(Dispatchers.IO) {
        return@withContext try {
            ensureVPNServiceStarted()
            actualAdapter?.pingServers() ?: Result.failure(Exception("VPN service adapter not available"))
        } catch (e: Exception) {
            logError("Ping servers operation failed", emptyMap(), e)
            Result.failure(e)
        }
    }

    override suspend fun setServerListUrl(url: String): Result<Unit> = withContext(Dispatchers.IO) {
        return@withContext try {
            ensureVPNServiceStarted()
            actualAdapter?.setServerListUrl(url) ?: Result.failure(Exception("VPN service adapter not available"))
        } catch (e: Exception) {
            logError("Set server list URL operation failed", mapOf("url" to url), e)
            Result.failure(e)
        }
    }

    // MARK: - Private Helper Methods

    /**
     * NAME: ensureVPNServiceStarted
     *
     * DESCRIPTION:
     *     Ensures VPN service is started and adapter is created.
     *     Follows iOS pattern of starting VPN service on first method call.
     *     Uses startForegroundService() to handle Android background execution limits.
     */
    private suspend fun ensureVPNServiceStarted() {
        if (actualAdapter != null) {
            return // Already initialized
        }

        if (isInitializing) {
            // Wait for initialization to complete
            var retryCount = 0
            while (actualAdapter == null && retryCount < 50) {
                kotlinx.coroutines.delay(100)
                retryCount++
            }
            return
        }

        isInitializing = true
        try {
            logInfo("Starting VPN service on-demand (iOS pattern)")

            // Start VPN service using foreground service to handle background restrictions
            val intent = Intent(context, ITforceVPNService::class.java)

            try {
                // Use startForegroundService for Android 8.0+ to handle background execution limits
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    context.startForegroundService(intent)
                    logInfo("VPN service started as foreground service (Android 8.0+)")
                } else {
                    context.startService(intent)
                    logInfo("VPN service started as regular service (Android < 8.0)")
                }
            } catch (e: IllegalStateException) {
                // Handle background execution limit - this is expected behavior
                logWarn("Cannot start service in background, this is normal Android behavior", mapOf(
                    "error" to (e.message ?: "unknown"),
                    "solution" to "Service will be started when app comes to foreground"
                ), e)

                // Check if service is already running
                val existingService = ITforceVPNService.getInstance()
                if (existingService != null) {
                    logInfo("VPN service already running, using existing instance")
                    actualAdapter = VPNServiceAdapter(existingService, context)
                    return
                } else {
                    // Service not running and can't start in background
                    throw Exception("Cannot start VPN service while app is in background. Please bring app to foreground and try again.")
                }
            }

            // Wait for service to start
            var vpnService: ITforceVPNService? = null
            var retryCount = 0
            while (vpnService == null && retryCount < 50) {
                kotlinx.coroutines.delay(100)
                vpnService = ITforceVPNService.getInstance()
                retryCount++
            }

            if (vpnService == null) {
                throw Exception("Failed to start VPN service after retries")
            }

            // Create actual adapter
            actualAdapter = VPNServiceAdapter(vpnService, context)
            logInfo("VPN service started and adapter created successfully")

        } catch (e: Exception) {
            logError("Failed to ensure VPN service started", emptyMap(), e)
            throw e
        } finally {
            isInitializing = false
        }
    }

    // MARK: - Internal Access

    /**
     * NAME: getConnectionManager
     *
     * DESCRIPTION:
     *     Get the underlying connection manager for internal operations.
     *
     * RETURNS:
     *     ConnectionManager? - Connection manager instance or null if not available
     */
    override fun getConnectionManager(): com.panabit.client.connection.ConnectionManager? {
        return try {
            actualAdapter?.getConnectionManager()
        } catch (e: Exception) {
            logError("Failed to get connection manager from lazy adapter", emptyMap(), e)
            null
        }
    }
}
