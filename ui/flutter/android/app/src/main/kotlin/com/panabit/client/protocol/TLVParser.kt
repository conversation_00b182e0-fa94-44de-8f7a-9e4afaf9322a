/**
 * FILE: TLVParser.kt
 *
 * DESCRIPTION:
 *     TLV attribute parser for SDWAN protocol packets.
 *     Handles parsing and building of TLV attribute sequences.
 *     Enforces strict attribute ordering required by protocol specification.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.protocol

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import com.panabit.client.protocol.models.TLVAttribute
import com.panabit.client.protocol.models.TLVAttributeType
import com.panabit.client.protocol.models.EncryptionMethod

/**
 * NAME: TLVParser
 *
 * DESCRIPTION:
 *     Parser for TLV attribute sequences in SDWAN protocol packets.
 *     Provides methods for parsing and building TLV attribute data.
 *     Enforces protocol-required attribute ordering.
 *
 * METHODS:
 *     parseAttributes - Parses TLV attributes from binary data
 *     buildAttributes - Builds binary data from TLV attributes
 *     validateOpenPacketOrder - Validates OPEN packet attribute ordering
 */
class TLVParser {

    /**
     * NAME: parseAttributes
     *
     * DESCRIPTION:
     *     Parses TLV attributes from binary data.
     *     Processes all attributes in sequence until data is exhausted.
     *
     * PARAMETERS:
     *     data - Binary data containing TLV attributes
     *
     * RETURNS:
     *     List<TLVAttribute> - List of parsed attributes
     *
     * THROWS:
     *     IllegalArgumentException - If data format is invalid
     */
    fun parseAttributes(data: ByteArray): List<TLVAttribute> {
        val attributes = mutableListOf<TLVAttribute>()
        var offset = 0

        while (offset < data.size - 2) { // Need at least 2 bytes for type+length
            try {
                val (attribute, nextOffset) = TLVAttribute.fromByteArray(data, offset)
                attributes.add(attribute)
                offset = nextOffset
            } catch (e: Exception) {
                throw IllegalArgumentException("Failed to parse TLV attribute at offset $offset", e)
            }
        }

        return attributes
    }

    /**
     * NAME: buildAttributes
     *
     * DESCRIPTION:
     *     Builds binary data from TLV attributes.
     *     Concatenates all attributes into single byte array.
     *
     * PARAMETERS:
     *     attributes - List of TLV attributes to encode
     *
     * RETURNS:
     *     ByteArray - Binary data containing all attributes
     */
    fun buildAttributes(attributes: List<TLVAttribute>): ByteArray {
        return attributes.fold(ByteArray(0)) { acc, attr ->
            acc + attr.toByteArray()
        }
    }

    /**
     * NAME: validateOpenPacketOrder
     *
     * DESCRIPTION:
     *     Validates that OPEN packet attributes follow required ordering.
     *     Protocol requires: MTU (first), USERNAME (second), PASSWORD (third), ENCRYPT (fourth).
     *     This ordering matches Go backend TLV constants and iOS implementation.
     *
     * PARAMETERS:
     *     attributes - List of attributes to validate
     *
     * RETURNS:
     *     Boolean - true if ordering is correct, false otherwise
     */
    fun validateOpenPacketOrder(attributes: List<TLVAttribute>): Boolean {
        if (attributes.size < 4) return false

        // Check required first four attributes (corrected order)
        if (attributes[0].type != TLVAttributeType.MTU) return false
        if (attributes[1].type != TLVAttributeType.USERNAME) return false
        if (attributes[2].type != TLVAttributeType.PASSWORD) return false
        if (attributes[3].type != TLVAttributeType.ENCRYPT) return false

        return true
    }

    /**
     * NAME: findAttribute
     *
     * DESCRIPTION:
     *     Finds first attribute of specified type in attribute list.
     *
     * PARAMETERS:
     *     attributes - List of attributes to search
     *     type - Attribute type to find
     *
     * RETURNS:
     *     TLVAttribute? - Found attribute or null if not found
     */
    fun findAttribute(attributes: List<TLVAttribute>, type: TLVAttributeType): TLVAttribute? {
        return attributes.find { it.type == type }
    }

    /**
     * NAME: findAllAttributes
     *
     * DESCRIPTION:
     *     Finds all attributes of specified type in attribute list.
     *
     * PARAMETERS:
     *     attributes - List of attributes to search
     *     type - Attribute type to find
     *
     * RETURNS:
     *     List<TLVAttribute> - List of matching attributes
     */
    fun findAllAttributes(attributes: List<TLVAttribute>, type: TLVAttributeType): List<TLVAttribute> {
        return attributes.filter { it.type == type }
    }

    /**
     * NAME: extractStringValue
     *
     * DESCRIPTION:
     *     Extracts string value from TLV attribute.
     *     Assumes UTF-8 encoding.
     *
     * PARAMETERS:
     *     attribute - TLV attribute containing string data
     *
     * RETURNS:
     *     String - Decoded string value
     */
    fun extractStringValue(attribute: TLVAttribute): String {
        return String(attribute.value, Charsets.US_ASCII)
    }

    /**
     * NAME: extractIntValue
     *
     * DESCRIPTION:
     *     Extracts integer value from TLV attribute.
     *     Supports 1, 2, and 4 byte integers in big-endian format.
     *
     * PARAMETERS:
     *     attribute - TLV attribute containing integer data
     *
     * RETURNS:
     *     Int - Decoded integer value
     *
     * THROWS:
     *     IllegalArgumentException - If attribute size is not 1, 2, or 4 bytes
     */
    fun extractIntValue(attribute: TLVAttribute): Int {
        return when (attribute.value.size) {
            1 -> attribute.value[0].toInt() and 0xFF
            2 -> {
                val value = (attribute.value[0].toInt() and 0xFF) shl 8 or
                           (attribute.value[1].toInt() and 0xFF)
                value
            }
            4 -> {
                val value = (attribute.value[0].toInt() and 0xFF) shl 24 or
                           (attribute.value[1].toInt() and 0xFF) shl 16 or
                           (attribute.value[2].toInt() and 0xFF) shl 8 or
                           (attribute.value[3].toInt() and 0xFF)
                value
            }
            else -> throw IllegalArgumentException("Unsupported integer size: ${attribute.value.size}")
        }
    }

    /**
     * NAME: extractIPAddress
     *
     * DESCRIPTION:
     *     Extracts IPv4 address from TLV attribute.
     *     Converts 4-byte binary data to dotted decimal notation.
     *     Uses defensive programming - returns null for invalid data instead of throwing.
     *
     * PARAMETERS:
     *     attribute - TLV attribute containing IPv4 address (should be 4 bytes)
     *
     * RETURNS:
     *     String? - IPv4 address in dotted decimal notation, null if invalid
     */
    fun extractIPAddress(attribute: TLVAttribute): String? {
        // Follow iOS pattern: check if we have at least 4 bytes, return null if not
        if (attribute.value.size < 4) {
            return null
        }

        // Use only the first 4 bytes (in case there are more)
        return attribute.value.take(4).joinToString(".") { (it.toInt() and 0xFF).toString() }
    }

    /**
     * NAME: buildOpenPacketAttributes
     *
     * DESCRIPTION:
     *     Builds TLV attributes for OPEN packet with correct ordering.
     *     Ensures MTU, USERNAME, PASSWORD are in required positions (matches Go backend).
     *
     * PARAMETERS:
     *     username - Username string
     *     encryptedPassword - Pre-encrypted password (16 bytes)
     *     mtu - MTU value
     *     encryptionMethod - Encryption method to include in packet
     *     additionalAttributes - Optional additional attributes
     *
     * RETURNS:
     *     List<TLVAttribute> - Properly ordered attribute list
     */
    fun buildOpenPacketAttributes(
        username: String,
        encryptedPassword: ByteArray,
        mtu: UShort,
        encryptionMethod: EncryptionMethod,
        additionalAttributes: List<TLVAttribute> = emptyList()
    ): List<TLVAttribute> {
        val attributes = mutableListOf<TLVAttribute>()

        // Required attributes in exact order (corrected to match Go backend and iOS)
        // Go backend order: MTU (first), Username (second), Password (third), Encrypt (fourth)
        attributes.add(TLVAttribute.createMTU(mtu))                    // Must be first (Type=0x03)
        attributes.add(TLVAttribute.createUsername(username))          // Must be second (Type=0x01)
        attributes.add(TLVAttribute.createPassword(encryptedPassword)) // Must be third (Type=0x02)

        // Always add encryption attribute (including NONE)
        attributes.add(TLVAttribute.createEncrypt(encryptionMethod))   // Fourth (Type=0x08)

        // Add any additional attributes
        attributes.addAll(additionalAttributes)

        return attributes
    }
}
