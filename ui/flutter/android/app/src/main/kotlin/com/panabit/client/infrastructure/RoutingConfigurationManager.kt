package com.panabit.client.infrastructure

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError
import com.panabit.client.platform.models.RoutingMode
import com.panabit.client.platform.models.RouteInfo

/**
 * NAME: RoutingConfiguration
 *
 * DESCRIPTION:
 *     Data class representing VPN routing configuration.
 *     Equivalent to iOS RoutingConfiguration for cross-platform consistency.
 *
 * PROPERTIES:
 *     mode - Routing mode (ALL or CUSTOM)
 *     customRoutes - List of custom routes for CUSTOM mode
 */
data class RoutingConfiguration(
    val mode: RoutingMode = RoutingMode.ALL,
    val customRoutes: List<String> = emptyList()
) {
    /**
     * NAME: isValid
     *
     * DESCRIPTION:
     *     Validates the routing configuration.
     *
     * RETURNS:
     *     Boolean - true if configuration is valid
     */
    fun isValid(): Boolean {
        return when (mode) {
            RoutingMode.ALL -> true
            RoutingMode.CUSTOM -> customRoutes.isNotEmpty() && customRoutes.all { isValidCIDR(it) }
        }
    }

    /**
     * NAME: toRouteInfoList
     *
     * DESCRIPTION:
     *     Converts custom routes to RouteInfo list for VPN configuration.
     *
     * RETURNS:
     *     List<RouteInfo> - List of route information objects
     */
    fun toRouteInfoList(): List<RouteInfo> {
        return customRoutes.mapNotNull { cidr ->
            try {
                val parts = cidr.split("/")
                if (parts.size == 2) {
                    RouteInfo(parts[0], parts[1].toInt())
                } else null
            } catch (e: Exception) {
                null
            }
        }
    }

    private fun isValidCIDR(cidr: String): Boolean {
        return try {
            val parts = cidr.split("/")
            if (parts.size != 2) return false
            
            val ip = parts[0]
            val prefix = parts[1].toInt()
            
            // Basic IP validation
            val ipParts = ip.split(".")
            if (ipParts.size != 4) return false
            
            ipParts.all { part ->
                val num = part.toInt()
                num in 0..255
            } && prefix in 0..32
        } catch (e: Exception) {
            false
        }
    }
}

/**
 * NAME: RoutingConfigurationManager
 *
 * DESCRIPTION:
 *     Manages VPN routing configuration in memory for Android backend.
 *     UI layer handles SharedPreferences persistence, backend only stores in memory.
 *     Configuration is applied during VPN connection establishment.
 *
 * FEATURES:
 *     - In-memory routing configuration storage
 *     - Routing mode storage (ALL/CUSTOM)
 *     - Custom routes storage for VPN inclusive IP configuration
 *     - Configuration validation and error handling
 *     - Thread-safe operations with proper synchronization
 *     - Logging integration for debugging and monitoring
 *
 * USAGE:
 *     val manager = RoutingConfigurationManager()
 *     manager.updateRoutingConfiguration(RoutingConfiguration(RoutingMode.CUSTOM, listOf("***********/24")))
 *     val config = manager.getCurrentRoutingConfiguration()
 */
class RoutingConfigurationManager {

    companion object {
        // Route separator for parsing
        private const val ROUTE_SEPARATOR = ","
    }

    // In-memory routing configuration storage
    @Volatile
    private var currentConfiguration: RoutingConfiguration = RoutingConfiguration()

    // Thread synchronization
    private val configurationLock = Any()

    /**
     * NAME: updateRoutingConfiguration
     *
     * DESCRIPTION:
     *     Updates routing configuration in memory.
     *     UI layer handles SharedPreferences persistence separately.
     *
     * PARAMETERS:
     *     configuration - Routing configuration to store in memory
     *
     * RETURNS:
     *     Boolean - true if update was successful, false otherwise
     */
    fun updateRoutingConfiguration(configuration: RoutingConfiguration): Boolean {
        return try {
            logInfo("Updating routing configuration in memory", mapOf(
                "mode" to configuration.mode.toConfigValue(),
                "custom_routes_count" to configuration.customRoutes.size
            ))

            // Validate configuration before storing
            if (!configuration.isValid()) {
                logWarn("Invalid routing configuration rejected", mapOf(
                    "mode" to configuration.mode.toConfigValue(),
                    "routes" to configuration.customRoutes.toString()
                ))
                return false
            }

            // Update in-memory configuration with thread safety
            synchronized(configurationLock) {
                currentConfiguration = configuration
            }

            logInfo("Routing configuration updated successfully in memory", mapOf(
                "mode" to configuration.mode.toConfigValue(),
                "custom_routes" to configuration.customRoutes.toString()
            ))

            true
        } catch (e: Exception) {
            logError("Exception updating routing configuration", mapOf("error" to (e.message ?: "unknown")))
            false
        }
    }

    /**
     * NAME: getCurrentRoutingConfiguration
     *
     * DESCRIPTION:
     *     Retrieves current routing configuration from memory.
     *     Used during VPN connection establishment to apply routing settings.
     *
     * RETURNS:
     *     RoutingConfiguration - Current routing configuration in memory
     */
    fun getCurrentRoutingConfiguration(): RoutingConfiguration {
        return try {
            logDebug("Retrieving current routing configuration from memory")

            // Get current configuration with thread safety
            val configuration = synchronized(configurationLock) {
                currentConfiguration
            }

            logDebug("Retrieved routing configuration from memory", mapOf(
                "mode" to configuration.mode.toConfigValue(),
                "custom_routes_count" to configuration.customRoutes.size,
                "custom_routes" to configuration.customRoutes.toString()
            ))

            configuration
        } catch (e: Exception) {
            logError("Exception retrieving routing configuration, returning defaults", mapOf(
                "error" to (e.message ?: "unknown")
            ))
            RoutingConfiguration() // Return default configuration on error
        }
    }

    /**
     * NAME: resetToDefaults
     *
     * DESCRIPTION:
     *     Resets routing configuration to defaults in memory.
     *
     * RETURNS:
     *     Boolean - true if reset was successful, false otherwise
     */
    fun resetToDefaults(): Boolean {
        return try {
            logInfo("Resetting routing configuration to defaults")

            synchronized(configurationLock) {
                currentConfiguration = RoutingConfiguration()
            }

            logInfo("Routing configuration reset to defaults successfully")
            true
        } catch (e: Exception) {
            logError("Exception resetting routing configuration", mapOf("error" to (e.message ?: "unknown")))
            false
        }
    }

    /**
     * NAME: updateFromFlutterSettings
     *
     * DESCRIPTION:
     *     Updates routing configuration from Flutter Method Channel arguments.
     *     Parses Flutter settings map and updates in-memory configuration.
     *
     * PARAMETERS:
     *     settings - Map containing routing settings from Flutter
     *
     * RETURNS:
     *     Boolean - true if update was successful, false otherwise
     */
    fun updateFromFlutterSettings(settings: Map<*, *>): Boolean {
        return try {
            logInfo("Updating routing configuration from Flutter settings", mapOf(
                "settings" to settings.toString()
            ))

            // Parse mode
            val modeString = settings["mode"] as? String ?: "all"
            val mode = when (modeString.lowercase()) {
                "custom" -> RoutingMode.CUSTOM
                else -> RoutingMode.ALL
            }

            // Parse custom routes
            val customRoutesString = settings["custom_routes"] as? String ?: ""
            val customRoutes = if (customRoutesString.isBlank()) {
                emptyList()
            } else {
                customRoutesString.split(ROUTE_SEPARATOR)
                    .map { it.trim() }
                    .filter { it.isNotBlank() }
            }

            val configuration = RoutingConfiguration(mode, customRoutes)
            return updateRoutingConfiguration(configuration)
        } catch (e: Exception) {
            logError("Exception updating routing configuration from Flutter", mapOf(
                "error" to (e.message ?: "unknown"),
                "settings" to settings.toString()
            ))
            false
        }
    }

    /**
     * NAME: toFlutterSettingsMap
     *
     * DESCRIPTION:
     *     Converts current routing configuration to Flutter-compatible map.
     *
     * RETURNS:
     *     Map<String, Any> - Flutter-compatible settings map
     */
    fun toFlutterSettingsMap(): Map<String, Any> {
        val configuration = getCurrentRoutingConfiguration()
        return mapOf(
            "mode" to configuration.mode.toConfigValue(),
            "custom_routes" to configuration.customRoutes.joinToString(ROUTE_SEPARATOR)
        )
    }
}
