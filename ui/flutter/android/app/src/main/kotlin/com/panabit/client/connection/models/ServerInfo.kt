/**
 * FILE: ServerInfo.kt
 *
 * DESCRIPTION:
 *     Server information data class for VPN connection management.
 *     Contains server details required for VPN connection establishment.
 *     Compatible with iOS ServerInfo and Go backend Server structures.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create, 11/07/2025 enhanced for server manager implementation
 */

package com.panabit.client.connection.models

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerialName

/**
 * NAME: ServerStatus
 *
 * DESCRIPTION:
 *     Server status enumeration for health monitoring.
 *     Compatible with iOS ServerStatus and Go backend status strings.
 */
@Serializable
enum class ServerStatus {
    Unknown,    // Initial state, not yet tested
    Online,     // Server is reachable and responding
    Offline     // Server is unreachable or not responding
}

/**
 * NAME: ServerSelectionMode
 *
 * DESCRIPTION:
 *     Server selection mode enumeration for auto/manual selection.
 *     Compatible with iOS SelectionMode and Go backend selection logic.
 */
@Serializable
enum class ServerSelectionMode {
    Auto,       // Automatic selection based on ping results
    Manual      // Manual selection by user preference
}

/**
 * NAME: PingResult
 *
 * DESCRIPTION:
 *     Ping test result data class containing latency and status information.
 *     Compatible with iOS PingResult and Go backend ping response.
 *
 * PROPERTIES:
 *     latency - Ping latency in milliseconds (0 if failed)
 *     isSuccess - Whether ping test was successful
 *     timestamp - Timestamp when ping was performed
 *     error - Error message if ping failed
 */
@Serializable
data class PingResult(
    val latency: Int,
    val isSuccess: Boolean,
    val timestamp: Long,
    val error: String? = null
)

/**
 * NAME: ServerConfiguration
 *
 * DESCRIPTION:
 *     Server manager configuration data class.
 *     Contains timeout values and operational parameters.
 *
 * PROPERTIES:
 *     pingTimeout - Timeout for individual ping operations (ms)
 *     pingInterval - Interval between ping operations (ms)
 *     updateInterval - Interval between server list updates (ms)
 *     maxConcurrentPings - Maximum concurrent ping operations
 *     maxRetries - Maximum retry attempts for failed operations
 */
@Serializable
data class ServerConfiguration(
    val pingTimeout: Long = 5000L,
    val pingInterval: Long = 15000L,  // Changed to 15s to match iOS/Go backend
    val updateInterval: Long = 3600000L,  // Changed to 1 hour to match iOS/Go backend
    val maxConcurrentPings: Int = 10,
    val maxRetries: Int = 3
) {
    companion object {
        val default = ServerConfiguration()
    }
}

/**
 * NAME: ServerInfo
 *
 * DESCRIPTION:
 *     Server information data class containing VPN server details.
 *     Used for server selection and connection establishment.
 *     Compatible with iOS ServerInfo and Go backend Server structures.
 *
 * PROPERTIES:
 *     id - Unique server identifier
 *     name - Server display name (Chinese)
 *     nameEn - Server display name (English)
 *     serverName - Server hostname or IP address
 *     serverPort - Server port number
 *     isAuto - Whether this is an auto-select server entry
 *     ping - Current ping latency in milliseconds
 *     status - Current server status (Online/Offline/Unknown)
 *     lastCheck - Timestamp of last ping check
 */
@Serializable
@Parcelize
data class ServerInfo(
    val id: String,
    val name: String,
    @SerialName("name_en") val nameEn: String,
    @SerialName("server_name") val serverName: String,
    @SerialName("server_port") val serverPort: Int,
    @SerialName("isauto") val isAuto: Boolean = false,
    var ping: Int = 0,
    var status: ServerStatus = ServerStatus.Unknown,
    @SerialName("last_check") var lastCheck: Long = System.currentTimeMillis()
) : Parcelable {

    /**
     * NAME: toMap
     *
     * DESCRIPTION:
     *     Converts server info to map for Flutter communication.
     *     Compatible with iOS/Windows Flutter bridge format.
     *
     * RETURNS:
     *     Map<String, Any> - Server information as map
     */
    fun toMap(): Map<String, Any> = mapOf(
        "id" to id,
        "name" to name,
        "name_en" to nameEn,  // Flutter UI expects snake_case
        "server_name" to serverName,  // Flutter UI expects snake_case
        "server_port" to serverPort,  // Flutter UI expects snake_case
        "isauto" to isAuto,  // Flutter UI expects this exact field name
        "ping" to ping,
        "status" to status.name.lowercase(),
        "isdefault" to false  // Flutter UI expects this field, default to false
    )

    /**
     * NAME: isValid
     *
     * DESCRIPTION:
     *     Validates server information for correctness.
     *     Ensures all required fields are properly set.
     *
     * RETURNS:
     *     Boolean - true if server info is valid, false otherwise
     */
    fun isValid(): Boolean {
        return id.isNotBlank() &&
               name.isNotBlank() &&
               nameEn.isNotBlank() &&
               serverName.isNotBlank() &&
               serverPort in 1..65535
    }

    /**
     * NAME: isOnline
     *
     * DESCRIPTION:
     *     Checks if server is currently online and available.
     *
     * RETURNS:
     *     Boolean - true if server is online, false otherwise
     */
    fun isOnline(): Boolean = status == ServerStatus.Online

    /**
     * NAME: displayName
     *
     * DESCRIPTION:
     *     Gets the display name for the server (Chinese name preferred).
     *
     * RETURNS:
     *     String - Display name for the server
     */
    val displayName: String
        get() = if (name.isNotBlank()) name else nameEn

    /**
     * NAME: hasValidPing
     *
     * DESCRIPTION:
     *     Checks if server has valid ping result (> 0).
     *
     * RETURNS:
     *     Boolean - true if ping is valid, false otherwise
     */
    fun hasValidPing(): Boolean = ping > 0

    /**
     * NAME: updatePingResult
     *
     * DESCRIPTION:
     *     Updates server ping result. UI determines reachability based on ping value only.
     *     ping > 0 = reachable, ping <= 0 = unreachable
     *
     * PARAMETERS:
     *     pingResult - Ping test result
     */
    fun updatePingResult(pingResult: PingResult) {
        // Only update ping value - UI uses ping > 0 to determine reachability
        this.ping = if (pingResult.isSuccess) pingResult.latency else 0
        this.lastCheck = pingResult.timestamp
        // Note: status field is not used for reachability determination in UI
    }

    companion object {
        /**
         * NAME: fromMap
         *
         * DESCRIPTION:
         *     Creates ServerInfo from map data (e.g., from Flutter or JSON).
         *
         * PARAMETERS:
         *     map - Map containing server information
         *
         * RETURNS:
         *     ServerInfo - Server information object
         */
        fun fromMap(map: Map<String, Any>): ServerInfo {
            return ServerInfo(
                id = map["id"] as? String ?: "",
                name = map["name"] as? String ?: "",
                nameEn = map["nameEn"] as? String ?: "",
                serverName = map["serverName"] as? String ?: "",
                serverPort = (map["serverPort"] as? Number)?.toInt() ?: 0,
                isAuto = map["isAuto"] as? Boolean ?: false,
                ping = (map["ping"] as? Number)?.toInt() ?: 0,
                status = when (map["status"] as? String) {
                    "online" -> ServerStatus.Online
                    "offline" -> ServerStatus.Offline
                    else -> ServerStatus.Unknown
                },
                lastCheck = (map["lastCheck"] as? Number)?.toLong() ?: System.currentTimeMillis()
            )
        }
    }
}
