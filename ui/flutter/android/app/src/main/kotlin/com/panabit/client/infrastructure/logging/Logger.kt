/**
 *
 * DESCRIPTION:
 *     Timber-based logging system for Panabit Client Android application.
 *     Provides simple, efficient logging with automatic source information capture.
 *     Follows Timber best practices for Android development.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.infrastructure.logging

import timber.log.Timber

/**
 * NAME: PanabitDebugTree
 *
 * DESCRIPTION:
 *     Custom Timber tree for debug builds with enhanced logging capabilities.
 *     Provides detailed logging with automatic tag generation and stack trace info.
 */
class PanabitDebugTree : Timber.DebugTree() {

    override fun createStackElementTag(element: StackTraceElement): String {
        return "PanabitClient-${element.className.substringAfterLast('.')}"
    }

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        val stackTrace = Throwable().stackTrace
        if (stackTrace.size > 5) {
            val element = stackTrace[5] // Skip Timber internal calls
            val enhancedMessage = "[${element.fileName}:${element.methodName}:${element.lineNumber}] $message"
            super.log(priority, tag, enhancedMessage, t)
        } else {
            super.log(priority, tag, message, t)
        }
    }
}

/**
 * NAME: PanabitReleaseTree
 *
 * DESCRIPTION:
 *     Custom Timber tree for release builds with minimal logging overhead.
 *     Only logs warnings and errors to reduce performance impact.
 */
class PanabitReleaseTree : Timber.Tree() {

    override fun isLoggable(tag: String?, priority: Int): Boolean {
        // 在release模式下也允许INFO级别日志，以便调试客户域修改问题
        return priority >= android.util.Log.INFO
    }

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        if (isLoggable(tag, priority)) {
            android.util.Log.println(priority, tag ?: "ITforceWAN", message)
            t?.printStackTrace()
        }
    }
}

/**
 * NAME: Global logging functions
 *
 * DESCRIPTION:
 *     Global logging functions that follow Timber best practices.
 *     Provides convenient logging with context support and automatic source capture.
 */

/**
 * Log debug message with optional context map
 */
inline fun logDebug(message: String, context: Map<String, Any> = emptyMap(), throwable: Throwable? = null) {
    val finalMessage = if (context.isNotEmpty()) {
        "$message [${context.entries.joinToString(", ") { "${it.key}=${it.value}" }}]"
    } else {
        message
    }

    if (throwable != null) {
        Timber.d(throwable, finalMessage)
    } else {
        Timber.d(finalMessage)
    }
}

/**
 * Log info message with optional context map
 */
inline fun logInfo(message: String, context: Map<String, Any> = emptyMap(), throwable: Throwable? = null) {
    val finalMessage = if (context.isNotEmpty()) {
        "$message [${context.entries.joinToString(", ") { "${it.key}=${it.value}" }}]"
    } else {
        message
    }

    if (throwable != null) {
        Timber.i(throwable, finalMessage)
    } else {
        Timber.i(finalMessage)
    }
}

/**
 * Log warning message with optional context map
 */
inline fun logWarn(message: String, context: Map<String, Any> = emptyMap(), throwable: Throwable? = null) {
    val finalMessage = if (context.isNotEmpty()) {
        "$message [${context.entries.joinToString(", ") { "${it.key}=${it.value}" }}]"
    } else {
        message
    }

    if (throwable != null) {
        Timber.w(throwable, finalMessage)
    } else {
        Timber.w(finalMessage)
    }
}

/**
 * Log error message with optional context map
 */
inline fun logError(message: String, context: Map<String, Any> = emptyMap(), throwable: Throwable? = null) {
    val finalMessage = if (context.isNotEmpty()) {
        "$message [${context.entries.joinToString(", ") { "${it.key}=${it.value}" }}]"
    } else {
        message
    }

    if (throwable != null) {
        Timber.e(throwable, finalMessage)
    } else {
        Timber.e(finalMessage)
    }
}




