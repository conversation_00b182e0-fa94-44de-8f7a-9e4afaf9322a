/**
 * FILE: PacketSignature.kt
 *
 * DESCRIPTION:
 *     Packet signature calculation and verification for SDWAN protocol.
 *     Implements MD5 signature algorithm compatible with Go backend.
 *     All non-data packets require signature verification.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.protocol

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import com.panabit.client.protocol.models.PacketHeader
import com.panabit.client.protocol.models.PacketType
import java.security.MessageDigest

/**
 * NAME: PacketSignature
 *
 * DESCRIPTION:
 *     Utility class for packet signature operations.
 *     Provides MD5 signature calculation and verification.
 *     Compatible with Go backend signature algorithm.
 */
object PacketSignature {
    private const val SIGNATURE_SALT = "mw"
    private const val SIGNATURE_SIZE = 16

    /**
     * NAME: calculateSignature
     *
     * DESCRIPTION:
     *     Calculates MD5 signature for packet header.
     *     Algorithm: MD5(header + "mw")
     *     Compatible with Go backend signature calculation.
     *
     * PARAMETERS:
     *     header - Packet header to sign
     *
     * RETURNS:
     *     ByteArray - 16-byte MD5 signature
     */
    fun calculateSignature(header: PacketHeader): ByteArray {
        val headerBytes = header.toByteArray()
        val saltBytes = SIGNATURE_SALT.toByteArray(Charsets.US_ASCII)
        val signatureData = headerBytes + saltBytes

        val md5 = MessageDigest.getInstance("MD5")
        return md5.digest(signatureData)
    }

    /**
     * NAME: verifySignature
     *
     * DESCRIPTION:
     *     Verifies packet signature against expected value.
     *     Calculates expected signature and compares with provided signature.
     *
     * PARAMETERS:
     *     header - Packet header to verify
     *     signature - Provided signature to verify against
     *
     * RETURNS:
     *     Boolean - true if signature is valid, false otherwise
     */
    fun verifySignature(header: PacketHeader, signature: ByteArray): Boolean {
        if (signature.size != SIGNATURE_SIZE) return false

        val expectedSignature = calculateSignature(header)
        return signature.contentEquals(expectedSignature)
    }

    /**
     * NAME: requiresSignature
     *
     * DESCRIPTION:
     *     Checks if packet type requires signature verification.
     *     Data packets do not require signatures, all other packets do.
     *
     * PARAMETERS:
     *     packetType - Packet type to check
     *
     * RETURNS:
     *     Boolean - true if packet requires signature, false otherwise
     */
    fun requiresSignature(packetType: PacketType): Boolean {
        return !packetType.isDataPacket
    }

    /**
     * NAME: buildSignedPacket
     *
     * DESCRIPTION:
     *     Builds complete packet with header, signature, and payload.
     *     Only adds signature for non-data packets.
     *
     * PARAMETERS:
     *     header - Packet header
     *     payload - Packet payload data
     *
     * RETURNS:
     *     ByteArray - Complete packet with signature if required
     */
    fun buildSignedPacket(header: PacketHeader, payload: ByteArray): ByteArray {
        val headerBytes = header.toByteArray()
        
        return if (requiresSignature(header.type)) {
            // Non-data packet: Header + Signature + Payload
            val signature = calculateSignature(header)
            headerBytes + signature + payload
        } else {
            // Data packet: Header + Payload (no signature)
            headerBytes + payload
        }
    }

    /**
     * NAME: extractSignature
     *
     * DESCRIPTION:
     *     Extracts signature from packet data.
     *     Returns signature and remaining payload data.
     *
     * PARAMETERS:
     *     packetData - Complete packet data
     *     header - Parsed packet header
     *
     * RETURNS:
     *     Pair<ByteArray?, ByteArray> - Signature (null for data packets) and payload
     *
     * THROWS:
     *     IllegalArgumentException - If packet data is too short for signature
     */
    fun extractSignature(packetData: ByteArray, header: PacketHeader): Pair<ByteArray?, ByteArray> {
        val headerSize = PacketHeader.HEADER_SIZE
        
        if (!requiresSignature(header.type)) {
            // Data packet: no signature, payload starts after header
            val payload = packetData.copyOfRange(headerSize, packetData.size)
            return Pair(null, payload)
        }

        // Non-data packet: signature follows header
        require(packetData.size >= headerSize + SIGNATURE_SIZE) {
            "Packet data too short for signature (need ${headerSize + SIGNATURE_SIZE} bytes, got ${packetData.size})"
        }

        val signature = packetData.copyOfRange(headerSize, headerSize + SIGNATURE_SIZE)
        val payload = packetData.copyOfRange(headerSize + SIGNATURE_SIZE, packetData.size)
        
        return Pair(signature, payload)
    }

    /**
     * NAME: validatePacketSignature
     *
     * DESCRIPTION:
     *     Validates complete packet signature.
     *     Extracts signature from packet and verifies it.
     *
     * PARAMETERS:
     *     packetData - Complete packet data
     *     header - Parsed packet header
     *
     * RETURNS:
     *     Boolean - true if signature is valid or not required, false if invalid
     */
    fun validatePacketSignature(packetData: ByteArray, header: PacketHeader): Boolean {
        if (!requiresSignature(header.type)) {
            // Data packets don't require signature validation
            return true
        }

        return try {
            val (signature, _) = extractSignature(packetData, header)
            signature?.let { verifySignature(header, it) } ?: false
        } catch (e: Exception) {
            false
        }
    }

    /**
     * NAME: createTestSignature
     *
     * DESCRIPTION:
     *     Creates test signature for validation purposes.
     *     Used in unit tests to verify signature algorithm.
     *
     * PARAMETERS:
     *     testData - Test data to sign
     *
     * RETURNS:
     *     ByteArray - MD5 signature of test data + "mw"
     */
    fun createTestSignature(testData: ByteArray): ByteArray {
        val saltBytes = SIGNATURE_SALT.toByteArray(Charsets.US_ASCII)
        val signatureData = testData + saltBytes

        val md5 = MessageDigest.getInstance("MD5")
        return md5.digest(signatureData)
    }

    /**
     * NAME: getSignatureSize
     *
     * DESCRIPTION:
     *     Returns the size of packet signatures.
     *
     * RETURNS:
     *     Int - Signature size in bytes (always 16 for MD5)
     */
    fun getSignatureSize(): Int = SIGNATURE_SIZE
}
