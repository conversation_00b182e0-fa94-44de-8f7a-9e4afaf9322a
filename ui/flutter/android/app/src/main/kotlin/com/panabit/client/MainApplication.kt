/**
 * FILE: MainApplication.kt
 *
 * DESCRIPTION:
 *     ITforce WAN Android应用程序类，负责应用程序初始化和依赖注入配置。
 *     初始化Koin依赖注入框架和Flutter引擎缓存。
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.app.Activity
import android.app.Application
import android.os.Bundle
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.embedding.engine.dart.DartExecutor
import org.koin.android.ext.koin.androidContext
import org.koin.core.context.startKoin
import timber.log.Timber
import com.panabit.client.infrastructure.logging.LoggingManager
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.di.appModule
import com.panabit.client.di.vpnModule
import com.panabit.client.di.networkModule
import com.panabit.client.di.platformModule

/**
 * NAME: MainApplication
 *
 * DESCRIPTION:
 *     ITforce WAN Android应用程序类，负责应用程序初始化和依赖注入配置。
 *     初始化Koin依赖注入框架、Timber日志系统和Flutter引擎缓存。
 *
 * PROPERTIES:
 *     FLUTTER_ENGINE_ID - Flutter引擎缓存标识符
 */
class MainApplication : Application() {

    companion object {
        const val FLUTTER_ENGINE_ID = "itforce_wan_engine"
    }

    private var activityCount = 0

    override fun onCreate() {
        super.onCreate()

        // 直接使用Android Log确保能看到初始化过程
        android.util.Log.i("ITforceWAN-App", "MainApplication onCreate() started")

        // 初始化日志系统
        android.util.Log.i("ITforceWAN-App", "Initializing logging system...")
        initializeLogging()

        // 初始化依赖注入
        android.util.Log.i("ITforceWAN-App", "Initializing dependency injection...")
        initializeDependencyInjection()

        // 初始化Flutter引擎
        android.util.Log.i("ITforceWAN-App", "Initializing Flutter engine...")
        initializeFlutterEngine()

        // 注册Activity生命周期回调以便在应用真正退出时清理资源
        android.util.Log.i("ITforceWAN-App", "Registering activity lifecycle callbacks...")
        registerActivityLifecycleCallbacks(activityLifecycleCallbacks)

        android.util.Log.i("ITforceWAN-App", "MainApplication onCreate() completed")
    }

    override fun onTerminate() {
        super.onTerminate()

        // 注意：onTerminate()在正常情况下不会被调用，仅在模拟器中调用
        // 清理日志系统资源
        cleanupLogging()
    }

    override fun onLowMemory() {
        super.onLowMemory()

        // 在内存不足时也尝试清理资源
        this.logWarn("Low memory detected, cleaning up logging resources")
        cleanupLogging()
    }

    /**
     * NAME: initializeLogging
     *
     * DESCRIPTION:
     *     Initialize ITforce WAN logging system with LoggingManager.
     *     Configures both console and file logging with proper setup.
     */
    private fun initializeLogging() {
        android.util.Log.i("ITforceWAN-App", "initializeLogging() called, BuildConfig.DEBUG = ${BuildConfig.DEBUG}")

        // Initialize LoggingManager with file logging enabled
        LoggingManager.initialize(
            context = this,
            isDebug = BuildConfig.DEBUG,
            enableFileLogging = true
        )

        android.util.Log.i("ITforceWAN-App", "LoggingManager initialized")

        // Log application initialization
        this.logInfo("ITforce WAN Application initialized with enhanced logging")

        android.util.Log.i("ITforceWAN-App", "initializeLogging() completed")
    }

    /**
     * NAME: initializeDependencyInjection
     *
     * DESCRIPTION:
     *     Initialize Koin dependency injection framework with simplified architecture modules.
     *     Configures core VPN components following simplified architecture design.
     */
    private fun initializeDependencyInjection() {
        startKoin {
            androidContext(this@MainApplication)
            modules(
                appModule,
                vpnModule,
                networkModule,
                platformModule
            )
        }
        this.logInfo("Koin dependency injection initialized with simplified architecture modules")
    }

    /**
     * NAME: initializeFlutterEngine
     *
     * DESCRIPTION:
     *     Initialize Flutter engine and cache for improved startup performance.
     *     Sets up Dart executor and caches engine for reuse.
     */
    private fun initializeFlutterEngine() {
        val flutterEngine = FlutterEngine(this)

        // Start Dart executor
        flutterEngine.dartExecutor.executeDartEntrypoint(
            DartExecutor.DartEntrypoint.createDefault()
        )

        // Cache Flutter engine
        FlutterEngineCache
            .getInstance()
            .put(FLUTTER_ENGINE_ID, flutterEngine)

        this.logInfo("Flutter engine initialized and cached")
    }

    /**
     * NAME: cleanupLogging
     *
     * DESCRIPTION:
     *     Clean up logging system resources to prevent memory leaks.
     *     Shuts down LoggingManager and cancels all background tasks.
     */
    private fun cleanupLogging() {
        try {
            this.logInfo("Cleaning up logging system resources")
            LoggingManager.cleanup()
        } catch (e: Exception) {
            // Use Android Log directly since logging system might be shutting down
            android.util.Log.w("ITforceWAN", "Error during logging cleanup", e)
        }
    }

    /**
     * NAME: activityLifecycleCallbacks
     *
     * DESCRIPTION:
     *     Activity lifecycle callbacks to track when the app is truly exiting.
     *     Cleans up logging resources when all activities are destroyed.
     */
    private val activityLifecycleCallbacks = object : ActivityLifecycleCallbacks {
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            activityCount++
        }

        override fun onActivityStarted(activity: Activity) {}

        override fun onActivityResumed(activity: Activity) {}

        override fun onActivityPaused(activity: Activity) {}

        override fun onActivityStopped(activity: Activity) {}

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}

        override fun onActivityDestroyed(activity: Activity) {
            activityCount--
            if (activityCount <= 0) {
                // All activities destroyed, app is likely exiting
                <EMAIL>("All activities destroyed, cleaning up logging resources")
                cleanupLogging()
            }
        }
    }
}
