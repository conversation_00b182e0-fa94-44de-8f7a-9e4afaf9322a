/**
 * FILE: ErrorHandler.kt
 *
 * DESCRIPTION:
 *     Unified error handling system for VPN service operations. Provides centralized
 *     error processing, recovery strategies, retry mechanisms, and user notifications.
 *     Integrates with Android system features including notifications, intents, and
 *     background task scheduling for comprehensive error management and recovery.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.infrastructure.error

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Looper
import androidx.core.app.NotificationCompat
import kotlinx.coroutines.*
import android.R as AndroidR
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * NAME: ErrorHandler
 *
 * DESCRIPTION:
 *     Centralized error handling system for VPN service operations. Manages error
 *     processing, recovery strategies, retry mechanisms, and user notifications.
 *     Provides intelligent error recovery based on error types and system state.
 *     Integrates with logging system for comprehensive error tracking and debugging.
 *
 * PROPERTIES:
 *     context - Android application context for system integration
 *     logger - Logging interface for error tracking and debugging
 *     notificationManager - Android notification manager for user alerts
 *     errorScope - Coroutine scope for asynchronous error handling operations
 *     retryHandler - Handler for scheduling retry operations on main thread
 */
class ErrorHandler(
    private val context: Context
) {
    private val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    private val errorScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val retryHandler = Handler(Looper.getMainLooper())

    // Error handling configuration
    private val maxRetryAttempts = 3
    private val notificationChannelId = "vpn_error_channel"
    private val errorNotificationId = 2001

    init {
        createNotificationChannel()
    }

    /**
     * NAME: handleVPNError
     *
     * DESCRIPTION:
     *     Main entry point for VPN error handling. Processes the error, determines
     *     appropriate recovery strategy, and executes recovery actions including
     *     retry mechanisms, user notifications, and system-level interventions.
     *
     * PARAMETERS:
     *     error - VPN service error to be handled
     *     onRetry - Optional callback for retry operations
     *     context - Additional context information for error handling
     *
     * RETURNS:
     *     Unit - Asynchronous operation, results communicated through callbacks
     */
    fun handleVPNError(
        error: VPNServiceError,
        onRetry: (() -> Unit)? = null,
        context: Map<String, Any> = emptyMap()
    ) {
        logError("VPN Error occurred", mapOf(
            "error_type" to error::class.java.simpleName,
            "error_message" to (error.message ?: "unknown"),
            "is_recoverable" to error.isRecoverable(),
            "context" to context
        ))

        when (error) {
            is VPNServiceError.NetworkUnavailable -> {
                handleNetworkError(error, onRetry)
            }
            is VPNServiceError.ReconnectionFailed -> {
                handleReconnectionError(error, onRetry)
            }
            is VPNServiceError.PermissionDenied -> {
                handlePermissionError(error)
            }
            is VPNServiceError.AuthenticationFailed -> {
                handleAuthenticationError(error, onRetry)
            }
            is VPNServiceError.ConnectionFailed -> {
                handleConnectionError(error, onRetry)
            }
            is VPNServiceError.ServerUnavailable -> {
                handleServerError(error, onRetry)
            }
            is VPNServiceError.InterfaceError -> {
                handleInterfaceError(error, onRetry)
            }
            is VPNServiceError.ProtocolError -> {
                handleProtocolError(error, onRetry)
            }
            is VPNServiceError.TimeoutError -> {
                handleTimeoutError(error, onRetry)
            }
            is VPNServiceError.ConfigurationInvalid -> {
                handleConfigurationError(error)
            }
        }

        // Always show user notification for errors
        showErrorNotification(error)
    }

    /**
     * NAME: handleNetworkError
     *
     * DESCRIPTION:
     *     Handles network unavailability errors by scheduling network monitoring
     *     and automatic retry when network becomes available.
     *
     * PARAMETERS:
     *     error - Network unavailability error
     *     onRetry - Retry callback function
     */
    private fun handleNetworkError(error: VPNServiceError.NetworkUnavailable, onRetry: (() -> Unit)?) {
        if (error.isTemporary && onRetry != null) {
            scheduleNetworkRetry(error, onRetry)
        } else {
            logWarn("Network permanently unavailable", mapOf(
                "network_type" to (error.networkType ?: "unknown"),
                "is_temporary" to error.isTemporary
            ))
        }
    }

    /**
     * NAME: handleReconnectionError
     *
     * DESCRIPTION:
     *     Handles reconnection failures by attempting server switching or
     *     scheduling delayed retry with exponential backoff.
     *
     * PARAMETERS:
     *     error - Reconnection failure error
     *     onRetry - Retry callback function
     */
    private fun handleReconnectionError(error: VPNServiceError.ReconnectionFailed, onRetry: (() -> Unit)?) {
        if (error.attemptCount < maxRetryAttempts && onRetry != null) {
            scheduleDelayedRetry(error, onRetry)
        } else {
            logError("Reconnection failed after maximum attempts", mapOf(
                "attempt_count" to error.attemptCount,
                "last_error" to (error.lastError ?: "unknown")
            ))
            // Try server switching as last resort
            scheduleServerSwitch(onRetry)
        }
    }

    /**
     * NAME: handlePermissionError
     *
     * DESCRIPTION:
     *     Handles permission denial errors by guiding user to appropriate
     *     system settings or permission request dialogs.
     *
     * PARAMETERS:
     *     error - Permission denial error
     */
    private fun handlePermissionError(error: VPNServiceError.PermissionDenied) {
        when (error.permissionType) {
            "VPN_PERMISSION" -> showVPNPermissionDialog()
            "BATTERY_OPTIMIZATION" -> showBatteryOptimizationDialog()
            else -> showGenericPermissionDialog(error.permissionType)
        }
    }

    /**
     * NAME: handleAuthenticationError
     *
     * DESCRIPTION:
     *     Handles authentication failures with appropriate retry strategies
     *     based on whether the issue is credential-related or server-related.
     *
     * PARAMETERS:
     *     error - Authentication failure error
     *     onRetry - Retry callback function
     */
    private fun handleAuthenticationError(error: VPNServiceError.AuthenticationFailed, onRetry: (() -> Unit)?) {
        if (!error.isCredentialIssue && onRetry != null) {
            // Server-side auth issue, retry after delay
            scheduleDelayedRetry(error, onRetry)
        } else {
            // Credential issue, require user intervention
            logWarn("Authentication failed due to credential issue", mapOf(
                "error_code" to (error.serverErrorCode ?: "unknown"),
                "vpn_error_code" to error.errorCode.code,
                "error_category" to error.errorCode.category
            ))
        }
    }

    /**
     * NAME: handleConnectionError
     *
     * DESCRIPTION:
     *     Handles connection failures with retry logic and server switching
     *     when maximum retry attempts are reached.
     *
     * PARAMETERS:
     *     error - Connection failure error
     *     onRetry - Retry callback function
     */
    private fun handleConnectionError(error: VPNServiceError.ConnectionFailed, onRetry: (() -> Unit)?) {
        if (error.retryCount < maxRetryAttempts && onRetry != null) {
            scheduleDelayedRetry(error, onRetry)
        } else {
            logError("Connection failed after maximum retries", mapOf(
                "server_address" to (error.serverAddress ?: "unknown"),
                "retry_count" to error.retryCount
            ))
            scheduleServerSwitch(onRetry)
        }
    }

    /**
     * NAME: handleServerError
     *
     * DESCRIPTION:
     *     Handles server unavailability by switching to alternative servers
     *     or scheduling retry when no alternatives are available.
     *
     * PARAMETERS:
     *     error - Server unavailability error
     *     onRetry - Retry callback function
     */
    private fun handleServerError(error: VPNServiceError.ServerUnavailable, onRetry: (() -> Unit)?) {
        if (error.alternativeAvailable && onRetry != null) {
            // Switch to alternative server immediately
            scheduleServerSwitch(onRetry)
        } else {
            // No alternatives, schedule delayed retry
            scheduleDelayedRetry(error, onRetry)
        }
    }

    /**
     * NAME: handleInterfaceError
     *
     * DESCRIPTION:
     *     Handles VPN interface errors by scheduling interface recreation
     *     and connection retry.
     *
     * PARAMETERS:
     *     error - Interface error
     *     onRetry - Retry callback function
     */
    private fun handleInterfaceError(error: VPNServiceError.InterfaceError, onRetry: (() -> Unit)?) {
        logWarn("VPN interface error", mapOf(
            "interface_type" to error.interfaceType,
            "system_error" to (error.systemError ?: "unknown")
        ))
        
        if (onRetry != null) {
            scheduleDelayedRetry(error, onRetry)
        }
    }

    /**
     * NAME: handleProtocolError
     *
     * DESCRIPTION:
     *     Handles protocol communication errors by scheduling retry
     *     with appropriate delay.
     *
     * PARAMETERS:
     *     error - Protocol error
     *     onRetry - Retry callback function
     */
    private fun handleProtocolError(error: VPNServiceError.ProtocolError, onRetry: (() -> Unit)?) {
        logWarn("Protocol communication error", mapOf(
            "protocol_version" to (error.protocolVersion ?: "unknown"),
            "packet_type" to (error.packetType ?: "unknown")
        ))
        
        if (onRetry != null) {
            scheduleDelayedRetry(error, onRetry)
        }
    }

    /**
     * NAME: handleTimeoutError
     *
     * DESCRIPTION:
     *     Handles operation timeout errors by scheduling retry with
     *     increased timeout duration.
     *
     * PARAMETERS:
     *     error - Timeout error
     *     onRetry - Retry callback function
     */
    private fun handleTimeoutError(error: VPNServiceError.TimeoutError, onRetry: (() -> Unit)?) {
        logWarn("Operation timeout", mapOf(
            "operation" to error.operation,
            "timeout_duration" to error.timeoutDuration
        ))
        
        if (onRetry != null) {
            scheduleDelayedRetry(error, onRetry)
        }
    }

    /**
     * NAME: handleConfigurationError
     *
     * DESCRIPTION:
     *     Handles configuration errors by logging detailed information
     *     for debugging purposes. No automatic retry for config errors.
     *
     * PARAMETERS:
     *     error - Configuration error
     */
    private fun handleConfigurationError(error: VPNServiceError.ConfigurationInvalid) {
        logError("Invalid configuration", mapOf(
            "config_field" to (error.configField ?: "unknown"),
            "expected_format" to (error.expectedFormat ?: "unknown")
        ))
    }

    /**
     * NAME: scheduleNetworkRetry
     *
     * DESCRIPTION:
     *     Schedules network availability monitoring and retry when network
     *     becomes available again.
     *
     * PARAMETERS:
     *     error - Network error information
     *     onRetry - Retry callback function
     */
    private fun scheduleNetworkRetry(error: VPNServiceError.NetworkUnavailable, onRetry: () -> Unit) {
        val retryDelay = error.getRetryDelay()
        
        logInfo("Scheduling network retry", mapOf(
            "retry_delay" to retryDelay,
            "network_type" to (error.networkType ?: "unknown")
        ))
        
        retryHandler.postDelayed({
            logInfo("Executing network retry")
            onRetry()
        }, retryDelay)
    }

    /**
     * NAME: scheduleDelayedRetry
     *
     * DESCRIPTION:
     *     Schedules delayed retry operation with appropriate backoff delay
     *     based on error type and retry history.
     *
     * PARAMETERS:
     *     error - Error information for retry calculation
     *     onRetry - Retry callback function
     */
    private fun scheduleDelayedRetry(error: VPNServiceError, onRetry: (() -> Unit)?) {
        if (onRetry == null) return
        
        val retryDelay = error.getRetryDelay()
        
        logInfo("Scheduling delayed retry", mapOf(
            "error_type" to error::class.java.simpleName,
            "retry_delay" to retryDelay
        ))
        
        retryHandler.postDelayed({
            logInfo("Executing delayed retry")
            onRetry()
        }, retryDelay)
    }

    /**
     * NAME: scheduleServerSwitch
     *
     * DESCRIPTION:
     *     Schedules server switching operation to attempt connection
     *     with alternative servers.
     *
     * PARAMETERS:
     *     onRetry - Retry callback function
     */
    private fun scheduleServerSwitch(onRetry: (() -> Unit)?) {
        if (onRetry == null) return

        logInfo("Scheduling server switch")

        errorScope.launch {
            try {
                // Simulate server selection logic
                delay(1000) // Brief delay before server switch

                retryHandler.post {
                    logInfo("Executing server switch retry")
                    onRetry()
                }
            } catch (e: Exception) {
                logError("Server switch failed", mapOf(
                    "error" to (e.message ?: "unknown")
                ))
            }
        }
    }

    /**
     * NAME: showErrorNotification
     *
     * DESCRIPTION:
     *     Displays user notification for VPN errors with appropriate
     *     error message and action buttons for user intervention.
     *
     * PARAMETERS:
     *     error - VPN service error to display
     */
    private fun showErrorNotification(error: VPNServiceError) {
        val notification = NotificationCompat.Builder(context, notificationChannelId)
            .setContentTitle("Panabit Client - 连接错误")
            .setContentText(error.getUserFriendlyMessage())
            .setSmallIcon(AndroidR.drawable.ic_dialog_alert)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText(error.getUserFriendlyMessage()))
            .build()

        notificationManager.notify(errorNotificationId, notification)

        logInfo("Error notification displayed", mapOf(
            "error_type" to error::class.java.simpleName,
            "user_message" to error.getUserFriendlyMessage()
        ))
    }

    /**
     * NAME: showVPNPermissionDialog
     *
     * DESCRIPTION:
     *     Shows system dialog to guide user to VPN permission settings.
     *     Creates intent to launch VPN permission request activity.
     */
    private fun showVPNPermissionDialog() {
        try {
            val intent = Intent("com.panabit.client.PERMISSION_REQUEST").apply {
                putExtra("permission_type", "VPN_PERMISSION")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)

            logInfo("VPN permission dialog launched")
        } catch (e: Exception) {
            logError("Failed to show VPN permission dialog", mapOf(
                "error" to (e.message ?: "unknown")
            ))
        }
    }

    /**
     * NAME: showBatteryOptimizationDialog
     *
     * DESCRIPTION:
     *     Shows dialog to guide user to battery optimization settings
     *     for ensuring VPN service stability.
     */
    private fun showBatteryOptimizationDialog() {
        try {
            val intent = Intent("com.panabit.client.BATTERY_OPTIMIZATION").apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)

            logInfo("Battery optimization dialog launched")
        } catch (e: Exception) {
            logError("Failed to show battery optimization dialog", mapOf(
                "error" to (e.message ?: "unknown")
            ))
        }
    }

    /**
     * NAME: showGenericPermissionDialog
     *
     * DESCRIPTION:
     *     Shows generic permission dialog for other permission types.
     *
     * PARAMETERS:
     *     permissionType - Type of permission that was denied
     */
    private fun showGenericPermissionDialog(permissionType: String) {
        try {
            val intent = Intent("com.panabit.client.PERMISSION_REQUEST").apply {
                putExtra("permission_type", permissionType)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)

            logInfo("Generic permission dialog launched", mapOf(
                "permission_type" to permissionType
            ))
        } catch (e: Exception) {
            logError("Failed to show permission dialog", mapOf(
                "permission_type" to permissionType,
                "error" to (e.message ?: "unknown")
            ))
        }
    }

    /**
     * NAME: createNotificationChannel
     *
     * DESCRIPTION:
     *     Creates notification channel for VPN error notifications on Android 8.0+.
     *     Required for displaying notifications on modern Android versions.
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                notificationChannelId,
                "VPN错误通知",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "VPN连接错误和状态通知"
                enableVibration(true)
                enableLights(true)
            }

            notificationManager.createNotificationChannel(channel)

            logInfo("Error notification channel created")
        }
    }

    /**
     * NAME: clearErrorNotifications
     *
     * DESCRIPTION:
     *     Clears all error notifications when VPN connection is restored
     *     or when user manually dismisses error state.
     */
    fun clearErrorNotifications() {
        notificationManager.cancel(errorNotificationId)
        logInfo("Error notifications cleared")
    }

    /**
     * NAME: dispose
     *
     * DESCRIPTION:
     *     Cleans up error handler resources including coroutine scope
     *     and pending retry operations.
     */
    fun dispose() {
        retryHandler.removeCallbacksAndMessages(null)
        errorScope.cancel()
        clearErrorNotifications()

        logInfo("ErrorHandler disposed")
    }
}
