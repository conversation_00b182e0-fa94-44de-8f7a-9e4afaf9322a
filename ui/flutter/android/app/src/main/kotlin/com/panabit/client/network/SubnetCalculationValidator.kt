/**
 * FILE: SubnetCalculationValidator.kt
 *
 * DESCRIPTION:
 *     Validator for subnet calculation functionality to ensure correctness.
 *     Provides test cases and validation methods for network address computation.
 *
 * AUTHOR: wei
 * HISTORY: 24/07/2025 create
 */

package com.panabit.client.network

import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logError
import java.net.Inet4Address
import java.net.InetAddress

/**
 * NAME: SubnetCalculationValidator
 *
 * DESCRIPTION:
 *     Provides validation methods for subnet calculation functionality.
 *     Tests various network scenarios to ensure mathematical correctness.
 */
object SubnetCalculationValidator {

    /**
     * Test cases for subnet calculation validation
     */
    private val testCases = listOf(
        // WiFi networks
        Triple("*************", 24, "***********"),
        Triple("************", 24, "***********"),
        Triple("**********", 24, "10.0.0.0"),
        
        // Enterprise networks
        Triple("************", 22, "**********"),
        Triple("***********", 22, "**********"),
        Triple("********", 16, "********"),
        
        // Mobile data networks
        Triple("**********", 16, "10.0.0.0"),
        Triple("***********", 10, "**********"),
        
        // Edge cases
        Triple("***********", 32, "***********"),
        Triple("********", 8, "10.0.0.0"),
        Triple("**********", 12, "**********")
    )

    /**
     * NAME: validateSubnetCalculation
     *
     * DESCRIPTION:
     *     Validates subnet calculation using reflection to access private method.
     *     Tests the calculateNetworkAddress method with known test cases.
     *
     * RETURNS:
     *     Boolean - true if all test cases pass
     */
    fun validateSubnetCalculation(): Boolean {
        logInfo("Starting subnet calculation validation")
        
        var passedTests = 0
        var totalTests = testCases.size

        for ((deviceIP, prefixLength, expectedNetwork) in testCases) {
            try {
                val ipAddress = InetAddress.getByName(deviceIP) as Inet4Address
                val calculatedNetwork = calculateNetworkAddressTest(ipAddress, prefixLength)
                
                if (calculatedNetwork == expectedNetwork) {
                    logInfo("✓ Test passed", mapOf(
                        "device_ip" to deviceIP,
                        "prefix_length" to prefixLength,
                        "expected" to expectedNetwork,
                        "calculated" to calculatedNetwork
                    ))
                    passedTests++
                } else {
                    logError("✗ Test failed", mapOf(
                        "device_ip" to deviceIP,
                        "prefix_length" to prefixLength,
                        "expected" to expectedNetwork,
                        "calculated" to calculatedNetwork
                    ))
                }
            } catch (e: Exception) {
                logError("Test exception", mapOf(
                    "device_ip" to deviceIP,
                    "error" to (e.message ?: "unknown")
                ), e)
            }
        }

        val allPassed = passedTests == totalTests
        logInfo("Subnet calculation validation completed", mapOf(
            "passed_tests" to passedTests,
            "total_tests" to totalTests,
            "success_rate" to "${(passedTests * 100) / totalTests}%",
            "overall_result" to if (allPassed) "PASS" else "FAIL"
        ))

        return allPassed
    }

    /**
     * NAME: calculateNetworkAddressTest
     *
     * DESCRIPTION:
     *     Test implementation of network address calculation.
     *     Duplicates the logic from NetworkUtils for validation.
     *
     * PARAMETERS:
     *     ipAddress - IP address to calculate network for
     *     prefixLength - CIDR prefix length
     *
     * RETURNS:
     *     String - Calculated network address
     */
    private fun calculateNetworkAddressTest(ipAddress: Inet4Address, prefixLength: Int): String {
        // Get IP address as 32-bit integer
        val ipBytes = ipAddress.address
        var ipInt = 0
        for (i in ipBytes.indices) {
            ipInt = (ipInt shl 8) or (ipBytes[i].toInt() and 0xFF)
        }

        // Create subnet mask from prefix length
        val mask = if (prefixLength == 0) 0 else (-1 shl (32 - prefixLength))

        // Apply mask to get network address
        val networkInt = ipInt and mask

        // Convert back to dotted decimal notation
        return String.format(
            "%d.%d.%d.%d",
            (networkInt ushr 24) and 0xFF,
            (networkInt ushr 16) and 0xFF,
            (networkInt ushr 8) and 0xFF,
            networkInt and 0xFF
        )
    }

    /**
     * NAME: validateIPInSubnet
     *
     * DESCRIPTION:
     *     Validates that a device IP falls within the calculated subnet.
     *
     * PARAMETERS:
     *     deviceIP - Device IP address string
     *     networkAddress - Network address string
     *     prefixLength - CIDR prefix length
     *
     * RETURNS:
     *     Boolean - true if device IP is in the subnet
     */
    fun validateIPInSubnet(deviceIP: String, networkAddress: String, prefixLength: Int): Boolean {
        return try {
            val deviceIPParts = deviceIP.split(".").map { it.toInt() }
            val networkIPParts = networkAddress.split(".").map { it.toInt() }

            if (deviceIPParts.size != 4 || networkIPParts.size != 4) {
                return false
            }

            // Convert to 32-bit integers
            var deviceIPInt = 0
            var networkIPInt = 0
            
            for (i in 0..3) {
                deviceIPInt = (deviceIPInt shl 8) or deviceIPParts[i]
                networkIPInt = (networkIPInt shl 8) or networkIPParts[i]
            }

            // Create subnet mask and check if device IP is in network
            val mask = if (prefixLength == 0) 0 else (-1 shl (32 - prefixLength))
            val deviceNetworkInt = deviceIPInt and mask

            deviceNetworkInt == networkIPInt

        } catch (e: Exception) {
            false
        }
    }

    /**
     * NAME: runComprehensiveValidation
     *
     * DESCRIPTION:
     *     Runs comprehensive validation including subnet calculation and IP membership tests.
     *
     * RETURNS:
     *     Boolean - true if all validations pass
     */
    fun runComprehensiveValidation(): Boolean {
        logInfo("=== Starting Comprehensive Subnet Validation ===")

        // Test 1: Subnet calculation
        val calculationTest = validateSubnetCalculation()
        
        // Test 2: IP membership validation
        var membershipTests = 0
        var membershipPassed = 0
        
        for ((deviceIP, prefixLength, expectedNetwork) in testCases) {
            membershipTests++
            if (validateIPInSubnet(deviceIP, expectedNetwork, prefixLength)) {
                membershipPassed++
            } else {
                logError("IP membership test failed", mapOf(
                    "device_ip" to deviceIP,
                    "network" to expectedNetwork,
                    "prefix_length" to prefixLength
                ))
            }
        }

        val membershipTest = membershipPassed == membershipTests
        
        logInfo("IP membership validation", mapOf(
            "passed" to membershipPassed,
            "total" to membershipTests,
            "result" to if (membershipTest) "PASS" else "FAIL"
        ))

        val overallResult = calculationTest && membershipTest
        
        logInfo("=== Comprehensive Validation Result ===", mapOf(
            "subnet_calculation" to if (calculationTest) "PASS" else "FAIL",
            "ip_membership" to if (membershipTest) "PASS" else "FAIL",
            "overall_result" to if (overallResult) "PASS" else "FAIL"
        ))

        return overallResult
    }
}
