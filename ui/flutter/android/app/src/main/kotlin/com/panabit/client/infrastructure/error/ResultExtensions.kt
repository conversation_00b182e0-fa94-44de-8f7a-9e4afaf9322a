/**
 * FILE: ResultExtensions.kt
 *
 * DESCRIPTION:
 *     Kotlin Result type extensions for enhanced error handling in VPN service operations.
 *     Provides convenient methods for asynchronous error handling, chain operations,
 *     error transformation, and integration with coroutines. Simplifies error handling
 *     patterns throughout the VPN service implementation while maintaining type safety.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.infrastructure.error

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import kotlin.coroutines.CoroutineContext

/**
 * NAME: mapError
 *
 * DESCRIPTION:
 *     Transforms the error type of a failed Result while preserving success values.
 *     Useful for converting generic exceptions to specific VPN service errors.
 *
 * RECEIVER:
 *     Result<T> - Result instance to transform
 *
 * PARAMETERS:
 *     transform - Function to transform the exception to a new type
 *
 * RETURNS:
 *     Result<T> - Result with transformed error type
 */
inline fun <T> Result<T>.mapError(transform: (Throwable) -> Throwable): Result<T> {
    return when {
        isSuccess -> this
        else -> Result.failure(transform(exceptionOrNull()!!))
    }
}

/**
 * NAME: mapToVPNError
 *
 * DESCRIPTION:
 *     Converts generic exceptions to appropriate VPN service errors based on
 *     exception type and message content. Provides intelligent error mapping
 *     for common network and system exceptions.
 *
 * RECEIVER:
 *     Result<T> - Result instance to convert
 *
 * RETURNS:
 *     Result<T> - Result with VPN service error on failure
 */
fun <T> Result<T>.mapToVPNError(): Result<T> {
    return mapError { throwable ->
        when (throwable) {
            is VPNServiceError -> throwable
            is java.net.SocketTimeoutException -> VPNServiceError.TimeoutError(
                errorMessage = "Network operation timed out",
                operation = "socket_operation",
                timeoutDuration = 30000L,
                expectedDuration = 30000L
            )
            is java.net.UnknownHostException -> VPNServiceError.NetworkUnavailable(
                errorMessage = "Unable to resolve server address: ${throwable.message}",
                networkType = "DNS",
                isTemporary = true
            )
            is java.net.ConnectException -> VPNServiceError.ConnectionFailed(
                errorMessage = "Failed to connect to server: ${throwable.message}",
                retryCount = 0
            )
            is SecurityException -> VPNServiceError.PermissionDenied(
                errorMessage = "Security permission denied: ${throwable.message}",
                permissionType = "SYSTEM_PERMISSION"
            )
            is IllegalArgumentException -> VPNServiceError.ConfigurationInvalid(
                errorMessage = "Invalid configuration: ${throwable.message}"
            )
            is IllegalStateException -> VPNServiceError.InterfaceError(
                errorMessage = "Invalid state: ${throwable.message}",
                interfaceType = "STATE_ERROR"
            )
            else -> VPNServiceError.ConnectionFailed(
                errorMessage = throwable.message ?: "Unknown error occurred"
            )
        }
    }
}

/**
 * NAME: onFailureWithVPNError
 *
 * DESCRIPTION:
 *     Executes a callback when Result contains a VPN service error, providing
 *     type-safe access to VPN-specific error information.
 *
 * RECEIVER:
 *     Result<T> - Result instance to check
 *
 * PARAMETERS:
 *     action - Callback function to execute with VPN service error
 *
 * RETURNS:
 *     Result<T> - Original result for chaining
 */
inline fun <T> Result<T>.onFailureWithVPNError(action: (VPNServiceError) -> Unit): Result<T> {
    exceptionOrNull()?.let { exception ->
        if (exception is VPNServiceError) {
            action(exception)
        }
    }
    return this
}

/**
 * NAME: recoverWithVPNError
 *
 * DESCRIPTION:
 *     Recovers from VPN service errors by providing alternative values or
 *     executing recovery operations based on error type.
 *
 * RECEIVER:
 *     Result<T> - Result instance to recover
 *
 * PARAMETERS:
 *     recovery - Function to provide recovery value based on VPN error
 *
 * RETURNS:
 *     Result<T> - Recovered result or original success result
 */
inline fun <T> Result<T>.recoverWithVPNError(recovery: (VPNServiceError) -> T): Result<T> {
    return when {
        isSuccess -> this
        else -> {
            val exception = exceptionOrNull()
            if (exception is VPNServiceError) {
                Result.success(recovery(exception))
            } else {
                this
            }
        }
    }
}

/**
 * NAME: retryOnVPNError
 *
 * DESCRIPTION:
 *     Retries the operation when specific VPN errors occur, with configurable
 *     retry count and delay. Supports intelligent retry based on error recoverability.
 *
 * RECEIVER:
 *     Result<T> - Result instance to potentially retry
 *
 * PARAMETERS:
 *     maxRetries - Maximum number of retry attempts
 *     retryDelay - Base delay between retries in milliseconds
 *     shouldRetry - Predicate to determine if error should trigger retry
 *     operation - Suspend function to retry
 *
 * RETURNS:
 *     Result<T> - Result after retry attempts or original result
 */
suspend inline fun <T> Result<T>.retryOnVPNError(
    maxRetries: Int = 3,
    retryDelay: Long = 1000L,
    crossinline shouldRetry: (VPNServiceError) -> Boolean = { it.isRecoverable() },
    crossinline operation: suspend () -> Result<T>
): Result<T> {
    if (isSuccess) return this
    
    val exception = exceptionOrNull()
    if (exception !is VPNServiceError || !shouldRetry(exception)) {
        return this
    }
    
    var currentResult = this
    var retryCount = 0
    
    while (retryCount < maxRetries && currentResult.isFailure) {
        val currentException = currentResult.exceptionOrNull()
        if (currentException !is VPNServiceError || !shouldRetry(currentException)) {
            break
        }
        
        // Calculate delay with exponential backoff
        val delay = retryDelay * (1L shl retryCount)
        kotlinx.coroutines.delay(delay)
        
        retryCount++
        currentResult = try {
            operation()
        } catch (e: CancellationException) {
            throw e
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    return currentResult
}

/**
 * NAME: toVPNResult
 *
 * DESCRIPTION:
 *     Converts a suspend function call to a Result with automatic VPN error mapping.
 *     Catches all exceptions and converts them to appropriate VPN service errors.
 *
 * PARAMETERS:
 *     context - Coroutine context for execution (default: Dispatchers.IO)
 *     block - Suspend function to execute
 *
 * RETURNS:
 *     Result<T> - Result with VPN error mapping applied
 */
suspend inline fun <T> toVPNResult(
    context: CoroutineContext = Dispatchers.IO,
    crossinline block: suspend () -> T
): Result<T> = withContext(context) {
    try {
        Result.success(block())
    } catch (e: CancellationException) {
        throw e
    } catch (e: Exception) {
        Result.failure(e)
    }
}.mapToVPNError()

/**
 * NAME: asyncVPNResult
 *
 * DESCRIPTION:
 *     Executes a suspend function asynchronously and returns a Deferred<Result<T>>
 *     with automatic VPN error mapping. Useful for parallel operations.
 *
 * RECEIVER:
 *     CoroutineScope - Scope for async execution
 *
 * PARAMETERS:
 *     context - Coroutine context for execution (default: Dispatchers.IO)
 *     block - Suspend function to execute asynchronously
 *
 * RETURNS:
 *     Deferred<Result<T>> - Deferred result with VPN error mapping
 */
inline fun <T> CoroutineScope.asyncVPNResult(
    context: CoroutineContext = Dispatchers.IO,
    crossinline block: suspend () -> T
) = async(context) {
    toVPNResult(context) { block() }
}

/**
 * NAME: combineVPNResults
 *
 * DESCRIPTION:
 *     Combines multiple Result instances, succeeding only if all results succeed.
 *     Returns the first VPN error encountered or a success with combined values.
 *
 * PARAMETERS:
 *     results - Variable number of Result instances to combine
 *     combiner - Function to combine success values
 *
 * RETURNS:
 *     Result<R> - Combined result or first error encountered
 */
inline fun <T, R> combineVPNResults(
    results: List<Result<T>>,
    combiner: (List<T>) -> R
): Result<R> {
    val values = mutableListOf<T>()
    
    for (result in results) {
        when {
            result.isSuccess -> values.add(result.getOrThrow())
            else -> return Result.failure(result.exceptionOrNull()!!)
        }
    }
    
    return Result.success(combiner(values))
}

/**
 * NAME: flatMapVPNResult
 *
 * DESCRIPTION:
 *     Flat maps a Result to another Result, useful for chaining operations
 *     that may fail. Preserves VPN error types throughout the chain.
 *
 * RECEIVER:
 *     Result<T> - Source result to flat map
 *
 * PARAMETERS:
 *     transform - Function to transform success value to another Result
 *
 * RETURNS:
 *     Result<R> - Flat mapped result
 */
inline fun <T, R> Result<T>.flatMapVPNResult(
    transform: (T) -> Result<R>
): Result<R> {
    return when {
        isSuccess -> transform(getOrThrow())
        else -> Result.failure(exceptionOrNull()!!)
    }
}

/**
 * NAME: zipVPNResults
 *
 * DESCRIPTION:
 *     Zips two Result instances together, succeeding only if both succeed.
 *     Useful for operations that depend on multiple successful results.
 *
 * RECEIVER:
 *     Result<T> - First result to zip
 *
 * PARAMETERS:
 *     other - Second result to zip
 *     combiner - Function to combine both success values
 *
 * RETURNS:
 *     Result<R> - Zipped result or first error encountered
 */
inline fun <T, U, R> Result<T>.zipVPNResults(
    other: Result<U>,
    combiner: (T, U) -> R
): Result<R> {
    return when {
        isSuccess && other.isSuccess -> Result.success(combiner(getOrThrow(), other.getOrThrow()))
        isFailure -> Result.failure(exceptionOrNull()!!)
        else -> Result.failure(other.exceptionOrNull()!!)
    }
}

/**
 * NAME: logVPNError
 *
 * DESCRIPTION:
 *     Logs VPN errors with structured information for debugging and monitoring.
 *     Preserves the original Result while adding logging side effects.
 *
 * RECEIVER:
 *     Result<T> - Result to log errors from
 *
 * PARAMETERS:
 *     logger - Logger instance for error logging
 *     context - Additional context information for logging
 *
 * RETURNS:
 *     Result<T> - Original result for chaining
 */
fun <T> Result<T>.logVPNError(
    context: Map<String, Any> = emptyMap()
): Result<T> {
    exceptionOrNull()?.let { exception ->
        when (exception) {
            is VPNServiceError -> {
                logError("VPN operation failed", buildMap {
                    put("error_type", exception::class.simpleName ?: "Unknown")
                    put("error_message", exception.message ?: "No message")
                    put("is_recoverable", exception.isRecoverable())
                    put("retry_delay", exception.getRetryDelay())
                    putAll(context)
                })
            }
            else -> {
                logError("Unexpected error in VPN operation", buildMap {
                    put("error_type", exception::class.simpleName ?: "Unknown")
                    put("error_message", exception.message ?: "No message")
                    putAll(context)
                })
            }
        }
    }
    return this
}
