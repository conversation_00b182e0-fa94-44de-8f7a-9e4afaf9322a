/**
 * FILE: TLVAttribute.kt
 *
 * DESCRIPTION:
 *     TLV (Type-Length-Value) attribute implementation for SDWAN protocol.
 *     Handles encoding and decoding of protocol attributes with strict ordering.
 *     Compatible with Go backend TLV format and attribute types.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.protocol.models

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * NAME: TLVAttributeType
 *
 * DESCRIPTION:
 *     Enumeration of all supported TLV attribute types.
 *     Values must match exactly with Go backend constants for compatibility.
 *
 * PROPERTIES:
 *     value - UShort value matching Go backend constants
 *     expectedLength - Expected length for fixed-size attributes
 *     description - Human-readable attribute type description
 */
enum class TLVAttributeType(val value: UByte) {
    USERNAME(1u),      // Username for authentication (matches Go TLVUsername = 0x01)
    PASSWORD(2u),      // Encrypted password (matches Go TLVPassword = 0x02)
    MTU(3u),           // MTU size (matches Go TLVMTU = 0x03)
    IP(4u),            // IP address (matches Go TLVIP = 0x04)
    DNS(5u),           // DNS server (matches Go TLVDNS = 0x05)
    GATEWAY(6u),       // Gateway address (matches Go TLVGateway = 0x06)
    NETMASK(7u),       // Network mask (matches Go TLVNetmask = 0x07)
    ENCRYPT(8u),       // Encryption method (matches Go TLVEncrypt = 0x08)
    ROUTES(9u),        // Route information
    TIMESTAMP(10u),    // Timestamp
    DELAY(11u),        // Delay information
    SDRT(12u);         // SDRT tag

    /**
     * NAME: expectedLength
     *
     * DESCRIPTION:
     *     Returns expected length for fixed-size attributes.
     *     Variable-length attributes return 0.
     *
     * RETURNS:
     *     Int - Expected length in bytes, 0 for variable length
     */
    val expectedLength: Int
        get() = when (this) {
            MTU -> 2
            IP, GATEWAY, DNS, NETMASK -> 4
            ENCRYPT -> 1
            TIMESTAMP -> 8
            DELAY -> 4
            SDRT -> 4
            USERNAME, PASSWORD, ROUTES -> 0    // Variable length
        }

    /**
     * NAME: description
     *
     * DESCRIPTION:
     *     Provides human-readable description of attribute type.
     *
     * RETURNS:
     *     String - Attribute type description
     */
    val description: String
        get() = when (this) {
            USERNAME -> "USERNAME"
            PASSWORD -> "PASSWORD"
            MTU -> "MTU"
            IP -> "IP"
            DNS -> "DNS"
            GATEWAY -> "GATEWAY"
            NETMASK -> "NETMASK"
            ENCRYPT -> "ENCRYPT"
            ROUTES -> "ROUTES"
            TIMESTAMP -> "TIMESTAMP"
            DELAY -> "DELAY"
            SDRT -> "SDRT"
        }

    companion object {
        /**
         * NAME: fromValue
         *
         * DESCRIPTION:
         *     Creates TLVAttributeType from raw value.
         *
         * PARAMETERS:
         *     value - Raw UByte value
         *
         * RETURNS:
         *     TLVAttributeType? - Attribute type if valid, null otherwise
         */
        fun fromValue(value: UByte): TLVAttributeType? {
            return values().find { it.value == value }
        }
    }
}

/**
 * NAME: TLVAttribute
 *
 * DESCRIPTION:
 *     Represents a single TLV attribute with type, length, and value.
 *     Provides encoding and decoding functionality compatible with Go backend.
 *     Uses big-endian byte order for network transmission.
 *
 * PROPERTIES:
 *     type - Attribute type identifier
 *     value - Attribute value data
 */
data class TLVAttribute(
    val type: TLVAttributeType,
    val value: ByteArray
) {
    /**
     * NAME: length
     *
     * DESCRIPTION:
     *     Gets the total length of the TLV attribute (Type + Length + Value).
     *     Length field includes Type and Length bytes (2 bytes total).
     *
     * RETURNS:
     *     UByte - Total length including Type and Length bytes
     */
    val length: UByte
        get() = (value.size + 2).toUByte()

    companion object {
        /**
         * NAME: fromByteArray
         *
         * DESCRIPTION:
         *     Parses TLV attribute from binary data.
         *     Returns attribute and offset to next attribute.
         *     Format: Type(1 byte) + Length(1 byte) + Value(Length-2 bytes)
         *
         * PARAMETERS:
         *     data - Binary data containing TLV attribute
         *     offset - Starting offset in data
         *
         * RETURNS:
         *     Pair<TLVAttribute, Int> - Parsed attribute and next offset
         *
         * THROWS:
         *     IllegalArgumentException - If data is invalid or too short
         */
        fun fromByteArray(data: ByteArray, offset: Int): Pair<TLVAttribute, Int> {
            require(offset + 2 <= data.size) { "Not enough data for TLV header" }

            val buffer = ByteBuffer.wrap(data, offset, data.size - offset).order(ByteOrder.BIG_ENDIAN)

            val typeValue = buffer.get().toUByte()
            val length = buffer.get().toUByte()

            // Length includes Type and Length bytes (2 bytes total)
            val valueLength = length.toInt() - 2
            require(valueLength >= 0) { "Invalid TLV length: $length (must be >= 2)" }

            require(offset + 2 + valueLength <= data.size) {
                "Not enough data for TLV value (need $valueLength bytes)"
            }

            val attributeType = TLVAttributeType.fromValue(typeValue)
                ?: throw IllegalArgumentException("Invalid TLV attribute type: $typeValue")

            val value = ByteArray(valueLength)
            buffer.get(value)

            return Pair(TLVAttribute(attributeType, value), offset + 2 + valueLength)
        }

        /**
         * NAME: createMTU
         *
         * DESCRIPTION:
         *     Creates MTU attribute with 2-byte integer value.
         *     Must be first attribute in OPEN packet.
         *
         * PARAMETERS:
         *     mtu - MTU value as UShort
         *
         * RETURNS:
         *     TLVAttribute - MTU attribute
         */
        fun createMTU(mtu: UShort): TLVAttribute {
            val buffer = ByteBuffer.allocate(2).order(ByteOrder.BIG_ENDIAN)
            buffer.putShort(mtu.toShort())
            return TLVAttribute(TLVAttributeType.MTU, buffer.array())
        }

        /**
         * NAME: createUsername
         *
         * DESCRIPTION:
         *     Creates username attribute with UTF-8 encoded string.
         *     Must be second attribute in OPEN packet.
         *
         * PARAMETERS:
         *     username - Username string
         *
         * RETURNS:
         *     TLVAttribute - Username attribute
         */
        fun createUsername(username: String): TLVAttribute {
            val data = username.toByteArray(Charsets.US_ASCII)
            return TLVAttribute(TLVAttributeType.USERNAME, data)
        }

        /**
         * NAME: createPassword
         *
         * DESCRIPTION:
         *     Creates password attribute with encrypted password data.
         *     Must be third attribute in OPEN packet.
         *     Password should be pre-encrypted using AES-ECB with MD5("mw" + username) key.
         *
         * PARAMETERS:
         *     encryptedPassword - Pre-encrypted password data (16 bytes)
         *
         * RETURNS:
         *     TLVAttribute - Password attribute
         */
        fun createPassword(encryptedPassword: ByteArray): TLVAttribute {
            require(encryptedPassword.size == 16) { "Encrypted password must be 16 bytes" }
            return TLVAttribute(TLVAttributeType.PASSWORD, encryptedPassword)
        }

        /**
         * NAME: createEncrypt
         *
         * DESCRIPTION:
         *     Creates encryption method attribute.
         *
         * PARAMETERS:
         *     method - Encryption method
         *
         * RETURNS:
         *     TLVAttribute - Encryption attribute
         */
        fun createEncrypt(method: EncryptionMethod): TLVAttribute {
            return TLVAttribute(TLVAttributeType.ENCRYPT, byteArrayOf(method.value.toByte()))
        }
    }

    /**
     * NAME: toByteArray
     *
     * DESCRIPTION:
     *     Converts TLV attribute to binary data using big-endian byte order.
     *     Format: Type(1 byte) + Length(1 byte) + Value(Length-2 bytes)
     *     Length includes Type and Length bytes (2 bytes total).
     *
     * RETURNS:
     *     ByteArray - TLV attribute as binary data
     */
    fun toByteArray(): ByteArray {
        val buffer = ByteBuffer.allocate(2 + value.size).order(ByteOrder.BIG_ENDIAN)
        buffer.put(type.value.toByte())
        buffer.put(length.toByte())
        buffer.put(value)
        return buffer.array()
    }

    /**
     * NAME: equals
     *
     * DESCRIPTION:
     *     Custom equals implementation for data class with ByteArray.
     *
     * PARAMETERS:
     *     other - Object to compare with
     *
     * RETURNS:
     *     Boolean - true if attributes are equal, false otherwise
     */
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as TLVAttribute

        if (type != other.type) return false
        if (!value.contentEquals(other.value)) return false

        return true
    }

    /**
     * NAME: hashCode
     *
     * DESCRIPTION:
     *     Custom hashCode implementation for data class with ByteArray.
     *
     * RETURNS:
     *     Int - Hash code for attribute
     */
    override fun hashCode(): Int {
        var result = type.hashCode()
        result = 31 * result + value.contentHashCode()
        return result
    }
}
