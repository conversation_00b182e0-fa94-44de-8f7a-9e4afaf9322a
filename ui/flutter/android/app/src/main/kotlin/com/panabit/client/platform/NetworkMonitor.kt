/**
 * FILE: NetworkMonitor.kt
 *
 * DESCRIPTION:
 *     Android network monitoring implementation following iOS NWPathMonitor delegate pattern.
 *     Monitors network connectivity changes, interface switching (WiFi ↔ Cellular), and
 *     notifies delegates about network changes. Decoupled architecture for better maintainability.
 *     Based on iOS NWPathMonitor patterns with Android ConnectivityManager integration.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create, 11/07/2025 refactored for delegate pattern
 */

package com.panabit.client.platform

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.content.Context
import android.net.*
import android.net.NetworkCapabilities.*
import kotlinx.coroutines.*
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

/**
 * NAME: NetworkMonitorDelegate
 *
 * DESCRIPTION:
 *     Delegate interface for network monitoring events.
 *     Follows iOS NWPathMonitor delegate pattern for decoupled architecture.
 *
 * METHODS:
 *     onNetworkChanged - Called when network interface type changes
 *     onNetworkAvailable - Called when new network becomes available
 *     onNetworkLost - Called when network is lost
 *     shouldTriggerReconnection - Determines if reconnection should be triggered
 */
interface NetworkMonitorDelegate {
    /**
     * Called when network interface type changes (WiFi ↔ Cellular)
     */
    fun onNetworkChanged(changeType: String, networkType: Int)

    /**
     * Called when new network becomes available
     */
    fun onNetworkAvailable(network: Network)

    /**
     * Called when network is lost
     */
    fun onNetworkLost(network: Network)

    /**
     * Determines if reconnection should be triggered for network change
     */
    fun shouldTriggerReconnection(oldType: Int, newType: Int): Boolean
}

/**
 * NAME: NetworkMonitor
 *
 * DESCRIPTION:
 *     Network monitoring class for Android VPN operations using ConnectivityManager.
 *     Monitors network state changes, interface switching, and connectivity status.
 *     Uses delegate pattern to notify about network changes, following iOS NWPathMonitor design.
 *     Decoupled from VPN components for better architecture and maintainability.
 *
 * FEATURES:
 *     - Network interface change detection (WiFi ↔ Cellular)
 *     - Network availability monitoring with validation
 *     - Delegate-based notification system
 *     - Thread-safe operation using Kotlin coroutines
 *     - Cross-platform consistent architecture
 *
 * DESIGN PRINCIPLES:
 *     - Functional consistency with iOS NWPathMonitor
 *     - Android-optimized using ConnectivityManager.NetworkCallback
 *     - Delegate pattern for decoupled architecture
 *     - Reactive network state management
 */
class NetworkMonitor(
    private val context: Context
) {
    companion object {
        private const val TAG = "NetworkMonitor"
    }

    // MARK: - Core Components
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

    // Dedicated coroutine scope for network operations (replacing GlobalScope)
    private val networkScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // Monitoring state
    private val isMonitoring = AtomicBoolean(false)
    private val isVPNConnected = AtomicBoolean(false)

    // Delegate for network change notifications
    var delegate: NetworkMonitorDelegate? = null
    
    // Network callback for monitoring
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            logDebug("Network available", mapOf("network" to network.toString()))
            // Check for physical interface changes when VPN is connected
            if (isVPNConnected.get()) {
                checkPhysicalInterfaceChange()
            }
        }

        override fun onLost(network: Network) {
            logDebug("Network lost", mapOf("network" to network.toString()))
            // Check for physical interface changes when VPN is connected
            if (isVPNConnected.get()) {
                checkPhysicalInterfaceChange()
            }
        }

        override fun onCapabilitiesChanged(network: Network, capabilities: NetworkCapabilities) {
            logDebug("Network capabilities changed", mapOf(
                "network" to network.toString(),
                "wifi" to capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI),
                "cellular" to capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
            ))
            // Check for physical interface changes when VPN is connected
            if (isVPNConnected.get()) {
                checkPhysicalInterfaceChange()
            }
        }

        override fun onLinkPropertiesChanged(network: Network, linkProperties: LinkProperties) {
            logDebug("Network link properties changed", mapOf(
                "network" to network.toString(),
                "interface" to (linkProperties.interfaceName ?: "unknown")
            ))
            // Check for physical interface changes when VPN is connected
            if (isVPNConnected.get()) {
                checkPhysicalInterfaceChange()
            }
        }

        override fun onBlockedStatusChanged(network: Network, blocked: Boolean) {
            logDebug("Network blocked status changed", mapOf(
                "network" to network.toString(),
                "blocked" to blocked
            ))
            // Check for physical interface changes when VPN is connected
            if (isVPNConnected.get()) {
                checkPhysicalInterfaceChange()
            }
        }
    }

    // MARK: - Public Interface

    /**
     * NAME: startMonitoring
     *
     * DESCRIPTION:
     *     Starts network monitoring using ConnectivityManager.NetworkCallback.
     *     Registers for network state change notifications.
     */
    fun startMonitoring() {
        if (isMonitoring.compareAndSet(false, true)) {
            logInfo("Starting network monitoring")
            
            try {
                // Register network callback for all networks
                val networkRequest = NetworkRequest.Builder()
                    .addCapability(NET_CAPABILITY_INTERNET)
                    .addCapability(NET_CAPABILITY_VALIDATED)
                    .build()
                
                connectivityManager.registerNetworkCallback(networkRequest, networkCallback)

                logInfo("Network monitoring started successfully")
                
            } catch (e: Exception) {
                logError("Failed to start network monitoring", e)
                isMonitoring.set(false)
            }
        }
    }

    /**
     * NAME: stopMonitoring
     *
     * DESCRIPTION:
     *     Stops network monitoring and unregisters callbacks.
     *     Also cancels any pending network operations.
     */
    fun stopMonitoring() {
        if (isMonitoring.compareAndSet(true, false)) {
            logInfo("Stopping network monitoring")

            try {
                // Unregister network callback
                connectivityManager.unregisterNetworkCallback(networkCallback)

                // Cancel any pending network operations
                networkScope.coroutineContext[Job]?.cancelChildren()

                logInfo("Network monitoring stopped successfully")

            } catch (e: Exception) {
                logError("Failed to stop network monitoring", mapOf("error" to (e.message ?: "unknown")), e)
            }
        }
    }

    /**
     * NAME: setVPNConnected
     *
     * DESCRIPTION:
     *     Updates VPN connection status for network change handling.
     *     Only triggers reconnection when VPN is connected.
     *
     * PARAMETERS:
     *     connected - Whether VPN is currently connected
     */
    fun setVPNConnected(connected: Boolean) {
        val wasConnected = isVPNConnected.getAndSet(connected)
        
        if (wasConnected != connected) {
            logInfo("VPN connection status updated", mapOf(
                "was_connected" to wasConnected,
                "now_connected" to connected
            ))
        }
    }

    // MARK: - Network Change Handling

    /**
     * NAME: handleNetworkChange
     *
     * DESCRIPTION:
     *     Handles basic network availability changes using delegate pattern.
     *     优化：避免在VPN连接时频繁通知网络变化，减少不必要的重连触发。
     *
     * PARAMETERS:
     *     network - Network that changed
     *     changeType - Type of change (available, lost)
     */
    private fun handleNetworkChange(network: Network?, changeType: String) {
        logInfo("Network change detected", mapOf(
            "network_id" to (network?.toString() ?: "null"),
            "change_type" to changeType,
            "vpn_connected" to isVPNConnected.get()
        ))

        // 如果VPN已连接，减少网络变化通知的频率，避免前后台切换时的误触发
        if (isVPNConnected.get()) {
            logDebug("VPN is connected, filtering network change notifications to prevent unnecessary reconnections")
            // 只在真正的网络丢失时通知，避免短暂的网络状态变化
            if (changeType == "lost") {
                logWarn("Network lost while VPN connected, notifying delegate")
                delegate?.onNetworkLost(network!!)
            }
            // 忽略 "available" 事件，因为VPN已连接时不需要响应新网络可用
            return
        }

        // VPN未连接时正常处理网络变化
        network?.let { net ->
            when (changeType) {
                "available" -> {
                    logInfo("New network available, notifying delegate")
                    delegate?.onNetworkAvailable(net)
                }

                "lost" -> {
                    logWarn("Network lost, notifying delegate")
                    delegate?.onNetworkLost(net)
                }
            }
        }
    }





    // MARK: - Helper Methods















    /**
     * NAME: checkPhysicalInterfaceChange
     *
     * DESCRIPTION:
     *     Checks if current physical interface differs from recorded interface at connection time.
     *     Simple comparison of interface name and IP address.
     */
    private fun checkPhysicalInterfaceChange() {
        try {
            // Get delegate as ConnectionManager
            val connectionManager = delegate as? com.panabit.client.connection.ConnectionManager ?: return

            // Get recorded interface info from connection time
            val recordedInterface = connectionManager.getRecordedPhysicalInterfaceInfo()
            val recordedInterfaceName = recordedInterface.first
            val recordedIP = recordedInterface.second

            // Skip if no recorded interface info
            if (recordedInterfaceName == "unknown" || recordedIP == "unknown") {
                return
            }

            // Get current physical interface info
            val currentInterface = getCurrentPhysicalInterfaceInfo()
            val currentInterfaceName = currentInterface.first
            val currentIP = currentInterface.second

            // Check if interface has changed (name or IP)
            if (recordedInterfaceName != currentInterfaceName || recordedIP != currentIP) {
                logInfo("Physical interface changed, notifying for reconnect", mapOf(
                    "recorded" to "$recordedInterfaceName($recordedIP)",
                    "current" to "$currentInterfaceName($currentIP)"
                ))

                // Notify delegate to trigger reconnection
                val changeDescription = "Physical interface changed: $recordedInterfaceName -> $currentInterfaceName"
                delegate?.onNetworkChanged(changeDescription, 0)
            }

        } catch (e: Exception) {
            logError("Failed to check physical interface change", e)
        }
    }

    /**
     * NAME: getCurrentPhysicalInterfaceInfo
     *
     * DESCRIPTION:
     *     Gets current optimal physical interface info, excluding VPN interfaces.
     *     Prioritizes WiFi over cellular when both are available and validated.
     *
     * RETURNS:
     *     Pair<String, String> - Interface name and IP address
     */
    private fun getCurrentPhysicalInterfaceInfo(): Pair<String, String> {
        return try {
            val allNetworks = connectivityManager.allNetworks
            var wifiInterface: Pair<String, String>? = null
            var cellularInterface: Pair<String, String>? = null
            var otherInterface: Pair<String, String>? = null

            logDebug("Checking ${allNetworks.size} networks for physical interface")

            for (network in allNetworks) {
                try {
                    val capabilities = connectivityManager.getNetworkCapabilities(network)
                    val linkProperties = connectivityManager.getLinkProperties(network)

                    // Skip if network doesn't have required capabilities
                    if (capabilities == null || linkProperties == null ||
                        !capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) ||
                        !capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)) {
                        continue
                    }

                    val interfaceName = linkProperties.interfaceName ?: continue

                    // Skip VPN interfaces
                    if (interfaceName.startsWith("tun") ||
                        interfaceName.startsWith("ppp") ||
                        interfaceName.startsWith("vpn")) {
                        logDebug("Skipping VPN interface: $interfaceName")
                        continue
                    }

                    // Get IP address
                    val ipAddress = linkProperties.linkAddresses
                        ?.firstOrNull { !it.address.isLoopbackAddress && !it.address.isLinkLocalAddress }
                        ?.address?.hostAddress ?: continue

                    val interfaceInfo = Pair(interfaceName, ipAddress)

                    // Categorize by transport type
                    when {
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> {
                            wifiInterface = interfaceInfo
                            logDebug("Found WiFi interface: $interfaceName ($ipAddress)")
                        }
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                            cellularInterface = interfaceInfo
                            logDebug("Found cellular interface: $interfaceName ($ipAddress)")
                        }
                        else -> {
                            if (otherInterface == null) {
                                otherInterface = interfaceInfo
                                logDebug("Found other interface: $interfaceName ($ipAddress)")
                            }
                        }
                    }
                } catch (e: Exception) {
                    logDebug("Error processing network $network: ${e.message}")
                    continue
                }
            }

            // Prioritize: WiFi > Cellular > Other
            val selectedInterface = wifiInterface ?: cellularInterface ?: otherInterface

            if (selectedInterface != null) {
                logDebug("Selected physical interface: ${selectedInterface.first} (${selectedInterface.second})")
                return selectedInterface
            } else {
                logDebug("No physical interface found")
                return Pair("unknown", "unknown")
            }

        } catch (e: Exception) {
            logError("Failed to get current physical interface info", e)
            Pair("unknown", "unknown")
        }
    }
}
