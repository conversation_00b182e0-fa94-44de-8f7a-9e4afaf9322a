/**
 * FILE: VPNStateManager.kt
 *
 * DESCRIPTION:
 *     VPN state management class providing thread-safe state transitions and StateFlow-based
 *     state observation for Android VPN operations. Implements state transition validation,
 *     state persistence, and Flutter communication support while maintaining simplicity
 *     according to user's architecture preferences.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.connection.state

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import com.panabit.client.connection.models.VPNState
import com.panabit.client.connection.models.ServerInfo
import com.panabit.client.infrastructure.error.VPNServiceError
import java.util.concurrent.atomic.AtomicLong

/**
 * NAME: VPNStateManager
 *
 * DESCRIPTION:
 *     Thread-safe VPN state manager using Kotlin StateFlow for reactive state management.
 *     Provides state transition validation, persistence, and observation capabilities.
 *     Designed for simplicity while ensuring thread safety and cross-platform compatibility.
 *
 * FEATURES:
 *     - Thread-safe state transitions using Mutex
 *     - StateFlow-based reactive state observation
 *     - Basic state transition validation
 *     - State persistence for app restart recovery
 *     - Flutter-compatible state mapping
 *     - Android lifecycle-aware state management
 *
 * DESIGN PRINCIPLES:
 *     - Simple architecture without over-engineering
 *     - Functional consistency with iOS (not implementation equivalence)
 *     - Android platform optimization (StateFlow, SharedPreferences)
 *     - Thread safety through coroutines and Mutex
 */
class VPNStateManager(
    private val context: Context
) {
    companion object {
        private const val TAG = "VPNStateManager"
        private const val PREFS_NAME = "vpn_state_prefs"
        private const val KEY_LAST_STATE = "last_state"
        private const val KEY_STATE_TIMESTAMP = "state_timestamp"
    }

    // MARK: - State Management Properties
    
    private val stateMutex = Mutex()
    private val _currentState = MutableStateFlow<VPNState>(VPNState.Disconnected)
    private val stateTimestamp = AtomicLong(System.currentTimeMillis())
    
    /**
     * NAME: currentState
     *
     * DESCRIPTION:
     *     Observable current VPN state using StateFlow for reactive UI updates.
     *     Thread-safe access to current state without blocking.
     *
     * RETURNS:
     *     StateFlow<VPNState> - Current VPN state flow for observation
     */
    val currentState: StateFlow<VPNState> = _currentState.asStateFlow()
    
    /**
     * NAME: currentStateValue
     *
     * DESCRIPTION:
     *     Synchronous access to current state value for immediate checks.
     *
     * RETURNS:
     *     VPNState - Current VPN state value
     */
    val currentStateValue: VPNState
        get() = _currentState.value

    // MARK: - State Persistence
    
    private val sharedPreferences: SharedPreferences by lazy {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    // MARK: - Initialization
    
    init {
        Log.d(TAG, "VPNStateManager initialized")
        restoreStateFromPersistence()
    }

    // MARK: - State Transition Methods
    
    /**
     * NAME: updateState
     *
     * DESCRIPTION:
     *     Updates VPN state with thread safety and validation.
     *     Performs state transition validation, deduplication, and persistence.
     *
     * PARAMETERS:
     *     newState - New VPN state to transition to
     *     skipValidation - Whether to skip state transition validation (default: false)
     */
    suspend fun updateState(newState: VPNState, skipValidation: Boolean = false) {
        stateMutex.withLock {
            val oldState = _currentState.value
            
            // State deduplication: Skip if state hasn't actually changed
            if (oldState == newState) {
                Log.d(TAG, "State update skipped - no change: $oldState")
                return
            }
            
            // State transition validation (unless skipped)
            if (!skipValidation && !VPNState.isValidStateTransition(oldState, newState)) {
                Log.w(TAG, "Invalid state transition blocked: $oldState -> $newState")
                return
            }
            
            // Update state and timestamp
            _currentState.value = newState
            stateTimestamp.set(System.currentTimeMillis())
            
            Log.i(TAG, "State transition: $oldState -> $newState")
            
            // Persist state for recovery
            persistState(newState)
        }
    }

    /**
     * NAME: forceUpdateState
     *
     * DESCRIPTION:
     *     Forces state update without validation for error recovery scenarios.
     *
     * PARAMETERS:
     *     newState - New VPN state to force transition to
     */
    suspend fun forceUpdateState(newState: VPNState) {
        updateState(newState, skipValidation = true)
    }

    // MARK: - State Query Methods
    
    /**
     * NAME: isOperationInProgress
     *
     * DESCRIPTION:
     *     Checks if any VPN operation is currently in progress.
     *
     * RETURNS:
     *     Boolean - true if operation is in progress, false otherwise
     */
    fun isOperationInProgress(): Boolean = currentStateValue.isOperationInProgress
    
    /**
     * NAME: isConnected
     *
     * DESCRIPTION:
     *     Checks if VPN is currently connected.
     *
     * RETURNS:
     *     Boolean - true if VPN is connected, false otherwise
     */
    fun isConnected(): Boolean = currentStateValue.isConnected
    
    /**
     * NAME: getConnectedServer
     *
     * DESCRIPTION:
     *     Gets the currently connected server information.
     *
     * RETURNS:
     *     ServerInfo? - Connected server info if available, null otherwise
     */
    fun getConnectedServer(): ServerInfo? = currentStateValue.connectedServer

    /**
     * NAME: getStateAge
     *
     * DESCRIPTION:
     *     Gets the age of current state in milliseconds.
     *
     * RETURNS:
     *     Long - State age in milliseconds
     */
    fun getStateAge(): Long = System.currentTimeMillis() - stateTimestamp.get()



    // MARK: - State Persistence Methods

    /**
     * NAME: persistState
     *
     * DESCRIPTION:
     *     Persists current state to SharedPreferences for app restart recovery.
     *     Enhanced for Android platform with additional metadata.
     *
     * PARAMETERS:
     *     state - VPN state to persist
     */
    private fun persistState(state: VPNState) {
        try {
            val editor = sharedPreferences.edit()

            // Basic state information
            editor.putString(KEY_LAST_STATE, state.flutterStatusString)
            editor.putLong(KEY_STATE_TIMESTAMP, stateTimestamp.get())

            // Android-specific persistence enhancements
            when (state) {
                is VPNState.Connected -> {
                    editor.putString("last_connected_server_id", state.server.id)
                    editor.putString("last_tunnel_ip", state.tunnelIP)
                    editor.putLong("last_connected_at", state.connectedAt)
                }
                is VPNState.Error -> {
                    editor.putString("last_error_type", state.error.javaClass.simpleName)
                    editor.putString("last_error_message", state.error.message)
                    state.lastServer?.let { server ->
                        editor.putString("last_error_server_id", server.id)
                    }
                }
                else -> {
                    // Clear previous connection data for other states
                    editor.remove("last_connected_server_id")
                    editor.remove("last_tunnel_ip")
                    editor.remove("last_connected_at")
                    editor.remove("last_error_type")
                    editor.remove("last_error_message")
                    editor.remove("last_error_server_id")
                }
            }

            editor.apply()
            Log.d(TAG, "State persisted: ${state.flutterStatusString}")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to persist state", e)
        }
    }

    /**
     * NAME: restoreStateFromPersistence
     *
     * DESCRIPTION:
     *     Restores state from SharedPreferences after app restart.
     *     Enhanced for Android platform with safety checks and metadata restoration.
     */
    private fun restoreStateFromPersistence() {
        try {
            val lastStateString = sharedPreferences.getString(KEY_LAST_STATE, "disconnected")
            val lastTimestamp = sharedPreferences.getLong(KEY_STATE_TIMESTAMP, 0L)

            // Check if persisted state is too old (older than 1 hour)
            val stateAge = System.currentTimeMillis() - lastTimestamp
            if (stateAge > 3_600_000L) { // 1 hour
                Log.i(TAG, "Persisted state too old (${stateAge}ms), defaulting to disconnected")
                _currentState.value = VPNState.Disconnected
                return
            }

            // Only restore safe terminal states
            val restoredState = when (lastStateString) {
                "disconnected" -> VPNState.Disconnected
                "error" -> {
                    val errorType = sharedPreferences.getString("last_error_type", "ConnectionFailed")
                    val errorMessage = sharedPreferences.getString("last_error_message",
                        "Connection lost during app restart") ?: "Unknown error"

                    VPNState.Error(
                        VPNServiceError.ConnectionFailed(errorMessage),
                        lastServer = null // Don't restore server info for safety
                    )
                }
                else -> {
                    Log.w(TAG, "Unsafe state for restoration: $lastStateString, defaulting to disconnected")
                    VPNState.Disconnected
                }
            }

            _currentState.value = restoredState
            stateTimestamp.set(lastTimestamp)

            Log.i(TAG, "State restored from persistence: $restoredState")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to restore state from persistence", e)
            _currentState.value = VPNState.Disconnected
            stateTimestamp.set(System.currentTimeMillis())
        }
    }

    /**
     * NAME: clearPersistedState
     *
     * DESCRIPTION:
     *     Clears all persisted state data. Useful for clean app reset.
     */
    fun clearPersistedState() {
        try {
            sharedPreferences.edit().clear().apply()
            Log.d(TAG, "Persisted state cleared")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear persisted state", e)
        }
    }

    // MARK: - Cleanup
    
    /**
     * NAME: cleanup
     *
     * DESCRIPTION:
     *     Cleans up state manager resources.
     *     Should be called when manager is no longer needed.
     */
    fun cleanup() {
        Log.d(TAG, "VPNStateManager cleanup")
        // StateFlow cleanup is automatic with coroutine scope cancellation
    }
}
