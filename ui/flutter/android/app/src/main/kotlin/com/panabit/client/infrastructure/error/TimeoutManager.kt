/**
 * FILE: TimeoutManager.kt
 *
 * DESCRIPTION:
 *     统一超时管理器，与Windows版本保持一致的超时配置和处理
 *     提供标准化的超时时间配置和超时错误创建功能
 *
 * AUTHOR: wei
 * HISTORY: 24/07/2025 create - 立即实施Android错误处理改进
 */

package com.panabit.client.infrastructure.error

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logWarn

/**
 * NAME: TimeoutManager
 *
 * DESCRIPTION:
 *     统一超时管理器，提供与Windows版本一致的超时配置
 *     支持不同操作类型的超时时间配置和超时错误创建
 */
class TimeoutManager {
    
    companion object {
        // 超时配置（与Windows版本保持完全一致）
        const val CONNECTION_TIMEOUT = 25_000L        // 25秒 - 连接建立超时
        const val AUTHENTICATION_TIMEOUT = 15_000L    // 15秒 - 认证超时
        const val HEARTBEAT_TIMEOUT = 45_000L         // 45秒 - 心跳超时
        const val DNS_RESOLUTION_TIMEOUT = 10_000L    // 10秒 - DNS解析超时
        const val PROTOCOL_HANDSHAKE_TIMEOUT = 20_000L // 20秒 - 协议握手超时
        const val DATA_TRANSFER_TIMEOUT = 30_000L     // 30秒 - 数据传输超时
        const val RECONNECTION_TIMEOUT = 60_000L      // 60秒 - 重连超时
        const val TUNNEL_INIT_TIMEOUT = 15_000L       // 15秒 - 隧道初始化超时
        
        // 默认超时时间
        const val DEFAULT_TIMEOUT = 30_000L           // 30秒 - 默认超时
        
        // 超时操作类型常量
        const val OPERATION_CONNECTION = "connection"
        const val OPERATION_AUTHENTICATION = "authentication"
        const val OPERATION_AUTH = "auth"
        const val OPERATION_HEARTBEAT = "heartbeat"
        const val OPERATION_DNS = "dns"
        const val OPERATION_DNS_RESOLUTION = "dns_resolution"
        const val OPERATION_PROTOCOL_HANDSHAKE = "protocol_handshake"
        const val OPERATION_HANDSHAKE = "handshake"
        const val OPERATION_DATA_TRANSFER = "data_transfer"
        const val OPERATION_RECONNECTION = "reconnection"
        const val OPERATION_TUNNEL_INIT = "tunnel_init"
    }

    /**
     * NAME: createTimeoutError
     *
     * DESCRIPTION:
     *     创建统一的超时错误对象，根据操作类型自动选择合适的错误代码
     *
     * PARAMETERS:
     *     operation - 超时的操作类型
     *     duration - 实际超时时长（毫秒）
     *     additionalInfo - 附加信息（可选）
     *
     * RETURNS:
     *     VPNServiceError.TimeoutError - 超时错误对象
     */
    fun createTimeoutError(
        operation: String, 
        duration: Long, 
        additionalInfo: Map<String, Any> = emptyMap()
    ): VPNServiceError.TimeoutError {
        
        val normalizedOperation = operation.lowercase().trim()
        val errorCode = mapOperationToErrorCode(normalizedOperation)
        val expectedDuration = getTimeoutForOperation(normalizedOperation)
        
        val errorMessage = buildString {
            append("Operation '$operation' timed out after ${duration}ms")
            if (expectedDuration > 0) {
                append(" (expected: ${expectedDuration}ms)")
            }
            if (additionalInfo.isNotEmpty()) {
                append(" - Additional info: $additionalInfo")
            }
        }
        
        logWarn("Timeout occurred", mapOf(
            "operation" to operation,
            "duration_ms" to duration,
            "expected_duration_ms" to expectedDuration,
            "error_code" to errorCode.code,
            "additional_info" to additionalInfo
        ))
        
        return VPNServiceError.TimeoutError(
            errorMessage = errorMessage,
            operation = operation,
            timeoutDuration = duration,
            expectedDuration = expectedDuration,
            errorCode = errorCode
        )
    }

    /**
     * NAME: getTimeoutForOperation
     *
     * DESCRIPTION:
     *     获取指定操作的标准超时时长
     *
     * PARAMETERS:
     *     operation - 操作类型
     *
     * RETURNS:
     *     Long - 超时时长（毫秒），如果操作类型未知则返回默认超时
     */
    fun getTimeoutForOperation(operation: String): Long {
        return when (operation.lowercase().trim()) {
            OPERATION_CONNECTION -> CONNECTION_TIMEOUT
            OPERATION_AUTHENTICATION, OPERATION_AUTH -> AUTHENTICATION_TIMEOUT
            OPERATION_HEARTBEAT -> HEARTBEAT_TIMEOUT
            OPERATION_DNS, OPERATION_DNS_RESOLUTION -> DNS_RESOLUTION_TIMEOUT
            OPERATION_PROTOCOL_HANDSHAKE, OPERATION_HANDSHAKE -> PROTOCOL_HANDSHAKE_TIMEOUT
            OPERATION_DATA_TRANSFER -> DATA_TRANSFER_TIMEOUT
            OPERATION_RECONNECTION -> RECONNECTION_TIMEOUT
            OPERATION_TUNNEL_INIT -> TUNNEL_INIT_TIMEOUT
            else -> {
                logDebug("Unknown operation type for timeout", mapOf(
                    "operation" to operation,
                    "using_default_timeout" to DEFAULT_TIMEOUT
                ))
                DEFAULT_TIMEOUT
            }
        }
    }

    /**
     * NAME: mapOperationToErrorCode
     *
     * DESCRIPTION:
     *     将操作类型映射到对应的错误代码
     *
     * PARAMETERS:
     *     operation - 操作类型
     *
     * RETURNS:
     *     VPNErrorCode - 对应的错误代码
     */
    private fun mapOperationToErrorCode(operation: String): VPNErrorCode {
        return when (operation) {
            OPERATION_CONNECTION -> VPNErrorCode.NETWORK_TIMEOUT
            OPERATION_AUTHENTICATION, OPERATION_AUTH -> VPNErrorCode.AUTH_TOKEN_EXPIRED
            OPERATION_HEARTBEAT -> VPNErrorCode.NETWORK_TIMEOUT
            OPERATION_DNS, OPERATION_DNS_RESOLUTION -> VPNErrorCode.NETWORK_DNS_FAILURE
            OPERATION_PROTOCOL_HANDSHAKE, OPERATION_HANDSHAKE -> VPNErrorCode.PROTOCOL_HANDSHAKE_FAILED
            OPERATION_DATA_TRANSFER -> VPNErrorCode.NETWORK_TIMEOUT
            OPERATION_RECONNECTION -> VPNErrorCode.NETWORK_TIMEOUT
            OPERATION_TUNNEL_INIT -> VPNErrorCode.TUNNEL_INIT_FAILED
            else -> VPNErrorCode.NETWORK_TIMEOUT
        }
    }

    /**
     * NAME: isTimeoutError
     *
     * DESCRIPTION:
     *     判断给定的异常是否为超时错误
     *
     * PARAMETERS:
     *     throwable - 要检查的异常
     *
     * RETURNS:
     *     Boolean - 如果是超时错误返回true
     */
    fun isTimeoutError(throwable: Throwable): Boolean {
        return when {
            throwable is VPNServiceError.TimeoutError -> true
            throwable.message?.contains("timeout", ignoreCase = true) == true -> true
            throwable.message?.contains("timed out", ignoreCase = true) == true -> true
            throwable::class.java.simpleName.contains("Timeout", ignoreCase = true) -> true
            else -> false
        }
    }

    /**
     * NAME: createTimeoutErrorFromException
     *
     * DESCRIPTION:
     *     从异常创建超时错误对象
     *
     * PARAMETERS:
     *     exception - 原始异常
     *     operation - 超时的操作类型
     *     duration - 超时时长
     *
     * RETURNS:
     *     VPNServiceError.TimeoutError - 超时错误对象
     */
    fun createTimeoutErrorFromException(
        exception: Throwable,
        operation: String,
        duration: Long
    ): VPNServiceError.TimeoutError {
        val additionalInfo = mapOf(
            "original_exception" to exception::class.java.simpleName,
            "original_message" to (exception.message ?: "No message")
        )
        
        return createTimeoutError(operation, duration, additionalInfo)
    }

    /**
     * NAME: getRetryDelay
     *
     * DESCRIPTION:
     *     根据操作类型和重试次数计算重试延迟
     *
     * PARAMETERS:
     *     operation - 操作类型
     *     retryCount - 重试次数
     *
     * RETURNS:
     *     Long - 重试延迟时间（毫秒）
     */
    fun getRetryDelay(operation: String, retryCount: Int): Long {
        val baseDelay = when (operation.lowercase().trim()) {
            OPERATION_CONNECTION -> 2000L
            OPERATION_AUTHENTICATION, OPERATION_AUTH -> 5000L
            OPERATION_HEARTBEAT -> 10000L
            OPERATION_DNS, OPERATION_DNS_RESOLUTION -> 3000L
            OPERATION_PROTOCOL_HANDSHAKE, OPERATION_HANDSHAKE -> 5000L
            else -> 3000L
        }
        
        // 指数退避，最大延迟30秒
        return minOf(baseDelay * (1L shl retryCount), 30000L)
    }

    /**
     * NAME: shouldRetryOnTimeout
     *
     * DESCRIPTION:
     *     判断超时错误是否应该重试
     *
     * PARAMETERS:
     *     operation - 操作类型
     *     retryCount - 已重试次数
     *
     * RETURNS:
     *     Boolean - 如果应该重试返回true
     */
    fun shouldRetryOnTimeout(operation: String, retryCount: Int): Boolean {
        val maxRetries = when (operation.lowercase().trim()) {
            OPERATION_CONNECTION -> 3
            OPERATION_AUTHENTICATION, OPERATION_AUTH -> 2
            OPERATION_HEARTBEAT -> 5
            OPERATION_DNS, OPERATION_DNS_RESOLUTION -> 3
            OPERATION_PROTOCOL_HANDSHAKE, OPERATION_HANDSHAKE -> 3
            OPERATION_DATA_TRANSFER -> 2
            OPERATION_RECONNECTION -> 3
            OPERATION_TUNNEL_INIT -> 2
            else -> 3
        }
        
        return retryCount < maxRetries
    }

    /**
     * NAME: getAllTimeoutConfigurations
     *
     * DESCRIPTION:
     *     获取所有超时配置信息，用于调试和监控
     *
     * RETURNS:
     *     Map<String, Long> - 所有超时配置的映射
     */
    fun getAllTimeoutConfigurations(): Map<String, Long> {
        return mapOf(
            OPERATION_CONNECTION to CONNECTION_TIMEOUT,
            OPERATION_AUTHENTICATION to AUTHENTICATION_TIMEOUT,
            OPERATION_HEARTBEAT to HEARTBEAT_TIMEOUT,
            OPERATION_DNS_RESOLUTION to DNS_RESOLUTION_TIMEOUT,
            OPERATION_PROTOCOL_HANDSHAKE to PROTOCOL_HANDSHAKE_TIMEOUT,
            OPERATION_DATA_TRANSFER to DATA_TRANSFER_TIMEOUT,
            OPERATION_RECONNECTION to RECONNECTION_TIMEOUT,
            OPERATION_TUNNEL_INIT to TUNNEL_INIT_TIMEOUT,
            "default" to DEFAULT_TIMEOUT
        )
    }

    /**
     * NAME: validateTimeout
     *
     * DESCRIPTION:
     *     验证超时配置是否合理
     *
     * PARAMETERS:
     *     operation - 操作类型
     *     timeout - 超时时间
     *
     * RETURNS:
     *     Boolean - 如果超时配置合理返回true
     */
    fun validateTimeout(operation: String, timeout: Long): Boolean {
        val standardTimeout = getTimeoutForOperation(operation)
        val minTimeout = standardTimeout / 2
        val maxTimeout = standardTimeout * 3
        
        return timeout in minTimeout..maxTimeout
    }
}
