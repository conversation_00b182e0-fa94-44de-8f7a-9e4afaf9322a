/**
 * FILE: ITforceVPNService.kt
 *
 * DESCRIPTION:
 *     Android VPN service implementation based on simplified architecture design.
 *     Manages VPN connection lifecycle, permissions, state management, and notification system.
 *     Follows iOS VPNService patterns while leveraging Android-specific features.
 *     Implements unified state management and avoids over-engineering.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create, 13/07/2025 refactor based on architecture review
 */

package com.panabit.client.vpn

import com.panabit.client.R
import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.content.BroadcastReceiver
import android.content.IntentFilter
import android.net.VpnService
import android.os.Build
import android.os.PowerManager
import android.content.Context
import androidx.core.app.NotificationCompat
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import org.koin.android.ext.android.inject
import com.panabit.client.MainActivity
import com.panabit.client.connection.ConnectionManager
import com.panabit.client.connection.ServerManager
import com.panabit.client.connection.models.VPNState
import com.panabit.client.connection.models.ServerInfo
import com.panabit.client.connection.models.ConnectionProgress
import com.panabit.client.infrastructure.error.VPNServiceError
import com.panabit.client.platform.VPNInterfaceManager
import com.panabit.client.domain.adapters.VPNServiceAdapter
import kotlinx.serialization.json.Json
import kotlinx.serialization.decodeFromString

/**
 * NAME: ITforceVPNService
 *
 * DESCRIPTION:
 *     Android VPN service implementation following simplified architecture design.
 *     Manages VPN connection lifecycle, permissions, unified state management, and notifications.
 *     Based on iOS VPNService patterns while leveraging Android VpnService framework.
 *
 * FEATURES:
 *     - Unified state management with single VPNState source
 *     - VPN permission handling with user-friendly flow
 *     - Core component integration (ConnectionManager + ServerManager)
 *     - Foreground service with notification system
 *     - Cross-platform compatibility with iOS/Windows
 *
 * DESIGN PRINCIPLES:
 *     - Simplified architecture avoiding component overlap
 *     - Single responsibility for VPN service management
 *     - Functional consistency with iOS implementation
 *     - Android platform optimization
 */
class ITforceVPNService : VpnService() {

    companion object {
        private const val TAG = "ITforceVPNService"
        private const val NOTIFICATION_ID = 1001
        private const val NOTIFICATION_CHANNEL_ID = "vpn_service_channel"

        // Service actions
        const val ACTION_CONNECT = "com.panabit.client.CONNECT"
        const val ACTION_DISCONNECT = "com.panabit.client.DISCONNECT"
        const val ACTION_RECONNECT = "com.panabit.client.RECONNECT"
        const val ACTION_CHECK_PERMISSION = "com.panabit.client.CHECK_PERMISSION"

        // 锁屏恢复检测阈值：超过此时间的锁屏恢复将触发VPN重连
        private const val SCREEN_LOCK_RECOVERY_THRESHOLD_MS = 300000L // 5分钟

        // Service instance management
        private var serviceInstance: ITforceVPNService? = null

        fun getInstance(): ITforceVPNService? = serviceInstance
        fun isServiceRunning(): Boolean = serviceInstance != null
    }

    // MARK: - Core Components (Simplified Architecture)

    // Core components - following iOS pattern with simplified dependencies
    private val connectionManager: ConnectionManager by inject()
    private val serverManager: ServerManager by inject()

    // VPN interface manager for TUN interface management
    private lateinit var vpnInterfaceManager: VPNInterfaceManager

    // Service scope for coroutine management
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    // Foreground service state tracking
    private var isForegroundServiceRunning = false

    // WakeLock for preventing device sleep during VPN operation
    private var wakeLock: PowerManager.WakeLock? = null

    // 锁屏恢复检测相关属性
    private var screenOffTime: Long = 0  // 记录屏幕关闭时间
    private var isScreenLocked = false   // 记录屏幕是否处于锁定状态
    private var screenStateReceiver: BroadcastReceiver? = null

    // MARK: - Unified State Management

    // Single source of truth for VPN state
    private val _vpnState = MutableStateFlow<VPNState>(VPNState.Disconnected)
    val vpnState: StateFlow<VPNState> = _vpnState.asStateFlow()

    // VPN permission state
    private val _permissionState = MutableStateFlow<VPNPermissionState>(VPNPermissionState.Unknown)
    val permissionState: StateFlow<VPNPermissionState> = _permissionState.asStateFlow()

    // MARK: - Service Lifecycle Management

    override fun onCreate() {
        super.onCreate()
        serviceInstance = this

        logInfo("VPN service created - class: ${this::class.simpleName}, process: ${android.os.Process.myPid()}")

        // CRITICAL: Start foreground service immediately to prevent system killing
        // This must be done synchronously in onCreate() before any async operations
        startForegroundServiceImmediately()

        // Initialize service components
        initializeService()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        logInfo("VPN service command received", mapOf(
            "action" to (intent?.action ?: "null"),
            "start_id" to startId,
            "flags" to flags
        ))

        // CRITICAL: Ensure foreground service is running for any command
        // This provides additional protection against system killing
        if (!isForegroundServiceRunning) {
            startForegroundServiceImmediately()
        }

        when (intent?.action) {
            ACTION_CONNECT -> {
                val serverJson = intent.getStringExtra("server")
                serverJson?.let { handleConnect(it) }
            }
            ACTION_DISCONNECT -> handleDisconnect()
            ACTION_RECONNECT -> {
                val reason = intent.getStringExtra("reason") ?: "user_initiated"
                val lockDuration = intent.getLongExtra("lock_duration_ms", 0L)
                handleReconnect(reason, lockDuration)
            }
            ACTION_CHECK_PERMISSION -> handleCheckPermission()
            else -> {
                logWarn("Unknown action received", mapOf("action" to (intent?.action ?: "null")))
            }
        }

        return START_STICKY // Service restart after being killed
    }

    override fun onDestroy() {
        super.onDestroy()

        logInfo("VPN service destroying", mapOf(
            "current_state" to _vpnState.value.name,
            "permission_state" to _permissionState.value.name
        ))

        // Clean up resources and stop operations
        cleanupResources()
        serviceInstance = null

        logInfo("VPN service destroyed")
    }

    // MARK: - Service Initialization

    /**
     * NAME: initializeService
     *
     * DESCRIPTION:
     *     Initializes VPN service components and starts background monitoring.
     *     Sets up state observation, permission checking, and foreground service.
     */
    private fun initializeService() {
        serviceScope.launch {
            try {
                // Start foreground service first to avoid ANR
                startForegroundService()

                // Check initial VPN permission state
                checkVPNPermission()

                // Initialize VPN interface manager with this VpnService instance
                vpnInterfaceManager = VPNInterfaceManager(this@ITforceVPNService)
                logInfo("VPN interface manager initialized successfully")

                // Pass VPN interface manager to ConnectionManager
                connectionManager.setVPNInterfaceManager(vpnInterfaceManager)
                logInfo("VPN interface manager set in ConnectionManager")

                // Start observing state changes from core components
                observeConnectionState()

                // Start observing server manager state changes
                observeServerManagerState()

                // Start server manager (server list URL will be set during login if needed)
                logInfo("Starting ServerManager from VPN service")
                serverManager.start()
                logInfo("ServerManager started successfully from VPN service")

                // Acquire WakeLock to prevent device sleep during VPN operation
                acquireWakeLock()

                // Start memory monitoring for GC debugging
                startMemoryMonitoring()

                // 注册屏幕状态监听器用于锁屏恢复检测
                registerScreenStateReceiver()

                logInfo("VPN service initialized successfully", mapOf(
                    "initial_state" to _vpnState.value.name,
                    "permission_state" to _permissionState.value.name,
                    "wakelock_held" to (wakeLock?.isHeld == true)
                ))

            } catch (e: Exception) {
                logError("Failed to initialize VPN service", mapOf("error" to (e.message ?: "unknown")), e)
                handleServiceError(VPNServiceError.ConfigurationInvalid("Service initialization failed: ${e.message}"))
            }
        }
    }

    // MARK: - VPN Permission Management

    /**
     * NAME: checkVPNPermission
     *
     * DESCRIPTION:
     *     Checks current VPN permission status using VpnService.prepare().
     *     Updates permission state for UI and connection management.
     */
    private suspend fun checkVPNPermission() {
        try {
            val prepareIntent = prepare(this)
            val newState = VPNPermissionState.fromVpnServicePrepare(prepareIntent)

            _permissionState.value = newState

            logInfo("VPN permission checked", mapOf(
                "permission_state" to newState.name,
                "prepare_intent_null" to (prepareIntent == null)
            ))

        } catch (e: Exception) {
            val errorState = VPNPermissionState.Error("Permission check failed: ${e.message}")
            _permissionState.value = errorState

            logError("VPN permission check failed", emptyMap(), e)
        }
    }

    /**
     * NAME: handleCheckPermission
     *
     * DESCRIPTION:
     *     Handles permission check request from external components.
     *     Used by Flutter UI to check permission status.
     */
    private fun handleCheckPermission() {
        serviceScope.launch {
            checkVPNPermission()
        }
    }

    // MARK: - Connection Management

    /**
     * NAME: handleConnect
     *
     * DESCRIPTION:
     *     Handles VPN connection request with server information.
     *     Implements permission checking and connection establishment flow.
     *
     * PARAMETERS:
     *     serverJson - JSON string containing server information
     */
    private fun handleConnect(serverJson: String) {
        serviceScope.launch {
            try {
                logInfo("VPN connection requested", mapOf("server_json_length" to serverJson.length))

                // Check VPN permission first
                // Note: Flutter layer should handle permission request before calling connect
                checkVPNPermission()
                if (!_permissionState.value.isGranted) {
                    // 如果权限仍被拒绝，提供友好的错误信息
                    val error = VPNServiceError.PermissionDenied(
                        "VPN权限被拒绝。请在系统设置中允许此应用使用VPN功能，然后重试连接。"
                    )
                    handleConnectionError(error)
                    return@launch
                }

                // Parse server information
                val server = Json.decodeFromString<ServerInfo>(serverJson)

                // Update state to connecting
                updateVPNState(VPNState.Connecting(server, com.panabit.client.connection.models.ConnectionProgress.INITIALIZING))

                // Perform connection through ConnectionManager
                val result = connectionManager.connect(server)

                result.fold(
                    onSuccess = {
                        logInfo("VPN connection successful", mapOf(
                            "server_id" to server.id,
                            "server_name" to server.displayName
                        ))
                    },
                    onFailure = { error ->
                        logError("VPN connection failed", mapOf(
                            "server_id" to server.id,
                            "error" to (error.message ?: "unknown")
                        ), error)

                        val vpnError = when (error) {
                            is VPNServiceError -> error
                            else -> VPNServiceError.ConnectionFailed("Connection failed: ${error.message}")
                        }
                        handleConnectionError(vpnError)
                    }
                )

            } catch (e: Exception) {
                logError("Failed to handle connect request", mapOf("server_json" to serverJson), e)
                handleConnectionError(VPNServiceError.ConnectionFailed("Connect request failed: ${e.message}"))
            }
        }
    }

    /**
     * NAME: handleDisconnect
     *
     * DESCRIPTION:
     *     Handles VPN disconnection request.
     *     Implements clean disconnection with proper state management.
     */
    private fun handleDisconnect() {
        serviceScope.launch {
            try {
                logInfo("VPN disconnection requested", mapOf(
                    "current_state" to _vpnState.value.name
                ))

                // Update state to disconnecting
                updateVPNState(VPNState.Disconnecting)

                // Perform disconnection through ConnectionManager
                val result = connectionManager.disconnect()

                result.fold(
                    onSuccess = {
                        logInfo("VPN disconnection successful")
                        updateVPNState(VPNState.Disconnected)
                    },
                    onFailure = { error ->
                        logError("VPN disconnection failed", emptyMap(), error)
                        // Force disconnected state even if disconnect failed
                        updateVPNState(VPNState.Disconnected)
                    }
                )

            } catch (e: Exception) {
                logError("Failed to handle disconnect request", emptyMap(), e)
                // Force disconnected state on error
                updateVPNState(VPNState.Disconnected)
            }
        }
    }

    /**
     * NAME: handleReconnect
     *
     * DESCRIPTION:
     *     Handles VPN reconnection request.
     *     Implements reconnection with current or best available server.
     *     Supports different reconnection reasons including screen lock recovery.
     *
     * PARAMETERS:
     *     reason - Reason for reconnection (user_initiated, screen_lock_recovery, etc.)
     *     lockDuration - Duration of screen lock in milliseconds (for screen_lock_recovery)
     */
    private fun handleReconnect(reason: String = "user_initiated", lockDuration: Long = 0L) {
        serviceScope.launch {
            try {
                val logData = mutableMapOf<String, Any>(
                    "current_state" to _vpnState.value.name,
                    "reason" to reason
                )

                if (reason == "screen_lock_recovery" && lockDuration > 0) {
                    logData["lock_duration_ms"] = lockDuration
                    logData["lock_duration_minutes"] = (lockDuration / 60000.0)
                }

                logInfo("VPN reconnection requested", logData)

                // Get current or best server for reconnection
                val server = _vpnState.value.connectedServer ?: serverManager.selectBestServer()

                if (server == null) {
                    val error = VPNServiceError.ServerUnavailable("No server available for reconnection")
                    handleConnectionError(error)
                    return@launch
                }

                // Since we removed Reconnecting state, transition directly to Connecting
                val reconnectReason = when (reason) {
                    "screen_lock_recovery" -> "Screen lock recovery"
                    "heartbeat_timeout" -> "Heartbeat timeout"
                    "network_change" -> "Network change"
                    else -> "User initiated"
                }
                updateVPNState(VPNState.Connecting(server, ConnectionProgress.INITIALIZING))

                // Perform reconnection through ConnectionManager
                val result = connectionManager.connect(server) // Use connect instead of reconnect

                result.fold(
                    onSuccess = {
                        logInfo("VPN reconnection successful", mapOf(
                            "server_id" to server.id,
                            "server_name" to server.displayName
                        ))
                    },
                    onFailure = { error ->
                        logError("VPN reconnection failed", mapOf(
                            "server_id" to server.id,
                            "error" to (error.message ?: "unknown")
                        ), error)

                        val vpnError = when (error) {
                            is VPNServiceError -> error
                            else -> VPNServiceError.ReconnectionFailed("Reconnection failed: ${error.message}", 1, error.message)
                        }
                        handleConnectionError(vpnError)
                    }
                )

            } catch (e: Exception) {
                logError("Failed to handle reconnect request", emptyMap(), e)
                handleConnectionError(VPNServiceError.ReconnectionFailed("Reconnect request failed: ${e.message}", 1, e.message))
            }
        }
    }

    // MARK: - Unified State Management

    /**
     * NAME: updateVPNState
     *
     * DESCRIPTION:
     *     Updates unified VPN state and handles state-specific logic.
     *     Provides single point for state transitions to ensure consistency.
     *
     * PARAMETERS:
     *     newState - New VPN state to transition to
     */
    private fun updateVPNState(newState: VPNState) {
        val oldState = _vpnState.value

        // State deduplication: Skip if state hasn't actually changed
        if (oldState == newState) {
            return
        }

        // Validate state transition
        if (!VPNState.isValidStateTransition(oldState, newState)) {
            logWarn("Invalid state transition blocked", mapOf(
                "from_state" to oldState.name,
                "to_state" to newState.name
            ))
            return
        }

        // Update state
        _vpnState.value = newState

        logInfo("VPN state transition", mapOf(
            "from_state" to oldState.name,
            "to_state" to newState.name,
            "is_operation_in_progress" to newState.isOperationInProgress,
            "is_connected" to newState.isConnected
        ))

        // Handle state-specific logic
        handleStateTransition(oldState, newState)

        // Update notification
        updateNotification(newState)

        // Send state to Flutter (if available)
        sendStateToFlutter(newState)
    }

    /**
     * NAME: handleStateTransition
     *
     * DESCRIPTION:
     *     Handles state-specific logic during state transitions.
     *     Implements side effects and cleanup for different states.
     *
     * PARAMETERS:
     *     oldState - Previous VPN state
     *     newState - New VPN state
     */
    private fun handleStateTransition(oldState: VPNState, newState: VPNState) {
        when (newState) {
            is VPNState.Connected -> {
                logInfo("VPN connection established", mapOf(
                    "server_id" to newState.server.id,
                    "server_name" to newState.server.displayName,
                    "tunnel_ip" to newState.tunnelIP,
                    "connected_at" to newState.connectedAt
                ))
            }
            is VPNState.Disconnected -> {
                logInfo("VPN disconnected", mapOf(
                    "previous_state" to oldState.name
                ))
            }
            is VPNState.Error -> {
                logError("VPN error state - type: ${newState.error::class.simpleName}, message: ${newState.error.message}, last_server: ${newState.lastServer?.id ?: "none"}")
            }
            else -> {
                // Other states don't require special handling
            }
        }
    }

    /**
     * NAME: handleConnectionError
     *
     * DESCRIPTION:
     *     Handles connection errors with unified error management.
     *     Updates state and provides error recovery guidance.
     *
     * PARAMETERS:
     *     error - VPN service error to handle
     */
    private fun handleConnectionError(error: VPNServiceError) {
        val currentServer = _vpnState.value.connectedServer
        updateVPNState(VPNState.Error(error, currentServer))
    }

    /**
     * NAME: handleServiceError
     *
     * DESCRIPTION:
     *     Handles service-level errors that affect overall service operation.
     *
     * PARAMETERS:
     *     error - Service error to handle
     */
    private fun handleServiceError(error: VPNServiceError) {
        logError("Service error occurred", mapOf(
            "error_type" to (error::class.simpleName ?: "unknown"),
            "error_message" to (error.message ?: "unknown")
        ))

        updateVPNState(VPNState.Error(error))
    }

    // MARK: - State Observation

    /**
     * NAME: observeConnectionState
     *
     * DESCRIPTION:
     *     Observes ConnectionManager state changes and synchronizes with VPN state.
     *     Implements unified state management by bridging component states.
     */
    private fun observeConnectionState() {
        serviceScope.launch {
            connectionManager.state.collect { connectionState ->
                // Synchronize ConnectionManager state with VPN state
                // Only update if the state actually represents a change
                if (connectionState != _vpnState.value) {
                    val oldState = _vpnState.value
                    _vpnState.value = connectionState

                    // Handle state-specific logic with correct parameters
                    handleStateTransition(oldState, connectionState)

                    // Update notification and Flutter
                    updateNotification(connectionState)
                    sendStateToFlutter(connectionState)
                }
            }
        }
    }

    /**
     * NAME: observeServerManagerState
     *
     * DESCRIPTION:
     *     Observes ServerManager state changes and sends updates to Flutter.
     *     Monitors server list updates and ping results for UI synchronization.
     */
    private fun observeServerManagerState() {
        serviceScope.launch {
            // Observe server list changes
            serverManager.servers.collect { servers ->
                try {
                    val platformHandler = MainActivity.getPlatformChannelHandler()
                    if (platformHandler != null) {
                        // Send server list update to Flutter
                        val serverData = servers.map { it.toMap() }
                        platformHandler.sendEvent("servers", serverData)
                        logDebug("Server list sent to Flutter", mapOf("server_count" to servers.size))
                    }
                } catch (e: Exception) {
                    logWarn("Failed to send server list to Flutter", emptyMap(), e)
                }
            }
        }

        // Note: Ping results events are now handled by unified VPNServiceAdapter.pingServers() method
        // This eliminates duplicate event sending and provides better control over event timing
    }

    // MARK: - Foreground Service and Notifications

    /**
     * NAME: startForegroundServiceImmediately
     *
     * DESCRIPTION:
     *     Immediately starts foreground service to prevent system from killing the VPN service.
     *     This MUST be called synchronously in onCreate() or onStartCommand() to avoid ANR.
     *     Creates notification channel and initial notification with minimal delay.
     */
    private fun startForegroundServiceImmediately() {
        if (isForegroundServiceRunning) {
            logDebug("Foreground service already running, skipping duplicate start")
            return
        }

        try {
            val notification = createVPNNotification(_vpnState.value)

            // For Android 14+ (API 34+), specify foreground service type
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                startForeground(
                    NOTIFICATION_ID,
                    notification,
                    android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_SPECIAL_USE
                )
            } else {
                startForeground(NOTIFICATION_ID, notification)
            }

            isForegroundServiceRunning = true

            logInfo("Foreground service started immediately", mapOf(
                "notification_id" to NOTIFICATION_ID,
                "channel_id" to NOTIFICATION_CHANNEL_ID,
                "api_level" to android.os.Build.VERSION.SDK_INT
            ))

        } catch (e: Exception) {
            logError("Failed to start foreground service immediately", mapOf("error" to (e.message ?: "unknown")), e)
            // Don't throw - service must continue to avoid crash
        }
    }

    /**
     * NAME: startForegroundService
     *
     * DESCRIPTION:
     *     Starts foreground service to prevent system from killing the VPN service.
     *     Creates notification channel and initial notification.
     *     This is the async version used during initialization.
     */
    private fun startForegroundService() {
        startForegroundServiceImmediately()
    }

    /**
     * NAME: createVPNNotification
     *
     * DESCRIPTION:
     *     Creates VPN notification based on current state.
     *     Provides user-friendly status information and basic interaction.
     *
     * PARAMETERS:
     *     state - Current VPN state for notification content
     *
     * RETURNS:
     *     Notification - Configured notification for foreground service
     */
    private fun createVPNNotification(state: VPNState): Notification {
        // Create notification channel (Android 8.0+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                "Panabit Client VPN Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Panabit Client VPN connection status"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }

        // Create notification content based on state
        val (title, text, icon) = getNotificationContent(state)

        // Create intent to open main activity
        val openAppIntent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        val openAppPendingIntent = PendingIntent.getActivity(
            this, 0, openAppIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Build notification
        return NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(text)
            .setSmallIcon(icon)
            .setContentIntent(openAppPendingIntent)
            .setOngoing(true)
            .setAutoCancel(false)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
    }

    /**
     * NAME: getNotificationContent
     *
     * DESCRIPTION:
     *     Gets notification content based on VPN state.
     *
     * PARAMETERS:
     *     state - Current VPN state
     *
     * RETURNS:
     *     Triple<String, String, Int> - Title, text, and icon resource ID
     */
    private fun getNotificationContent(state: VPNState): Triple<String, String, Int> {
        return when (state) {
            is VPNState.Disconnected -> Triple(
                "Panabit Client",
                "VPN disconnected",
                R.drawable.ic_notification
            )
            is VPNState.Connecting -> Triple(
                "Panabit Client",
                "Connecting to ${state.server.displayName}...",
                R.drawable.ic_notification
            )
            is VPNState.Connected -> Triple(
                "Panabit Client",
                "Connected to ${state.server.displayName}",
                R.drawable.ic_notification
            )
            is VPNState.Disconnecting -> Triple(
                "Panabit Client",
                "Disconnecting...",
                R.drawable.ic_notification
            )

            is VPNState.Error -> Triple(
                "Panabit Client",
                "Connection error: ${state.error.message}",
                R.drawable.ic_notification
            )
        }
    }

    /**
     * NAME: updateNotification
     *
     * DESCRIPTION:
     *     Updates foreground service notification based on VPN state.
     *
     * PARAMETERS:
     *     state - Current VPN state for notification update
     */
    private fun updateNotification(state: VPNState) {
        try {
            val notification = createVPNNotification(state)
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.notify(NOTIFICATION_ID, notification)

        } catch (e: Exception) {
            logError("Failed to update notification", mapOf("state" to state.name), e)
        }
    }

    /**
     * NAME: sendStateToFlutter
     *
     * DESCRIPTION:
     *     Sends VPN state to Flutter UI through platform channel.
     *     Handles communication errors gracefully.
     *
     * PARAMETERS:
     *     state - VPN state to send to Flutter
     */
    private fun sendStateToFlutter(state: VPNState) {
        try {
            // Send state to Flutter through PlatformChannelHandler
            val platformHandler = MainActivity.getPlatformChannelHandler()
            if (platformHandler != null) {
                platformHandler.sendStatusEvent(state)
                logDebug("State sent to Flutter successfully", mapOf(
                    "state" to state.name,
                    "flutter_status" to state.flutterStatusString
                ))

                // When connected, send updated interface info with tunnel IP
                if (state is VPNState.Connected) {
                    serviceScope.launch {
                        try {
                            // Get updated interface info with tunnel IP
                            val vpnServiceAdapter = VPNServiceAdapter(this@ITforceVPNService, applicationContext)
                            val interfaceInfo = vpnServiceAdapter.getInterfaceInfo()

                            // Send interface_info event with tunnel IP
                            platformHandler.sendInterfaceInfoEvent(interfaceInfo)
                            logDebug("Interface info with tunnel IP sent to Flutter", mapOf<String, Any>(
                                "tunnel_ip" to state.tunnelIP,
                                "interface_name" to (interfaceInfo.interfaceName ?: ""),
                                "local_ip" to (interfaceInfo.localIp ?: "")
                            ), null)
                        } catch (e: Exception) {
                            logWarn("Failed to send interface info after connection", mapOf("error" to (e.message ?: "unknown")), e)
                        }
                    }
                }
            } else {
                logDebug("PlatformChannelHandler not available, state not sent", mapOf(
                    "state" to state.name
                ))
            }

        } catch (e: Exception) {
            // Ignore Flutter communication errors to avoid affecting VPN operation
            logWarn("Failed to send state to Flutter", mapOf("state" to state.name), e)
        }
    }

    // MARK: - Resource Management

    /**
     * NAME: cleanupResources
     *
     * DESCRIPTION:
     *     Cleans up service resources and stops background operations.
     *     Ensures proper resource disposal when service is destroyed.
     */
    private fun cleanupResources() {
        logInfo("Cleaning up VPN service resources")

        try {
            // Cancel service scope to stop all coroutines
            serviceScope.cancel()

            // Release WakeLock
            releaseWakeLock()

            // 取消注册屏幕状态监听器
            unregisterScreenStateReceiver()

            // Stop server manager
            serviceScope.launch {
                try {
                    serverManager.stop()
                } catch (e: Exception) {
                    logError("Failed to stop server manager", mapOf("error" to (e.message ?: "unknown")), e)
                }
            }

            // Disconnect VPN if connected
            if (_vpnState.value.isConnected || _vpnState.value.isOperationInProgress) {
                serviceScope.launch {
                    try {
                        connectionManager.disconnect()
                    } catch (e: Exception) {
                        logError("Failed to disconnect during cleanup", mapOf("error" to (e.message ?: "unknown")), e)
                    }
                }
            }

            // Stop foreground service
            if (isForegroundServiceRunning) {
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                    stopForeground(STOP_FOREGROUND_REMOVE)
                } else {
                    @Suppress("DEPRECATION")
                    stopForeground(true)
                }
                isForegroundServiceRunning = false
                logInfo("Foreground service stopped")
            }

            logInfo("VPN service resources cleaned up successfully")

        } catch (e: Exception) {
            logError("Error during resource cleanup", mapOf("error" to (e.message ?: "unknown")), e)
        }
    }

    // MARK: - Public API for External Access

    /**
     * NAME: getCurrentState
     *
     * DESCRIPTION:
     *     Gets current VPN state for external components.
     *
     * RETURNS:
     *     VPNState - Current VPN state
     */
    fun getCurrentState(): VPNState = _vpnState.value

    /**
     * NAME: getPermissionState
     *
     * DESCRIPTION:
     *     Gets current VPN permission state for external components.
     *
     * RETURNS:
     *     VPNPermissionState - Current permission state
     */
    fun getPermissionState(): VPNPermissionState = _permissionState.value

    // MARK: - Memory Monitoring

    /**
     * NAME: startMemoryMonitoring
     *
     * DESCRIPTION:
     *     启动内存监控，用于调试GC问题
     */
    private fun startMemoryMonitoring() {
        serviceScope.launch {
            while (isActive) {
                try {
                    logMemoryUsage("VPN Service")
                    delay(15000L) // 每15秒记录一次内存使用
                } catch (e: Exception) {
                    logError("Memory monitoring error", mapOf("error" to (e.message ?: "unknown")))
                    delay(30000L) // 出错时延长间隔
                }
            }
        }
    }

    /**
     * NAME: logMemoryUsage
     *
     * DESCRIPTION:
     *     记录当前内存使用情况
     */
    private fun logMemoryUsage(context: String) {
        try {
            val runtime = Runtime.getRuntime()
            val maxMemory = runtime.maxMemory()
            val totalMemory = runtime.totalMemory()
            val freeMemory = runtime.freeMemory()
            val usedMemory = totalMemory - freeMemory
            val availableMemory = maxMemory - usedMemory

            logInfo("Memory usage [$context]", mapOf(
                "used_mb" to (usedMemory / 1024 / 1024),
                "total_mb" to (totalMemory / 1024 / 1024),
                "max_mb" to (maxMemory / 1024 / 1024),
                "free_mb" to (freeMemory / 1024 / 1024),
                "available_mb" to (availableMemory / 1024 / 1024),
                "memory_usage_percent" to ((usedMemory * 100) / maxMemory),
                "vpn_state" to _vpnState.value.name
            ))

            // 如果内存使用超过85%，记录警告并建议GC
            if (usedMemory * 100 / maxMemory > 85) {
                logWarn("High memory usage detected - suggesting GC", mapOf(
                    "context" to context,
                    "usage_percent" to ((usedMemory * 100) / maxMemory),
                    "used_mb" to (usedMemory / 1024 / 1024)
                ))

                // 建议系统进行垃圾回收
                System.gc()
            }
        } catch (e: Exception) {
            logError("Failed to log memory usage", mapOf(
                "context" to context,
                "error" to (e.message ?: "unknown")
            ))
        }
    }

    // MARK: - WakeLock Management

    /**
     * NAME: acquireWakeLock
     *
     * DESCRIPTION:
     *     Acquires WakeLock to prevent device sleep during VPN operation.
     *     This ensures VPN service continues running even when device goes to sleep.
     */
    private fun acquireWakeLock() {
        try {
            if (wakeLock?.isHeld == true) {
                logDebug("WakeLock already held, skipping acquisition")
                return
            }

            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                "ITforceVPN::VPNService"
            ).apply {
                acquire()
            }

            logInfo("WakeLock acquired for VPN service", mapOf(
                "wakelock_held" to (wakeLock?.isHeld == true)
            ))

        } catch (e: Exception) {
            logError("Failed to acquire WakeLock", mapOf("error" to (e.message ?: "unknown")), e)
        }
    }

    /**
     * NAME: releaseWakeLock
     *
     * DESCRIPTION:
     *     Releases WakeLock when VPN service is no longer needed.
     *     Should be called during service cleanup.
     */
    private fun releaseWakeLock() {
        try {
            wakeLock?.let { lock ->
                if (lock.isHeld) {
                    lock.release()
                    logInfo("WakeLock released")
                } else {
                    logDebug("WakeLock was not held, no release needed")
                }
            }
            wakeLock = null

        } catch (e: Exception) {
            logError("Failed to release WakeLock", mapOf("error" to (e.message ?: "unknown")), e)
        }
    }

    // MARK: - Socket Protection (Server IP Exclusion)

    /**
     * NAME: protectUDPSocket
     *
     * DESCRIPTION:
     *     Protects UDP socket from VPN routing to prevent routing loops.
     *     Called when creating UDP sockets for authentication, heartbeat, and data transmission.
     *     Equivalent to iOS excludedIPs functionality.
     *
     *     NOTE: If current app is in VPN disallowed list (addDisallowedApplication),
     *     socket protection is not needed as app traffic won't enter VPN tunnel.
     *
     * PARAMETERS:
     *     socket - DatagramSocket to protect
     *
     * RETURNS:
     *     Boolean - true if protection was applied or not needed, false if protection failed
     */
    fun protectUDPSocket(socket: java.net.DatagramSocket): Boolean {
        return try {
            // Check if current app is in VPN disallowed list
            // If so, socket protection is not needed
            if (isAppInDisallowedList()) {
                logDebug("UDP socket protection skipped - app in VPN disallowed list", mapOf(
                    "optimization" to "no_protect_call_needed",
                    "reason" to "app_traffic_excluded_from_vpn"
                ))
                return true
            }

            // Fallback to socket protection if app not in disallowed list
            val result = protect(socket)
            if (result) {
                logDebug("UDP socket protected from VPN routing via protect() method")
            } else {
                logWarn("Failed to protect UDP socket from VPN routing")
            }
            result
        } catch (e: Exception) {
            logError("UDP socket protection failed", mapOf("error" to (e.message ?: "unknown")))
            false
        }
    }

    /**
     * NAME: isAppInDisallowedList
     *
     * DESCRIPTION:
     *     Checks if current application is in VPN disallowed list.
     *     This is used to optimize socket protection by skipping protect() calls
     *     when app traffic is already excluded from VPN tunnel.
     *
     * RETURNS:
     *     Boolean - true if app is in disallowed list, false otherwise
     */
    private fun isAppInDisallowedList(): Boolean {
        return try {
            // Check if VPN interface manager has configured app disallowance
            val interfaceManager = vpnInterfaceManager
            if (interfaceManager != null) {
                // VPN interface manager tracks if app was added to disallowed list
                val isDisallowed = interfaceManager.isCurrentAppDisallowed()
                logDebug("App disallowed status check", mapOf(
                    "is_disallowed" to isDisallowed,
                    "vpn_interface_manager_available" to true
                ))
                return isDisallowed
            } else {
                logDebug("VPN interface manager not available for disallowed check")
                return false
            }
        } catch (e: Exception) {
            logWarn("Failed to check app disallowed status", mapOf("error" to (e.message ?: "unknown")))
            false
        }
    }

    // MARK: - Screen Lock Recovery Detection

    /**
     * NAME: registerScreenStateReceiver
     *
     * DESCRIPTION:
     *     注册屏幕状态监听器，用于检测锁屏恢复事件
     */
    private fun registerScreenStateReceiver() {
        try {
            screenStateReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    when (intent?.action) {
                        Intent.ACTION_SCREEN_OFF -> handleScreenOff()
                        Intent.ACTION_SCREEN_ON -> handleScreenOn()
                    }
                }
            }

            val filter = IntentFilter().apply {
                addAction(Intent.ACTION_SCREEN_OFF)
                addAction(Intent.ACTION_SCREEN_ON)
            }

            registerReceiver(screenStateReceiver, filter)
            logInfo("Screen state receiver registered for lock recovery detection")

        } catch (e: Exception) {
            logError("Failed to register screen state receiver", mapOf("error" to (e.message ?: "unknown")), e)
        }
    }

    /**
     * NAME: unregisterScreenStateReceiver
     *
     * DESCRIPTION:
     *     取消注册屏幕状态监听器
     */
    private fun unregisterScreenStateReceiver() {
        try {
            screenStateReceiver?.let { receiver ->
                unregisterReceiver(receiver)
                screenStateReceiver = null
                logInfo("Screen state receiver unregistered")
            }
        } catch (e: Exception) {
            logError("Failed to unregister screen state receiver", mapOf("error" to (e.message ?: "unknown")), e)
        }
    }

    /**
     * NAME: handleScreenOff
     *
     * DESCRIPTION:
     *     处理屏幕关闭事件，记录锁屏时间
     */
    private fun handleScreenOff() {
        val currentTime = System.currentTimeMillis()
        screenOffTime = currentTime
        isScreenLocked = true

        logDebug("Screen turned off - recording lock time", mapOf(
            "screen_off_time" to currentTime,
            "current_vpn_state" to _vpnState.value.flutterStatusString
        ))
    }

    /**
     * NAME: handleScreenOn
     *
     * DESCRIPTION:
     *     处理屏幕开启事件，检测长时间锁屏恢复并触发重连
     */
    private fun handleScreenOn() {
        val currentTime = System.currentTimeMillis()
        val lockDuration = if (isScreenLocked && screenOffTime > 0) {
            currentTime - screenOffTime
        } else {
            0L
        }

        logDebug("Screen turned on - checking for lock recovery", mapOf(
            "lock_duration_ms" to lockDuration,
            "threshold_ms" to SCREEN_LOCK_RECOVERY_THRESHOLD_MS,
            "was_locked" to isScreenLocked,
            "current_vpn_state" to _vpnState.value.flutterStatusString
        ))

        // 检查是否需要触发锁屏恢复重连
        if (isScreenLocked && lockDuration > SCREEN_LOCK_RECOVERY_THRESHOLD_MS) {
            handleLongScreenLockRecovery(lockDuration)
        }

        // 重置锁屏状态
        isScreenLocked = false
        screenOffTime = 0L
    }

    /**
     * NAME: handleLongScreenLockRecovery
     *
     * DESCRIPTION:
     *     处理长时间锁屏恢复，通过事件通知UI层进行重连
     *     与心跳超时、网络接口变化的处理方式保持一致
     *
     * PARAMETERS:
     *     lockDuration - 锁屏持续时间（毫秒）
     */
    private fun handleLongScreenLockRecovery(lockDuration: Long) {
        logInfo("Long screen lock recovery detected - notifying UI for reconnection", mapOf(
            "lock_duration_ms" to lockDuration,
            "lock_duration_minutes" to (lockDuration / 60000.0),
            "current_vpn_state" to _vpnState.value.flutterStatusString
        ))

        // 只有在VPN连接状态下才发送重连通知
        val currentState = _vpnState.value
        if (currentState.isConnected) {
            serviceScope.launch {
                try {
                    val server = currentState.connectedServer
                    if (server != null) {
                        // 构建网络信息，包含锁屏恢复的详细信息
                        val networkInfo = mapOf(
                            "lock_duration_ms" to lockDuration,
                            "lock_duration_minutes" to (lockDuration / 60000.0),
                            "server_id" to server.id,
                            "server_name" to server.name,
                            "current_state" to currentState.flutterStatusString,
                            "recovery_trigger" to "screen_unlock_detection"
                        )

                        // 通过ConnectionManager发送重连事件到UI层
                        val vpnStateEventSender = connectionManager.getVPNStateEventSender()
                        vpnStateEventSender.sendReconnectRequiredEvent(
                            reason = com.panabit.client.connection.flutter.VPNStateEventSender.RECONNECT_REASON_SCREEN_LOCK_RECOVERY,
                            message = "Screen lock recovery detected after ${lockDuration / 60000.0} minutes, VPN reconnection required to restore network connectivity",
                            networkInfo = networkInfo
                        )

                        logInfo("Sent screen lock recovery reconnect notification to UI", mapOf(
                            "reason" to com.panabit.client.connection.flutter.VPNStateEventSender.RECONNECT_REASON_SCREEN_LOCK_RECOVERY,
                            "lock_duration_minutes" to (lockDuration / 60000.0)
                        ))
                    } else {
                        logError("Screen lock recovery detected but no server info available")
                    }
                } catch (e: Exception) {
                    logError("Failed to send screen lock recovery notification", mapOf("error" to (e.message ?: "unknown")), e)
                }
            }
        } else {
            logDebug("VPN not connected - skipping screen lock recovery notification")
        }
    }
}
