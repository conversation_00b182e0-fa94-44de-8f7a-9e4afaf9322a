/**
 * FILE: ServerManagerFactory.kt
 *
 * DESCRIPTION:
 *     Factory for creating ServerManager instances with proper dependency injection.
 *     Provides configured ServerManagerImpl instances for VPN service integration.
 *
 * AUTHOR: wei
 * HISTORY: 11/07/2025 create
 */

package com.panabit.client.connection

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.content.Context
import com.panabit.client.connection.models.ServerConfiguration
import com.panabit.client.network.UDPConnection
import com.panabit.client.protocol.SDWANProtocol
import kotlinx.coroutines.CoroutineScope

/**
 * NAME: ServerManagerFactory
 *
 * DESCRIPTION:
 *     Factory class for creating ServerManager instances.
 *     Handles dependency injection and configuration setup.
 *     Provides both default and custom configuration options.
 */
object ServerManagerFactory {
    
    /**
     * NAME: create
     *
     * DESCRIPTION:
     *     Creates ServerManager instance with default configuration.
     *
     * PARAMETERS:
     *     context - Android context
     *     logger - Logging manager instance
     *     serviceScope - Coroutine scope for background operations
     *
     * RETURNS:
     *     ServerManager - Configured server manager instance
     */
    fun create(
        context: Context,
        serviceScope: CoroutineScope
    ): ServerManager {
        return create(context, serviceScope, ServerConfiguration.default)
    }
    
    /**
     * NAME: create
     *
     * DESCRIPTION:
     *     Creates ServerManager instance with custom configuration.
     *
     * PARAMETERS:
     *     context - Android context
     *     logger - Logging manager instance
     *     serviceScope - Coroutine scope for background operations
     *     configuration - Server manager configuration
     *
     * RETURNS:
     *     ServerManager - Configured server manager instance
     */
    fun create(
        context: Context,
        serviceScope: CoroutineScope,
        configuration: ServerConfiguration
    ): ServerManager {
        
        // Create UDP connection factory
        // Note: This factory is only called from IO context in performPing method
        val udpConnectionFactory: (String, Int) -> UDPConnection = { host, port ->
            try {
                val serverAddress = java.net.InetAddress.getByName(host)
                UDPConnection(serverAddress, port)
            } catch (e: Exception) {
                logError("Failed to create UDP connection", mapOf(
                    "host" to host,
                    "port" to port,
                    "error" to (e.message ?: "unknown")
                ), e)
                throw e
            }
        }
        
        // Create SDWAN protocol factory
        val sdwanProtocolFactory: (String, Int) -> SDWANProtocol = { host, port ->
            try {
                SDWANProtocol(host, port)
            } catch (e: Exception) {
                logError("Failed to create SDWAN protocol", mapOf(
                    "host" to host,
                    "port" to port,
                    "error" to (e.message ?: "unknown")
                ), e)
                throw e
            }
        }
        
        return ServerManagerImpl(
            context = context,
            udpConnectionFactory = udpConnectionFactory,
            sdwanProtocolFactory = sdwanProtocolFactory,
            serviceScope = serviceScope,
            configuration = configuration
        )
    }
    
    /**
     * NAME: createWithCustomFactories
     *
     * DESCRIPTION:
     *     Creates ServerManager instance with custom factory functions.
     *     Useful for testing and dependency injection frameworks.
     *
     * PARAMETERS:
     *     context - Android context
     *     udpConnectionFactory - Factory for creating UDP connections
     *     sdwanProtocolFactory - Factory for creating SDWAN protocol instances
     *     logger - Logging manager instance
     *     serviceScope - Coroutine scope for background operations
     *     configuration - Server manager configuration
     *
     * RETURNS:
     *     ServerManager - Configured server manager instance
     */
    fun createWithCustomFactories(
        context: Context,
        udpConnectionFactory: (String, Int) -> UDPConnection,
        sdwanProtocolFactory: (String, Int) -> SDWANProtocol,
        serviceScope: CoroutineScope,
        configuration: ServerConfiguration = ServerConfiguration.default
    ): ServerManager {
        return ServerManagerImpl(
            context = context,
            udpConnectionFactory = udpConnectionFactory,
            sdwanProtocolFactory = sdwanProtocolFactory,
            serviceScope = serviceScope,
            configuration = configuration
        )
    }
}
