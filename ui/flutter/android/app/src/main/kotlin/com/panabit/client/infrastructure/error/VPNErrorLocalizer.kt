/**
 * FILE: VPNErrorLocalizer.kt
 *
 * DESCRIPTION:
 *     VPN错误消息本地化器，支持中英文错误消息和动态语言切换
 *     与Windows版本的国际化机制保持一致
 *
 * AUTHOR: wei
 * HISTORY: 24/07/2025 create - 立即实施Android错误处理改进
 */

package com.panabit.client.infrastructure.error

import java.util.Locale
import java.util.concurrent.ConcurrentHashMap

/**
 * NAME: VPNErrorLocalizer
 *
 * DESCRIPTION:
 *     VPN错误消息本地化器，提供多语言错误消息支持
 *     使用单例模式确保性能，支持动态语言切换
 */
class VPNErrorLocalizer private constructor(private val locale: Locale) {
    
    companion object {
        // 实例缓存，按语言代码缓存
        private val instances = ConcurrentHashMap<String, VPNErrorLocalizer>()
        
        /**
         * NAME: getInstance
         *
         * DESCRIPTION:
         *     获取指定语言的本地化器实例
         *
         * PARAMETERS:
         *     locale - 目标语言环境，默认使用系统语言
         *
         * RETURNS:
         *     VPNErrorLocalizer - 本地化器实例
         */
        fun getInstance(locale: Locale = Locale.getDefault()): VPNErrorLocalizer {
            val key = locale.language
            return instances.getOrPut(key) { VPNErrorLocalizer(locale) }
        }

        // 英文错误消息模板（与Windows版本保持一致）
        private val englishMessages = mapOf(
            // General errors
            VPNErrorCode.UNKNOWN to "Unknown error occurred",
            VPNErrorCode.INTERNAL to "Internal system error",
            VPNErrorCode.INVALID_ARGUMENT to "Invalid parameter provided",
            VPNErrorCode.NOT_FOUND to "Requested resource not found",
            VPNErrorCode.PERMISSION_DENIED to "Permission denied",
            VPNErrorCode.UNAUTHENTICATED to "Authentication required",
            VPNErrorCode.UNAVAILABLE to "Service temporarily unavailable",

            // Network errors
            VPNErrorCode.NETWORK_UNREACHABLE to "Network unreachable, please check your internet connection",
            VPNErrorCode.NETWORK_TIMEOUT to "Network operation timed out, please try again",
            VPNErrorCode.NETWORK_DNS_FAILURE to "DNS resolution failed, please check server address",
            VPNErrorCode.NETWORK_CONNECTION_RESET to "Network connection was reset",
            VPNErrorCode.NETWORK_CONNECTION_CLOSED to "Network connection was closed",
            VPNErrorCode.NETWORK_PROXY_ERROR to "Proxy server error",
            VPNErrorCode.NETWORK_TLS_ERROR to "TLS/SSL connection error",

            // Authentication errors
            VPNErrorCode.AUTH_INVALID_CREDENTIALS to "Invalid username or password",
            VPNErrorCode.AUTH_EXPIRED_CREDENTIALS to "Your credentials have expired, please login again",
            VPNErrorCode.AUTH_RATE_LIMITED to "Too many authentication attempts, please wait and try again",
            VPNErrorCode.AUTH_ACCOUNT_LOCKED to "Account has been locked, please contact administrator",
            VPNErrorCode.AUTH_TOKEN_INVALID to "Invalid authentication token",
            VPNErrorCode.AUTH_TOKEN_EXPIRED to "Authentication token has expired, please login again",
            VPNErrorCode.AUTH_MISSING_CREDENTIALS to "Authentication credentials are missing",

            // Tunnel errors
            VPNErrorCode.TUNNEL_INIT_FAILED to "Failed to initialize VPN tunnel",
            VPNErrorCode.TUNNEL_CLOSED_UNEXPECTED to "VPN tunnel closed unexpectedly",
            VPNErrorCode.TUNNEL_DEVICE_ERROR to "VPN device error occurred",
            VPNErrorCode.TUNNEL_ROUTE_ERROR to "VPN routing configuration error",
            VPNErrorCode.TUNNEL_ENCRYPTION_ERROR to "VPN encryption error",

            // Configuration errors
            VPNErrorCode.CONFIG_INVALID to "Invalid VPN configuration",
            VPNErrorCode.CONFIG_MISSING to "VPN configuration is missing",
            VPNErrorCode.CONFIG_PERMISSION_DENIED to "Permission denied to access configuration",

            // Platform errors
            VPNErrorCode.PLATFORM_PERMISSION_DENIED to "VPN permission denied, please grant VPN access in system settings",
            VPNErrorCode.PLATFORM_UNSUPPORTED to "This platform is not supported",
            VPNErrorCode.PLATFORM_SYSTEM_ERROR to "System error occurred",

            // Protocol errors
            VPNErrorCode.PROTOCOL_HANDSHAKE_FAILED to "VPN protocol handshake failed",
            VPNErrorCode.PROTOCOL_ENCRYPTION_ERROR to "Protocol encryption error",
            VPNErrorCode.PROTOCOL_DECRYPTION_ERROR to "Protocol decryption error"
        )

        // 中文错误消息模板（与Windows版本保持一致）
        private val chineseMessages = mapOf(
            // General errors
            VPNErrorCode.UNKNOWN to "发生未知错误",
            VPNErrorCode.INTERNAL to "系统内部错误",
            VPNErrorCode.INVALID_ARGUMENT to "提供的参数无效",
            VPNErrorCode.NOT_FOUND to "请求的资源未找到",
            VPNErrorCode.PERMISSION_DENIED to "权限被拒绝",
            VPNErrorCode.UNAUTHENTICATED to "需要身份验证",
            VPNErrorCode.UNAVAILABLE to "服务暂时不可用",

            // Network errors
            VPNErrorCode.NETWORK_UNREACHABLE to "网络不可达，请检查您的网络连接",
            VPNErrorCode.NETWORK_TIMEOUT to "网络操作超时，请重试",
            VPNErrorCode.NETWORK_DNS_FAILURE to "DNS解析失败，请检查服务器地址",
            VPNErrorCode.NETWORK_CONNECTION_RESET to "网络连接被重置",
            VPNErrorCode.NETWORK_CONNECTION_CLOSED to "网络连接已关闭",
            VPNErrorCode.NETWORK_PROXY_ERROR to "代理服务器错误",
            VPNErrorCode.NETWORK_TLS_ERROR to "TLS/SSL连接错误",

            // Authentication errors
            VPNErrorCode.AUTH_INVALID_CREDENTIALS to "用户名或密码错误",
            VPNErrorCode.AUTH_EXPIRED_CREDENTIALS to "您的凭据已过期，请重新登录",
            VPNErrorCode.AUTH_RATE_LIMITED to "认证尝试次数过多，请稍后重试",
            VPNErrorCode.AUTH_ACCOUNT_LOCKED to "账户已被锁定，请联系管理员",
            VPNErrorCode.AUTH_TOKEN_INVALID to "无效的认证令牌",
            VPNErrorCode.AUTH_TOKEN_EXPIRED to "认证令牌已过期，请重新登录",
            VPNErrorCode.AUTH_MISSING_CREDENTIALS to "缺少认证凭据",

            // Tunnel errors
            VPNErrorCode.TUNNEL_INIT_FAILED to "VPN隧道初始化失败",
            VPNErrorCode.TUNNEL_CLOSED_UNEXPECTED to "VPN隧道意外关闭",
            VPNErrorCode.TUNNEL_DEVICE_ERROR to "VPN设备错误",
            VPNErrorCode.TUNNEL_ROUTE_ERROR to "VPN路由配置错误",
            VPNErrorCode.TUNNEL_ENCRYPTION_ERROR to "VPN加密错误",

            // Configuration errors
            VPNErrorCode.CONFIG_INVALID to "无效的VPN配置",
            VPNErrorCode.CONFIG_MISSING to "VPN配置缺失",
            VPNErrorCode.CONFIG_PERMISSION_DENIED to "访问配置的权限被拒绝",

            // Platform errors
            VPNErrorCode.PLATFORM_PERMISSION_DENIED to "VPN权限被拒绝，请在系统设置中授予VPN访问权限",
            VPNErrorCode.PLATFORM_UNSUPPORTED to "不支持此平台",
            VPNErrorCode.PLATFORM_SYSTEM_ERROR to "系统错误",

            // Protocol errors
            VPNErrorCode.PROTOCOL_HANDSHAKE_FAILED to "VPN协议握手失败",
            VPNErrorCode.PROTOCOL_ENCRYPTION_ERROR to "协议加密错误",
            VPNErrorCode.PROTOCOL_DECRYPTION_ERROR to "协议解密错误"
        )
    }

    /**
     * NAME: getLocalizedMessage
     *
     * DESCRIPTION:
     *     获取指定错误代码的本地化消息
     *
     * PARAMETERS:
     *     errorCode - VPN错误代码
     *
     * RETURNS:
     *     String - 本地化的错误消息
     */
    fun getLocalizedMessage(errorCode: VPNErrorCode): String {
        val messages = when (locale.language) {
            "zh" -> chineseMessages
            "en" -> englishMessages
            else -> englishMessages // 默认使用英文
        }
        return messages[errorCode] ?: getDefaultMessage(errorCode)
    }

    /**
     * NAME: getLocalizedUserFriendlyMessage
     *
     * DESCRIPTION:
     *     获取用户友好的本地化错误消息，包含额外的上下文信息
     *
     * PARAMETERS:
     *     error - VPN服务错误对象
     *
     * RETURNS:
     *     String - 用户友好的本地化错误消息
     */
    fun getLocalizedUserFriendlyMessage(error: VPNServiceError): String {
        return when (error) {
            is VPNServiceError.ConnectionFailed -> {
                val baseMessage = getLocalizedMessage(error.errorCode)
                if (error.retryCount > 0) {
                    val retryInfo = if (locale.language == "zh") {
                        "（已重试${error.retryCount}次）"
                    } else {
                        " (retried ${error.retryCount} times)"
                    }
                    baseMessage + retryInfo
                } else {
                    baseMessage
                }
            }
            is VPNServiceError.AuthenticationFailed -> {
                if (error.isCredentialIssue) {
                    getLocalizedMessage(VPNErrorCode.AUTH_INVALID_CREDENTIALS)
                } else {
                    if (locale.language == "zh") {
                        "服务器认证失败，请稍后重试"
                    } else {
                        "Server authentication failed, please try again later"
                    }
                }
            }
            is VPNServiceError.NetworkUnavailable -> {
                if (error.isTemporary) {
                    if (locale.language == "zh") {
                        "网络暂时不可用，正在尝试重新连接"
                    } else {
                        "Network temporarily unavailable, attempting to reconnect"
                    }
                } else {
                    getLocalizedMessage(error.errorCode)
                }
            }
            is VPNServiceError.TimeoutError -> {
                val baseMessage = getLocalizedMessage(error.errorCode)
                val timeoutInfo = if (locale.language == "zh") {
                    "（超时时间：${error.timeoutDuration / 1000}秒）"
                } else {
                    " (timeout: ${error.timeoutDuration / 1000}s)"
                }
                baseMessage + timeoutInfo
            }
            else -> getLocalizedMessage(error.errorCode)
        }
    }

    /**
     * NAME: getRecoverySuggestion
     *
     * DESCRIPTION:
     *     获取错误恢复建议的本地化消息
     *
     * PARAMETERS:
     *     errorCode - VPN错误代码
     *
     * RETURNS:
     *     String - 本地化的恢复建议
     */
    fun getRecoverySuggestion(errorCode: VPNErrorCode): String {
        return when (errorCode) {
            VPNErrorCode.NETWORK_UNREACHABLE, VPNErrorCode.NETWORK_TIMEOUT -> {
                if (locale.language == "zh") {
                    "请检查网络连接并重试"
                } else {
                    "Please check your network connection and try again"
                }
            }
            VPNErrorCode.NETWORK_DNS_FAILURE -> {
                if (locale.language == "zh") {
                    "请检查DNS设置或尝试其他服务器"
                } else {
                    "Please check DNS settings or try a different server"
                }
            }
            VPNErrorCode.AUTH_INVALID_CREDENTIALS -> {
                if (locale.language == "zh") {
                    "请验证您的用户名和密码"
                } else {
                    "Please verify your username and password"
                }
            }
            VPNErrorCode.AUTH_TOKEN_EXPIRED -> {
                if (locale.language == "zh") {
                    "请重新登录"
                } else {
                    "Please log in again"
                }
            }
            VPNErrorCode.PLATFORM_PERMISSION_DENIED -> {
                if (locale.language == "zh") {
                    "请在系统设置中授予所需权限"
                } else {
                    "Please grant the required permissions in system settings"
                }
            }
            else -> {
                if (locale.language == "zh") {
                    "请重试或联系技术支持"
                } else {
                    "Please try again or contact support if the problem persists"
                }
            }
        }
    }

    /**
     * NAME: getDefaultMessage
     *
     * DESCRIPTION:
     *     获取默认错误消息（当没有找到对应的本地化消息时使用）
     *
     * PARAMETERS:
     *     errorCode - VPN错误代码
     *
     * RETURNS:
     *     String - 默认错误消息
     */
    private fun getDefaultMessage(errorCode: VPNErrorCode): String {
        return if (locale.language == "zh") {
            "未知错误（错误代码：${errorCode.code}）"
        } else {
            "Unknown error (error code: ${errorCode.code})"
        }
    }

    /**
     * NAME: getCurrentLanguage
     *
     * DESCRIPTION:
     *     获取当前语言代码
     *
     * RETURNS:
     *     String - 语言代码（如 "zh", "en"）
     */
    fun getCurrentLanguage(): String = locale.language

    /**
     * NAME: isChineseLocale
     *
     * DESCRIPTION:
     *     判断当前是否为中文语言环境
     *
     * RETURNS:
     *     Boolean - 如果是中文返回true
     */
    fun isChineseLocale(): Boolean = locale.language == "zh"
}
