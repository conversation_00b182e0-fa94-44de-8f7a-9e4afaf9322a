/**
 * FILE: EncryptionService.kt
 *
 * DESCRIPTION:
 *     Encryption service interfaces and implementations for SDWAN protocol.
 *     Provides XOR and AES encryption compatible with Go backend.
 *     Includes key management and password encryption functionality.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.protocol

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import com.panabit.client.protocol.models.EncryptionMethod
import java.security.MessageDigest
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

/**
 * NAME: EncryptionService
 *
 * DESCRIPTION:
 *     Interface for encryption and decryption operations.
 *     Provides unified API for different encryption methods.
 *
 * METHODS:
 *     encrypt - Encrypts data using configured method
 *     decrypt - Decrypts data using configured method
 *     getMethod - Returns encryption method identifier
 */
interface EncryptionService {
    /**
     * NAME: encrypt
     *
     * DESCRIPTION:
     *     Encrypts data using the configured encryption method.
     *
     * PARAMETERS:
     *     data - Raw data to encrypt
     *
     * RETURNS:
     *     ByteArray - Encrypted data
     *
     * THROWS:
     *     Exception - If encryption fails
     */
    fun encrypt(data: ByteArray): ByteArray

    /**
     * NAME: decrypt
     *
     * DESCRIPTION:
     *     Decrypts data using the configured encryption method.
     *
     * PARAMETERS:
     *     data - Encrypted data to decrypt
     *
     * RETURNS:
     *     ByteArray - Decrypted data
     *
     * THROWS:
     *     Exception - If decryption fails
     */
    fun decrypt(data: ByteArray): ByteArray

    /**
     * NAME: getMethod
     *
     * DESCRIPTION:
     *     Returns the encryption method used by this service.
     *
     * RETURNS:
     *     EncryptionMethod - Encryption method identifier
     */
    fun getMethod(): EncryptionMethod
}

/**
 * NAME: KeyManager
 *
 * DESCRIPTION:
 *     Manages encryption keys for SDWAN protocol.
 *     Generates session keys and password encryption keys using MD5.
 *     Compatible with Go backend key generation algorithms.
 *
 * PROPERTIES:
 *     sessionKey - Current session key for data encryption
 *     passwordKey - Key for password encryption in OPEN packets
 */
class KeyManager {
    private var sessionKey: ByteArray? = null
    private var passwordKey: ByteArray? = null

    /**
     * NAME: generateSessionKey
     *
     * DESCRIPTION:
     *     Generates session key using MD5(username + password).
     *     This key is used for data packet encryption/decryption.
     *     Algorithm matches Go backend implementation exactly.
     *
     * PARAMETERS:
     *     username - Username string
     *     password - Password string
     *
     * RETURNS:
     *     ByteArray - 16-byte session key
     */
    fun generateSessionKey(username: String, password: String): ByteArray {
        val combined = username + password
        val md5 = MessageDigest.getInstance("MD5")
        // FIXED: Use US_ASCII encoding to match original Android implementation
        sessionKey = md5.digest(combined.toByteArray(Charsets.US_ASCII))
        return sessionKey!!
    }

    /**
     * NAME: generatePasswordKey
     *
     * DESCRIPTION:
     *     Generates password encryption key using MD5("mw" + username).
     *     This key is used for encrypting passwords in OPEN packets.
     *     Algorithm matches Go backend implementation exactly.
     *
     * PARAMETERS:
     *     username - Username string
     *
     * RETURNS:
     *     ByteArray - 16-byte password encryption key
     */
    fun generatePasswordKey(username: String): ByteArray {
        // Match original Java implementation exactly:
        // byte[] nameMd5 = {109, 119}; // "mw"
        // md5.update(nameMd5);
        // md5.update(username.getBytes(StandardCharsets.US_ASCII));
        val md5 = MessageDigest.getInstance("MD5")
        md5.update(byteArrayOf(109, 119)) // 'm', 'w'
        // FIXED: Use US_ASCII encoding to match original Android implementation
        md5.update(username.toByteArray(Charsets.US_ASCII))
        passwordKey = md5.digest()
        return passwordKey!!
    }

    /**
     * NAME: getSessionKey
     *
     * DESCRIPTION:
     *     Gets the current session key.
     *
     * RETURNS:
     *     ByteArray? - Session key if available, null otherwise
     */
    fun getSessionKey(): ByteArray? = sessionKey

    /**
     * NAME: getPasswordKey
     *
     * DESCRIPTION:
     *     Gets the current password encryption key.
     *
     * RETURNS:
     *     ByteArray? - Password key if available, null otherwise
     */
    fun getPasswordKey(): ByteArray? = passwordKey

    /**
     * NAME: clearKeys
     *
     * DESCRIPTION:
     *     Clears all stored keys for security.
     */
    fun clearKeys() {
        sessionKey?.fill(0)
        passwordKey?.fill(0)
        sessionKey = null
        passwordKey = null
    }
}

/**
 * NAME: PasswordEncryption
 *
 * DESCRIPTION:
 *     Utility class for password encryption in OPEN packets.
 *     Uses AES-ECB mode with MD5("mw" + username) as key.
 *     Compatible with Go backend password encryption.
 */
object PasswordEncryption {
    /**
     * NAME: encryptPassword
     *
     * DESCRIPTION:
     *     Encrypts password using AES-ECB mode with MD5("mw" + username) as key.
     *     Implementation matches Go backend encryptPasswordAES function exactly.
     *     Returns only first 16 bytes of encrypted result.
     *
     * PARAMETERS:
     *     password - Password to encrypt
     *     username - Username for key generation
     *
     * RETURNS:
     *     ByteArray - 16-byte encrypted password
     *
     * THROWS:
     *     Exception - If encryption fails
     */
    fun encryptPassword(password: String, username: String): ByteArray {
        // logDebug("=== Android Password Encryption Debug ===")
        // logDebug("Username: '$username'")
        // logDebug("Password: '$password'")

        // Step 1: Key Generation
        // logDebug("=== Step 1: Key Generation ===")
        val md5 = MessageDigest.getInstance("MD5")
        val mwBytes = byteArrayOf(109, 119) // 'm', 'w'
        // FIXED: Use US_ASCII encoding to match original Android implementation
        val usernameBytes = username.toByteArray(Charsets.US_ASCII)

        // logDebug("MW bytes: ${mwBytes.contentToString()}")
        // logDebug("MW bytes (hex): ${mwBytes.joinToString(" ") { "%02x".format(it) }}")
        // logDebug("Username bytes: ${usernameBytes.contentToString()}")
        // logDebug("Username bytes (hex): ${usernameBytes.joinToString(" ") { "%02x".format(it) }}")

        md5.update(mwBytes)
        md5.update(usernameBytes)
        val key = md5.digest()

        // logDebug("Combined key input: [109,119] + username.getBytes(US_ASCII)")
        // logDebug("MD5 key: ${key.contentToString()}")
        // logDebug("MD5 key (hex): ${key.joinToString(""){ "%02x".format(it) }}")
        // logDebug("MD5 key (spaced): ${key.joinToString(" ") { "%02x".format(it) }}")
        // logDebug("MD5 key length: ${key.size} bytes")

        // Step 2: Password Processing
        // logDebug("=== Step 2: Password Processing ===")
        // FIXED: Use US_ASCII encoding to match original Android implementation
        val passwordBytes = password.toByteArray(Charsets.US_ASCII)
        // logDebug("Password string: '$password'")
        // logDebug("Password bytes: ${passwordBytes.contentToString()}")
        // logDebug("Password bytes (hex): ${passwordBytes.joinToString("") { "%02x".format(it) }}")
        // logDebug("Password bytes (spaced): ${passwordBytes.joinToString(" ") { "%02x".format(it) }}")
        // logDebug("Password length: ${passwordBytes.size} bytes")

        val paddedPassword = ByteArray(32)
        System.arraycopy(passwordBytes, 0, paddedPassword, 0, minOf(passwordBytes.size, 32))
        // logDebug("Padded password (32 bytes): ${paddedPassword.contentToString()}")
        // logDebug("Padded password (hex): ${paddedPassword.joinToString("") { "%02x".format(it) }}")
        // logDebug("Padded password (spaced): ${paddedPassword.joinToString(" ") { "%02x".format(it) }}")

        // Step 3: AES Cipher Setup
        // logDebug("=== Step 3: AES Cipher Setup ===")
        val cipher = Cipher.getInstance("AES/ECB/NoPadding")
        val secretKey = SecretKeySpec(key, "AES")
        cipher.init(Cipher.ENCRYPT_MODE, secretKey)
        // logDebug("AES cipher created successfully")
        // logDebug("AES algorithm: AES/ECB/NoPadding")
        // logDebug("AES key size: ${key.size} bytes")

        // Step 4: AES-ECB Encryption (Android Method)
        // logDebug("=== Step 4: AES-ECB Encryption (Android Method) ===")
        // logDebug("Encrypting only first 16 bytes of padded password")

        val firstBlock = paddedPassword.copyOf(16)
        // logDebug("Input block: ${firstBlock.contentToString()}")
        // logDebug("Input block (hex): ${firstBlock.joinToString("") { "%02x".format(it) }}")
        // logDebug("Input block (spaced): ${firstBlock.joinToString(" ") { "%02x".format(it) }}")

        val result = cipher.doFinal(firstBlock)
        // logDebug("Output block: ${result.contentToString()}")
        // logDebug("Output block (hex): ${result.joinToString("") { "%02x".format(it) }}")
        // logDebug("Output block (spaced): ${result.joinToString(" ") { "%02x".format(it) }}")

        // Step 5: Expected Result Comparison
        // logDebug("=== Step 5: Expected Result Comparison ===")
        val expected = byteArrayOf(
            0xcb.toByte(), 0x7d.toByte(), 0x34.toByte(), 0x35.toByte(),
            0x27.toByte(), 0x31.toByte(), 0x73.toByte(), 0xaa.toByte(),
            0x7a.toByte(), 0x12.toByte(), 0x06.toByte(), 0x28.toByte(),
            0xc9.toByte(), 0xbc.toByte(), 0xca.toByte(), 0xaa.toByte()
        )
        // logDebug("Expected result: ${expected.contentToString()}")
        // logDebug("Expected result (hex): ${expected.joinToString("") { "%02x".format(it) }}")
        // logDebug("Expected result (spaced): ${expected.joinToString(" ") { "%02x".format(it) }}")

        // Step 6: Final Comparison
        // logDebug("=== Step 6: Final Comparison ===")
        // logDebug("Android result: ${result.joinToString(" ") { "%02x".format(it) }}")
        // logDebug("Expected result: ${expected.joinToString(" ") { "%02x".format(it) }}")

        if (result.contentEquals(expected)) {
            // logDebug("✅ Android result matches expected result")
        } else {
            // logDebug("❌ Android result does NOT match expected result")
            // Print byte-by-byte differences
            for (i in 0 until maxOf(result.size, expected.size)) {
                val actualByte = if (i < result.size) "%02x".format(result[i]) else "--"
                val expectedByte = if (i < expected.size) "%02x".format(expected[i]) else "--"
                val match = if (actualByte == expectedByte) "✅" else "❌"
                // logDebug("  Byte $i: actual=$actualByte expected=$expectedByte $match")
            }
        }

        return result
    }
}

/**
 * NAME: NoEncryption
 *
 * DESCRIPTION:
 *     No-operation encryption service for unencrypted data.
 *     Simply returns data unchanged.
 */
class NoEncryption : EncryptionService {
    override fun encrypt(data: ByteArray): ByteArray = data
    override fun decrypt(data: ByteArray): ByteArray = data
    override fun getMethod(): EncryptionMethod = EncryptionMethod.NONE
}
