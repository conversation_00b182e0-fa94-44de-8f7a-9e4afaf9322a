/**
 * FILE: VPNStateEventSender.kt
 *
 * DESCRIPTION:
 *     VPN state event sender for Flutter communication using Method Channel and Event Channel.
 *     Handles state change notifications, error events, and cross-platform compatibility
 *     for Android VPN implementation with Flutter UI layer.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.connection.flutter

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.util.Log
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import com.panabit.client.connection.models.VPNState
import com.panabit.client.connection.state.VPNStateManager

/**
 * NAME: VPNStateEventSender
 *
 * DESCRIPTION:
 *     Event sender for VPN state changes to Flutter UI layer using Event Channel.
 *     Provides reactive state updates, error notifications, and cross-platform
 *     compatibility for Android VPN implementation.
 *
 * FEATURES:
 *     - Reactive state change notifications via Event Channel
 *     - Flutter-compatible event format
 *     - Error handling and logging
 *     - Cross-platform compatibility validation
 *     - Automatic state flow observation
 *
 * DESIGN PRINCIPLES:
 *     - Simple event sending without over-engineering
 *     - Functional consistency with iOS implementation
 *     - Android platform optimization (Coroutines, StateFlow)
 *     - Robust error handling for Flutter communication
 */
class VPNStateEventSender(
    private val stateManager: VPNStateManager,
    private val coroutineScope: CoroutineScope = CoroutineScope(Dispatchers.Main)
) {
    companion object {
        private const val TAG = "VPNStateEventSender"
        private const val EVENT_TYPE_STATE_CHANGE = "stateChange"
        private const val EVENT_TYPE_ERROR = "error"
        private const val EVENT_TYPE_CONNECTION_INFO = "connectionInfo"
        private const val EVENT_TYPE_RECONNECT_REQUIRED = "reconnect_required"

        // 重连原因常量，与Windows版本保持一致
        const val RECONNECT_REASON_NETWORK_CHANGE = "network_interface_change"
        const val RECONNECT_REASON_HEARTBEAT_TIMEOUT = "heartbeat_timeout"
        const val RECONNECT_REASON_CONNECTION_LOST = "connection_lost"
        const val RECONNECT_REASON_SCREEN_LOCK_RECOVERY = "screen_lock_recovery"
    }

    // MARK: - Event Channel Properties
    
    private var eventSink: EventChannel.EventSink? = null
    private var isObservingState = false

    // MARK: - Event Channel Setup
    
    /**
     * NAME: setEventSink
     *
     * DESCRIPTION:
     *     Sets the Flutter Event Channel sink for sending events.
     *
     * PARAMETERS:
     *     sink - Flutter Event Channel sink
     */
    fun setEventSink(sink: EventChannel.EventSink?) {
        eventSink = sink
        
        if (sink != null) {
            Log.d(TAG, "Event sink connected, starting state observation")
            startStateObservation()
        } else {
            Log.d(TAG, "Event sink disconnected, stopping state observation")
            stopStateObservation()
        }
    }

    // MARK: - State Observation
    
    /**
     * NAME: startStateObservation
     *
     * DESCRIPTION:
     *     Starts observing VPN state changes and sending events to Flutter.
     */
    private fun startStateObservation() {
        if (isObservingState) return
        
        isObservingState = true
        
        coroutineScope.launch {
            stateManager.currentState.collect { state ->
                sendStateChangeEvent(state)
            }
        }
        
        Log.d(TAG, "Started VPN state observation")
    }
    
    /**
     * NAME: stopStateObservation
     *
     * DESCRIPTION:
     *     Stops observing VPN state changes.
     */
    private fun stopStateObservation() {
        isObservingState = false
        Log.d(TAG, "Stopped VPN state observation")
    }

    // MARK: - Event Sending Methods
    
    /**
     * NAME: sendStateChangeEvent
     *
     * DESCRIPTION:
     *     Sends VPN state change event to Flutter.
     *
     * PARAMETERS:
     *     state - New VPN state
     */
    private fun sendStateChangeEvent(state: VPNState) {
        try {
            val stateMap = state.toFlutterMap()
            
            // Validate compatibility before sending
            val compatibilityIssues = FlutterCompatibilityHelper.compareWithIOSFormat(stateMap)
            if (compatibilityIssues.isNotEmpty()) {
                Log.w(TAG, "Compatibility issues detected: ${compatibilityIssues.joinToString(", ")}")
            }
            
            val event = mapOf(
                "event" to EVENT_TYPE_STATE_CHANGE,  // Use "event" field for Flutter compatibility
                "data" to stateMap,
                "timestamp" to (System.currentTimeMillis() / 1000)  // Use seconds for consistency with iOS
            )
            
            sendEvent(event)
            Log.d(TAG, "Sent state change event: ${state.flutterStatusString}")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send state change event", e)
        }
    }
    
    /**
     * NAME: sendErrorEvent
     *
     * DESCRIPTION:
     *     Sends VPN error event to Flutter.
     *
     * PARAMETERS:
     *     error - VPN service error
     *     context - Error context information
     */
    fun sendErrorEvent(error: com.panabit.client.infrastructure.error.VPNServiceError, context: String = "") {
        try {
            val event = mapOf(
                "event" to EVENT_TYPE_ERROR,  // Use "event" field for Flutter compatibility
                "data" to mapOf(
                    "error" to error.toMap(),
                    "context" to context,
                    "timestamp" to (System.currentTimeMillis() / 1000)  // Use seconds for consistency with iOS
                )
            )
            
            sendEvent(event)
            Log.d(TAG, "Sent error event: ${error.message}")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send error event", e)
        }
    }
    
    /**
     * NAME: sendConnectionInfoEvent
     *
     * DESCRIPTION:
     *     Sends connection information event to Flutter.
     *
     * PARAMETERS:
     *     connectionInfo - Connection information map
     */
    fun sendConnectionInfoEvent(connectionInfo: Map<String, Any>) {
        try {
            val event = mapOf(
                "event" to EVENT_TYPE_CONNECTION_INFO,  // Use "event" field for Flutter compatibility
                "data" to connectionInfo,
                "timestamp" to (System.currentTimeMillis() / 1000)  // Use seconds for consistency with iOS
            )

            sendEvent(event)
            Log.d(TAG, "Sent connection info event")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to send connection info event", e)
        }
    }

    /**
     * NAME: sendReconnectRequiredEvent
     *
     * DESCRIPTION:
     *     Sends reconnect required event to Flutter UI layer.
     *     This notifies the UI that a reconnection is needed due to network changes
     *     or heartbeat timeout, allowing the UI to control the reconnection process.
     *
     * PARAMETERS:
     *     reason - Reason for requiring reconnect (network_interface_change, heartbeat_timeout, etc.)
     *     message - Detailed message describing the reason
     *     networkInfo - Optional network status information
     */
    fun sendReconnectRequiredEvent(
        reason: String,
        message: String,
        networkInfo: Map<String, Any>? = null
    ) {
        try {
            val eventData = mutableMapOf<String, Any>(
                "reason" to reason,
                "message" to message,
                "timestamp" to (System.currentTimeMillis() / 1000)  // Use seconds for consistency with iOS
            )

            // 添加网络状态信息（如果提供）
            networkInfo?.let { info ->
                eventData["network_info"] = info
            }

            val event = mapOf(
                "event" to EVENT_TYPE_RECONNECT_REQUIRED,  // Use "event" field for Flutter compatibility
                "data" to eventData,
                "timestamp" to (System.currentTimeMillis() / 1000)
            )

            sendEvent(event)
            Log.i(TAG, "Sent reconnect required event: reason=$reason, message=$message")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to send reconnect required event", e)
        }
    }

    // MARK: - Core Event Sending
    
    /**
     * NAME: sendEvent
     *
     * DESCRIPTION:
     *     Sends event to Flutter via Event Channel.
     *
     * PARAMETERS:
     *     event - Event data to send
     */
    private fun sendEvent(event: Map<String, Any>) {
        val sink = eventSink
        if (sink == null) {
            Log.w(TAG, "Event sink not available, event dropped: ${event["type"]}")
            return
        }
        
        try {
            // Always use main thread for Flutter communication
            coroutineScope.launch(Dispatchers.Main) {
                sink.success(event)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send event to Flutter", e)
        }
    }

    // MARK: - Utility Methods
    
    /**
     * NAME: sendCurrentState
     *
     * DESCRIPTION:
     *     Sends current VPN state immediately to Flutter.
     *     Useful for initial state synchronization.
     */
    fun sendCurrentState() {
        sendStateChangeEvent(stateManager.currentStateValue)
    }
    
    /**
     * NAME: isConnected
     *
     * DESCRIPTION:
     *     Checks if Event Channel is connected to Flutter.
     *
     * RETURNS:
     *     Boolean - true if connected, false otherwise
     */
    fun isConnected(): Boolean = eventSink != null
    


    // MARK: - Cleanup
    
    /**
     * NAME: cleanup
     *
     * DESCRIPTION:
     *     Cleans up event sender resources.
     *     Should be called when sender is no longer needed.
     */
    fun cleanup() {
        Log.d(TAG, "VPNStateEventSender cleanup")
        stopStateObservation()
        eventSink = null
    }

    // MARK: - Debug and Testing Support
    
    /**
     * NAME: validateEventFormat
     *
     * DESCRIPTION:
     *     Validates event format for debugging purposes.
     *
     * PARAMETERS:
     *     event - Event to validate
     *
     * RETURNS:
     *     Boolean - true if format is valid, false otherwise
     */
    private fun validateEventFormat(event: Map<String, Any>): Boolean {
        val requiredFields = listOf("type", "data", "timestamp")
        
        for (field in requiredFields) {
            if (!event.containsKey(field)) {
                Log.w(TAG, "Event missing required field: $field")
                return false
            }
        }
        
        val timestamp = event["timestamp"] as? Long
        if (timestamp == null || timestamp <= 0) {
            Log.w(TAG, "Event has invalid timestamp: $timestamp")
            return false
        }
        
        return true
    }
    
    /**
     * NAME: logEventStatistics
     *
     * DESCRIPTION:
     *     Logs event sending statistics for debugging.
     */
    fun logEventStatistics() {
        Log.d(TAG, "Event sender statistics:")
        Log.d(TAG, "  - Event sink connected: ${eventSink != null}")
        Log.d(TAG, "  - State observation active: $isObservingState")
        Log.d(TAG, "  - Current state: ${stateManager.currentStateValue.flutterStatusString}")
    }
}
