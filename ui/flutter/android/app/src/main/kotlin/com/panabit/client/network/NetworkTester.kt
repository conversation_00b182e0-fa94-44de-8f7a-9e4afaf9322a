/**
 * FILE: NetworkTester.kt
 *
 * DESCRIPTION:
 *     Network testing utilities for UDP communication validation.
 *     Provides simple testing functions to verify UDP connection functionality.
 *     Used for debugging and validation of network implementation.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.network

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import kotlinx.coroutines.*
import java.net.InetAddress

/**
 * NAME: NetworkTester
 *
 * DESCRIPTION:
 *     Utility object for testing UDP network functionality.
 *     Provides methods to test connection, send/receive operations, and latency.
 *
 * METHODS:
 *     testConnection - Tests basic UDP connection
 *     testSendReceive - Tests send and receive operations
 *     testLatency - Measures network latency
 *     runBasicTests - Runs comprehensive test suite
 */
object NetworkTester {
    /**
     * NAME: testConnection
     *
     * DESCRIPTION:
     *     Tests basic UDP connection establishment.
     *
     * PARAMETERS:
     *     serverAddress - Target server address
     *     serverPort - Target server port
     *
     * RETURNS:
     *     Result<Map<String, Any>> - Test results or failure
     */
    suspend fun testConnection(
        serverAddress: InetAddress,
        serverPort: Int
    ): Result<Map<String, Any>> {
        return try {
            logInfo("Testing UDP connection", mapOf(
                "server_address" to serverAddress.hostAddress,
                "server_port" to serverPort
            ))

            val startTime = System.currentTimeMillis()
            val connection = UDPConnection(serverAddress, serverPort)
            
            val connectResult = connection.connect()
            val connectionTime = System.currentTimeMillis() - startTime

            val result = if (connectResult.isSuccess) {
                val stats = connection.getConnectionStats()
                connection.close()
                
                mapOf(
                    "success" to true,
                    "connection_time_ms" to connectionTime,
                    "local_port" to (stats["local_port"] ?: 0),
                    "server_endpoint" to "${serverAddress.hostAddress}:$serverPort"
                )
            } else {
                mapOf(
                    "success" to false,
                    "connection_time_ms" to connectionTime,
                    "error" to (connectResult.exceptionOrNull()?.message ?: "Unknown error")
                )
            }

            logInfo("Connection test completed")
            Result.success(result)

        } catch (e: Exception) {
            val errorResult = mapOf(
                "success" to false,
                "error" to (e.message ?: "unknown")
            )
            logError("Connection test failed", errorResult, e)
            Result.failure(e)
        }
    }

    /**
     * NAME: testSendReceive
     *
     * DESCRIPTION:
     *     Tests send and receive operations with test data.
     *
     * PARAMETERS:
     *     serverAddress - Target server address
     *     serverPort - Target server port
     *     testData - Data to send for testing
     *
     * RETURNS:
     *     Result<Map<String, Any>> - Test results or failure
     */
    suspend fun testSendReceive(
        serverAddress: InetAddress,
        serverPort: Int,
        testData: ByteArray = "TEST_PACKET".toByteArray()
    ): Result<Map<String, Any>> {
        return try {
            logInfo("Testing UDP send/receive", mapOf(
                "server_address" to serverAddress.hostAddress,
                "server_port" to serverPort,
                "test_data_size" to testData.size
            ))

            val connection = UDPConnection(serverAddress, serverPort)
            val connectResult = connection.connect()

            if (connectResult.isFailure) {
                return Result.failure(connectResult.exceptionOrNull() ?: Exception("Connection failed"))
            }

            try {
                // Test send operation
                val sendStartTime = System.currentTimeMillis()
                val sendResult = connection.send(testData)
                val sendTime = System.currentTimeMillis() - sendStartTime

                if (sendResult.isFailure) {
                    return Result.success(mapOf(
                        "success" to false,
                        "operation" to "send",
                        "error" to (sendResult.exceptionOrNull()?.message ?: "Send failed")
                    ))
                }

                // Test receive operation (may timeout, which is expected)
                val receiveStartTime = System.currentTimeMillis()
                val receiveResult = connection.receive()
                val receiveTime = System.currentTimeMillis() - receiveStartTime

                val stats = connection.getConnectionStats()
                
                val result = mapOf<String, Any>(
                    "success" to true,
                    "send_success" to sendResult.isSuccess,
                    "send_time_ms" to sendTime,
                    "receive_success" to receiveResult.isSuccess,
                    "receive_time_ms" to receiveTime,
                    "bytes_sent" to (stats["bytes_sent"] ?: 0),
                    "bytes_received" to (stats["bytes_received"] ?: 0),
                    "received_data_size" to (if (receiveResult.isSuccess) receiveResult.getOrNull()?.size ?: 0 else 0)
                )

                logInfo("Send/receive test completed")
                Result.success(result)

            } finally {
                connection.close()
            }

        } catch (e: Exception) {
            val errorResult = mapOf<String, Any>(
                "success" to false,
                "error" to (e.message ?: "unknown")
            )
            logError("Send/receive test failed", mapOf("error" to (e.message ?: "unknown")), e)
            Result.failure(e)
        }
    }

    /**
     * NAME: testLatency
     *
     * DESCRIPTION:
     *     Tests network latency to specified endpoint.
     *
     * PARAMETERS:
     *     serverAddress - Target server address
     *     serverPort - Target server port
     *     attempts - Number of latency test attempts
     *
     * RETURNS:
     *     Result<Map<String, Any>> - Latency test results or failure
     */
    suspend fun testLatency(
        serverAddress: InetAddress,
        serverPort: Int,
        attempts: Int = 3
    ): Result<Map<String, Any>> {
        return try {
            logInfo("Testing network latency", mapOf(
                "server_address" to serverAddress.hostAddress,
                "server_port" to serverPort,
                "attempts" to attempts
            ))

            val endpoint = NetworkEndpoint(serverAddress, serverPort)
            val latencies = mutableListOf<Long>()
            var successCount = 0

            repeat(attempts) { attempt ->
                val latencyResult = NetworkUtils.measureLatency(endpoint)
                if (latencyResult.isSuccess) {
                    latencies.add(latencyResult.getOrThrow())
                    successCount++
                }
                
                // Small delay between attempts
                if (attempt < attempts - 1) {
                    delay(100)
                }
            }

            val result = if (latencies.isNotEmpty()) {
                mapOf<String, Any>(
                    "success" to true,
                    "attempts" to attempts,
                    "successful_attempts" to successCount,
                    "min_latency_ms" to (latencies.minOrNull() ?: 0L),
                    "max_latency_ms" to (latencies.maxOrNull() ?: 0L),
                    "avg_latency_ms" to latencies.average(),
                    "latencies" to latencies
                )
            } else {
                mapOf<String, Any>(
                    "success" to false,
                    "attempts" to attempts,
                    "successful_attempts" to 0,
                    "error" to "No successful latency measurements"
                )
            }

            logInfo("Latency test completed")
            Result.success(result)

        } catch (e: Exception) {
            val errorResult = mapOf<String, Any>(
                "success" to false,
                "error" to (e.message ?: "unknown")
            )
            logError("Latency test failed", mapOf("error" to (e.message ?: "unknown")), e)
            Result.failure(e)
        }
    }

    /**
     * NAME: runBasicTests
     *
     * DESCRIPTION:
     *     Runs comprehensive test suite for UDP network functionality.
     *
     * PARAMETERS:
     *     serverAddress - Target server address
     *     serverPort - Target server port
     *
     * RETURNS:
     *     Result<Map<String, Any>> - Complete test results or failure
     */
    suspend fun runBasicTests(
        serverAddress: InetAddress,
        serverPort: Int
    ): Result<Map<String, Any>> {
        return try {
            logInfo("Running basic UDP network tests", mapOf(
                "server_address" to serverAddress.hostAddress,
                "server_port" to serverPort
            ))

            val testResults = mutableMapOf<String, Any>()
            val startTime = System.currentTimeMillis()

            // Test 1: Basic connection
            val connectionTest = testConnection(serverAddress, serverPort)
            testResults["connection_test"] = connectionTest.getOrElse {
                mapOf<String, Any>("success" to false, "error" to (it.message ?: "unknown"))
            }

            // Test 2: Send/receive operations
            val sendReceiveTest = testSendReceive(serverAddress, serverPort)
            testResults["send_receive_test"] = sendReceiveTest.getOrElse {
                mapOf<String, Any>("success" to false, "error" to (it.message ?: "unknown"))
            }

            // Test 3: Latency measurement
            val latencyTest = testLatency(serverAddress, serverPort)
            testResults["latency_test"] = latencyTest.getOrElse { 
                mapOf("success" to false, "error" to it.message) 
            }

            val totalTime = System.currentTimeMillis() - startTime
            
            // Calculate overall success
            val allTests = listOf(connectionTest, sendReceiveTest, latencyTest)
            val successfulTests = allTests.count { it.isSuccess }
            val overallSuccess = successfulTests == allTests.size

            val finalResult = mapOf<String, Any>(
                "overall_success" to overallSuccess,
                "successful_tests" to successfulTests,
                "total_tests" to allTests.size,
                "total_time_ms" to totalTime,
                "server_endpoint" to "${serverAddress.hostAddress}:$serverPort",
                "test_results" to testResults
            )

            logInfo("Basic tests completed", mapOf(
                "overall_success" to overallSuccess,
                "successful_tests" to "$successfulTests/${allTests.size}",
                "total_time_ms" to totalTime
            ))

            Result.success(finalResult)

        } catch (e: Exception) {
            val errorResult = mapOf<String, Any>(
                "overall_success" to false,
                "error" to (e.message ?: "unknown")
            )
            logError("Basic tests failed", mapOf("error" to (e.message ?: "unknown")), e)
            Result.failure(e)
        }
    }

    /**
     * NAME: testDNSResolution
     *
     * DESCRIPTION:
     *     Tests DNS resolution functionality.
     *
     * PARAMETERS:
     *     hostname - Hostname to resolve
     *
     * RETURNS:
     *     Result<Map<String, Any>> - DNS resolution test results
     */
    suspend fun testDNSResolution(hostname: String): Result<Map<String, Any>> {
        return try {
            logInfo("Testing DNS resolution", mapOf("hostname" to hostname))

            val startTime = System.currentTimeMillis()
            val resolveResult = NetworkUtils.resolveAddress(hostname)
            val resolveTime = System.currentTimeMillis() - startTime

            val result = if (resolveResult.isSuccess) {
                val address = resolveResult.getOrThrow()
                mapOf<String, Any>(
                    "success" to true,
                    "hostname" to hostname,
                    "resolved_ip" to address.hostAddress,
                    "resolve_time_ms" to resolveTime
                )
            } else {
                mapOf<String, Any>(
                    "success" to false,
                    "hostname" to hostname,
                    "resolve_time_ms" to resolveTime,
                    "error" to (resolveResult.exceptionOrNull()?.message ?: "Resolution failed")
                )
            }

            logInfo("DNS resolution test completed")
            Result.success(result)

        } catch (e: Exception) {
            val errorResult = mapOf<String, Any>(
                "success" to false,
                "hostname" to hostname,
                "error" to (e.message ?: "unknown")
            )
            logError("DNS resolution test failed", mapOf("error" to (e.message ?: "unknown")), e)
            Result.failure(e)
        }
    }
}
