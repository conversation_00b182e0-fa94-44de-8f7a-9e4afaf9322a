/**
 * FILE: Constants.kt
 *
 * DESCRIPTION:
 *     Panabit Client Android应用程序常量定义。
 *     包含网络超时、协议常量、UI常量等全局配置。
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.infrastructure.utils

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

/**
 * NAME: Constants
 *
 * DESCRIPTION:
 *     应用程序全局常量定义，包含网络、协议、UI等各类常量。
 *
 * VALUES:
 *     Network constants - 网络相关超时和配置
 *     Protocol constants - SDWAN协议相关常量
 *     UI constants - 用户界面相关常量
 *     Service constants - 服务相关常量
 */
object Constants {
    
    // 网络超时常量
    object Network {
        const val CONNECT_TIMEOUT_MS = 8000L
        const val READ_TIMEOUT_MS = 5000L
        const val WRITE_TIMEOUT_MS = 5000L
        const val HEARTBEAT_INTERVAL_MS = 15000L // 与iOS版本和Go后端保持一致
        const val RECONNECT_DELAY_MS = 2000L
        const val MAX_RECONNECT_ATTEMPTS = 3
    }
    
    // 协议常量
    object Protocol {
        const val PROTOCOL_VERSION = 1
        const val DEFAULT_MTU = 1500
        const val MIN_MTU = 1280
        const val MAX_MTU = 1500
        const val PACKET_HEADER_SIZE = 20
    }
    
    // UI常量
    object UI {
        const val NOTIFICATION_CHANNEL_ID = "vpn_service_channel"
        const val NOTIFICATION_ID = 1001
        const val PERMISSION_REQUEST_CODE = 1001
    }
    
    // 服务常量
    object Service {
        const val VPN_SERVICE_ACTION_CONNECT = "com.panabit.client.CONNECT"
        const val VPN_SERVICE_ACTION_DISCONNECT = "com.panabit.client.DISCONNECT"
        const val VPN_SERVICE_ACTION_RECONNECT = "com.panabit.client.RECONNECT"
    }
    
    // 日志标签
    object LogTags {
        const val VPN_SERVICE = "VPNService"
        const val CONNECTION_MANAGER = "ConnectionManager"
        const val NETWORK_MONITOR = "NetworkMonitor"
        const val PLATFORM_CHANNEL = "PlatformChannel"
    }
}
