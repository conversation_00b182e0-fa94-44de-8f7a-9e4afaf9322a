/**
 * FILE: FlutterCompatibilityHelper.kt
 *
 * DESCRIPTION:
 *     Flutter compatibility helper for ensuring consistent cross-platform communication
 *     between Android VPN implementation and Flutter UI layer. Provides validation,
 *     formatting, and compatibility checks for state mapping and data serialization.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.connection.flutter

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.util.Log
import com.panabit.client.connection.models.VPNState
import com.panabit.client.connection.models.ServerInfo
import com.panabit.client.connection.models.ConnectionProgress
import com.panabit.client.infrastructure.error.VPNServiceError

/**
 * NAME: FlutterCompatibilityHelper
 *
 * DESCRIPTION:
 *     Helper class for ensuring Flutter compatibility and cross-platform consistency.
 *     Provides validation methods, format converters, and compatibility checks for
 *     Android VPN state communication with Flutter UI layer.
 *
 * FEATURES:
 *     - Cross-platform state format validation
 *     - Flutter-compatible data type conversion
 *     - Timestamp format standardization
 *     - Error message localization support
 *     - Debug logging for compatibility issues
 */
object FlutterCompatibilityHelper {
    private const val TAG = "FlutterCompatibility"

    // MARK: - State Format Validation
    
    /**
     * NAME: validateStateMap
     *
     * DESCRIPTION:
     *     Basic validation for Flutter state map compatibility.
     *     Simplified validation following user's architecture preferences.
     *
     * PARAMETERS:
     *     stateMap - State map to validate
     *     expectedState - Expected state string
     *
     * RETURNS:
     *     Boolean - true if map is valid, false otherwise
     */
    fun validateStateMap(stateMap: Map<String, Any>, expectedState: String): Boolean {
        try {
            // Basic validation only
            val state = stateMap["state"] as? String
            val timestamp = stateMap["timestamp"] as? Long

            return state == expectedState && timestamp != null && timestamp > 0
        } catch (e: Exception) {
            Log.e(TAG, "State map validation failed", e)
            return false
        }
    }
    


    // MARK: - Format Conversion Helpers
    
    /**
     * NAME: ensureFlutterCompatibleTimestamp
     *
     * DESCRIPTION:
     *     Ensures timestamp is in Flutter-compatible format (seconds since epoch for consistency with iOS).
     *
     * PARAMETERS:
     *     timestamp - Timestamp to convert
     *
     * RETURNS:
     *     Long - Flutter-compatible timestamp in seconds
     */
    fun ensureFlutterCompatibleTimestamp(timestamp: Long): Long {
        // If timestamp appears to be in milliseconds, convert to seconds
        return if (timestamp > 1_000_000_000_000L) {
            timestamp / 1000
        } else {
            timestamp
        }
    }
    
    /**
     * NAME: formatProgressForFlutter
     *
     * DESCRIPTION:
     *     Formats connection progress for Flutter compatibility.
     *
     * PARAMETERS:
     *     progress - Connection progress enum
     *
     * RETURNS:
     *     String - Flutter-compatible progress string
     */
    fun formatProgressForFlutter(progress: ConnectionProgress): String {
        return progress.name.lowercase()
    }

    // MARK: - Cross-Platform Compatibility Checks
    
    /**
     * NAME: compareWithIOSFormat
     *
     * DESCRIPTION:
     *     Compares Android state map format with expected iOS format for compatibility.
     *
     * PARAMETERS:
     *     androidMap - Android-generated state map
     *
     * RETURNS:
     *     List<String> - List of compatibility issues found (empty if compatible)
     */
    fun compareWithIOSFormat(androidMap: Map<String, Any>): List<String> {
        val issues = mutableListOf<String>()
        
        // Check state field format
        val state = androidMap["state"] as? String
        if (state == null) {
            issues.add("Missing 'state' field")
        }
        
        // Check timestamp format (now expecting seconds for consistency with iOS)
        val timestamp = androidMap["timestamp"] as? Long
        if (timestamp == null) {
            issues.add("Missing 'timestamp' field")
        } else if (timestamp > 1_000_000_000_000L) {
            issues.add("Timestamp appears to be in milliseconds instead of seconds")
        }
        
        // Check state-specific compatibility
        when (state) {
            "connecting" -> {
                if (!androidMap.containsKey("progress")) {
                    issues.add("Connecting state missing 'progress' field")
                }
            }
            "connected" -> {
                if (!androidMap.containsKey("connected_at")) {
                    issues.add("Connected state missing 'connected_at' field")
                }
                if (!androidMap.containsKey("tunnel_ip")) {
                    issues.add("Connected state missing 'tunnel_ip' field")
                }
            }
            "error" -> {
                if (!androidMap.containsKey("error")) {
                    issues.add("Error state missing 'error' field")
                }
            }
        }
        
        return issues
    }
    
    /**
     * NAME: logCompatibilityCheck
     *
     * DESCRIPTION:
     *     Logs compatibility check results for debugging.
     *
     * PARAMETERS:
     *     state - VPN state being checked
     *     issues - List of compatibility issues
     */
    fun logCompatibilityCheck(state: VPNState, issues: List<String>) {
        if (issues.isEmpty()) {
            Log.d(TAG, "Flutter compatibility check passed for state: $state")
        } else {
            Log.w(TAG, "Flutter compatibility issues found for state $state: ${issues.joinToString(", ")}")
        }
    }
}
