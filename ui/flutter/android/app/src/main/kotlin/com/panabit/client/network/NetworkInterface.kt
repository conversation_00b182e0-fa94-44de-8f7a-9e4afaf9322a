/**
 * FILE: NetworkInterface.kt
 *
 * DESCRIPTION:
 *     Simple network data models for UDP communication.
 *     Contains only essential data classes, avoiding over-abstraction.
 *     Based on Windows/iOS simple design patterns.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.network

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import java.net.InetAddress

/**
 * NAME: NetworkEndpoint
 *
 * DESCRIPTION:
 *     Represents network endpoint information.
 *     Contains address and port information for network connections.
 *
 * PROPERTIES:
 *     address - IP address of endpoint
 *     port - Port number of endpoint
 *     hostname - Optional hostname of endpoint
 */
data class NetworkEndpoint(
    val address: InetAddress,
    val port: Int,
    val hostname: String? = null
) {
    /**
     * NAME: toString
     *
     * DESCRIPTION:
     *     Returns string representation of endpoint.
     *
     * RETURNS:
     *     String - Endpoint in "address:port" format
     */
    override fun toString(): String {
        return "${address.hostAddress}:$port"
    }

    /**
     * NAME: toMap
     *
     * DESCRIPTION:
     *     Converts endpoint to map representation.
     *
     * RETURNS:
     *     Map<String, Any> - Endpoint information as map
     */
    fun toMap(): Map<String, Any> {
        return mapOf(
            "address" to address.hostAddress,
            "port" to port,
            "hostname" to (hostname ?: ""),
            "endpoint" to toString()
        )
    }
}
