/**
 * FILE: NetworkModule.kt
 *
 * DESCRIPTION:
 *     ITforce WAN Android network module dependency injection configuration.
 *     Defines network communication components following simplified architecture design.
 *     Focuses on essential network functionality without over-engineering.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create, 13/07/2025 refactor for simplified architecture
 */

package com.panabit.client.di

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import org.koin.dsl.module
import com.panabit.client.protocol.EncryptionService
import com.panabit.client.protocol.NoEncryption
import com.panabit.client.protocol.KeyManager

/**
 * NAME: networkModule
 *
 * DESCRIPTION:
 *     Network module defining dependency injection configuration for network communication components.
 *     Follows simplified architecture by including only essential network functionality.
 *     Removes over-engineered components and focuses on core SDWAN protocol support.
 *
 * COMPONENTS:
 *     - EncryptionService: Packet encryption and decryption
 *     - KeyManager: Encryption key management
 *
 * DESIGN PRINCIPLES:
 *     - Simplified architecture avoiding over-engineering
 *     - Focus on core SDWAN protocol functionality
 *     - Factory pattern for stateless components
 *     - Singleton pattern for shared state components
 */
val networkModule = module {

    // Key Manager - encryption key management
    single<KeyManager> {
        KeyManager()
    }

    // Encryption Service - packet encryption and decryption (default to no encryption)
    single<EncryptionService> {
        NoEncryption()
    }

    // Note: Other network components like UDPConnection and SDWANProtocol are created
    // as factories in VPNModule to avoid circular dependencies and maintain simplified architecture.
    // HeartbeatManager is managed directly by ConnectionManager to maintain unified state management.
    // All packet processing is handled directly in ConnectionManager for simplicity.
}
