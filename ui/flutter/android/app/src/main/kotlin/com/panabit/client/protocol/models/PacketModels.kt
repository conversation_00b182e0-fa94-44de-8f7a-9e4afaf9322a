/**
 * FILE: PacketModels.kt
 *
 * DESCRIPTION:
 *     SDWAN protocol packet models and data structures.
 *     Defines core packet types, headers, and enumerations compatible with Go backend.
 *     Ensures byte-level compatibility with protocol specification.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.protocol.models

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * NAME: PacketType
 *
 * DESCRIPTION:
 *     Enumeration of all supported packet types in SDWAN protocol.
 *     Values must match exactly with Go backend constants for compatibility.
 *
 * PROPERTIES:
 *     value - UByte value matching Go backend constants
 *     isDataPacket - Checks if packet type carries IP data
 *     description - Human-readable packet type description
 */
enum class PacketType(val value: UByte) {
    OPEN_REJECT(0x11u),      // Server rejects client connection
    OPEN_ACK(0x12u),         // Server acknowledges client connection
    OPEN(0x13u),             // Client requests connection
    DATA(0x14u),             // IPv4 data packet
    ECHO_REQUEST(0x15u),     // Echo request (keepalive)
    ECHO_RESPONSE(0x16u),    // Echo response
    CLOSE(0x17u),            // Close connection
    DATA_ENCRYPTED(0x18u),   // Encrypted IPv4 data packet
    DATA_DUP(0x19u),         // Duplicate IPv4 data packet (matches iOS dataDup)
    DATA_ENC_DUP(0x20u),     // Encrypted duplicate IPv4 data packet (matches iOS dataEncDup)
    SEGMENT_ROUTING(0x21u),  // Segment routing packet (SDWAN_IWANSR in Java)
    IP_FRAGMENT(0x22u),      // IPv4 fragment packet (matches iOS ipFrag)
    DATA_IPV6(0x23u),        // IPv6 data packet (matches iOS data6)
    IP_FRAGMENT_IPV6(0x24u), // IPv6 fragment packet (matches iOS ipFrag6)
    PING_REQUEST(0x29u),     // Ping request (matches iOS pingRequest)
    PING_RESPONSE(0x2Au);    // Ping response (matches iOS pingResponse)

    /**
     * NAME: isDataPacket
     *
     * DESCRIPTION:
     *     Checks if this packet type carries IP data payload.
     *
     * RETURNS:
     *     Boolean - true if packet carries IP data, false otherwise
     */
    val isDataPacket: Boolean
        get() = when (this) {
            DATA, DATA_ENCRYPTED, DATA_DUP, DATA_ENC_DUP, DATA_IPV6,
            IP_FRAGMENT, IP_FRAGMENT_IPV6, SEGMENT_ROUTING -> true
            else -> false
        }

    /**
     * NAME: description
     *
     * DESCRIPTION:
     *     Provides human-readable description of packet type.
     *
     * RETURNS:
     *     String - Packet type description
     */
    val description: String
        get() = when (this) {
            OPEN_REJECT -> "OPENREJ"
            OPEN_ACK -> "OPENACK"
            OPEN -> "OPEN"
            DATA -> "DATA"
            ECHO_REQUEST -> "ECHOREQ"
            ECHO_RESPONSE -> "ECHORES"
            CLOSE -> "CLOSE"
            DATA_ENCRYPTED -> "DATAENC"
            DATA_DUP -> "DATADUP"
            DATA_ENC_DUP -> "DATAENCDUP"
            IP_FRAGMENT -> "IPFRAG"
            DATA_IPV6 -> "DATA6"
            IP_FRAGMENT_IPV6 -> "IPFRAG6"
            SEGMENT_ROUTING -> "SEGRT"
            PING_REQUEST -> "PINGREQ"
            PING_RESPONSE -> "PINGRES"
        }

    companion object {
        /**
         * NAME: fromValue
         *
         * DESCRIPTION:
         *     Creates PacketType from raw byte value.
         *
         * PARAMETERS:
         *     value - Raw byte value
         *
         * RETURNS:
         *     PacketType? - Packet type if valid, null otherwise
         */
        fun fromValue(value: UByte): PacketType? {
            return values().find { it.value == value }
        }
    }
}

/**
 * NAME: EncryptionMethod
 *
 * DESCRIPTION:
 *     Enumeration of supported encryption methods.
 *     Must be compatible with Go backend encryption constants.
 *
 * PROPERTIES:
 *     value - UByte value matching Go backend constants
 *     description - Human-readable encryption method description
 */
enum class EncryptionMethod(val value: UByte) {
    NONE(0x00u),            // No encryption
    XOR(0x01u),             // XOR encryption
    AES(0x02u);             // AES encryption

    /**
     * NAME: description
     *
     * DESCRIPTION:
     *     Provides human-readable description of encryption method.
     *
     * RETURNS:
     *     String - Encryption method description
     */
    val description: String
        get() = when (this) {
            NONE -> "NONE"
            XOR -> "XOR"
            AES -> "AES"
        }

    companion object {
        /**
         * NAME: fromValue
         *
         * DESCRIPTION:
         *     Creates EncryptionMethod from raw byte value.
         *
         * PARAMETERS:
         *     value - Raw byte value
         *
         * RETURNS:
         *     EncryptionMethod? - Encryption method if valid, null otherwise
         */
        fun fromValue(value: UByte): EncryptionMethod? {
            return values().find { it.value == value }
        }
    }
}

/**
 * NAME: PacketHeader
 *
 * DESCRIPTION:
 *     Represents SDWAN protocol packet header structure.
 *     8-byte header consistent with protocol specification and Go backend.
 *     Uses big-endian byte order for network transmission.
 *
 * PROPERTIES:
 *     type - Packet type identifier
 *     encrypt - Encryption method used
 *     sessionID - Session ID for connection tracking (SID in Go)
 *     token - Authentication token
 */
data class PacketHeader(
    val type: PacketType,
    val encrypt: EncryptionMethod,
    val sessionID: UShort,        // Maps to SID in Go backend
    val token: UInt
) {
    companion object {
        const val HEADER_SIZE = 8

        /**
         * NAME: fromByteArray
         *
         * DESCRIPTION:
         *     Creates PacketHeader from binary data using big-endian byte order.
         *     Matches Go backend binary format exactly.
         *
         * PARAMETERS:
         *     data - Binary header data (must be at least 8 bytes)
         *
         * RETURNS:
         *     PacketHeader - Parsed packet header
         *
         * THROWS:
         *     IllegalArgumentException - If data is too short or contains invalid values
         */
        fun fromByteArray(data: ByteArray): PacketHeader {
            require(data.size >= HEADER_SIZE) { "Header data must be at least $HEADER_SIZE bytes" }

            val buffer = ByteBuffer.wrap(data).order(ByteOrder.BIG_ENDIAN)

            val typeValue = buffer.get().toUByte()
            val encryptValue = buffer.get().toUByte()
            val sessionID = buffer.getShort().toUShort()
            val token = buffer.getInt().toUInt()

            val packetType = PacketType.fromValue(typeValue)
                ?: throw IllegalArgumentException("Invalid packet type: 0x${typeValue.toString(16)}")

            val encryptionMethod = EncryptionMethod.fromValue(encryptValue)
                ?: throw IllegalArgumentException("Invalid encryption method: 0x${encryptValue.toString(16)}")

            return PacketHeader(packetType, encryptionMethod, sessionID, token)
        }
    }

    /**
     * NAME: toByteArray
     *
     * DESCRIPTION:
     *     Converts packet header to binary data using big-endian byte order.
     *     Compatible with Go backend binary format.
     *
     * RETURNS:
     *     ByteArray - 8-byte header in network byte order
     */
    fun toByteArray(): ByteArray {
        val buffer = ByteBuffer.allocate(HEADER_SIZE).order(ByteOrder.BIG_ENDIAN)
        buffer.put(type.value.toByte())
        buffer.put(encrypt.value.toByte())
        buffer.putShort(sessionID.toShort())
        buffer.putInt(token.toInt())
        return buffer.array()
    }
}

/**
 * NAME: SDWANPacket
 *
 * DESCRIPTION:
 *     Represents a complete SDWAN protocol packet.
 *     Contains packet header and data payload for network transmission.
 *     Compatible with Go backend packet structure.
 *
 * PROPERTIES:
 *     header - Packet header with type, encryption, session info
 *     data - Packet data payload
 */
data class SDWANPacket(
    val header: PacketHeader,
    val data: ByteArray
) {
    companion object {
        /**
         * NAME: fromByteArray
         *
         * DESCRIPTION:
         *     Creates SDWANPacket from binary data.
         *     Parses header and extracts data payload.
         *
         * PARAMETERS:
         *     data - Binary packet data
         *
         * RETURNS:
         *     SDWANPacket - Parsed packet with header and payload
         *
         * THROWS:
         *     IllegalArgumentException - If data too small for header
         */
        fun fromByteArray(data: ByteArray): SDWANPacket {
            require(data.size >= PacketHeader.HEADER_SIZE) {
                "Packet data must be at least ${PacketHeader.HEADER_SIZE} bytes"
            }

            val header = PacketHeader.fromByteArray(data)
            val payload = data.copyOfRange(PacketHeader.HEADER_SIZE, data.size)

            return SDWANPacket(header, payload)
        }


    }

    /**
     * NAME: toByteArray
     *
     * DESCRIPTION:
     *     Converts packet to binary data for transmission.
     *     Combines header and payload into single byte array.
     *
     * RETURNS:
     *     ByteArray - Complete packet as binary data
     */
    fun toByteArray(): ByteArray {
        val headerBytes = header.toByteArray()
        return headerBytes + data
    }

    /**
     * NAME: equals
     *
     * DESCRIPTION:
     *     Custom equals implementation for data class with ByteArray.
     *
     * PARAMETERS:
     *     other - Object to compare with
     *
     * RETURNS:
     *     Boolean - true if packets are equal, false otherwise
     */
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as SDWANPacket

        if (header != other.header) return false
        if (!data.contentEquals(other.data)) return false

        return true
    }

    /**
     * NAME: hashCode
     *
     * DESCRIPTION:
     *     Custom hashCode implementation for data class with ByteArray.
     *
     * RETURNS:
     *     Int - Hash code for packet
     */
    override fun hashCode(): Int {
        var result = header.hashCode()
        result = 31 * result + data.contentHashCode()
        return result
    }
}
