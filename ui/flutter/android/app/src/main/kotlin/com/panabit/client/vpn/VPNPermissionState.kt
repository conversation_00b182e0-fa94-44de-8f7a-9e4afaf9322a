/**
 * FILE: VPNPermissionState.kt
 *
 * DESCRIPTION:
 *     VPN permission state management for Android VPN service.
 *     Provides type-safe permission state handling and user-friendly permission flow.
 *     Based on iOS VPNPermissionStatus patterns while leveraging Android VpnService.prepare().
 *
 * AUTHOR: wei
 * HISTORY: 13/07/2025 create
 */

package com.panabit.client.vpn

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * NAME: VPNPermissionState
 *
 * DESCRIPTION:
 *     VPN permission state sealed class for type-safe permission management.
 *     Provides comprehensive permission state tracking for Android VPN operations.
 *     Supports permission request flow, error handling, and user guidance.
 *
 * DESIGN PRINCIPLES:
 *     - Type-safe permission state management
 *     - User-friendly permission request flow
 *     - Error handling with recovery guidance
 *     - Cross-platform compatibility with iOS patterns
 *
 * STATES:
 *     Unknown - Permission state not yet determined
 *     Granted - VPN permission granted, ready for connection
 *     Denied - VPN permission denied by user
 *     Requesting - Permission request in progress
 *     Error - Permission check/request failed with error details
 */
@Parcelize
sealed class VPNPermissionState : Parcelable {
    
    @Parcelize
    object Unknown : VPNPermissionState()
    
    @Parcelize
    object Granted : VPNPermissionState()
    
    @Parcelize
    object Denied : VPNPermissionState()
    
    @Parcelize
    object Requesting : VPNPermissionState()
    
    @Parcelize
    data class Error(
        val message: String,
        val canRetry: Boolean = true
    ) : VPNPermissionState()

    // MARK: - State Query Properties

    /**
     * NAME: isGranted
     *
     * DESCRIPTION:
     *     Indicates whether VPN permission is granted and ready for use.
     *
     * RETURNS:
     *     Boolean - true if permission is granted, false otherwise
     */
    val isGranted: Boolean
        get() = this is Granted

    /**
     * NAME: canRequestPermission
     *
     * DESCRIPTION:
     *     Indicates whether permission can be requested in current state.
     *
     * RETURNS:
     *     Boolean - true if permission request is allowed, false otherwise
     */
    val canRequestPermission: Boolean
        get() = when (this) {
            is Unknown, is Denied -> true
            is Error -> canRetry
            else -> false
        }

    /**
     * NAME: isInProgress
     *
     * DESCRIPTION:
     *     Indicates whether permission request is currently in progress.
     *
     * RETURNS:
     *     Boolean - true if request is in progress, false otherwise
     */
    val isInProgress: Boolean
        get() = this is Requesting

    /**
     * NAME: name
     *
     * DESCRIPTION:
     *     Gets the human-readable name of the current permission state.
     *
     * RETURNS:
     *     String - State name for logging and debugging
     */
    val name: String
        get() = when (this) {
            is Unknown -> "Unknown"
            is Granted -> "Granted"
            is Denied -> "Denied"
            is Requesting -> "Requesting"
            is Error -> "Error"
        }

    // MARK: - Flutter Compatibility

    /**
     * NAME: toFlutterMap
     *
     * DESCRIPTION:
     *     Converts permission state to Flutter-compatible map format.
     *
     * RETURNS:
     *     Map<String, Any> - Flutter-compatible permission state information
     */
    fun toFlutterMap(): Map<String, Any> {
        return when (this) {
            is Unknown -> mapOf(
                "state" to "unknown",
                "timestamp" to System.currentTimeMillis()
            )
            is Granted -> mapOf(
                "state" to "granted",
                "timestamp" to System.currentTimeMillis()
            )
            is Denied -> mapOf(
                "state" to "denied",
                "timestamp" to System.currentTimeMillis()
            )
            is Requesting -> mapOf(
                "state" to "requesting",
                "timestamp" to System.currentTimeMillis()
            )
            is Error -> mapOf(
                "state" to "error",
                "message" to message,
                "can_retry" to canRetry,
                "timestamp" to System.currentTimeMillis()
            )
        }
    }

    companion object {
        /**
         * NAME: fromVpnServicePrepare
         *
         * DESCRIPTION:
         *     Creates VPNPermissionState from VpnService.prepare() result.
         *     Maps Android VPN permission check result to type-safe state.
         *
         * PARAMETERS:
         *     prepareIntent - Result from VpnService.prepare() call
         *
         * RETURNS:
         *     VPNPermissionState - Corresponding permission state
         */
        fun fromVpnServicePrepare(prepareIntent: android.content.Intent?): VPNPermissionState {
            return if (prepareIntent == null) {
                Granted
            } else {
                Denied
            }
        }
    }
}
