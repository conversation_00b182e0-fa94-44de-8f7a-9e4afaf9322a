/**
 * FILE: PlatformModule.kt
 *
 * DESCRIPTION:
 *     ITforce WAN Android platform module dependency injection configuration.
 *     Defines Android platform-specific components following simplified architecture design.
 *     Focuses on essential platform integration without over-engineering.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create, 13/07/2025 refactor for simplified architecture
 */

package com.panabit.client.di

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import com.panabit.client.platform.PermissionManager
import org.koin.dsl.module
import org.koin.android.ext.koin.androidContext

/**
 * NAME: platformModule
 *
 * DESCRIPTION:
 *     Platform module defining dependency injection configuration for essential Android platform components.
 *     Follows simplified architecture by including only core platform integration components.
 *     Removes over-engineered features like performance monitoring and complex optimization managers.
 *
 * COMPONENTS:
 *     - PermissionManager: Basic permission handling
 *
 * DESIGN PRINCIPLES:
 *     - Simplified architecture avoiding over-engineering
 *     - Focus on core VPN platform integration
 *     - Remove complex optimization and monitoring features
 *     - Maintain essential Android platform compatibility
 */
val platformModule = module {

    // Basic Permission Management
    single<PermissionManager> {
        PermissionManager(androidContext())
    }

    // Note: Removed components to follow simplified architecture:
    // - VPNInterfaceManager (managed directly by VPNService to avoid component overlap)
    // - SystemOptimizationManager (over-engineering)
    // - BatteryOptimizationManager (over-engineering)
    // - NetworkMonitor (functionality moved to ConnectionManager)
    // - PerformanceManager (over-engineering)
    //
    // These components violated the simplified architecture principle and
    // created unnecessary complexity and component overlap.
    // VPNInterfaceManager is now created directly in VPNService when needed.
}
