/**
 * FILE: StateTransitionHelper.kt
 *
 * DESCRIPTION:
 *     Helper class for common VPN state transitions and state management operations.
 *     Provides convenient methods for typical VPN operation state changes while
 *     maintaining consistency with the simplified architecture principles.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.connection.state

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.util.Log
import com.panabit.client.connection.models.VPNState
import com.panabit.client.connection.models.ServerInfo
import com.panabit.client.connection.models.ConnectionProgress
import com.panabit.client.infrastructure.error.VPNServiceError

/**
 * NAME: StateTransitionHelper
 *
 * DESCRIPTION:
 *     Helper class providing convenient methods for common VPN state transitions.
 *     Encapsulates state transition logic and provides type-safe transition methods
 *     for typical VPN operations like connection, disconnection, and error handling.
 *
 * DESIGN PRINCIPLES:
 *     - Simplified state transition patterns
 *     - Type-safe transition methods
 *     - Consistent error handling
 *     - Android platform optimization
 */
object StateTransitionHelper {
    private const val TAG = "StateTransitionHelper"

    // MARK: - Connection State Transitions
    
    /**
     * NAME: startConnection
     *
     * DESCRIPTION:
     *     Initiates connection state transition with server information.
     *
     * PARAMETERS:
     *     stateManager - VPN state manager instance
     *     server - Target server information
     */
    suspend fun startConnection(stateManager: VPNStateManager, server: ServerInfo) {
        Log.d(TAG, "Starting connection to server: ${server.displayName}")
        val connectingState = VPNState.Connecting(
            server = server,
            progress = ConnectionProgress.INITIALIZING
        )
        stateManager.updateState(connectingState)
    }
    
    /**
     * NAME: updateConnectionProgress
     *
     * DESCRIPTION:
     *     Updates connection progress within connecting state.
     *
     * PARAMETERS:
     *     stateManager - VPN state manager instance
     *     progress - New connection progress
     */
    suspend fun updateConnectionProgress(stateManager: VPNStateManager, progress: ConnectionProgress) {
        val currentState = stateManager.currentStateValue
        if (currentState is VPNState.Connecting) {
            val updatedState = currentState.copy(progress = progress)
            stateManager.updateState(updatedState)
            Log.d(TAG, "Connection progress updated: $progress")
        } else {
            Log.w(TAG, "Cannot update progress - not in connecting state: $currentState")
        }
    }
    
    /**
     * NAME: completeConnection
     *
     * DESCRIPTION:
     *     Completes connection transition to connected state.
     *
     * PARAMETERS:
     *     stateManager - VPN state manager instance
     *     server - Connected server information
     *     tunnelIP - Assigned tunnel IP address
     *     latencyMs - Authentication latency in milliseconds (optional)
     */
    suspend fun completeConnection(stateManager: VPNStateManager, server: ServerInfo, tunnelIP: String, latencyMs: Int? = null) {
        Log.d(TAG, "Connection completed to server: ${server.displayName}, tunnel IP: $tunnelIP, latency: ${latencyMs ?: "unknown"}ms")
        val connectedState = VPNState.Connected(
            server = server,
            connectedAt = System.currentTimeMillis(),
            tunnelIP = tunnelIP,
            latencyMs = latencyMs
        )
        stateManager.updateState(connectedState)
    }

    // MARK: - Disconnection State Transitions
    
    /**
     * NAME: startDisconnection
     *
     * DESCRIPTION:
     *     Initiates disconnection state transition.
     *
     * PARAMETERS:
     *     stateManager - VPN state manager instance
     */
    suspend fun startDisconnection(stateManager: VPNStateManager) {
        Log.d(TAG, "Starting disconnection")
        stateManager.updateState(VPNState.Disconnecting)
    }
    
    /**
     * NAME: completeDisconnection
     *
     * DESCRIPTION:
     *     Completes disconnection transition to disconnected state.
     *
     * PARAMETERS:
     *     stateManager - VPN state manager instance
     */
    suspend fun completeDisconnection(stateManager: VPNStateManager) {
        Log.d(TAG, "Disconnection completed")
        stateManager.updateState(VPNState.Disconnected)
    }

    // MARK: - Reconnection State Transitions
    
    /**
     * NAME: startReconnection
     *
     * DESCRIPTION:
     *     Initiates reconnection state transition.
     *
     * PARAMETERS:
     *     stateManager - VPN state manager instance
     *     server - Server to reconnect to
     *     attempt - Reconnection attempt number
     *     reason - Reason for reconnection
     */
    suspend fun startReconnection(
        stateManager: VPNStateManager,
        server: ServerInfo,
        attempt: Int,
        reason: String
    ) {
        Log.d(TAG, "Starting reconnection attempt $attempt: $reason")
        // Since we removed Reconnecting state, transition directly to Connecting
        val connectingState = VPNState.Connecting(
            server = server,
            progress = com.panabit.client.connection.models.ConnectionProgress.INITIALIZING
        )
        stateManager.updateState(connectingState)
    }

    // MARK: - Error State Transitions
    
    /**
     * NAME: handleConnectionError
     *
     * DESCRIPTION:
     *     Handles connection error and transitions to error state.
     *
     * PARAMETERS:
     *     stateManager - VPN state manager instance
     *     error - VPN service error
     *     lastServer - Last connected server (optional)
     */
    suspend fun handleConnectionError(
        stateManager: VPNStateManager,
        error: VPNServiceError,
        lastServer: ServerInfo? = null
    ) {
        Log.e(TAG, "Connection error: ${error.message}")
        val errorState = VPNState.Error(
            error = error,
            lastServer = lastServer ?: stateManager.getConnectedServer()
        )
        stateManager.updateState(errorState)
    }
    
    /**
     * NAME: handleAuthenticationError
     *
     * DESCRIPTION:
     *     Handles authentication error with specific error type.
     *
     * PARAMETERS:
     *     stateManager - VPN state manager instance
     *     errorMessage - Authentication error message
     *     isCredentialIssue - Whether error is related to credentials
     */
    suspend fun handleAuthenticationError(
        stateManager: VPNStateManager,
        errorMessage: String,
        isCredentialIssue: Boolean = true
    ) {
        val authError = VPNServiceError.AuthenticationFailed(
            errorMessage = errorMessage,
            isCredentialIssue = isCredentialIssue
        )
        handleConnectionError(stateManager, authError)
    }
    
    /**
     * NAME: handleNetworkError
     *
     * DESCRIPTION:
     *     Handles network-related error.
     *
     * PARAMETERS:
     *     stateManager - VPN state manager instance
     *     errorMessage - Network error message
     *     isTemporary - Whether error is likely temporary
     */
    suspend fun handleNetworkError(
        stateManager: VPNStateManager,
        errorMessage: String,
        isTemporary: Boolean = true
    ) {
        val networkError = VPNServiceError.NetworkUnavailable(
            errorMessage = errorMessage,
            isTemporary = isTemporary
        )
        handleConnectionError(stateManager, networkError)
    }

    // MARK: - State Recovery Methods
    
    /**
     * NAME: recoverFromError
     *
     * DESCRIPTION:
     *     Recovers from error state by transitioning to disconnected.
     *
     * PARAMETERS:
     *     stateManager - VPN state manager instance
     */
    suspend fun recoverFromError(stateManager: VPNStateManager) {
        val currentState = stateManager.currentStateValue
        if (currentState is VPNState.Error) {
            Log.d(TAG, "Recovering from error state")
            stateManager.updateState(VPNState.Disconnected)
        }
    }
    
    /**
     * NAME: forceReset
     *
     * DESCRIPTION:
     *     Forces state reset to disconnected for emergency recovery.
     *
     * PARAMETERS:
     *     stateManager - VPN state manager instance
     */
    suspend fun forceReset(stateManager: VPNStateManager) {
        Log.w(TAG, "Force resetting state to disconnected")
        stateManager.forceUpdateState(VPNState.Disconnected)
    }

    // MARK: - State Validation Helpers
    
    /**
     * NAME: canStartConnection
     *
     * DESCRIPTION:
     *     Checks if connection can be started from current state.
     *
     * PARAMETERS:
     *     stateManager - VPN state manager instance
     *
     * RETURNS:
     *     Boolean - true if connection can be started, false otherwise
     */
    fun canStartConnection(stateManager: VPNStateManager): Boolean {
        val currentState = stateManager.currentStateValue
        return when (currentState) {
            is VPNState.Disconnected, is VPNState.Error -> true
            else -> false
        }
    }
    
    /**
     * NAME: canStartDisconnection
     *
     * DESCRIPTION:
     *     Checks if disconnection can be started from current state.
     *
     * PARAMETERS:
     *     stateManager - VPN state manager instance
     *
     * RETURNS:
     *     Boolean - true if disconnection can be started, false otherwise
     */
    fun canStartDisconnection(stateManager: VPNStateManager): Boolean {
        val currentState = stateManager.currentStateValue
        return when (currentState) {
            is VPNState.Connected, is VPNState.Connecting -> true
            else -> false
        }
    }
    
    /**
     * NAME: isStuckState
     *
     * DESCRIPTION:
     *     Checks if current state appears to be stuck based on age.
     *
     * PARAMETERS:
     *     stateManager - VPN state manager instance
     *     timeoutMs - Timeout in milliseconds (default: 30 seconds)
     *
     * RETURNS:
     *     Boolean - true if state appears stuck, false otherwise
     */
    fun isStuckState(stateManager: VPNStateManager, timeoutMs: Long = 30_000L): Boolean {
        val currentState = stateManager.currentStateValue
        val stateAge = stateManager.getStateAge()
        
        return currentState.isOperationInProgress && stateAge > timeoutMs
    }
}
