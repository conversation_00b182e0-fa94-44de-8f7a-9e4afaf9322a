// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      VPNServiceError.kt
//  *
//  * DESCRIPTION :    VPN服务错误类型定义，提供统一的错误处理机制
//  *                  
//  *                  UPDATED: 24/07/2025 - 添加统一错误代码支持
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  *                  24/07/2025 update - 添加VPNErrorCode支持
//  ******************************************************************************/

package com.panabit.client.infrastructure.error

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Locale

/**
 * NAME: VPNServiceError
 *
 * DESCRIPTION:
 *     VPN服务错误的基类，定义了所有VPN相关错误的通用结构和行为。
 *     提供统一的错误处理机制，支持错误分类、本地化消息和Flutter集成。
 *
 * FEATURES:
 *     - 统一的错误代码体系（与Windows版本兼容）
 *     - 支持错误分类和严重程度
 *     - 本地化错误消息支持
 *     - Flutter平台集成
 *     - 详细的错误上下文信息
 *
 * UPDATED: 24/07/2025 - 添加errorCode属性和本地化支持
 */
sealed class VPNServiceError(
    open val errorMessage: String,
    open val errorCode: VPNErrorCode,
    val timestamp: Long = System.currentTimeMillis(),
    override val cause: Throwable? = null
) : Exception(errorMessage, cause), Parcelable {

    /**
     * NAME: ConnectionFailed
     *
     * DESCRIPTION:
     *     Network connection establishment failures including server unreachable,
     *     connection timeout, and network infrastructure issues.
     */
    @Parcelize
    data class ConnectionFailed(
        override val errorMessage: String,
        val serverAddress: String? = null,
        val retryCount: Int = 0,
        val networkType: String? = null,
        override val errorCode: VPNErrorCode = VPNErrorCode.NETWORK_UNREACHABLE
    ) : VPNServiceError(errorMessage, errorCode)

    /**
     * NAME: AuthenticationFailed
     *
     * DESCRIPTION:
     *     User authentication and authorization failures during VPN connection process.
     */
    @Parcelize
    data class AuthenticationFailed(
        override val errorMessage: String,
        val serverErrorCode: String? = null,
        val isCredentialIssue: Boolean = true,
        val authMethod: String? = null,
        override val errorCode: VPNErrorCode = VPNErrorCode.AUTH_INVALID_CREDENTIALS
    ) : VPNServiceError(errorMessage, errorCode)

    /**
     * NAME: NetworkUnavailable
     *
     * DESCRIPTION:
     *     Network infrastructure unavailability including internet connectivity issues.
     */
    @Parcelize
    data class NetworkUnavailable(
        override val errorMessage: String,
        val networkType: String? = null,
        val isTemporary: Boolean = true,
        override val errorCode: VPNErrorCode = VPNErrorCode.NETWORK_UNREACHABLE
    ) : VPNServiceError(errorMessage, errorCode)

    /**
     * NAME: PermissionDenied
     *
     * DESCRIPTION:
     *     System permission denials including VPN permission and battery optimization.
     */
    @Parcelize
    data class PermissionDenied(
        override val errorMessage: String,
        val permissionType: String = "VPN_PERMISSION",
        val canRetry: Boolean = true,
        override val errorCode: VPNErrorCode = VPNErrorCode.PLATFORM_PERMISSION_DENIED
    ) : VPNServiceError(errorMessage, errorCode)

    /**
     * NAME: ConfigurationInvalid
     *
     * DESCRIPTION:
     *     Invalid VPN configuration parameters.
     */
    @Parcelize
    data class ConfigurationInvalid(
        override val errorMessage: String,
        val configField: String? = null,
        val expectedFormat: String? = null,
        override val errorCode: VPNErrorCode = VPNErrorCode.CONFIG_INVALID
    ) : VPNServiceError(errorMessage, errorCode)

    /**
     * NAME: ReconnectionFailed
     *
     * DESCRIPTION:
     *     Automatic reconnection failures.
     */
    @Parcelize
    data class ReconnectionFailed(
        override val errorMessage: String,
        val attemptCount: Int = 0,
        val lastError: String? = null,
        override val errorCode: VPNErrorCode = VPNErrorCode.TUNNEL_CLOSED_UNEXPECTED
    ) : VPNServiceError(errorMessage, errorCode)

    /**
     * NAME: InterfaceError
     *
     * DESCRIPTION:
     *     VPN interface and tunnel management errors.
     */
    @Parcelize
    data class InterfaceError(
        override val errorMessage: String,
        val interfaceType: String = "TUN_INTERFACE",
        val systemError: String? = null,
        override val errorCode: VPNErrorCode = VPNErrorCode.TUNNEL_DEVICE_ERROR
    ) : VPNServiceError(errorMessage, errorCode)

    /**
     * NAME: ServerUnavailable
     *
     * DESCRIPTION:
     *     VPN server unavailability and service disruptions.
     */
    @Parcelize
    data class ServerUnavailable(
        override val errorMessage: String,
        val serverId: String? = null,
        val alternativeAvailable: Boolean = false,
        override val errorCode: VPNErrorCode = VPNErrorCode.NETWORK_UNREACHABLE
    ) : VPNServiceError(errorMessage, errorCode)

    /**
     * NAME: ProtocolError
     *
     * DESCRIPTION:
     *     SDWAN protocol communication errors.
     */
    @Parcelize
    data class ProtocolError(
        override val errorMessage: String,
        val protocolVersion: String? = null,
        val packetType: String? = null,
        override val errorCode: VPNErrorCode = VPNErrorCode.PROTOCOL_HANDSHAKE_FAILED
    ) : VPNServiceError(errorMessage, errorCode)

    /**
     * NAME: TimeoutError
     *
     * DESCRIPTION:
     *     Operation timeout errors.
     */
    @Parcelize
    data class TimeoutError(
        override val errorMessage: String,
        val operation: String,
        val timeoutDuration: Long,
        val expectedDuration: Long,
        override val errorCode: VPNErrorCode = VPNErrorCode.NETWORK_TIMEOUT
    ) : VPNServiceError(errorMessage, errorCode)

    // ==========================================================================
    // 错误类型和分类方法
    // ==========================================================================

    /**
     * NAME: getErrorType
     *
     * DESCRIPTION:
     *     获取错误类型字符串，用于日志记录和调试
     *
     * RETURNS:
     *     String - 错误类型字符串
     */
    fun getErrorType(): String = when (this) {
        is ConnectionFailed -> "CONNECTION_FAILED"
        is AuthenticationFailed -> "AUTHENTICATION_FAILED"
        is NetworkUnavailable -> "NETWORK_UNAVAILABLE"
        is PermissionDenied -> "PERMISSION_DENIED"
        is ConfigurationInvalid -> "CONFIGURATION_INVALID"
        is ReconnectionFailed -> "RECONNECTION_FAILED"
        is InterfaceError -> "INTERFACE_ERROR"
        is ServerUnavailable -> "SERVER_UNAVAILABLE"
        is ProtocolError -> "PROTOCOL_ERROR"
        is TimeoutError -> "TIMEOUT_ERROR"
    }

    /**
     * NAME: getUserFriendlyMessage
     *
     * DESCRIPTION:
     *     获取用户友好的本地化错误消息
     *
     * PARAMETERS:
     *     locale - 目标语言环境（可选，默认使用系统语言）
     *
     * RETURNS:
     *     String - 本地化的用户友好错误消息
     */
    fun getUserFriendlyMessage(locale: Locale = Locale.getDefault()): String {
        return try {
            val localizer = VPNErrorLocalizer.getInstance(locale)
            localizer.getLocalizedUserFriendlyMessage(this)
        } catch (e: Exception) {
            // 如果本地化失败，回退到原有的中文消息
            getLegacyChineseMessage()
        }
    }

    /**
     * NAME: getRecoverySuggestion
     *
     * DESCRIPTION:
     *     获取错误恢复建议
     *
     * PARAMETERS:
     *     locale - 目标语言环境（可选，默认使用系统语言）
     *
     * RETURNS:
     *     String - 本地化的恢复建议
     */
    fun getRecoverySuggestion(locale: Locale = Locale.getDefault()): String {
        return try {
            val localizer = VPNErrorLocalizer.getInstance(locale)
            localizer.getRecoverySuggestion(errorCode)
        } catch (e: Exception) {
            "请检查网络连接后重试"
        }
    }

    /**
     * NAME: getLegacyChineseMessage
     *
     * DESCRIPTION:
     *     获取原有的中文错误消息（向后兼容）
     *
     * RETURNS:
     *     String - 中文错误消息
     */
    private fun getLegacyChineseMessage(): String = when (this) {
        is ConnectionFailed -> "连接服务器失败，请检查网络连接后重试"
        is AuthenticationFailed -> if (isCredentialIssue) {
            "身份验证失败，请检查用户名和密码"
        } else {
            "服务器认证失败，请稍后重试"
        }
        is NetworkUnavailable -> if (isTemporary) {
            "网络暂时不可用，正在尝试重新连接"
        } else {
            "网络不可用，请检查网络设置"
        }
        is PermissionDenied -> when (permissionType) {
            "VPN_PERMISSION" -> "VPN权限被拒绝，请在设置中允许VPN权限"
            "BATTERY_OPTIMIZATION" -> "请将应用加入电池优化白名单以确保稳定连接"
            else -> "权限被拒绝，请检查应用权限设置"
        }
        is ConfigurationInvalid -> "配置信息无效，请重新配置VPN设置"
        is ReconnectionFailed -> "自动重连失败，正在尝试恢复连接"
        is InterfaceError -> "VPN接口错误，请重新连接"
        is ServerUnavailable -> if (alternativeAvailable) {
            "当前服务器不可用，正在切换到其他服务器"
        } else {
            "服务器暂时不可用，请稍后重试"
        }
        is ProtocolError -> "协议通信错误，请检查网络连接"
        is TimeoutError -> "操作超时，请检查网络连接后重试"
    }

    // ==========================================================================
    // Flutter集成方法
    // ==========================================================================

    /**
     * NAME: toMap
     *
     * DESCRIPTION:
     *     将错误对象转换为Map，用于传递给Flutter层
     *
     * RETURNS:
     *     Map<String, Any> - 包含错误信息的Map
     */
    fun toMap(): Map<String, Any> {
        val baseMap = mutableMapOf<String, Any>(
            "type" to getErrorType(),
            "message" to errorMessage,
            "error_code" to errorCode.code,
            "error_category" to errorCode.category,
            "error_severity" to errorCode.severity.name,
            "timestamp" to timestamp
        )

        // 添加特定错误类型的字段
        when (this) {
            is ConnectionFailed -> {
                serverAddress?.let { baseMap["server_address"] = it }
                baseMap["retry_count"] = retryCount
                networkType?.let { baseMap["network_type"] = it }
            }
            is AuthenticationFailed -> {
                serverErrorCode?.let { baseMap["server_error_code"] = it }
                baseMap["is_credential_issue"] = isCredentialIssue
                authMethod?.let { baseMap["auth_method"] = it }
            }
            is NetworkUnavailable -> {
                networkType?.let { baseMap["network_type"] = it }
                baseMap["is_temporary"] = isTemporary
            }
            is PermissionDenied -> {
                baseMap["permission_type"] = permissionType
                baseMap["can_retry"] = canRetry
            }
            is ConfigurationInvalid -> {
                configField?.let { baseMap["config_field"] = it }
                expectedFormat?.let { baseMap["expected_format"] = it }
            }
            is ReconnectionFailed -> {
                baseMap["attempt_count"] = attemptCount
                lastError?.let { baseMap["last_error"] = it }
            }
            is InterfaceError -> {
                baseMap["interface_type"] = interfaceType
                systemError?.let { baseMap["system_error"] = it }
            }
            is ServerUnavailable -> {
                serverId?.let { baseMap["server_id"] = it }
                baseMap["alternative_available"] = alternativeAvailable
            }
            is ProtocolError -> {
                protocolVersion?.let { baseMap["protocol_version"] = it }
                packetType?.let { baseMap["packet_type"] = it }
            }
            is TimeoutError -> {
                baseMap["operation"] = operation
                baseMap["timeout_duration"] = timeoutDuration
                baseMap["expected_duration"] = expectedDuration
            }
        }

        return baseMap
    }

    // ==========================================================================
    // 兼容性方法（用于ErrorHandler）
    // ==========================================================================

    /**
     * NAME: isRecoverable
     *
     * DESCRIPTION:
     *     判断错误是否可恢复（可重试）
     *
     * RETURNS:
     *     Boolean - 如果错误可恢复返回true
     */
    fun isRecoverable(): Boolean = when (this) {
        is NetworkUnavailable -> isTemporary
        is ConnectionFailed -> true
        is TimeoutError -> true
        is AuthenticationFailed -> !isCredentialIssue
        is PermissionDenied -> canRetry
        is ReconnectionFailed -> attemptCount < 3
        is ServerUnavailable -> alternativeAvailable
        is InterfaceError -> true
        is ProtocolError -> true
        is ConfigurationInvalid -> false
    }

    /**
     * NAME: getRetryDelay
     *
     * DESCRIPTION:
     *     获取重试延迟时间（毫秒）
     *
     * RETURNS:
     *     Long - 重试延迟时间
     */
    fun getRetryDelay(): Long = when (this) {
        is NetworkUnavailable -> if (isTemporary) 5000L else 30000L
        is ConnectionFailed -> (retryCount + 1) * 3000L
        is TimeoutError -> 10000L
        is AuthenticationFailed -> if (isCredentialIssue) 0L else 15000L
        is PermissionDenied -> 0L // 需要用户手动操作
        is ReconnectionFailed -> (attemptCount + 1) * 5000L
        is ServerUnavailable -> if (alternativeAvailable) 2000L else 60000L
        is InterfaceError -> 8000L
        is ProtocolError -> 12000L
        is ConfigurationInvalid -> 0L // 需要用户修复配置
    }
}
