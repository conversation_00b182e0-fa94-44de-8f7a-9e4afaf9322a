/**
 * FILE: VPNState.kt
 *
 * DESCRIPTION:
 *     Android VPN state management using Kotlin sealed classes for type-safe state handling.
 *     Provides unified VPN state representation with Flutter compatibility and protocol mapping.
 *     Designed to maintain functional consistency with iOS VPNState while leveraging Android
 *     platform features like Parcelable for efficient state persistence and IPC communication.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.connection.models

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import com.panabit.client.infrastructure.error.VPNServiceError

/**
 * NAME: VPNState
 *
 * DESCRIPTION:
 *     Unified VPN state sealed class that provides type-safe state management for Android VPN operations.
 *     Combines operation state, connection state, and server context into a single comprehensive
 *     state machine that eliminates state inconsistency issues. Supports Flutter communication,
 *     protocol mapping, and Android platform features like Parcelable serialization.
 *
 * DESIGN PRINCIPLES:
 *     - Single source of truth for all VPN state information
 *     - Self-contained state with associated data for context
 *     - Platform-optimized design with Android-specific features
 *     - Clear state transitions without manual synchronization
 *     - Functional consistency with iOS implementation
 *
 * STATES:
 *     Disconnected - VPN is disconnected and idle
 *     Connecting - VPN connection establishment in progress with server and progress info
 *     Connected - VPN tunnel active with server info, connection time, and tunnel IP
 *     Disconnecting - VPN disconnection in progress
 *     Error - VPN error state with error details and optional last server info
 */
@Parcelize
sealed class VPNState : Parcelable {
    @Parcelize
    object Disconnected : VPNState()

    @Parcelize
    data class Connecting(
        val server: ServerInfo,
        val progress: ConnectionProgress
    ) : VPNState()

    @Parcelize
    data class Connected(
        val server: ServerInfo,
        val connectedAt: Long,
        val tunnelIP: String,
        val latencyMs: Int? = null
    ) : VPNState()

    @Parcelize
    object Disconnecting : VPNState()

    @Parcelize
    data class Error(
        val error: VPNServiceError,
        val lastServer: ServerInfo? = null
    ) : VPNState()

    // MARK: - State Query Properties

    /**
     * NAME: isOperationInProgress
     *
     * DESCRIPTION:
     *     Indicates whether a VPN operation is currently in progress.
     *     Used to prevent concurrent operations and provide UI feedback.
     *     Simplified to only include Connecting and Disconnecting states.
     *
     * RETURNS:
     *     Boolean - true if operation is in progress, false otherwise
     */
    val isOperationInProgress: Boolean
        get() = when (this) {
            is Connecting, is Disconnecting -> true
            else -> false
        }

    /**
     * NAME: isConnected
     *
     * DESCRIPTION:
     *     Indicates whether VPN is currently connected and active.
     *
     * RETURNS:
     *     Boolean - true if VPN is connected, false otherwise
     */
    val isConnected: Boolean
        get() = this is Connected

    /**
     * NAME: isDisconnected
     *
     * DESCRIPTION:
     *     Indicates whether VPN is currently disconnected.
     *
     * RETURNS:
     *     Boolean - true if VPN is disconnected, false otherwise
     */
    val isDisconnected: Boolean
        get() = this is Disconnected

    /**
     * NAME: isDisconnecting
     *
     * DESCRIPTION:
     *     Indicates whether VPN is currently in the process of disconnecting.
     *
     * RETURNS:
     *     Boolean - true if VPN is disconnecting, false otherwise
     */
    val isDisconnecting: Boolean
        get() = this is Disconnecting

    /**
     * NAME: connectedServer
     *
     * DESCRIPTION:
     *     Gets the server information associated with the current state.
     *     Returns server info for states that have server context.
     *
     * RETURNS:
     *     ServerInfo? - Server information if available, null otherwise
     */
    val connectedServer: ServerInfo?
        get() = when (this) {
            is Connected -> server
            is Connecting -> server
            is Error -> lastServer
            else -> null
        }

    /**
     * NAME: name
     *
     * DESCRIPTION:
     *     Gets the human-readable name of the current VPN state.
     *     Used for logging and debugging purposes.
     *
     * RETURNS:
     *     String - State name
     */
    val name: String
        get() = when (this) {
            is Disconnected -> "Disconnected"
            is Connecting -> "Connecting"
            is Connected -> "Connected"
            is Disconnecting -> "Disconnecting"
            is Error -> "Error"
        }

    // MARK: - Protocol and Flutter Compatibility

    /**
     * NAME: protocolValue
     *
     * DESCRIPTION:
     *     Maps VPNState to Go backend protocol state values for SDWAN communication.
     *     Ensures compatibility with Windows backend state machine constants.
     *
     * RETURNS:
     *     UByte - Protocol state value (2-7 matching Go backend constants)
     */
    val protocolValue: UByte
        get() = when (this) {
            is Disconnected, is Error -> 6u  // StateClosed
            is Connecting -> when (progress) {
                ConnectionProgress.INITIALIZING -> 2u  // StateInit
                ConnectionProgress.RESOLVING_SERVER -> 3u  // StateInit1
                ConnectionProgress.AUTHENTICATING,
                ConnectionProgress.ESTABLISHING_TUNNEL -> 4u  // StateAuth
            }
            is Connected -> 5u  // StateData
            is Disconnecting -> 6u  // StateClosed
        }

    /**
     * NAME: flutterStatusString
     *
     * DESCRIPTION:
     *     Provides Flutter-compatible status strings for Platform Channel communication.
     *     Maps unified VPNState to Flutter ConnectionStatus enum values for cross-platform
     *     UI consistency. Must match Flutter lib/utils/constants.dart ConnectionStatusStrings.
     *
     * RETURNS:
     *     String - Status string compatible with Flutter ConnectionStatus enum
     */
    val flutterStatusString: String
        get() = when (this) {
            is Disconnected -> "disconnected"
            is Connecting -> "connecting"
            is Connected -> "connected"
            is Disconnecting -> "disconnecting"
            is Error -> "error"
        }

    /**
     * NAME: toFlutterMap
     *
     * DESCRIPTION:
     *     Converts VPN state to Flutter-compatible map format for cross-platform communication.
     *     Includes state information, server details, timestamps, and state-specific properties
     *     for comprehensive state transfer to Flutter UI layer.
     *
     * RETURNS:
     *     Map<String, Any> - Flutter-compatible state information map
     */
    fun toFlutterMap(): Map<String, Any> {
        return when (this) {
            is Disconnected -> mapOf<String, Any>(
                "status" to "disconnected",  // Flutter UI expects 'status' field
                "message" to "disconnected",  // Add message field for notification display (matching iOS)
                "timestamp" to (System.currentTimeMillis() / 1000)  // Use seconds for consistency with iOS
            )
            is Connecting -> mapOf<String, Any>(
                "status" to "connecting",  // Flutter UI expects 'status' field
                "message" to "connecting",  // Add message field for notification display (matching iOS)
                "server" to server.toMap(),
                "progress" to progress.name.lowercase(),
                "timestamp" to (System.currentTimeMillis() / 1000)  // Use seconds for consistency with iOS
            )
            is Connected -> mapOf<String, Any>(
                "status" to "connected",  // Flutter UI expects 'status' field
                "message" to "connected",  // Add message field for notification display (matching iOS)
                "server" to server.toMap(),
                "connected_time" to (connectedAt / 1000),  // Use seconds for consistency with iOS (field name matches iOS and Flutter)
                "tunnel_ip" to tunnelIP,
                "latency_ms" to (latencyMs ?: -1),  // Include authentication latency in milliseconds
                "timestamp" to (System.currentTimeMillis() / 1000)  // Use seconds for consistency with iOS
            )
            is Disconnecting -> mapOf<String, Any>(
                "status" to "disconnecting",  // Flutter UI expects 'status' field
                "message" to "disconnecting",  // Add message field for notification display (matching iOS)
                "timestamp" to (System.currentTimeMillis() / 1000)  // Use seconds for consistency with iOS
            )
            is Error -> mapOf<String, Any>(
                "status" to "error",  // Flutter UI expects 'status' field
                "message" to (error.getUserFriendlyMessage()),  // Use actual error message instead of hardcoded "error"
                "error" to error.toMap(),
                "last_server" to (lastServer?.toMap() ?: emptyMap<String, Any>()),
                "timestamp" to (System.currentTimeMillis() / 1000)  // Use seconds for consistency with iOS
            )

        }
    }

    // MARK: - State Transition Support

    companion object {
        /**
         * NAME: isValidStateTransition
         *
         * DESCRIPTION:
         *     Validates whether a state transition is allowed based on current and target states.
         *     Enhanced validation logic based on architecture design review, referencing iOS
         *     implementation patterns while maintaining Android-specific simplicity.
         *
         * PARAMETERS:
         *     from - Current VPN state
         *     to - Target VPN state
         *
         * RETURNS:
         *     Boolean - true if transition is valid, false otherwise
         */
        fun isValidStateTransition(from: VPNState, to: VPNState): Boolean {
            // Same state transition is always valid (no-op)
            if (from::class == to::class) return true

            return when (from) {
                is Disconnected -> {
                    // From Disconnected, can go to Connecting or Error (for permission/setup failures)
                    to is Connecting || to is Error
                }
                is Connecting -> {
                    // From Connecting, can go to Connected, Error, or Disconnecting
                    to is Connected || to is Error || to is Disconnecting
                }
                is Connected -> {
                    // From Connected, can go to Disconnecting or Error
                    to is Disconnecting || to is Error
                }
                is Disconnecting -> {
                    // From Disconnecting, can go to Disconnected or Error
                    to is Disconnected || to is Error
                }
                is Error -> {
                    // From Error, can go to Connecting or Disconnected (for retry/reset)
                    to is Connecting || to is Disconnected
                }
            }
        }

        /**
         * NAME: fromProtocolValue
         *
         * DESCRIPTION:
         *     Creates VPNState from Go backend protocol state value for SDWAN communication.
         *     Provides compatibility with Windows backend state machine constants.
         *
         * PARAMETERS:
         *     protocolValue - Go backend state value from protocol communication
         *     server - Server information for states that require it (optional)
         *
         * RETURNS:
         *     VPNState? - Corresponding VPN state, null if invalid protocol value
         */
        fun fromProtocolValue(protocolValue: UByte, server: ServerInfo? = null): VPNState? {
            return when (protocolValue.toInt()) {
                2 -> server?.let { Connecting(it, ConnectionProgress.INITIALIZING) } // StateInit
                3 -> server?.let { Connecting(it, ConnectionProgress.RESOLVING_SERVER) } // StateInit1
                4 -> server?.let { Connecting(it, ConnectionProgress.AUTHENTICATING) } // StateAuth
                5 -> server?.let { Connected(it, System.currentTimeMillis(), "", null) } // StateData
                6 -> Disconnected // StateClosed
                7 -> Error(com.panabit.client.infrastructure.error.VPNServiceError.AuthenticationFailed("Authentication failed"), server) // StateAuthFail
                else -> null
            }
        }
    }
}

/**
 * NAME: ConnectionProgress
 *
 * DESCRIPTION:
 *     Connection progress enumeration for tracking VPN connection establishment phases.
 *     Used within Connecting state to provide detailed progress information to UI.
 *
 * VALUES:
 *     INITIALIZING - Initial connection setup and preparation
 *     RESOLVING_SERVER - DNS resolution and server address lookup
 *     AUTHENTICATING - User authentication and credential validation
 *     ESTABLISHING_TUNNEL - VPN tunnel establishment and configuration
 */
enum class ConnectionProgress {
    INITIALIZING,
    RESOLVING_SERVER,
    AUTHENTICATING,
    ESTABLISHING_TUNNEL
}
