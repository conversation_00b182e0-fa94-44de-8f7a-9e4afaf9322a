/**
 * FILE: NetworkUtils.kt
 * AUTHOR: wei
 * DATE: 23/06/2025
 * VERSION: 1.0
 * DESCRIPTION: Network utility functions for Android platform
 * HISTORY:
 *   23/06/2025 - Initial implementation
 */

package com.panabit.client.infrastructure

import android.net.NetworkCapabilities.*

/**
 * NAME: NetworkUtils
 *
 * DESCRIPTION:
 *     Utility class providing network-related helper functions.
 *     Contains common network operations and type conversions.
 *
 * FEATURES:
 *     - Network type name conversion
 *     - Network capability checking
 *     - Network state validation
 */
object NetworkUtils {

    /**
     * NAME: getNetworkTypeName
     *
     * DESCRIPTION:
     *     Converts network type constant to human-readable name.
     *     Used for logging and debugging network changes.
     *
     * PARAMETERS:
     *     networkType - Network type constant from NetworkCapabilities
     *
     * RETURNS:
     *     String - Human-readable network type name
     */
    fun getNetworkTypeName(networkType: Int): String {
        return when (networkType) {
            TRANSPORT_WIFI -> "WiFi"
            TRANSPORT_CELLULAR -> "Cellular"
            TRANSPORT_ETHERNET -> "Ethernet"
            TRANSPORT_BLUETOOTH -> "Bluetooth"
            TRANSPORT_VPN -> "VPN"
            TRANSPORT_WIFI_AWARE -> "WiFi Aware"
            TRANSPORT_LOWPAN -> "LoWPAN"
            TRANSPORT_USB -> "USB"
            else -> "Unknown($networkType)"
        }
    }

    /**
     * NAME: isSignificantNetworkChange
     *
     * DESCRIPTION:
     *     Determines if a network change is significant enough to trigger VPN reconnection.
     *     Considers interface type changes and network availability changes.
     *
     * PARAMETERS:
     *     oldType - Previous network type
     *     newType - New network type
     *
     * RETURNS:
     *     Boolean - True if change requires VPN reconnection
     */
    fun isSignificantNetworkChange(oldType: Int, newType: Int): Boolean {
        return when {
            // WiFi to Cellular or vice versa
            (oldType == TRANSPORT_WIFI && newType == TRANSPORT_CELLULAR) ||
            (oldType == TRANSPORT_CELLULAR && newType == TRANSPORT_WIFI) -> true

            // From unknown to known network type (network became available)
            (oldType == -1 && newType != -1) -> true

            // From known to unknown network type (network lost)
            (oldType != -1 && newType == -1) -> true

            // Other changes don't require reconnection
            else -> false
        }
    }

    /**
     * NAME: isNetworkAvailable
     *
     * DESCRIPTION:
     *     Checks if the network type represents an available network.
     *
     * PARAMETERS:
     *     networkType - Network type constant
     *
     * RETURNS:
     *     Boolean - True if network is available
     */
    fun isNetworkAvailable(networkType: Int): Boolean {
        return networkType != -1
    }

    /**
     * NAME: isMobileNetwork
     *
     * DESCRIPTION:
     *     Checks if the network type is a mobile/cellular network.
     *
     * PARAMETERS:
     *     networkType - Network type constant
     *
     * RETURNS:
     *     Boolean - True if network is cellular
     */
    fun isMobileNetwork(networkType: Int): Boolean {
        return networkType == TRANSPORT_CELLULAR
    }

    /**
     * NAME: isWifiNetwork
     *
     * DESCRIPTION:
     *     Checks if the network type is a WiFi network.
     *
     * PARAMETERS:
     *     networkType - Network type constant
     *
     * RETURNS:
     *     Boolean - True if network is WiFi
     */
    fun isWifiNetwork(networkType: Int): Boolean {
        return networkType == TRANSPORT_WIFI
    }

    /**
     * NAME: getNetworkChangeDescription
     *
     * DESCRIPTION:
     *     Provides a descriptive string for network changes.
     *
     * PARAMETERS:
     *     oldType - Previous network type
     *     newType - New network type
     *
     * RETURNS:
     *     String - Description of the network change
     */
    fun getNetworkChangeDescription(oldType: Int, newType: Int): String {
        val oldName = getNetworkTypeName(oldType)
        val newName = getNetworkTypeName(newType)
        
        return when {
            oldType == newType -> "No change ($oldName)"
            oldType == -1 -> "Network became available ($newName)"
            newType == -1 -> "Network lost ($oldName)"
            else -> "Network changed ($oldName → $newName)"
        }
    }
}
