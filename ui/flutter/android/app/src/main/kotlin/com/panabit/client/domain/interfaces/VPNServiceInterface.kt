/**
 * FILE: VPNServiceInterface.kt
 *
 * DESCRIPTION:
 *     VPN service interface abstraction for dependency inversion principle.
 *     Provides unified VPN management interface for Platform Channel integration.
 *     Abstracts VPN operations from concrete implementation details.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create VPN service interface abstraction
 */

package com.panabit.client.domain.interfaces

import com.panabit.client.connection.models.VPNState
import com.panabit.client.connection.models.ServerInfo

/**
 * NAME: InterfaceInfo
 *
 * DESCRIPTION:
 *     Simple data class for network interface information.
 *     Used for Flutter communication compatibility.
 */
data class InterfaceInfo(
    val interfaceName: String?,
    val localIp: String?,
    val tunIp: String? = null,  // VPN tunnel IP address
    val timestamp: Long = System.currentTimeMillis() / 1000
) {
    companion object {
        fun empty() = InterfaceInfo(null, null, null)
        fun createDefault() = InterfaceInfo(null, null, null)
        fun create(interfaceName: String?, localIp: String?, tunIp: String? = null) =
            InterfaceInfo(interfaceName, localIp, tunIp)
    }

    fun toFlutterMap(): Map<String, Any?> = mapOf(
        "interface_name" to interfaceName,
        "local_ip" to localIp,
        "tun_ip" to tunIp,  // Flutter UI expects this field
        "timestamp" to timestamp
    )
}

/**
 * NAME: VPNServiceInterface
 *
 * DESCRIPTION:
 *     Abstract interface for VPN service operations.
 *     Provides unified API for VPN management, server operations, and status monitoring.
 *     Enables dependency inversion for Platform Channel handler.
 *
 * METHODS:
 *     - Lifecycle management (initialize, isInitialized, isHealthy)
 *     - Authentication (login)
 *     - Connection management (connect, disconnect)
 *     - Status monitoring (getStatus, getInterfaceInfo)
 *     - Server management (getServers, pingServer, pingServers)
 */
interface VPNServiceInterface {

    // MARK: - Lifecycle Management

    /**
     * NAME: initialize
     *
     * DESCRIPTION:
     *     Initialize VPN service components and dependencies.
     *
     * RETURNS:
     *     Result<Unit> - Success or failure of initialization
     */
    suspend fun initialize(): Result<Unit>

    /**
     * NAME: isInitialized
     *
     * DESCRIPTION:
     *     Check if VPN service is properly initialized.
     *
     * RETURNS:
     *     Boolean - true if initialized, false otherwise
     */
    fun isInitialized(): Boolean

    /**
     * NAME: isHealthy
     *
     * DESCRIPTION:
     *     Check if VPN service is healthy and responsive.
     *
     * RETURNS:
     *     Boolean - true if healthy, false otherwise
     */
    suspend fun isHealthy(): Boolean

    // MARK: - Authentication

    /**
     * NAME: login
     *
     * DESCRIPTION:
     *     Authenticate user with username and password.
     *
     * PARAMETERS:
     *     username - User login name
     *     password - User password
     *
     * RETURNS:
     *     Result<Map<String, Any>> - Login result with user data or error
     */
    suspend fun login(username: String, password: String): Result<Map<String, Any>>

    // MARK: - Connection Management

    /**
     * NAME: connect
     *
     * DESCRIPTION:
     *     Connect to VPN server with specified server ID.
     *     Returns interface information upon successful connection initiation.
     *
     * PARAMETERS:
     *     serverId - Target server identifier
     *
     * RETURNS:
     *     Result<Map<String, Any>> - Success with interface info or failure
     */
    suspend fun connect(serverId: String): Result<Map<String, Any>>

    /**
     * NAME: disconnect
     *
     * DESCRIPTION:
     *     Disconnect from current VPN connection.
     *
     * RETURNS:
     *     Result<Unit> - Success or failure of disconnection
     */
    suspend fun disconnect(): Result<Unit>

    // MARK: - Status Monitoring

    /**
     * NAME: getStatus
     *
     * DESCRIPTION:
     *     Get current VPN connection status.
     *
     * RETURNS:
     *     VPNState - Current VPN state with connection details
     */
    suspend fun getStatus(): VPNState

    /**
     * NAME: getInterfaceInfo
     *
     * DESCRIPTION:
     *     Get network interface information.
     *
     * RETURNS:
     *     InterfaceInfo - Network interface details
     */
    suspend fun getInterfaceInfo(): InterfaceInfo

    // MARK: - Server Management

    /**
     * NAME: getServers
     *
     * DESCRIPTION:
     *     Get list of available VPN servers.
     *
     * RETURNS:
     *     List<ServerInfo> - Available server list with status
     */
    suspend fun getServers(): List<ServerInfo>

    /**
     * NAME: pingServer
     *
     * DESCRIPTION:
     *     Ping single server to measure latency.
     *
     * PARAMETERS:
     *     serverId - Server identifier to ping
     *
     * RETURNS:
     *     Int - Ping latency in milliseconds, -1 if failed
     */
    suspend fun pingServer(serverId: String): Int

    /**
     * NAME: pingServers
     *
     * DESCRIPTION:
     *     Ping all available servers to measure latencies.
     *     Results are delivered through event callbacks.
     *
     * RETURNS:
     *     Result<Unit> - Success or failure of ping operation initiation
     */
    suspend fun pingServers(): Result<Unit>

    /**
     * NAME: setServerListUrl
     *
     * DESCRIPTION:
     *     Set server list provider URL for dynamic server list fetching.
     *     Updates the server list URL and triggers server list refresh.
     *
     * PARAMETERS:
     *     url - Server list provider URL
     *
     * RETURNS:
     *     Result<Unit> - Success or failure of URL setting operation
     */
    suspend fun setServerListUrl(url: String): Result<Unit>

    // MARK: - Internal Access

    /**
     * NAME: getConnectionManager
     *
     * DESCRIPTION:
     *     Get the underlying connection manager for internal operations.
     *     Used for accessing VPNStateEventSender and other internal components.
     *
     * RETURNS:
     *     ConnectionManager? - Connection manager instance or null if not available
     */
    fun getConnectionManager(): com.panabit.client.connection.ConnectionManager?
}
