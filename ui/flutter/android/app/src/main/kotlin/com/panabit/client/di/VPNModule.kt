/**
 * FILE: VPNModule.kt
 *
 * DESCRIPTION:
 *     Panabit Client Android VPN module dependency injection configuration.
 *     Defines VPN-related component dependencies following simplified architecture design.
 *     Supports ConnectionManager and ServerManager as core components for VPNService.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create, 13/07/2025 refactor for simplified architecture
 */

package com.panabit.client.di

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import org.koin.dsl.module
import org.koin.android.ext.koin.androidContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import com.panabit.client.connection.ConnectionManager
import com.panabit.client.connection.ServerManager
import com.panabit.client.connection.ServerManagerFactory
import com.panabit.client.protocol.SDWANProtocol
import com.panabit.client.network.UDPConnection
import java.net.InetAddress

/**
 * NAME: vpnModule
 *
 * DESCRIPTION:
 *     VPN module defining dependency injection configuration for VPN components.
 *     Follows simplified architecture with ConnectionManager and ServerManager as core components.
 *     Eliminates over-engineering by focusing on essential VPN functionality.
 *
 * COMPONENTS:
 *     - ServerManager: Server selection and management
 *     - ConnectionManager: VPN connection lifecycle management
 *     - Service scope: Coroutine scope for background operations
 *
 * DESIGN PRINCIPLES:
 *     - Simplified architecture avoiding component overlap
 *     - Factory pattern for complex component creation
 *     - Singleton pattern for shared state management
 *     - Android context integration for platform features
 */
val vpnModule = module {

    // Service scope for VPN operations
    single<CoroutineScope>(qualifier = org.koin.core.qualifier.named("vpnServiceScope")) {
        CoroutineScope(Dispatchers.Main + SupervisorJob())
    }

    // ServerManager - Server selection and management
    single<ServerManager> {
        ServerManagerFactory.create(
            context = androidContext(),
            serviceScope = get(qualifier = org.koin.core.qualifier.named("vpnServiceScope"))
        )
    }

    // ConnectionManager - VPN connection lifecycle management
    // Creates SDWANProtocol instances per-connection for state isolation
    single<ConnectionManager> {
        ConnectionManager(
            context = androidContext(),
            serverManager = get(),
            serviceScope = get(qualifier = org.koin.core.qualifier.named("vpnServiceScope")),
            routingConfigurationManager = get()
        )
    }

    // Note: SDWANProtocol instances are created per-connection by ConnectionManager
    // to ensure state isolation and avoid conflicts between different server connections.
    // This design provides:
    // 1. Clean state isolation - each connection has fresh protocol state
    // 2. Memory efficiency - no protocol instances when not connected
    // 3. Simplified lifecycle - protocol lifetime matches connection lifetime
    // 4. No placeholder issues - protocol created with actual server information
}
