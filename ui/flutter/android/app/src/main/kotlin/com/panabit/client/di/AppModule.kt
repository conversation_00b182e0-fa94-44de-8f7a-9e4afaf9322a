/**
 * FILE: AppModule.kt
 *
 * DESCRIPTION:
 *     ITforce WAN Android application dependency injection module.
 *     Defines application-level dependency injection configuration for infrastructure components.
 *     Follows simplified architecture by including only essential infrastructure components.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create, 13/07/2025 refactor for simplified architecture
 */

package com.panabit.client.di

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import org.koin.dsl.module
import org.koin.android.ext.koin.androidContext
import com.panabit.client.infrastructure.error.ErrorHandler
import com.panabit.client.infrastructure.RoutingConfigurationManager

/**
 * NAME: appModule
 *
 * DESCRIPTION:
 *     Application module defining dependency injection configuration for essential infrastructure components.
 *     Follows simplified architecture by including only core infrastructure functionality.
 *     Removes over-engineered components like performance monitoring and complex logging systems.
 *
 * COMPONENTS:
 *     - ErrorHandler: Basic error handling and recovery
 *     - RoutingConfigurationManager: VPN routing configuration persistence
 *
 * DESIGN PRINCIPLES:
 *     - Simplified architecture avoiding over-engineering
 *     - Focus on essential infrastructure functionality
 *     - Remove complex monitoring and optimization features
 *     - Maintain basic error handling capabilities
 */
val appModule = module {

    // Basic Error Handler - essential error handling and recovery
    single<ErrorHandler> {
        ErrorHandler(androidContext())
    }

    // Routing Configuration Manager - VPN routing settings in-memory storage
    single<RoutingConfigurationManager> {
        RoutingConfigurationManager()
    }

    // Note: Removed complex components to follow simplified architecture:
    // - Logger (using LoggingManager directly)
    // - PerformanceManager (over-engineering)
    // - Complex monitoring systems (over-engineering)
    //
    // These components violated the simplified architecture principle and
    // created unnecessary complexity without significant benefit.
}
