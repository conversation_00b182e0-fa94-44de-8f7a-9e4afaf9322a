/**
 * FILE: ServerManager.kt
 *
 * DESCRIPTION:
 *     Server management interface for Android VPN implementation.
 *     Defines server selection, ping testing, and server list management
 *     functionality required by ConnectionManager. Based on iOS/Windows
 *     server management patterns while maintaining Android-specific simplicity.
 *     Enhanced with lifecycle management and callback support.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create, 11/07/2025 enhanced for full implementation
 */

package com.panabit.client.connection

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import com.panabit.client.connection.models.*
import kotlinx.coroutines.flow.StateFlow

/**
 * NAME: ServerManager
 *
 * DESCRIPTION:
 *     Interface for server management operations including server selection,
 *     ping testing, and server list maintenance. Provides reactive server
 *     information through StateFlow for UI updates and connection management.
 *
 * FEATURES:
 *     - Server list management with reactive updates
 *     - Ping testing for server selection optimization
 *     - Best server selection algorithm
 *     - Server availability monitoring
 *     - Cross-platform compatibility with iOS/Windows implementations
 */
interface ServerManager {
    
    /**
     * NAME: servers
     *
     * DESCRIPTION:
     *     Reactive server list observable through StateFlow.
     *     Provides real-time updates when server list changes.
     *
     * RETURNS:
     *     StateFlow<List<ServerInfo>> - Observable server list
     */
    val servers: StateFlow<List<ServerInfo>>
    
    /**
     * NAME: pingResults
     *
     * DESCRIPTION:
     *     Reactive ping results observable through StateFlow.
     *     Maps server IDs to ping latency in milliseconds.
     *
     * RETURNS:
     *     StateFlow<Map<String, Int>> - Observable ping results map
     */
    val pingResults: StateFlow<Map<String, Int>>
    
    /**
     * NAME: updateServerList
     *
     * DESCRIPTION:
     *     Updates the server list with new server information.
     *     Triggers server list StateFlow updates for observers.
     *
     * PARAMETERS:
     *     newServers - List of updated server information
     */
    suspend fun updateServerList(newServers: List<ServerInfo>)
    
    /**
     * NAME: selectBestServer
     *
     * DESCRIPTION:
     *     Selects the best available server based on ping results and availability.
     *     Implements server selection algorithm prioritizing lowest latency.
     *
     * RETURNS:
     *     ServerInfo? - Best available server or null if none available
     */
    suspend fun selectBestServer(): ServerInfo?
    
    /**
     * NAME: pingAllServers
     *
     * DESCRIPTION:
     *     Performs ping test on all available servers concurrently.
     *     Updates ping results StateFlow with test results.
     *
     * RETURNS:
     *     Map<String, Int> - Map of server IDs to ping latency (ms)
     */
    suspend fun pingAllServers(): Map<String, Int>
    
    /**
     * NAME: pingServer
     *
     * DESCRIPTION:
     *     Performs ping test on specific server.
     *     Uses SDWAN protocol-based ping for accurate latency measurement.
     *
     * PARAMETERS:
     *     server - Server to ping test
     *
     * RETURNS:
     *     Int - Ping latency in milliseconds, -1 if failed
     */
    suspend fun pingServer(server: ServerInfo): Int
    
    /**
     * NAME: getServerById
     *
     * DESCRIPTION:
     *     Retrieves server information by server ID.
     *
     * PARAMETERS:
     *     serverId - Unique server identifier
     *
     * RETURNS:
     *     ServerInfo? - Server information or null if not found
     */
    fun getServerById(serverId: String): ServerInfo?
    
    /**
     * NAME: isServerAvailable
     *
     * DESCRIPTION:
     *     Checks if server is currently available for connections.
     *     Based on recent ping results and server status.
     *
     * PARAMETERS:
     *     server - Server to check availability
     *
     * RETURNS:
     *     Boolean - true if server is available, false otherwise
     */
    suspend fun isServerAvailable(server: ServerInfo): Boolean

    // MARK: - Lifecycle Management

    /**
     * NAME: start
     *
     * DESCRIPTION:
     *     Starts server manager background tasks.
     *     Begins server list updates, ping monitoring, and network monitoring.
     *
     * THROWS:
     *     ServerManagerError - If start operation fails
     */
    suspend fun start()

    /**
     * NAME: stop
     *
     * DESCRIPTION:
     *     Stops server manager background tasks and cleans up resources.
     */
    suspend fun stop()

    // MARK: - Extended Server Management

    /**
     * NAME: getServers
     *
     * DESCRIPTION:
     *     Returns current server list sorted by latency.
     *     Online servers are prioritized first, then sorted by ping time.
     *
     * RETURNS:
     *     List<ServerInfo> - Server list sorted by latency
     */
    fun getServers(): List<ServerInfo>

    /**
     * NAME: selectBestAutoServer
     *
     * DESCRIPTION:
     *     Selects best auto server (isAuto = true) based on latency.
     *     Compatible with iOS selectBestAutoServer() functionality.
     *
     * RETURNS:
     *     ServerInfo? - Best auto server or null if none available
     *
     * THROWS:
     *     ServerManagerError.NoServersAvailable - If no auto servers available
     */
    suspend fun selectBestAutoServer(): ServerInfo?

    /**
     * NAME: setSelectionMode
     *
     * DESCRIPTION:
     *     Sets server selection mode (Auto/Manual).
     *
     * PARAMETERS:
     *     mode - Server selection mode
     */
    fun setSelectionMode(mode: ServerSelectionMode)

    /**
     * NAME: setCurrentServer
     *
     * DESCRIPTION:
     *     Sets current manually selected server.
     *
     * PARAMETERS:
     *     server - Server to set as current (null to clear)
     */
    fun setCurrentServer(server: ServerInfo?)

    // MARK: - State Flows

    /**
     * NAME: currentServer
     *
     * DESCRIPTION:
     *     Reactive current server observable through StateFlow.
     *
     * RETURNS:
     *     StateFlow<ServerInfo?> - Observable current server
     */
    val currentServer: StateFlow<ServerInfo?>

    /**
     * NAME: selectionMode
     *
     * DESCRIPTION:
     *     Reactive selection mode observable through StateFlow.
     *
     * RETURNS:
     *     StateFlow<ServerSelectionMode> - Observable selection mode
     */
    val selectionMode: StateFlow<ServerSelectionMode>

    // MARK: - Callback Management

    /**
     * NAME: addServerUpdateCallback
     *
     * DESCRIPTION:
     *     Adds callback for server list updates.
     *
     * PARAMETERS:
     *     callback - Callback function to add
     */
    fun addServerUpdateCallback(callback: (List<ServerInfo>) -> Unit)

    /**
     * NAME: addStatusChangeCallback
     *
     * DESCRIPTION:
     *     Adds callback for server status changes.
     *
     * PARAMETERS:
     *     callback - Callback function to add
     */
    fun addStatusChangeCallback(callback: (String, ServerStatus) -> Unit)

    /**
     * NAME: removeServerUpdateCallback
     *
     * DESCRIPTION:
     *     Removes callback for server list updates.
     *
     * PARAMETERS:
     *     callback - Callback function to remove
     */
    fun removeServerUpdateCallback(callback: (List<ServerInfo>) -> Unit)

    /**
     * NAME: removeStatusChangeCallback
     *
     * DESCRIPTION:
     *     Removes callback for server status changes.
     *
     * PARAMETERS:
     *     callback - Callback function to remove
     */
    fun removeStatusChangeCallback(callback: (String, ServerStatus) -> Unit)

    // MARK: - IP Resolution Management

    /**
     * NAME: getCachedServerIP
     *
     * DESCRIPTION:
     *     Gets cached IP address for a server hostname.
     *     Returns cached IP if available, null otherwise.
     *
     * PARAMETERS:
     *     hostname - Server hostname to lookup
     *
     * RETURNS:
     *     String? - Cached IP address or null if not available
     */
    fun getCachedServerIP(hostname: String): String?

    /**
     * NAME: resolveServerIP
     *
     * DESCRIPTION:
     *     Resolves server hostname to IP address with caching.
     *     Uses cache first, performs DNS resolution if needed.
     *
     * PARAMETERS:
     *     hostname - Server hostname to resolve
     *
     * RETURNS:
     *     String? - Resolved IP address or null if failed
     */
    suspend fun resolveServerIP(hostname: String): String?

    /**
     * NAME: getAllCachedServerIPs
     *
     * DESCRIPTION:
     *     Gets all cached server IP addresses for route exclusion.
     *     Used by ConnectionManager to exclude server IPs from VPN routing.
     *
     * RETURNS:
     *     List<String> - List of all cached server IP addresses
     */
    fun getAllCachedServerIPs(): List<String>
}
