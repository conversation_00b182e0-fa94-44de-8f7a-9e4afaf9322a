/**
 * FILE: LoggingExtensions.kt
 *
 * DESCRIPTION:
 *     Logging extension functions for Panabit Client Android application.
 *     Provides convenient logging methods with automatic source information
 *     capture via Timber. Follows Timber best practices with direct usage.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.infrastructure.logging

import timber.log.Timber

/**
 * NAME: Object extension functions for logging
 *
 * DESCRIPTION:
 *     Extension functions that use <PERSON>ber directly with automatic class name tagging.
 *     These functions follow Timber best practices and provide optimal performance.
 */

/**
 * Extension function for debug logging with automatic class name as tag
 */
inline fun Any.logDebug(message: String, throwable: Throwable? = null) {
    if (throwable != null) {
        Timber.tag(this::class.java.simpleName).d(throwable, message)
    } else {
        Timber.tag(this::class.java.simpleName).d(message)
    }
}

/**
 * Extension function for info logging with automatic class name as tag
 */
inline fun Any.logInfo(message: String, throwable: Throwable? = null) {
    if (throwable != null) {
        Timber.tag(this::class.java.simpleName).i(throwable, message)
    } else {
        Timber.tag(this::class.java.simpleName).i(message)
    }
}

/**
 * Extension function for warning logging with automatic class name as tag
 */
inline fun Any.logWarn(message: String, throwable: Throwable? = null) {
    if (throwable != null) {
        Timber.tag(this::class.java.simpleName).w(throwable, message)
    } else {
        Timber.tag(this::class.java.simpleName).w(message)
    }
}

/**
 * Extension function for warning logging with metadata and automatic class name as tag
 */
inline fun Any.logWarn(message: String, metadata: Map<String, Any>, throwable: Throwable? = null) {
    val metadataString = metadata.entries.joinToString(", ") { "${it.key}=${it.value}" }
    val fullMessage = "$message [$metadataString]"

    if (throwable != null) {
        Timber.tag(this::class.java.simpleName).w(throwable, fullMessage)
    } else {
        Timber.tag(this::class.java.simpleName).w(fullMessage)
    }
}

/**
 * Extension function for error logging with automatic class name as tag
 */
inline fun Any.logError(message: String, throwable: Throwable? = null) {
    if (throwable != null) {
        Timber.tag(this::class.java.simpleName).e(throwable, message)
    } else {
        Timber.tag(this::class.java.simpleName).e(message)
    }
}

/**
 * Extension function for error logging with metadata and automatic class name as tag
 */
inline fun Any.logError(message: String, metadata: Map<String, Any>, throwable: Throwable? = null) {
    val metadataString = metadata.entries.joinToString(", ") { "${it.key}=${it.value}" }
    val fullMessage = "$message [$metadataString]"

    if (throwable != null) {
        Timber.tag(this::class.java.simpleName).e(throwable, fullMessage)
    } else {
        Timber.tag(this::class.java.simpleName).e(fullMessage)
    }
}

/**
 * Extension function for info logging with metadata and automatic class name as tag
 */
inline fun Any.logInfo(message: String, metadata: Map<String, Any>, throwable: Throwable? = null) {
    val metadataString = metadata.entries.joinToString(", ") { "${it.key}=${it.value}" }
    val fullMessage = "$message [$metadataString]"

    if (throwable != null) {
        Timber.tag(this::class.java.simpleName).i(throwable, fullMessage)
    } else {
        Timber.tag(this::class.java.simpleName).i(fullMessage)
    }
}

/**
 * Extension function for debug logging with metadata and automatic class name as tag
 */
inline fun Any.logDebug(message: String, metadata: Map<String, Any>, throwable: Throwable? = null) {
    val metadataString = metadata.entries.joinToString(", ") { "${it.key}=${it.value}" }
    val fullMessage = "$message [$metadataString]"

    if (throwable != null) {
        Timber.tag(this::class.java.simpleName).d(throwable, fullMessage)
    } else {
        Timber.tag(this::class.java.simpleName).d(fullMessage)
    }
}


