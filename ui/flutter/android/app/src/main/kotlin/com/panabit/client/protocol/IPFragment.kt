/**
 * FILE: IPFragment.kt
 *
 * DESCRIPTION:
 *     IP fragment handling for SDWAN protocol.
 *     Implements fragment parsing, queue management, and reassembly.
 *     Based on Go backend and original Android Java implementation.
 *
 * AUTHOR: wei
 * HISTORY: 18/07/2025 create
 */

package com.panabit.client.protocol

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError
import java.nio.ByteBuffer

/**
 * NAME: IPFragment
 *
 * DESCRIPTION:
 *     Represents an IP packet fragment with metadata.
 *     Contains fragment identification, position, and timing information.
 *
 * PROPERTIES:
 *     id - Fragment identifier for grouping related fragments
 *     eop - End of Packet flag (1 if last fragment, 0 otherwise)
 *     offset - Fragment offset within the original packet
 *     length - Length of fragment data
 *     data - Fragment payload data
 *     insertTime - Time when fragment was added to queue
 */
data class IPFragment(
    val id: Int,
    val eop: Int,
    val offset: Int,
    val length: Int,
    val data: ByteArray,
    val insertTime: Long = System.currentTimeMillis()
) {
    companion object {
        /**
         * NAME: parseFragment
         *
         * DESCRIPTION:
         *     Parses fragment data from raw bytes.
         *     Based on Go backend and Android Java implementation.
         *
         * PARAMETERS:
         *     data - Raw fragment bytes to parse
         *
         * RETURNS:
         *     IPFragment - Parsed fragment structure
         *
         * THROWS:
         *     IllegalArgumentException - If data is invalid
         */
        /**
         * NAME: parseFragment
         *
         * DESCRIPTION:
         *     Parses fragment from SDWAN packet data (after SDWAN header removed).
         *     Follows Go backend implementation which receives sdwanPacket.data.
         *
         * PARAMETERS:
         *     data - Fragment data (without SDWAN header)
         *
         * RETURNS:
         *     IPFragment - Parsed fragment structure
         */
        fun parseFragment(data: ByteArray): IPFragment {
            // 检查最小数据长度：8字节分片头
            if (data.size < 8) {
                throw IllegalArgumentException("Fragment data too short: ${data.size} bytes (minimum 8 bytes)")
            }

            val buffer = ByteBuffer.wrap(data)

            // Parse fragment header (following Go backend implementation)
            // data already has SDWAN header removed
            val id = buffer.int
            val fragInfo = Integer.reverseBytes(buffer.int)

            // Extract fragment information (exact bit operations from Java implementation)
            val eop = ((fragInfo shl 31) ushr 31)
            val offset = ((fragInfo shl 17) ushr 19)
            val length = ((fragInfo shl 6) ushr 21)

            // Validate length
            if (length <= 0 || length > MAX_FRAG_DATA_SIZE) {
                throw IllegalArgumentException("Invalid fragment length: $length")
            }

            // Check if we have enough data for fragment payload
            val remainingData = data.size - 8  // 8 bytes fragment header
            if (remainingData < length) {
                throw IllegalArgumentException("Not enough fragment data: expected $length, got $remainingData")
            }

            // Copy fragment data (following Java implementation exactly)
            val fragmentData = ByteArray(MAX_FRAG_DATA_SIZE)  // Fixed size array like Java
            buffer.get(fragmentData, 0, length)

            return IPFragment(id, eop, offset, length, fragmentData)
        }

        /**
         * NAME: parseFragmentFromRawPacket
         *
         * DESCRIPTION:
         *     Parses fragment from complete SDWAN packet (including SDWAN header).
         *     Follows original Java implementation which receives complete packet.
         *
         * PARAMETERS:
         *     received - Complete SDWAN packet data
         *
         * RETURNS:
         *     IPFragment - Parsed fragment structure
         */
        fun parseFragmentFromRawPacket(received: ByteArray): IPFragment {
            // 检查最小数据长度：8字节SDWAN头 + 8字节分片头 = 16字节
            if (received.size < 16) {
                throw IllegalArgumentException("Packet data too short: ${received.size} bytes (minimum 16 bytes)")
            }

            val buffer = ByteBuffer.wrap(received)

            // Skip SDWAN packet header (8 bytes) - following Java implementation
            buffer.int  // Skip first 4 bytes
            buffer.int  // Skip next 4 bytes

            // Parse fragment header
            val id = buffer.int
            val fragInfo = Integer.reverseBytes(buffer.int)

            // Extract fragment information (exact bit operations from Java implementation)
            val eop = ((fragInfo shl 31) ushr 31)
            val offset = ((fragInfo shl 17) ushr 19)
            val length = ((fragInfo shl 6) ushr 21)

            // Validate length
            if (length <= 0 || length > MAX_FRAG_DATA_SIZE) {
                throw IllegalArgumentException("Invalid fragment length: $length")
            }

            // Check if we have enough data for fragment payload
            val remainingData = received.size - 16  // 16 bytes total header
            if (remainingData < length) {
                throw IllegalArgumentException("Not enough fragment data: expected $length, got $remainingData")
            }

            // Copy fragment data (following Java implementation exactly)
            val fragmentData = ByteArray(MAX_FRAG_DATA_SIZE)  // Fixed size array like Java
            buffer.get(fragmentData, 0, length)

            return IPFragment(id, eop, offset, length, fragmentData)
        }

        private const val MAX_FRAG_DATA_SIZE = 2048
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as IPFragment

        if (id != other.id) return false
        if (eop != other.eop) return false
        if (offset != other.offset) return false
        if (length != other.length) return false
        if (!data.contentEquals(other.data)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + eop.hashCode()
        result = 31 * result + offset.hashCode()
        result = 31 * result + length.hashCode()
        result = 31 * result + data.contentHashCode()
        return result
    }
}

/**
 * NAME: FragmentQueue
 *
 * DESCRIPTION:
 *     Queue for managing IP packet fragments and reassembly.
 *     Provides thread-safe fragment storage with timeout handling.
 *     Based on Go backend FragmentQueue implementation.
 *
 * PROPERTIES:
 *     fragments - Array of fragment lists for different packet IDs
 */
class FragmentQueue {
    companion object {
        private const val FRAG_QUEUE_CAPACITY = 8
        private const val FRAG_TIMEOUT_MS = 100L
        private const val MAX_IP_PACKET_SIZE = 4096
    }

    // Array of fragment lists for different packet IDs (following Go backend design)
    private val fragments = Array<MutableList<IPFragment>?>(FRAG_QUEUE_CAPACITY) { null }

    /**
     * NAME: addFragment
     *
     * DESCRIPTION:
     *     Adds a fragment to the queue and attempts reassembly.
     *     Returns complete IP packet data if reassembly is successful.
     *     Based on Go backend implementation.
     *
     * PARAMETERS:
     *     fragment - IP fragment to add to the queue
     *
     * RETURNS:
     *     ByteArray? - Complete reassembled packet data (null if not ready)
     */
    @Synchronized
    fun addFragment(fragment: IPFragment): ByteArray? {
        val currentTime = System.currentTimeMillis()

        // Find matching fragment group (following Java implementation exactly)
        for (i in 0 until FRAG_QUEUE_CAPACITY) {
            val fragmentList = fragments[i]

            if (fragmentList != null && fragmentList.size == 1) {
                if (fragmentList[0].id == fragment.id) {
                    // Check timeout
                    if (currentTime - fragmentList[0].insertTime <= FRAG_TIMEOUT_MS) {
                        // Add new fragment (following Java implementation logic)
                        if (fragment.eop == 0) {
                            // Non-ending fragment, add to beginning
                            fragmentList.add(0, fragment)
                        } else {
                            // Ending fragment, add to end
                            fragmentList.add(fragment)
                        }

                        // Attempt reassembly and write to TUN (like Java implementation)
                        val reassembledData = reassembleFragments(i)
                        fragmentList.clear()  // Clear after processing
                        return reassembledData
                    }

                    // Timeout, clear this slot
                    fragmentList.clear()
                    return null
                }
            }
        }

        // No matching fragment group found, look for empty slot (following Java implementation)
        var freeIndex = -1
        for (i in 0 until FRAG_QUEUE_CAPACITY) {
            val fragmentList = fragments[i]
            if (fragmentList == null || fragmentList.isEmpty()) {
                freeIndex = i
            } else if (currentTime - fragmentList[0].insertTime > FRAG_TIMEOUT_MS) {
                // Found timeout slot, clear it
                fragmentList.clear()
                freeIndex = i
            }
        }

        // If empty slot found, add new fragment
        if (freeIndex >= 0) {
            if (fragments[freeIndex] == null) {
                fragments[freeIndex] = mutableListOf()
            }
            fragments[freeIndex]!!.add(fragment)
        } else {
            logWarn("Fragment queue is full, dropping fragment", mapOf(
                "id" to fragment.id,
                "eop" to fragment.eop,
                "offset" to fragment.offset,
                "length" to fragment.length
            ))
        }

        return null
    }

    /**
     * NAME: reassembleFragments
     *
     * DESCRIPTION:
     *     Reassembles fragments into complete IP packet.
     *     Based on Go backend reassembleFragments implementation.
     *
     * PARAMETERS:
     *     index - Index of fragment group to reassemble
     *
     * RETURNS:
     *     ByteArray? - Reassembled packet data (null if not complete)
     */
    private fun reassembleFragments(index: Int): ByteArray? {
        val fragmentList = fragments[index] ?: return null

        if (fragmentList.size < 2) {
            return null // Need at least 2 fragments
        }

        // Calculate total length (following Java implementation exactly)
        var ipLength = 0
        for (fragment in fragmentList) {
            ipLength += fragment.length
        }

        if (ipLength > MAX_IP_PACKET_SIZE) {
            logWarn("Reassembled packet too large", mapOf(
                "total_length" to ipLength,
                "max_size" to MAX_IP_PACKET_SIZE
            ))
            return null
        }

        // Create reassembled packet using ByteBuffer (like Java implementation)
        val ipData = ByteBuffer.allocate(ipLength)

        // Copy fragments in order (following Java implementation)
        for (fragment in fragmentList) {
            ipData.put(fragment.data, 0, fragment.length)
        }

        logDebug("Fragment reassembly successful", mapOf(
            "fragment_count" to fragmentList.size,
            "total_length" to ipLength,
            "fragment_id" to fragmentList[0].id
        ))

        return ipData.array()
    }
}
