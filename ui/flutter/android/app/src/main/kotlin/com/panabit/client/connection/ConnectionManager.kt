/**
 * FILE: ConnectionManager.kt
 *
 * DESCRIPTION:
 *     Android VPN connection manager implementation based on iOS VPNService and Windows Manager.
 *     Manages SDWAN protocol connection lifecycle, state transitions, heartbeat mechanism,
 *     and network change handling. Designed for Android VPN service integration with
 *     thread-safe operations using Kotlin coroutines and StateFlow.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.connection

import android.content.Context
import com.panabit.client.connection.models.VPNState
import com.panabit.client.connection.models.ServerInfo
import com.panabit.client.connection.models.ConnectionProgress

import com.panabit.client.infrastructure.error.VPNServiceError
import com.panabit.client.infrastructure.error.VPNErrorCode
import com.panabit.client.infrastructure.error.ErrorHandler
import com.panabit.client.infrastructure.error.TimeoutManager
import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError
import com.panabit.client.infrastructure.RoutingConfigurationManager
import com.panabit.client.connection.state.VPNStateManager
import com.panabit.client.connection.flutter.VPNStateEventSender
import com.panabit.client.network.UDPConnection
import com.panabit.client.protocol.SDWANProtocol
import com.panabit.client.protocol.HeartbeatManager
import com.panabit.client.protocol.IPFragment
import com.panabit.client.protocol.FragmentQueue

import java.io.IOException
import java.net.SocketException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.net.ConnectException
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.withTimeout
import com.panabit.client.protocol.HeartbeatTimeoutCallback
import com.panabit.client.platform.NetworkMonitorDelegate
import com.panabit.client.platform.NetworkMonitor
import com.panabit.client.platform.VPNInterfaceManager
import com.panabit.client.platform.models.VPNConfig
import com.panabit.client.protocol.models.PacketType
import com.panabit.client.protocol.models.PacketHeader
import com.panabit.client.protocol.models.EncryptionMethod
import com.panabit.client.protocol.models.SDWANPacket
import com.panabit.client.protocol.models.ProtocolState
import com.panabit.client.network.NetworkUtils as NetworkUtilsNetwork
import com.panabit.client.infrastructure.NetworkUtils
import com.panabit.client.MainActivity
import android.os.ParcelFileDescriptor
import java.io.FileInputStream
import java.io.FileOutputStream
import android.net.Network
import android.net.NetworkCapabilities.*
import android.net.ConnectivityManager
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.net.InetAddress
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * NAME: DisconnectReason
 *
 * DESCRIPTION:
 *     Enumeration for VPN disconnection reasons, matching iOS implementation patterns.
 *
 * VALUES:
 *     UserInitiated - User manually disconnected VPN
 *     NetworkChange - Network interface change triggered disconnection
 *     HeartbeatTimeout - Heartbeat timeout caused disconnection
 *     ServerError - Server-side error caused disconnection
 *     AuthenticationExpired - Authentication token expired
 */
enum class DisconnectReason {
    UserInitiated,
    NetworkChange,
    HeartbeatTimeout,
    ServerError,
    AuthenticationExpired,
    ServerInitiated  // 🔧 服务器主动断开连接（close包）
}

/**
 * NAME: ConnectionManager
 *
 * DESCRIPTION:
 *     Thread-safe connection manager for Android VPN operations using Kotlin coroutines.
 *     Manages SDWAN protocol connection lifecycle, state transitions, heartbeat mechanism,
 *     and automatic reconnection. Based on iOS VPNService and Windows Manager implementations
 *     while leveraging Android-specific features and maintaining architectural simplicity.
 *
 * FEATURES:
 *     - Thread-safe state management using StateFlow
 *     - SDWAN protocol integration for authentication and data transmission
 *     - Heartbeat mechanism with timeout detection and recovery
 *     - Automatic reconnection on network changes and failures
 *     - Connection lifecycle management (connect, disconnect, reconnect)
 *     - Error handling with recovery strategies
 *     - Network change detection and handling
 *
 * DESIGN PRINCIPLES:
 *     - Functional consistency with iOS/Windows implementations
 *     - Android-optimized using Kotlin coroutines and Flow
 *     - Simple architecture without over-engineering
 *     - Thread safety through structured concurrency
 */
class ConnectionManager(
    private val context: Context,
    private val serverManager: ServerManager,
    private val serviceScope: CoroutineScope,
    private val routingConfigurationManager: RoutingConfigurationManager
) : NetworkMonitorDelegate, HeartbeatTimeoutCallback {
    companion object {
        private const val TAG = "ConnectionManager"

        // Network type constants
        private const val TRANSPORT_UNKNOWN = 0
        private const val TRANSPORT_WIFI = android.net.NetworkCapabilities.TRANSPORT_WIFI
        private const val TRANSPORT_CELLULAR = android.net.NetworkCapabilities.TRANSPORT_CELLULAR

        // Heartbeat配置已移至HeartbeatManager中统一管理
        // ConnectionManager不再维护重复的heartbeat常量，确保配置一致性

        // Connection timeouts
        private const val CONNECTION_TIMEOUT = 25_000L // 25 seconds (aligned with iOS)
        private const val AUTHENTICATION_TIMEOUT = 15_000L // 15 seconds

        // Retry configuration
        private const val MAX_RETRY_ATTEMPTS = 3
        private const val RETRY_DELAY_BASE = 2_000L // 2 seconds base delay
    }

    // MARK: - Core Components
    // State management (与iOS VPNService保持一致)
    private val _state = MutableStateFlow<VPNState>(VPNState.Disconnected)
    val state: StateFlow<VPNState> = _state.asStateFlow()

    // VPN state management and event sending
    private val vpnStateManager: VPNStateManager by lazy { VPNStateManager(context) }
    private val vpnStateEventSender: VPNStateEventSender by lazy {
        VPNStateEventSender(vpnStateManager, serviceScope)
    }



    // 统一错误处理和超时管理（新增）
    private val errorHandler: ErrorHandler by lazy {
        ErrorHandler(context)
    }
    private val timeoutManager: TimeoutManager by lazy { TimeoutManager() }

    // Credentials management - simple in-memory storage
    @Volatile private var currentUsername: String? = null
    @Volatile private var currentPassword: String? = null

    // Connection resources (added @Volatile for thread safety)
    @Volatile private var udpConnection: UDPConnection? = null
    @Volatile private var currentServer: ServerInfo? = null
    @Volatile private var heartbeatManager: HeartbeatManager? = null
    @Volatile private var protocolHandler: SDWANProtocol? = null

    // VPN interface management (integrated from VPNInterfaceManager)
    @Volatile private var vpnInterfaceManager: VPNInterfaceManager? = null
    @Volatile private var currentTunInterface: ParcelFileDescriptor? = null

    // TUN data flow management (integrated from TUNDataFlowManager)
    private val dataFlowScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var tunReaderJob: Job? = null
    private var udpReaderJob: Job? = null
    private val isDataFlowActive = AtomicBoolean(false)

    // Traffic statistics
    private val tunBytesRead = AtomicLong(0)
    private val tunBytesWritten = AtomicLong(0)
    private val packetsProcessed = AtomicLong(0)

    // Traffic speed calculation
    private val previousUploadBytes = AtomicLong(0)
    private val previousDownloadBytes = AtomicLong(0)
    private val currentUploadSpeed = AtomicLong(0)
    private val currentDownloadSpeed = AtomicLong(0)
    private var lastTrafficUpdateTime = System.currentTimeMillis()

    // Traffic monitoring
    private var trafficMonitoringJob: Job? = null
    private val trafficUpdateInterval = 2000L // 2 seconds, matching iOS implementation

    // Connection state flags
    private val isConnecting = AtomicBoolean(false)
    private val isDisconnecting = AtomicBoolean(false)

    // Network interface tracking (记录连接前的物理接口信息)
    @Volatile private var connectionPhysicalInterface: String? = null
    @Volatile private var connectionPhysicalIP: String? = null

    // Network monitoring for interface change detection
    private val networkMonitor: NetworkMonitor by lazy {
        NetworkMonitor(context).apply {
            delegate = this@ConnectionManager
        }
    }

    // IP fragment handling
    private val fragmentQueue = FragmentQueue()

    // Packet processing statistics
    private var decryptionCount = 0L
    private var decryptionFailures = 0L
    private var receivedPacketCount = 0L
    private var lastPacketLogTime = System.currentTimeMillis()
    private val packetLogInterval = 10000L // 10 seconds



    // MARK: - Protocol Factory

    /**
     * NAME: createProtocolHandler
     *
     * DESCRIPTION:
     *     Creates a new SDWANProtocol instance for the specified server.
     *     Each connection gets a fresh protocol handler to ensure state isolation
     *     and avoid conflicts between different server connections.
     *
     * PARAMETERS:
     *     server - Server information for protocol configuration
     *     resolvedIP - Pre-resolved IP address to avoid DNS resolution in protocol layer
     *
     * RETURNS:
     *     SDWANProtocol - New protocol handler instance
     */
    private fun createProtocolHandler(server: ServerInfo, resolvedIP: String): SDWANProtocol {
        return SDWANProtocol(resolvedIP, server.serverPort)
    }

    // MARK: - Public Interface

    /**
     * NAME: getRecordedPhysicalInterfaceInfo
     *
     * DESCRIPTION:
     *     Gets the physical interface information recorded before VPN connection.
     *     This avoids querying the interface after VPN is established (which would return VPN interface).
     *
     * RETURNS:
     *     Pair<String, String> - Interface name and IP address, or ("unknown", "unknown") if not recorded
     */
    fun getRecordedPhysicalInterfaceInfo(): Pair<String, String> {
        return Pair(
            connectionPhysicalInterface ?: "unknown",
            connectionPhysicalIP ?: "unknown"
        )
    }

    // MARK: - Credentials Management

    /**
     * NAME: saveUserCredentials
     *
     * DESCRIPTION:
     *     Saves user credentials in memory for VPN authentication.
     *     Simple in-memory storage for current session.
     *
     * PARAMETERS:
     *     username - User login name
     *     password - User password
     *     domain - Optional domain (ignored for simplicity)
     *     rememberCredentials - Whether to persist credentials (ignored for simplicity)
     */
    fun saveUserCredentials(
        username: String,
        password: String,
        domain: String = "",
        rememberCredentials: Boolean = true
    ) {
        currentUsername = username
        currentPassword = password
        logInfo("User credentials saved in memory", mapOf("username" to username))
    }

    /**
     * NAME: getCurrentCredentials
     *
     * DESCRIPTION:
     *     Retrieves current user credentials from memory.
     *
     * RETURNS:
     *     Pair<String, String>? - (username, password) or null if not available
     */
    fun getCurrentCredentials(): Pair<String, String>? {
        val username = currentUsername
        val password = currentPassword

        return if (!username.isNullOrBlank() && !password.isNullOrBlank()) {
            logDebug("Retrieved credentials from memory", mapOf("username" to username))
            Pair(username, password)
        } else {
            logDebug("No credentials available in memory")
            null
        }
    }

    /**
     * NAME: hasValidCredentials
     *
     * DESCRIPTION:
     *     Checks if valid credentials are available in memory.
     *
     * RETURNS:
     *     Boolean - True if valid credentials are available
     */
    fun hasValidCredentials(): Boolean {
        return getCurrentCredentials() != null
    }

    /**
     * NAME: clearCredentials
     *
     * DESCRIPTION:
     *     Clears credentials from memory.
     */
    fun clearCredentials() {
        currentUsername = null
        currentPassword = null
        logInfo("User credentials cleared from memory")
    }

    // MARK: - Connection Management

    /**
     * NAME: connect
     *
     * DESCRIPTION:
     *     Establishes VPN connection to specified server using SDWAN protocol.
     *     Implements connection lifecycle: DNS resolution → UDP connection → 
     *     SDWAN authentication → tunnel establishment, matching iOS/Windows patterns.
     *
     * PARAMETERS:
     *     server - Target server information
     *
     * RETURNS:
     *     Result<Unit> - Success or failure result with error details
     */
    suspend fun connect(server: ServerInfo): Result<Unit> = withContext(Dispatchers.IO) {
        logInfo("Starting VPN connection", mapOf(
            "server_id" to server.id,
            "server_name" to server.displayName,
            "server_address" to server.serverName,
            "server_port" to server.serverPort
        ))

        try {
            // Validate current state BEFORE setting isConnecting flag
            val currentState = _state.value

            // Enhanced state validation with recovery mechanism
            when {
                // Allow connection from Error state (user retry scenario)
                currentState is VPNState.Error -> {
                    logInfo("Connection allowed from Error state - user retry", mapOf(
                        "server_id" to server.id,
                        "previous_error" to currentState.error.errorMessage
                    ))
                    // Force clear any residual operation flags
                    isDisconnecting.set(false)
                }

                // Allow connection from Disconnected state (normal scenario)
                currentState is VPNState.Disconnected -> {
                    logInfo("Connection allowed from Disconnected state", mapOf(
                        "server_id" to server.id
                    ))
                }

                // Block connection if other operations are in progress (Connecting, Disconnecting)
                currentState.isOperationInProgress -> {
                    // Perform smart validation to check if operation is actually in progress
                    val actuallyInProgress = validateOperationInProgress(currentState)

                    if (actuallyInProgress) {
                        logWarn("Connection blocked - operation genuinely in progress", mapOf(
                            "current_state" to currentState.flutterStatusString,
                            "server_id" to server.id,
                            "is_connecting_flag" to isConnecting.get(),
                            "is_disconnecting_flag" to isDisconnecting.get(),
                            "validation_result" to "genuine_operation"
                        ))
                        return@withContext Result.failure(
                            VPNServiceError.ConnectionFailed("Another operation is in progress")
                        )
                    } else {
                        logWarn("Detected stale operation state - performing recovery", mapOf(
                            "current_state" to currentState.flutterStatusString,
                            "server_id" to server.id,
                            "is_connecting_flag" to isConnecting.get(),
                            "is_disconnecting_flag" to isDisconnecting.get(),
                            "validation_result" to "stale_state_detected"
                        ))

                        // Force reset the stale state and allow connection to proceed
                        forceResetConnectionState("Stale operation state detected during connect")

                        logInfo("State reset completed, allowing connection to proceed", mapOf(
                            "server_id" to server.id,
                            "new_state" to _state.value.flutterStatusString
                        ))
                    }
                }

                // Connected state - should disconnect first
                currentState is VPNState.Connected -> {
                    logWarn("Connection blocked - already connected", mapOf(
                        "current_server" to currentState.server.id,
                        "new_server" to server.id
                    ))
                    return@withContext Result.failure(
                        VPNServiceError.ConnectionFailed("Already connected to a server")
                    )
                }
            }

            // Now set the connecting flag after state validation
            if (!isConnecting.compareAndSet(false, true)) {
                return@withContext Result.failure(
                    VPNServiceError.ConnectionFailed("Connection already in progress")
                )
            }

            // Record physical interface information BEFORE VPN connection
            // This ensures we capture the real physical interface, not the VPN interface
            val (physicalInterface, physicalIP) = NetworkUtilsNetwork.getPhysicalInterfaceInfo(context)
            connectionPhysicalInterface = physicalInterface
            connectionPhysicalIP = physicalIP

            logInfo("Physical interface recorded for connection", mapOf(
                "interface_name" to physicalInterface,
                "interface_ip" to physicalIP,
                "server_id" to server.id
            ))

            // Update to connecting state
            updateVPNState(VPNState.Connecting(server, ConnectionProgress.INITIALIZING))

            // Step 1: DNS resolution (moved before protocol handler creation)
            updateVPNState(VPNState.Connecting(server, ConnectionProgress.RESOLVING_SERVER))
            val resolvedAddress = resolveServerAddress(server.serverName)

            val (connectionAddress, protocolAddress, useResolvedIP) = if (resolvedAddress != null) {
                logInfo("Server address resolved [resolved_address=${resolvedAddress.hostAddress}]")
                Triple(resolvedAddress, resolvedAddress.hostAddress, true)
            } else {
                // IP resolution failed, use hostname as fallback
                logWarn("IP resolution failed, using hostname as fallback", mapOf(
                    "hostname" to server.serverName,
                    "note" to "Will perform DNS resolution in protocol and connection layers"
                ))

                // Try direct hostname resolution for connection
                val hostnameAddress = try {
                    InetAddress.getByName(server.serverName)
                } catch (e: Exception) {
                    return@withContext Result.failure(
                        VPNServiceError.NetworkUnavailable("Failed to resolve server address: ${e.message}")
                    )
                }
                Triple(hostnameAddress, server.serverName, false)
            }

            // Step 2: Create protocol handler
            protocolHandler = createProtocolHandler(server, protocolAddress)
            logInfo("Protocol handler created for server", mapOf(
                "server_address" to server.serverName,
                "protocol_address" to protocolAddress,
                "server_port" to server.serverPort,
                "using_resolved_ip" to useResolvedIP
            ))

            // Step 3: UDP connection establishment
            val connection = UDPConnection(connectionAddress, server.serverPort)
            val connectionResult = connection.connect()
            if (connectionResult.isFailure) {
                return@withContext Result.failure(
                    VPNServiceError.ConnectionFailed(
                        "Failed to establish UDP connection: ${connectionResult.exceptionOrNull()?.message}",
                        server.serverName
                    )
                )
            }

            logInfo("UDP connection established for all operations", mapOf(
                "socket_local_port" to connection.socket.localPort,
                "server_endpoint" to "${connectionAddress.hostAddress}:${server.serverPort}",
                "socket_id" to connection.socket.hashCode()
            ))

            // Step 4: SDWAN authentication (认证自己处理响应包)
            updateVPNState(VPNState.Connecting(server, ConnectionProgress.AUTHENTICATING))
            val authResult = performAuthentication(connection, server, if (useResolvedIP) connectionAddress.hostAddress else null)
            if (authResult.isFailure) {
                connection.close()
                return@withContext Result.failure(authResult.exceptionOrNull() ?:
                    VPNServiceError.AuthenticationFailed("Authentication failed")
                )
            }

            // Extract latency from authentication result
            val authLatencyMs = authResult.getOrNull()

            // Step 5: Set currentServer BEFORE tunnel establishment
            // This is critical for heartbeat initialization in establishTunnel method
            currentServer = server

            // Step 6: Tunnel establishment (UDP接收器将在这里启动)
            updateVPNState(VPNState.Connecting(server, ConnectionProgress.ESTABLISHING_TUNNEL))
            logInfo("Starting tunnel establishment with shared socket", mapOf(
                "socket_local_port" to connection.socket.localPort,
                "socket_id" to connection.socket.hashCode()
            ))
            val tunnelIP = establishTunnel(connection)
            if (tunnelIP.isEmpty()) {
                connection.close()
                currentServer = null // Reset on failure
                return@withContext Result.failure(
                    VPNServiceError.InterfaceError("Failed to establish VPN tunnel")
                )
            }

            // Step 7: Connection successful
            udpConnection = connection
            logInfo("VPN connection successful with authentication latency", mapOf(
                "server_id" to server.id,
                "tunnel_ip" to tunnelIP,
                "auth_latency_ms" to (authLatencyMs ?: -1)
            ))
            updateVPNState(VPNState.Connected(server, System.currentTimeMillis(), tunnelIP, authLatencyMs))

            // Heartbeat is started in establishTunnel method after UDP receiver is ready

            // Start traffic monitoring
            startTrafficMonitoring()

            // Start network interface monitoring for reconnection detection
            startNetworkInterfaceMonitoring()

            logInfo("VPN connection established with unified UDP socket", mapOf(
                "server_id" to server.id,
                "tunnel_ip" to tunnelIP,
                "socket_local_port" to connection.socket.localPort,
                "socket_id" to connection.socket.hashCode(),
                "operations" to "authentication,data_transfer,heartbeat,traffic_monitoring,network_monitoring",
                "heartbeat_manager_initialized" to (heartbeatManager != null)
            ))

            logInfo("VPN connection established successfully", mapOf(
                "server_id" to server.id,
                "tunnel_ip" to tunnelIP,
                "connection_time" to System.currentTimeMillis()
            ))

            Result.success(Unit)

        } catch (e: Exception) {
            logError("Connection failed with exception", mapOf(
                "server_id" to server.id,
                "error_message" to (e.message ?: "Unknown error")
            ), e)

            val error = when (e) {
                is VPNServiceError -> e
                else -> VPNServiceError.ConnectionFailed(e.message ?: "Unknown connection error")
            }

            // Use unified error handling (similar to iOS handleConnectionFailure)
            handleConnectionFailure(error, server, shouldAllowRetry = false)
            Result.failure(error)

        } finally {
            isConnecting.set(false)
        }
    }

    /**
     * NAME: disconnect
     *
     * DESCRIPTION:
     *     Disconnects VPN connection with specified reason.
     *     Implements clean disconnection with resource cleanup.
     *
     * PARAMETERS:
     *     reason - Reason for disconnection (default: UserInitiated)
     *
     * RETURNS:
     *     Result<Unit> - Success or failure result
     */
    suspend fun disconnect(reason: DisconnectReason = DisconnectReason.UserInitiated): Result<Unit> = withContext(Dispatchers.IO) {
        val disconnectStartTime = System.currentTimeMillis()
        logInfo("🔄 [CM-DISCONNECT-PERF] Starting ConnectionManager disconnect", mapOf(
            "reason" to reason.name,
            "current_state" to _state.value.flutterStatusString,
            "start_time" to disconnectStartTime
        ))

        // Prevent concurrent disconnection attempts
        if (!isDisconnecting.compareAndSet(false, true)) {
            logInfo("🔄 [CM-DISCONNECT-PERF] Already disconnecting, skipping", mapOf(
                "duration_ms" to (System.currentTimeMillis() - disconnectStartTime)
            ))
            return@withContext Result.success(Unit) // Already disconnecting
        }

        try {
            // Update to disconnecting state
            val stateUpdateStartTime = System.currentTimeMillis()
            updateVPNState(VPNState.Disconnecting)
            val stateUpdateDuration = System.currentTimeMillis() - stateUpdateStartTime
            logInfo("📊 [CM-DISCONNECT-PERF] State updated to disconnecting", mapOf(
                "duration_ms" to stateUpdateDuration
            ))

            // Stop heartbeat
            val heartbeatStopStartTime = System.currentTimeMillis()
            stopHeartbeat()
            val heartbeatStopDuration = System.currentTimeMillis() - heartbeatStopStartTime
            logInfo("💓 [CM-DISCONNECT-PERF] Heartbeat stopped", mapOf(
                "duration_ms" to heartbeatStopDuration
            ))

            // Stop traffic monitoring
            val trafficStopStartTime = System.currentTimeMillis()
            stopTrafficMonitoring()
            val trafficStopDuration = System.currentTimeMillis() - trafficStopStartTime
            logInfo("📈 [CM-DISCONNECT-PERF] Traffic monitoring stopped", mapOf(
                "duration_ms" to trafficStopDuration
            ))

            // Stop network interface monitoring
            val networkStopStartTime = System.currentTimeMillis()
            stopNetworkInterfaceMonitoring()
            val networkStopDuration = System.currentTimeMillis() - networkStopStartTime
            logInfo("🌐 [CM-DISCONNECT-PERF] Network interface monitoring stopped", mapOf(
                "duration_ms" to networkStopDuration
            ))

            // Clean up tunnel resources (TUN interface and data flow)
            val tunnelCleanupStartTime = System.currentTimeMillis()
            cleanupTunnelResources()
            val tunnelCleanupDuration = System.currentTimeMillis() - tunnelCleanupStartTime
            logInfo("🧹 [CM-DISCONNECT-PERF] Tunnel resources cleaned up", mapOf(
                "duration_ms" to tunnelCleanupDuration
            ))

            // Close UDP connection
            val udpCloseStartTime = System.currentTimeMillis()
            udpConnection?.close()
            udpConnection = null
            val udpCloseDuration = System.currentTimeMillis() - udpCloseStartTime
            logInfo("🔌 [CM-DISCONNECT-PERF] UDP connection closed", mapOf(
                "duration_ms" to udpCloseDuration
            ))

            // Clear protocol handler
            val protocolClearStartTime = System.currentTimeMillis()
            protocolHandler = null
            val protocolClearDuration = System.currentTimeMillis() - protocolClearStartTime
            logInfo("🔧 [CM-DISCONNECT-PERF] Protocol handler cleared", mapOf(
                "duration_ms" to protocolClearDuration
            ))

            // Clear server reference and recorded interface info
            val serverClearStartTime = System.currentTimeMillis()
            currentServer = null
            connectionPhysicalInterface = null
            connectionPhysicalIP = null
            val serverClearDuration = System.currentTimeMillis() - serverClearStartTime
            logInfo("🖥️ [CM-DISCONNECT-PERF] Server reference and interface info cleared", mapOf(
                "duration_ms" to serverClearDuration
            ))

            // Update to disconnected state
            val finalStateUpdateStartTime = System.currentTimeMillis()
            updateVPNState(VPNState.Disconnected)
            val finalStateUpdateDuration = System.currentTimeMillis() - finalStateUpdateStartTime

            val totalDuration = System.currentTimeMillis() - disconnectStartTime
            logInfo("✅ [CM-DISCONNECT-PERF] VPN disconnected successfully", mapOf(
                "reason" to reason.name,
                "final_state_update_ms" to finalStateUpdateDuration,
                "total_duration_ms" to totalDuration,
                "breakdown" to mapOf(
                    "state_update" to stateUpdateDuration,
                    "heartbeat_stop" to heartbeatStopDuration,
                    "traffic_stop" to trafficStopDuration,
                    "tunnel_cleanup" to tunnelCleanupDuration,
                    "udp_close" to udpCloseDuration,
                    "protocol_clear" to protocolClearDuration,
                    "server_clear" to serverClearDuration,
                    "final_state_update" to finalStateUpdateDuration
                )
            ))
            Result.success(Unit)

        } catch (e: Exception) {
            val totalDuration = System.currentTimeMillis() - disconnectStartTime
            logError("❌ [CM-DISCONNECT-PERF] Disconnection failed", mapOf(
                "reason" to reason.name,
                "total_duration_ms" to totalDuration
            ), e)
            // Force state to disconnected even if cleanup failed
            updateVPNState(VPNState.Disconnected)
            Result.failure(VPNServiceError.InterfaceError("Disconnection cleanup failed: ${e.message}"))

        } finally {
            isDisconnecting.set(false)
        }
    }

    /**
     * NAME: performInternalReconnection
     *
     * DESCRIPTION:
     *     Performs internal reconnection for network changes or connection failures.
     *     Simplified to use unified error handling approach.
     *
     * PARAMETERS:
     *     reason - Reason for reconnection
     *
     * RETURNS:
     *     Result<Unit> - Success or failure result
     */
    suspend fun performInternalReconnection(reason: String): Result<Unit> = withContext(Dispatchers.IO) {
        val server = currentServer ?: return@withContext Result.failure(
            VPNServiceError.ReconnectionFailed("No server information available for reconnection")
        )

        logInfo("Starting internal reconnection", mapOf(
            "reason" to reason,
            "server_id" to server.id
        ))

        try {
            // Use unified auto-reconnection handling
            handleAutoReconnection(server, reason)
            Result.success(Unit)

        } catch (e: Exception) {
            logError("Internal reconnection failed", mapOf(
                "reason" to reason,
                "server_id" to server.id
            ), e)

            val error = VPNServiceError.ReconnectionFailed(
                "Reconnection failed: ${e.message}",
                1,
                e.message
            )

            // Use unified error handling
            handleConnectionFailure(error, server, shouldAllowRetry = true)
            Result.failure(error)
        }
    }

    /**
     * NAME: handleNetworkChange
     *
     * DESCRIPTION:
     *     Handles network interface changes (WiFi ↔ Cellular switching).
     *     Sends reconnect required event to UI layer instead of auto-reconnecting.
     *     This allows UI to control the reconnection process.
     *
     * PARAMETERS:
     *     reason - Description of network change
     */
    suspend fun handleNetworkChange(reason: String) {
        val currentState = _state.value
        val server = currentServer

        logInfo("Network change detected", mapOf(
            "reason" to reason,
            "current_state" to currentState.flutterStatusString,
            "is_connected" to currentState.isConnected,
            "has_server" to (server != null)
        ))

        // Only send reconnect notification if currently connected and have server info
        if (currentState.isConnected && server != null) {
            try {
                logInfo("Network change detected, notifying UI for reconnect")

                // Collect network status information
                val networkInfo = mapOf(
                    "change_type" to reason,
                    "server_id" to server.id,
                    "server_name" to server.name,
                    "current_state" to currentState.flutterStatusString
                )

                // Send reconnect required event to UI layer
                vpnStateEventSender.sendReconnectRequiredEvent(
                    reason = VPNStateEventSender.RECONNECT_REASON_NETWORK_CHANGE,
                    message = "Network interface configuration has changed, reconnection required: $reason",
                    networkInfo = networkInfo
                )

                logInfo("Sent reconnect required notification to UI", mapOf(
                    "reason" to VPNStateEventSender.RECONNECT_REASON_NETWORK_CHANGE,
                    "message" to reason
                ))

            } catch (e: Exception) {
                logError("Failed to send reconnect required notification", mapOf("reason" to reason), e)
                // Fallback to old auto-reconnect behavior if event sending fails
                logWarn("Falling back to auto-reconnect due to event sending failure")
                try {
                    handleAutoReconnection(server, "Network change: $reason (fallback)")
                } catch (fallbackError: Exception) {
                    logError("Fallback auto-reconnection also failed", mapOf("reason" to reason), fallbackError)
                    val error = VPNServiceError.NetworkUnavailable("Network change caused connection failure")
                    handleConnectionFailure(error, server, shouldAllowRetry = true)
                }
            }
        }
    }

    // MARK: - State Management

    /**
     * NAME: updateVPNState
     *
     * DESCRIPTION:
     *     Updates VPN state with transition validation.
     *     Implements state transition validation based on architecture review.
     *
     * PARAMETERS:
     *     newState - New VPN state to transition to
     */
    private fun updateVPNState(newState: VPNState) {
        val oldState = _state.value

        // Skip if state hasn't changed
        if (oldState::class == newState::class) {
            return
        }

        // Validate state transition
        if (!VPNState.isValidStateTransition(oldState, newState)) {
            logWarn("Invalid state transition blocked", mapOf(
                "from_state" to oldState.flutterStatusString,
                "to_state" to newState.flutterStatusString
            ))
            return
        }

        // Update state
        _state.value = newState

        logInfo("VPN state changed", mapOf(
            "old_state" to oldState.flutterStatusString,
            "new_state" to newState.flutterStatusString,
            "timestamp" to System.currentTimeMillis()
        ))
    }



    // MARK: - Heartbeat Mechanism (using HeartbeatManager)

    /**
     * NAME: startHeartbeat
     *
     * DESCRIPTION:
     *     Starts heartbeat mechanism using HeartbeatManager for connection monitoring.
     *     Unified heartbeat implementation to avoid duplication.
     *
     * PARAMETERS:
     *     connection - UDP connection for heartbeat
     *     server - Server information for heartbeat
     */
    private fun startHeartbeat(connection: UDPConnection, server: ServerInfo) {
        stopHeartbeat() // Stop any existing heartbeat

        try {
            // Create HeartbeatManager instance
            val currentProtocolHandler = protocolHandler
                ?: throw IllegalStateException("Protocol handler not initialized")

            // Get resolved IP address to avoid DNS resolution in HeartbeatManager
            val serverIP = connection.serverAddress.hostAddress
                ?: throw IllegalStateException("Server IP address not available")

            heartbeatManager = HeartbeatManager(
                socket = connection.socket,
                serverAddress = serverIP, // Use resolved IP instead of hostname
                serverPort = server.serverPort,
                stateMachine = currentProtocolHandler.stateMachine,
                encryptionService = currentProtocolHandler.encryptionService,
                timeoutCallback = this // Pass ConnectionManager as callback
            )

            // Start heartbeat monitoring
            heartbeatManager?.start()

            logInfo("Heartbeat mechanism started using shared UDP socket", mapOf(
                "server_id" to server.id,
                "socket_local_port" to connection.socket.localPort,
                "socket_id" to connection.socket.hashCode(),
                "interval_ms" to 15000L, // HeartbeatManager.HEARTBEAT_INTERVAL_MS
                "threshold" to 3, // HeartbeatManager.MISSED_HEARTBEAT_THRESHOLD
                "protocol_state" to currentProtocolHandler.stateMachine.getState().name,
                "heartbeat_manager_created" to (heartbeatManager != null),
                "note" to "Unified HeartbeatManager implementation - configuration managed by HeartbeatManager"
            ))

        } catch (e: Exception) {
            logError("Failed to start heartbeat mechanism", mapOf("server_name" to server.serverName), e)
            // 心跳启动失败时不自动重连，避免连锁反应
            // 连接仍然可用，只是缺少心跳监控
            logWarn("Heartbeat mechanism failed to start - connection remains active without heartbeat monitoring")
        }
    }

    /**
     * NAME: stopHeartbeat
     *
     * DESCRIPTION:
     *     Stops heartbeat mechanism using HeartbeatManager.
     */
    private fun stopHeartbeat() {
        heartbeatManager?.stop()
        heartbeatManager = null
        logDebug("Heartbeat mechanism stopped")
    }



    // MARK: - Connection Implementation

    /**
     * NAME: resolveServerAddress
     *
     * DESCRIPTION:
     *     Resolves server hostname to IP address using ServerManager's IP cache.
     *     Uses cached IP first, fallback to DNS resolution if needed.
     *
     * PARAMETERS:
     *     hostname - Server hostname to resolve
     *
     * RETURNS:
     *     InetAddress? - Resolved IP address or null if failed
     */
    private suspend fun resolveServerAddress(hostname: String): InetAddress? = withContext(Dispatchers.IO) {
        try {
            logDebug("Resolving server address using IP cache", mapOf("hostname" to hostname))

            // Try to get cached IP first
            val cachedIP = serverManager.getCachedServerIP(hostname)
            if (cachedIP != null) {
                logInfo("Using cached server IP", mapOf(
                    "hostname" to hostname,
                    "cached_ip" to cachedIP
                ))
                return@withContext InetAddress.getByName(cachedIP)
            }

            // If not cached, resolve and cache
            val resolvedIP = serverManager.resolveServerIP(hostname)
            if (resolvedIP != null) {
                logInfo("Server address resolved and cached", mapOf(
                    "hostname" to hostname,
                    "resolved_ip" to resolvedIP
                ))
                return@withContext InetAddress.getByName(resolvedIP)
            }

            logError("Failed to resolve server address", mapOf("hostname" to hostname))
            null
        } catch (e: Exception) {
            logError("Failed to resolve server address", mapOf("hostname" to hostname), e)
            null
        }
    }

    /**
     * NAME: performAuthentication
     *
     * DESCRIPTION:
     *     Performs SDWAN authentication with server using unified error handling.
     *     UPDATED: 24/07/2025 - 集成统一错误处理机制和延迟测量
     *
     * PARAMETERS:
     *     connection - UDP connection to use
     *     server - Server information for authentication
     *     targetIP - Pre-resolved IP address (optional, null means use protocol's serverAddress)
     *
     * RETURNS:
     *     Result<Int?> - Authentication result with latency in milliseconds (null if failed)
     */
    private suspend fun performAuthentication(connection: UDPConnection, server: ServerInfo, targetIP: String? = null): Result<Int?> = withContext(Dispatchers.IO) {
        try {
            logInfo("Starting SDWAN authentication using shared UDP socket", mapOf(
                "server_id" to server.id,
                "socket_local_port" to connection.socket.localPort
            ))

            // Use SDWAN protocol for authentication
            val currentProtocolHandler = protocolHandler
                ?: return@withContext Result.failure(
                    VPNServiceError.AuthenticationFailed(
                        errorMessage = "Protocol handler not initialized",
                        errorCode = VPNErrorCode.INTERNAL
                    )
                )

            // Get credentials from memory
            val credentials = getCurrentCredentials()
                ?: return@withContext Result.failure(
                    VPNServiceError.AuthenticationFailed(
                        errorMessage = "No credentials available. Please login first.",
                        errorCode = VPNErrorCode.AUTH_MISSING_CREDENTIALS
                    )
                )

            val (username, password) = credentials

            logDebug("Using credentials for authentication with shared socket", mapOf(
                "username" to username,
                "socket_local_port" to connection.socket.localPort
            ))

            // 🔧 使用共享UDP socket进行认证，认证方法自己处理响应包
            // 如果有预解析的IP地址就使用，否则让协议层使用其serverAddress（可能是域名）
            // 添加认证超时处理
            val authTimeout = timeoutManager.getTimeoutForOperation(TimeoutManager.OPERATION_AUTHENTICATION)

            val authResult = withTimeout(authTimeout) {
                currentProtocolHandler.authenticateWithSocket(
                    socket = connection.socket,
                    username = username,
                    password = password,
                    targetIP = targetIP
                )
            }

            if (authResult.success) {
                logInfo("SDWAN authentication successful using shared socket - session_id=${authResult.sessionID?.toString()}, latency=${authResult.latencyMs}ms, socket_local_port=${connection.socket.localPort}, socket_id=${connection.socket.hashCode()}")
                Result.success(authResult.latencyMs)
            } else {
                // 使用统一错误处理
                val error = VPNServiceError.AuthenticationFailed(
                    errorMessage = authResult.errorMessage ?: "Authentication failed",
                    serverErrorCode = authResult.errorMessage,
                    isCredentialIssue = isCredentialError(authResult.errorMessage),
                    authMethod = "SDWAN",
                    errorCode = mapAuthErrorCode(authResult.errorMessage)
                )

                // 处理错误并记录
                errorHandler.handleVPNError(error, context = mapOf(
                    "server_id" to server.id,
                    "server_address" to server.serverName,
                    "socket_local_port" to connection.socket.localPort,
                    "target_ip" to (targetIP ?: "unknown")
                ))

                Result.failure(error)
            }

        } catch (e: TimeoutCancellationException) {
            // 处理认证超时 - 这是网络超时，不是凭证错误
            logWarn("Authentication timed out - no response from server", mapOf(
                "server_id" to server.id,
                "target_ip" to (targetIP ?: "unknown"),
                "socket_local_port" to connection.socket.localPort,
                "timeout_duration" to timeoutManager.getTimeoutForOperation(TimeoutManager.OPERATION_AUTHENTICATION)
            ))

            val timeoutError = timeoutManager.createTimeoutError(
                operation = TimeoutManager.OPERATION_AUTHENTICATION,
                duration = timeoutManager.getTimeoutForOperation(TimeoutManager.OPERATION_AUTHENTICATION),
                additionalInfo = mapOf(
                    "server_id" to server.id,
                    "target_ip" to (targetIP ?: "unknown"),
                    "socket_local_port" to connection.socket.localPort
                )
            )

            errorHandler.handleVPNError(timeoutError)
            Result.failure(timeoutError)

        } catch (e: Exception) {
            // 处理其他认证异常
            val error = VPNServiceError.AuthenticationFailed(
                errorMessage = "Authentication failed: ${e.message}",
                errorCode = VPNErrorCode.AUTH_INVALID_CREDENTIALS
            )

            errorHandler.handleVPNError(error, context = mapOf(
                "exception_type" to e::class.java.simpleName,
                "server_id" to server.id,
                "socket_local_port" to connection.socket.localPort
            ))

            Result.failure(error)
        }
    }

    /**
     * NAME: establishTunnel
     *
     * DESCRIPTION:
     *     Establishes VPN tunnel and returns tunnel IP.
     *     Creates VPN interface, configures routing, and starts data flow processing.
     *
     * PARAMETERS:
     *     connection - UDP connection to use
     *
     * RETURNS:
     *     String - Tunnel IP address or empty string if failed
     */
    private suspend fun establishTunnel(connection: UDPConnection): String = withContext(Dispatchers.IO) {
        try {
            logInfo("Establishing VPN tunnel with interface and data flow")

            // For SDWAN protocol, tunnel is established during authentication
            // Check if protocol is in connected state
            val currentProtocolHandler = protocolHandler
            if (currentProtocolHandler == null || !currentProtocolHandler.isConnected()) {
                logError("Failed to establish VPN tunnel - protocol not connected or not initialized")
                return@withContext ""
            }

            // Get tunnel configuration from protocol
            val tunnelIP = currentProtocolHandler.getTunnelIP() ?: "********"
            val gatewayIP = currentProtocolHandler.getGatewayIP() ?: "********"
            val dnsIP = currentProtocolHandler.getDnsIP() ?: "*******"
            val mtu = currentProtocolHandler.getMtu() ?: 1400

            logInfo("VPN tunnel configuration retrieved", mapOf(
                "tunnel_ip" to tunnelIP,
                "gateway_ip" to gatewayIP,
                "dns_ip" to dnsIP,
                "mtu" to mtu
            ))

            // Step 1: Create VPN interface manager (requires VpnService context)
            // Note: This will be injected from VPN service
            val interfaceManager = vpnInterfaceManager
            if (interfaceManager == null) {
                logError("VPN interface manager not available")

                return@withContext ""
            }

            // Step 2: Collect server IPs for exclusion (prevent routing loops)
            val excludedIPs = mutableListOf<String>()

            // Add current target server IP
            val currentServerIP = connection.serverAddress.hostAddress
            excludedIPs.add(currentServerIP)
            logDebug("Added current server IP to exclusions", mapOf("ip" to currentServerIP))

            // Add all cached server IPs to prevent routing loops (matching iOS pattern)
            try {
                val cachedServerIPs = serverManager.getAllCachedServerIPs()
                for (serverIP in cachedServerIPs) {
                    if (!excludedIPs.contains(serverIP)) {
                        excludedIPs.add(serverIP)
                    }
                }
                logInfo("Added cached server IPs to exclusions", mapOf(
                    "cached_server_ips" to cachedServerIPs.size,
                    "total_excluded_ips" to excludedIPs.size
                ))
            } catch (e: Exception) {
                logWarn("Failed to get server list for exclusions, using current server only", mapOf(
                    "error" to (e.message ?: "unknown")
                ))
            }

            // Step 3: Get routing configuration from memory and apply to VPN
            val routingConfig = routingConfigurationManager.getCurrentRoutingConfiguration()
            logInfo("Applying routing configuration to VPN", mapOf(
                "mode" to routingConfig.mode.toConfigValue(),
                "custom_routes_count" to routingConfig.customRoutes.size,
                "custom_routes" to routingConfig.customRoutes.toString()
            ))

            // Step 4: Create VPN configuration with dynamic routing
            val serverAddress = currentServer?.serverName?.takeIf { it.isNotBlank() } ?: "server.placeholder"
            val configBuilder = interfaceManager.createConfigBuilder()
                .setLocalIP(tunnelIP)
                .setServerAddress(serverAddress)
                .setDNSServers(listOf(dnsIP))
                .setMTU(mtu)

            // Apply routing configuration based on mode
            when (routingConfig.mode) {
                com.panabit.client.platform.models.RoutingMode.ALL -> {
                    configBuilder.useGlobalRouting()
                    logInfo("Applied global routing mode (all traffic through VPN)")
                }
                com.panabit.client.platform.models.RoutingMode.CUSTOM -> {
                    configBuilder.setRoutingMode(com.panabit.client.platform.models.RoutingMode.CUSTOM)
                    // Add custom routes from configuration
                    val routeInfoList = routingConfig.toRouteInfoList()
                    routeInfoList.forEach { route ->
                        configBuilder.addRoute(route.destination, route.prefixLength)
                    }
                    logInfo("Applied custom routing mode", mapOf(
                        "routes_applied" to routeInfoList.size,
                        "routes" to routeInfoList.map { "${it.destination}/${it.prefixLength}" }
                    ))
                }
            }

            val vpnConfigResult = configBuilder
                .addServerExclusions(excludedIPs)
                .addLocalNetworkExclusions()
                .addDynamicLocalSubnetExclusion(context)
                .build()

            if (vpnConfigResult.isFailure) {
                logError("Failed to create VPN configuration", mapOf(
                    "error" to (vpnConfigResult.exceptionOrNull()?.message ?: "unknown")
                ))
                return@withContext ""
            }

            val vpnConfig = vpnConfigResult.getOrNull()!!

            // Step 4: Establish VPN interface
            val interfaceResult = interfaceManager.establishInterface(vpnConfig)
            if (interfaceResult.isFailure) {
                logError("Failed to establish VPN interface", mapOf(
                    "error" to (interfaceResult.exceptionOrNull()?.message ?: "unknown")
                ))
                return@withContext ""
            }

            val tunInterface = interfaceResult.getOrNull()!!
            logInfo("VPN interface established successfully", mapOf(
                "interface_fd" to tunInterface.fd,
                "local_ip" to tunnelIP
            ))

            // Step 5: Protect UDP socket after VPN interface creation
            // This is critical timing - protect() only works after VPN is established
            val socketProtected = connection.protectSocket()
            if (!socketProtected) {
                logWarn("Failed to protect UDP socket - may cause routing loops")
            } else {
                logInfo("UDP socket protected successfully after VPN creation")
            }

            // Step 6: Start integrated TUN data flow processing
            currentTunInterface = tunInterface
            val dataFlowResult = startIntegratedDataFlow(tunInterface, connection)
            if (dataFlowResult.isFailure) {
                logError("Failed to start TUN data flow", mapOf(
                    "error" to (dataFlowResult.exceptionOrNull()?.message ?: "unknown")
                ))
                // Close interface on data flow failure
                tunInterface.close()
                currentTunInterface = null
                return@withContext ""
            }

            // Step 7: Start heartbeat mechanism AFTER UDP receiver is ready
            // This ensures UDP receiver can process ECHO_RESPONSE packets
            currentServer?.let { server ->
                val protocolState = currentProtocolHandler.stateMachine.getState()
                logInfo("Starting heartbeat with protocol state check", mapOf(
                    "server_id" to server.id,
                    "timing" to "after_udp_receiver_ready",
                    "current_server_available" to true,
                    "protocol_handler_available" to (protocolHandler != null),
                    "protocol_state" to protocolState.name
                ))

                startHeartbeat(connection, server)

                logInfo("Heartbeat started after UDP receiver initialization", mapOf(
                    "server_id" to server.id,
                    "heartbeat_manager_created" to (heartbeatManager != null),
                    "heartbeat_manager_running" to (heartbeatManager?.isHealthy() ?: false),
                    "protocol_state" to protocolState.name
                ))
            } ?: run {
                logError("CRITICAL: currentServer is null - heartbeat will NOT start", mapOf(
                    "timing" to "after_udp_receiver_ready",
                    "current_server_available" to false,
                    "protocol_handler_available" to (protocolHandler != null),
                    "note" to "This is the root cause of missing heartbeat packets"
                ))
            }

            logInfo("VPN tunnel established successfully with integrated data flow", mapOf(
                "tunnel_ip" to tunnelIP,
                "interface_fd" to tunInterface.fd,
                "data_flow_active" to isDataFlowActive.get(),
                "heartbeat_started" to (heartbeatManager != null)
            ))

            tunnelIP

        } catch (e: Exception) {
            logError("Tunnel establishment exception", mapOf("error" to (e.message ?: "unknown")), e)
            // Cleanup on failure
            cleanupTunnelResources()
            ""
        }
    }



    /**
     * NAME: startIntegratedDataFlow
     *
     * DESCRIPTION:
     *     Starts integrated TUN data flow processing within ConnectionManager.
     *     Replaces separate TUNDataFlowManager for simplified architecture.
     *
     * PARAMETERS:
     *     tunInterface - TUN interface file descriptor
     *     connection - UDP connection for tunnel communication
     *
     * RETURNS:
     *     Result<Unit> - Success or failure result
     */
    private suspend fun startIntegratedDataFlow(
        tunInterface: ParcelFileDescriptor,
        connection: UDPConnection
    ): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            if (isDataFlowActive.get()) {
                logWarn("Data flow already active - stopping existing flow first")
                stopIntegratedDataFlow()
            }

            logInfo("Starting integrated TUN data flow", mapOf(
                "tun_fd" to tunInterface.fd,
                "udp_connected" to connection.isConnected()
            ))

            // Update state
            isDataFlowActive.set(true)

            // Start data flow coroutines
            startTUNReaderCoroutine(tunInterface, connection)
            startUDPReceiverCoroutine(tunInterface, connection)

            logInfo("Integrated TUN data flow started successfully")
            Result.success(Unit)

        } catch (e: Exception) {
            logError("Failed to start integrated TUN data flow", mapOf("error" to (e.message ?: "unknown")), e)
            stopIntegratedDataFlow()
            Result.failure(e)
        }
    }

    /**
     * NAME: stopIntegratedDataFlow
     *
     * DESCRIPTION:
     *     Stops integrated data flow and cleans up coroutines.
     *     Fixed resource cleanup order to prevent EBADF errors.
     */
    private suspend fun stopIntegratedDataFlow() {
        val stopStartTime = System.currentTimeMillis()
        logInfo("🛑 [DATAFLOW-PERF] Starting integrated TUN data flow stop", mapOf(
            "start_time" to stopStartTime,
            "tun_reader_active" to (tunReaderJob?.isActive ?: false),
            "udp_reader_active" to (udpReaderJob?.isActive ?: false),
            "data_flow_active" to isDataFlowActive.get()
        ))

        // Step 1: Set stop flag FIRST to signal coroutines to stop
        val flagSetStartTime = System.currentTimeMillis()
        isDataFlowActive.set(false)
        val flagSetDuration = System.currentTimeMillis() - flagSetStartTime
        logInfo("🚩 [DATAFLOW-PERF] Stop flag set", mapOf(
            "duration_ms" to flagSetDuration
        ))

        // Step 2: Cancel coroutines BEFORE closing resources
        // This prevents new socket operations from starting
        val cancelStartTime = System.currentTimeMillis()
        tunReaderJob?.cancel()
        udpReaderJob?.cancel()
        val cancelDuration = System.currentTimeMillis() - cancelStartTime
        logInfo("❌ [DATAFLOW-PERF] Coroutines cancelled", mapOf(
            "duration_ms" to cancelDuration,
            "tun_reader_active_after_cancel" to (tunReaderJob?.isActive ?: false),
            "udp_reader_active_after_cancel" to (udpReaderJob?.isActive ?: false)
        ))

        // Step 3: Close resources to interrupt any remaining blocking IO operations
        val udpCloseStartTime = System.currentTimeMillis()
        try {
            // Close UDP connection to interrupt socket.receive()
            udpConnection?.close()
            val udpCloseDuration = System.currentTimeMillis() - udpCloseStartTime
            logInfo("🔌 [DATAFLOW-PERF] UDP connection closed to interrupt receiver", mapOf(
                "duration_ms" to udpCloseDuration
            ))
        } catch (e: Exception) {
            val udpCloseDuration = System.currentTimeMillis() - udpCloseStartTime
            logWarn("⚠️ [DATAFLOW-PERF] Error closing UDP connection during shutdown", mapOf(
                "error" to (e.message ?: "unknown"),
                "duration_ms" to udpCloseDuration
            ))
        }

        val tunCloseStartTime = System.currentTimeMillis()
        try {
            // Close TUN interface to interrupt inputStream.read()
            currentTunInterface?.close()
            val tunCloseDuration = System.currentTimeMillis() - tunCloseStartTime
            logInfo("🔌 [DATAFLOW-PERF] TUN interface closed to interrupt reader", mapOf(
                "duration_ms" to tunCloseDuration
            ))
        } catch (e: Exception) {
            val tunCloseDuration = System.currentTimeMillis() - tunCloseStartTime
            logWarn("⚠️ [DATAFLOW-PERF] Error closing TUN interface during shutdown", mapOf(
                "error" to (e.message ?: "unknown"),
                "duration_ms" to tunCloseDuration
            ))
        }

        // Step 4: Optimized coroutine cleanup with smart timeout strategy
        val joinStartTime = System.currentTimeMillis()

        // Check if coroutines are already inactive - if so, skip join entirely
        val tunActive = tunReaderJob?.isActive ?: false
        val udpActive = udpReaderJob?.isActive ?: false

        if (!tunActive && !udpActive) {
            logInfo("⚡ [DATAFLOW-PERF] Coroutines already inactive - skipping join", mapOf(
                "tun_reader_active" to tunActive,
                "udp_reader_active" to udpActive,
                "optimization" to "skip_join_for_inactive_coroutines"
            ))
        } else {
            // Use shorter timeout since we've already cancelled and closed resources
            val joinTimeout = 200L // Reduced to 200ms - should be sufficient after resource closure

            logInfo("⚡ [DATAFLOW-PERF] Starting coroutine join with optimized timeout", mapOf(
                "timeout_ms" to joinTimeout,
                "tun_reader_active" to tunActive,
                "udp_reader_active" to udpActive,
                "optimization" to "short_timeout_after_resource_closure"
            ))

            val joinResult = withTimeoutOrNull(joinTimeout) {
                // Only join coroutines that are still active
                if (tunActive) {
                    val tunJoinStartTime = System.currentTimeMillis()
                    tunReaderJob?.join()
                    val tunJoinDuration = System.currentTimeMillis() - tunJoinStartTime
                    logInfo("🔗 [DATAFLOW-PERF] TUN reader join completed", mapOf(
                        "duration_ms" to tunJoinDuration
                    ))
                }

                if (udpActive) {
                    val udpJoinStartTime = System.currentTimeMillis()
                    udpReaderJob?.join()
                    val udpJoinDuration = System.currentTimeMillis() - udpJoinStartTime
                    logInfo("🔗 [DATAFLOW-PERF] UDP reader join completed", mapOf(
                        "duration_ms" to udpJoinDuration
                    ))
                }
            }

            val joinDuration = System.currentTimeMillis() - joinStartTime

            if (joinResult == null) {
                logWarn("⏰ [DATAFLOW-PERF] Timeout waiting for coroutines (proceeding with cleanup)", mapOf(
                    "timeout_ms" to joinTimeout,
                    "actual_wait_ms" to joinDuration,
                    "tun_reader_active" to (tunReaderJob?.isActive ?: false),
                    "udp_reader_active" to (udpReaderJob?.isActive ?: false),
                    "note" to "Timeout is acceptable - resources already closed"
                ))
            } else {
                logInfo("✅ [DATAFLOW-PERF] Coroutine join completed successfully", mapOf(
                    "join_duration_ms" to joinDuration
                ))
            }
        }

        // Clear job references
        tunReaderJob = null
        udpReaderJob = null

        val totalDuration = System.currentTimeMillis() - stopStartTime
        val joinDuration = System.currentTimeMillis() - joinStartTime

        logInfo("✅ [DATAFLOW-PERF] Integrated TUN data flow stopped", mapOf(
            "total_duration_ms" to totalDuration,
            "join_successful" to (!tunActive && !udpActive),
            "tun_bytes_read" to tunBytesRead.get(),
            "tun_bytes_written" to tunBytesWritten.get(),
            "packets_processed" to packetsProcessed.get(),
            "breakdown" to mapOf(
                "flag_set" to flagSetDuration,
                "resource_close" to (tunCloseStartTime - udpCloseStartTime + System.currentTimeMillis() - tunCloseStartTime),
                "coroutine_cancel" to cancelDuration,
                "coroutine_join" to joinDuration
            )
        ))
    }

    /**
     * NAME: cleanupTunnelResources
     *
     * DESCRIPTION:
     *     Cleans up tunnel-related resources including data flow and interface.
     */
    private suspend fun cleanupTunnelResources() {
        val cleanupStartTime = System.currentTimeMillis()
        logInfo("🧹 [TUNNEL-CLEANUP-PERF] Starting tunnel resources cleanup", mapOf(
            "start_time" to cleanupStartTime
        ))

        try {
            // Stop integrated data flow
            val dataFlowStopStartTime = System.currentTimeMillis()
            stopIntegratedDataFlow()
            val dataFlowStopDuration = System.currentTimeMillis() - dataFlowStopStartTime
            logInfo("🛑 [TUNNEL-CLEANUP-PERF] Integrated data flow stopped", mapOf(
                "duration_ms" to dataFlowStopDuration
            ))

            // Close TUN interface
            val tunCloseStartTime = System.currentTimeMillis()
            currentTunInterface?.close()
            currentTunInterface = null
            val tunCloseDuration = System.currentTimeMillis() - tunCloseStartTime
            logInfo("🔌 [TUNNEL-CLEANUP-PERF] TUN interface closed", mapOf(
                "duration_ms" to tunCloseDuration
            ))

            // Close VPN interface
            val vpnCloseStartTime = System.currentTimeMillis()
            vpnInterfaceManager?.closeInterface()
            val vpnCloseDuration = System.currentTimeMillis() - vpnCloseStartTime
            logInfo("🔌 [TUNNEL-CLEANUP-PERF] VPN interface closed", mapOf(
                "duration_ms" to vpnCloseDuration
            ))

            val totalDuration = System.currentTimeMillis() - cleanupStartTime
            logInfo("✅ [TUNNEL-CLEANUP-PERF] Tunnel resources cleaned up successfully", mapOf(
                "total_duration_ms" to totalDuration,
                "breakdown" to mapOf(
                    "data_flow_stop" to dataFlowStopDuration,
                    "tun_close" to tunCloseDuration,
                    "vpn_close" to vpnCloseDuration
                )
            ))
        } catch (e: Exception) {
            val totalDuration = System.currentTimeMillis() - cleanupStartTime
            logWarn("⚠️ [TUNNEL-CLEANUP-PERF] Error during tunnel cleanup", mapOf(
                "error" to (e.message ?: "unknown"),
                "total_duration_ms" to totalDuration
            ))
        }
    }

    /**
     * NAME: startTUNReaderCoroutine
     *
     * DESCRIPTION:
     *     Starts TUN interface reader coroutine for packet forwarding.
     */
    private fun startTUNReaderCoroutine(tunInterface: ParcelFileDescriptor, connection: UDPConnection) {
        tunReaderJob = dataFlowScope.launch {
            logInfo("TUN reader coroutine started")
            val buffer = ByteArray(32768) // 32KB buffer
            val inputStream = FileInputStream(tunInterface.fileDescriptor)

            // Performance optimized: removed all packet counting and statistics

            try {
                while (isDataFlowActive.get()) {
                    // 🔧 FIX: Check coroutine cancellation before each iteration
                    ensureActive()

                    try {
                        // 🚀 PERFORMANCE FIX: Use runInterruptible to make blocking read cancellable
                        // This allows the coroutine to respond to cancellation immediately
                        val bytesRead = runInterruptible(Dispatchers.IO) {
                            inputStream.read(buffer)
                        }

                        // Handle end of stream or interface closed
                        if (bytesRead <= 0) {
                            logInfo("TUN interface reached end of stream, stopping reader")
                            break
                        }

                        if (bytesRead > 0) {
                            // Performance optimized: removed packet counting and debug logging
                            tunBytesRead.addAndGet(bytesRead.toLong())

                            // 检查协议状态，避免在协议未连接时处理数据包
                            val currentProtocolHandler = protocolHandler
                            if (currentProtocolHandler == null || !currentProtocolHandler.isConnected()) {
                                logWarn("TUN reader: Protocol not connected, dropping packet", mapOf(
                                    "packet_size" to bytesRead,
                                    "protocol_available" to (currentProtocolHandler != null),
                                    "protocol_connected" to (currentProtocolHandler?.isConnected() ?: false)
                                ))
                                continue // 跳过这个包，避免异常
                            }

                            try {
                                // Create SDWAN packet and send through UDP tunnel
                                val packetData = buffer.copyOfRange(0, bytesRead)
                                val sdwanPacket = createDataPacket(packetData)
                                val sdwanPacketBytes = sdwanPacket.toByteArray()

                                val sendResult = connection.send(sdwanPacketBytes)
                                if (sendResult.isSuccess) {
                                    packetsProcessed.incrementAndGet()

                                    // logDebug("TUN reader: Packet forwarded to UDP tunnel", mapOf(
                                    //     "original_size" to bytesRead,
                                    //     "sdwan_packet_size" to sdwanPacketBytes.size,
                                    //     "overhead_bytes" to (sdwanPacketBytes.size - bytesRead)
                                    // ))
                                } else {
                                    val sendError = sendResult.exceptionOrNull()
                                    val errorMessage = sendError?.message ?: "unknown"

                                    logError("TUN reader: Failed to send packet to UDP tunnel", mapOf(
                                        "error" to errorMessage,
                                        "error_type" to (sendError?.javaClass?.simpleName ?: "unknown")
                                    ))

                                    // Check if this is a network error that should trigger reconnection
                                    val isNetworkError = when {
                                        errorMessage.contains("ENETUNREACH") -> true
                                        errorMessage.contains("Network is unreachable") -> true
                                        errorMessage.contains("EHOSTUNREACH") -> true
                                        errorMessage.contains("Host is unreachable") -> true
                                        errorMessage.contains("ENETDOWN") -> true
                                        errorMessage.contains("Network is down") -> true
                                        errorMessage.contains("ECONNREFUSED") -> true
                                        errorMessage.contains("Connection refused") -> true
                                        errorMessage.contains("EHOSTDOWN") -> true
                                        errorMessage.contains("Host is down") -> true
                                        else -> false
                                    }

                                    if (isNetworkError && _state.value.isConnected) {
                                        logWarn("Network error detected in TUN data forwarding, notifying UI for reconnect", mapOf(
                                            "error_message" to errorMessage,
                                            "current_state" to _state.value.flutterStatusString
                                        ))

                                        // Notify UI for reconnection
                                        handleTunNetworkError(errorMessage)
                                    }
                                }
                            } catch (e: Exception) {
                                logError("TUN reader: Failed to process packet", mapOf(
                                    "error" to (e.message ?: "unknown"),
                                    "packet_size" to bytesRead
                                ), e)
                                // 继续处理下一个包，不要让单个包的错误影响整个数据流
                            }

                            // Performance optimized: removed periodic packet statistics logging

                        }
                        // Note: No delay needed here - runInterruptible with blocking read
                        // doesn't cause CPU spinning, and delay would hurt performance

                        // Performance optimized: removed memory usage logging

                    } catch (e: IOException) {
                        // Check if this is due to interface being closed during shutdown
                        if (!isDataFlowActive.get()) {
                            logInfo("TUN interface closed during shutdown")
                            break
                        }
                        logError("TUN reader IO error", mapOf(
                            "error" to (e.message ?: "unknown"),
                            "error_type" to e::class.java.simpleName
                        ), e)
                        delay(10L) // Brief delay before retry
                    } catch (e: Exception) {
                        logError("TUN reader unexpected error", mapOf(
                            "error" to (e.message ?: "unknown"),
                            "error_type" to e::class.java.simpleName
                        ), e)
                        delay(10L) // Brief delay before retry
                    }
                }
            } catch (e: CancellationException) {
                logInfo("TUN reader cancelled")
                throw e // Re-throw cancellation exception
            } catch (e: Exception) {
                logError("TUN reader fatal error", mapOf("error" to (e.message ?: "unknown")), e)
            } finally {
                try {
                    inputStream.close()
                } catch (e: Exception) {
                    logWarn("Error closing TUN input stream", mapOf("error" to (e.message ?: "unknown")))
                }
                logInfo("TUN reader coroutine stopped")
            }
        }
    }



    /**
     * NAME: startUDPReceiverCoroutine
     *
     * DESCRIPTION:
     *     Starts UDP receiver coroutine for packet writing to TUN.
     */
    private fun startUDPReceiverCoroutine(tunInterface: ParcelFileDescriptor, connection: UDPConnection) {
        udpReaderJob = dataFlowScope.launch {
            logInfo("UDP receiver coroutine started")
            val outputStream = FileOutputStream(tunInterface.fileDescriptor)

            // Performance optimized: removed all packet counting and statistics

            try {
                while (isDataFlowActive.get()) {
                    // 🔧 FIX: Check coroutine cancellation before each iteration
                    ensureActive()

                    try {
                        // 🚀 PERFORMANCE FIX: runInterruptible is applied inside UDPConnection.receive()
                        // This allows the coroutine to respond to cancellation immediately
                        val receiveResult = connection.receive()

                        if (receiveResult.isSuccess) {
                            val packetData = receiveResult.getOrNull()
                            if (packetData != null && packetData.isNotEmpty()) {
                                // Performance optimized: removed packet counting and debug logging

                                // Parse SDWAN packet and extract IP payload
                                val sdwanPacket = try {
                                    SDWANPacket.fromByteArray(packetData)
                                } catch (e: Exception) {
                                    logError("Failed to parse SDWAN packet", mapOf(
                                        "error" to (e.message ?: "unknown"),
                                        "packet_size" to packetData.size
                                    ))
                                    continue // Skip malformed packet
                                }

                                // // 🔍 DEBUG: Log ALL received packets to verify ECHO_RESPONSE reception
                                // logInfo("UDP receiver: Packet received and parsed", mapOf(
                                //     "packet_type" to sdwanPacket.header.type.name,
                                //     "packet_type_value" to "0x${sdwanPacket.header.type.value.toString(16)}",
                                //     "session_id" to sdwanPacket.header.sessionID,
                                //     "token" to sdwanPacket.header.token,
                                //     "packet_size" to packetData.size,
                                //     "payload_size" to sdwanPacket.data.size,
                                //     "encryption" to sdwanPacket.header.encrypt.name,
                                //     "is_echo_response" to (sdwanPacket.header.type == PacketType.ECHO_RESPONSE)
                                // ))

                                // 🔧 统一处理所有类型的包（参考iOS实现）
                                when (sdwanPacket.header.type) {
                                    // 数据包：写入TUN接口
                                    PacketType.DATA,
                                    PacketType.DATA_ENCRYPTED,
                                    PacketType.DATA_DUP,
                                    PacketType.DATA_ENC_DUP,
                                    PacketType.DATA_IPV6,
                                    PacketType.SEGMENT_ROUTING -> {
                                        // Decrypt data if it's encrypted
                                        val ipPayload = if (isEncryptedDataPacket(sdwanPacket.header.type)) {
                                            val currentProtocolHandler = protocolHandler
                                            if (currentProtocolHandler != null) {
                                                try {
                                                    currentProtocolHandler.encryptionService.decrypt(sdwanPacket.data)
                                                } catch (e: Exception) {
                                                    logError("Failed to decrypt data packet", mapOf(
                                                        "error" to (e.message ?: "unknown"),
                                                        "packet_type" to sdwanPacket.header.type.name
                                                    ))
                                                    continue // Skip this packet
                                                }
                                            } else {
                                                logError("No protocol handler available for decryption")
                                                continue // Skip this packet
                                            }
                                        } else {
                                            sdwanPacket.data
                                        }

                                        // Write IP payload to TUN interface
                                        try {
                                            // Add basic validation before writing to TUN
                                            if (ipPayload.isEmpty()) {
                                                logError("Empty IP payload, skipping TUN write", mapOf(
                                                    "packet_type" to sdwanPacket.header.type.name
                                                ))
                                                continue
                                            }

                                            outputStream.write(ipPayload)
                                            outputStream.flush()
                                        } catch (e: Exception) {
                                            logError("Failed to write IP payload to TUN interface", mapOf(
                                                "error" to (e.message ?: "unknown"),
                                                "ip_payload_size" to ipPayload.size,
                                                "packet_type" to sdwanPacket.header.type.name,
                                                "was_encrypted" to (sdwanPacket.header.type == PacketType.DATA_ENCRYPTED),
                                                "first_bytes" to if (ipPayload.isNotEmpty())
                                                    ipPayload.take(16).joinToString(" ") { "%02x".format(it) }
                                                else "empty"
                                            ), e)
                                            continue // Skip this packet and continue processing
                                        }

                                        val newTotalBytesWritten = tunBytesWritten.addAndGet(ipPayload.size.toLong())
                                        packetsProcessed.incrementAndGet()

                                        // logDebug("UDP receiver: Data packet written to TUN", mapOf(
                                        //     "sdwan_packet_size" to packetData.size,
                                        //     "ip_payload_size" to ipPayload.size,
                                        //     "total_bytes_written" to newTotalBytesWritten,
                                        //     "packet_type" to sdwanPacket.header.type.name,
                                        //     "was_encrypted" to (sdwanPacket.header.type == PacketType.DATA_ENCRYPTED),
                                        //     "overhead_bytes" to (packetData.size - ipPayload.size)
                                        // ))
                                    }

                                    // IP分片包：进行分片重组处理 (following Go backend implementation)
                                    PacketType.IP_FRAGMENT,
                                    PacketType.IP_FRAGMENT_IPV6 -> {
                                        try {
                                            // Use sdwanPacket.data (SDWAN header already removed) like Go backend
                                            val fragment = IPFragment.parseFragment(sdwanPacket.data)
                                            val reassembledData = fragmentQueue.addFragment(fragment)

                                            if (reassembledData != null) {
                                                // 分片重组完成，写入TUN接口
                                                outputStream.write(reassembledData)
                                                outputStream.flush()

                                                val newTotalBytesWritten = tunBytesWritten.addAndGet(reassembledData.size.toLong())
                                                packetsProcessed.incrementAndGet()

                                                logDebug("Fragment reassembled and written to TUN", mapOf(
                                                    "fragment_id" to fragment.id,
                                                    "reassembled_size" to reassembledData.size,
                                                    "total_bytes_written" to newTotalBytesWritten,
                                                    "packet_type" to sdwanPacket.header.type.name
                                                ))
                                            }
                                            // Note: No logging for individual fragments to match Java implementation
                                        } catch (e: Exception) {
                                            logError("Failed to process fragment packet", mapOf(
                                                "error" to (e.message ?: "unknown"),
                                                "packet_type" to sdwanPacket.header.type.name,
                                                "fragment_size" to sdwanPacket.data.size
                                            ), e)
                                        }
                                    }

                                    // Close包：服务器关闭连接
                                    PacketType.CLOSE -> {
                                        logInfo("UDP receiver: Server close packet received", mapOf(
                                            "packet_type" to sdwanPacket.header.type.name,
                                            "session_id" to sdwanPacket.header.sessionID,
                                            "token" to sdwanPacket.header.token
                                        ))

                                        // 更新协议状态为关闭状态
                                        val currentProtocolHandler = protocolHandler
                                        if (currentProtocolHandler != null) {
                                            try {
                                                currentProtocolHandler.stateMachine.setState(ProtocolState.CLOSED, "Server sent close packet")
                                                logInfo("Protocol state updated to CLOSED due to server close packet")
                                            } catch (e: Exception) {
                                                logError("Failed to update protocol state on close packet", mapOf(
                                                    "error" to (e.message ?: "unknown")
                                                ), e)
                                            }
                                        }

                                        // 🔧 执行完整的断线处理（匹配iOS/Go后端行为）
                                        logInfo("Server initiated disconnect - executing full disconnect process")

                                        // 在协程中执行断线处理，避免阻塞UDP接收循环
                                        CoroutineScope(Dispatchers.IO).launch {
                                            try {
                                                // 调用disconnect方法，触发状态变更和Event Channel通知
                                                val disconnectResult = disconnect(DisconnectReason.ServerInitiated)
                                                if (disconnectResult.isSuccess) {
                                                    logInfo("Server-initiated disconnect completed successfully")
                                                } else {
                                                    logError("Server-initiated disconnect failed", mapOf(
                                                        "error" to (disconnectResult.exceptionOrNull()?.message ?: "unknown")
                                                    ))
                                                }
                                            } catch (e: Exception) {
                                                logError("Failed to handle server-initiated disconnect", mapOf(
                                                    "error" to (e.message ?: "unknown")
                                                ), e)
                                            }
                                        }
                                    }

                                    // Echo响应包（heartbeat）：转发给HeartbeatManager处理
                                    PacketType.ECHO_RESPONSE -> {
                                        val currentHeartbeatManager = heartbeatManager

                                        logInfo("UDP receiver: Echo response (heartbeat) received", mapOf(
                                            "packet_type" to sdwanPacket.header.type.name,
                                            "packet_type_value" to "0x${sdwanPacket.header.type.value.toString(16)}",
                                            "session_id" to sdwanPacket.header.sessionID,
                                            "token" to sdwanPacket.header.token,
                                            "heartbeat_manager_available" to (currentHeartbeatManager != null),
                                            "heartbeat_manager_running" to (currentHeartbeatManager?.isHealthy() ?: false),
                                            "receive_timestamp" to System.currentTimeMillis(),
                                            "packet_size" to packetData.size,
                                            "payload_size" to sdwanPacket.data.size
                                        ))

                                        if (currentHeartbeatManager != null) {
                                            // 使用统一的heartbeat响应处理方法
                                            val processed = currentHeartbeatManager.processEchoResponse(sdwanPacket)

                                            logInfo("UDP receiver: Echo response processed successfully", mapOf(
                                                "processed_successfully" to processed,
                                                "session_id" to sdwanPacket.header.sessionID,
                                                "token" to sdwanPacket.header.token,
                                                "heartbeat_manager_healthy" to currentHeartbeatManager.isHealthy(),
                                                "average_delay_ms" to currentHeartbeatManager.getAverageDelay()
                                            ))
                                        } else {
                                            // HeartbeatManager not available - this indicates a timing issue
                                            logWarn("UDP receiver: HeartbeatManager not available for echo response", mapOf(
                                                "session_id" to sdwanPacket.header.sessionID,
                                                "token" to sdwanPacket.header.token,
                                                "vpn_state" to _state.value.flutterStatusString,
                                                "current_server_available" to (currentServer != null),
                                                "protocol_handler_available" to (protocolHandler != null),
                                                "warning" to "Heartbeat manager was not initialized - this is the root cause"
                                            ))

                                            // Update state machine heartbeat directly as fallback
                                            protocolHandler?.stateMachine?.updateHeartbeat()
                                        }
                                    }

                                    // 其他控制包：记录但不处理
                                    else -> {
                                         logDebug("UDP receiver: Control packet received", mapOf(
                                             "packet_type" to sdwanPacket.header.type.name,
                                             "session_id" to sdwanPacket.header.sessionID,
                                             "token" to sdwanPacket.header.token
                                         ))
                                    }
                                }

                            }

                            // Performance optimized: removed packet statistics logging
                        }
                        // Note: No delay needed here - runInterruptible with blocking receive
                        // doesn't cause CPU spinning, and delay would hurt performance

                        // 定期记录内存使用情况（已注释以提升性能）
                        // val currentTime = System.currentTimeMillis()
                        // if (currentTime - lastMemoryLogTime > memoryLogInterval) {
                        //     logMemoryUsage("UDP receiver")
                        //     lastMemoryLogTime = currentTime
                        // }

                    } catch (e: SocketException) {
                        // Check if this is due to socket being closed during shutdown
                        // Handle EBADF (Bad file descriptor) specifically
                        val isShutdownRelated = !isDataFlowActive.get() ||
                                               e.message?.contains("Bad file descriptor") == true ||
                                               e.message?.contains("EBADF") == true ||
                                               !connection.isConnected()

                        if (isShutdownRelated) {
                            logInfo("UDP socket closed during shutdown", mapOf(
                                "error_message" to (e.message ?: "unknown"),
                                "data_flow_active" to isDataFlowActive.get(),
                                "connection_state" to connection.isConnected()
                            ))
                            break
                        }

                        logError("UDP receiver socket error", mapOf(
                            "error" to (e.message ?: "unknown"),
                            "error_type" to e::class.java.simpleName
                        ), e)

                        // Check if this is a network error that should trigger reconnection
                        val errorMessage = e.message ?: ""
                        val isNetworkError = when {
                            errorMessage.contains("ENETUNREACH") -> true
                            errorMessage.contains("Network is unreachable") -> true
                            errorMessage.contains("EHOSTUNREACH") -> true
                            errorMessage.contains("Host is unreachable") -> true
                            errorMessage.contains("ENETDOWN") -> true
                            errorMessage.contains("Network is down") -> true
                            errorMessage.contains("ECONNREFUSED") -> true
                            errorMessage.contains("Connection refused") -> true
                            errorMessage.contains("EHOSTDOWN") -> true
                            errorMessage.contains("Host is down") -> true
                            else -> false
                        }

                        if (isNetworkError && _state.value.isConnected) {
                            logWarn("Network error detected in UDP receiver, notifying UI for reconnect", mapOf(
                                "error_message" to errorMessage,
                                "current_state" to _state.value.flutterStatusString
                            ))

                            // Notify UI for reconnection
                            handleTunNetworkError(errorMessage)
                        }

                        delay(10L) // Brief delay before retry
                    } catch (e: Exception) {
                        logError("UDP receiver unexpected error", mapOf(
                            "error" to (e.message ?: "unknown"),
                            "error_type" to e::class.java.simpleName
                        ), e)
                        delay(10L) // Brief delay before retry
                    }
                }
            } catch (e: CancellationException) {
                logInfo("UDP receiver cancelled")
                throw e // Re-throw cancellation exception
            } catch (e: Exception) {
                logError("UDP receiver fatal error", mapOf("error" to (e.message ?: "unknown")), e)
            } finally {
                try {
                    outputStream.close()
                } catch (e: Exception) {
                    logWarn("Error closing TUN output stream", mapOf("error" to (e.message ?: "unknown")))
                }
                logInfo("UDP receiver coroutine stopped")
            }
        }
    }

    /**
     * NAME: logMemoryUsage
     *
     * DESCRIPTION:
     *     记录当前内存使用情况，用于调试GC问题（已注释以提升性能）
     *
     * PARAMETERS:
     *     context - 调用上下文标识
     */
    // private fun logMemoryUsage(context: String) {
    //     try {
    //         val runtime = Runtime.getRuntime()
    //         val maxMemory = runtime.maxMemory()
    //         val totalMemory = runtime.totalMemory()
    //         val freeMemory = runtime.freeMemory()
    //         val usedMemory = totalMemory - freeMemory
    //         val availableMemory = maxMemory - usedMemory
    //
    //         logInfo("Memory usage [$context]", mapOf(
    //             "used_mb" to (usedMemory / 1024 / 1024),
    //             "total_mb" to (totalMemory / 1024 / 1024),
    //             "max_mb" to (maxMemory / 1024 / 1024),
    //             "free_mb" to (freeMemory / 1024 / 1024),
    //             "available_mb" to (availableMemory / 1024 / 1024),
    //             "memory_usage_percent" to ((usedMemory * 100) / maxMemory)
    //         ))
    //
    //         // 如果内存使用超过80%，记录警告
    //         if (usedMemory * 100 / maxMemory > 80) {
    //             logWarn("High memory usage detected", mapOf(
    //                 "context" to context,
    //                 "usage_percent" to ((usedMemory * 100) / maxMemory),
    //                 "used_mb" to (usedMemory / 1024 / 1024)
    //             ))
    //         }
    //     } catch (e: Exception) {
    //         logError("Failed to log memory usage", mapOf(
    //             "context" to context,
    //             "error" to (e.message ?: "unknown")
    //         ))
    //     }
    // }

    /**
     * NAME: isDataPacket
     *
     * DESCRIPTION:
     *     Checks if packet type is a data packet (following iOS pattern).
     *     Matches iOS/Go backend logic for data packet identification.
     *
     * PARAMETERS:
     *     packetType - Packet type to check
     *
     * RETURNS:
     *     Boolean - true if packet carries IP data
     */
    private fun isDataPacket(packetType: PacketType): Boolean {
        return when (packetType) {
            PacketType.DATA,
            PacketType.DATA_ENCRYPTED,
            PacketType.DATA_DUP,
            PacketType.DATA_ENC_DUP,
            PacketType.DATA_IPV6,
            PacketType.IP_FRAGMENT,
            PacketType.IP_FRAGMENT_IPV6,
            PacketType.SEGMENT_ROUTING -> true
            else -> false
        }
    }

    /**
     * NAME: isEncryptedDataPacket
     *
     * DESCRIPTION:
     *     Checks if packet type is an encrypted data packet (following iOS pattern).
     *
     * PARAMETERS:
     *     packetType - Packet type to check
     *
     * RETURNS:
     *     Boolean - true if packet is encrypted
     */
    private fun isEncryptedDataPacket(packetType: PacketType): Boolean {
        return when (packetType) {
            PacketType.DATA_ENCRYPTED,
            PacketType.DATA_ENC_DUP -> true
            else -> false
        }
    }



    /**
     * NAME: createDataPacket
     *
     * DESCRIPTION:
     *     Creates SDWAN data packet from IP payload with current session info.
     *     Following iOS PacketBuilder pattern for data packet creation.
     *
     * PARAMETERS:
     *     ipPayload - IP packet data to encapsulate
     *
     * RETURNS:
     *     SDWANPacket - Data packet with current session info
     */
    private fun createDataPacket(ipPayload: ByteArray): SDWANPacket {
        // Get current session info from protocol handler
        val currentProtocolHandler = protocolHandler
        return if (currentProtocolHandler != null && currentProtocolHandler.isConnected()) {
            try {
                // Get session info from protocol handler
                val sessionInfo = currentProtocolHandler.getSessionInfo()
                val encryptionMethod = currentProtocolHandler.getEncryptionMethod()

                // Following iOS PacketBuilder pattern: create packet based on encryption method
                if (encryptionMethod != EncryptionMethod.NONE) {
                    // Create encrypted data packet
                    val encryptedPayload = currentProtocolHandler.encryptionService.encrypt(ipPayload)

                    val header = PacketHeader(
                        type = PacketType.DATA_ENCRYPTED,
                        encrypt = encryptionMethod,
                        sessionID = sessionInfo.first,
                        token = sessionInfo.second
                    )

                    SDWANPacket(header, encryptedPayload)
                } else {
                    // Create unencrypted data packet
                    createUnencryptedDataPacket(ipPayload, sessionInfo.first, sessionInfo.second)
                }
            } catch (e: Exception) {
                logError("Failed to get session info for data packet", mapOf(
                    "error" to (e.message ?: "unknown")
                ))
                throw e
            }
        } else {
            logError("Protocol not connected, cannot create data packet")
            throw IllegalStateException("Protocol not connected")
        }
    }

    /**
     * NAME: createUnencryptedDataPacket
     *
     * DESCRIPTION:
     *     Creates unencrypted data packet (following iOS pattern).
     *
     * PARAMETERS:
     *     ipPayload - IP packet data
     *     sessionID - Session ID
     *     token - Authentication token
     *
     * RETURNS:
     *     SDWANPacket - Unencrypted data packet
     */
    private fun createUnencryptedDataPacket(
        ipPayload: ByteArray,
        sessionID: UShort,
        token: UInt
    ): SDWANPacket {
        val header = PacketHeader(
            type = PacketType.DATA,
            encrypt = EncryptionMethod.NONE,
            sessionID = sessionID,
            token = token
        )

        return SDWANPacket(header, ipPayload)
    }

    /**
     * NAME: setVPNInterfaceManager
     *
     * DESCRIPTION:
     *     Sets the VPN interface manager instance.
     *     This should be called from VPN service during initialization.
     *
     * PARAMETERS:
     *     manager - VPN interface manager instance
     */
    fun setVPNInterfaceManager(manager: VPNInterfaceManager) {
        vpnInterfaceManager = manager
        logInfo("VPN interface manager set successfully")
    }

    // MARK: - Public State Queries

    /**
     * NAME: isConnected
     *
     * DESCRIPTION:
     *     Returns whether VPN is currently connected.
     *
     * RETURNS:
     *     Boolean - true if connected, false otherwise
     */
    fun isConnected(): Boolean = _state.value.isConnected

    /**
     * NAME: getCurrentServer
     *
     * DESCRIPTION:
     *     Returns currently connected server information.
     *
     * RETURNS:
     *     ServerInfo? - Connected server or null
     */
    fun getCurrentServer(): ServerInfo? = currentServer

    /**
     * NAME: getCurrentState
     *
     * DESCRIPTION:
     *     Returns current VPN state.
     *
     * RETURNS:
     *     VPNState - Current VPN state
     */
    fun getCurrentState(): VPNState = _state.value

    /**
     * NAME: forceResetConnectionState
     *
     * DESCRIPTION:
     *     Force reset connection state to allow new connection attempts.
     *     This is a recovery method for when the state machine gets stuck.
     *     Should only be called when the UI detects an inconsistent state.
     *
     * PARAMETERS:
     *     reason - Reason for the force reset (for logging)
     *
     * RETURNS:
     *     Unit
     */
    suspend fun forceResetConnectionState(reason: String = "UI requested reset") {
        logWarn("Force resetting connection state", mapOf(
            "reason" to reason,
            "current_state" to _state.value.flutterStatusString,
            "is_connecting_flag" to isConnecting.get(),
            "is_disconnecting_flag" to isDisconnecting.get()
        ))

        // Force cleanup all resources
        stopHeartbeat()
        stopTrafficMonitoring()
        udpConnection?.close()
        udpConnection = null
        currentServer = null
        connectionPhysicalInterface = null
        connectionPhysicalIP = null

        // Clear operation flags
        isConnecting.set(false)
        isDisconnecting.set(false)

        // Reset to disconnected state
        updateVPNState(VPNState.Disconnected)

        logInfo("Connection state force reset completed", mapOf(
            "reason" to reason,
            "final_state" to _state.value.flutterStatusString,
            "can_connect" to !_state.value.isOperationInProgress
        ))
    }

    /**
     * NAME: validateOperationInProgress
     *
     * DESCRIPTION:
     *     Validates whether an operation is actually in progress or if the state is stale.
     *     Checks multiple indicators to determine if the operation is genuinely active.
     *
     * PARAMETERS:
     *     currentState - Current VPN state to validate
     *
     * RETURNS:
     *     Boolean - true if operation is genuinely in progress, false if state is stale
     */
    private fun validateOperationInProgress(currentState: VPNState): Boolean {
        return when (currentState) {
            is VPNState.Connecting -> {
                // For connecting state, check if connecting flag is set and we have active resources
                val hasConnectingFlag = isConnecting.get()
                val hasActiveConnection = udpConnection != null
                val hasCurrentServer = currentServer != null

                logDebug("Validating Connecting state", mapOf(
                    "has_connecting_flag" to hasConnectingFlag,
                    "has_active_connection" to hasActiveConnection,
                    "has_current_server" to hasCurrentServer,
                    "progress" to currentState.progress.name
                ))

                // Consider it genuine if we have the connecting flag set
                hasConnectingFlag
            }

            is VPNState.Disconnecting -> {
                // For disconnecting state, check if disconnecting flag is set
                val hasDisconnectingFlag = isDisconnecting.get()

                logDebug("Validating Disconnecting state", mapOf(
                    "has_disconnecting_flag" to hasDisconnectingFlag
                ))

                // Consider it genuine if we have the disconnecting flag set
                hasDisconnectingFlag
            }

            else -> {
                // For other states that claim to be in progress, they're likely stale
                logDebug("Validating non-operation state claiming to be in progress", mapOf(
                    "state_type" to (currentState::class.simpleName ?: "Unknown")
                ))
                false
            }
        }
    }

    // MARK: - Unified Error Handling (iOS-style)

    /**
     * NAME: handleConnectionFailure
     *
     * DESCRIPTION:
     *     Unified error handling for all connection failures.
     *     Based on iOS VPNService.handleConnectionFailure method.
     *     Ensures consistent resource cleanup and state management.
     *
     * PARAMETERS:
     *     error - The error that caused the failure
     *     lastServer - Server that was being used when error occurred (optional)
     *     shouldAllowRetry - Whether to set state to disconnected (allows retry) or error
     */
    private suspend fun handleConnectionFailure(
        error: VPNServiceError,
        lastServer: ServerInfo? = null,
        shouldAllowRetry: Boolean = true
    ) {
        logError("Handling connection failure with unified cleanup", mapOf(
            "error_type" to error.getErrorType(),
            "error_message" to error.errorMessage,
            "current_state" to _state.value.flutterStatusString,
            "should_allow_retry" to shouldAllowRetry,
            "last_server" to (lastServer?.id ?: "none"),
            "is_connecting_flag" to isConnecting.get(),
            "is_disconnecting_flag" to isDisconnecting.get()
        ))

        // 1. Force cleanup of connection resources
        stopHeartbeat()
        stopTrafficMonitoring()
        udpConnection?.close()
        udpConnection = null
        currentServer = null
        connectionPhysicalInterface = null
        connectionPhysicalIP = null

        // 2. Clear any pending operations with explicit logging
        val wasConnecting = isConnecting.getAndSet(false)
        val wasDisconnecting = isDisconnecting.getAndSet(false)

        if (wasConnecting || wasDisconnecting) {
            logInfo("Cleared operation flags during error handling", mapOf(
                "was_connecting" to wasConnecting,
                "was_disconnecting" to wasDisconnecting,
                "error_type" to error.getErrorType()
            ))
        }

        // 3. Set appropriate final state based on error type
        // Always set to error state to ensure UI shows the error and allows user to retry
        updateVPNState(VPNState.Error(error, lastServer))

        // 4. Verify state consistency after cleanup
        val finalState = _state.value
        val canRetry = !finalState.isOperationInProgress

        logInfo("Connection failure handled - set to error state", mapOf(
            "error_type" to error.getErrorType(),
            "should_allow_retry" to shouldAllowRetry,
            "error_code" to error.errorCode.code,
            "final_state" to finalState.flutterStatusString,
            "can_retry" to canRetry,
            "is_connecting_flag" to isConnecting.get(),
            "is_disconnecting_flag" to isDisconnecting.get()
        ))

        // 5. Additional safety check - if state is still showing operation in progress, force reset
        if (finalState.isOperationInProgress) {
            logWarn("State still shows operation in progress after cleanup, forcing reset", mapOf(
                "problematic_state" to finalState.flutterStatusString,
                "error_type" to error.getErrorType()
            ))
            updateVPNState(VPNState.Error(error, lastServer))
        }

        logInfo("Connection failure handling completed", mapOf(
            "final_state" to _state.value.flutterStatusString,
            "error_code" to error.errorCode.code,
            "can_retry" to !_state.value.isOperationInProgress
        ))
    }

    /**
     * NAME: handleAutoReconnection
     *
     * DESCRIPTION:
     *     Handles automatic reconnection for network changes or connection failures.
     *     Based on iOS VPNService auto-reconnection patterns.
     *     Fixed to ensure proper state transitions: connected → disconnected → connecting → connected
     *
     * PARAMETERS:
     *     server - Server to reconnect to
     *     reason - Reason for reconnection
     */
    private suspend fun handleAutoReconnection(server: ServerInfo, reason: String) {
        logInfo("Starting auto-reconnection", mapOf(
            "reason" to reason,
            "server_id" to server.id,
            "current_state" to _state.value.flutterStatusString
        ))

        try {
            // Step 1: First perform complete disconnection if currently connected
            // This ensures proper state transition: connected → disconnecting → disconnected
            if (_state.value.isConnected) {
                logInfo("Performing complete disconnection before reconnection")
                val disconnectResult = disconnect(DisconnectReason.NetworkChange)
                if (disconnectResult.isFailure) {
                    logWarn("Disconnect failed during auto-reconnection, forcing cleanup")
                    // Force cleanup and state reset
                    stopHeartbeat()
                    stopTrafficMonitoring()
                    udpConnection?.close()
                    udpConnection = null
                    currentServer = null
                    updateVPNState(VPNState.Disconnected)
                }
            }

            // Step 2: Wait briefly before reconnecting (similar to iOS)
            delay(2000L)

            // Step 3: Reconnect to the same server
            // This will transition: disconnected → connecting → connected
            val result = connect(server)
            if (result.isSuccess) {
                logInfo("Auto-reconnection completed successfully")
            } else {
                throw result.exceptionOrNull() ?: Exception("Auto-reconnection failed")
            }

        } catch (e: Exception) {
            logError("Auto-reconnection failed", mapOf(
                "reason" to reason,
                "server_id" to server.id
            ), e)

            // Reset state to disconnected when auto-reconnection fails
            // This prevents the VPN from being stuck in reconnecting state
            logInfo("Resetting VPN state to disconnected after failed auto-reconnection")
            updateVPNState(VPNState.Disconnected)
        }
    }

    /**
     * NAME: scheduleAutoReconnection
     *
     * DESCRIPTION:
     *     Schedules automatic reconnection with delay.
     *     Uses service scope to ensure proper lifecycle management.
     *
     * PARAMETERS:
     *     server - Server to reconnect to (optional)
     *     reason - Reason for reconnection
     */
    private fun scheduleAutoReconnection(server: ServerInfo?, reason: String) {
        if (server == null) {
            logWarn("Cannot schedule auto-reconnection: no server information")
            return
        }

        serviceScope.launch {
            try {
                // Wait for network to stabilize
                delay(3000L)

                // Only proceed if still disconnected (user hasn't manually connected)
                if (_state.value is VPNState.Disconnected) {
                    handleAutoReconnection(server, reason)
                }
            } catch (e: Exception) {
                logError("Scheduled auto-reconnection failed", mapOf(
                    "reason" to reason,
                    "server_id" to server.id
                ), e)
            }
        }
    }

    /**
     * NAME: handleTunNetworkError
     *
     * DESCRIPTION:
     *     Handles network errors detected during TUN data forwarding.
     *     Notifies UI for reconnection when network errors occur.
     *
     * PARAMETERS:
     *     errorMessage - The network error message
     */
    private fun handleTunNetworkError(errorMessage: String) {
        try {
            val currentState = _state.value
            val server = currentServer

            // Only send notification if we have server info and are in connected state
            // UI will handle duplicate notifications and reconnection logic
            if (server != null && currentState is VPNState.Connected) {
                logInfo("TUN network error detected, notifying UI for reconnect", mapOf(
                    "error_message" to errorMessage,
                    "server_id" to server.id,
                    "current_state" to currentState.flutterStatusString
                ))

                // Collect network status information for UI
                val networkInfo = mapOf(
                    "error_type" to "tun_network_error",
                    "error_message" to errorMessage,
                    "server_id" to server.id,
                    "server_name" to server.name,
                    "current_state" to currentState.flutterStatusString,
                    "tunnel_ip" to currentState.tunnelIP,
                    "server_id" to currentState.server.id,
                    "timestamp" to System.currentTimeMillis()
                )

                // Send reconnect required event to UI layer
                // UI will handle throttling and reconnection logic
                vpnStateEventSender.sendReconnectRequiredEvent(
                    reason = VPNStateEventSender.RECONNECT_REASON_CONNECTION_LOST,
                    message = "TUN data forwarding network error detected, reconnection required: $errorMessage",
                    networkInfo = networkInfo
                )

                logInfo("Sent TUN network error reconnect notification to UI", mapOf(
                    "reason" to VPNStateEventSender.RECONNECT_REASON_CONNECTION_LOST,
                    "error_message" to errorMessage
                ))

            } else {
                logDebug("TUN network error detected but not notifying UI", mapOf(
                    "error_message" to errorMessage,
                    "has_server" to (server != null),
                    "current_state" to currentState.javaClass.simpleName,
                    "is_connected_state" to (currentState is VPNState.Connected),
                    "reason" to when {
                        server == null -> "no_server"
                        currentState !is VPNState.Connected -> "not_connected_state"
                        else -> "unknown"
                    }
                ))
            }

        } catch (e: Exception) {
            logError("Failed to handle TUN network error", mapOf(
                "error_message" to errorMessage,
                "handler_error" to (e.message ?: "unknown")
            ), e)
        }
    }



    // MARK: - NetworkMonitorDelegate Implementation

    /**
     * NAME: onNetworkChanged
     *
     * DESCRIPTION:
     *     Handles network interface changes from NetworkMonitor.
     *     Triggers VPN reconnection when network interface changes significantly.
     *
     * PARAMETERS:
     *     changeType - Description of the network change
     *     networkType - New network type (TRANSPORT_WIFI, TRANSPORT_CELLULAR, etc.)
     */
    override fun onNetworkChanged(changeType: String, networkType: Int) {
        logInfo("Network change notification received", mapOf(
            "change_type" to changeType,
            "network_type" to NetworkUtils.getNetworkTypeName(networkType),
            "current_state" to _state.value.name
        ))

        // Only handle network changes when VPN is connected
        if (_state.value is VPNState.Connected) {
            logInfo("Triggering VPN reconnection due to network change", mapOf(
                "reason" to changeType
            ))

            // Use existing handleNetworkChange method in coroutine scope
            serviceScope.launch {
                handleNetworkChange(changeType)
            }
        }
    }

    /**
     * NAME: onNetworkAvailable
     *
     * DESCRIPTION:
     *     Handles new network availability from NetworkMonitor.
     *     May trigger reconnection if VPN was disconnected due to network loss.
     *
     * PARAMETERS:
     *     network - The newly available network
     */
    override fun onNetworkAvailable(network: Network) {
        logDebug("Network available notification", mapOf(
            "network_id" to network.toString(),
            "current_state" to _state.value.name
        ))

        // 网络可用时不自动重连，避免前后台切换时的误触发
        // 用户应该手动控制VPN连接，而不是依赖网络状态变化
        logDebug("Network available - auto-reconnection disabled to prevent unwanted reconnections during app lifecycle changes")
    }

    /**
     * NAME: onNetworkLost
     *
     * DESCRIPTION:
     *     Handles network loss from NetworkMonitor.
     *     May trigger VPN disconnection if no alternative networks are available.
     *
     * PARAMETERS:
     *     network - The lost network
     */
    override fun onNetworkLost(network: Network) {
        logWarn("Network lost notification", mapOf(
            "network_id" to network.toString(),
            "current_state" to _state.value.name
        ))

        // Network loss is typically handled by the system
        // VPN will automatically attempt to use alternative networks
        // Only log for monitoring purposes
    }

    /**
     * NAME: shouldTriggerReconnection
     *
     * DESCRIPTION:
     *     Determines if network change should trigger VPN reconnection.
     *     Simple logic: trigger reconnection for any network change when VPN is connected.
     *
     * PARAMETERS:
     *     oldType - Previous network type
     *     newType - New network type
     *
     * RETURNS:
     *     Boolean - true if reconnection should be triggered
     */
    override fun shouldTriggerReconnection(oldType: Int, newType: Int): Boolean {
        // Only trigger reconnection if VPN is connected
        if (_state.value !is VPNState.Connected) {
            return false
        }

        // Trigger reconnection for any network change
        return true
    }

    // MARK: - HeartbeatTimeoutCallback Implementation

    /**
     * NAME: onHeartbeatTimeout
     *
     * DESCRIPTION:
     *     Handles heartbeat timeout notifications from HeartbeatManager.
     *     Sends reconnect required event to UI layer instead of auto-reconnecting.
     *     This allows UI to control the reconnection process.
     *
     * PARAMETERS:
     *     reason - Reason for heartbeat timeout
     */
    override suspend fun onHeartbeatTimeout(reason: String) {
        logWarn("Heartbeat timeout detected, notifying UI for reconnect", mapOf(
            "reason" to reason,
            "current_state" to _state.value.flutterStatusString
        ))

        // Only send reconnect notification if currently connected
        if (_state.value.isConnected) {
            try {
                val server = currentServer
                if (server != null) {
                    logInfo("Heartbeat timeout detected, notifying UI for reconnect")

                    // Collect network status information
                    val networkInfo = mapOf(
                        "timeout_reason" to reason,
                        "server_id" to server.id,
                        "server_name" to server.name,
                        "current_state" to _state.value.flutterStatusString,
                        "heartbeat_interval_ms" to 15000L // HeartbeatManager.HEARTBEAT_INTERVAL_MS
                    )

                    // Send reconnect required event to UI layer
                    vpnStateEventSender.sendReconnectRequiredEvent(
                        reason = VPNStateEventSender.RECONNECT_REASON_HEARTBEAT_TIMEOUT,
                        message = "Heartbeat timeout detected, reconnection required: $reason",
                        networkInfo = networkInfo
                    )

                    logInfo("Sent reconnect required notification to UI", mapOf(
                        "reason" to VPNStateEventSender.RECONNECT_REASON_HEARTBEAT_TIMEOUT,
                        "message" to reason
                    ))
                } else {
                    logError("Heartbeat timeout but no server info available")
                    // Fallback to direct error handling
                    val error = VPNServiceError.TimeoutError(
                        errorMessage = "Heartbeat timeout with no server info",
                        operation = "heartbeat_timeout_no_server",
                        timeoutDuration = 15000L * 3,
                        expectedDuration = 15000L
                    )
                    handleConnectionFailure(error, null, shouldAllowRetry = true)
                }

            } catch (e: Exception) {
                logError("Failed to send heartbeat timeout reconnect notification", mapOf(
                    "reason" to reason
                ), e)

                // Fallback to old auto-reconnect behavior if event sending fails
                logWarn("Falling back to auto-reconnect due to event sending failure")
                try {
                    performInternalReconnection("Heartbeat timeout: $reason (fallback)")
                } catch (fallbackError: Exception) {
                    logError("Fallback auto-reconnection also failed", mapOf("reason" to reason), fallbackError)
                    val error = VPNServiceError.TimeoutError(
                        errorMessage = "Heartbeat timeout and reconnection failed",
                        operation = "heartbeat_reconnection",
                        timeoutDuration = 15000L * 3,
                        expectedDuration = 15000L
                    )
                    handleConnectionFailure(error, currentServer, shouldAllowRetry = true)
                }
            }
        } else {
            logDebug("Heartbeat timeout received but VPN not connected, ignoring", mapOf(
                "current_state" to _state.value.flutterStatusString
            ))
        }
    }

    // MARK: - Traffic Statistics

    /**
     * NAME: startTrafficMonitoring
     *
     * DESCRIPTION:
     *     Starts traffic monitoring with periodic updates.
     *     Matches iOS implementation behavior.
     */
    private fun startTrafficMonitoring() {
        stopTrafficMonitoring() // Stop any existing monitoring

        logInfo("Starting traffic monitoring", mapOf(
            "update_interval_ms" to trafficUpdateInterval
        ))

        // Initialize baseline values
        previousUploadBytes.set(tunBytesRead.get())
        previousDownloadBytes.set(tunBytesWritten.get())
        currentUploadSpeed.set(0)
        currentDownloadSpeed.set(0)
        lastTrafficUpdateTime = System.currentTimeMillis()

        trafficMonitoringJob = dataFlowScope.launch {
            while (isActive && _state.value.isConnected) {
                try {
                    updateTrafficStatistics()
                    delay(trafficUpdateInterval)
                } catch (e: Exception) {
                    logError("Error in traffic monitoring", mapOf(
                        "error" to (e.message ?: "unknown")
                    ), e)
                    delay(trafficUpdateInterval * 2) // Longer delay on error
                }
            }
        }
    }

    /**
     * NAME: stopTrafficMonitoring
     *
     * DESCRIPTION:
     *     Stops traffic monitoring.
     */
    private fun stopTrafficMonitoring() {
        trafficMonitoringJob?.cancel()
        trafficMonitoringJob = null

        // Reset traffic statistics (matching iOS behavior)
        tunBytesRead.set(0)
        tunBytesWritten.set(0)
        packetsProcessed.set(0)
        previousUploadBytes.set(0)
        previousDownloadBytes.set(0)
        currentUploadSpeed.set(0)
        currentDownloadSpeed.set(0)
        lastTrafficUpdateTime = System.currentTimeMillis()

        logDebug("Traffic monitoring stopped and statistics reset")
    }

    /**
     * NAME: updateTrafficStatistics
     *
     * DESCRIPTION:
     *     Updates traffic statistics and calculates speeds.
     *     Matches iOS implementation logic.
     */
    private suspend fun updateTrafficStatistics() {
        val currentTime = System.currentTimeMillis()
        val currentUpload = tunBytesRead.get()
        val currentDownload = tunBytesWritten.get()

        val duration = (currentTime - lastTrafficUpdateTime) / 1000.0 // seconds

        if (duration > 0) {
            // Calculate speed (bytes/second)
            val uploadDiff = currentUpload - previousUploadBytes.get()
            val downloadDiff = currentDownload - previousDownloadBytes.get()

            currentUploadSpeed.set((uploadDiff / duration).toLong())
            currentDownloadSpeed.set((downloadDiff / duration).toLong())

            // logDebug("Updated traffic speed", mapOf(
            //     "duration_seconds" to duration,
            //     "upload_diff_bytes" to uploadDiff,
            //     "download_diff_bytes" to downloadDiff,
            //     "upload_speed_bps" to currentUploadSpeed.get(),
            //     "download_speed_bps" to currentDownloadSpeed.get(),
            //     "total_upload_bytes" to currentUpload,
            //     "total_download_bytes" to currentDownload
            // ))

            // Update previous values for next calculation
            previousUploadBytes.set(currentUpload)
            previousDownloadBytes.set(currentDownload)
            lastTrafficUpdateTime = currentTime

            // Send traffic statistics to Flutter
            sendTrafficStatisticsToFlutter()
        }
    }

    /**
     * NAME: sendTrafficStatisticsToFlutter
     *
     * DESCRIPTION:
     *     Sends traffic statistics to Flutter via event channel.
     *     Matches iOS data format exactly.
     */
    private suspend fun sendTrafficStatisticsToFlutter() {
        try {
            val trafficData = mapOf(
                "upload_speed" to currentUploadSpeed.get(),
                "download_speed" to currentDownloadSpeed.get(),
                "total_upload" to tunBytesRead.get(),
                "total_download" to tunBytesWritten.get(),
                "timestamp" to (System.currentTimeMillis() / 1000) // Unix timestamp in seconds
            )

            // Send event to Flutter via platform channel handler
            val platformHandler = MainActivity.getPlatformChannelHandler()
            platformHandler?.sendTrafficStatisticsEvent(trafficData)

            // logDebug("Traffic statistics sent to Flutter", mapOf(
            //     "upload_speed" to currentUploadSpeed.get(),
            //     "download_speed" to currentDownloadSpeed.get(),
            //     "total_upload" to tunBytesRead.get(),
            //     "total_download" to tunBytesWritten.get()
            // ))

        } catch (e: Exception) {
            logError("Failed to send traffic statistics to Flutter", mapOf(
                "error" to (e.message ?: "unknown")
            ), e)
        }
    }

    /**
     * NAME: getTrafficStatistics
     *
     * DESCRIPTION:
     *     Returns current traffic statistics.
     *     Provides API for external access to traffic data.
     *
     * RETURNS:
     *     Map<String, Any> - Traffic statistics data
     */
    fun getTrafficStatistics(): Map<String, Any> {
        return mapOf(
            "total_upload" to tunBytesRead.get(),
            "total_download" to tunBytesWritten.get(),
            "upload_speed" to currentUploadSpeed.get(),
            "download_speed" to currentDownloadSpeed.get(),
            "packets_processed" to packetsProcessed.get(),
            "last_update" to lastTrafficUpdateTime
        )
    }

    /**
     * NAME: getVPNStateEventSender
     *
     * DESCRIPTION:
     *     Returns the VPN state event sender for external access.
     *     Allows other components to send events to Flutter UI layer.
     *
     * RETURNS:
     *     VPNStateEventSender - Event sender instance
     */
    fun getVPNStateEventSender(): VPNStateEventSender {
        return vpnStateEventSender
    }

    // MARK: - 错误处理辅助方法（新增）

    /**
     * NAME: mapAuthErrorCode
     *
     * DESCRIPTION:
     *     将服务器返回的认证错误代码映射到统一的VPN错误代码
     *
     * PARAMETERS:
     *     serverErrorCode - 服务器返回的错误代码
     *
     * RETURNS:
     *     VPNErrorCode - 对应的VPN错误代码
     */
    private fun mapAuthErrorCode(serverErrorCode: String?): VPNErrorCode {
        return when (serverErrorCode?.lowercase()) {
            "invalid_credentials", "401", "unauthorized" -> VPNErrorCode.AUTH_INVALID_CREDENTIALS
            "expired_credentials", "403", "forbidden" -> VPNErrorCode.AUTH_EXPIRED_CREDENTIALS
            "rate_limited", "429", "too_many_requests" -> VPNErrorCode.AUTH_RATE_LIMITED
            "account_locked", "423", "locked" -> VPNErrorCode.AUTH_ACCOUNT_LOCKED
            "token_invalid", "invalid_token" -> VPNErrorCode.AUTH_TOKEN_INVALID
            "token_expired", "expired_token" -> VPNErrorCode.AUTH_TOKEN_EXPIRED
            "missing_credentials", "400", "bad_request" -> VPNErrorCode.AUTH_MISSING_CREDENTIALS
            else -> VPNErrorCode.AUTH_INVALID_CREDENTIALS // 默认为凭据错误
        }
    }

    /**
     * NAME: isCredentialError
     *
     * DESCRIPTION:
     *     判断错误消息是否表示凭据相关的错误。
     *     核心逻辑：
     *     - 收到OPENREJECT包的任何错误 = 凭证问题（用户名密码错误）
     *     - 认证超时（没有收到响应）= 网络问题，不是凭证问题
     *
     * PARAMETERS:
     *     errorMessage - 错误消息
     *
     * RETURNS:
     *     Boolean - 如果是凭证错误返回true，如果是超时或网络错误返回false
     */
    private fun isCredentialError(errorMessage: String?): Boolean {
        if (errorMessage == null) return false // null表示未知错误，不应默认为凭证错误

        val lowerMessage = errorMessage.lowercase()

        // 明确的非凭据错误（超时、网络、服务器问题）
        val nonCredentialErrors = listOf(
            "timeout", "timed out", "超时",
            "connection failed", "连接失败",
            "network", "网络",
            "unreachable", "不可达",
            "server error", "服务器错误",
            "server is full", "服务器已满"
        )

        // 如果是明确的非凭据错误，返回false
        if (nonCredentialErrors.any { error -> lowerMessage.contains(error) }) {
            return false
        }

        // 来自OPENREJECT包的所有错误都视为凭证错误
        // 包括：invalid username, invalid password, account expired, account disabled等
        // 以及其他认证相关的错误消息
        val credentialKeywords = listOf(
            "invalid username", "invalid password", "用户名", "密码",
            "account expired", "account disabled", "账户",
            "invalid token", "unauthorized", "未授权",
            "authentication failed", "认证失败", "auth failed",
            "invalid", "wrong", "incorrect", "credential", "凭据"
        )

        return credentialKeywords.any { keyword -> lowerMessage.contains(keyword) }
    }

    // MARK: - Network Interface Monitoring

    /**
     * NAME: startNetworkInterfaceMonitoring
     *
     * DESCRIPTION:
     *     Starts network interface monitoring to detect physical interface changes.
     *     Records current physical interface info and starts monitoring for changes.
     */
    private fun startNetworkInterfaceMonitoring() {
        try {
            // Record current physical interface info before starting monitoring
            recordCurrentPhysicalInterface()

            // Start network monitoring
            networkMonitor.startMonitoring()

            // Set VPN connected status to enable interface change detection
            networkMonitor.setVPNConnected(true)

            logInfo("Network interface monitoring started", mapOf(
                "recorded_interface" to (connectionPhysicalInterface ?: "unknown"),
                "recorded_ip" to (connectionPhysicalIP ?: "unknown")
            ))

        } catch (e: Exception) {
            logError("Failed to start network interface monitoring", mapOf(
                "error" to (e.message ?: "unknown")
            ), e)
        }
    }

    /**
     * NAME: stopNetworkInterfaceMonitoring
     *
     * DESCRIPTION:
     *     Stops network interface monitoring and cleans up resources.
     */
    private fun stopNetworkInterfaceMonitoring() {
        try {
            // Set VPN disconnected status to disable interface change detection
            networkMonitor.setVPNConnected(false)

            // Stop network monitoring
            networkMonitor.stopMonitoring()

            logInfo("Network interface monitoring stopped")

        } catch (e: Exception) {
            logError("Failed to stop network interface monitoring", mapOf(
                "error" to (e.message ?: "unknown")
            ), e)
        }
    }

    /**
     * NAME: recordCurrentPhysicalInterface
     *
     * DESCRIPTION:
     *     Records current physical interface information for comparison during monitoring.
     *     Excludes VPN interfaces to get the actual physical network interface.
     */
    private fun recordCurrentPhysicalInterface() {
        try {
            val physicalInterfaceInfo = getCurrentPhysicalInterface()
            connectionPhysicalInterface = physicalInterfaceInfo.first
            connectionPhysicalIP = physicalInterfaceInfo.second

            logInfo("Physical interface recorded for monitoring", mapOf(
                "interface" to (connectionPhysicalInterface ?: "unknown"),
                "ip" to (connectionPhysicalIP ?: "unknown")
            ))

        } catch (e: Exception) {
            logError("Failed to record physical interface", mapOf(
                "error" to (e.message ?: "unknown")
            ), e)
            connectionPhysicalInterface = "unknown"
            connectionPhysicalIP = "unknown"
        }
    }

    /**
     * NAME: getCurrentPhysicalInterface
     *
     * DESCRIPTION:
     *     Gets current optimal physical interface, excluding VPN interfaces.
     *     Prioritizes WiFi over cellular when both are available.
     *
     * RETURNS:
     *     Pair<String, String> - Interface name and IP address
     */
    private fun getCurrentPhysicalInterface(): Pair<String, String> {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val allNetworks = connectivityManager.allNetworks
            var wifiInterface: Pair<String, String>? = null
            var cellularInterface: Pair<String, String>? = null
            var otherInterface: Pair<String, String>? = null

            for (network in allNetworks) {
                try {
                    val capabilities = connectivityManager.getNetworkCapabilities(network)
                    val linkProperties = connectivityManager.getLinkProperties(network)

                    // Skip if network doesn't have required capabilities
                    if (capabilities == null || linkProperties == null ||
                        !capabilities.hasCapability(NET_CAPABILITY_INTERNET) ||
                        !capabilities.hasCapability(NET_CAPABILITY_VALIDATED)) {
                        continue
                    }

                    val interfaceName = linkProperties.interfaceName ?: continue

                    // Skip VPN interfaces
                    if (interfaceName.startsWith("tun") ||
                        interfaceName.startsWith("ppp") ||
                        interfaceName.startsWith("vpn")) {
                        continue
                    }

                    // Get IP address
                    val ipAddress = linkProperties.linkAddresses
                        ?.firstOrNull { !it.address.isLoopbackAddress && !it.address.isLinkLocalAddress }
                        ?.address?.hostAddress ?: continue

                    val interfaceInfo = Pair(interfaceName, ipAddress)

                    // Categorize by transport type
                    when {
                        capabilities.hasTransport(TRANSPORT_WIFI) -> {
                            wifiInterface = interfaceInfo
                        }
                        capabilities.hasTransport(TRANSPORT_CELLULAR) -> {
                            cellularInterface = interfaceInfo
                        }
                        else -> {
                            if (otherInterface == null) {
                                otherInterface = interfaceInfo
                            }
                        }
                    }
                } catch (e: Exception) {
                    continue
                }
            }

            // Prioritize: WiFi > Cellular > Other
            val selectedInterface = wifiInterface ?: cellularInterface ?: otherInterface

            if (selectedInterface != null) {
                logDebug("Selected physical interface", mapOf(
                    "interface" to selectedInterface.first,
                    "ip" to selectedInterface.second
                ))
                return selectedInterface
            } else {
                logDebug("No physical interface found")
                return Pair("unknown", "unknown")
            }

        } catch (e: Exception) {
            logError("Failed to get current physical interface", mapOf(
                "error" to (e.message ?: "unknown")
            ), e)
            Pair("unknown", "unknown")
        }
    }
}
