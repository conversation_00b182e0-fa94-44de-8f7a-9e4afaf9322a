/**
 * FILE: LoggingManager.kt
 *
 * DESCRIPTION:
 *     Global logging configuration manager for ITforce WAN Android application.
 *     Provides centralized logging setup, file management, performance optimization
 *     with asynchronous writing, and rolling log file cleanup functionality.
 *     Integrates with <PERSON><PERSON> for enhanced logging capabilities.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.infrastructure.logging

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import timber.log.Timber
import java.io.File
import java.io.FileWriter
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * NAME: LoggingManager
 *
 * DESCRIPTION:
 *     Global logging configuration and file management system.
 *     Provides centralized logging setup, asynchronous file writing,
 *     rolling log file management, and performance optimization.
 *     Supports both console and file logging with configurable levels.
 *
 * PROPERTIES:
 *     context - Android application context
 *     logDirectory - Directory for storing log files
 *     maxLogFiles - Maximum number of log files to keep
 *     maxLogFileSize - Maximum size per log file in bytes
 *     isFileLoggingEnabled - Whether file logging is enabled
 *     currentLogFile - Current active log file
 *     logQueue - Queue for asynchronous log writing
 *     fileWriterScope - Coroutine scope for file operations
 */
class LoggingManager private constructor(
    private val context: Context
) {
    
    companion object {
        @Volatile
        private var INSTANCE: LoggingManager? = null
        
        private const val LOG_DIRECTORY_NAME = "logs"
        private const val LOG_FILE_PREFIX = "panabit"
        private const val LOG_FILE_EXTENSION = ".log"
        private const val MAX_LOG_FILES = 5
        private const val MAX_LOG_FILE_SIZE = 10L * 1024 * 1024 // 10MB
        private const val LOG_CLEANUP_INTERVAL = 24 * 60 * 60 * 1000L // 24 hours
        
        /**
         * NAME: getInstance
         *
         * DESCRIPTION:
         *     Get singleton instance of LoggingManager.
         *     Creates new instance if not exists, thread-safe implementation.
         *
         * PARAMETERS:
         *     context - Android application context
         *
         * RETURNS:
         *     LoggingManager - Singleton instance
         */
        fun getInstance(context: Context): LoggingManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: LoggingManager(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        /**
         * NAME: initialize
         *
         * DESCRIPTION:
         *     Initialize logging system with default configuration.
         *     Sets up Timber, creates log directories, and starts file logging.
         *
         * PARAMETERS:
         *     context - Android application context
         *     isDebug - Whether running in debug mode
         *     enableFileLogging - Whether to enable file logging
         */
        fun initialize(context: Context, isDebug: Boolean = true, enableFileLogging: Boolean = true) {
            val manager = getInstance(context)
            manager.setupLogging(isDebug, enableFileLogging)
        }

        /**
         * NAME: cleanup
         *
         * DESCRIPTION:
         *     Clean up singleton instance and all resources.
         *     Should be called when application is terminating.
         */
        fun cleanup() {
            INSTANCE?.let { instance ->
                instance.shutdown()
                synchronized(this) {
                    INSTANCE = null
                }
            }
        }
    }
    
    private val logDirectory: File = File(context.filesDir, LOG_DIRECTORY_NAME)
    private val maxLogFiles: Int = MAX_LOG_FILES
    private val maxLogFileSize: Long = MAX_LOG_FILE_SIZE
    private var isFileLoggingEnabled: Boolean = false
    private var currentLogFile: File? = null

    private val logQueue = ConcurrentLinkedQueue<LogEntry>()
    private val fileWriterScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // Thread-safe date formatters using ThreadLocal
    private val dateFormat = ThreadLocal.withInitial {
        SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())
    }
    private val timestampFormat = ThreadLocal.withInitial {
        SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    }

    private var fileWriterJob: Job? = null
    private var cleanupJob: Job? = null
    
    /**
     * NAME: setupLogging
     *
     * DESCRIPTION:
     *     Setup logging system with specified configuration.
     *     Initializes Timber, creates directories, and starts background tasks.
     *
     * PARAMETERS:
     *     isDebug - Whether running in debug mode
     *     enableFileLogging - Whether to enable file logging
     */
    fun setupLogging(isDebug: Boolean, enableFileLogging: Boolean) {
        // Initialize Timber with Panabit custom trees
        if (isDebug) {
            Timber.plant(PanabitDebugTree())
        } else {
            Timber.plant(PanabitReleaseTree())
        }

        // Setup file logging if enabled
        if (enableFileLogging) {
            setupFileLogging()
        }

        // Plant file logging tree if file logging is enabled
        if (isFileLoggingEnabled) {
            Timber.plant(FileLoggingTree())
        }

        // Start periodic cleanup
        startPeriodicCleanup()
    }
    
    /**
     * NAME: setupFileLogging
     *
     * DESCRIPTION:
     *     Setup file logging system with directory creation and file management.
     *     Creates log directory, initializes current log file, and starts writer.
     */
    private fun setupFileLogging() {
        try {
            // Create log directory if not exists
            if (!logDirectory.exists()) {
                logDirectory.mkdirs()
            }
            
            // Create current log file
            currentLogFile = createNewLogFile()
            isFileLoggingEnabled = true
            
            // Start asynchronous file writer
            startFileWriter()
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to setup file logging")
            isFileLoggingEnabled = false
        }
    }
    
    /**
     * NAME: createNewLogFile
     *
     * DESCRIPTION:
     *     Create new log file with timestamp-based naming.
     *     Ensures unique file names and proper file creation.
     *
     * RETURNS:
     *     File - Created log file
     *
     * THROWS:
     *     IOException - If file creation fails
     */
    private fun createNewLogFile(): File {
        val timestamp = dateFormat.get()?.format(Date()) ?: "unknown"
        val fileName = "${LOG_FILE_PREFIX}_$timestamp$LOG_FILE_EXTENSION"
        val file = File(logDirectory, fileName)
        
        if (!file.exists()) {
            file.createNewFile()
        }
        
        return file
    }
    
    /**
     * NAME: startFileWriter
     *
     * DESCRIPTION:
     *     Start asynchronous file writer coroutine.
     *     Processes log queue and writes entries to file with batching.
     */
    private fun startFileWriter() {
        fileWriterJob?.cancel()
        fileWriterJob = fileWriterScope.launch {
            while (isActive) {
                try {
                    processLogQueue()
                    delay(100) // Process every 100ms
                } catch (e: Exception) {
                    Timber.e(e, "Error in file writer")
                    delay(1000) // Wait longer on error
                }
            }
        }
    }
    
    /**
     * NAME: processLogQueue
     *
     * DESCRIPTION:
     *     Process queued log entries and write to file.
     *     Handles file rotation and batch writing for performance.
     */
    private suspend fun processLogQueue() = withContext(Dispatchers.IO) {
        val currentFile = currentLogFile ?: return@withContext
        val entries = mutableListOf<LogEntry>()
        
        // Collect batch of entries
        while (entries.size < 50 && logQueue.isNotEmpty()) {
            logQueue.poll()?.let { entries.add(it) }
        }
        
        if (entries.isEmpty()) return@withContext
        
        try {
            // Check if file rotation is needed
            if (currentFile.length() > maxLogFileSize) {
                rotateLogFile()
                return@withContext
            }
            
            // Write entries to file with enhanced error handling
            try {
                FileWriter(currentFile, true).use { writer ->
                    entries.forEach { entry ->
                        writer.write(formatLogEntry(entry))
                        writer.write("\n")
                    }
                    writer.flush()
                }
            } catch (e: SecurityException) {
                // File permission issues
                android.util.Log.e("ITforceWAN-LoggingManager", "File permission denied: ${currentFile.absolutePath}", e)
                isFileLoggingEnabled = false // Disable file logging if permissions are denied
            } catch (e: IOException) {
                // I/O issues, but don't disable logging completely
                android.util.Log.w("ITforceWAN-LoggingManager", "I/O error writing to log file", e)
            }
        } catch (e: Exception) {
            // Handle any other unexpected errors
            android.util.Log.e("ITforceWAN-LoggingManager", "Unexpected error in log processing", e)
        }
    }
    
    /**
     * NAME: rotateLogFile
     *
     * DESCRIPTION:
     *     Rotate current log file when size limit is reached.
     *     Creates new log file and triggers cleanup of old files.
     */
    private fun rotateLogFile() {
        try {
            currentLogFile = createNewLogFile()
            cleanupOldLogFiles()
        } catch (e: Exception) {
            Timber.e(e, "Failed to rotate log file")
        }
    }
    
    /**
     * NAME: cleanupOldLogFiles
     *
     * DESCRIPTION:
     *     Clean up old log files to maintain file count limit.
     *     Keeps only the most recent files based on modification time.
     */
    private fun cleanupOldLogFiles() {
        try {
            val logFiles = logDirectory.listFiles { file ->
                file.name.startsWith(LOG_FILE_PREFIX) && file.name.endsWith(LOG_FILE_EXTENSION)
            }?.sortedByDescending { it.lastModified() } ?: return
            
            // Delete excess files
            if (logFiles.size > maxLogFiles) {
                logFiles.drop(maxLogFiles).forEach { file ->
                    if (file.delete()) {
                        Timber.d("Deleted old log file: ${file.name}")
                    }
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to cleanup old log files")
        }
    }
    
    /**
     * NAME: startPeriodicCleanup
     *
     * DESCRIPTION:
     *     Start periodic cleanup task for log file maintenance.
     *     Runs cleanup every 24 hours to manage disk space.
     */
    private fun startPeriodicCleanup() {
        cleanupJob?.cancel()
        cleanupJob = fileWriterScope.launch {
            while (isActive) {
                delay(LOG_CLEANUP_INTERVAL)
                cleanupOldLogFiles()
            }
        }
    }
    
    /**
     * NAME: formatLogEntry
     *
     * DESCRIPTION:
     *     Format log entry for file output with timestamp and metadata.
     *
     * PARAMETERS:
     *     entry - Log entry to format
     *
     * RETURNS:
     *     String - Formatted log entry
     */
    private fun formatLogEntry(entry: LogEntry): String {
        val timestamp = timestampFormat.get()?.format(Date(entry.timestamp)) ?: "unknown"
        val level = entry.level.padEnd(5)
        val tag = entry.tag.padEnd(20)
        
        return "[$timestamp] $level [$tag] ${entry.message}"
    }
    
    /**
     * NAME: addLogEntry
     *
     * DESCRIPTION:
     *     Add log entry to queue for asynchronous file writing.
     *     Called by FileLoggingTree to queue log messages.
     *
     * PARAMETERS:
     *     level - Log level
     *     tag - Log tag
     *     message - Log message
     */
    internal fun addLogEntry(level: String, tag: String, message: String) {
        if (isFileLoggingEnabled) {
            logQueue.offer(LogEntry(System.currentTimeMillis(), level, tag, message))
        }
    }
    
    /**
     * NAME: shutdown
     *
     * DESCRIPTION:
     *     Shutdown logging manager and cleanup resources.
     *     Cancels background tasks and flushes remaining logs safely.
     */
    fun shutdown() {
        try {
            // Cancel background jobs first
            fileWriterJob?.cancel()
            cleanupJob?.cancel()

            // Process remaining logs synchronously to ensure they're written
            runBlocking {
                try {
                    withTimeout(5000) { // 5 second timeout
                        processLogQueue()
                    }
                } catch (e: Exception) {
                    // Log to Android Log directly since our logging might be shutting down
                    android.util.Log.w("ITforceWAN-LoggingManager", "Error flushing logs during shutdown", e)
                }
            }

            // Cancel the scope
            fileWriterScope.cancel()

            // Clear the queue
            logQueue.clear()

            // Mark as disabled
            isFileLoggingEnabled = false

        } catch (e: Exception) {
            android.util.Log.e("PanabitClient-LoggingManager", "Error during shutdown", e)
        }
    }
    

    
    /**
     * NAME: FileLoggingTree
     *
     * DESCRIPTION:
     *     Custom Timber tree for file logging integration.
     *     Forwards log messages to LoggingManager for file writing.
     */
    private inner class FileLoggingTree : Timber.Tree() {
        
        override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
            val level = when (priority) {
                android.util.Log.DEBUG -> "DEBUG"
                android.util.Log.INFO -> "INFO"
                android.util.Log.WARN -> "WARN"
                android.util.Log.ERROR -> "ERROR"
                else -> "INFO"
            }
            
            val finalMessage = if (t != null) {
                "$message\n${android.util.Log.getStackTraceString(t)}"
            } else {
                message
            }
            
            addLogEntry(level, tag ?: "Unknown", finalMessage)
        }
    }
}

/**
 * NAME: LogEntry
 *
 * DESCRIPTION:
 *     Data class representing a log entry for file logging.
 *     Contains all necessary information for log formatting and writing.
 *
 * PROPERTIES:
 *     timestamp - When the log entry was created
 *     level - Log level (DEBUG, INFO, WARN, ERROR)
 *     tag - Log tag for categorization
 *     message - Log message content
 */
data class LogEntry(
    val timestamp: Long,
    val level: String,
    val tag: String,
    val message: String
)


