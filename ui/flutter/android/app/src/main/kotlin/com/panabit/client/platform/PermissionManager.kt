/**
 * FILE: PermissionManager.kt
 *
 * DESCRIPTION:
 *     ITforce WAN Android permission and battery optimization management.
 *     Handles VPN permission requests, battery optimization whitelist management,
 *     and permission status monitoring for stable VPN service operation.
 *     Uses on-demand permission checking pattern consistent with iOS implementation.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.platform

import android.app.Activity
import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.net.VpnService
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import androidx.annotation.RequiresApi
import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logError
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

/**
 * NAME: VPNPermissionStatus
 *
 * DESCRIPTION:
 *     VPN permission status enumeration consistent with iOS implementation.
 *     Provides cross-platform compatible permission status representation.
 */
enum class VPNPermissionStatus(val value: String) {
    NOT_DETERMINED("not_determined"),
    DENIED("denied"),
    AUTHORIZED("authorized"),
    RESTRICTED("restricted")
}

/**
 * NAME: VPNPermissionResult
 *
 * DESCRIPTION:
 *     VPN permission request result with user-friendly messages.
 *     Provides comprehensive permission status information for UI display.
 *
 * PROPERTIES:
 *     granted - Whether permission was granted
 *     status - Current permission status
 *     userMessage - User-friendly message for UI display
 *     error - Optional error information
 */
data class VPNPermissionResult(
    val granted: Boolean,
    val status: VPNPermissionStatus,
    val userMessage: String,
    val error: String? = null
)

/**
 * NAME: PermissionManager
 *
 * DESCRIPTION:
 *     VPN permission and battery optimization management for Android platform.
 *     Provides comprehensive permission handling including VPN authorization,
 *     battery optimization whitelist management, and permission status checking.
 *     Uses on-demand checking pattern consistent with iOS implementation.
 *
 * PROPERTIES:
 *     context - Application context for system service access
 *     powerManager - PowerManager for battery optimization control
 *     packageName - Application package name for permission operations
 */
class PermissionManager(private val context: Context) {

    private val powerManager: PowerManager by lazy {
        context.getSystemService(Context.POWER_SERVICE) as PowerManager
    }
    
    private val packageName: String = context.packageName

    companion object {
        private const val VPN_PERMISSION_REQUEST_CODE = 1001
        private const val BATTERY_OPTIMIZATION_REQUEST_CODE = 1002
        
        // Permission result callback storage
        private var vpnPermissionCallback: ((Boolean) -> Unit)? = null
    }

    /**
     * NAME: requestVPNPermission
     *
     * DESCRIPTION:
     *     Requests VPN permission from the user with proper error handling.
     *     Uses Android VpnService.prepare() to check and request VPN authorization.
     *     Provides user-friendly guidance for permission approval process.
     *
     * PARAMETERS:
     *     activity - Activity context for permission request UI
     *
     * RETURNS:
     *     Result<VPNPermissionResult> - Permission request result with status
     *
     * THROWS:
     *     SecurityException - If VPN permission is restricted by system policy
     */
    suspend fun requestVPNPermission(activity: Activity): Result<VPNPermissionResult> {
        return try {
            logInfo("Starting VPN permission request")
            
            val intent = VpnService.prepare(context)
            if (intent != null) {
                logDebug("VPN permission required, showing system dialog")
                
                // Need user authorization
                val granted = suspendCancellableCoroutine<Boolean> { continuation ->
                    vpnPermissionCallback = { result ->
                        logInfo("VPN permission result received: $result")
                        continuation.resume(result)
                        vpnPermissionCallback = null
                    }
                    
                    try {
                        activity.startActivityForResult(intent, VPN_PERMISSION_REQUEST_CODE)
                    } catch (e: Exception) {
                        logError("Failed to start VPN permission activity", e)
                        continuation.resume(false)
                        vpnPermissionCallback = null
                    }
                }
                
                val result = if (granted) {
                    VPNPermissionResult(
                        granted = true,
                        status = VPNPermissionStatus.AUTHORIZED,
                        userMessage = "VPN权限已成功授权"
                    )
                } else {
                    VPNPermissionResult(
                        granted = false,
                        status = VPNPermissionStatus.DENIED,
                        userMessage = "VPN权限被拒绝，无法建立VPN连接"
                    )
                }
                
                Result.success(result)
            } else {
                // Already have permission
                logInfo("VPN permission already granted")
                val result = VPNPermissionResult(
                    granted = true,
                    status = VPNPermissionStatus.AUTHORIZED,
                    userMessage = "VPN权限已授权"
                )
                Result.success(result)
            }
        } catch (e: SecurityException) {
            logError("VPN permission restricted by system policy", e)
            val result = VPNPermissionResult(
                granted = false,
                status = VPNPermissionStatus.RESTRICTED,
                userMessage = "VPN功能被系统限制，请检查设备管理策略",
                error = e.message
            )
            Result.success(result)
        } catch (e: Exception) {
            logError("Unexpected error during VPN permission request", e)
            Result.failure(e)
        }
    }

    /**
     * NAME: checkVPNPermissionStatus
     *
     * DESCRIPTION:
     *     Checks current VPN permission status with user-friendly messages.
     *     Uses on-demand checking pattern consistent with iOS implementation.
     *     Does not request permission, only checks current status.
     *
     * RETURNS:
     *     VPNPermissionResult - Current permission status with user message
     */
    fun checkVPNPermissionStatus(): VPNPermissionResult {
        return try {
            val intent = VpnService.prepare(context)
            val status = if (intent == null) {
                VPNPermissionStatus.AUTHORIZED
            } else {
                VPNPermissionStatus.NOT_DETERMINED
            }
            
            val result = when (status) {
                VPNPermissionStatus.AUTHORIZED -> VPNPermissionResult(
                    granted = true,
                    status = status,
                    userMessage = "VPN权限已授权"
                )
                VPNPermissionStatus.NOT_DETERMINED -> VPNPermissionResult(
                    granted = false,
                    status = status,
                    userMessage = "需要请求VPN权限才能建立连接"
                )
                else -> VPNPermissionResult(
                    granted = false,
                    status = status,
                    userMessage = "VPN权限状态未知"
                )
            }
            
            logDebug("VPN permission status checked: ${status.value}")
            result
        } catch (e: SecurityException) {
            logError("VPN permission restricted by system policy", e)
            VPNPermissionResult(
                granted = false,
                status = VPNPermissionStatus.RESTRICTED,
                userMessage = "VPN功能被系统限制，请检查设备管理策略",
                error = e.message
            )
        } catch (e: Exception) {
            logError("Error checking VPN permission status", e)
            VPNPermissionResult(
                granted = false,
                status = VPNPermissionStatus.DENIED,
                userMessage = "检查VPN权限状态时发生错误",
                error = e.message
            )
        }
    }



    /**
     * NAME: requestBatteryOptimizationExemption
     *
     * DESCRIPTION:
     *     Requests battery optimization exemption to ensure VPN service stability.
     *     Handles Android 6.0+ Doze mode restrictions by guiding user to whitelist app.
     *     Provides fallback dialog for devices that don't support direct intent.
     *
     * PARAMETERS:
     *     activity - Activity context for launching system settings
     */
    fun requestBatteryOptimizationExemption(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            logInfo("Requesting battery optimization exemption")
            
            if (!powerManager.isIgnoringBatteryOptimizations(packageName)) {
                logDebug("App not in battery optimization whitelist, requesting exemption")
                
                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                    data = Uri.parse("package:$packageName")
                }
                
                try {
                    activity.startActivityForResult(intent, BATTERY_OPTIMIZATION_REQUEST_CODE)
                    logInfo("Battery optimization exemption request launched")
                } catch (e: Exception) {
                    logWarn("Direct battery optimization intent not supported, showing dialog", e)
                    showBatteryOptimizationDialog(activity)
                }
            } else {
                logInfo("App already in battery optimization whitelist")
            }
        } else {
            logDebug("Battery optimization not applicable for Android version < 6.0")
        }
    }

    /**
     * NAME: isBatteryOptimizationIgnored
     *
     * DESCRIPTION:
     *     Checks if app is in battery optimization whitelist.
     *     Returns true for Android versions below 6.0 (no battery optimization).
     *
     * RETURNS:
     *     Boolean - true if app is exempt from battery optimization, false otherwise
     */
    fun isBatteryOptimizationIgnored(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val isIgnored = powerManager.isIgnoringBatteryOptimizations(packageName)
            logDebug("Battery optimization status: ignored=$isIgnored")
            isIgnored
        } else {
            logDebug("Battery optimization not applicable for Android version < 6.0")
            true
        }
    }

    /**
     * NAME: showBatteryOptimizationDialog
     *
     * DESCRIPTION:
     *     Shows dialog to guide user to battery optimization settings
     *     for ensuring VPN service stability. Used as fallback when
     *     direct intent is not supported by the device.
     *
     * PARAMETERS:
     *     activity - Activity context for dialog display
     */
    private fun showBatteryOptimizationDialog(activity: Activity) {
        AlertDialog.Builder(activity)
            .setTitle("电池优化设置")
            .setMessage("为确保VPN连接稳定，请在电池优化设置中将本应用加入白名单")
            .setPositiveButton("去设置") { _, _ ->
                try {
                    val intent = Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
                    activity.startActivity(intent)
                    logInfo("Battery optimization settings opened")
                } catch (e: Exception) {
                    logWarn("Failed to open battery optimization settings", e)
                }
            }
            .setNegativeButton("取消") { dialog, _ ->
                dialog.dismiss()
                logDebug("Battery optimization dialog cancelled by user")
            }
            .show()
    }

    /**
     * NAME: handleActivityResult
     *
     * DESCRIPTION:
     *     Handles activity results for permission requests.
     *     Should be called from Activity.onActivityResult().
     *
     * PARAMETERS:
     *     requestCode - Request code from activity result
     *     resultCode - Result code from activity result
     */
    fun handleActivityResult(requestCode: Int, resultCode: Int) {
        when (requestCode) {
            VPN_PERMISSION_REQUEST_CODE -> {
                val granted = resultCode == Activity.RESULT_OK
                logInfo("VPN permission activity result: granted=$granted")
                vpnPermissionCallback?.invoke(granted)
            }
            BATTERY_OPTIMIZATION_REQUEST_CODE -> {
                val ignored = isBatteryOptimizationIgnored()
                logInfo("Battery optimization activity result: ignored=$ignored")
                // No callback needed for battery optimization
            }
        }
    }
}
