/**
 * FILE: ServerManagerError.kt
 *
 * DESCRIPTION:
 *     Server manager specific error definitions for Android VPN implementation.
 *     Provides detailed error information for server operations.
 *     Compatible with iOS ServerManagerError and Go backend error handling.
 *
 * AUTHOR: wei
 * HISTORY: 11/07/2025 create
 */

package com.panabit.client.connection.models

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

/**
 * NAME: ServerManagerError
 *
 * DESCRIPTION:
 *     Sealed class for server manager specific errors.
 *     Provides detailed error information for server operations.
 *     Compatible with iOS ServerManagerError enum and Go backend error types.
 *
 * ERROR TYPES:
 *     NoServersAvailable - No servers available for selection
 *     ServerNotFound - Specified server not found in server list
 *     PingFailed - Ping operation failed for specified reason
 *     UpdateFailed - Server list update operation failed
 *     ConfigurationInvalid - Invalid configuration parameters
 *     NetworkUnavailable - Network is not available for operations
 *     TimeoutError - Operation timed out
 *     ConcurrencyError - Concurrent operation conflict
 */
sealed class ServerManagerError : Exception() {
    
    /**
     * NAME: NoServersAvailable
     *
     * DESCRIPTION:
     *     Error when no servers are available for selection.
     *     Occurs when server list is empty or all servers are offline.
     */
    object NoServersAvailable : ServerManagerError() {
        override val message: String = "No servers available for selection"
    }
    
    /**
     * NAME: ServerNotFound
     *
     * DESCRIPTION:
     *     Error when specified server is not found in server list.
     *
     * PROPERTIES:
     *     serverId - ID of the server that was not found
     */
    data class ServerNotFound(val serverId: String) : ServerManagerError() {
        override val message: String = "Server not found: $serverId"
    }
    
    /**
     * NAME: PingFailed
     *
     * DESCRIPTION:
     *     Error when ping operation fails.
     *
     * PROPERTIES:
     *     reason - Detailed reason for ping failure
     */
    data class PingFailed(val reason: String) : ServerManagerError() {
        override val message: String = "Ping operation failed: $reason"
    }
    
    /**
     * NAME: UpdateFailed
     *
     * DESCRIPTION:
     *     Error when server list update operation fails.
     *
     * PROPERTIES:
     *     reason - Detailed reason for update failure
     */
    data class UpdateFailed(val reason: String) : ServerManagerError() {
        override val message: String = "Server list update failed: $reason"
    }
    
    /**
     * NAME: ConfigurationInvalid
     *
     * DESCRIPTION:
     *     Error when configuration parameters are invalid.
     *
     * PROPERTIES:
     *     reason - Detailed reason for configuration invalidity
     */
    data class ConfigurationInvalid(val reason: String) : ServerManagerError() {
        override val message: String = "Invalid configuration: $reason"
    }
    
    /**
     * NAME: NetworkUnavailable
     *
     * DESCRIPTION:
     *     Error when network is not available for operations.
     */
    object NetworkUnavailable : ServerManagerError() {
        override val message: String = "Network is not available"
    }
    
    /**
     * NAME: TimeoutError
     *
     * DESCRIPTION:
     *     Error when operation times out.
     *
     * PROPERTIES:
     *     operation - Name of the operation that timed out
     *     timeoutMs - Timeout value in milliseconds
     */
    data class TimeoutError(val operation: String, val timeoutMs: Long) : ServerManagerError() {
        override val message: String = "Operation '$operation' timed out after ${timeoutMs}ms"
    }
    
    /**
     * NAME: ConcurrencyError
     *
     * DESCRIPTION:
     *     Error when concurrent operation conflict occurs.
     *
     * PROPERTIES:
     *     reason - Detailed reason for concurrency conflict
     */
    data class ConcurrencyError(val reason: String) : ServerManagerError() {
        override val message: String = "Concurrency error: $reason"
    }
    
    /**
     * NAME: getErrorCode
     *
     * DESCRIPTION:
     *     Gets error code for logging and debugging purposes.
     *
     * RETURNS:
     *     String - Error code identifier
     */
    fun getErrorCode(): String = when (this) {
        is NoServersAvailable -> "SM_NO_SERVERS"
        is ServerNotFound -> "SM_SERVER_NOT_FOUND"
        is PingFailed -> "SM_PING_FAILED"
        is UpdateFailed -> "SM_UPDATE_FAILED"
        is ConfigurationInvalid -> "SM_CONFIG_INVALID"
        is NetworkUnavailable -> "SM_NETWORK_UNAVAILABLE"
        is TimeoutError -> "SM_TIMEOUT"
        is ConcurrencyError -> "SM_CONCURRENCY"
    }
    
    /**
     * NAME: toMap
     *
     * DESCRIPTION:
     *     Converts error to map for Flutter communication.
     *
     * RETURNS:
     *     Map<String, Any> - Error information as map
     */
    fun toMap(): Map<String, Any> = mapOf(
        "code" to getErrorCode(),
        "message" to (message ?: "Unknown error"),
        "type" to this::class.simpleName.orEmpty()
    )
}
