/**
 * FILE: HeartbeatManager.kt
 *
 * DESCRIPTION:
 *     Heartbeat management for SDWAN protocol.
 *     Handles echo request/response packets and connection monitoring.
 *     Implements timeout detection based on response times.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.protocol

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import com.panabit.client.protocol.models.*
import kotlinx.coroutines.*
import java.net.DatagramSocket
import java.net.DatagramPacket
import java.net.InetAddress
import java.nio.ByteBuffer
import java.util.concurrent.atomic.AtomicLong
import java.util.concurrent.atomic.AtomicInteger
import java.nio.ByteOrder

/**
 * NAME: HeartbeatTimeoutCallback
 *
 * DESCRIPTION:
 *     Callback interface for heartbeat timeout notifications.
 *     Allows ConnectionManager to handle timeout events.
 *     Simplified to match iOS implementation.
 */
interface HeartbeatTimeoutCallback {
    suspend fun onHeartbeatTimeout(reason: String)
}

/**
 * NAME: HeartbeatManager
 *
 * DESCRIPTION:
 *     Manages heartbeat protocol for SDWAN connections.
 *     Sends echo requests and processes echo responses.
 *     Monitors connection health and detects timeouts.
 *
 * PROPERTIES:
 *     socket - UDP socket for communication
 *     serverAddress - Target server address
 *     serverPort - Target server port
 *     stateMachine - Protocol state machine
 *     encryptionService - Encryption service for packets
 *     isRunning - Whether heartbeat is currently running
 *     lastResponseTime - Timestamp of last echo response
 *     averageDelay - Running average of response delays
 */
class HeartbeatManager(
    private val socket: DatagramSocket,
    private val serverAddress: String,
    private val serverPort: Int,
    private val stateMachine: ProtocolStateMachine,
    private val encryptionService: EncryptionService,
    private val timeoutCallback: HeartbeatTimeoutCallback? = null
) {
    private var heartbeatJob: Job? = null
    @Volatile private var isRunning = false
    @Volatile private var lastSendTime = 0L
    @Volatile private var lastReceiveTime = 0L  // 新增：记录最后收到心跳响应的时间
    @Volatile private var averageDelay = AtomicLong(0)
    private val missedHeartbeats = AtomicInteger(0)
    private val responseCount = AtomicLong(0)
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    companion object {
        // 与Go后端保持一致的心跳间隔
        private const val HEARTBEAT_INTERVAL_MS = 15000L // 15 seconds
        // 新增：60秒超时阈值 - 当超过60秒未收到心跳包时触发重连
        private const val HEARTBEAT_TIMEOUT_MS = 60000L // 60 seconds
        // 与iOS版本保持一致的超时阈值（保留作为备用机制）
        private const val MISSED_HEARTBEAT_THRESHOLD = 3 // 连续3次心跳失败才触发重连
        private const val DELAY_SMOOTHING_FACTOR = 0.8   // For running average calculation
    }

    /**
     * NAME: start
     *
     * DESCRIPTION:
     *     Starts heartbeat monitoring.
     *     Sends periodic echo requests and monitors responses.
     *     Uses iOS-style missed heartbeat counting for reliable timeout detection.
     */
    fun start() {
        if (isRunning) {
            logWarn("HeartbeatManager already running - ignoring start request")
            return
        }

        // 获取session信息进行验证
        val (sessionID, token) = stateMachine.getSessionInfo()

        logInfo("Starting HeartbeatManager with detailed configuration", mapOf(
            "interval_ms" to HEARTBEAT_INTERVAL_MS,
            "timeout_ms" to HEARTBEAT_TIMEOUT_MS,
            "threshold" to MISSED_HEARTBEAT_THRESHOLD,
            "state" to stateMachine.getState().name,
            "session_id" to sessionID,
            "token" to token,
            "server_address" to serverAddress,
            "server_port" to serverPort,
            "socket_local_port" to socket.localPort,
            "socket_connected" to (!socket.isClosed),
            "encryption_method" to encryptionService.getMethod().name
        ))

        isRunning = true
        resetHeartbeatMissedCount()

        // 初始化最后接收时间为当前时间
        lastReceiveTime = System.currentTimeMillis()

        heartbeatJob = scope.launch {
            logInfo("HeartbeatManager coroutine started - beginning heartbeat loop")

            while (isRunning) {
                try {
                    // Send echo request
                    val requestTime = System.currentTimeMillis()
                    sendEchoRequest(requestTime)

                    // iOS-style: 发送后增加missed count，但不立即检查超时
                    val currentMissedCount = missedHeartbeats.incrementAndGet()

                    logInfo("Heartbeat sent - missed count updated", mapOf(
                        "missed_count_after_send" to currentMissedCount,
                        "request_time" to requestTime,
                        "interval_ms" to HEARTBEAT_INTERVAL_MS,
                        "threshold" to MISSED_HEARTBEAT_THRESHOLD,
                        "state" to stateMachine.getState().name,
                        "note" to "Will check timeout after interval delay"
                    ))

                    // Wait for heartbeat interval before next iteration
                    delay(HEARTBEAT_INTERVAL_MS)

                    // 新增：检查60秒超时机制（优先级更高）
                    val currentTime = System.currentTimeMillis()
                    val timeSinceLastReceive = currentTime - lastReceiveTime
                    if (timeSinceLastReceive > HEARTBEAT_TIMEOUT_MS) {
                        logWarn("Heartbeat timeout detected (60s) - triggering reconnection", mapOf(
                            "time_since_last_receive_ms" to timeSinceLastReceive,
                            "timeout_threshold_ms" to HEARTBEAT_TIMEOUT_MS,
                            "last_receive_time" to lastReceiveTime,
                            "current_time" to currentTime,
                            "note" to "60-second timeout mechanism - higher priority than missed count"
                        ))
                        onHeartbeatTimeout()
                        break
                    }

                    // Check for heartbeat timeout AFTER the delay (iOS-style logic)
                    // This gives time for the response to be received and processed
                    val finalMissedCount = missedHeartbeats.get()
                    if (finalMissedCount >= MISSED_HEARTBEAT_THRESHOLD) {
                        logWarn("Too many missed heartbeats - triggering reconnection", mapOf(
                            "missed_count" to finalMissedCount,
                            "max_threshold" to MISSED_HEARTBEAT_THRESHOLD,
                            "interval_ms" to HEARTBEAT_INTERVAL_MS,
                            "time_since_last_receive_ms" to timeSinceLastReceive,
                            "note" to "iOS-style missed heartbeat detection after delay"
                        ))
                        onHeartbeatTimeout()
                        break
                    }

                } catch (e: Exception) {
                    // Heartbeat error
                    logError("Heartbeat loop error", mapOf(
                        "error" to (e.message ?: "unknown"),
                        "error_type" to e.javaClass.simpleName
                    ), e)
                    onHeartbeatError(e)
                    break
                }
            }

            logInfo("HeartbeatManager coroutine ended")
        }
    }

    /**
     * NAME: stop
     *
     * DESCRIPTION:
     *     Stops heartbeat monitoring and resets counters.
     *     Ensures clean state for next connection.
     */
    fun stop() {
        if (!isRunning) {
            logDebug("HeartbeatManager already stopped")
            return
        }

        logInfo("Stopping HeartbeatManager", mapOf(
            "missed_heartbeats_before_stop" to missedHeartbeats.get(),
            "response_count_before_stop" to responseCount.get(),
            "average_delay_before_stop" to averageDelay.get()
        ))

        isRunning = false
        heartbeatJob?.cancel()
        heartbeatJob = null

        // 🔧 FIX: Reset counters on stop to ensure clean state for next connection
        resetHeartbeatMissedCount()
        responseCount.set(0)
        averageDelay.set(0L)
        lastSendTime = 0L
        lastReceiveTime = 0L  // 新增：重置最后接收时间

        logInfo("HeartbeatManager stopped and counters reset")
    }

    /**
     * NAME: processEchoRequest
     *
     * DESCRIPTION:
     *     Processes received echo request packet from server.
     *     Generates and sends echo response back to server.
     *     Extracts payload for proper response formatting.
     *
     * PARAMETERS:
     *     packet - Received echo request packet
     *
     * RETURNS:
     *     Boolean - true if request processed successfully, false otherwise
     */
    fun processEchoRequest(packet: SDWANPacket): Boolean {
        if (packet.header.type != PacketType.ECHO_REQUEST) {
            return false
        }

        try {
            // Extract signature and payload for response
            val (signature, payload) = PacketSignature.extractSignature(packet.toByteArray(), packet.header)

            // Verify signature if present
            if (signature != null && !PacketSignature.verifySignature(packet.header, signature)) {
                return false
            }

            // Send echo response back to server with original payload
            sendEchoResponse(payload)

            // logDebug("Processed echo request from server", mapOf(
            //     "session_id" to packet.header.sessionID,
            //     "token" to packet.header.token
            // ))
            return true
        } catch (e: Exception) {
            logError("Failed to process echo request", mapOf(
                "error" to (e.message ?: "unknown")
            ), e)
            return false
        }
    }

    /**
     * NAME: processEchoResponse
     *
     * DESCRIPTION:
     *     Unified heartbeat response processing method.
     *     Handles echo response packets from unified UDP receiver.
     *     Resets missed heartbeat counter and updates basic statistics.
     *
     * PARAMETERS:
     *     packet - Received echo response packet
     *
     * RETURNS:
     *     Boolean - true if response processed successfully, false otherwise
     */
    fun processEchoResponse(packet: SDWANPacket): Boolean {
        try {
            // Parse ECHO_RESPONSE payload to extract timing information
            val payload = packet.data
            logInfo("Processing ECHO_RESPONSE payload", mapOf(
                "payload_size" to payload.size,
                "expected_format" to "MD5(16) + Timestamp(8) + Delays(12) + SDRT(4)",
                "session_id" to packet.header.sessionID,
                "token" to packet.header.token
            ))

            // iOS-style simple processing: Update state machine heartbeat timestamp
            stateMachine.updateHeartbeat()

            // Calculate round-trip delay using lastSendTime (simplified like iOS)
            val currentTime = System.currentTimeMillis()
            val delay = if (lastSendTime > 0) {
                currentTime - lastSendTime
            } else {
                50L // Default latency like iOS (50ms)
            }

            // 记录收到响应前的missed count
            val previousMissedCount = missedHeartbeats.get()

            // 新增：更新最后接收时间（用于60秒超时检测）
            lastReceiveTime = System.currentTimeMillis()

            // Update delay statistics
            updateAverageDelay(delay)
            responseCount.incrementAndGet()

            // Reset missed heartbeat counter (iOS-style) - 这是关键步骤
            resetHeartbeatMissedCount()

            logInfo("Heartbeat response processed successfully", mapOf(
                "session_id" to packet.header.sessionID,
                "token" to packet.header.token,
                "delay_ms" to delay,
                "average_delay_ms" to averageDelay.get(),
                "previous_missed_count" to previousMissedCount,
                "missed_count_after_reset" to missedHeartbeats.get(),
                "response_count" to responseCount.get(),
                "manager_running" to isRunning,
                "payload_size" to payload.size
            ))

            return true
        } catch (e: Exception) {
            logError("Failed to process heartbeat response", mapOf(
                "error" to (e.message ?: "unknown"),
                "packet_type" to packet.header.type.name,
                "session_id" to packet.header.sessionID,
                "token" to packet.header.token
            ), e)
            return false
        }
    }

    /**
     * NAME: getAverageDelay
     *
     * DESCRIPTION:
     *     Gets the average response delay.
     *
     * RETURNS:
     *     Long - Average delay in milliseconds
     */
    fun getAverageDelay(): Long = averageDelay.get()

    /**
     * NAME: isHealthy
     *
     * DESCRIPTION:
     *     Checks if connection is healthy based on both missed heartbeat count and 60-second timeout.
     *     Uses iOS-style missed heartbeat counting for consistency, with additional timeout check.
     *
     * RETURNS:
     *     Boolean - true if connection is healthy, false otherwise
     */
    fun isHealthy(): Boolean {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastReceive = currentTime - lastReceiveTime

        // 检查60秒超时（优先级更高）
        if (timeSinceLastReceive > HEARTBEAT_TIMEOUT_MS) {
            return false
        }

        // 检查连续失败次数
        return missedHeartbeats.get() < MISSED_HEARTBEAT_THRESHOLD
    }

    /**
     * NAME: resetHeartbeatMissedCount
     *
     * DESCRIPTION:
     *     Resets the missed heartbeat counter.
     *     Called when heartbeat response is received or connection is established.
     */
    private fun resetHeartbeatMissedCount() {
        val previousCount = missedHeartbeats.getAndSet(0)
        // logInfo("Heartbeat missed count reset", mapOf(
        //     "previous_missed_count" to previousCount,
        //     "new_missed_count" to 0,
        //     "max_missed_threshold" to MISSED_HEARTBEAT_THRESHOLD,
        //     "reset_timestamp" to System.currentTimeMillis()
        // ))
    }

    private suspend fun sendEchoRequest(requestTime: Long) {
        val (sessionID, token) = stateMachine.getSessionInfo()

        // Create ECHO_REQUEST packet header
        val header = PacketHeader(
            type = PacketType.ECHO_REQUEST,
            encrypt = encryptionService.getMethod(),
            sessionID = sessionID,
            token = token
        )

        // Build echo request payload
        val payload = buildEchoRequestPayload(requestTime)

        // Build packet with signature
        val packetData = PacketSignature.buildSignedPacket(header, payload)

        try {
            // 🔍 DEBUG: Log detailed packet information
            val packetHex = packetData.joinToString(" ") { "%02x".format(it) }
            logInfo("ECHO_REQUEST packet details", mapOf(
                "header_type" to "0x${header.type.value.toString(16)}",
                "header_encrypt" to "0x${header.encrypt.value.toString(16)}",
                "header_session_id" to "0x${header.sessionID.toString(16)}",
                "header_token" to "0x${header.token.toString(16)}",
                "payload_size" to payload.size,
                "total_packet_size" to packetData.size,
                "first_16_bytes" to packetData.take(16).joinToString(" ") { "%02x".format(it) }
            ))

            // Send packet
            val address = InetAddress.getByName(serverAddress)
            val packet = DatagramPacket(packetData, packetData.size, address, serverPort)
            socket.send(packet)

            // 更新发送时间，用于计算响应延迟
            lastSendTime = requestTime

            logInfo("ECHO_REQUEST sent successfully", mapOf(
                "server_address" to serverAddress,
                "server_port" to serverPort,
                "session_id" to sessionID,
                "token" to token,
                "packet_size" to packetData.size,
                "send_time" to requestTime,
                "socket_local_port" to socket.localPort,
                "socket_connected" to (!socket.isClosed),
                "socket_bound" to (socket.localPort > 0)
            ))
        } catch (e: Exception) {
            logError("Failed to send ECHO_REQUEST", mapOf(
                "error" to (e.message ?: "unknown"),
                "error_type" to e.javaClass.simpleName,
                "server_address" to serverAddress,
                "server_port" to serverPort,
                "session_id" to sessionID,
                "token" to token
            ), e)
            throw e // 重新抛出异常以便上层处理
        }
    }

    private fun buildEchoRequestPayload(requestTime: Long): ByteArray {
        // Go backend expects: Timestamp(8) + CurDelay(4) + MinDelay(4) + MaxDelay(4) + SDRT(4) = 24 bytes
        val buffer = ByteBuffer.allocate(24).order(ByteOrder.BIG_ENDIAN)

        // Add timestamp in microseconds (8 bytes) - matching Go backend format exactly
        val timestampMicros = requestTime * 1000
        buffer.putLong(timestampMicros)

        // Add current delay (4 bytes) - current average delay
        buffer.putInt(averageDelay.get().toInt())

        // Add min delay (4 bytes) - simplified to 0 for now
        buffer.putInt(0)

        // Add max delay (4 bytes) - simplified to average delay
        buffer.putInt(averageDelay.get().toInt())

        // Add SDRT tag (4 bytes) - "SDRT" as bytes
        buffer.put('S'.code.toByte())
        buffer.put('D'.code.toByte())
        buffer.put('R'.code.toByte())
        buffer.put('T'.code.toByte())

        return buffer.array()
    }



    private fun onHeartbeatTimeout() {
        stateMachine.setState(ProtocolState.CLOSED, "Heartbeat timeout")
        stop()

        // Trigger reconnection through callback if available
        timeoutCallback?.let { callback ->
            scope.launch {
                try {
                    callback.onHeartbeatTimeout("Heartbeat timeout detected")
                } catch (e: Exception) {
                    // Log callback error but don't propagate
                    // Heartbeat timeout handling should not fail due to callback issues
                }
            }
        }
    }

    private fun onHeartbeatError(error: Exception) {
        stateMachine.setState(ProtocolState.CLOSED, "Heartbeat error: ${error.message}")
        stop()

        // Trigger reconnection through callback if available
        timeoutCallback?.let { callback ->
            scope.launch {
                try {
                    callback.onHeartbeatTimeout("Heartbeat error: ${error.message}")
                } catch (e: Exception) {
                    // Log callback error but don't propagate
                    // Heartbeat error handling should not fail due to callback issues
                }
            }
        }
    }

    /**
     * NAME: cleanup
     *
     * DESCRIPTION:
     *     Cleans up heartbeat manager resources.
     */
    fun cleanup() {
        stop()
        scope.cancel()
    }

    /**
     * NAME: sendEchoResponse
     *
     * DESCRIPTION:
     *     Sends echo response packet back to server.
     *     Uses original payload format for proper response.
     *
     * PARAMETERS:
     *     originalPayload - Original echo request payload to echo back
     */
    private fun sendEchoResponse(originalPayload: ByteArray) {
        try {
            val (sessionID, token) = stateMachine.getSessionInfo()

            // Create echo response header
            val header = PacketHeader(
                type = PacketType.ECHO_RESPONSE,
                encrypt = EncryptionMethod.NONE,
                sessionID = sessionID,
                token = token
            )

            // Build echo response payload with original format
            val responsePayload = buildEchoResponsePayload(originalPayload)

            // Build complete packet with signature
            val packetData = PacketSignature.buildSignedPacket(header, responsePayload)

            // Send packet
            val address = InetAddress.getByName(serverAddress)
            val packet = DatagramPacket(packetData, packetData.size, address, serverPort)
            socket.send(packet)

            // logDebug("Sent echo response to server")
        } catch (e: Exception) {
            logError("Failed to send echo response", mapOf(
                "error" to (e.message ?: "unknown")
            ), e)
        }
    }

    /**
     * NAME: buildEchoResponsePayload
     *
     * DESCRIPTION:
     *     Builds echo response payload with timing information.
     *     Includes original timestamp and response delay.
     *
     * PARAMETERS:
     *     originalPayload - Original echo request payload
     *
     * RETURNS:
     *     ByteArray - Echo response payload
     */
    private fun buildEchoResponsePayload(originalPayload: ByteArray): ByteArray {
        val responseTime = System.currentTimeMillis()
        val buffer = ByteBuffer.allocate(24).order(ByteOrder.BIG_ENDIAN)

        // Echo back original timestamp if available
        if (originalPayload.size >= 8) {
            buffer.put(originalPayload, 0, 8)
        } else {
            // Use current time if original timestamp not available
            buffer.putLong(responseTime * 1000)
        }

        // Add response delay (4 bytes) - time to process request
        val delay = (System.currentTimeMillis() - responseTime).toInt()
        buffer.putInt(delay)

        // Add SDRT tag (4 bytes) - simple sequence number
        buffer.putInt(responseCount.get().toInt())

        // Add padding (8 bytes) - reserved for future use
        buffer.putLong(0L)

        return buffer.array()
    }

    /**
     * NAME: updateAverageDelay
     *
     * DESCRIPTION:
     *     更新平均延迟统计
     *
     * PARAMETERS:
     *     delay - 当前延迟时间（毫秒）
     */
    private fun updateAverageDelay(delay: Long) {
        val currentAverage = averageDelay.get()
        val newAverage = if (currentAverage == 0L) {
            delay
        } else {
            (currentAverage * DELAY_SMOOTHING_FACTOR + delay * (1 - DELAY_SMOOTHING_FACTOR)).toLong()
        }
        averageDelay.set(newAverage)
    }


}
