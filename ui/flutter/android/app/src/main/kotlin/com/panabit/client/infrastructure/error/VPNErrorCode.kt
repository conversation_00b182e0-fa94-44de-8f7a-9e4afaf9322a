/**
 * FILE: VPNErrorCode.kt
 *
 * DESCRIPTION:
 *     统一的VPN错误代码枚举，与Windows版本保持完全一致
 *     使用数字代码范围：1000-6999，支持错误分类和严重程度
 *
 * AUTHOR: wei
 * HISTORY: 24/07/2025 create - 立即实施Android错误处理改进
 */

package com.panabit.client.infrastructure.error

/**
 * NAME: ErrorSeverity
 *
 * DESCRIPTION:
 *     错误严重程度枚举
 */
enum class ErrorSeverity {
    LOW,        // 低级错误，不影响主要功能
    MEDIUM,     // 中级错误，影响部分功能
    HIGH,       // 高级错误，影响主要功能
    CRITICAL    // 严重错误，导致服务不可用
}

/**
 * NAME: VPNErrorCode
 *
 * DESCRIPTION:
 *     统一的VPN错误代码枚举，与Windows版本保持完全一致
 *     错误代码范围：
 *     - 0-999: 通用错误
 *     - 1000-1999: 网络错误
 *     - 2000-2999: 认证错误
 *     - 3000-3999: 隧道错误
 *     - 4000-4999: 配置错误
 *     - 5000-5999: 平台错误
 *     - 6000-6999: 协议错误
 */
enum class VPNErrorCode(
    val code: Int,
    val category: String,
    val severity: ErrorSeverity,
    val description: String
) {
    // General errors (0-999)
    UNKNOWN(0, "General", ErrorSeverity.LOW, "Unknown error"),
    INTERNAL(1, "General", ErrorSeverity.HIGH, "Internal error"),
    INVALID_ARGUMENT(2, "General", ErrorSeverity.MEDIUM, "Invalid argument"),
    NOT_FOUND(3, "General", ErrorSeverity.MEDIUM, "Resource not found"),
    ALREADY_EXISTS(4, "General", ErrorSeverity.LOW, "Resource already exists"),
    PERMISSION_DENIED(5, "General", ErrorSeverity.HIGH, "Permission denied"),
    UNAUTHENTICATED(6, "General", ErrorSeverity.HIGH, "Unauthenticated"),
    RESOURCE_EXHAUSTED(7, "General", ErrorSeverity.HIGH, "Resource exhausted"),
    FAILED_PRECONDITION(8, "General", ErrorSeverity.MEDIUM, "Failed precondition"),
    ABORTED(9, "General", ErrorSeverity.MEDIUM, "Operation aborted"),
    OUT_OF_RANGE(10, "General", ErrorSeverity.MEDIUM, "Value out of range"),
    NOT_IMPLEMENTED(11, "General", ErrorSeverity.LOW, "Feature not implemented"),
    UNAVAILABLE(12, "General", ErrorSeverity.HIGH, "Service unavailable"),

    // Network errors (1000-1999)
    NETWORK_UNREACHABLE(1000, "Network", ErrorSeverity.HIGH, "Network unreachable"),
    NETWORK_TIMEOUT(1001, "Network", ErrorSeverity.MEDIUM, "Network operation timed out"),
    NETWORK_DNS_FAILURE(1002, "Network", ErrorSeverity.HIGH, "DNS resolution failed"),
    NETWORK_CONNECTION_RESET(1003, "Network", ErrorSeverity.MEDIUM, "Network connection was reset"),
    NETWORK_CONNECTION_CLOSED(1004, "Network", ErrorSeverity.LOW, "Network connection was closed"),
    NETWORK_PROXY_ERROR(1005, "Network", ErrorSeverity.HIGH, "Proxy server error"),
    NETWORK_TLS_ERROR(1006, "Network", ErrorSeverity.HIGH, "TLS/SSL error"),

    // Authentication errors (2000-2999)
    AUTH_INVALID_CREDENTIALS(2000, "Authentication", ErrorSeverity.HIGH, "Invalid user credentials"),
    AUTH_EXPIRED_CREDENTIALS(2001, "Authentication", ErrorSeverity.MEDIUM, "Credentials expired"),
    AUTH_RATE_LIMITED(2002, "Authentication", ErrorSeverity.MEDIUM, "Authentication rate limited"),
    AUTH_ACCOUNT_LOCKED(2003, "Authentication", ErrorSeverity.HIGH, "Account locked"),
    AUTH_TOKEN_INVALID(2004, "Authentication", ErrorSeverity.MEDIUM, "Invalid authentication token"),
    AUTH_TOKEN_EXPIRED(2005, "Authentication", ErrorSeverity.MEDIUM, "Authentication token expired"),
    AUTH_MISSING_CREDENTIALS(2006, "Authentication", ErrorSeverity.HIGH, "Missing authentication credentials"),

    // Tunnel errors (3000-3999)
    TUNNEL_INIT_FAILED(3000, "Tunnel", ErrorSeverity.HIGH, "Tunnel initialization failed"),
    TUNNEL_CLOSED_UNEXPECTED(3001, "Tunnel", ErrorSeverity.MEDIUM, "Tunnel closed unexpectedly"),
    TUNNEL_PACKET_DROPPED(3002, "Tunnel", ErrorSeverity.LOW, "Tunnel packet dropped"),
    TUNNEL_DEVICE_ERROR(3003, "Tunnel", ErrorSeverity.HIGH, "Tunnel device error"),
    TUNNEL_ROUTE_ERROR(3004, "Tunnel", ErrorSeverity.HIGH, "Tunnel routing error"),
    TUNNEL_DNS_ERROR(3005, "Tunnel", ErrorSeverity.MEDIUM, "Tunnel DNS error"),
    TUNNEL_ENCRYPTION_ERROR(3006, "Tunnel", ErrorSeverity.HIGH, "Tunnel encryption error"),

    // Configuration errors (4000-4999)
    CONFIG_INVALID(4000, "Configuration", ErrorSeverity.HIGH, "Invalid configuration"),
    CONFIG_MISSING(4001, "Configuration", ErrorSeverity.HIGH, "Configuration missing"),
    CONFIG_PERMISSION_DENIED(4002, "Configuration", ErrorSeverity.HIGH, "Configuration permission denied"),
    CONFIG_READ_ERROR(4003, "Configuration", ErrorSeverity.MEDIUM, "Configuration read error"),
    CONFIG_WRITE_ERROR(4004, "Configuration", ErrorSeverity.MEDIUM, "Configuration write error"),
    CONFIG_PARSE_ERROR(4005, "Configuration", ErrorSeverity.HIGH, "Configuration parse error"),
    CONFIG_VALIDATION_ERROR(4006, "Configuration", ErrorSeverity.HIGH, "Configuration validation error"),

    // Platform errors (5000-5999)
    PLATFORM_UNSUPPORTED(5000, "Platform", ErrorSeverity.CRITICAL, "Platform not supported"),
    PLATFORM_PERMISSION_DENIED(5001, "Platform", ErrorSeverity.HIGH, "Platform permission denied"),
    PLATFORM_DRIVER_ERROR(5002, "Platform", ErrorSeverity.HIGH, "Platform driver error"),
    PLATFORM_SYSTEM_ERROR(5003, "Platform", ErrorSeverity.HIGH, "Platform system error"),
    PLATFORM_RESOURCE_ERROR(5004, "Platform", ErrorSeverity.HIGH, "Platform resource error"),
    PLATFORM_NETWORK_ERROR(5005, "Platform", ErrorSeverity.HIGH, "Platform network error"),
    PLATFORM_FIREWALL_ERROR(5006, "Platform", ErrorSeverity.HIGH, "Platform firewall error"),

    // Protocol errors (6000-6999)
    PROTOCOL_VERSION_MISMATCH(6000, "Protocol", ErrorSeverity.HIGH, "Protocol version mismatch"),
    PROTOCOL_INVALID_FORMAT(6001, "Protocol", ErrorSeverity.MEDIUM, "Invalid protocol format"),
    PROTOCOL_UNSUPPORTED(6002, "Protocol", ErrorSeverity.HIGH, "Protocol not supported"),
    PROTOCOL_HANDSHAKE_FAILED(6003, "Protocol", ErrorSeverity.HIGH, "Protocol handshake failed"),
    PROTOCOL_ENCRYPTION_ERROR(6004, "Protocol", ErrorSeverity.HIGH, "Protocol encryption error"),
    PROTOCOL_DECRYPTION_ERROR(6005, "Protocol", ErrorSeverity.HIGH, "Protocol decryption error"),
    PROTOCOL_AUTH_ERROR(6006, "Protocol", ErrorSeverity.HIGH, "Protocol authentication error");

    companion object {
        /**
         * NAME: fromCode
         *
         * DESCRIPTION:
         *     根据错误代码获取对应的枚举值
         *
         * PARAMETERS:
         *     code - 错误代码
         *
         * RETURNS:
         *     VPNErrorCode - 对应的错误代码枚举，如果未找到则返回UNKNOWN
         */
        fun fromCode(code: Int): VPNErrorCode {
            return values().find { it.code == code } ?: UNKNOWN
        }

        /**
         * NAME: getByCategory
         *
         * DESCRIPTION:
         *     根据错误分类获取所有相关的错误代码
         *
         * PARAMETERS:
         *     category - 错误分类名称
         *
         * RETURNS:
         *     List<VPNErrorCode> - 该分类下的所有错误代码
         */
        fun getByCategory(category: String): List<VPNErrorCode> {
            return values().filter { it.category == category }
        }

        /**
         * NAME: getBySeverity
         *
         * DESCRIPTION:
         *     根据错误严重程度获取所有相关的错误代码
         *
         * PARAMETERS:
         *     severity - 错误严重程度
         *
         * RETURNS:
         *     List<VPNErrorCode> - 该严重程度的所有错误代码
         */
        fun getBySeverity(severity: ErrorSeverity): List<VPNErrorCode> {
            return values().filter { it.severity == severity }
        }

        /**
         * NAME: getNetworkErrors
         *
         * DESCRIPTION:
         *     获取所有网络相关错误代码
         *
         * RETURNS:
         *     List<VPNErrorCode> - 网络错误代码列表
         */
        fun getNetworkErrors(): List<VPNErrorCode> = getByCategory("Network")

        /**
         * NAME: getAuthErrors
         *
         * DESCRIPTION:
         *     获取所有认证相关错误代码
         *
         * RETURNS:
         *     List<VPNErrorCode> - 认证错误代码列表
         */
        fun getAuthErrors(): List<VPNErrorCode> = getByCategory("Authentication")

        /**
         * NAME: getTunnelErrors
         *
         * DESCRIPTION:
         *     获取所有隧道相关错误代码
         *
         * RETURNS:
         *     List<VPNErrorCode> - 隧道错误代码列表
         */
        fun getTunnelErrors(): List<VPNErrorCode> = getByCategory("Tunnel")

        /**
         * NAME: getCriticalErrors
         *
         * DESCRIPTION:
         *     获取所有严重错误代码
         *
         * RETURNS:
         *     List<VPNErrorCode> - 严重错误代码列表
         */
        fun getCriticalErrors(): List<VPNErrorCode> = getBySeverity(ErrorSeverity.CRITICAL)
    }

    /**
     * NAME: isNetworkError
     *
     * DESCRIPTION:
     *     判断是否为网络相关错误
     *
     * RETURNS:
     *     Boolean - 如果是网络错误返回true
     */
    fun isNetworkError(): Boolean = category == "Network"

    /**
     * NAME: isAuthError
     *
     * DESCRIPTION:
     *     判断是否为认证相关错误
     *
     * RETURNS:
     *     Boolean - 如果是认证错误返回true
     */
    fun isAuthError(): Boolean = category == "Authentication"

    /**
     * NAME: isCritical
     *
     * DESCRIPTION:
     *     判断是否为严重错误
     *
     * RETURNS:
     *     Boolean - 如果是严重错误返回true
     */
    fun isCritical(): Boolean = severity == ErrorSeverity.CRITICAL

    /**
     * NAME: canRetry
     *
     * DESCRIPTION:
     *     判断该错误是否可以重试
     *
     * RETURNS:
     *     Boolean - 如果可以重试返回true
     */
    fun canRetry(): Boolean {
        return when (this) {
            // 网络错误通常可以重试
            NETWORK_TIMEOUT, NETWORK_UNREACHABLE, NETWORK_CONNECTION_RESET -> true
            // 认证错误中，除了凭据错误外可以重试
            AUTH_RATE_LIMITED, AUTH_TOKEN_EXPIRED -> true
            // 隧道错误部分可以重试
            TUNNEL_CLOSED_UNEXPECTED, TUNNEL_PACKET_DROPPED -> true
            // 平台错误通常不可重试
            PLATFORM_PERMISSION_DENIED, PLATFORM_UNSUPPORTED -> false
            // 协议错误部分可以重试
            PROTOCOL_HANDSHAKE_FAILED -> true
            // 其他情况根据严重程度判断
            else -> severity != ErrorSeverity.CRITICAL
        }
    }
}
