/**
 * FILE: VPNConfig.kt
 *
 * DESCRIPTION:
 *     VPN configuration data structures for Android platform.
 *     Defines VPN configuration parameters, route information, and DNS settings.
 *     Equivalent to iOS VPNConfiguration with Android-specific adaptations.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.platform.models

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * NAME: VPNConfig
 *
 * DESCRIPTION:
 *     Main VPN configuration data class for Android VPN interface management.
 *     Contains all necessary parameters for establishing VPN connection and configuring network settings.
 *     Equivalent to iOS VPNConfiguration with Android VpnService.Builder compatibility.
 *
 * PROPERTIES:
 *     localIP - Local tunnel IP address assigned to VPN interface
 *     prefixLength - Network prefix length for tunnel IP (default: 32)
 *     mtu - Maximum transmission unit for VPN interface (default: 1500)
 *     routes - List of routes to be configured through VPN
 *     dnsServers - List of DNS server addresses
 *     excludedRoutes - List of routes to be excluded from VPN (protected routes)
 *     serverAddress - VPN server address for remote endpoint
 *     serverPort - VPN server port number
 */
@Parcelize
data class VPNConfig(
    val localIP: String,
    val prefixLength: Int = 32,
    val mtu: Int = 1500,
    val routes: List<RouteInfo>,
    val dnsServers: List<String>,
    val excludedRoutes: List<RouteInfo>,
    val serverAddress: String,
    val serverPort: Int
) : Parcelable {

    /**
     * NAME: isValid
     *
     * DESCRIPTION:
     *     Validates VPN configuration parameters for correctness.
     *     Checks IP address format, port range, and required fields.
     *
     * RETURNS:
     *     Boolean - true if configuration is valid, false otherwise
     */
    fun isValid(): Boolean {
        return try {
            // Validate local IP address format
            if (!isValidIPAddress(localIP)) return false
            
            // Validate server address
            if (serverAddress.isBlank()) return false
            
            // Validate port range
            if (serverPort !in 1..65535) return false
            
            // Validate prefix length
            if (prefixLength !in 1..32) return false
            
            // Validate MTU range
            if (mtu !in 576..9000) return false
            
            // Validate DNS servers
            if (dnsServers.any { !isValidIPAddress(it) }) return false
            
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * NAME: isValidIPAddress
     *
     * DESCRIPTION:
     *     Validates IPv4 address format using regex pattern.
     *
     * PARAMETERS:
     *     ip - IP address string to validate
     *
     * RETURNS:
     *     Boolean - true if valid IPv4 address, false otherwise
     */
    private fun isValidIPAddress(ip: String): Boolean {
        val ipPattern = Regex(
            "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
        )
        return ipPattern.matches(ip)
    }

    /**
     * NAME: toDebugString
     *
     * DESCRIPTION:
     *     Creates debug-friendly string representation of VPN configuration.
     *     Includes detailed network configuration for troubleshooting.
     *
     * RETURNS:
     *     String - Debug string representation
     */
    fun toDebugString(): String {
        val routesStr = if (routes.isNotEmpty()) {
            routes.joinToString(", ") { it.toCIDR() }
        } else {
            "default(0.0.0.0/0)"
        }

        val excludedStr = if (excludedRoutes.isNotEmpty()) {
            excludedRoutes.joinToString(", ") { it.toCIDR() }
        } else {
            "none"
        }

        return buildString {
            append("VPNConfig(")
            append("localIP=$localIP/$prefixLength, ")
            append("mtu=$mtu, ")
            append("server=${serverAddress}:${serverPort}, ")
            append("dns=[${dnsServers.joinToString(",")}], ")
            append("routes=[$routesStr], ")
            append("excluded=[$excludedStr]")
            append(")")
        }
    }
}

/**
 * NAME: RouteInfo
 *
 * DESCRIPTION:
 *     Route information data class for VPN routing configuration.
 *     Represents network routes to be added or excluded from VPN tunnel.
 *     Compatible with Android VpnService.Builder route configuration.
 *
 * PROPERTIES:
 *     destination - Destination network address (IP or CIDR)
 *     prefixLength - Network prefix length for route
 *     gateway - Gateway address for route (optional)
 *     metric - Route metric for priority (optional)
 */
@Parcelize
data class RouteInfo(
    val destination: String,
    val prefixLength: Int,
    val gateway: String? = null,
    val metric: Int? = null
) : Parcelable {

    /**
     * NAME: isValid
     *
     * DESCRIPTION:
     *     Validates route information for correctness.
     *     Checks destination address format and prefix length range.
     *
     * RETURNS:
     *     Boolean - true if route is valid, false otherwise
     */
    fun isValid(): Boolean {
        return try {
            // Validate destination address
            if (!isValidIPAddress(destination)) return false
            
            // Validate prefix length
            if (prefixLength !in 0..32) return false
            
            // Validate gateway if provided
            if (gateway != null && !isValidIPAddress(gateway)) return false
            
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * NAME: isValidIPAddress
     *
     * DESCRIPTION:
     *     Validates IPv4 address format using regex pattern.
     *
     * PARAMETERS:
     *     ip - IP address string to validate
     *
     * RETURNS:
     *     Boolean - true if valid IPv4 address, false otherwise
     */
    private fun isValidIPAddress(ip: String): Boolean {
        val ipPattern = Regex(
            "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
        )
        return ipPattern.matches(ip)
    }

    /**
     * NAME: toCIDR
     *
     * DESCRIPTION:
     *     Converts route to CIDR notation string.
     *
     * RETURNS:
     *     String - Route in CIDR format (e.g., "***********/24")
     */
    fun toCIDR(): String = "$destination/$prefixLength"
}

/**
 * NAME: RoutingMode
 *
 * DESCRIPTION:
 *     Routing mode enumeration for VPN traffic routing strategy.
 *     Equivalent to iOS RoutingMode with Android-specific adaptations.
 *
 * VALUES:
 *     ALL - Route all traffic through VPN (global routing)
 *     CUSTOM - Route only specified networks through VPN (split tunneling)
 */
enum class RoutingMode {
    ALL,
    CUSTOM;

    /**
     * NAME: toConfigValue
     *
     * DESCRIPTION:
     *     Converts routing mode to configuration string value.
     *
     * RETURNS:
     *     String - Configuration value for routing mode
     */
    fun toConfigValue(): String = when (this) {
        ALL -> "all"
        CUSTOM -> "custom"
    }

    companion object {
        /**
         * NAME: fromConfigValue
         *
         * DESCRIPTION:
         *     Creates routing mode from configuration string value.
         *
         * PARAMETERS:
         *     value - Configuration string value
         *
         * RETURNS:
         *     RoutingMode - Corresponding routing mode, defaults to ALL
         */
        fun fromConfigValue(value: String): RoutingMode = when (value.lowercase()) {
            "custom" -> CUSTOM
            else -> ALL
        }
    }
}
