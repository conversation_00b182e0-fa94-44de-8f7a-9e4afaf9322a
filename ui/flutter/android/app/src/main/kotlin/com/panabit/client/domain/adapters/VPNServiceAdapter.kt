/**
 * FILE: VPNServiceAdapter.kt
 *
 * DESCRIPTION:
 *     Adapter implementation of VPNServiceInterface for ITforceVPNService.
 *     Bridges Platform Channel handler with concrete VPN service implementation.
 *     Provides abstraction layer following dependency inversion principle.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create VPN service adapter
 */

package com.panabit.client.domain.adapters

import android.content.Context
import android.content.Intent
import com.panabit.client.domain.interfaces.VPNServiceInterface
import com.panabit.client.domain.interfaces.InterfaceInfo
import com.panabit.client.connection.models.VPNState
import com.panabit.client.connection.models.ServerInfo
import com.panabit.client.connection.models.ServerStatus
import com.panabit.client.connection.ConnectionManager
import com.panabit.client.connection.ServerManager
import com.panabit.client.vpn.ITforceVPNService
import com.panabit.client.network.NetworkUtils
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logError
import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logWarn
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.delay
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

/**
 * NAME: VPNServiceAdapter
 *
 * DESCRIPTION:
 *     Adapter implementation that bridges VPNServiceInterface with ITforceVPNService.
 *     Provides abstraction layer for Platform Channel integration.
 *     Converts between domain models and concrete service implementation.
 *
 * PROPERTIES:
 *     vpnService - Concrete ITforceVPNService instance
 *     context - Android context for service communication
 */
class VPNServiceAdapter(
    private val vpnService: ITforceVPNService,
    private val context: Context
) : VPNServiceInterface, KoinComponent {

    // Inject core components through Koin
    private val serverManager: ServerManager by inject()
    private val injectedConnectionManager: ConnectionManager by inject()

    companion object {
        // Intent actions matching ITforceVPNService
        private const val ACTION_CONNECT = "com.panabit.client.CONNECT"
        private const val ACTION_DISCONNECT = "com.panabit.client.DISCONNECT"
        private const val ACTION_CHECK_PERMISSION = "com.panabit.client.CHECK_PERMISSION"
    }

    // MARK: - Lifecycle Management

    override suspend fun initialize(): Result<Unit> = withContext(Dispatchers.IO) {
        return@withContext try {
            // ITforceVPNService initialization is handled by Android service lifecycle
            // Check if service instance is available
            val serviceRunning = ITforceVPNService.isServiceRunning()
            
            if (serviceRunning) {
                logInfo("VPN service adapter initialized successfully")
                Result.success(Unit)
            } else {
                logError("VPN service is not running")
                Result.failure(Exception("VPN service is not running"))
            }
        } catch (e: Exception) {
            logError("VPN service adapter initialization failed", emptyMap(), e)
            Result.failure(e)
        }
    }

    override fun isInitialized(): Boolean {
        return try {
            ITforceVPNService.isServiceRunning()
        } catch (e: Exception) {
            logError("Failed to check initialization status", emptyMap(), e)
            false
        }
    }

    override suspend fun isHealthy(): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            // Check if service is running and responsive
            ITforceVPNService.isServiceRunning()
        } catch (e: Exception) {
            logError("Health check failed", emptyMap(), e)
            false
        }
    }

    // MARK: - Authentication

    override suspend fun login(username: String, password: String): Result<Map<String, Any>> =
        withContext(Dispatchers.IO) {
            return@withContext try {
                logInfo("Processing login request", mapOf("username" to username))

                if (username.isBlank() || password.isBlank()) {
                    logError("Login failed: empty credentials")
                    return@withContext Result.failure(Exception("Username and password cannot be empty"))
                }

                // Get server list for authentication (equivalent to iOS login flow)
                logInfo("Getting server list for authentication")
                var servers = serverManager.getServers()
                logInfo("Retrieved server list", mapOf("server_count" to servers.size))

                // If no servers available, server list should be initialized by UI via setServerListUrl
                if (servers.isEmpty()) {
                    logInfo("No servers available - server list should be set by UI before login")
                    return@withContext Result.failure(Exception("Server list not available. Please ensure server list URL is set via UI before attempting login."))
                }

                // Execute ping operation to get latest latency data before server selection (matching iOS)
                logInfo("Starting ping operation for all servers before authentication")

                // Send ping start event to notify Flutter UI (matching iOS implementation)
                try {
                    val platformHandler = com.panabit.client.MainActivity.getPlatformChannelHandler()
                    platformHandler?.sendEvent("ping_start", emptyMap<String, Any>())
                    logDebug("Ping start event sent to Flutter")
                } catch (e: Exception) {
                    logWarn("Failed to send ping start event", emptyMap(), e)
                }

                // Use unified pingServers() method for consistent event handling
                try {
                    pingServers()
                    logInfo("Ping operation completed for authentication")
                } catch (e: Exception) {
                    logWarn("Ping operation failed during login, continuing with existing data: ${e.message}")
                }

                // Get best server for authentication (with fresh ping data)
                val bestServer = try {
                    serverManager.selectBestServer()
                } catch (e: Exception) {
                    logError("Failed to select best server: ${e.message}", e)
                    return@withContext Result.failure(e)
                }

                if (bestServer == null) {
                    logError("No server available for authentication")
                    return@withContext Result.failure(Exception("No server available for authentication"))
                }

                // ✅ CRITICAL FIX: Perform actual authentication with server using decoupled method
                logInfo("Performing authentication with server", mapOf(
                    "username" to username,
                    "server" to bestServer.displayName,
                    "server_address" to bestServer.serverName,
                    "server_port" to bestServer.serverPort
                ))

                try {
                    // Create SDWAN protocol instance for authentication
                    val protocol = com.panabit.client.protocol.SDWANProtocol(bestServer.serverName, bestServer.serverPort)

                    // Create UDP socket for authentication (following iOS pattern)
                    val socket = java.net.DatagramSocket().apply {
                        soTimeout = 2000 // 2 second timeout per attempt
                    }

                    try {
                        // Use the new decoupled authentication method with external socket
                        val authResult = protocol.authenticateWithSocket(
                            socket = socket,
                            username = username,
                            password = password,
                            mtu = 1400u,
                            encryptionMethod = com.panabit.client.protocol.models.EncryptionMethod.XOR
                        )

                        if (!authResult.success) {
                            logError("Authentication failed", mapOf(
                                "username" to username,
                                "server" to bestServer.displayName,
                                "error_message" to (authResult.errorMessage ?: "Unknown error")
                            ))
                            return@withContext Result.failure(Exception("Authentication failed: ${authResult.errorMessage ?: "Invalid username or password"}"))
                        }

                        logInfo("Authentication successful", mapOf(
                            "username" to username,
                            "server" to bestServer.displayName,
                            "session_id" to String.format("0x%04X", (authResult.sessionID ?: 0u).toInt()),
                            "token" to String.format("0x%08X", (authResult.token ?: 0u).toInt())
                        ))

                        // Save user credentials for VPN authentication (matching iOS pattern)
                        // This ensures credentials are available for subsequent VPN connections
                        logInfo("Attempting to save user credentials for VPN authentication", mapOf(
                            "username" to username,
                            "has_password" to password.isNotEmpty(),
                            "password_length" to password.length
                        ))

                        try {
                            injectedConnectionManager.saveUserCredentials(
                                username = username,
                                password = password,
                                domain = "", // Default empty domain
                                rememberCredentials = true // Enable credential persistence
                            )
                            logInfo("User credentials saved successfully for VPN authentication")

                            // Verify credentials were saved by attempting to retrieve them
                            val savedCredentials = injectedConnectionManager.getCurrentCredentials()
                            if (savedCredentials != null) {
                                val (savedUsername, savedPassword) = savedCredentials
                                logInfo("Credential verification successful", mapOf(
                                    "saved_username" to savedUsername,
                                    "has_saved_password" to savedPassword.isNotEmpty()
                                ))
                            } else {
                                logError("Credential verification failed - no credentials found after saving")
                            }
                        } catch (e: Exception) {
                            logError("Failed to save user credentials", mapOf(
                                "username" to username,
                                "error_type" to e.javaClass.simpleName,
                                "error_message" to (e.message ?: "Unknown error")
                            ), e)
                            // Continue with login success even if credential saving fails
                            // This ensures login doesn't fail due to storage issues
                        }

                        // Note: ping_results event will be sent automatically by ITforceVPNService observer
                        // when ServerManager.pingResults StateFlow is updated during login ping operation

                        // Return successful authentication result with server info and session data
                        val userData = mapOf(
                            "success" to true,
                            "message" to "Login successful",
                            "data" to mapOf(
                                "best_server" to bestServer.toMap(),
                                "session_id" to (authResult.sessionID?.toInt() ?: 0),
                                "token" to (authResult.token?.toLong() ?: 0L)
                            )
                        )

                        Result.success(userData)

                    } finally {
                        // Close the socket after authentication
                        // In a full implementation, this socket would be reused for data transmission
                        socket.close()
                    }

                } catch (e: Exception) {
                    logError("Authentication operation failed", mapOf("username" to username), e)
                    return@withContext Result.failure(Exception("Authentication failed: ${e.message}"))
                }

            } catch (e: Exception) {
                logError("Login operation failed", emptyMap(), e)
                Result.failure(e)
            }
        }

    // MARK: - Connection Management

    /**
     * NAME: waitForDisconnectionComplete
     *
     * DESCRIPTION:
     *     Waits for ongoing disconnection operation to complete.
     *     Implements timeout-based waiting to prevent infinite blocking.
     *
     * RETURNS:
     *     Boolean - true if disconnection completed, false if timeout
     */
    private suspend fun waitForDisconnectionComplete(): Boolean {
        val maxWaitTime = 10_000L // Reduced from 10s to 2s - runInterruptible should make disconnect much faster
        val checkInterval = 100L // Reduced from 200ms to 100ms for faster response
        val startTime = System.currentTimeMillis()

        while (System.currentTimeMillis() - startTime < maxWaitTime) {
            val currentState = vpnService.getCurrentState()

            // If no longer disconnecting, operation completed
            if (!currentState.isDisconnecting) {
                logInfo("Disconnection completed", mapOf(
                    "final_state" to currentState.name,
                    "wait_time_ms" to (System.currentTimeMillis() - startTime)
                ))
                return true
            }

            // Wait before next check
            delay(checkInterval)
        }

        logWarn("Timeout waiting for disconnection to complete", mapOf(
            "timeout_ms" to maxWaitTime,
            "current_state" to vpnService.getCurrentState().name
        ))
        return false
    }

    /**
     * NAME: waitForConnectionStateChange
     *
     * DESCRIPTION:
     *     Waits for VPN state to change from current state.
     *     Used to ensure disconnect operation actually completes.
     *
     * PARAMETERS:
     *     expectedFinalState - Expected final state after operation
     *     timeoutMs - Maximum wait time in milliseconds
     *
     * RETURNS:
     *     Boolean - true if state changed to expected state, false if timeout
     */
    private suspend fun waitForConnectionStateChange(
        expectedFinalState: VPNState,
        timeoutMs: Long = 5_000L // Reduced from 3s to 1s - runInterruptible should make disconnect much faster
    ): Boolean {
        val checkInterval = 100L // Reduced from 200ms to 100ms for faster response
        val startTime = System.currentTimeMillis()
        var checkCount = 0

        logInfo("🔍 [DISCONNECT-PERF] Starting state change wait", mapOf(
            "expected_state" to expectedFinalState.name,
            "timeout_ms" to timeoutMs,
            "check_interval_ms" to checkInterval
        ))

        while (System.currentTimeMillis() - startTime < timeoutMs) {
            checkCount++
            val checkStartTime = System.currentTimeMillis()
            val currentState = vpnService.getCurrentState()
            val checkDuration = System.currentTimeMillis() - checkStartTime

            // Log every 5th check to avoid spam
            if (checkCount % 5 == 0) {
                logInfo("🔍 [DISCONNECT-PERF] State check progress", mapOf(
                    "check_count" to checkCount,
                    "current_state" to currentState.name,
                    "expected_state" to expectedFinalState.name,
                    "elapsed_ms" to (System.currentTimeMillis() - startTime),
                    "check_duration_ms" to checkDuration
                ))
            }

            if (currentState == expectedFinalState ||
                (expectedFinalState is VPNState.Disconnected && currentState.isDisconnected)) {
                val totalWaitTime = System.currentTimeMillis() - startTime
                logInfo("✅ [DISCONNECT-PERF] VPN state changed to expected state", mapOf(
                    "expected_state" to expectedFinalState.name,
                    "actual_state" to currentState.name,
                    "wait_time_ms" to totalWaitTime,
                    "check_count" to checkCount,
                    "avg_check_duration_ms" to (totalWaitTime / checkCount.coerceAtLeast(1))
                ))
                return true
            }

            delay(checkInterval)
        }

        val totalWaitTime = System.currentTimeMillis() - startTime
        logWarn("⏰ [DISCONNECT-PERF] Timeout waiting for VPN state change", mapOf(
            "expected_state" to expectedFinalState.name,
            "current_state" to vpnService.getCurrentState().name,
            "timeout_ms" to timeoutMs,
            "actual_wait_ms" to totalWaitTime,
            "check_count" to checkCount
        ))
        return false
    }

    override suspend fun connect(serverId: String): Result<Map<String, Any>> = withContext(Dispatchers.IO) {
        return@withContext try {
            logInfo("Processing connect request", mapOf("server_id" to serverId))

            // Check current VPN state to prevent concurrent operations
            val currentState = vpnService.getCurrentState()
            if (currentState.isOperationInProgress) {
                val errorMessage = "Cannot connect while another operation is in progress: ${currentState.name}"
                logWarn(errorMessage, mapOf("current_state" to currentState.name))
                return@withContext Result.failure(Exception(errorMessage))
            }

            // Wait for any ongoing disconnection to complete
            if (currentState.isDisconnecting) {
                logInfo("Waiting for ongoing disconnection to complete before connecting")
                val disconnectCompleted = waitForDisconnectionComplete()
                if (!disconnectCompleted) {
                    val errorMessage = "Timeout waiting for disconnection to complete"
                    logError(errorMessage)
                    return@withContext Result.failure(Exception(errorMessage))
                }
            }

            // Ensure server list is loaded before connection
            ensureServerListLoaded()

            // Create server info for connection
            val serverInfo = createServerInfoForConnection(serverId)
            val serverJson = Json.encodeToString(serverInfo)

            // Send connect intent to VPN service
            val intent = Intent(context, ITforceVPNService::class.java).apply {
                action = ACTION_CONNECT
                putExtra("server", serverJson)
            }

            context.startService(intent)

            // Get interface info for response
            val interfaceInfo = getInterfaceInfo()

            // Return success response with interface info and initial latency
            val response = mapOf(
                "success" to true,
                "message" to "Connection initiated",
                "server_id" to serverId,
                "interface_info" to interfaceInfo.toFlutterMap(),
                "latency_ms" to -1  // Initial value, will be updated when connection completes
            )

            //logInfo("Connect request sent to VPN service", mapOf(
            //    "server_id" to serverId,
            //    "interface_name" to interfaceInfo.interfaceName,
            //    "local_ip" to interfaceInfo.localIp
            //))
            Result.success(response)
        } catch (e: Exception) {
            logError("Connect operation failed", emptyMap(), e)
            Result.failure(e)
        }
    }

    override suspend fun disconnect(): Result<Unit> = withContext(Dispatchers.IO) {
        return@withContext try {
            val disconnectStartTime = System.currentTimeMillis()
            logInfo("🔄 [DISCONNECT-PERF] Starting disconnect operation", mapOf(
                "start_time" to disconnectStartTime
            ))

            // Check current state - if already disconnected, return success immediately
            val stateCheckStartTime = System.currentTimeMillis()
            val currentState = vpnService.getCurrentState()
            val stateCheckDuration = System.currentTimeMillis() - stateCheckStartTime

            logInfo("📊 [DISCONNECT-PERF] State check completed", mapOf(
                "current_state" to currentState.name,
                "duration_ms" to stateCheckDuration
            ))

            if (currentState.isDisconnected) {
                val totalDuration = System.currentTimeMillis() - disconnectStartTime
                logInfo("✅ [DISCONNECT-PERF] Already disconnected, no action needed", mapOf(
                    "total_duration_ms" to totalDuration
                ))
                return@withContext Result.success(Unit)
            }

            // If already disconnecting, wait for completion
            if (currentState.isDisconnecting) {
                val waitStartTime = System.currentTimeMillis()
                logInfo("⏳ [DISCONNECT-PERF] Disconnection already in progress, waiting for completion", mapOf(
                    "wait_start_time" to waitStartTime
                ))

                val completed = waitForConnectionStateChange(VPNState.Disconnected)
                val waitDuration = System.currentTimeMillis() - waitStartTime
                val totalDuration = System.currentTimeMillis() - disconnectStartTime

                logInfo("📊 [DISCONNECT-PERF] Wait for existing disconnection completed", mapOf(
                    "completed" to completed,
                    "wait_duration_ms" to waitDuration,
                    "total_duration_ms" to totalDuration
                ))

                return@withContext if (completed) {
                    Result.success(Unit)
                } else {
                    Result.failure(Exception("Timeout waiting for existing disconnection to complete"))
                }
            }

            // Send disconnect intent to VPN service
            val intentStartTime = System.currentTimeMillis()
            val intent = Intent(context, ITforceVPNService::class.java).apply {
                action = ACTION_DISCONNECT
            }

            context.startService(intent)
            val intentDuration = System.currentTimeMillis() - intentStartTime
            logInfo("📤 [DISCONNECT-PERF] Disconnect intent sent to VPN service", mapOf(
                "intent_duration_ms" to intentDuration
            ))

            // Wait for disconnection to complete
            val waitStartTime = System.currentTimeMillis()
            logInfo("⏳ [DISCONNECT-PERF] Starting wait for disconnection completion", mapOf(
                "wait_start_time" to waitStartTime
            ))

            val disconnectCompleted = waitForConnectionStateChange(VPNState.Disconnected)
            val waitDuration = System.currentTimeMillis() - waitStartTime
            val totalDuration = System.currentTimeMillis() - disconnectStartTime

            if (disconnectCompleted) {
                logInfo("✅ [DISCONNECT-PERF] Disconnect operation completed successfully", mapOf(
                    "wait_duration_ms" to waitDuration,
                    "total_duration_ms" to totalDuration
                ))
                Result.success(Unit)
            } else {
                val errorMessage = "Disconnect operation timed out"
                logError("❌ [DISCONNECT-PERF] Disconnect operation timed out", mapOf(
                    "wait_duration_ms" to waitDuration,
                    "total_duration_ms" to totalDuration
                ))
                Result.failure(Exception(errorMessage))
            }
        } catch (e: Exception) {
            logError("❌ [DISCONNECT-PERF] Disconnect operation failed", emptyMap(), e)
            Result.failure(e)
        }
    }

    // MARK: - Status Monitoring

    override suspend fun getStatus(): VPNState = withContext(Dispatchers.IO) {
        return@withContext try {
            // Get current VPN state from ITforceVPNService
            val currentState = vpnService.getCurrentState()
            
            // Convert to domain model
            convertToVPNState(currentState)
        } catch (e: Exception) {
            logError("Failed to get VPN status", emptyMap(), e)
            VPNState.Disconnected
        }
    }

    override suspend fun getInterfaceInfo(): InterfaceInfo = withContext(Dispatchers.IO) {
        return@withContext try {
            // Get VPN tunnel IP from current state
            val currentState = vpnService.getCurrentState()
            val tunIp = when (currentState) {
                is VPNState.Connected -> currentState.tunnelIP
                else -> null
            }

            // Try to get recorded physical interface info first (preferred when VPN is connected)
            val (physicalInterfaceName, physicalLocalIp) = if (currentState is VPNState.Connected) {
                // When VPN is connected, use the recorded physical interface info
                // This avoids getting VPN interface (tun0) instead of real physical interface
                val recordedInfo = injectedConnectionManager.getRecordedPhysicalInterfaceInfo()
                if (recordedInfo.first != "unknown" && recordedInfo.second != "unknown") {
                    logDebug("Using recorded physical interface info", mapOf(
                        "interface_name" to recordedInfo.first,
                        "interface_ip" to recordedInfo.second,
                        "source" to "recorded_before_connection"
                    ))
                    recordedInfo
                } else {
                    // Fallback to current detection if no recorded info
                    logWarn("No recorded interface info available, falling back to current detection")
                    NetworkUtils.getPhysicalInterfaceInfo(context)
                }
            } else {
                // When VPN is not connected, use current interface detection
                NetworkUtils.getPhysicalInterfaceInfo(context)
            }

            logDebug("Interface info retrieved", mapOf(
                "physical_interface" to physicalInterfaceName,
                "physical_ip" to physicalLocalIp,
                "tunnel_ip" to (tunIp ?: "none"),
                "vpn_state" to currentState.javaClass.simpleName,
                "method" to if (currentState is VPNState.Connected) "recorded" else "current_detection"
            ))

            InterfaceInfo.create(
                interfaceName = physicalInterfaceName,  // Physical interface name (e.g., wlan0, eth0, rmnet0)
                localIp = physicalLocalIp,              // Physical interface IP
                tunIp = tunIp                           // VPN tunnel IP from OPENACK packet
            )
        } catch (e: Exception) {
            logError("Failed to get interface info", emptyMap(), e)
            InterfaceInfo.empty()
        }
    }

    // MARK: - Server Management

    override suspend fun getServers(): List<ServerInfo> = withContext(Dispatchers.IO) {
        return@withContext try {
            // Get server list from ServerManager
            val servers = serverManager.getServers()
            logInfo("Retrieved server list", mapOf("server_count" to servers.size))
            servers
        } catch (e: Exception) {
            logError("Failed to get server list", emptyMap(), e)
            emptyList()
        }
    }

    override suspend fun pingServer(serverId: String): Int = withContext(Dispatchers.IO) {
        return@withContext try {
            // Find server in the server list
            val servers = serverManager.getServers()
            val server = servers.find { it.id == serverId }

            if (server != null) {
                val pingResult = serverManager.pingServer(server)
                logDebug("Server ping completed", mapOf("server_id" to serverId, "ping" to pingResult))
                pingResult
            } else {
                logError("Server not found for ping", mapOf("server_id" to serverId))
                -1
            }
        } catch (e: Exception) {
            logError("Failed to ping server", mapOf("server_id" to serverId), e)
            -1
        }
    }

    override suspend fun pingServers(): Result<Unit> = withContext(Dispatchers.IO) {
        return@withContext try {
            // Send ping start event to notify Flutter UI (matching iOS implementation)
            try {
                val platformHandler = com.panabit.client.MainActivity.getPlatformChannelHandler()
                platformHandler?.sendEvent("ping_start", emptyMap<String, Any>())
                logDebug("Ping start event sent to Flutter")
            } catch (e: Exception) {
                logWarn("Failed to send ping start event", emptyMap(), e)
            }

            // Initiate batch ping using ServerManager
            val pingStartTime = System.currentTimeMillis()
            serverManager.pingAllServers()
            val pingDuration = System.currentTimeMillis() - pingStartTime

            logInfo("Ping servers request completed successfully", mapOf("duration_ms" to pingDuration))

            // Send ping_results event to update Flutter UI with latest latency data (unified approach)
            try {
                val platformHandler = com.panabit.client.MainActivity.getPlatformChannelHandler()
                if (platformHandler != null) {
                    val updatedServers = serverManager.getServers()
                    platformHandler.sendPingResultsEvent(updatedServers)
                    logInfo("Ping results event sent via unified pingServers method", mapOf("server_count" to updatedServers.size))
                }
            } catch (e: Exception) {
                logWarn("Failed to send ping results event", emptyMap(), e)
            }

            // Send ping complete event to notify Flutter UI (matching iOS implementation)
            try {
                val platformHandler = com.panabit.client.MainActivity.getPlatformChannelHandler()
                platformHandler?.sendEvent("ping_complete", emptyMap<String, Any>())
                logDebug("Ping complete event sent to Flutter")
            } catch (e: Exception) {
                logWarn("Failed to send ping complete event", emptyMap(), e)
            }

            Result.success(Unit)
        } catch (e: Exception) {
            logError("Ping servers operation failed", emptyMap(), e)
            Result.failure(e)
        }
    }

    override suspend fun setServerListUrl(url: String): Result<Unit> = withContext(Dispatchers.IO) {
        return@withContext try {
            logInfo("Setting server list URL", mapOf("url" to url))

            // Ensure server manager is available and is the correct implementation
            if (serverManager is com.panabit.client.connection.ServerManagerImpl) {
                val serverManagerImpl = serverManager as com.panabit.client.connection.ServerManagerImpl

                // Set server list URL with SSL verification enabled by default
                serverManagerImpl.setServerListUrl(url, skipSslVerification = false)

                logInfo("Server list URL set successfully", mapOf("url" to url))
                Result.success(Unit)
            } else {
                val errorMessage = "ServerManager is not the expected implementation type"
                logError(errorMessage, mapOf("actual_type" to serverManager.javaClass.simpleName))
                Result.failure(Exception(errorMessage))
            }
        } catch (e: Exception) {
            logError("Failed to set server list URL", mapOf("url" to url), e)
            Result.failure(e)
        }
    }

    // MARK: - Private Helper Methods

    /**
     * NAME: ensureServerListLoaded
     *
     * DESCRIPTION:
     *     Ensures server list is loaded before connection attempts.
     *     Initializes server list URL and fetches servers if needed.
     */
    private suspend fun ensureServerListLoaded() {
        val servers = serverManager.getServers()
        if (servers.isEmpty()) {
            logInfo("Server list empty - should be set by UI via setServerListUrl before connection")
            // Server list should be initialized by UI before connection attempts
            // No fallback to hardcoded URL - rely on proper UI flow
        } else {
            logDebug("Server list already loaded", mapOf("server_count" to servers.size))
        }
    }

    /**
     * NAME: createServerInfoForConnection
     *
     * DESCRIPTION:
     *     Creates ServerInfo object for connection request.
     *     Fetches real server information from ServerManager.
     *
     * PARAMETERS:
     *     serverId - Server identifier
     *
     * RETURNS:
     *     ServerInfo - Server info for connection
     */
    private fun createServerInfoForConnection(serverId: String): ServerInfo {
        return try {
            // Use direct server lookup by ID
            val targetServer = serverManager.getServerById(serverId)

            if (targetServer != null) {
                logInfo("Found server info for connection", mapOf(
                    "server_id" to serverId,
                    "server_name" to targetServer.serverName,
                    "display_name" to targetServer.displayName,
                    "server_port" to targetServer.serverPort
                ))
                targetServer
            } else {
                // Log available servers for debugging
                val servers = serverManager.getServers()
                logWarn("Server not found in manager, creating fallback", mapOf(
                    "server_id" to serverId,
                    "total_servers" to servers.size,
                    "available_servers" to servers.map { "${it.id}:${it.serverName}" }
                ))

                // Fallback to basic server info if not found
                ServerInfo(
                    id = serverId,
                    name = "Server $serverId",
                    nameEn = "Server $serverId",
                    serverName = "server$serverId.itforce.com",
                    serverPort = 443,
                    isAuto = false,
                    ping = -1,
                    status = ServerStatus.Online
                )
            }
        } catch (e: Exception) {
            logError("Failed to get server info, using fallback", mapOf("server_id" to serverId), e)
            // Fallback server info
            ServerInfo(
                id = serverId,
                name = "Server $serverId",
                nameEn = "Server $serverId",
                serverName = "server$serverId.itforce.com",
                serverPort = 443,
                isAuto = false,
                ping = -1,
                status = ServerStatus.Online
            )
        }
    }

    private fun convertToVPNState(serviceState: Any?): VPNState {
        // The service already returns the correct VPNState type
        return when (serviceState) {
            is VPNState -> serviceState
            else -> VPNState.Disconnected
        }
    }

    /**
     * NAME: convertToServerInfo
     *
     * DESCRIPTION:
     *     Converts ITforceVPNService server data to ServerInfo.
     *
     * PARAMETERS:
     *     serviceServer - Service-specific server object
     *
     * RETURNS:
     *     ServerInfo - Server info
     */
    private fun convertToServerInfo(serviceServer: Any): ServerInfo {
        // The service already returns the correct ServerInfo type
        return when (serviceServer) {
            is ServerInfo -> serviceServer
            else -> ServerInfo(
                id = "unknown",
                name = "Unknown Server",
                nameEn = "Unknown Server",
                serverName = "unknown.server.com",
                serverPort = 443,
                isAuto = false,
                ping = -1,
                status = ServerStatus.Unknown
            )
        }
    }

    // MARK: - Internal Access

    /**
     * NAME: getConnectionManager
     *
     * DESCRIPTION:
     *     Get the underlying connection manager for internal operations.
     *
     * RETURNS:
     *     ConnectionManager? - Connection manager instance or null if not available
     */
    override fun getConnectionManager(): com.panabit.client.connection.ConnectionManager? {
        return try {
            injectedConnectionManager
        } catch (e: Exception) {
            logError("Failed to get connection manager", emptyMap(), e)
            null
        }
    }
}
