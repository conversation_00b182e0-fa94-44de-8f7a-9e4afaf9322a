/**
 * FILE: ProtocolState.kt
 *
 * DESCRIPTION:
 *     SDWAN protocol state machine definitions and state management.
 *     Defines protocol states and state transition logic.
 *     Compatible with Go backend state machine implementation.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.protocol.models

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

/**
 * NAME: ProtocolState
 *
 * DESCRIPTION:
 *     Enumeration of SDWAN protocol states.
 *     Values match Go backend state constants for compatibility.
 *     Supports Flutter UI state mapping through protocolValue.
 *
 * PROPERTIES:
 *     value - UByte value matching Go backend constants
 *     protocolValue - Value for Flutter UI compatibility
 *     description - Human-readable state description
 */
enum class ProtocolState(val value: UByte, val protocolValue: UByte) {
    FREE(0u, 6u),           // Uninitialized state
    INIT(1u, 2u),           // DNS resolution in progress
    INIT1(2u, 3u),          // DNS resolved, ready for authentication
    AUTH(3u, 4u),           // Authentication in progress
    DATA(4u, 5u),           // Data transmission state (connected)
    CLOSED(5u, 6u),         // Connection closed
    AUTH_FAIL(6u, 7u);      // Authentication failed

    /**
     * NAME: description
     *
     * DESCRIPTION:
     *     Provides human-readable description of protocol state.
     *
     * RETURNS:
     *     String - State description
     */
    val description: String
        get() = when (this) {
            FREE -> "FREE"
            INIT -> "INIT"
            INIT1 -> "INIT1"
            AUTH -> "AUTH"
            DATA -> "DATA"
            CLOSED -> "CLOSED"
            AUTH_FAIL -> "AUTH_FAIL"
        }

    /**
     * NAME: isConnected
     *
     * DESCRIPTION:
     *     Checks if state represents a connected state.
     *
     * RETURNS:
     *     Boolean - true if connected, false otherwise
     */
    val isConnected: Boolean
        get() = this == DATA

    /**
     * NAME: isConnecting
     *
     * DESCRIPTION:
     *     Checks if state represents a connecting state.
     *
     * RETURNS:
     *     Boolean - true if connecting, false otherwise
     */
    val isConnecting: Boolean
        get() = this == INIT || this == INIT1 || this == AUTH

    /**
     * NAME: isDisconnected
     *
     * DESCRIPTION:
     *     Checks if state represents a disconnected state.
     *
     * RETURNS:
     *     Boolean - true if disconnected, false otherwise
     */
    val isDisconnected: Boolean
        get() = this == FREE || this == CLOSED || this == AUTH_FAIL

    companion object {
        /**
         * NAME: fromValue
         *
         * DESCRIPTION:
         *     Creates ProtocolState from raw value.
         *
         * PARAMETERS:
         *     value - Raw UByte value
         *
         * RETURNS:
         *     ProtocolState? - Protocol state if valid, null otherwise
         */
        fun fromValue(value: UByte): ProtocolState? {
            return values().find { it.value == value }
        }

        /**
         * NAME: fromProtocolValue
         *
         * DESCRIPTION:
         *     Creates ProtocolState from protocol value (for Flutter compatibility).
         *
         * PARAMETERS:
         *     protocolValue - Protocol value from Flutter UI
         *
         * RETURNS:
         *     ProtocolState? - Protocol state if valid, null otherwise
         */
        fun fromProtocolValue(protocolValue: UByte): ProtocolState? {
            return values().find { it.protocolValue == protocolValue }
        }
    }
}

/**
 * NAME: StateTransition
 *
 * DESCRIPTION:
 *     Represents a state transition with reason and timestamp.
 *     Used for state machine logging and debugging.
 *
 * PROPERTIES:
 *     fromState - Previous state
 *     toState - New state
 *     reason - Reason for transition
 *     timestamp - When transition occurred
 */
data class StateTransition(
    val fromState: ProtocolState,
    val toState: ProtocolState,
    val reason: String,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * NAME: ProtocolStateMachine
 *
 * DESCRIPTION:
 *     State machine for SDWAN protocol state management.
 *     Handles state transitions and validation.
 *     Maintains state history for debugging.
 *
 * PROPERTIES:
 *     currentState - Current protocol state
 *     sessionID - Current session ID
 *     token - Current authentication token
 *     lastHeartbeat - Timestamp of last heartbeat
 *     stateHistory - History of state transitions
 */
class ProtocolStateMachine {
    private var currentState: ProtocolState = ProtocolState.FREE
    private var sessionID: UShort = 0u
    private var token: UInt = 0u
    private var lastHeartbeat: Long = 0L
    private val stateHistory = mutableListOf<StateTransition>()

    /**
     * NAME: getState
     *
     * DESCRIPTION:
     *     Gets the current protocol state.
     *
     * RETURNS:
     *     ProtocolState - Current state
     */
    fun getState(): ProtocolState = currentState

    /**
     * NAME: setState
     *
     * DESCRIPTION:
     *     Sets new protocol state with transition logging.
     *
     * PARAMETERS:
     *     newState - New state to transition to
     *     reason - Reason for state change
     */
    fun setState(newState: ProtocolState, reason: String = "State change") {
        if (newState != currentState) {
            val transition = StateTransition(currentState, newState, reason)
            stateHistory.add(transition)

            // Keep only last 100 transitions to prevent memory growth
            if (stateHistory.size > 100) {
                stateHistory.removeAt(0)
            }

            currentState = newState
        }
    }

    /**
     * NAME: getSessionInfo
     *
     * DESCRIPTION:
     *     Gets current session information.
     *
     * RETURNS:
     *     Pair<UShort, UInt> - Session ID and token
     */
    fun getSessionInfo(): Pair<UShort, UInt> = Pair(sessionID, token)

    /**
     * NAME: setSessionInfo
     *
     * DESCRIPTION:
     *     Sets session information after successful authentication.
     *
     * PARAMETERS:
     *     sessionID - New session ID
     *     token - New authentication token
     */
    fun setSessionInfo(sessionID: UShort, token: UInt) {
        this.sessionID = sessionID
        this.token = token

        logInfo("Session info updated", mapOf(
            "sessionID" to sessionID.toString(),
            "token" to token.toString()
        ))
    }

    /**
     * NAME: clearSession
     *
     * DESCRIPTION:
     *     Clears session information on disconnect.
     */
    fun clearSession() {
        sessionID = 0u
        token = 0u
        lastHeartbeat = 0L
    }

    /**
     * NAME: updateHeartbeat
     *
     * DESCRIPTION:
     *     Updates last heartbeat timestamp.
     */
    fun updateHeartbeat() {
        lastHeartbeat = System.currentTimeMillis()
    }

    /**
     * NAME: getLastHeartbeat
     *
     * DESCRIPTION:
     *     Gets timestamp of last heartbeat.
     *
     * RETURNS:
     *     Long - Last heartbeat timestamp
     */
    fun getLastHeartbeat(): Long = lastHeartbeat

    /**
     * NAME: reset
     *
     * DESCRIPTION:
     *     Resets state machine to initial state.
     */
    fun reset() {
        setState(ProtocolState.FREE, "State machine reset")
        clearSession()
    }
}
