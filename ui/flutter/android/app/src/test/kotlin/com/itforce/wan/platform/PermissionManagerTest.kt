/**
 * FILE: PermissionManagerTest.kt
 *
 * DESCRIPTION:
 *     Unit tests for PermissionManager VPN permission functionality.
 *     Tests permission checking, request flow, and callback handling.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.itforce.wan.platform

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.VpnService
import android.os.PowerManager
import io.mockk.*
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

class PermissionManagerTest {

    private lateinit var context: Context
    private lateinit var activity: Activity
    private lateinit var powerManager: PowerManager
    private lateinit var permissionManager: PermissionManager

    @Before
    fun setup() {
        context = mockk(relaxed = true)
        activity = mockk(relaxed = true)
        powerManager = mockk(relaxed = true)
        
        every { context.getSystemService(Context.POWER_SERVICE) } returns powerManager
        every { context.packageName } returns "com.itforce.wan"
        
        permissionManager = PermissionManager(context)
    }

    @Test
    fun `checkVPNPermissionStatus returns AUTHORIZED when VpnService prepare returns null`() {
        // Given
        mockkStatic(VpnService::class)
        every { VpnService.prepare(context) } returns null

        // When
        val result = permissionManager.checkVPNPermissionStatus()

        // Then
        assertTrue(result.granted)
        assertEquals(VPNPermissionStatus.AUTHORIZED, result.status)
        assertEquals("VPN权限已授权", result.userMessage)
    }

    @Test
    fun `checkVPNPermissionStatus returns NOT_DETERMINED when VpnService prepare returns intent`() {
        // Given
        val mockIntent = mockk<Intent>()
        mockkStatic(VpnService::class)
        every { VpnService.prepare(context) } returns mockIntent

        // When
        val result = permissionManager.checkVPNPermissionStatus()

        // Then
        assertFalse(result.granted)
        assertEquals(VPNPermissionStatus.NOT_DETERMINED, result.status)
        assertEquals("需要请求VPN权限才能建立连接", result.userMessage)
    }

    @Test
    fun `checkVPNPermissionStatus handles SecurityException`() {
        // Given
        mockkStatic(VpnService::class)
        every { VpnService.prepare(context) } throws SecurityException("VPN restricted")

        // When
        val result = permissionManager.checkVPNPermissionStatus()

        // Then
        assertFalse(result.granted)
        assertEquals(VPNPermissionStatus.RESTRICTED, result.status)
        assertEquals("VPN功能被系统限制，请检查设备管理策略", result.userMessage)
        assertEquals("VPN restricted", result.error)
    }

    @Test
    fun `requestVPNPermission returns success when permission already granted`() = runTest {
        // Given
        mockkStatic(VpnService::class)
        every { VpnService.prepare(context) } returns null

        // When
        val result = permissionManager.requestVPNPermission(activity)

        // Then
        assertTrue(result.isSuccess)
        val vpnResult = result.getOrNull()!!
        assertTrue(vpnResult.granted)
        assertEquals(VPNPermissionStatus.AUTHORIZED, vpnResult.status)
    }

    @Test
    fun `handleActivityResult processes VPN permission correctly`() {
        // Given
        var callbackResult: Boolean? = null
        
        // Simulate setting up a callback (this would normally be done in requestVPNPermission)
        val permissionManagerClass = PermissionManager::class.java
        val callbackField = permissionManagerClass.getDeclaredField("vpnPermissionCallback")
        callbackField.isAccessible = true
        callbackField.set(null) { result: Boolean -> callbackResult = result }

        // When
        permissionManager.handleActivityResult(1001, Activity.RESULT_OK)

        // Then
        assertEquals(true, callbackResult)
    }

    @Test
    fun `handleActivityResult processes VPN permission denial correctly`() {
        // Given
        var callbackResult: Boolean? = null
        
        // Simulate setting up a callback
        val permissionManagerClass = PermissionManager::class.java
        val callbackField = permissionManagerClass.getDeclaredField("vpnPermissionCallback")
        callbackField.isAccessible = true
        callbackField.set(null) { result: Boolean -> callbackResult = result }

        // When
        permissionManager.handleActivityResult(1001, Activity.RESULT_CANCELED)

        // Then
        assertEquals(false, callbackResult)
    }
}
