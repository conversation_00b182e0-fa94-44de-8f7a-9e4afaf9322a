package com.itforce.wan.integration

import android.content.Context
import com.itforce.wan.infrastructure.RoutingConfigurationManager
import com.itforce.wan.infrastructure.RoutingConfiguration
import com.itforce.wan.platform.models.RoutingMode
import com.itforce.wan.platform.VPNInterfaceManager
import com.itforce.wan.connection.ConnectionManager
import com.itforce.wan.connection.ServerManager
import com.itforce.wan.presentation.flutter.PlatformChannelHandler
import com.itforce.wan.domain.interfaces.VPNServiceInterface
import io.flutter.plugin.common.MethodChannel
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.coEvery
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * NAME: RoutingSettingsIntegrationTest
 *
 * DESCRIPTION:
 *     Integration tests for complete routing settings functionality.
 *     Tests the full flow from Flutter UI to VPN connection with routing configuration.
 *
 * TEST COVERAGE:
 *     - End-to-end routing configuration flow
 *     - Cross-platform compatibility verification
 *     - Memory storage and VPN application integration
 *     - Error handling and edge cases
 */
class RoutingSettingsIntegrationTest {

    private lateinit var routingConfigurationManager: RoutingConfigurationManager
    private lateinit var mockContext: Context
    private lateinit var mockServerManager: ServerManager
    private lateinit var mockServiceScope: CoroutineScope
    private lateinit var mockVpnServiceInterface: VPNServiceInterface
    private lateinit var mockMethodChannelResult: MethodChannel.Result
    private lateinit var connectionManager: ConnectionManager
    private lateinit var platformChannelHandler: PlatformChannelHandler

    @Before
    fun setUp() {
        // Create real RoutingConfigurationManager (no mocking for core functionality)
        routingConfigurationManager = RoutingConfigurationManager()

        // Mock dependencies
        mockContext = mockk()
        mockServerManager = mockk()
        mockServiceScope = mockk()
        mockVpnServiceInterface = mockk()
        mockMethodChannelResult = mockk()

        // Setup basic mock behavior
        every { mockMethodChannelResult.success(any()) } returns Unit
        every { mockMethodChannelResult.error(any(), any(), any()) } returns Unit

        // Create real components with mocked dependencies
        connectionManager = ConnectionManager(
            mockContext,
            mockServerManager,
            mockServiceScope,
            routingConfigurationManager
        )

        platformChannelHandler = PlatformChannelHandler(
            mockVpnServiceInterface,
            mockContext,
            routingConfigurationManager
        )
    }

    @Test
    fun `test complete routing settings flow - ALL mode`() = runTest {
        // Step 1: Set routing settings via Method Channel (simulating Flutter call)
        val allModeSettings = mapOf(
            "mode" to "all",
            "custom_routes" to ""
        )

        // Call setRoutingSettings using reflection
        val setMethod = PlatformChannelHandler::class.java.getDeclaredMethod(
            "handleSetRoutingSettings",
            Any::class.java,
            MethodChannel.Result::class.java
        )
        setMethod.isAccessible = true
        setMethod.invoke(platformChannelHandler, allModeSettings, mockMethodChannelResult)

        // Verify settings were stored in memory
        val storedConfig = routingConfigurationManager.getCurrentRoutingConfiguration()
        assertEquals("Mode should be ALL", RoutingMode.ALL, storedConfig.mode)
        assertTrue("Custom routes should be empty", storedConfig.customRoutes.isEmpty())

        // Step 2: Verify settings can be retrieved via Method Channel
        val getMethod = PlatformChannelHandler::class.java.getDeclaredMethod(
            "handleGetRoutingSettings",
            MethodChannel.Result::class.java
        )
        getMethod.isAccessible = true
        getMethod.invoke(platformChannelHandler, mockMethodChannelResult)

        // Verify Method Channel responses
        verify { 
            mockMethodChannelResult.success(
                mapOf(
                    "success" to true,
                    "message" to "Routing settings updated successfully"
                )
            )
        }
        verify { 
            mockMethodChannelResult.success(
                mapOf(
                    "mode" to "all",
                    "custom_routes" to ""
                )
            )
        }
    }

    @Test
    fun `test complete routing settings flow - CUSTOM mode`() = runTest {
        // Step 1: Set custom routing settings
        val customModeSettings = mapOf(
            "mode" to "custom",
            "custom_routes" to "192.168.1.0/24,10.0.0.0/8,172.16.0.0/12"
        )

        // Call setRoutingSettings
        val setMethod = PlatformChannelHandler::class.java.getDeclaredMethod(
            "handleSetRoutingSettings",
            Any::class.java,
            MethodChannel.Result::class.java
        )
        setMethod.isAccessible = true
        setMethod.invoke(platformChannelHandler, customModeSettings, mockMethodChannelResult)

        // Verify settings were stored correctly
        val storedConfig = routingConfigurationManager.getCurrentRoutingConfiguration()
        assertEquals("Mode should be CUSTOM", RoutingMode.CUSTOM, storedConfig.mode)
        assertEquals("Should have 3 custom routes", 3, storedConfig.customRoutes.size)
        assertTrue("Should contain first route", storedConfig.customRoutes.contains("192.168.1.0/24"))
        assertTrue("Should contain second route", storedConfig.customRoutes.contains("10.0.0.0/8"))
        assertTrue("Should contain third route", storedConfig.customRoutes.contains("172.16.0.0/12"))

        // Step 2: Verify RouteInfo conversion
        val routeInfoList = storedConfig.toRouteInfoList()
        assertEquals("Should convert to 3 RouteInfo objects", 3, routeInfoList.size)
        
        // Verify specific route conversions
        val firstRoute = routeInfoList.find { it.destination == "192.168.1.0" }
        assertNotNull("First route should be converted", firstRoute)
        assertEquals("First route prefix should be 24", 24, firstRoute?.prefixLength)

        val secondRoute = routeInfoList.find { it.destination == "10.0.0.0" }
        assertNotNull("Second route should be converted", secondRoute)
        assertEquals("Second route prefix should be 8", 8, secondRoute?.prefixLength)
    }

    @Test
    fun `test routing configuration persistence across operations`() = runTest {
        // Step 1: Set initial configuration
        val initialConfig = RoutingConfiguration(
            RoutingMode.CUSTOM,
            listOf("192.168.1.0/24", "10.0.0.0/8")
        )
        val updateSuccess = routingConfigurationManager.updateRoutingConfiguration(initialConfig)
        assertTrue("Initial configuration should be stored", updateSuccess)

        // Step 2: Verify configuration persists
        val retrievedConfig1 = routingConfigurationManager.getCurrentRoutingConfiguration()
        assertEquals("Mode should persist", RoutingMode.CUSTOM, retrievedConfig1.mode)
        assertEquals("Routes should persist", 2, retrievedConfig1.customRoutes.size)

        // Step 3: Update configuration
        val updatedConfig = RoutingConfiguration(
            RoutingMode.ALL,
            emptyList()
        )
        val updateSuccess2 = routingConfigurationManager.updateRoutingConfiguration(updatedConfig)
        assertTrue("Updated configuration should be stored", updateSuccess2)

        // Step 4: Verify update was applied
        val retrievedConfig2 = routingConfigurationManager.getCurrentRoutingConfiguration()
        assertEquals("Mode should be updated", RoutingMode.ALL, retrievedConfig2.mode)
        assertTrue("Routes should be empty", retrievedConfig2.customRoutes.isEmpty())
    }

    @Test
    fun `test invalid routing configuration handling`() = runTest {
        // Test invalid CUSTOM mode (no routes)
        val invalidConfig = RoutingConfiguration(RoutingMode.CUSTOM, emptyList())
        val updateSuccess = routingConfigurationManager.updateRoutingConfiguration(invalidConfig)
        assertFalse("Invalid configuration should be rejected", updateSuccess)

        // Verify configuration wasn't changed
        val currentConfig = routingConfigurationManager.getCurrentRoutingConfiguration()
        assertEquals("Should remain default ALL mode", RoutingMode.ALL, currentConfig.mode)

        // Test invalid routes in CUSTOM mode
        val invalidRoutesConfig = RoutingConfiguration(
            RoutingMode.CUSTOM,
            listOf("invalid-route", "192.168.1.0/24", "another-invalid")
        )
        val updateSuccess2 = routingConfigurationManager.updateRoutingConfiguration(invalidRoutesConfig)
        assertFalse("Configuration with invalid routes should be rejected", updateSuccess2)
    }

    @Test
    fun `test Method Channel error handling`() = runTest {
        // Test null arguments
        val setMethod = PlatformChannelHandler::class.java.getDeclaredMethod(
            "handleSetRoutingSettings",
            Any::class.java,
            MethodChannel.Result::class.java
        )
        setMethod.isAccessible = true
        setMethod.invoke(platformChannelHandler, null, mockMethodChannelResult)

        // Verify error response
        verify { 
            mockMethodChannelResult.error(
                "INVALID_ARGUMENTS",
                "Routing settings arguments cannot be null",
                null
            )
        }

        // Test invalid settings (CUSTOM mode without routes)
        val invalidSettings = mapOf(
            "mode" to "custom",
            "custom_routes" to ""
        )
        setMethod.invoke(platformChannelHandler, invalidSettings, mockMethodChannelResult)

        // Verify update failure response
        verify { 
            mockMethodChannelResult.error(
                "UPDATE_FAILED",
                "Failed to update routing settings",
                null
            )
        }
    }

    @Test
    fun `test cross-platform compatibility`() = runTest {
        // Test iOS-compatible settings format
        val iosCompatibleSettings = mapOf(
            "mode" to "custom",
            "custom_routes" to "192.168.1.0/24,10.0.0.0/8"
        )

        // Update configuration
        val updateSuccess = routingConfigurationManager.updateFromFlutterSettings(iosCompatibleSettings)
        assertTrue("iOS-compatible settings should be accepted", updateSuccess)

        // Verify Flutter-compatible output
        val flutterMap = routingConfigurationManager.toFlutterSettingsMap()
        assertEquals("Mode should match iOS format", "custom", flutterMap["mode"])
        assertEquals("Routes should match iOS format", "192.168.1.0/24,10.0.0.0/8", flutterMap["custom_routes"])

        // Test case insensitivity
        val caseInsensitiveSettings = mapOf(
            "mode" to "CUSTOM",
            "custom_routes" to "192.168.1.0/24"
        )
        val updateSuccess2 = routingConfigurationManager.updateFromFlutterSettings(caseInsensitiveSettings)
        assertTrue("Case insensitive mode should be accepted", updateSuccess2)

        val config = routingConfigurationManager.getCurrentRoutingConfiguration()
        assertEquals("Mode should be parsed correctly", RoutingMode.CUSTOM, config.mode)
    }

    @Test
    fun `test thread safety of routing configuration`() = runTest {
        // This test verifies that concurrent access to routing configuration is safe
        val config1 = RoutingConfiguration(RoutingMode.CUSTOM, listOf("192.168.1.0/24"))
        val config2 = RoutingConfiguration(RoutingMode.ALL, emptyList())

        // Simulate concurrent updates (in real scenario, these would be on different threads)
        val update1 = routingConfigurationManager.updateRoutingConfiguration(config1)
        val update2 = routingConfigurationManager.updateRoutingConfiguration(config2)

        // Both updates should succeed
        assertTrue("First update should succeed", update1)
        assertTrue("Second update should succeed", update2)

        // Final state should be consistent (last update wins)
        val finalConfig = routingConfigurationManager.getCurrentRoutingConfiguration()
        assertEquals("Final mode should be ALL", RoutingMode.ALL, finalConfig.mode)
    }
}
