/**
 * FILE: CrossPlatformProtocolTest.kt
 *
 * DESCRIPTION:
 *     Cross-platform protocol compatibility test.
 *     Parses Go-generated packets and generates Android packets for Go validation.
 *     Ensures protocol compatibility between Go backend and Android implementation.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create cross-platform protocol test
 */

package com.itforce.wan.protocol

import org.junit.Test
import org.junit.Assert.*
import org.junit.Before
import java.io.File
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.security.MessageDigest

import com.itforce.wan.protocol.models.*
import com.itforce.wan.protocol.SDWANProtocol

class CrossPlatformProtocolTest {

    // Remove these as they require complex dependencies
    
    private val testDataDir = File("/Users/<USER>/Documents/mobile/tools/test_data")
    private val goToAndroidDir = File(testDataDir, "go_to_android")
    private val androidToGoDir = File(testDataDir, "android_to_go")

    @Before
    fun setUp() {
        // Create test directories
        createTestDirectories()
    }

    private fun createTestDirectories() {
        testDataDir.mkdirs()
        goToAndroidDir.mkdirs()
        androidToGoDir.mkdirs()
    }

    /**
     * Test parsing Go-generated packets
     */
    @Test
    fun testParseGoGeneratedPackets() {
        println("🔍 Testing Android parsing of Go-generated packets...")
        
        if (!goToAndroidDir.exists() || goToAndroidDir.listFiles()?.isEmpty() == true) {
            println("⚠️  No Go packets found. Run Go validator first.")
            return
        }
        
        var successCount = 0
        var totalCount = 0
        
        goToAndroidDir.listFiles { _, name -> name.endsWith(".bin") }?.forEach { file ->
            totalCount++
            try {
                val packetData = file.readBytes()
                val result = parseGoPacket(file.name, packetData)
                
                if (result) {
                    println("✅ ${file.name}: Parsed successfully")
                    successCount++
                } else {
                    println("❌ ${file.name}: Parse failed")
                }
            } catch (e: Exception) {
                println("❌ ${file.name}: Exception - ${e.message}")
            }
        }
        
        println("📊 Go Packet Parsing: $successCount/$totalCount successful")
        assertTrue("Should parse at least some Go packets", successCount > 0)
    }

    /**
     * Test generating Android packets for Go validation
     */
    @Test
    fun testGenerateAndroidPackets() {
        println("🔧 Generating Android packets for Go validation...")
        
        val testPackets = listOf(
            generateOpenPacket(),
            generateOpenAckPacket(),
            generateOpenRejectPacket(),
            generateEchoRequestPacket(),
            generateEchoResponsePacket(),
            generateClosePacket(),
            generateDataPacket(),
            generateEncryptedDataPacket()
        )
        
        testPackets.forEachIndexed { index, (name, description, data) ->
            val filename = "android_packet_%02d_$name.bin".format(index + 1)
            val file = File(androidToGoDir, filename)
            
            try {
                file.writeBytes(data)
                println("✅ Generated: $filename (${data.size} bytes) - $description")
                
                // Also generate metadata
                val metadataFile = File(androidToGoDir, "$filename.meta")
                val metadata = "Name: $name\nDescription: $description\nSize: ${data.size} bytes\n"
                metadataFile.writeText(metadata)
                
            } catch (e: Exception) {
                println("❌ Failed to generate $filename: ${e.message}")
            }
        }
        
        println("📊 Android packet generation complete")
    }

    /**
     * Parse a Go-generated packet and validate structure
     */
    private fun parseGoPacket(filename: String, data: ByteArray): Boolean {
        if (data.size < 8) {
            println("  ❌ Packet too short: ${data.size} bytes")
            return false
        }
        
        // Parse header
        val header = parsePacketHeader(data)
        println("  📋 Type: 0x%02X, Encrypt: 0x%02X, SID: %d, Token: 0x%08X".format(
            header.type.value.toInt(), header.encrypt.value.toInt(), 
            header.sessionID.toInt(), header.token.toInt()
        ))
        
        // Validate packet type
        val validTypes = setOf(
            PacketType.OPEN_REJECT, PacketType.OPEN_ACK, PacketType.OPEN,
            PacketType.DATA, PacketType.ECHO_REQUEST, PacketType.ECHO_RESPONSE,
            PacketType.CLOSE, PacketType.DATA_ENCRYPTED, PacketType.DATA_DUP, PacketType.DATA_ENC_DUP
        )
        
        if (header.type !in validTypes) {
            println("  ❌ Invalid packet type: 0x%02X".format(header.type.value.toInt()))
            return false
        }
        
        // For control packets, validate signature
        if (isControlPacket(header.type)) {
            if (data.size < 24) {
                println("  ❌ Control packet too short for signature: ${data.size} bytes")
                return false
            }
            
            val expectedSignature = calculateSignature(header)
            val actualSignature = data.sliceArray(8..23)
            
            if (!expectedSignature.contentEquals(actualSignature)) {
                println("  ❌ Signature mismatch")
                return false
            }
            
            println("  ✅ Signature valid")
        }
        
        // Parse TLV attributes for applicable packets
        if (header.type == PacketType.OPEN || header.type == PacketType.OPEN_ACK) {
            val tlvData = data.sliceArray(24 until data.size)
            val attributes = parseTLVAttributes(tlvData)
            println("  📝 TLV Attributes: ${attributes.size}")
        }
        
        return true
    }

    /**
     * Parse packet header from byte array
     */
    private fun parsePacketHeader(data: ByteArray): PacketHeader {
        val typeValue = data[0].toUByte()
        val encryptValue = data[1].toUByte()

        return PacketHeader(
            type = PacketType.values().first { it.value == typeValue },
            encrypt = EncryptionMethod.values().first { it.value == encryptValue },
            sessionID = ((data[2].toInt() and 0xFF) shl 8 or (data[3].toInt() and 0xFF)).toUShort(),
            token = ((data[4].toInt() and 0xFF) shl 24 or
                    (data[5].toInt() and 0xFF) shl 16 or
                    (data[6].toInt() and 0xFF) shl 8 or
                    (data[7].toInt() and 0xFF)).toUInt()
        )
    }

    /**
     * Check if packet type is a control packet (has signature)
     */
    private fun isControlPacket(type: PacketType): Boolean {
        return type != PacketType.DATA && type != PacketType.DATA_ENCRYPTED && 
               type != PacketType.DATA_DUP && type != PacketType.DATA_ENC_DUP
    }

    /**
     * Calculate MD5(header + "mw") signature
     */
    private fun calculateSignature(header: PacketHeader): ByteArray {
        val headerBytes = headerToBytes(header)
        val salt = byteArrayOf(109, 119) // ASCII 'm', 'w'
        val signatureData = headerBytes + salt
        
        val md5 = MessageDigest.getInstance("MD5")
        return md5.digest(signatureData)
    }

    /**
     * Convert PacketHeader to byte array
     */
    private fun headerToBytes(header: PacketHeader): ByteArray {
        return byteArrayOf(
            header.type.value.toByte(),
            header.encrypt.value.toByte(),
            (header.sessionID.toInt() shr 8).toByte(),
            (header.sessionID.toInt() and 0xFF).toByte(),
            (header.token.toInt() shr 24).toByte(),
            (header.token.toInt() shr 16).toByte(),
            (header.token.toInt() shr 8).toByte(),
            (header.token.toInt() and 0xFF).toByte()
        )
    }

    /**
     * Parse TLV attributes from byte array
     */
    private fun parseTLVAttributes(data: ByteArray): List<TLVAttribute> {
        val attributes = mutableListOf<TLVAttribute>()
        var offset = 0
        
        while (offset + 2 <= data.size) {
            val typeValue = data[offset].toUByte().toUShort()
            val type = TLVAttributeType.values().firstOrNull { it.value == typeValue }
                ?: break
            val length = data[offset + 1].toInt() and 0xFF
            
            if (offset + 2 + length > data.size) break
            
            val value = data.sliceArray(offset + 2 until offset + 2 + length)
            attributes.add(TLVAttribute(type, value))
            
            offset += 2 + length
        }
        
        return attributes
    }

    // Packet generation methods

    private fun generateOpenPacket(): Triple<String, String, ByteArray> {
        val header = PacketHeader(
            type = PacketType.OPEN,
            encrypt = EncryptionMethod.XOR,
            sessionID = 0u,
            token = 0u
        )
        
        val username = "testuser"
        val password = "testpass"
        val mtu = 1500u
        
        // Create TLV attributes in correct order with ENCRYPT attribute
        val attributes = listOf(
            TLVAttribute(TLVAttributeType.MTU, mtu.toByteArray()),
            TLVAttribute(TLVAttributeType.USERNAME, username.toByteArray()),
            TLVAttribute(TLVAttributeType.PASSWORD, encryptPassword(password, username)),
            TLVAttribute(TLVAttributeType.ENCRYPT, byteArrayOf(header.encrypt.value.toByte()))
        )
        
        val packet = buildPacket(header, attributes)
        return Triple("open", "Client connection request with authentication", packet)
    }

    private fun generateOpenAckPacket(): Triple<String, String, ByteArray> {
        val header = PacketHeader(
            type = PacketType.OPEN_ACK,
            encrypt = EncryptionMethod.NONE,
            sessionID = 12345u,
            token = 0xABCDEF01u
        )
        
        val attributes = listOf(
            TLVAttribute(TLVAttributeType.IP, byteArrayOf(192.toByte(), 168.toByte(), 1, 100)),
            TLVAttribute(TLVAttributeType.GATEWAY, byteArrayOf(192.toByte(), 168.toByte(), 1, 1)),
            TLVAttribute(TLVAttributeType.NETMASK, byteArrayOf(255.toByte(), 255.toByte(), 255.toByte(), 0)),
            TLVAttribute(TLVAttributeType.DNS, byteArrayOf(8, 8, 8, 8))
        )
        
        val packet = buildPacket(header, attributes)
        return Triple("open_ack", "Server acknowledges connection with network config", packet)
    }

    private fun generateOpenRejectPacket(): Triple<String, String, ByteArray> {
        val header = PacketHeader(
            type = PacketType.OPEN_REJECT,
            encrypt = EncryptionMethod.NONE,
            sessionID = 0u,
            token = 0u
        )
        
        val packet = buildPacket(header, emptyList())
        return Triple("open_reject", "Server rejects connection request", packet)
    }

    private fun generateEchoRequestPacket(): Triple<String, String, ByteArray> {
        val header = PacketHeader(
            type = PacketType.ECHO_REQUEST,
            encrypt = EncryptionMethod.NONE,
            sessionID = 12345u,
            token = 0xABCDEF01u
        )
        
        val timestamp = System.currentTimeMillis() / 1000
        val timestampBytes = ByteBuffer.allocate(8).order(ByteOrder.BIG_ENDIAN).putLong(timestamp).array()
        
        val headerBytes = headerToBytes(header)
        val signature = calculateSignature(header)
        val packet = headerBytes + signature + timestampBytes
        
        return Triple("echo_request", "Heartbeat request with timestamp", packet)
    }

    private fun generateEchoResponsePacket(): Triple<String, String, ByteArray> {
        val header = PacketHeader(
            type = PacketType.ECHO_RESPONSE,
            encrypt = EncryptionMethod.NONE,
            sessionID = 12345u,
            token = 0xABCDEF01u
        )
        
        val timestamp = System.currentTimeMillis() / 1000
        val timestampBytes = ByteBuffer.allocate(8).order(ByteOrder.BIG_ENDIAN).putLong(timestamp).array()
        val delayInfo = byteArrayOf(0x00, 0x10) // 16ms delay
        val sdrtTag = "SDRT".toByteArray()
        
        val headerBytes = headerToBytes(header)
        val signature = calculateSignature(header)
        val packet = headerBytes + signature + timestampBytes + delayInfo + sdrtTag
        
        return Triple("echo_response", "Heartbeat response with timing info", packet)
    }

    private fun generateClosePacket(): Triple<String, String, ByteArray> {
        val header = PacketHeader(
            type = PacketType.CLOSE,
            encrypt = EncryptionMethod.NONE,
            sessionID = 12345u,
            token = 0xABCDEF01u
        )
        
        val packet = buildPacket(header, emptyList())
        return Triple("close", "Connection close request", packet)
    }

    private fun generateDataPacket(): Triple<String, String, ByteArray> {
        val header = PacketHeader(
            type = PacketType.DATA,
            encrypt = EncryptionMethod.NONE,
            sessionID = 12345u,
            token = 0xABCDEF01u
        )
        
        // Sample IPv4 packet data
        val ipv4Data = byteArrayOf(
            0x45, 0x00, 0x00, 0x3C, // Version, IHL, ToS, Total Length
            0x1C, 0x46, 0x40, 0x00, // Identification, Flags, Fragment Offset
            0x40, 0x06, 0x00, 0x00, // TTL, Protocol, Header Checksum
            0xC0.toByte(), 0xA8.toByte(), 0x01, 0x64, // Source IP
            0xC0.toByte(), 0xA8.toByte(), 0x01, 0x01  // Destination IP
        )
        
        val headerBytes = headerToBytes(header)
        val packet = headerBytes + ipv4Data
        
        return Triple("data", "IPv4 data packet", packet)
    }

    private fun generateEncryptedDataPacket(): Triple<String, String, ByteArray> {
        val header = PacketHeader(
            type = PacketType.DATA_ENCRYPTED,
            encrypt = EncryptionMethod.XOR,
            sessionID = 12345u,
            token = 0xABCDEF01u
        )
        
        // Sample IPv4 packet data
        val ipv4Data = byteArrayOf(
            0x45, 0x00, 0x00, 0x3C,
            0x1C, 0x46, 0x40, 0x00,
            0x40, 0x06, 0x00, 0x00,
            0xC0.toByte(), 0xA8.toByte(), 0x01, 0x64,
            0xC0.toByte(), 0xA8.toByte(), 0x01, 0x01
        )
        
        // Encrypt data using XOR
        val sessionKey = generateSessionKey("testuser", "testpass")
        val encryptedData = xorEncrypt(ipv4Data, sessionKey)
        
        val headerBytes = headerToBytes(header)
        val packet = headerBytes + encryptedData
        
        return Triple("data_encrypted", "XOR encrypted IPv4 data packet", packet)
    }

    // Helper methods

    private fun buildPacket(header: PacketHeader, attributes: List<TLVAttribute>): ByteArray {
        val headerBytes = headerToBytes(header)
        val signature = calculateSignature(header)
        
        val tlvBytes = attributes.flatMap { attr ->
            listOf(attr.type.value.toByte(), attr.value.size.toByte()) + attr.value.toList()
        }.toByteArray()
        
        return headerBytes + signature + tlvBytes
    }

    private fun encryptPassword(password: String, username: String): ByteArray {
        // Simplified password encryption for testing
        val key = MessageDigest.getInstance("MD5").digest(("mw$username").toByteArray())
        val paddedPassword = ByteArray(32)
        password.toByteArray().copyInto(paddedPassword, 0, 0, minOf(password.length, 32))
        
        // Simplified XOR encryption (should be AES-ECB in real implementation)
        val encrypted = ByteArray(16)
        for (i in 0 until 16) {
            encrypted[i] = (paddedPassword[i].toInt() xor key[i % 16].toInt()).toByte()
        }
        
        return encrypted
    }

    private fun generateSessionKey(username: String, password: String): ByteArray {
        return MessageDigest.getInstance("MD5").digest((username + password).toByteArray())
    }

    private fun xorEncrypt(data: ByteArray, key: ByteArray): ByteArray {
        val result = data.copyOf()
        
        // Process 8 bytes at a time using UInt32 operations (matches fixed Android implementation)
        val blockCount = result.size / 8
        
        for (i in 0 until blockCount) {
            val blockOffset = i * 8
            val dataBuffer = ByteBuffer.wrap(result, blockOffset, 8).order(ByteOrder.LITTLE_ENDIAN)
            val keyBuffer = ByteBuffer.wrap(key).order(ByteOrder.LITTLE_ENDIAN)
            
            val data32_0 = dataBuffer.getInt(0)
            val data32_1 = dataBuffer.getInt(4)
            val key32_0 = keyBuffer.getInt(0)
            val key32_1 = keyBuffer.getInt(4)
            
            dataBuffer.putInt(0, data32_0 xor key32_0)
            dataBuffer.putInt(4, data32_1 xor key32_1)
        }
        
        // Process remaining bytes
        val remainder = result.size % 8
        if (remainder > 0) {
            for (i in 0 until remainder) {
                result[result.size - remainder + i] = (result[result.size - remainder + i].toInt() xor key[i].toInt()).toByte()
            }
        }
        
        return result
    }

    private fun UInt.toByteArray(): ByteArray {
        return ByteBuffer.allocate(4).order(ByteOrder.BIG_ENDIAN).putInt(this.toInt()).array().takeLast(2).toByteArray()
    }
}
