/**
 * FILE: MinimalVerificationTest.kt
 *
 * DESCRIPTION:
 *     最小化验证测试 - 验证Android代码能否真正解析Go数据包
 *     这个测试专门验证我们修复的XOR加密问题是否真的解决了
 *
 * AUTHOR: wei
 * HISTORY: 16/07/2025 create minimal verification test
 */

package com.itforce.wan.protocol

import com.itforce.wan.protocol.models.*
import org.junit.Test
import org.junit.Assert.*
import java.io.File

class MinimalVerificationTest {

    /**
     * 最关键的测试：验证Android能否解析Go生成的真实数据包
     * 这是真正的跨平台兼容性验证
     */
    @Test
    fun testParseRealGoPackets() {
        // println("=== 验证Android解析Go真实数据包 ===")

        // 测试Go生成的OPEN_ACK数据包（包含session信息）
        val openAckFile = File("src/test/resources/go_to_android/go_openack_xor.bin")
        if (openAckFile.exists()) {
            val packetData = openAckFile.readBytes()
            // println("读取Go数据包: ${packetData.size} 字节")
            // println("原始数据: ${packetData.joinToString("") { "%02x".format(it) }}")
            
            try {
                // 使用Android代码解析Go数据包
                val packet = SDWANPacket.fromByteArray(packetData)
                
                // println("✅ Android成功解析Go数据包!")
                // println("   包类型: ${packet.header.type}")
                // println("   加密方法: ${packet.header.encrypt}")
                // println("   SessionID: 0x${packet.header.sessionID.toString(16)}")
                // println("   Token: 0x${packet.header.token.toString(16)}")
                // println("   负载大小: ${packet.data.size} 字节")
                
                // 验证关键字段
                assertEquals("包类型应该是OPEN_ACK", PacketType.OPEN_ACK, packet.header.type)
                assertEquals("加密方法应该是XOR", EncryptionMethod.XOR, packet.header.encrypt)
                assertEquals("SessionID应该是0x1234", 0x1234u, packet.header.sessionID)
                assertEquals("Token应该是0xDEADBEEF", 0xDEADBEEFu, packet.header.token)
                
            } catch (e: Exception) {
                fail("❌ Android解析Go数据包失败: ${e.message}")
            }
        } else {
            fail("❌ Go数据包文件不存在，请先运行Go生成器")
        }
    }

    /**
     * 验证XOR加密修复是否真的有效
     * 使用真实的session数据测试加解密
     */
    @Test
    fun testXOREncryptionRealFix() {
        // println("=== 验证XOR加密修复效果 ===")

        // 使用真实的测试凭据
        val username = "testuser"
        val password = "testpass"
        
        try {
            val xorEncryption = XOREncryption.createWithCredentials(username, password)
            
            // 测试关键的8字节对齐数据（这种数据最容易暴露字节序问题）
            val criticalTestData = byteArrayOf(
                0x12, 0x34, 0x56, 0x78, 0x9A.toByte(), 0xBC.toByte(), 0xDE.toByte(), 0xF0.toByte()
            )
            
            // println("原始数据: ${criticalTestData.joinToString("") { "%02x".format(it) }}")

            val encrypted = xorEncryption.encrypt(criticalTestData)
            // println("加密结果: ${encrypted.joinToString("") { "%02x".format(it) }}")

            val decrypted = xorEncryption.decrypt(encrypted)
            // println("解密结果: ${decrypted.joinToString("") { "%02x".format(it) }}")
            
            // 验证往返加解密
            assertArrayEquals("XOR加解密往返应该恢复原始数据", criticalTestData, decrypted)
            assertFalse("加密应该改变数据", criticalTestData.contentEquals(encrypted))
            
            // 验证XOR的数学性质：加密两次应该恢复原始数据
            val doubleEncrypted = xorEncryption.encrypt(encrypted)
            assertArrayEquals("XOR两次应该恢复原始数据", criticalTestData, doubleEncrypted)
            
            // println("✅ XOR加密修复验证成功!")
            
        } catch (e: Exception) {
            fail("❌ XOR加密测试失败: ${e.message}")
        }
    }

    /**
     * 验证数据包头的字节序是否正确
     * 这直接关系到与Go后端的兼容性
     */
    @Test
    fun testPacketHeaderByteOrder() {
        // println("=== 验证数据包头字节序 ===")

        try {
            // 创建一个测试包头
            val header = PacketHeader(
                type = PacketType.DATA_ENCRYPTED,
                encrypt = EncryptionMethod.XOR,
                sessionID = 0x1234u,
                token = 0xDEADBEEFu
            )

            // 序列化包头
            val headerBytes = header.toByteArray()
            // println("包头字节: ${headerBytes.joinToString("") { "%02x".format(it) }}")
            
            // 验证字节序（应该是大端序）
            assertEquals("包头应该是8字节", 8, headerBytes.size)
            assertEquals("第一个字节应该是包类型", PacketType.DATA_ENCRYPTED.value.toByte(), headerBytes[0])
            assertEquals("第二个字节应该是加密方法", EncryptionMethod.XOR.value.toByte(), headerBytes[1])
            
            // 验证SessionID的字节序（大端序）
            val sessionIDBytes = headerBytes.sliceArray(2..3)
            assertEquals("SessionID高字节", 0x12, sessionIDBytes[0].toInt() and 0xFF)
            assertEquals("SessionID低字节", 0x34, sessionIDBytes[1].toInt() and 0xFF)
            
            // 验证Token的字节序（大端序）
            val tokenBytes = headerBytes.sliceArray(4..7)
            assertEquals("Token最高字节", 0xDE.toByte(), tokenBytes[0])
            assertEquals("Token次高字节", 0xAD.toByte(), tokenBytes[1])
            assertEquals("Token次低字节", 0xBE.toByte(), tokenBytes[2])
            assertEquals("Token最低字节", 0xEF.toByte(), tokenBytes[3])
            
            // 解析回来验证
            val parsedHeader = PacketHeader.fromByteArray(headerBytes)
            assertEquals("解析的包类型应该匹配", header.type, parsedHeader.type)
            assertEquals("解析的加密方法应该匹配", header.encrypt, parsedHeader.encrypt)
            assertEquals("解析的SessionID应该匹配", header.sessionID, parsedHeader.sessionID)
            assertEquals("解析的Token应该匹配", header.token, parsedHeader.token)
            
            // println("✅ 数据包头字节序验证成功!")
            
        } catch (e: Exception) {
            fail("❌ 数据包头字节序测试失败: ${e.message}")
        }
    }

    /**
     * 验证完整的数据包创建和解析流程
     * 模拟真实的VPN数据传输场景
     */
    @Test
    fun testCompletePacketFlow() {
        // println("=== 验证完整数据包流程 ===")

        try {
            // 模拟VPN会话参数
            val sessionID = 0x1234u
            val token = 0xDEADBEEFu
            val testPayload = "Hello from Android VPN!".toByteArray()

            // 创建加密数据包
            val header = PacketHeader(
                type = PacketType.DATA_ENCRYPTED,
                encrypt = EncryptionMethod.XOR,
                sessionID = sessionID,
                token = token
            )

            val packet = SDWANPacket(header, testPayload)
            val packetBytes = packet.toByteArray()

            // println("创建的数据包: ${packetBytes.size} 字节")
            // println("包头: ${packetBytes.sliceArray(0..7).joinToString("") { "%02x".format(it) }}")
            // println("负载: ${packetBytes.sliceArray(8 until packetBytes.size).joinToString("") { "%02x".format(it) }}")
            
            // 解析数据包
            val parsedPacket = SDWANPacket.fromByteArray(packetBytes)
            
            // 验证解析结果
            assertEquals("包类型应该匹配", PacketType.DATA_ENCRYPTED, parsedPacket.header.type)
            assertEquals("加密方法应该匹配", EncryptionMethod.XOR, parsedPacket.header.encrypt)
            assertEquals("SessionID应该匹配", sessionID, parsedPacket.header.sessionID)
            assertEquals("Token应该匹配", token, parsedPacket.header.token)
            assertArrayEquals("负载应该匹配", testPayload, parsedPacket.data)
            
            // println("✅ 完整数据包流程验证成功!")
            
        } catch (e: Exception) {
            fail("❌ 完整数据包流程测试失败: ${e.message}")
        }
    }

    /**
     * 最终验证：如果这个测试通过，说明Android修复真的有效
     */
    @Test
    fun testFinalVerification() {
        // println("=== 最终验证：Android修复是否真的有效 ===")
        
        var allTestsPassed = true
        val testResults = mutableListOf<String>()
        
        try {
            // 测试1：XOR加密
            val xorEncryption = XOREncryption.createWithCredentials("admin", "123456")
            val testData = byteArrayOf(1, 2, 3, 4, 5, 6, 7, 8)
            val encrypted = xorEncryption.encrypt(testData)
            val decrypted = xorEncryption.decrypt(encrypted)
            
            if (testData.contentEquals(decrypted)) {
                testResults.add("✅ XOR加密测试通过")
            } else {
                testResults.add("❌ XOR加密测试失败")
                allTestsPassed = false
            }
            
            // 测试2：数据包解析
            val header = PacketHeader(PacketType.DATA, EncryptionMethod.NONE, 0x1111u, 0x22222222u)
            val packet = SDWANPacket(header, byteArrayOf(0xAA.toByte(), 0xBB.toByte()))
            val packetBytes = packet.toByteArray()
            val parsedPacket = SDWANPacket.fromByteArray(packetBytes)
            
            if (packet.header.sessionID == parsedPacket.header.sessionID && 
                packet.header.token == parsedPacket.header.token) {
                testResults.add("✅ 数据包解析测试通过")
            } else {
                testResults.add("❌ 数据包解析测试失败")
                allTestsPassed = false
            }
            
            // 输出结果
            // testResults.forEach { println(it) }

            if (allTestsPassed) {
                // println("🎉 最终验证：Android修复完全成功！")
                // println("   Android现在可以与Go后端正确通信")
            } else {
                fail("❌ 最终验证失败：Android修复仍有问题")
            }
            
        } catch (e: Exception) {
            fail("❌ 最终验证异常: ${e.message}")
        }
    }
}
