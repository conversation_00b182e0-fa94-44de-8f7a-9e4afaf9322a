/**
 * FILE: UDPConnectionNetworkErrorTest.kt
 *
 * DESCRIPTION:
 *     Unit tests for UDPConnection network error detection functionality.
 *     Tests ENETUNREACH and related network errors that should trigger reconnection.
 *
 * AUTHOR: wei
 * HISTORY: 24/07/2025 create
 */

package com.panabit.client.network

import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mockito.*
import java.io.IOException
import java.net.InetAddress
import java.net.SocketException

/**
 * NAME: UDPConnectionNetworkErrorTest
 *
 * DESCRIPTION:
 *     Tests network error detection in UDPConnection send and receive operations.
 *     Verifies that ENETUNREACH and related errors are properly identified.
 */
class UDPConnectionNetworkErrorTest {

    @Test
    fun `test send method detects ENETUNREACH error`() = runTest {
        // Given
        val serverAddress = InetAddress.getByName("127.0.0.1")
        val connection = UDPConnection(serverAddress, 8080)
        
        // When - simulate ENETUNREACH error during send
        val testData = "test data".toByteArray()
        
        // Note: This is a unit test to verify error detection logic
        // In real scenario, ENETUNREACH would be thrown by DatagramSocket.send()
        val networkErrors = listOf(
            "sendto failed: ENETUNREACH (Network is unreachable)",
            "Network is unreachable",
            "EHOSTUNREACH (Host is unreachable)", 
            "Host is unreachable",
            "ENETDOWN (Network is down)",
            "Network is down",
            "ECONNREFUSED (Connection refused)",
            "Connection refused",
            "EHOSTDOWN (Host is down)",
            "Host is down"
        )
        
        // Then - verify each error type is detected as network error
        networkErrors.forEach { errorMessage ->
            val exception = IOException(errorMessage)
            assertTrue("Should detect '$errorMessage' as network error", 
                isNetworkError(exception))
        }
    }

    @Test
    fun `test receive method detects network errors`() = runTest {
        // Given
        val serverAddress = InetAddress.getByName("127.0.0.1")
        val connection = UDPConnection(serverAddress, 8080)
        
        // When - simulate network errors during receive
        val networkErrors = listOf(
            SocketException("ENETUNREACH (Network is unreachable)"),
            SocketException("Network is unreachable"),
            SocketException("EHOSTUNREACH (Host is unreachable)"),
            SocketException("Host is unreachable"),
            SocketException("ENETDOWN (Network is down)"),
            SocketException("Network is down"),
            SocketException("ECONNREFUSED (Connection refused)"),
            SocketException("Connection refused"),
            SocketException("EHOSTDOWN (Host is down)"),
            SocketException("Host is down")
        )
        
        // Then - verify each error type is detected as network error
        networkErrors.forEach { exception ->
            assertTrue("Should detect '${exception.message}' as network error", 
                isNetworkError(exception))
        }
    }

    @Test
    fun `test non-network errors are not detected as network errors`() = runTest {
        // Given
        val nonNetworkErrors = listOf(
            IOException("Connection timeout"),
            SocketException("Bad file descriptor"),
            SocketException("EBADF"),
            IOException("Socket closed"),
            RuntimeException("Unknown error")
        )
        
        // Then - verify these are not detected as network errors
        nonNetworkErrors.forEach { exception ->
            assertFalse("Should not detect '${exception.message}' as network error", 
                isNetworkError(exception))
        }
    }

    /**
     * Helper method to simulate network error detection logic from UDPConnection
     */
    private fun isNetworkError(exception: Exception): Boolean {
        val errorMessage = exception.message ?: ""
        return when {
            errorMessage.contains("ENETUNREACH") -> true
            errorMessage.contains("Network is unreachable") -> true
            errorMessage.contains("EHOSTUNREACH") -> true
            errorMessage.contains("Host is unreachable") -> true
            errorMessage.contains("ENETDOWN") -> true
            errorMessage.contains("Network is down") -> true
            errorMessage.contains("ECONNREFUSED") -> true
            errorMessage.contains("Connection refused") -> true
            errorMessage.contains("EHOSTDOWN") -> true
            errorMessage.contains("Host is down") -> true
            else -> false
        }
    }
}
