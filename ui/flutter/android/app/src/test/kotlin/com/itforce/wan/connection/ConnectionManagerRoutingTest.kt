package com.itforce.wan.connection

import android.content.Context
import com.itforce.wan.infrastructure.RoutingConfigurationManager
import com.itforce.wan.infrastructure.RoutingConfiguration
import com.itforce.wan.platform.models.RoutingMode
import com.itforce.wan.platform.VPNInterfaceManager
import com.itforce.wan.platform.models.RouteInfo
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * NAME: ConnectionManagerRoutingTest
 *
 * DESCRIPTION:
 *     Unit tests for ConnectionManager routing configuration application.
 *     Tests VPN connection establishment with different routing modes.
 *
 * TEST COVERAGE:
 *     - Global routing mode application
 *     - Custom routing mode application
 *     - Route configuration reading from RoutingConfigurationManager
 *     - VPN interface configuration with routing settings
 */
class ConnectionManagerRoutingTest {

    private lateinit var mockContext: Context
    private lateinit var mockServerManager: ServerManager
    private lateinit var mockServiceScope: CoroutineScope
    private lateinit var mockRoutingConfigurationManager: RoutingConfigurationManager
    private lateinit var mockVpnInterfaceManager: VPNInterfaceManager
    private lateinit var mockConfigBuilder: VPNInterfaceManager.ConfigBuilder
    private lateinit var connectionManager: ConnectionManager

    @Before
    fun setUp() {
        // Mock dependencies
        mockContext = mockk()
        mockServerManager = mockk()
        mockServiceScope = mockk()
        mockRoutingConfigurationManager = mockk()
        mockVpnInterfaceManager = mockk()
        mockConfigBuilder = mockk()

        // Setup basic mock behavior
        every { mockVpnInterfaceManager.createConfigBuilder() } returns mockConfigBuilder
        every { mockConfigBuilder.setLocalIP(any()) } returns mockConfigBuilder
        every { mockConfigBuilder.setServerAddress(any()) } returns mockConfigBuilder
        every { mockConfigBuilder.setDNSServers(any()) } returns mockConfigBuilder
        every { mockConfigBuilder.setMTU(any()) } returns mockConfigBuilder
        every { mockConfigBuilder.useGlobalRouting() } returns mockConfigBuilder
        every { mockConfigBuilder.setRoutingMode(any()) } returns mockConfigBuilder
        every { mockConfigBuilder.addRoute(any(), any()) } returns mockConfigBuilder
        every { mockConfigBuilder.addServerExclusions(any()) } returns mockConfigBuilder
        every { mockConfigBuilder.addLocalNetworkExclusions() } returns mockConfigBuilder
        every { mockConfigBuilder.build() } returns Result.success(mockk())

        // Create ConnectionManager instance
        connectionManager = ConnectionManager(
            mockContext,
            mockServerManager,
            mockServiceScope,
            mockRoutingConfigurationManager
        )
    }

    @Test
    fun `test routing configuration retrieval for global mode`() = runTest {
        // Setup mock for global routing
        val globalConfig = RoutingConfiguration(RoutingMode.ALL, emptyList())
        every { mockRoutingConfigurationManager.getCurrentRoutingConfiguration() } returns globalConfig

        // Test that global configuration is retrieved correctly
        val retrievedConfig = mockRoutingConfigurationManager.getCurrentRoutingConfiguration()
        
        assertEquals("Mode should be ALL", RoutingMode.ALL, retrievedConfig.mode)
        assertTrue("Custom routes should be empty for global mode", retrievedConfig.customRoutes.isEmpty())
        
        verify { mockRoutingConfigurationManager.getCurrentRoutingConfiguration() }
    }

    @Test
    fun `test routing configuration retrieval for custom mode`() = runTest {
        // Setup mock for custom routing
        val customRoutes = listOf("***********/24", "10.0.0.0/8")
        val customConfig = RoutingConfiguration(RoutingMode.CUSTOM, customRoutes)
        every { mockRoutingConfigurationManager.getCurrentRoutingConfiguration() } returns customConfig

        // Test that custom configuration is retrieved correctly
        val retrievedConfig = mockRoutingConfigurationManager.getCurrentRoutingConfiguration()
        
        assertEquals("Mode should be CUSTOM", RoutingMode.CUSTOM, retrievedConfig.mode)
        assertEquals("Custom routes should match", customRoutes, retrievedConfig.customRoutes)
        
        verify { mockRoutingConfigurationManager.getCurrentRoutingConfiguration() }
    }

    @Test
    fun `test route info conversion for custom routes`() {
        // Test data
        val customRoutes = listOf("***********/24", "10.0.0.0/8", "172.16.0.0/12")
        val customConfig = RoutingConfiguration(RoutingMode.CUSTOM, customRoutes)

        // Convert to RouteInfo list
        val routeInfoList = customConfig.toRouteInfoList()

        // Verify conversion
        assertEquals("Should convert all routes", 3, routeInfoList.size)
        
        assertEquals("First route destination", "***********", routeInfoList[0].destination)
        assertEquals("First route prefix", 24, routeInfoList[0].prefixLength)
        
        assertEquals("Second route destination", "10.0.0.0", routeInfoList[1].destination)
        assertEquals("Second route prefix", 8, routeInfoList[1].prefixLength)
        
        assertEquals("Third route destination", "172.16.0.0", routeInfoList[2].destination)
        assertEquals("Third route prefix", 12, routeInfoList[2].prefixLength)
    }

    @Test
    fun `test route info conversion with invalid routes`() {
        // Test data with some invalid routes
        val customRoutes = listOf("***********/24", "invalid-route", "10.0.0.0/8", "")
        val customConfig = RoutingConfiguration(RoutingMode.CUSTOM, customRoutes)

        // Convert to RouteInfo list
        val routeInfoList = customConfig.toRouteInfoList()

        // Verify that only valid routes are converted
        assertEquals("Should convert only valid routes", 2, routeInfoList.size)
        
        assertEquals("First valid route", "***********", routeInfoList[0].destination)
        assertEquals("Second valid route", "10.0.0.0", routeInfoList[1].destination)
    }

    @Test
    fun `test configuration validation for ALL mode`() {
        // ALL mode should always be valid
        val allConfig = RoutingConfiguration(RoutingMode.ALL, emptyList())
        assertTrue("ALL mode should be valid", allConfig.isValid())

        // ALL mode with routes should still be valid
        val allConfigWithRoutes = RoutingConfiguration(RoutingMode.ALL, listOf("***********/24"))
        assertTrue("ALL mode with routes should be valid", allConfigWithRoutes.isValid())
    }

    @Test
    fun `test configuration validation for CUSTOM mode`() {
        // CUSTOM mode with valid routes should be valid
        val validCustomConfig = RoutingConfiguration(RoutingMode.CUSTOM, listOf("***********/24"))
        assertTrue("CUSTOM mode with valid routes should be valid", validCustomConfig.isValid())

        // CUSTOM mode without routes should be invalid
        val invalidCustomConfig = RoutingConfiguration(RoutingMode.CUSTOM, emptyList())
        assertFalse("CUSTOM mode without routes should be invalid", invalidCustomConfig.isValid())

        // CUSTOM mode with invalid routes should be invalid
        val invalidRoutesConfig = RoutingConfiguration(RoutingMode.CUSTOM, listOf("invalid-route"))
        assertFalse("CUSTOM mode with invalid routes should be invalid", invalidRoutesConfig.isValid())
    }

    @Test
    fun `test VPN interface manager integration`() {
        // Test that VPN interface manager methods are called correctly
        val configBuilder = mockVpnInterfaceManager.createConfigBuilder()
        
        // Verify builder creation
        assertNotNull("Config builder should be created", configBuilder)
        verify { mockVpnInterfaceManager.createConfigBuilder() }
        
        // Test method chaining
        val result = configBuilder
            .setLocalIP("********")
            .setServerAddress("server.example.com")
            .setDNSServers(listOf("*******"))
            .setMTU(1400)
            .useGlobalRouting()
            .addServerExclusions(listOf("*******"))
            .addLocalNetworkExclusions()
            .build()
        
        // Verify all methods were called
        verify { mockConfigBuilder.setLocalIP("********") }
        verify { mockConfigBuilder.setServerAddress("server.example.com") }
        verify { mockConfigBuilder.setDNSServers(listOf("*******")) }
        verify { mockConfigBuilder.setMTU(1400) }
        verify { mockConfigBuilder.useGlobalRouting() }
        verify { mockConfigBuilder.addServerExclusions(listOf("*******")) }
        verify { mockConfigBuilder.addLocalNetworkExclusions() }
        verify { mockConfigBuilder.build() }
        
        assertTrue("Build should succeed", result.isSuccess)
    }

    @Test
    fun `test custom routing configuration application`() {
        // Test custom routing mode configuration
        val configBuilder = mockVpnInterfaceManager.createConfigBuilder()
        
        // Apply custom routing
        configBuilder
            .setRoutingMode(RoutingMode.CUSTOM)
            .addRoute("***********", 24)
            .addRoute("10.0.0.0", 8)
        
        // Verify custom routing methods were called
        verify { mockConfigBuilder.setRoutingMode(RoutingMode.CUSTOM) }
        verify { mockConfigBuilder.addRoute("***********", 24) }
        verify { mockConfigBuilder.addRoute("10.0.0.0", 8) }
    }
}
