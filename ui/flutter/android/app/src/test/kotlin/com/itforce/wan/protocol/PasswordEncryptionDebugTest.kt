/**
 * FILE: PasswordEncryptionDebugTest.kt
 *
 * DESCRIPTION:
 *     Debug test for password encryption to verify compatibility with Go backend.
 *
 * AUTHOR: wei
 * HISTORY: 15/07/2025 create debug test
 */

package com.itforce.wan.protocol

import com.itforce.wan.protocol.EncryptionService.PasswordEncryption
import org.junit.Test
import org.junit.Assert.*

class PasswordEncryptionDebugTest {

    @Test
    fun testPasswordEncryptionDebug() {
        // Test the specific case that should match Go backend
        val username = "tmptest"
        val password = "itforce"
        
        // println("=== Password Encryption Debug Test ===")

        val encrypted = PasswordEncryption.encryptPassword(password, username)

        // Expected result from Go backend and iOS
        val expected = byteArrayOf(
            0xcb.toByte(), 0x7d.toByte(), 0x34.toByte(), 0x35.toByte(),
            0x27.toByte(), 0x31.toByte(), 0x73.toByte(), 0xaa.toByte(),
            0x7a.toByte(), 0x12.toByte(), 0x06.toByte(), 0x28.toByte(),
            0xc9.toByte(), 0xbc.toByte(), 0xca.toByte(), 0xaa.toByte()
        )

        // println("Expected: ${expected.joinToString(" ") { "%02x".format(it) }}")
        // println("Actual:   ${encrypted.joinToString(" ") { "%02x".format(it) }}")
        
        assertArrayEquals("Password encryption should match Go backend", expected, encrypted)
    }
    
    @Test
    fun testKeyGeneration() {
        val username = "tmptest"
        val keyManager = com.itforce.wan.protocol.KeyManager()
        val key = keyManager.generatePasswordKey(username)
        
        // println("=== Key Generation Debug ===")
        // println("Username: '$username'")
        // println("Key input: 'mw$username'")
        // println("Generated key: ${key.joinToString(" ") { "%02x".format(it) }}")
        
        // The key should be MD5("mwtmptest")
        assertEquals("Key should be 16 bytes", 16, key.size)
    }
}
