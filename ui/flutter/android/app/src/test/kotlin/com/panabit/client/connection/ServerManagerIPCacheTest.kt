/**
 * FILE: ServerManagerIPCacheTest.kt
 *
 * DESCRIPTION:
 *     Unit tests for ServerManager IP caching functionality.
 *     Tests DNS resolution caching to prevent connection failures.
 *
 * AUTHOR: wei
 * DATE: 2025-01-18
 */

package com.panabit.client.connection

import com.panabit.client.connection.models.*
import com.panabit.client.network.UDPConnection
import com.panabit.client.protocol.SDWANProtocol
import kotlinx.coroutines.*
import kotlinx.coroutines.test.*
import org.junit.Test
import org.junit.Before
import org.junit.After
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class ServerManagerIPCacheTest {

    @Mock
    private lateinit var mockContext: android.content.Context

    @Mock
    private lateinit var mockUDPConnectionFactory: (String, Int) -> UDPConnection

    @Mock
    private lateinit var mockSDWANProtocolFactory: (String, Int) -> SDWANProtocol

    private lateinit var testScope: TestScope
    private lateinit var serverManager: ServerManagerImpl

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        testScope = TestScope()
        
        serverManager = ServerManagerImpl(
            context = mockContext,
            udpConnectionFactory = mockUDPConnectionFactory,
            sdwanProtocolFactory = mockSDWANProtocolFactory,
            serviceScope = testScope,
            configuration = ServerConfiguration.default
        )
    }

    @After
    fun tearDown() {
        testScope.cancel()
    }

    @Test
    fun `test IP caching during server list update`() = testScope.runTest {
        // Given: A list of servers with hostnames
        val servers = listOf(
            ServerInfo(
                id = "1",
                name = "Test Server 1",
                nameEn = "Test Server 1",
                serverName = "test1.example.com",
                serverPort = 8000,
                isAuto = false
            ),
            ServerInfo(
                id = "2", 
                name = "Test Server 2",
                nameEn = "Test Server 2",
                serverName = "*************", // Already an IP
                serverPort = 8001,
                isAuto = false
            )
        )

        // When: Update server list (this should trigger IP resolution)
        serverManager.updateServerList(servers)
        
        // Allow background resolution to complete
        advanceTimeBy(1000)

        // Then: IPs should be cached
        val cachedIP1 = serverManager.getCachedServerIP("test1.example.com")
        val cachedIP2 = serverManager.getCachedServerIP("*************")
        
        // IP addresses should be cached (or at least attempted)
        // Note: In real test, we'd mock DNS resolution
        println("Cached IP for test1.example.com: $cachedIP1")
        println("Cached IP for *************: $cachedIP2")
        
        // IP address should be returned as-is
        assertEquals("*************", cachedIP2)
    }

    @Test
    fun `test resolveServerIP with caching`() = testScope.runTest {
        // Given: A hostname that needs resolution
        val hostname = "google.com"

        // When: Resolve IP first time
        val firstResolve = serverManager.resolveServerIP(hostname)
        
        // Then: Should get an IP address
        assertNotNull(firstResolve)
        
        // When: Resolve same hostname again
        val secondResolve = serverManager.resolveServerIP(hostname)
        
        // Then: Should get same cached IP
        assertEquals(firstResolve, secondResolve)
        
        // And: Should be in cache
        val cachedIP = serverManager.getCachedServerIP(hostname)
        assertEquals(firstResolve, cachedIP)
    }

    @Test
    fun `test getAllCachedServerIPs returns all cached IPs`() = testScope.runTest {
        // Given: Servers with cached IPs
        val servers = listOf(
            ServerInfo(
                id = "1",
                name = "Server 1", 
                nameEn = "Server 1",
                serverName = "***********",
                serverPort = 8000,
                isAuto = false
            ),
            ServerInfo(
                id = "2",
                name = "Server 2",
                nameEn = "Server 2", 
                serverName = "***********",
                serverPort = 8001,
                isAuto = false
            )
        )

        // When: Update server list and resolve IPs
        serverManager.updateServerList(servers)
        advanceTimeBy(1000) // Allow resolution to complete

        // Then: Should return all cached IPs
        val allCachedIPs = serverManager.getAllCachedServerIPs()
        
        println("All cached IPs: $allCachedIPs")
        assertTrue(allCachedIPs.isNotEmpty())
    }

    @Test
    fun `test IP cache prevents DNS resolution failures`() = testScope.runTest {
        // Given: A server with pre-cached IP
        val hostname = "test.example.com"
        val cachedIP = "*************"
        
        // Manually add to cache (simulating previous successful resolution)
        serverManager.resolveServerIP(hostname) // This would normally fail for fake hostname
        
        // When: Try to get cached IP
        val retrievedIP = serverManager.getCachedServerIP(hostname)
        
        // Then: Should return null for non-existent hostname
        // (In real scenario, we'd mock successful resolution first)
        println("Retrieved IP for fake hostname: $retrievedIP")
    }
}
