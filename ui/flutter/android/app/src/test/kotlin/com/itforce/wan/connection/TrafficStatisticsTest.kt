package com.itforce.wan.connection

import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Assert.*

/**
 * NAME: TrafficStatisticsTest
 *
 * DESCRIPTION:
 *     Unit tests for traffic statistics functionality in ConnectionManager.
 *     Verifies data collection, calculation, and format compatibility.
 */
class TrafficStatisticsTest {

    @Test
    fun testTrafficDataFormat() {
        // Test data format matches iOS and Flutter expectations
        val trafficData = mapOf(
            "upload_speed" to 1024L,
            "download_speed" to 2048L,
            "total_upload" to 10240L,
            "total_download" to 20480L,
            "timestamp" to (System.currentTimeMillis() / 1000)
        )

        // Verify all required fields are present
        assertTrue("upload_speed field missing", trafficData.containsKey("upload_speed"))
        assertTrue("download_speed field missing", trafficData.containsKey("download_speed"))
        assertTrue("total_upload field missing", trafficData.containsKey("total_upload"))
        assertTrue("total_download field missing", trafficData.containsKey("total_download"))
        assertTrue("timestamp field missing", trafficData.containsKey("timestamp"))

        // Verify data types
        assertTrue("upload_speed should be Long", trafficData["upload_speed"] is Long)
        assertTrue("download_speed should be Long", trafficData["download_speed"] is Long)
        assertTrue("total_upload should be Long", trafficData["total_upload"] is Long)
        assertTrue("total_download should be Long", trafficData["total_download"] is Long)
        assertTrue("timestamp should be Long", trafficData["timestamp"] is Long)
    }

    @Test
    fun testEventFormat() {
        // Test event format matches Flutter expectations
        val trafficData = mapOf(
            "upload_speed" to 1024L,
            "download_speed" to 2048L,
            "total_upload" to 10240L,
            "total_download" to 20480L,
            "timestamp" to (System.currentTimeMillis() / 1000)
        )

        val event = mapOf(
            "event" to "traffic",
            "data" to trafficData
        )

        // Verify event structure
        assertEquals("Event type should be 'traffic'", "traffic", event["event"])
        assertTrue("Event data should be a Map", event["data"] is Map<*, *>)

        val eventData = event["data"] as Map<*, *>
        assertEquals("Event data should match traffic data", trafficData, eventData)
    }

    @Test
    fun testSpeedCalculation() {
        // Test speed calculation logic
        val previousBytes = 1000L
        val currentBytes = 3000L
        val durationSeconds = 2.0

        val expectedSpeed = ((currentBytes - previousBytes) / durationSeconds).toLong()
        val actualSpeed = ((currentBytes - previousBytes) / durationSeconds).toLong()

        assertEquals("Speed calculation should be correct", expectedSpeed, actualSpeed)
        assertEquals("Speed should be 1000 bytes/second", 1000L, actualSpeed)
    }

    @Test
    fun testTimestampFormat() {
        // Test timestamp format (Unix seconds)
        val timestamp = System.currentTimeMillis() / 1000
        val currentTime = System.currentTimeMillis() / 1000

        // Timestamp should be within 1 second of current time
        assertTrue("Timestamp should be recent", Math.abs(timestamp - currentTime) <= 1)
    }

    @Test
    fun testDataCompatibilityWithiOS() {
        // Test that Android data format is compatible with iOS format
        val androidData = mapOf(
            "upload_speed" to 1024L,
            "download_speed" to 2048L,
            "total_upload" to 10240L,
            "total_download" to 20480L,
            "timestamp" to 1640995200L // Example timestamp
        )

        // Simulate iOS data format (should be identical)
        val iosData = mapOf(
            "upload_speed" to 1024L,
            "download_speed" to 2048L,
            "total_upload" to 10240L,
            "total_download" to 20480L,
            "timestamp" to 1640995200L
        )

        assertEquals("Android and iOS data formats should be identical", iosData, androidData)
    }

    @Test
    fun testFlutterCompatibility() {
        // Test that data can be parsed by Flutter TrafficStats.fromJson
        val trafficData = mapOf(
            "upload_speed" to 1024,
            "download_speed" to 2048,
            "total_upload" to 10240,
            "total_download" to 20480,
            "timestamp" to 1640995200
        )

        // Verify all fields can be parsed as integers (Flutter expects int)
        assertTrue("upload_speed should be parseable as int", trafficData["upload_speed"] is Int)
        assertTrue("download_speed should be parseable as int", trafficData["download_speed"] is Int)
        assertTrue("total_upload should be parseable as int", trafficData["total_upload"] is Int)
        assertTrue("total_download should be parseable as int", trafficData["total_download"] is Int)
        assertTrue("timestamp should be parseable as int", trafficData["timestamp"] is Int)
    }
}
