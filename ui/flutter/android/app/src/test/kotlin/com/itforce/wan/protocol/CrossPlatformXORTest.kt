/**
 * FILE: CrossPlatformXORTest.kt
 *
 * DESCRIPTION:
 *     Cross-platform compatibility test for XOR encryption.
 *     Verifies that Android XOR encryption produces results compatible with iOS and Go backend.
 *     Tests the fix for byte order consistency issues.
 *
 * AUTHOR: wei
 * HISTORY: 16/07/2025 create cross-platform XOR compatibility test
 */

package com.itforce.wan.protocol

import org.junit.Test
import org.junit.Assert.*
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.security.MessageDigest

class CrossPlatformXORTest {

    /**
     * Test XOR encryption with known test vectors that should match iOS implementation
     */
    @Test
    fun testXOREncryptionCrossPlatformCompatibility() {
        // Use the same test credentials as iOS tests
        val username = "testuser"
        val password = "testpass"
        
        // Create XOR encryption service
        val xorEncryption = XOREncryption.createWithCredentials(username, password)
        
        // Test data that matches iOS test vectors
        val testData = byteArrayOf(
            0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
            0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10
        )
        
        // Encrypt and decrypt
        val encrypted = xorEncryption.encrypt(testData)
        val decrypted = xorEncryption.decrypt(encrypted)
        
        // Verify round-trip works
        assertArrayEquals("Round-trip encryption should restore original data", testData, decrypted)
        
        // Verify encryption actually changes the data
        assertFalse("Encrypted data should differ from original", testData.contentEquals(encrypted))
        
        // Print results for manual cross-platform verification
        println("=== Cross-Platform XOR Test Results ===")
        println("Username: $username")
        println("Password: $password")
        println("Original:  ${testData.joinToString("") { "%02x".format(it) }}")
        println("Encrypted: ${encrypted.joinToString("") { "%02x".format(it) }}")
        println("Decrypted: ${decrypted.joinToString("") { "%02x".format(it) }}")
        
        // Calculate and print session key for verification
        val sessionKeyData = (username + password).toByteArray()
        val md5 = MessageDigest.getInstance("MD5")
        val sessionKey = md5.digest(sessionKeyData)
        println("Session Key: ${sessionKey.joinToString("") { "%02x".format(it) }}")
    }

    /**
     * Test XOR encryption with various data sizes to ensure consistency
     */
    @Test
    fun testXOREncryptionVariousDataSizes() {
        val xorEncryption = XOREncryption.createWithCredentials("admin", "123456")
        
        // Test different data sizes
        val testSizes = listOf(1, 3, 7, 8, 9, 15, 16, 17, 24, 32)
        
        for (size in testSizes) {
            val testData = ByteArray(size) { (it % 256).toByte() }
            
            val encrypted = xorEncryption.encrypt(testData)
            val decrypted = xorEncryption.decrypt(encrypted)
            
            assertArrayEquals("Round-trip failed for size $size", testData, decrypted)
            
            if (size > 0) {
                assertFalse("Encryption should change data for size $size", testData.contentEquals(encrypted))
            }
        }
    }

    /**
     * Test that verifies the byte order fix
     * This test specifically checks that we're using native byte order correctly
     */
    @Test
    fun testByteOrderFix() {
        val username = "testuser"
        val password = "testpass"
        
        // Generate session key
        val sessionKeyData = (username + password).toByteArray()
        val md5 = MessageDigest.getInstance("MD5")
        val sessionKey = md5.digest(sessionKeyData)
        
        // Test data: 8 bytes that would reveal byte order issues
        val testData = byteArrayOf(0x12, 0x34, 0x56, 0x78, 0x9A.toByte(), 0xBC.toByte(), 0xDE.toByte(), 0xF0.toByte())
        
        // Method 1: Using XOR encryption service (fixed implementation)
        val xorEncryption = XOREncryption.createWithCredentials(username, password)
        val encryptedByService = xorEncryption.encrypt(testData)
        
        // Method 2: Manual UInt32 XOR with native byte order (should match service)
        val manualResult = testData.copyOf()
        val keyBuffer = ByteBuffer.wrap(sessionKey) // Native byte order
        val dataBuffer = ByteBuffer.wrap(manualResult) // Native byte order
        
        val key32_0 = keyBuffer.getInt(0)
        val key32_1 = keyBuffer.getInt(4)
        val data32_0 = dataBuffer.getInt(0)
        val data32_1 = dataBuffer.getInt(4)
        
        val xored32_0 = data32_0 xor key32_0
        val xored32_1 = data32_1 xor key32_1
        
        dataBuffer.putInt(0, xored32_0)
        dataBuffer.putInt(4, xored32_1)
        
        // Results should match (both using native byte order)
        assertArrayEquals("Service and manual UInt32 results should match", encryptedByService, manualResult)
        
        println("=== Byte Order Fix Verification ===")
        println("Session Key: ${sessionKey.joinToString("") { "%02x".format(it) }}")
        println("Test Data:   ${testData.joinToString("") { "%02x".format(it) }}")
        println("Service:     ${encryptedByService.joinToString("") { "%02x".format(it) }}")
        println("Manual:      ${manualResult.joinToString("") { "%02x".format(it) }}")
    }

    /**
     * Test edge cases and boundary conditions
     */
    @Test
    fun testEdgeCases() {
        val xorEncryption = XOREncryption.createWithCredentials("user", "pass")
        
        // Empty data
        val emptyData = ByteArray(0)
        val encryptedEmpty = xorEncryption.encrypt(emptyData)
        assertEquals("Empty data should remain empty", 0, encryptedEmpty.size)
        
        // Single byte
        val singleByte = byteArrayOf(0x42)
        val encryptedSingle = xorEncryption.encrypt(singleByte)
        val decryptedSingle = xorEncryption.decrypt(encryptedSingle)
        assertArrayEquals("Single byte round-trip", singleByte, decryptedSingle)
        
        // 7 bytes (not aligned to 8-byte boundary)
        val sevenBytes = ByteArray(7) { it.toByte() }
        val encryptedSeven = xorEncryption.encrypt(sevenBytes)
        val decryptedSeven = xorEncryption.decrypt(encryptedSeven)
        assertArrayEquals("7-byte round-trip", sevenBytes, decryptedSeven)
        
        // Exactly 8 bytes (one block)
        val eightBytes = ByteArray(8) { it.toByte() }
        val encryptedEight = xorEncryption.encrypt(eightBytes)
        val decryptedEight = xorEncryption.decrypt(encryptedEight)
        assertArrayEquals("8-byte round-trip", eightBytes, decryptedEight)
        
        // 9 bytes (one block + 1 remainder)
        val nineBytes = ByteArray(9) { it.toByte() }
        val encryptedNine = xorEncryption.encrypt(nineBytes)
        val decryptedNine = xorEncryption.decrypt(encryptedNine)
        assertArrayEquals("9-byte round-trip", nineBytes, decryptedNine)
    }

    /**
     * Test session key generation consistency
     */
    @Test
    fun testSessionKeyConsistency() {
        val username = "testuser"
        val password = "testpass"
        
        // Create two instances with same credentials
        val encryption1 = XOREncryption.createWithCredentials(username, password)
        val encryption2 = XOREncryption.createWithCredentials(username, password)
        
        val testData = "Test data for consistency check".toByteArray()
        
        val encrypted1 = encryption1.encrypt(testData)
        val encrypted2 = encryption2.encrypt(testData)
        
        // Same credentials should produce same results
        assertArrayEquals("Same credentials should produce same encryption", encrypted1, encrypted2)
        
        // Both should decrypt correctly
        val decrypted1 = encryption1.decrypt(encrypted1)
        val decrypted2 = encryption2.decrypt(encrypted2)
        
        assertArrayEquals("First instance decryption", testData, decrypted1)
        assertArrayEquals("Second instance decryption", testData, decrypted2)
    }
}
