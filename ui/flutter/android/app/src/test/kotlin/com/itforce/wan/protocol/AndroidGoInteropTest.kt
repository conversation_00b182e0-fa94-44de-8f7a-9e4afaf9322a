/**
 * FILE: AndroidGoInteropTest.kt
 *
 * DESCRIPTION:
 *     Android-Go protocol interoperability test suite.
 *     Tests bidirectional compatibility between Android and Go backend.
 *     Validates data integrity in both directions:
 *     - Test 1: Go backend packets → Android parsing/decryption
 *     - Test 2: Android packets → Go backend validation (generates packets for Go)
 *
 * AUTHOR: wei
 * HISTORY: 16/07/2025 create Android-Go interop test
 */

package com.itforce.wan.protocol

import com.itforce.wan.protocol.models.*
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before
import java.io.File
import java.io.FileOutputStream
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.security.MessageDigest

class AndroidGoInteropTest {

    private lateinit var testResourcesDir: File
    private lateinit var goToAndroidDir: File
    private lateinit var androidToGoDir: File

    @Before
    fun setUp() {
        // Set up test directories
        testResourcesDir = File("src/test/resources")
        goToAndroidDir = File(testResourcesDir, "go_to_android")
        androidToGoDir = File(testResourcesDir, "android_to_go")
        
        // Create directories if they don't exist
        androidToGoDir.mkdirs()
    }

    /**
     * Test 1: Go backend packets → Android parsing/decryption
     * Validates that Android can correctly parse and decrypt packets generated by Go backend
     */
    @Test
    fun testGoToAndroidCompatibility() {
        // println("=== Test 1: Go Backend → Android Compatibility ===")
        
        val testCases = listOf(
            "go_open_xor.bin" to "OPEN packet with XOR encryption",
            "go_openack_xor.bin" to "OPEN_ACK packet with session info",
            "go_data_encrypted.bin" to "Encrypted data packet",
            "go_data_plain.bin" to "Plain data packet",
            "go_echo_request.bin" to "Echo request packet"
        )
        
        var successCount = 0
        
        for ((filename, description) in testCases) {
            val file = File(goToAndroidDir, filename)
            
            if (!file.exists()) {
                println("⚠️  Skipping $filename - file not found (run Go generator first)")
                continue
            }
            
            try {
                val packetData = file.readBytes()
                val result = parseAndValidateGoPacket(packetData, description)
                
                if (result) {
                    // println("✅ $filename: $description - PASSED")
                    successCount++
                } else {
                    // println("❌ $filename: $description - FAILED")
                }
                
            } catch (e: Exception) {
                // println("❌ $filename: $description - ERROR: ${e.message}")
            }
        }
        
        println("📊 Go→Android Test Summary: $successCount/${testCases.size} packets parsed successfully")
        assertTrue("At least one Go packet should be parsed successfully", successCount > 0)
    }

    /**
     * Test 2: Android packets → Go backend validation
     * Generates Android packets for Go backend to validate
     */
    @Test
    fun testAndroidToGoCompatibility() {
        // println("=== Test 2: Android → Go Backend Compatibility ===")
        
        val testCases = listOf(
            Triple("android_open_xor.bin", "OPEN packet with XOR encryption") { 
                createAndroidOpenPacket("testuser", "testpass", 1420u, EncryptionMethod.XOR) 
            },
            Triple("android_openack_xor.bin", "OPEN_ACK packet with session info") { 
                createAndroidOpenAckPacket(0x1234u, 0xDEADBEEFu, EncryptionMethod.XOR) 
            },
            Triple("android_data_encrypted.bin", "Encrypted data packet") { 
                createAndroidEncryptedDataPacket(0x1234u, 0xDEADBEEFu, EncryptionMethod.XOR) 
            },
            Triple("android_data_plain.bin", "Plain data packet") { 
                createAndroidDataPacket(0x5678u, 0x12345678u, EncryptionMethod.NONE) 
            },
            Triple("android_echo_request.bin", "Echo request packet") { 
                createAndroidEchoRequestPacket(0x1234u, 0xDEADBEEFu, EncryptionMethod.XOR) 
            }
        )
        
        var successCount = 0
        
        for ((filename, description, generator) in testCases) {
            try {
                val packetData = generator()
                val file = File(androidToGoDir, filename)
                
                // Write packet to file for Go validation
                FileOutputStream(file).use { it.write(packetData) }
                
                // Validate the packet we just generated
                val isValid = validateAndroidGeneratedPacket(packetData, description)
                
                if (isValid) {
                    // println("✅ $filename: $description - GENERATED & VALIDATED")
                    successCount++
                } else {
                    // println("❌ $filename: $description - VALIDATION FAILED")
                }
                
            } catch (e: Exception) {
                // println("❌ $filename: $description - ERROR: ${e.message}")
            }
        }
        
        println("📊 Android→Go Test Summary: $successCount/${testCases.size} packets generated successfully")
        println("📁 Generated packets in: ${androidToGoDir.absolutePath}")
        assertEquals("All Android packets should be generated successfully", testCases.size, successCount)
    }

    /**
     * Test 3: Round-trip compatibility test
     * Tests encryption/decryption consistency between Android and expected Go behavior
     */
    @Test
    fun testRoundTripCompatibility() {
        // println("=== Test 3: Round-trip Compatibility ===")
        
        val testCredentials = listOf(
            "testuser" to "testpass",
            "admin" to "123456",
            "user123" to "password123"
        )
        
        var successCount = 0
        
        for ((username, password) in testCredentials) {
            try {
                // Test XOR encryption round-trip
                val xorEncryption = XOREncryption.createWithCredentials(username, password)
                
                val testData = "Hello from Android! Testing round-trip compatibility.".toByteArray()
                val encrypted = xorEncryption.encrypt(testData)
                val decrypted = xorEncryption.decrypt(encrypted)
                
                if (testData.contentEquals(decrypted)) {
                    // println("✅ Round-trip test for $username/$password - PASSED")
                    successCount++
                } else {
                    // println("❌ Round-trip test for $username/$password - FAILED")
                }

                // Print debug info for cross-platform verification
                // println("   Original:  ${testData.joinToString("") { "%02x".format(it) }}")
                // println("   Encrypted: ${encrypted.joinToString("") { "%02x".format(it) }}")
                // println("   Decrypted: ${decrypted.joinToString("") { "%02x".format(it) }}")
                
            } catch (e: Exception) {
                // println("❌ Round-trip test for $username/$password - ERROR: ${e.message}")
            }
        }
        
        println("📊 Round-trip Test Summary: $successCount/${testCredentials.size} tests passed")
        assertEquals("All round-trip tests should pass", testCredentials.size, successCount)
    }

    private fun parseAndValidateGoPacket(packetData: ByteArray, description: String): Boolean {
        if (packetData.size < 8) {
            // println("   ERROR: Packet too short (${packetData.size} bytes)")
            return false
        }
        
        try {
            // Parse packet using Android implementation
            val packet = SDWANPacket.fromByteArray(packetData)
            
            // println("   Parsed packet: Type=${packet.header.type}, Encrypt=${packet.header.encrypt}")
            // println("   Session ID=0x${packet.header.sessionID.toString(16)}, Token=0x${packet.header.token.toString(16)}")
            // println("   Payload size=${packet.data.size} bytes")
            
            // Validate header fields
            if (!isValidPacketType(packet.header.type)) {
                // println("   ERROR: Invalid packet type: ${packet.header.type}")
                return false
            }

            if (!isValidEncryptionMethod(packet.header.encrypt)) {
                // println("   ERROR: Invalid encryption method: ${packet.header.encrypt}")
                return false
            }
            
            // For control packets, verify signature if present
            if (packet.header.type != PacketType.DATA && 
                packet.header.type != PacketType.DATA_ENCRYPTED && 
                packetData.size >= 24) {
                
                val headerBytes = packetData.sliceArray(0..7)
                val signature = packetData.sliceArray(8..23)
                val expectedSignature = calculateMD5Signature(headerBytes)
                
                if (!signature.contentEquals(expectedSignature)) {
                    // println("   ERROR: Invalid signature")
                    return false
                }
                // println("   Signature verified ✓")
            }
            
            return true
            
        } catch (e: Exception) {
            // println("   ERROR: Failed to parse packet: ${e.message}")
            return false
        }
    }

    private fun validateAndroidGeneratedPacket(packetData: ByteArray, description: String): Boolean {
        if (packetData.size < 8) {
            // println("   ERROR: Generated packet too short (${packetData.size} bytes)")
            return false
        }
        
        try {
            // Parse our own generated packet
            val packet = SDWANPacket.fromByteArray(packetData)
            
            println("   Generated packet: Type=${packet.header.type}, Encrypt=${packet.header.encrypt}")
            println("   Session ID=0x${packet.header.sessionID.toString(16)}, Token=0x${packet.header.token.toString(16)}")
            println("   Payload size=${packet.data.size} bytes")
            
            // Basic validation
            return isValidPacketType(packet.header.type) && isValidEncryptionMethod(packet.header.encrypt)
            
        } catch (e: Exception) {
            println("   ERROR: Failed to validate generated packet: ${e.message}")
            return false
        }
    }

    private fun isValidPacketType(type: PacketType): Boolean {
        return type in listOf(
            PacketType.OPEN, PacketType.OPEN_ACK, PacketType.OPEN_REJECT,
            PacketType.DATA, PacketType.DATA_ENCRYPTED,
            PacketType.ECHO_REQUEST, PacketType.ECHO_RESPONSE
        )
    }

    private fun isValidEncryptionMethod(method: EncryptionMethod): Boolean {
        return method in listOf(EncryptionMethod.NONE, EncryptionMethod.XOR, EncryptionMethod.AES)
    }

    private fun calculateMD5Signature(headerBytes: ByteArray): ByteArray {
        val md5 = MessageDigest.getInstance("MD5")
        md5.update(headerBytes)
        md5.update(byteArrayOf(109, 119)) // ASCII 'm', 'w'
        return md5.digest()
    }

    // Android packet generation methods for Go backend validation

    private fun createAndroidOpenPacket(username: String, password: String, mtu: UShort, encryptionMethod: EncryptionMethod): ByteArray {
        val header = PacketHeader(
            type = PacketType.OPEN,
            encrypt = encryptionMethod,
            sessionID = 0u,
            token = 0u
        )

        val headerBytes = header.toByteArray()
        val signature = calculateMD5Signature(headerBytes)
        val attributes = createOpenAttributes(username, password, mtu, encryptionMethod)

        // Combine: header + signature + attributes
        return headerBytes + signature + attributes
    }

    private fun createAndroidOpenAckPacket(sessionID: UShort, token: UInt, encryptionMethod: EncryptionMethod): ByteArray {
        val header = PacketHeader(
            type = PacketType.OPEN_ACK,
            encrypt = encryptionMethod,
            sessionID = sessionID,
            token = token
        )

        val headerBytes = header.toByteArray()
        val signature = calculateMD5Signature(headerBytes)
        val attributes = createServerConfigAttributes()

        return headerBytes + signature + attributes
    }

    private fun createAndroidEncryptedDataPacket(sessionID: UShort, token: UInt, encryptionMethod: EncryptionMethod): ByteArray {
        val header = PacketHeader(
            type = PacketType.DATA_ENCRYPTED,
            encrypt = encryptionMethod,
            sessionID = sessionID,
            token = token
        )

        val headerBytes = header.toByteArray()

        // Sample IP packet (would be encrypted in real implementation)
        val ipPacket = byteArrayOf(
            0x45, 0x00, 0x00, 0x3C, // IP header
            0x1C, 0x46, 0x40, 0x00,
            0x40, 0x06, 0x00, 0x00,
            0xC0.toByte(), 0xA8.toByte(), 0x01, 0x64, // Source IP
            0xC0.toByte(), 0xA8.toByte(), 0x01, 0x01, // Dest IP
            // Payload: "Hello from Android!"
            0x48, 0x65, 0x6C, 0x6C, 0x6F, 0x20, 0x66, 0x72,
            0x6F, 0x6D, 0x20, 0x41, 0x6E, 0x64, 0x72, 0x6F,
            0x69, 0x64, 0x21, 0x00
        )

        return headerBytes + ipPacket
    }

    private fun createAndroidDataPacket(sessionID: UShort, token: UInt, encryptionMethod: EncryptionMethod): ByteArray {
        val header = PacketHeader(
            type = PacketType.DATA,
            encrypt = encryptionMethod,
            sessionID = sessionID,
            token = token
        )

        val headerBytes = header.toByteArray()

        // Sample IP packet
        val ipPacket = byteArrayOf(
            0x45, 0x00, 0x00, 0x28, // IP header
            0x1C, 0x47, 0x40, 0x00,
            0x40, 0x01, 0x00, 0x00,
            0xC0.toByte(), 0xA8.toByte(), 0x01, 0x01, // Source IP
            0xC0.toByte(), 0xA8.toByte(), 0x01, 0x64, // Dest IP
            // ICMP payload
            0x08, 0x00, 0xF7.toByte(), 0xFC.toByte(), 0x00, 0x00, 0x00, 0x00
        )

        return headerBytes + ipPacket
    }

    private fun createAndroidEchoRequestPacket(sessionID: UShort, token: UInt, encryptionMethod: EncryptionMethod): ByteArray {
        val header = PacketHeader(
            type = PacketType.ECHO_REQUEST,
            encrypt = encryptionMethod,
            sessionID = sessionID,
            token = token
        )

        val headerBytes = header.toByteArray()
        val signature = calculateMD5Signature(headerBytes)

        // Create timestamp (8 bytes, microseconds)
        val timestamp = System.currentTimeMillis() * 1000 // Convert to microseconds
        val timestampBytes = ByteArray(8)
        val buffer = ByteBuffer.wrap(timestampBytes).order(ByteOrder.BIG_ENDIAN)
        buffer.putLong(timestamp)

        return headerBytes + signature + timestampBytes
    }

    private fun createOpenAttributes(username: String, password: String, mtu: UShort, encryptionMethod: EncryptionMethod): ByteArray {
        var attributes = ByteArray(0)

        // MTU attribute (must be first)
        val mtuBytes = ByteArray(4)
        val mtuBuffer = ByteBuffer.wrap(mtuBytes).order(ByteOrder.BIG_ENDIAN)
        mtuBuffer.putShort(mtu.toShort())
        attributes += createTLVAttribute(0x03, mtuBytes)

        // Username attribute (must be second)
        attributes += createTLVAttribute(0x01, username.toByteArray())

        // Password attribute (must be third) - simplified encryption
        val encryptedPassword = encryptPassword(password, username)
        attributes += createTLVAttribute(0x02, encryptedPassword)

        // Encryption method attribute
        attributes += createTLVAttribute(0x04, byteArrayOf(encryptionMethod.value.toByte()))

        return attributes
    }

    private fun createServerConfigAttributes(): ByteArray {
        var attributes = ByteArray(0)

        // Server IP attribute
        val serverIP = byteArrayOf(192.toByte(), 168.toByte(), 1, 1)
        attributes += createTLVAttribute(0x10, serverIP)

        // DNS server attribute
        val dnsServer = byteArrayOf(8, 8, 8, 8)
        attributes += createTLVAttribute(0x11, dnsServer)

        return attributes
    }

    private fun createTLVAttribute(attrType: Byte, value: ByteArray): ByteArray {
        val attr = ByteArray(2 + value.size)
        attr[0] = attrType
        attr[1] = value.size.toByte()
        System.arraycopy(value, 0, attr, 2, value.size)
        return attr
    }

    private fun encryptPassword(password: String, username: String): ByteArray {
        // Simplified password encryption for testing (matches Go implementation)
        val key = MessageDigest.getInstance("MD5").digest(("mw$username").toByteArray())
        val encrypted = ByteArray(16) // Take first 16 bytes

        val passwordBytes = password.toByteArray()
        for (i in encrypted.indices) {
            if (i < passwordBytes.size) {
                encrypted[i] = (passwordBytes[i].toInt() xor key[i % 16].toInt()).toByte()
            }
        }

        return encrypted
    }
}
