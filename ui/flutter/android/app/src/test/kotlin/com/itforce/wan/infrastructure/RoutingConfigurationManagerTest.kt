package com.itforce.wan.infrastructure

import android.content.Context
import android.content.SharedPreferences
import com.itforce.wan.platform.models.RoutingMode
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * NAME: RoutingConfigurationManagerTest
 *
 * DESCRIPTION:
 *     Unit tests for RoutingConfigurationManager to verify SharedPreferences
 *     storage and retrieval functionality for VPN routing configuration.
 *
 * TEST COVERAGE:
 *     - Configuration storage and retrieval
 *     - Default configuration handling
 *     - Invalid configuration validation
 *     - SharedPreferences integration
 *     - Cross-platform compatibility (iOS UserDefaults equivalent)
 */
class RoutingConfigurationManagerTest {

    private lateinit var mockContext: Context
    private lateinit var mockSharedPreferences: SharedPreferences
    private lateinit var mockEditor: SharedPreferences.Editor
    private lateinit var routingConfigurationManager: RoutingConfigurationManager

    @Before
    fun setUp() {
        // Mock Android components
        mockContext = mockk()
        mockSharedPreferences = mockk()
        mockEditor = mockk()

        // Setup SharedPreferences mock behavior
        every { mockContext.getSharedPreferences("vpn_routing_settings", Context.MODE_PRIVATE) } returns mockSharedPreferences
        every { mockSharedPreferences.edit() } returns mockEditor
        every { mockEditor.putString(any(), any()) } returns mockEditor
        every { mockEditor.commit() } returns true

        // Create manager instance
        routingConfigurationManager = RoutingConfigurationManager(mockContext)
    }

    @Test
    fun `test store and retrieve ALL routing mode`() {
        // Setup mock for retrieval
        every { mockSharedPreferences.getString("vpn_routing_mode", "all") } returns "all"
        every { mockSharedPreferences.getString("vpn_custom_routes", "") } returns ""

        // Test configuration
        val config = RoutingConfiguration(RoutingMode.ALL, emptyList())

        // Store configuration
        val storeResult = routingConfigurationManager.storeRoutingConfiguration(config)
        assertTrue("Store operation should succeed", storeResult)

        // Verify storage calls
        verify { mockEditor.putString("vpn_routing_mode", "all") }
        verify { mockEditor.putString("vpn_custom_routes", "") }
        verify { mockEditor.commit() }

        // Retrieve configuration
        val retrievedConfig = routingConfigurationManager.getStoredRoutingConfiguration()

        // Verify retrieved configuration
        assertEquals("Mode should be ALL", RoutingMode.ALL, retrievedConfig.mode)
        assertTrue("Custom routes should be empty", retrievedConfig.customRoutes.isEmpty())
    }

    @Test
    fun `test store and retrieve CUSTOM routing mode`() {
        // Test data
        val customRoutes = listOf("192.168.1.0/24", "10.0.0.0/8")
        val customRoutesString = "192.168.1.0/24,10.0.0.0/8"

        // Setup mock for retrieval
        every { mockSharedPreferences.getString("vpn_routing_mode", "all") } returns "custom"
        every { mockSharedPreferences.getString("vpn_custom_routes", "") } returns customRoutesString

        // Test configuration
        val config = RoutingConfiguration(RoutingMode.CUSTOM, customRoutes)

        // Store configuration
        val storeResult = routingConfigurationManager.storeRoutingConfiguration(config)
        assertTrue("Store operation should succeed", storeResult)

        // Verify storage calls
        verify { mockEditor.putString("vpn_routing_mode", "custom") }
        verify { mockEditor.putString("vpn_custom_routes", customRoutesString) }
        verify { mockEditor.commit() }

        // Retrieve configuration
        val retrievedConfig = routingConfigurationManager.getStoredRoutingConfiguration()

        // Verify retrieved configuration
        assertEquals("Mode should be CUSTOM", RoutingMode.CUSTOM, retrievedConfig.mode)
        assertEquals("Custom routes should match", customRoutes, retrievedConfig.customRoutes)
    }

    @Test
    fun `test default configuration when no stored data`() {
        // Setup mock for no stored data
        every { mockSharedPreferences.getString("vpn_routing_mode", "all") } returns "all"
        every { mockSharedPreferences.getString("vpn_custom_routes", "") } returns ""

        // Retrieve configuration
        val config = routingConfigurationManager.getStoredRoutingConfiguration()

        // Verify default configuration
        assertEquals("Default mode should be ALL", RoutingMode.ALL, config.mode)
        assertTrue("Default custom routes should be empty", config.customRoutes.isEmpty())
    }

    @Test
    fun `test invalid CUSTOM configuration validation`() {
        // Test invalid configuration (CUSTOM mode with no routes)
        val invalidConfig = RoutingConfiguration(RoutingMode.CUSTOM, emptyList())

        // Store invalid configuration
        val storeResult = routingConfigurationManager.storeRoutingConfiguration(invalidConfig)
        assertFalse("Store operation should fail for invalid configuration", storeResult)

        // Verify no storage calls were made
        verify(exactly = 0) { mockEditor.commit() }
    }

    @Test
    fun `test configuration validation`() {
        // Valid ALL configuration
        val validAllConfig = RoutingConfiguration(RoutingMode.ALL, emptyList())
        assertTrue("ALL mode should always be valid", validAllConfig.isValid())

        // Valid CUSTOM configuration
        val validCustomConfig = RoutingConfiguration(RoutingMode.CUSTOM, listOf("192.168.1.0/24"))
        assertTrue("CUSTOM mode with valid routes should be valid", validCustomConfig.isValid())

        // Invalid CUSTOM configuration (no routes)
        val invalidCustomConfig = RoutingConfiguration(RoutingMode.CUSTOM, emptyList())
        assertFalse("CUSTOM mode without routes should be invalid", invalidCustomConfig.isValid())

        // Invalid CUSTOM configuration (invalid CIDR)
        val invalidCIDRConfig = RoutingConfiguration(RoutingMode.CUSTOM, listOf("invalid-cidr"))
        assertFalse("CUSTOM mode with invalid CIDR should be invalid", invalidCIDRConfig.isValid())
    }

    @Test
    fun `test toRouteInfoList conversion`() {
        val customRoutes = listOf("192.168.1.0/24", "10.0.0.0/8")
        val config = RoutingConfiguration(RoutingMode.CUSTOM, customRoutes)

        val routeInfoList = config.toRouteInfoList()

        assertEquals("Should convert to correct number of RouteInfo objects", 2, routeInfoList.size)
        assertEquals("First route destination should match", "192.168.1.0", routeInfoList[0].destination)
        assertEquals("First route prefix should match", 24, routeInfoList[0].prefixLength)
        assertEquals("Second route destination should match", "10.0.0.0", routeInfoList[1].destination)
        assertEquals("Second route prefix should match", 8, routeInfoList[1].prefixLength)
    }

    @Test
    fun `test hasStoredConfiguration`() {
        // Setup mock for no stored configuration
        every { mockSharedPreferences.contains("vpn_routing_mode") } returns false

        assertFalse("Should return false when no configuration is stored", 
                   routingConfigurationManager.hasStoredConfiguration())

        // Setup mock for stored configuration
        every { mockSharedPreferences.contains("vpn_routing_mode") } returns true

        assertTrue("Should return true when configuration is stored", 
                  routingConfigurationManager.hasStoredConfiguration())
    }

    @Test
    fun `test clearRoutingConfiguration`() {
        // Setup mock for clear operation
        every { mockEditor.remove(any()) } returns mockEditor

        // Clear configuration
        val clearResult = routingConfigurationManager.clearRoutingConfiguration()
        assertTrue("Clear operation should succeed", clearResult)

        // Verify clear calls
        verify { mockEditor.remove("vpn_routing_mode") }
        verify { mockEditor.remove("vpn_custom_routes") }
        verify { mockEditor.commit() }
    }
}
