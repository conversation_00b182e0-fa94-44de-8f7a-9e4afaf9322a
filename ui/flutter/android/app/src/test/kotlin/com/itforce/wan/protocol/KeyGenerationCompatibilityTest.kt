/**
 * FILE: KeyGenerationCompatibilityTest.kt
 *
 * DESCRIPTION:
 *     Test suite to verify key generation compatibility between Android, iOS, and Go implementations.
 *     Validates that session key and password key generation produce identical results across platforms.
 *
 * AUTHOR: wei
 * HISTORY: 16/07/2025 create key generation compatibility test
 */

package com.itforce.wan.protocol

import org.junit.Test
import org.junit.Assert.*
import java.security.MessageDigest
import java.nio.charset.Charsets

class KeyGenerationCompatibilityTest {

    /**
     * Test credentials that should produce identical results across all platforms
     */
    private val testUsername = "tmptest"
    private val testPassword = "itforce"
    
    /**
     * Expected results from Go backend and iOS implementation
     * These are the reference values that Android should match
     */
    private val expectedSessionKey = "a1b2c3d4e5f6789012345678901234ab" // Example - replace with actual
    private val expectedPasswordKey = "cb7d3435273173aa7a120628c9bccaaa" // From iOS test

    /**
     * Generate session key using the corrected algorithm (UTF-8 encoding)
     */
    private fun generateSessionKey(username: String, password: String): ByteArray {
        val combined = username + password
        val md5 = MessageDigest.getInstance("MD5")
        return md5.digest(combined.toByteArray(Charsets.UTF_8))
    }

    /**
     * Generate password key using the corrected algorithm (UTF-8 encoding)
     */
    private fun generatePasswordKey(username: String): ByteArray {
        val md5 = MessageDigest.getInstance("MD5")
        md5.update(byteArrayOf(109, 119)) // 'm', 'w'
        md5.update(username.toByteArray(Charsets.UTF_8))
        return md5.digest()
    }

    /**
     * Old session key generation using US_ASCII (incorrect)
     */
    private fun oldGenerateSessionKey(username: String, password: String): ByteArray {
        val combined = username + password
        val md5 = MessageDigest.getInstance("MD5")
        return md5.digest(combined.toByteArray(Charsets.US_ASCII))
    }

    /**
     * Old password key generation using US_ASCII (incorrect)
     */
    private fun oldGeneratePasswordKey(username: String): ByteArray {
        val md5 = MessageDigest.getInstance("MD5")
        md5.update(byteArrayOf(109, 119)) // 'm', 'w'
        md5.update(username.toByteArray(Charsets.US_ASCII))
        return md5.digest()
    }

    @Test
    fun testSessionKeyEncodingDifference() {
        val newKey = generateSessionKey(testUsername, testPassword)
        val oldKey = oldGenerateSessionKey(testUsername, testPassword)
        
        // For ASCII-only usernames/passwords, UTF-8 and US_ASCII should be the same
        // But this test documents the change for future reference
        println("Username: '$testUsername'")
        println("Password: '$testPassword'")
        println("New key (UTF-8):  ${newKey.joinToString("") { "%02x".format(it) }}")
        println("Old key (US_ASCII): ${oldKey.joinToString("") { "%02x".format(it) }}")
        
        // For ASCII-only strings, they should be the same
        if (testUsername.all { it.code < 128 } && testPassword.all { it.code < 128 }) {
            assertArrayEquals("For ASCII-only strings, UTF-8 and US_ASCII should be identical", 
                             newKey, oldKey)
        }
    }

    @Test
    fun testPasswordKeyEncodingDifference() {
        val newKey = generatePasswordKey(testUsername)
        val oldKey = oldGeneratePasswordKey(testUsername)
        
        println("Username: '$testUsername'")
        println("New password key (UTF-8):  ${newKey.joinToString("") { "%02x".format(it) }}")
        println("Old password key (US_ASCII): ${oldKey.joinToString("") { "%02x".format(it) }}")
        
        // For ASCII-only strings, they should be the same
        if (testUsername.all { it.code < 128 }) {
            assertArrayEquals("For ASCII-only strings, UTF-8 and US_ASCII should be identical", 
                             newKey, oldKey)
        }
    }

    @Test
    fun testPasswordKeyWithKnownValue() {
        val passwordKey = generatePasswordKey(testUsername)
        val hexResult = passwordKey.joinToString("") { "%02x".format(it) }
        
        println("Username: '$testUsername'")
        println("Generated password key: $hexResult")
        println("Expected password key:  $expectedPasswordKey")
        
        assertEquals("Password key should match iOS implementation", 
                    expectedPasswordKey, hexResult)
    }

    @Test
    fun testSessionKeyConsistency() {
        val key1 = generateSessionKey(testUsername, testPassword)
        val key2 = generateSessionKey(testUsername, testPassword)
        
        assertArrayEquals("Session key generation should be deterministic", key1, key2)
    }

    @Test
    fun testPasswordKeyConsistency() {
        val key1 = generatePasswordKey(testUsername)
        val key2 = generatePasswordKey(testUsername)
        
        assertArrayEquals("Password key generation should be deterministic", key1, key2)
    }

    @Test
    fun testEmptyCredentials() {
        val sessionKey = generateSessionKey("", "")
        val passwordKey = generatePasswordKey("")
        
        assertEquals("Session key should be 16 bytes", 16, sessionKey.size)
        assertEquals("Password key should be 16 bytes", 16, passwordKey.size)
        
        println("Empty session key: ${sessionKey.joinToString("") { "%02x".format(it) }}")
        println("Empty password key: ${passwordKey.joinToString("") { "%02x".format(it) }}")
    }

    @Test
    fun testUnicodeCredentials() {
        val unicodeUsername = "用户名"
        val unicodePassword = "密码"
        
        val sessionKey = generateSessionKey(unicodeUsername, unicodePassword)
        val passwordKey = generatePasswordKey(unicodeUsername)
        
        assertEquals("Session key should be 16 bytes", 16, sessionKey.size)
        assertEquals("Password key should be 16 bytes", 16, passwordKey.size)
        
        println("Unicode username: '$unicodeUsername'")
        println("Unicode password: '$unicodePassword'")
        println("Unicode session key: ${sessionKey.joinToString("") { "%02x".format(it) }}")
        println("Unicode password key: ${passwordKey.joinToString("") { "%02x".format(it) }}")
        
        // Verify UTF-8 vs US_ASCII difference for Unicode
        try {
            val oldSessionKey = oldGenerateSessionKey(unicodeUsername, unicodePassword)
            val oldPasswordKey = oldGeneratePasswordKey(unicodeUsername)
            
            assertFalse("Unicode keys should be different between UTF-8 and US_ASCII",
                       sessionKey.contentEquals(oldSessionKey))
            assertFalse("Unicode password keys should be different between UTF-8 and US_ASCII",
                       passwordKey.contentEquals(oldPasswordKey))
        } catch (e: Exception) {
            // US_ASCII encoding may fail for Unicode characters, which is expected
            println("US_ASCII encoding failed for Unicode (expected): ${e.message}")
        }
    }

    @Test
    fun testKeyLengths() {
        val sessionKey = generateSessionKey(testUsername, testPassword)
        val passwordKey = generatePasswordKey(testUsername)
        
        assertEquals("Session key must be exactly 16 bytes (MD5 hash)", 16, sessionKey.size)
        assertEquals("Password key must be exactly 16 bytes (MD5 hash)", 16, passwordKey.size)
    }

    @Test
    fun testSpecialCharacters() {
        val specialUsername = "<EMAIL>"
        val specialPassword = "p@ssw0rd!"
        
        val sessionKey = generateSessionKey(specialUsername, specialPassword)
        val passwordKey = generatePasswordKey(specialUsername)
        
        assertEquals("Session key should be 16 bytes", 16, sessionKey.size)
        assertEquals("Password key should be 16 bytes", 16, passwordKey.size)
        
        println("Special username: '$specialUsername'")
        println("Special password: '$specialPassword'")
        println("Special session key: ${sessionKey.joinToString("") { "%02x".format(it) }}")
        println("Special password key: ${passwordKey.joinToString("") { "%02x".format(it) }}")
    }

    /**
     * Test that demonstrates the encoding fix ensures cross-platform compatibility
     */
    @Test
    fun testCrossPlatformCompatibility() {
        val sessionKey = generateSessionKey(testUsername, testPassword)
        val passwordKey = generatePasswordKey(testUsername)
        
        // These should now match what iOS and Go would produce
        println("=== Cross-Platform Compatibility Test ===")
        println("Username: '$testUsername'")
        println("Password: '$testPassword'")
        println("Session key (should match iOS/Go): ${sessionKey.joinToString("") { "%02x".format(it) }}")
        println("Password key (should match iOS/Go): ${passwordKey.joinToString("") { "%02x".format(it) }}")
        
        // Verify deterministic results
        val sessionKey2 = generateSessionKey(testUsername, testPassword)
        val passwordKey2 = generatePasswordKey(testUsername)
        
        assertArrayEquals("Session key should be deterministic", sessionKey, sessionKey2)
        assertArrayEquals("Password key should be deterministic", passwordKey, passwordKey2)
    }
}
