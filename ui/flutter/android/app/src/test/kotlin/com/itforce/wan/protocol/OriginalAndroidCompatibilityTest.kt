/**
 * FILE: OriginalAndroidCompatibilityTest.kt
 *
 * DESCRIPTION:
 *     Test suite to verify compatibility with the original Android implementation.
 *     Validates that our implementation matches the original HandShakeHelper.java exactly.
 *
 * AUTHOR: wei
 * HISTORY: 16/07/2025 create compatibility test with original Android implementation
 */

package com.itforce.wan.protocol

import org.junit.Test
import org.junit.Assert.*
import java.security.MessageDigest
import java.nio.charset.Charsets
import java.util.Arrays

class OriginalAndroidCompatibilityTest {

    /**
     * Test credentials
     */
    private val testUsername = "testuser"
    private val testPassword = "testpass"
    private val testData = byteArrayOf(
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
        0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10
    )

    /**
     * Original Android session key generation algorithm
     * From HandShakeHelper.sdwan_encrypt()
     */
    private fun originalSessionKeyGeneration(username: String, password: String): ByteArray {
        val md5 = MessageDigest.getInstance("MD5")
        md5.update((username + password).toByteArray(Charsets.US_ASCII))
        return md5.digest()
    }

    /**
     * Original Android password key generation algorithm
     * From HandShakeHelper.generateOpenPacket()
     */
    private fun originalPasswordKeyGeneration(username: String): ByteArray {
        val md5 = MessageDigest.getInstance("MD5")
        val nameMd5 = byteArrayOf(109, 119) // 'm', 'w'
        md5.update(nameMd5)
        md5.update(username.toByteArray(Charsets.US_ASCII))
        return md5.digest()
    }

    /**
     * Original Android XOR encryption algorithm
     * From HandShakeHelper.sdwan_encrypt()
     */
    private fun originalXOREncrypt(data: ByteArray, key: ByteArray): ByteArray {
        val result = data.copyOf()
        for (i in result.indices) {
            result[i] = (result[i].toInt() xor key[i % 8].toInt()).toByte()
        }
        return result
    }

    @Test
    fun testSessionKeyGeneration() {
        // Test our implementation against original
        val ourKey = generateSessionKey(testUsername, testPassword)
        val originalKey = originalSessionKeyGeneration(testUsername, testPassword)
        
        assertArrayEquals("Session key should match original Android implementation", 
                         originalKey, ourKey)
        
        println("Username: '$testUsername'")
        println("Password: '$testPassword'")
        println("Session key: ${ourKey.joinToString("") { "%02x".format(it) }}")
    }

    @Test
    fun testPasswordKeyGeneration() {
        // Test our implementation against original
        val ourKey = generatePasswordKey(testUsername)
        val originalKey = originalPasswordKeyGeneration(testUsername)
        
        assertArrayEquals("Password key should match original Android implementation", 
                         originalKey, ourKey)
        
        println("Username: '$testUsername'")
        println("Password key: ${ourKey.joinToString("") { "%02x".format(it) }}")
    }

    @Test
    fun testXOREncryption() {
        val sessionKey = originalSessionKeyGeneration(testUsername, testPassword)
        
        // Test our implementation against original
        val ourEncrypted = xorEncrypt(testData, sessionKey)
        val originalEncrypted = originalXOREncrypt(testData, sessionKey)
        
        assertArrayEquals("XOR encryption should match original Android implementation", 
                         originalEncrypted, ourEncrypted)
        
        println("Test data: ${testData.joinToString("") { "%02x".format(it) }}")
        println("Session key: ${sessionKey.joinToString("") { "%02x".format(it) }}")
        println("Encrypted: ${ourEncrypted.joinToString("") { "%02x".format(it) }}")
    }

    @Test
    fun testXORSymmetry() {
        val sessionKey = originalSessionKeyGeneration(testUsername, testPassword)
        
        // Test that encryption/decryption is symmetric
        val encrypted = originalXOREncrypt(testData, sessionKey)
        val decrypted = originalXOREncrypt(encrypted, sessionKey)
        
        assertArrayEquals("XOR should be symmetric", testData, decrypted)
    }

    @Test
    fun testKeyLength() {
        val sessionKey = originalSessionKeyGeneration(testUsername, testPassword)
        val passwordKey = originalPasswordKeyGeneration(testUsername)
        
        assertEquals("Session key should be 16 bytes", 16, sessionKey.size)
        assertEquals("Password key should be 16 bytes", 16, passwordKey.size)
    }

    @Test
    fun testEmptyCredentials() {
        val sessionKey = originalSessionKeyGeneration("", "")
        val passwordKey = originalPasswordKeyGeneration("")
        
        assertEquals("Session key should be 16 bytes even for empty credentials", 16, sessionKey.size)
        assertEquals("Password key should be 16 bytes even for empty username", 16, passwordKey.size)
        
        println("Empty session key: ${sessionKey.joinToString("") { "%02x".format(it) }}")
        println("Empty password key: ${passwordKey.joinToString("") { "%02x".format(it) }}")
    }

    @Test
    fun testSpecialCharacters() {
        val specialUsername = "<EMAIL>"
        val specialPassword = "p@ssw0rd!"
        
        val sessionKey = originalSessionKeyGeneration(specialUsername, specialPassword)
        val passwordKey = originalPasswordKeyGeneration(specialUsername)
        
        assertEquals("Session key should be 16 bytes", 16, sessionKey.size)
        assertEquals("Password key should be 16 bytes", 16, passwordKey.size)
        
        println("Special username: '$specialUsername'")
        println("Special password: '$specialPassword'")
        println("Special session key: ${sessionKey.joinToString("") { "%02x".format(it) }}")
        println("Special password key: ${passwordKey.joinToString("") { "%02x".format(it) }}")
    }

    @Test
    fun testLongData() {
        val sessionKey = originalSessionKeyGeneration(testUsername, testPassword)
        val longData = ByteArray(100) { (it % 256).toByte() }
        
        val encrypted = originalXOREncrypt(longData, sessionKey)
        val decrypted = originalXOREncrypt(encrypted, sessionKey)
        
        assertArrayEquals("Long data should encrypt/decrypt correctly", longData, decrypted)
    }

    @Test
    fun testKeyModulo() {
        val sessionKey = originalSessionKeyGeneration(testUsername, testPassword)
        
        // Test that key cycling works correctly
        val data9Bytes = byteArrayOf(0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09)
        val encrypted = originalXOREncrypt(data9Bytes, sessionKey)
        
        // The 9th byte should be XORed with sessionKey[0] (9 % 8 = 1, but 0-indexed)
        val expected9thByte = (data9Bytes[8].toInt() xor sessionKey[0].toInt()).toByte()
        assertEquals("9th byte should use key[0] due to modulo", expected9thByte, encrypted[8])
    }

    // Helper methods that use our current implementation
    private fun generateSessionKey(username: String, password: String): ByteArray {
        val combined = username + password
        val md5 = MessageDigest.getInstance("MD5")
        return md5.digest(combined.toByteArray(Charsets.US_ASCII))
    }

    private fun generatePasswordKey(username: String): ByteArray {
        val md5 = MessageDigest.getInstance("MD5")
        md5.update(byteArrayOf(109, 119)) // 'm', 'w'
        md5.update(username.toByteArray(Charsets.US_ASCII))
        return md5.digest()
    }

    private fun xorEncrypt(data: ByteArray, sessionKey: ByteArray): ByteArray {
        val result = data.copyOf()
        for (i in result.indices) {
            result[i] = (result[i].toInt() xor sessionKey[i % 8].toInt()).toByte()
        }
        return result
    }
}
