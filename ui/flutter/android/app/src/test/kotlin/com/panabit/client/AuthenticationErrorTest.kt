package com.panabit.client

import com.panabit.client.infrastructure.error.VPNServiceError
import com.panabit.client.infrastructure.error.VPNErrorCode
import org.junit.Test
import org.junit.Assert.*

/**
 * 认证错误处理测试
 * 验证修复后的错误分类逻辑是否正确工作
 */
class AuthenticationErrorTest {

    /**
     * 测试凭证错误的识别
     * 来自OPENREJECT包的错误应该被识别为凭证问题
     */
    @Test
    fun testCredentialErrorIdentification() {
        val credentialErrors = listOf(
            "Invalid username",
            "Invalid password", 
            "Account expired",
            "Account disabled",
            "Invalid token",
            "Authentication failed"
        )
        
        credentialErrors.forEach { errorMessage ->
            val error = VPNServiceError.AuthenticationFailed(
                errorMessage = errorMessage,
                isCredentialIssue = isCredentialError(errorMessage)
            )
            
            assertTrue(
                "Error '$errorMessage' should be identified as credential issue",
                error.isCredentialIssue
            )
        }
    }

    /**
     * 测试非凭证错误的识别
     * 超时和网络错误不应该被识别为凭证问题
     */
    @Test
    fun testNonCredentialErrorIdentification() {
        val nonCredentialErrors = listOf(
            "Authentication timed out",
            "Connection failed",
            "Network unreachable",
            "Server error",
            "Server is full",
            "Operation timeout"
        )
        
        nonCredentialErrors.forEach { errorMessage ->
            val error = VPNServiceError.AuthenticationFailed(
                errorMessage = errorMessage,
                isCredentialIssue = isCredentialError(errorMessage)
            )
            
            assertFalse(
                "Error '$errorMessage' should NOT be identified as credential issue",
                error.isCredentialIssue
            )
        }
    }

    /**
     * 测试错误消息的用户友好性
     */
    @Test
    fun testUserFriendlyMessages() {
        // 凭证错误
        val credentialError = VPNServiceError.AuthenticationFailed(
            errorMessage = "Invalid password",
            isCredentialIssue = true
        )
        
        val credentialMessage = credentialError.getUserFriendlyMessage()
        assertTrue(
            "Credential error should mention username/password",
            credentialMessage.contains("用户名") || credentialMessage.contains("密码")
        )
        
        // 超时错误
        val timeoutError = VPNServiceError.AuthenticationFailed(
            errorMessage = "Authentication timed out",
            isCredentialIssue = false
        )
        
        val timeoutMessage = timeoutError.getUserFriendlyMessage()
        assertTrue(
            "Timeout error should mention server/retry",
            timeoutMessage.contains("服务器") || timeoutMessage.contains("重试")
        )
    }

    /**
     * 测试错误恢复策略
     */
    @Test
    fun testErrorRecoveryStrategy() {
        // 凭证错误不可自动恢复
        val credentialError = VPNServiceError.AuthenticationFailed(
            errorMessage = "Invalid password",
            isCredentialIssue = true
        )
        assertFalse("Credential errors should not be recoverable", credentialError.isRecoverable())
        assertEquals("Credential errors should have no retry delay", 0L, credentialError.getRetryDelay())
        
        // 超时错误可以重试
        val timeoutError = VPNServiceError.AuthenticationFailed(
            errorMessage = "Authentication timed out",
            isCredentialIssue = false
        )
        assertTrue("Timeout errors should be recoverable", timeoutError.isRecoverable())
        assertTrue("Timeout errors should have retry delay", timeoutError.getRetryDelay() > 0L)
    }

    /**
     * 模拟isCredentialError方法的逻辑
     * 这里简化实现，实际方法在ConnectionManager中
     */
    private fun isCredentialError(errorMessage: String?): Boolean {
        if (errorMessage == null) return false

        val lowerMessage = errorMessage.lowercase()
        
        // 明确的非凭据错误
        val nonCredentialErrors = listOf(
            "timeout", "timed out", "connection failed", "network", "unreachable", "server error", "server is full"
        )
        
        if (nonCredentialErrors.any { error -> lowerMessage.contains(error) }) {
            return false
        }
        
        // 凭据相关错误
        val credentialKeywords = listOf(
            "invalid username", "invalid password", "account expired", "account disabled",
            "invalid token", "unauthorized", "authentication failed", "invalid", "credential"
        )
        
        return credentialKeywords.any { keyword -> lowerMessage.contains(keyword) }
    }
}
