/**
 * FILE: DataPacketCompatibilityTest.kt
 *
 * DESCRIPTION:
 *     Test suite to verify Android data packet creation and processing compatibility.
 *     Tests the complete flow from session establishment to data packet encryption/decryption.
 *     Verifies the fix for XOR encryption byte order issues.
 *
 * AUTHOR: wei
 * HISTORY: 16/07/2025 create data packet compatibility test
 */

package com.itforce.wan.protocol

import com.itforce.wan.protocol.models.*
import org.junit.Test
import org.junit.Assert.*
import java.nio.ByteBuffer
import java.nio.ByteOrder

class DataPacketCompatibilityTest {

    /**
     * Test complete data packet creation and processing flow
     */
    @Test
    fun testDataPacketFlow() {
        // Simulate session establishment
        val sessionID: UShort = 0x1234u
        val token: UInt = 0xDEADBEEFu
        val encryptionMethod = EncryptionMethod.XOR
        
        // Create XOR encryption service
        val xorEncryption = XOREncryption.createWithCredentials("testuser", "testpass")
        
        // Test IP payload (simulated)
        val ipPayload = byteArrayOf(
            0x45, 0x00, 0x00, 0x3C, // IP header start
            0x1C, 0x46, 0x40, 0x00,
            0x40, 0x06, 0x00, 0x00, // Protocol, checksum
            0xC0.toByte(), 0xA8.toByte(), 0x01, 0x64, // Source IP
            0xC0.toByte(), 0xA8.toByte(), 0x01, 0x01  // Dest IP
        )
        
        // Create encrypted data packet header
        val header = PacketHeader(
            type = PacketType.DATA_ENCRYPTED,
            encrypt = encryptionMethod,
            sessionID = sessionID,
            token = token
        )
        
        // Encrypt the payload
        val encryptedPayload = xorEncryption.encrypt(ipPayload)
        
        // Create complete packet
        val packet = SDWANPacket(header, encryptedPayload)
        
        // Serialize packet to bytes (as would be sent over network)
        val packetBytes = packet.toByteArray()
        
        // Verify packet structure
        assertTrue("Packet should be larger than header", packetBytes.size > PacketHeader.HEADER_SIZE)
        assertEquals("Total size should match", PacketHeader.HEADER_SIZE + encryptedPayload.size, packetBytes.size)
        
        // Parse packet back (as would be received from network)
        val parsedPacket = SDWANPacket.fromByteArray(packetBytes)
        
        // Verify header fields
        assertEquals("Packet type should match", PacketType.DATA_ENCRYPTED, parsedPacket.header.type)
        assertEquals("Encryption method should match", encryptionMethod, parsedPacket.header.encrypt)
        assertEquals("Session ID should match", sessionID, parsedPacket.header.sessionID)
        assertEquals("Token should match", token, parsedPacket.header.token)
        
        // Decrypt the payload
        val decryptedPayload = xorEncryption.decrypt(parsedPacket.data)
        
        // Verify round-trip
        assertArrayEquals("Decrypted payload should match original", ipPayload, decryptedPayload)
        
        println("=== Data Packet Flow Test ===")
        println("Session ID: 0x${sessionID.toString(16)}")
        println("Token: 0x${token.toString(16)}")
        println("Original payload: ${ipPayload.joinToString("") { "%02x".format(it) }}")
        println("Encrypted payload: ${encryptedPayload.joinToString("") { "%02x".format(it) }}")
        println("Decrypted payload: ${decryptedPayload.joinToString("") { "%02x".format(it) }}")
    }

    /**
     * Test packet header serialization/deserialization with big-endian byte order
     */
    @Test
    fun testPacketHeaderByteOrder() {
        val sessionID: UShort = 0x1234u
        val token: UInt = 0xDEADBEEFu
        
        val header = PacketHeader(
            type = PacketType.DATA_ENCRYPTED,
            encrypt = EncryptionMethod.XOR,
            sessionID = sessionID,
            token = token
        )
        
        // Serialize header
        val headerBytes = header.toByteArray()
        assertEquals("Header should be 8 bytes", 8, headerBytes.size)
        
        // Verify big-endian byte order
        val buffer = ByteBuffer.wrap(headerBytes).order(ByteOrder.BIG_ENDIAN)
        assertEquals("Type should match", PacketType.DATA_ENCRYPTED.value.toByte(), buffer.get(0))
        assertEquals("Encrypt should match", EncryptionMethod.XOR.value.toByte(), buffer.get(1))
        assertEquals("Session ID should match", sessionID.toShort(), buffer.getShort(2))
        assertEquals("Token should match", token.toInt(), buffer.getInt(4))
        
        // Parse header back
        val parsedHeader = PacketHeader.fromByteArray(headerBytes)
        assertEquals("Parsed type should match", PacketType.DATA_ENCRYPTED, parsedHeader.type)
        assertEquals("Parsed encrypt should match", EncryptionMethod.XOR, parsedHeader.encrypt)
        assertEquals("Parsed session ID should match", sessionID, parsedHeader.sessionID)
        assertEquals("Parsed token should match", token, parsedHeader.token)
        
        println("=== Header Byte Order Test ===")
        println("Header bytes: ${headerBytes.joinToString("") { "%02x".format(it) }}")
        println("Session ID: 0x${sessionID.toString(16)} -> bytes: ${headerBytes.sliceArray(2..3).joinToString("") { "%02x".format(it) }}")
        println("Token: 0x${token.toString(16)} -> bytes: ${headerBytes.sliceArray(4..7).joinToString("") { "%02x".format(it) }}")
    }

    /**
     * Test unencrypted data packet creation
     */
    @Test
    fun testUnencryptedDataPacket() {
        val sessionID: UShort = 0x5678u
        val token: UInt = 0x12345678u
        
        val ipPayload = "Hello, World!".toByteArray()
        
        val header = PacketHeader(
            type = PacketType.DATA,
            encrypt = EncryptionMethod.NONE,
            sessionID = sessionID,
            token = token
        )
        
        val packet = SDWANPacket(header, ipPayload)
        val packetBytes = packet.toByteArray()
        
        // Parse back
        val parsedPacket = SDWANPacket.fromByteArray(packetBytes)
        
        assertEquals("Type should be DATA", PacketType.DATA, parsedPacket.header.type)
        assertEquals("Encryption should be NONE", EncryptionMethod.NONE, parsedPacket.header.encrypt)
        assertArrayEquals("Payload should be unchanged", ipPayload, parsedPacket.data)
    }

    /**
     * Test various packet types with correct session info
     */
    @Test
    fun testVariousPacketTypes() {
        val sessionID: UShort = 0xABCDu
        val token: UInt = 0x87654321u
        val testData = "Test payload".toByteArray()
        
        val packetTypes = listOf(
            PacketType.DATA,
            PacketType.DATA_ENCRYPTED,
            PacketType.DATA_DUP,
            PacketType.DATA_ENC_DUP
        )
        
        for (packetType in packetTypes) {
            val header = PacketHeader(
                type = packetType,
                encrypt = EncryptionMethod.XOR,
                sessionID = sessionID,
                token = token
            )
            
            val packet = SDWANPacket(header, testData)
            val packetBytes = packet.toByteArray()
            val parsedPacket = SDWANPacket.fromByteArray(packetBytes)
            
            assertEquals("Type should match for $packetType", packetType, parsedPacket.header.type)
            assertEquals("Session ID should match for $packetType", sessionID, parsedPacket.header.sessionID)
            assertEquals("Token should match for $packetType", token, parsedPacket.header.token)
            assertArrayEquals("Data should match for $packetType", testData, parsedPacket.data)
        }
    }

    /**
     * Test XOR encryption consistency with different data sizes
     */
    @Test
    fun testXOREncryptionConsistency() {
        val xorEncryption = XOREncryption.createWithCredentials("admin", "password123")
        
        // Test with various data sizes that would reveal byte order issues
        val testSizes = listOf(8, 16, 24, 32, 40) // All multiples of 8
        
        for (size in testSizes) {
            val testData = ByteArray(size) { (it * 17 % 256).toByte() } // Pseudo-random pattern
            
            val encrypted = xorEncryption.encrypt(testData)
            val decrypted = xorEncryption.decrypt(encrypted)
            
            assertArrayEquals("Round-trip should work for size $size", testData, decrypted)
            
            // Verify encryption actually changes the data
            if (size > 0) {
                assertFalse("Encryption should change data for size $size", testData.contentEquals(encrypted))
            }
        }
    }

    /**
     * Test that demonstrates the fix for byte order issues
     */
    @Test
    fun testByteOrderFix() {
        // This test uses specific data patterns that would fail with incorrect byte order
        val xorEncryption = XOREncryption.createWithCredentials("testuser", "testpass")
        
        // Pattern that would reveal little-endian vs big-endian issues
        val testPattern = byteArrayOf(
            0x01, 0x23, 0x45, 0x67, 0x89.toByte(), 0xAB.toByte(), 0xCD.toByte(), 0xEF.toByte(),
            0xFE.toByte(), 0xDC.toByte(), 0xBA.toByte(), 0x98.toByte(), 0x76, 0x54, 0x32, 0x10
        )
        
        val encrypted = xorEncryption.encrypt(testPattern)
        val decrypted = xorEncryption.decrypt(encrypted)
        
        assertArrayEquals("Byte order fix should preserve round-trip", testPattern, decrypted)
        
        println("=== Byte Order Fix Verification ===")
        println("Test pattern: ${testPattern.joinToString("") { "%02x".format(it) }}")
        println("Encrypted:    ${encrypted.joinToString("") { "%02x".format(it) }}")
        println("Decrypted:    ${decrypted.joinToString("") { "%02x".format(it) }}")
        
        // Additional verification: encrypt twice should restore original
        val doubleEncrypted = xorEncryption.encrypt(encrypted)
        assertArrayEquals("Double encryption should restore original (XOR property)", testPattern, doubleEncrypted)
    }
}
