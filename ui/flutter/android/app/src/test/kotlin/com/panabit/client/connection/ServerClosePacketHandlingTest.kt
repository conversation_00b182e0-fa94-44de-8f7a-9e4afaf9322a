/**
 * NAME: ServerClosePacketHandlingTest
 *
 * DESCRIPTION:
 *     测试Android平台处理服务器close包的完整流程，验证：
 *     1. close包的正确识别和处理
 *     2. Event Channel状态上报机制
 *     3. Flutter UI状态更新和重连逻辑
 *     4. 跨平台行为一致性
 *
 * AUTHOR: Android Team
 * DATE: 2024-12-19
 */

package com.panabit.client.connection

import com.panabit.client.connection.state.VPNState
import com.panabit.client.infrastructure.error.VPNServiceError
import com.panabit.client.protocol.models.PacketType
import com.panabit.client.protocol.models.SDWANPacket
import com.panabit.client.protocol.models.SDWANHeader
import com.panabit.client.protocol.models.EncryptionMethod
import com.panabit.client.infrastructure.models.ServerInfo
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.delay
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mockito.*

class ServerClosePacketHandlingTest {

    @Test
    fun `test server close packet identification and processing`() = runTest {
        // 创建模拟的close包
        val closeHeader = SDWANHeader(
            type = PacketType.CLOSE,
            encrypt = EncryptionMethod.NONE,
            sessionID = 12345u,
            token = 67890u
        )
        val closePacket = SDWANPacket(closeHeader, byteArrayOf())
        
        // 验证包类型识别
        assertEquals("Close packet type should be CLOSE", PacketType.CLOSE, closePacket.header.type)
        assertTrue("Close packet should be identified correctly", closePacket.header.type == PacketType.CLOSE)
    }

    @Test
    fun `test disconnect reason enum includes server initiated`() {
        // 验证DisconnectReason枚举包含ServerInitiated
        val serverInitiatedReason = DisconnectReason.ServerInitiated
        assertNotNull("ServerInitiated disconnect reason should exist", serverInitiatedReason)
        assertEquals("ServerInitiated should have correct name", "ServerInitiated", serverInitiatedReason.name)
    }

    @Test
    fun `test vpn state transitions after server close`() = runTest {
        // 模拟服务器close包处理后的状态变化
        val initialState = VPNState.Connected(
            ServerInfo("test-server", "Test Server", "192.168.1.1", 8080, 50),
            System.currentTimeMillis()
        )
        
        // 验证初始状态
        assertTrue("Initial state should be connected", initialState.isConnected)
        
        // 模拟接收close包后的状态变化
        val disconnectedState = VPNState.Disconnected
        assertFalse("After close packet, state should be disconnected", disconnectedState.isConnected)
        
        // 验证状态转换的有效性
        assertTrue("Transition from connected to disconnected should be valid", 
            VPNState.isValidStateTransition(initialState, disconnectedState))
    }

    @Test
    fun `test flutter map conversion for disconnected state`() {
        // 测试断线状态的Flutter映射
        val disconnectedState = VPNState.Disconnected
        val flutterMap = disconnectedState.toFlutterMap()
        
        assertEquals("Flutter map should contain correct status", "disconnected", flutterMap["status"])
        assertNotNull("Flutter map should contain timestamp", flutterMap["timestamp"])
    }

    @Test
    fun `test reconnection logic after server close`() = runTest {
        // 模拟服务器信息
        val testServer = ServerInfo("test-server", "Test Server", "192.168.1.1", 8080, 50)
        
        // 验证重连状态
        val reconnectingState = VPNState.Reconnecting(testServer, 1, "Server sent close packet")
        
        assertTrue("Reconnecting state should be operation in progress", reconnectingState.isOperationInProgress)
        assertEquals("Reconnecting state should have correct server", testServer, reconnectingState.server)
        assertEquals("Reconnecting state should have correct reason", "Server sent close packet", reconnectingState.reason)
    }

    @Test
    fun `test cross platform consistency - close packet handling`() {
        // 验证Android平台的close包处理与iOS/Go后端行为一致
        
        // 1. 验证close包类型值与其他平台一致
        assertEquals("Close packet type value should match cross-platform", 0x17u.toByte(), PacketType.CLOSE.value.toByte())
        
        // 2. 验证断线原因与其他平台一致
        val serverInitiatedReason = DisconnectReason.ServerInitiated
        assertNotNull("Server initiated disconnect reason should exist for cross-platform consistency", serverInitiatedReason)
        
        // 3. 验证状态转换逻辑与其他平台一致
        val connectedState = VPNState.Connected(
            ServerInfo("test", "Test", "1.1.1.1", 8080, 50),
            System.currentTimeMillis()
        )
        val disconnectedState = VPNState.Disconnected
        
        assertTrue("State transition should be valid like other platforms", 
            VPNState.isValidStateTransition(connectedState, disconnectedState))
    }

    @Test
    fun `test event channel data format for server close`() {
        // 测试Event Channel数据格式
        val disconnectedState = VPNState.Disconnected
        val eventData = disconnectedState.toFlutterMap()
        
        // 验证Event Channel数据格式符合Flutter UI期望
        assertTrue("Event data should be Map", eventData is Map<*, *>)
        assertEquals("Status should be disconnected", "disconnected", eventData["status"])
        assertNotNull("Timestamp should be present", eventData["timestamp"])
        
        // 验证数据类型符合Flutter期望
        assertTrue("Status should be String", eventData["status"] is String)
        assertTrue("Timestamp should be Number", eventData["timestamp"] is Number)
    }

    @Test
    fun `test user reconnection capability after server close`() = runTest {
        // 测试用户在收到服务器close包后的重连能力
        val testServer = ServerInfo("test-server", "Test Server", "192.168.1.1", 8080, 50)
        
        // 模拟从disconnected状态发起重连
        val disconnectedState = VPNState.Disconnected
        val connectingState = VPNState.Connecting(testServer, VPNState.ConnectingProgress.Initializing)
        
        // 验证状态转换有效性
        assertTrue("User should be able to reconnect from disconnected state", 
            VPNState.isValidStateTransition(disconnectedState, connectingState))
        
        // 验证重连状态正确性
        assertEquals("Connecting state should have correct server", testServer, connectingState.server)
        assertTrue("Connecting state should be operation in progress", connectingState.isOperationInProgress)
    }

    @Test
    fun `test error handling during server close processing`() {
        // 测试处理服务器close包时的错误处理
        val testError = VPNServiceError.ConnectionFailed("Failed to process server close packet", "test-server")
        
        // 验证错误类型和消息
        assertTrue("Error should be ConnectionFailed type", testError is VPNServiceError.ConnectionFailed)
        assertTrue("Error message should contain close packet info", 
            testError.message.contains("close packet"))
        
        // 验证错误恢复策略
        val recoveryStrategy = testError.getRecoveryStrategy()
        assertNotNull("Recovery strategy should be available", recoveryStrategy)
    }

    @Test
    fun `test performance of close packet processing`() = runTest {
        // 测试close包处理的性能
        val startTime = System.currentTimeMillis()
        
        // 模拟close包处理流程
        val closeHeader = SDWANHeader(
            type = PacketType.CLOSE,
            encrypt = EncryptionMethod.NONE,
            sessionID = 12345u,
            token = 67890u
        )
        val closePacket = SDWANPacket(closeHeader, byteArrayOf())
        
        // 验证包解析性能
        val parseTime = System.currentTimeMillis() - startTime
        assertTrue("Close packet parsing should be fast (< 10ms)", parseTime < 10)
        
        // 模拟状态更新性能
        val stateUpdateStart = System.currentTimeMillis()
        val disconnectedState = VPNState.Disconnected
        val stateUpdateTime = System.currentTimeMillis() - stateUpdateStart
        
        assertTrue("State update should be fast (< 5ms)", stateUpdateTime < 5)
    }
}
