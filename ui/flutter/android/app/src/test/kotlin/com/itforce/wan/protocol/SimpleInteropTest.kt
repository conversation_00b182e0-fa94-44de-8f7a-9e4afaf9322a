/**
 * FILE: SimpleInteropTest.kt
 *
 * DESCRIPTION:
 *     Simplified Android-Go protocol interoperability test.
 *     This test can be run in Android Studio without external dependencies.
 *     Tests the core XOR encryption fix and packet parsing compatibility.
 *
 * AUTHOR: wei
 * HISTORY: 16/07/2025 create simplified interop test
 */

package com.itforce.wan.protocol

import com.itforce.wan.protocol.models.*
import org.junit.Test
import org.junit.Assert.*
import java.io.File
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.security.MessageDigest

class SimpleInteropTest {

    /**
     * Test the XOR encryption fix - this is the core issue we fixed
     */
    @Test
    fun testXOREncryptionFix() {
        println("=== Testing XOR Encryption Fix ===")
        
        val testCredentials = listOf(
            "testuser" to "testpass",
            "admin" to "123456"
        )
        
        for ((username, password) in testCredentials) {
            val xorEncryption = XOREncryption.createWithCredentials(username, password)
            
            // Test with 8-byte aligned data (this would fail with wrong byte order)
            val testData = byteArrayOf(
                0x12, 0x34, 0x56, 0x78, 0x9A.toByte(), 0xBC.toByte(), 0xDE.toByte(), 0xF0.toByte()
            )
            
            val encrypted = xorEncryption.encrypt(testData)
            val decrypted = xorEncryption.decrypt(encrypted)
            
            assertArrayEquals("XOR round-trip failed for $username/$password", testData, decrypted)
            assertFalse("Encryption should change data", testData.contentEquals(encrypted))
            
            println("✅ XOR encryption test passed for $username/$password")
            println("   Original:  ${testData.joinToString("") { "%02x".format(it) }}")
            println("   Encrypted: ${encrypted.joinToString("") { "%02x".format(it) }}")
        }
    }

    /**
     * Test packet header serialization/deserialization
     */
    @Test
    fun testPacketHeaderCompatibility() {
        println("=== Testing Packet Header Compatibility ===")
        
        val testHeaders = listOf(
            PacketHeader(PacketType.OPEN, EncryptionMethod.XOR, 0u, 0u),
            PacketHeader(PacketType.OPEN_ACK, EncryptionMethod.XOR, 0x1234u, 0xDEADBEEFu),
            PacketHeader(PacketType.DATA_ENCRYPTED, EncryptionMethod.XOR, 0x1234u, 0xDEADBEEFu),
            PacketHeader(PacketType.DATA, EncryptionMethod.NONE, 0x5678u, 0x12345678u)
        )
        
        for (header in testHeaders) {
            // Serialize header
            val headerBytes = header.toByteArray()
            assertEquals("Header should be 8 bytes", 8, headerBytes.size)
            
            // Parse header back
            val parsedHeader = PacketHeader.fromByteArray(headerBytes)
            
            assertEquals("Type should match", header.type, parsedHeader.type)
            assertEquals("Encrypt should match", header.encrypt, parsedHeader.encrypt)
            assertEquals("SessionID should match", header.sessionID, parsedHeader.sessionID)
            assertEquals("Token should match", header.token, parsedHeader.token)
            
            println("✅ Header test passed: Type=${header.type}, SessionID=0x${header.sessionID.toString(16)}")
        }
    }

    /**
     * Test complete packet creation and parsing
     */
    @Test
    fun testCompletePacketFlow() {
        println("=== Testing Complete Packet Flow ===")
        
        val sessionID = 0x1234u
        val token = 0xDEADBEEFu
        val testData = "Hello from Android!".toByteArray()
        
        // Create encrypted data packet
        val header = PacketHeader(
            type = PacketType.DATA_ENCRYPTED,
            encrypt = EncryptionMethod.XOR,
            sessionID = sessionID,
            token = token
        )
        
        val packet = SDWANPacket(header, testData)
        val packetBytes = packet.toByteArray()
        
        // Parse packet back
        val parsedPacket = SDWANPacket.fromByteArray(packetBytes)
        
        assertEquals("Packet type should match", PacketType.DATA_ENCRYPTED, parsedPacket.header.type)
        assertEquals("Encryption should match", EncryptionMethod.XOR, parsedPacket.header.encrypt)
        assertEquals("SessionID should match", sessionID, parsedPacket.header.sessionID)
        assertEquals("Token should match", token, parsedPacket.header.token)
        assertArrayEquals("Payload should match", testData, parsedPacket.data)
        
        println("✅ Complete packet flow test passed")
        println("   Packet size: ${packetBytes.size} bytes")
        println("   Header: ${packetBytes.sliceArray(0..7).joinToString("") { "%02x".format(it) }}")
    }

    /**
     * Test cross-platform byte order consistency
     */
    @Test
    fun testByteOrderConsistency() {
        println("=== Testing Byte Order Consistency ===")
        
        // Test big-endian header serialization (network byte order)
        val sessionID = 0x1234u
        val token = 0xDEADBEEFu
        
        val header = PacketHeader(
            type = PacketType.DATA_ENCRYPTED,
            encrypt = EncryptionMethod.XOR,
            sessionID = sessionID,
            token = token
        )
        
        val headerBytes = header.toByteArray()
        
        // Manually verify big-endian encoding
        val buffer = ByteBuffer.wrap(headerBytes).order(ByteOrder.BIG_ENDIAN)
        assertEquals("Type should match", PacketType.DATA_ENCRYPTED.value.toByte(), buffer.get(0))
        assertEquals("Encrypt should match", EncryptionMethod.XOR.value.toByte(), buffer.get(1))
        assertEquals("SessionID should match", sessionID.toShort(), buffer.getShort(2))
        assertEquals("Token should match", token.toInt(), buffer.getInt(4))
        
        println("✅ Byte order consistency test passed")
        println("   Header bytes: ${headerBytes.joinToString("") { "%02x".format(it) }}")
        println("   SessionID bytes: ${headerBytes.sliceArray(2..3).joinToString("") { "%02x".format(it) }}")
        println("   Token bytes: ${headerBytes.sliceArray(4..7).joinToString("") { "%02x".format(it) }}")
    }

    /**
     * Test MD5 signature calculation (for control packets)
     */
    @Test
    fun testMD5SignatureCalculation() {
        println("=== Testing MD5 Signature Calculation ===")
        
        val header = PacketHeader(
            type = PacketType.OPEN,
            encrypt = EncryptionMethod.XOR,
            sessionID = 0u,
            token = 0u
        )
        
        val headerBytes = header.toByteArray()
        val signature = calculateMD5Signature(headerBytes)
        
        assertEquals("MD5 signature should be 16 bytes", 16, signature.size)
        
        // Verify signature is deterministic
        val signature2 = calculateMD5Signature(headerBytes)
        assertArrayEquals("MD5 signature should be deterministic", signature, signature2)
        
        println("✅ MD5 signature test passed")
        println("   Header: ${headerBytes.joinToString("") { "%02x".format(it) }}")
        println("   Signature: ${signature.joinToString("") { "%02x".format(it) }}")
    }

    /**
     * Test various data sizes with XOR encryption
     */
    @Test
    fun testXOREncryptionDataSizes() {
        println("=== Testing XOR Encryption with Various Data Sizes ===")
        
        val xorEncryption = XOREncryption.createWithCredentials("testuser", "testpass")
        val testSizes = listOf(1, 7, 8, 9, 15, 16, 17, 24, 32)
        
        for (size in testSizes) {
            val testData = ByteArray(size) { (it * 17 % 256).toByte() }
            
            val encrypted = xorEncryption.encrypt(testData)
            val decrypted = xorEncryption.decrypt(encrypted)
            
            assertArrayEquals("Round-trip failed for size $size", testData, decrypted)
            
            if (size > 0) {
                assertFalse("Encryption should change data for size $size", testData.contentEquals(encrypted))
            }
        }
        
        println("✅ XOR encryption data size tests passed for ${testSizes.size} different sizes")
    }

    /**
     * Test that demonstrates the byte order fix works correctly
     */
    @Test
    fun testByteOrderFixValidation() {
        println("=== Testing Byte Order Fix Validation ===")
        
        val xorEncryption = XOREncryption.createWithCredentials("testuser", "testpass")
        
        // This specific pattern would fail with incorrect byte order
        val testPattern = byteArrayOf(
            0x01, 0x23, 0x45, 0x67, 0x89.toByte(), 0xAB.toByte(), 0xCD.toByte(), 0xEF.toByte(),
            0xFE.toByte(), 0xDC.toByte(), 0xBA.toByte(), 0x98.toByte(), 0x76, 0x54, 0x32, 0x10
        )
        
        val encrypted = xorEncryption.encrypt(testPattern)
        val decrypted = xorEncryption.decrypt(encrypted)
        
        assertArrayEquals("Byte order fix should preserve round-trip", testPattern, decrypted)
        
        // Additional verification: XOR is its own inverse
        val doubleEncrypted = xorEncryption.encrypt(encrypted)
        assertArrayEquals("Double XOR should restore original", testPattern, doubleEncrypted)
        
        println("✅ Byte order fix validation passed")
        println("   Test pattern: ${testPattern.joinToString("") { "%02x".format(it) }}")
        println("   Encrypted:    ${encrypted.joinToString("") { "%02x".format(it) }}")
    }

    private fun calculateMD5Signature(headerBytes: ByteArray): ByteArray {
        val md5 = MessageDigest.getInstance("MD5")
        md5.update(headerBytes)
        md5.update(byteArrayOf(109, 119)) // ASCII 'm', 'w'
        return md5.digest()
    }
}
