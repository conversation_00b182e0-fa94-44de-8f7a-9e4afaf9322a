# Android错误处理改进实现示例

## 概述

本文档提供具体的代码实现示例，展示如何将Android版本的错误处理机制改进为与Windows版本一致的规范。

## 1. 统一错误代码实现示例

### 1.1 VPNErrorCode枚举实现

```kotlin
/**
 * FILE: VPNErrorCode.kt
 * 
 * DESCRIPTION:
 *     统一的VPN错误代码枚举，与Windows版本保持一致
 *     
 * AUTHOR: wei
 * HISTORY: 24/07/2025 create
 */

package com.panabit.client.infrastructure.error

enum class VPNErrorCode(val code: Int, val category: String, val severity: ErrorSeverity) {
    // Network errors (1000-1999)
    NETWORK_UNREACHABLE(1000, "Network", ErrorSeverity.HIGH),
    NETWORK_TIMEOUT(1001, "Network", ErrorSeverity.MEDIUM),
    NETWORK_DNS_FAILURE(1002, "Network", ErrorSeverity.HIGH),
    NETWORK_CONNECTION_RESET(1003, "Network", ErrorSeverity.MEDIUM),
    NETWORK_CONNECTION_CLOSED(1004, "Network", ErrorSeverity.LOW),

    // Authentication errors (2000-2999)
    AUTH_INVALID_CREDENTIALS(2000, "Authentication", ErrorSeverity.HIGH),
    AUTH_EXPIRED_CREDENTIALS(2001, "Authentication", ErrorSeverity.MEDIUM),
    AUTH_RATE_LIMITED(2002, "Authentication", ErrorSeverity.MEDIUM),
    AUTH_ACCOUNT_LOCKED(2003, "Authentication", ErrorSeverity.HIGH),
    AUTH_TOKEN_INVALID(2004, "Authentication", ErrorSeverity.MEDIUM),
    AUTH_TOKEN_EXPIRED(2005, "Authentication", ErrorSeverity.MEDIUM),

    // Tunnel errors (3000-3999)
    TUNNEL_INIT_FAILED(3000, "Tunnel", ErrorSeverity.HIGH),
    TUNNEL_CLOSED_UNEXPECTED(3001, "Tunnel", ErrorSeverity.MEDIUM),
    TUNNEL_DEVICE_ERROR(3003, "Tunnel", ErrorSeverity.HIGH),

    // Platform errors (5000-5999)
    PLATFORM_PERMISSION_DENIED(5001, "Platform", ErrorSeverity.HIGH),
    PLATFORM_SYSTEM_ERROR(5003, "Platform", ErrorSeverity.HIGH),

    // Protocol errors (6000-6999)
    PROTOCOL_HANDSHAKE_FAILED(6003, "Protocol", ErrorSeverity.HIGH),
    PROTOCOL_ENCRYPTION_ERROR(6004, "Protocol", ErrorSeverity.HIGH),

    // General errors (0-999)
    UNKNOWN(0, "General", ErrorSeverity.LOW),
    INTERNAL(1, "General", ErrorSeverity.HIGH);

    companion object {
        fun fromCode(code: Int): VPNErrorCode = values().find { it.code == code } ?: UNKNOWN
        fun getNetworkErrors(): List<VPNErrorCode> = getByCategory("Network")
        fun getAuthErrors(): List<VPNErrorCode> = getByCategory("Authentication")
        fun getByCategory(category: String): List<VPNErrorCode> = values().filter { it.category == category }
    }
}

enum class ErrorSeverity { LOW, MEDIUM, HIGH, CRITICAL }
```

### 1.2 改进的VPNServiceError实现

```kotlin
/**
 * FILE: VPNServiceError.kt (改进版本)
 * 
 * DESCRIPTION:
 *     统一的VPN服务错误类，支持错误代码和本地化
 */

@Parcelize
sealed class VPNServiceError(
    override val message: String,
    val errorCode: VPNErrorCode,
    val timestamp: Long = System.currentTimeMillis(),
    override val cause: Throwable? = null
) : Exception(message, cause), Parcelable {

    @Parcelize
    data class ConnectionFailed(
        val errorMessage: String,
        val serverAddress: String? = null,
        val retryCount: Int = 0,
        val networkType: String? = null,
        val specificErrorCode: VPNErrorCode = VPNErrorCode.NETWORK_UNREACHABLE
    ) : VPNServiceError(errorMessage, specificErrorCode)

    @Parcelize
    data class AuthenticationFailed(
        val errorMessage: String,
        val serverErrorCode: String? = null,
        val isCredentialIssue: Boolean = true,
        val authMethod: String? = null,
        val specificErrorCode: VPNErrorCode = VPNErrorCode.AUTH_INVALID_CREDENTIALS
    ) : VPNServiceError(errorMessage, specificErrorCode)

    @Parcelize
    data class TimeoutError(
        val errorMessage: String,
        val operation: String,
        val timeoutDuration: Long,
        val expectedDuration: Long? = null,
        val specificErrorCode: VPNErrorCode = VPNErrorCode.NETWORK_TIMEOUT
    ) : VPNServiceError(errorMessage, specificErrorCode)

    /**
     * 转换为Flutter兼容的Map格式
     */
    fun toMap(): Map<String, Any> = buildMap {
        put("type", getErrorType())
        put("message", message)
        put("error_code", errorCode.code)
        put("error_category", errorCode.category)
        put("error_severity", errorCode.severity.name)
        put("timestamp", timestamp)
        
        when (this@VPNServiceError) {
            is ConnectionFailed -> {
                put("server_address", serverAddress ?: "")
                put("retry_count", retryCount)
                put("network_type", networkType ?: "")
            }
            is AuthenticationFailed -> {
                put("server_error_code", serverErrorCode ?: "")
                put("is_credential_issue", isCredentialIssue)
                put("auth_method", authMethod ?: "")
            }
            is TimeoutError -> {
                put("operation", operation)
                put("timeout_duration", timeoutDuration)
                put("expected_duration", expectedDuration ?: 0L)
            }
        }
    }

    /**
     * 获取本地化的用户友好消息
     */
    fun getUserFriendlyMessage(locale: Locale = Locale.getDefault()): String {
        return VPNErrorLocalizer.getInstance(locale).getLocalizedUserFriendlyMessage(this)
    }

    /**
     * 获取错误恢复建议
     */
    fun getRecoveryAction(): RecoveryAction {
        return ErrorRecoveryStrategy.getRecoveryAction(this.errorCode)
    }
}
```

## 2. 本地化支持实现示例

### 2.1 VPNErrorLocalizer实现

```kotlin
/**
 * FILE: VPNErrorLocalizer.kt
 * 
 * DESCRIPTION:
 *     VPN错误消息本地化器，支持多语言错误消息
 */

class VPNErrorLocalizer private constructor(private val locale: Locale) {
    
    companion object {
        private val instances = mutableMapOf<String, VPNErrorLocalizer>()
        
        fun getInstance(locale: Locale = Locale.getDefault()): VPNErrorLocalizer {
            val key = locale.language
            return instances.getOrPut(key) { VPNErrorLocalizer(locale) }
        }

        // 英文错误消息模板
        private val englishMessages = mapOf(
            VPNErrorCode.NETWORK_UNREACHABLE to "Network unreachable, please check your internet connection",
            VPNErrorCode.NETWORK_TIMEOUT to "Network operation timed out, please try again",
            VPNErrorCode.NETWORK_DNS_FAILURE to "DNS resolution failed, please check server address",
            VPNErrorCode.AUTH_INVALID_CREDENTIALS to "Invalid username or password",
            VPNErrorCode.AUTH_EXPIRED_CREDENTIALS to "Your credentials have expired, please login again",
            VPNErrorCode.AUTH_RATE_LIMITED to "Too many authentication attempts, please wait and try again",
            VPNErrorCode.TUNNEL_INIT_FAILED to "Failed to initialize VPN tunnel",
            VPNErrorCode.PLATFORM_PERMISSION_DENIED to "VPN permission denied, please grant VPN access",
            VPNErrorCode.PROTOCOL_HANDSHAKE_FAILED to "VPN protocol handshake failed"
        )

        // 中文错误消息模板
        private val chineseMessages = mapOf(
            VPNErrorCode.NETWORK_UNREACHABLE to "网络不可达，请检查您的网络连接",
            VPNErrorCode.NETWORK_TIMEOUT to "网络操作超时，请重试",
            VPNErrorCode.NETWORK_DNS_FAILURE to "DNS解析失败，请检查服务器地址",
            VPNErrorCode.AUTH_INVALID_CREDENTIALS to "用户名或密码错误",
            VPNErrorCode.AUTH_EXPIRED_CREDENTIALS to "您的凭据已过期，请重新登录",
            VPNErrorCode.AUTH_RATE_LIMITED to "认证尝试次数过多，请稍后重试",
            VPNErrorCode.TUNNEL_INIT_FAILED to "VPN隧道初始化失败",
            VPNErrorCode.PLATFORM_PERMISSION_DENIED to "VPN权限被拒绝，请授予VPN访问权限",
            VPNErrorCode.PROTOCOL_HANDSHAKE_FAILED to "VPN协议握手失败"
        )
    }

    /**
     * 获取本地化的错误消息
     */
    fun getLocalizedMessage(errorCode: VPNErrorCode): String {
        val messages = when (locale.language) {
            "zh" -> chineseMessages
            "en" -> englishMessages
            else -> englishMessages
        }
        return messages[errorCode] ?: getDefaultMessage(errorCode)
    }

    /**
     * 获取用户友好的本地化错误消息
     */
    fun getLocalizedUserFriendlyMessage(error: VPNServiceError): String {
        return when (error) {
            is VPNServiceError.ConnectionFailed -> {
                val baseMessage = getLocalizedMessage(error.errorCode)
                if (error.retryCount > 0) {
                    val retryInfo = if (locale.language == "zh") {
                        "（已重试${error.retryCount}次）"
                    } else {
                        " (retried ${error.retryCount} times)"
                    }
                    baseMessage + retryInfo
                } else {
                    baseMessage
                }
            }
            is VPNServiceError.AuthenticationFailed -> {
                if (error.isCredentialIssue) {
                    getLocalizedMessage(VPNErrorCode.AUTH_INVALID_CREDENTIALS)
                } else {
                    if (locale.language == "zh") {
                        "服务器认证失败，请稍后重试"
                    } else {
                        "Server authentication failed, please try again later"
                    }
                }
            }
            is VPNServiceError.TimeoutError -> {
                val baseMessage = getLocalizedMessage(error.errorCode)
                val timeoutInfo = if (locale.language == "zh") {
                    "（超时时间：${error.timeoutDuration / 1000}秒）"
                } else {
                    " (timeout: ${error.timeoutDuration / 1000}s)"
                }
                baseMessage + timeoutInfo
            }
            else -> getLocalizedMessage(error.errorCode)
        }
    }

    private fun getDefaultMessage(errorCode: VPNErrorCode): String {
        return if (locale.language == "zh") {
            "未知错误（错误代码：${errorCode.code}）"
        } else {
            "Unknown error (error code: ${errorCode.code})"
        }
    }
}
```

## 3. 统一错误处理器实现示例

### 3.1 UnifiedErrorHandler实现

```kotlin
/**
 * FILE: UnifiedErrorHandler.kt
 * 
 * DESCRIPTION:
 *     统一错误处理器，提供与Windows版本一致的错误处理流程
 */

class UnifiedErrorHandler(
    private val context: Context,
    private val eventSender: VPNStateEventSender? = null
) {
    private val localizer = VPNErrorLocalizer.getInstance()
    private val recoveryStrategy = ErrorRecoveryStrategy()
    private val analytics = ErrorAnalytics(context)

    /**
     * 处理VPN错误的主要入口点
     */
    suspend fun handleError(
        error: VPNServiceError,
        contextInfo: Map<String, Any> = emptyMap(),
        shouldNotifyUser: Boolean = true
    ): ErrorHandlingResult {
        
        // 1. 记录错误分析数据
        analytics.recordError(error, contextInfo)
        
        // 2. 记录详细日志
        logError(error, contextInfo)
        
        // 3. 获取本地化用户消息
        val userMessage = error.getUserFriendlyMessage()
        
        // 4. 确定恢复策略
        val recoveryAction = recoveryStrategy.getRecoveryAction(error.errorCode)
        
        // 5. 显示用户通知（如果需要）
        if (shouldNotifyUser) {
            showErrorNotification(error, userMessage)
        }
        
        // 6. 发送错误事件到Flutter层
        sendErrorEventToFlutter(error, userMessage, recoveryAction, contextInfo)
        
        // 7. 执行自动恢复（如果适用）
        val autoRecoveryResult = if (recoveryAction == RecoveryAction.AUTO_RETRY) {
            executeAutoRecovery(error, contextInfo)
        } else null
        
        return ErrorHandlingResult(
            userMessage = userMessage,
            recoveryAction = recoveryAction,
            errorCode = error.errorCode,
            canRetry = recoveryAction.canRetry,
            autoRecoveryExecuted = autoRecoveryResult != null,
            autoRecoverySuccess = autoRecoveryResult?.isSuccess ?: false
        )
    }

    /**
     * 记录错误日志
     */
    private fun logError(error: VPNServiceError, contextInfo: Map<String, Any>) {
        val logData = buildMap {
            put("error_code", error.errorCode.code)
            put("error_category", error.errorCode.category)
            put("error_severity", error.errorCode.severity.name)
            put("error_message", error.message)
            put("timestamp", error.timestamp)
            putAll(contextInfo)
        }
        
        when (error.errorCode.severity) {
            ErrorSeverity.CRITICAL, ErrorSeverity.HIGH -> {
                logError("Critical VPN error occurred", logData)
            }
            ErrorSeverity.MEDIUM -> {
                logWarn("VPN error occurred", logData)
            }
            ErrorSeverity.LOW -> {
                logInfo("Minor VPN error occurred", logData)
            }
        }
    }

    /**
     * 发送错误事件到Flutter层
     */
    private suspend fun sendErrorEventToFlutter(
        error: VPNServiceError,
        userMessage: String,
        recoveryAction: RecoveryAction,
        contextInfo: Map<String, Any>
    ) {
        try {
            val errorEvent = mapOf(
                "type" to "error",
                "error_code" to error.errorCode.code,
                "error_category" to error.errorCode.category,
                "user_message" to userMessage,
                "recovery_action" to recoveryAction.name,
                "can_retry" to recoveryAction.canRetry,
                "timestamp" to error.timestamp,
                "context" to contextInfo,
                "error_details" to error.toMap()
            )
            
            eventSender?.sendErrorEvent(errorEvent)
            
        } catch (e: Exception) {
            logError("Failed to send error event to Flutter", mapOf(
                "original_error_code" to error.errorCode.code,
                "send_error" to e.message
            ))
        }
    }

    /**
     * 执行自动恢复
     */
    private suspend fun executeAutoRecovery(
        error: VPNServiceError,
        contextInfo: Map<String, Any>
    ): Result<Unit> {
        return try {
            when (error.errorCode.category) {
                "Network" -> executeNetworkRecovery(error)
                "Authentication" -> executeAuthRecovery(error)
                else -> Result.failure(Exception("No auto recovery available"))
            }
        } catch (e: Exception) {
            logError("Auto recovery failed", mapOf(
                "error_code" to error.errorCode.code,
                "recovery_error" to e.message
            ))
            Result.failure(e)
        }
    }

    // ... 其他辅助方法
}

/**
 * 错误处理结果数据类
 */
data class ErrorHandlingResult(
    val userMessage: String,
    val recoveryAction: RecoveryAction,
    val errorCode: VPNErrorCode,
    val canRetry: Boolean,
    val autoRecoveryExecuted: Boolean = false,
    val autoRecoverySuccess: Boolean = false
)

/**
 * 错误恢复策略枚举
 */
enum class RecoveryAction(val canRetry: Boolean) {
    AUTO_RETRY(true),        // 自动重试
    MANUAL_RETRY(true),      // 手动重试
    USER_ACTION(false),      // 需要用户干预
    RESET_CONNECTION(true),  // 重置连接
    RESTART_SERVICE(false),  // 重启服务
    FATAL(false)            // 致命错误
}
```

## 4. 在ConnectionManager中的集成示例

### 4.1 修改ConnectionManager使用新的错误处理

```kotlin
/**
 * 在ConnectionManager中集成统一错误处理
 */
class ConnectionManager {
    private val errorHandler = UnifiedErrorHandler(context, vpnStateEventSender)
    private val timeoutManager = TimeoutManager()

    /**
     * 改进的认证方法
     */
    private suspend fun performAuthentication(
        connection: UDPConnection, 
        server: ServerInfo, 
        targetIP: String? = null
    ): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // 设置认证超时
            val authTimeout = timeoutManager.getTimeoutForOperation("authentication")
            
            val authResult = withTimeout(authTimeout) {
                currentProtocolHandler?.authenticateWithSocket(
                    socket = connection.socket,
                    username = credentials.first,
                    password = credentials.second,
                    targetIP = targetIP
                )
            }

            if (authResult?.success == true) {
                logInfo("Authentication successful")
                Result.success(Unit)
            } else {
                // 使用统一错误处理
                val error = VPNServiceError.AuthenticationFailed(
                    errorMessage = authResult?.errorMessage ?: "Authentication failed",
                    serverErrorCode = authResult?.errorCode,
                    isCredentialIssue = isCredentialError(authResult?.errorMessage),
                    specificErrorCode = mapAuthErrorCode(authResult?.errorCode)
                )
                
                val handlingResult = errorHandler.handleError(error, mapOf(
                    "server_id" to server.id,
                    "server_address" to server.serverName,
                    "auth_method" to "SDWAN"
                ))
                
                Result.failure(error)
            }

        } catch (e: TimeoutCancellationException) {
            // 处理认证超时
            val timeoutError = timeoutManager.createTimeoutError(
                operation = "authentication",
                duration = timeoutManager.getTimeoutForOperation("authentication"),
                additionalInfo = mapOf(
                    "server_id" to server.id,
                    "target_ip" to (targetIP ?: "unknown")
                )
            )
            
            errorHandler.handleError(timeoutError)
            Result.failure(timeoutError)
            
        } catch (e: Exception) {
            // 处理其他认证异常
            val error = VPNServiceError.AuthenticationFailed(
                errorMessage = "Authentication failed: ${e.message}",
                specificErrorCode = VPNErrorCode.AUTH_INVALID_CREDENTIALS
            )
            
            errorHandler.handleError(error, mapOf(
                "exception_type" to e::class.java.simpleName,
                "server_id" to server.id
            ))
            
            Result.failure(error)
        }
    }

    /**
     * 映射认证错误代码
     */
    private fun mapAuthErrorCode(serverErrorCode: String?): VPNErrorCode {
        return when (serverErrorCode?.lowercase()) {
            "invalid_credentials", "401" -> VPNErrorCode.AUTH_INVALID_CREDENTIALS
            "expired_credentials", "403" -> VPNErrorCode.AUTH_EXPIRED_CREDENTIALS
            "rate_limited", "429" -> VPNErrorCode.AUTH_RATE_LIMITED
            "account_locked" -> VPNErrorCode.AUTH_ACCOUNT_LOCKED
            else -> VPNErrorCode.AUTH_INVALID_CREDENTIALS
        }
    }

    /**
     * 判断是否为凭据错误
     */
    private fun isCredentialError(errorMessage: String?): Boolean {
        val credentialKeywords = listOf(
            "invalid", "wrong", "incorrect", "credential", 
            "username", "password", "unauthorized"
        )
        return errorMessage?.lowercase()?.let { msg ->
            credentialKeywords.any { keyword -> msg.contains(keyword) }
        } ?: true
    }
}
```

## 5. 使用示例

### 5.1 在应用中使用改进的错误处理

```kotlin
/**
 * 使用示例：在VPN服务中处理连接错误
 */
class VPNService {
    private val errorHandler = UnifiedErrorHandler(this)

    suspend fun connectToVPN(server: ServerInfo) {
        try {
            // 尝试连接
            val result = connectionManager.connect(server)
            
            if (result.isFailure) {
                val exception = result.exceptionOrNull()
                if (exception is VPNServiceError) {
                    // 使用统一错误处理
                    val handlingResult = errorHandler.handleError(
                        error = exception,
                        contextInfo = mapOf(
                            "operation" to "connect_to_vpn",
                            "server_id" to server.id,
                            "server_name" to server.name,
                            "connection_attempt" to 1
                        ),
                        shouldNotifyUser = true
                    )
                    
                    // 根据恢复策略决定后续操作
                    when (handlingResult.recoveryAction) {
                        RecoveryAction.MANUAL_RETRY -> {
                            // 允许用户手动重试
                            showRetryOption(handlingResult.userMessage)
                        }
                        RecoveryAction.USER_ACTION -> {
                            // 需要用户干预
                            showUserActionRequired(handlingResult.userMessage)
                        }
                        RecoveryAction.AUTO_RETRY -> {
                            // 已自动重试，检查结果
                            if (!handlingResult.autoRecoverySuccess) {
                                showRetryOption(handlingResult.userMessage)
                            }
                        }
                        else -> {
                            // 其他情况
                            showErrorMessage(handlingResult.userMessage)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            // 处理未预期的异常
            val unknownError = VPNServiceError.ConnectionFailed(
                errorMessage = "Unexpected error: ${e.message}",
                specificErrorCode = VPNErrorCode.INTERNAL
            )
            errorHandler.handleError(unknownError)
        }
    }
}
```

通过以上实现示例，Android版本的错误处理机制将具备：

1. **统一的错误代码体系** - 与Windows版本完全兼容
2. **完整的国际化支持** - 支持中英文动态切换
3. **智能的错误恢复策略** - 根据错误类型自动选择恢复方案
4. **详细的错误分析** - 便于问题诊断和用户体验优化
5. **一致的用户体验** - 跨平台统一的错误提示和处理流程

这些改进将显著提升Android版本的错误处理质量，确保与Windows版本的一致性。
