/**
 * 在线Kotlin测试代码
 * 可以在 https://play.kotlinlang.org/ 运行
 * 验证XOR加密算法的核心逻辑
 */

import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.security.MessageDigest

// 简化的XOR加密实现（核心逻辑）
class SimpleXOREncryption(private val sessionKey: ByteArray) {
    
    fun encrypt(data: ByteArray): ByteArray {
        if (sessionKey.size != 16) {
            throw IllegalArgumentException("Session key must be 16 bytes")
        }
        
        val result = data.copyOf()
        val blockCount = result.size / 8
        
        // 🔧 修复：使用原生字节序（不指定order，使用默认的BIG_ENDIAN）
        val keyBuffer = ByteBuffer.wrap(sessionKey)
        val key32_0 = keyBuffer.getInt(0)
        val key32_1 = keyBuffer.getInt(4)
        
        // 处理每个8字节块
        for (i in 0 until blockCount) {
            val blockOffset = i * 8
            val dataBuffer = ByteBuffer.wrap(result, blockOffset, 8)
            
            val data32_0 = dataBuffer.getInt(0)
            val data32_1 = dataBuffer.getInt(4)
            
            val xored32_0 = data32_0 xor key32_0
            val xored32_1 = data32_1 xor key32_1
            
            dataBuffer.putInt(0, xored32_0)
            dataBuffer.putInt(4, xored32_1)
        }
        
        // 处理剩余字节
        val remainingBytes = result.size % 8
        if (remainingBytes > 0) {
            val startOffset = blockCount * 8
            for (i in 0 until remainingBytes) {
                result[startOffset + i] = (result[startOffset + i].toInt() xor sessionKey[i].toInt()).toByte()
            }
        }
        
        return result
    }
    
    fun decrypt(data: ByteArray): ByteArray {
        return encrypt(data) // XOR是对称的
    }
}

// 生成session key的方法
fun generateSessionKey(username: String, password: String): ByteArray {
    val combined = username + password
    val md5 = MessageDigest.getInstance("MD5")
    return md5.digest(combined.toByteArray())
}

// 数据包头结构
data class PacketHeader(
    val type: Byte,
    val encrypt: Byte,
    val sessionID: Short,
    val token: Int
) {
    fun toByteArray(): ByteArray {
        val buffer = ByteBuffer.allocate(8).order(ByteOrder.BIG_ENDIAN)
        buffer.put(type)
        buffer.put(encrypt)
        buffer.putShort(sessionID)
        buffer.putInt(token)
        return buffer.array()
    }
    
    companion object {
        fun fromByteArray(data: ByteArray): PacketHeader {
            val buffer = ByteBuffer.wrap(data).order(ByteOrder.BIG_ENDIAN)
            return PacketHeader(
                type = buffer.get(),
                encrypt = buffer.get(),
                sessionID = buffer.getShort(),
                token = buffer.getInt()
            )
        }
    }
}

fun main() {
    println("=== Android XOR加密修复验证 ===")
    
    // 测试1：XOR加密基本功能
    println("\n🔧 测试1：XOR加密基本功能")
    try {
        val sessionKey = generateSessionKey("testuser", "testpass")
        val xorEncryption = SimpleXOREncryption(sessionKey)
        
        val testData = byteArrayOf(0x12, 0x34, 0x56, 0x78, 0x9A.toByte(), 0xBC.toByte(), 0xDE.toByte(), 0xF0.toByte())
        println("原始数据: ${testData.joinToString("") { "%02x".format(it) }}")
        
        val encrypted = xorEncryption.encrypt(testData)
        println("加密结果: ${encrypted.joinToString("") { "%02x".format(it) }}")
        
        val decrypted = xorEncryption.decrypt(encrypted)
        println("解密结果: ${decrypted.joinToString("") { "%02x".format(it) }}")
        
        if (testData.contentEquals(decrypted)) {
            println("✅ XOR加密测试通过")
        } else {
            println("❌ XOR加密测试失败")
        }
        
        // 验证XOR的数学性质
        val doubleEncrypted = xorEncryption.encrypt(encrypted)
        if (testData.contentEquals(doubleEncrypted)) {
            println("✅ XOR数学性质验证通过（加密两次恢复原始数据）")
        } else {
            println("❌ XOR数学性质验证失败")
        }
        
    } catch (e: Exception) {
        println("❌ XOR加密测试异常: ${e.message}")
    }
    
    // 测试2：数据包头字节序
    println("\n🔧 测试2：数据包头字节序")
    try {
        val header = PacketHeader(
            type = 0x17, // DATA_ENCRYPTED
            encrypt = 0x01, // XOR
            sessionID = 0x1234.toShort(),
            token = 0xDEADBEEF.toInt()
        )
        
        val headerBytes = header.toByteArray()
        println("包头字节: ${headerBytes.joinToString("") { "%02x".format(it) }}")
        
        // 验证字节序
        if (headerBytes[0] == 0x17.toByte() && 
            headerBytes[1] == 0x01.toByte() &&
            headerBytes[2] == 0x12.toByte() && 
            headerBytes[3] == 0x34.toByte() &&
            headerBytes[4] == 0xDE.toByte() &&
            headerBytes[5] == 0xAD.toByte() &&
            headerBytes[6] == 0xBE.toByte() &&
            headerBytes[7] == 0xEF.toByte()) {
            println("✅ 数据包头字节序正确（大端序）")
        } else {
            println("❌ 数据包头字节序错误")
        }
        
        // 解析测试
        val parsedHeader = PacketHeader.fromByteArray(headerBytes)
        if (header == parsedHeader) {
            println("✅ 数据包头解析测试通过")
        } else {
            println("❌ 数据包头解析测试失败")
        }
        
    } catch (e: Exception) {
        println("❌ 数据包头测试异常: ${e.message}")
    }
    
    // 测试3：不同数据大小的XOR加密
    println("\n🔧 测试3：不同数据大小的XOR加密")
    try {
        val sessionKey = generateSessionKey("admin", "123456")
        val xorEncryption = SimpleXOREncryption(sessionKey)
        
        val testSizes = listOf(1, 7, 8, 9, 15, 16, 17)
        var allPassed = true
        
        for (size in testSizes) {
            val testData = ByteArray(size) { (it * 17 % 256).toByte() }
            val encrypted = xorEncryption.encrypt(testData)
            val decrypted = xorEncryption.decrypt(encrypted)
            
            if (testData.contentEquals(decrypted)) {
                println("✅ 大小${size}字节测试通过")
            } else {
                println("❌ 大小${size}字节测试失败")
                allPassed = false
            }
        }
        
        if (allPassed) {
            println("✅ 所有大小测试通过")
        }
        
    } catch (e: Exception) {
        println("❌ 不同大小测试异常: ${e.message}")
    }
    
    // 测试4：模拟Go后端数据包
    println("\n🔧 测试4：模拟Go后端数据包解析")
    try {
        // 模拟Go后端生成的OPEN_ACK包（简化版）
        val goPacketBytes = byteArrayOf(
            0x12, 0x01, 0x12, 0x34, // 包头：OPEN_ACK, XOR, SessionID=0x1234
            0xDE.toByte(), 0xAD.toByte(), 0xBE.toByte(), 0xEF.toByte(), // Token=0xDEADBEEF
            0x10, 0x04, 0xC0.toByte(), 0xA8.toByte(), 0x01, 0x01 // 简单属性：服务器IP
        )
        
        println("模拟Go数据包: ${goPacketBytes.joinToString("") { "%02x".format(it) }}")
        
        // 解析包头
        val header = PacketHeader.fromByteArray(goPacketBytes.sliceArray(0..7))
        println("解析结果:")
        println("  包类型: 0x${header.type.toString(16).padStart(2, '0')}")
        println("  加密方法: 0x${header.encrypt.toString(16).padStart(2, '0')}")
        println("  SessionID: 0x${header.sessionID.toString(16).padStart(4, '0')}")
        println("  Token: 0x${header.token.toString(16).padStart(8, '0')}")
        
        if (header.type == 0x12.toByte() && 
            header.encrypt == 0x01.toByte() &&
            header.sessionID == 0x1234.toShort() &&
            header.token == 0xDEADBEEF.toInt()) {
            println("✅ Go数据包解析测试通过")
        } else {
            println("❌ Go数据包解析测试失败")
        }
        
    } catch (e: Exception) {
        println("❌ Go数据包解析异常: ${e.message}")
    }
    
    println("\n=== 测试完成 ===")
    println("如果所有测试都显示✅，说明Android修复是有效的")
}
