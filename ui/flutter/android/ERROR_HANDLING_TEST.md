# Android错误处理机制测试验证

## 概述

本文档提供了测试验证新实施的Android错误处理机制的方法和示例，确保与Windows版本的兼容性和功能完整性。

## 1. 错误代码体系测试

### 1.1 基本错误代码测试

```kotlin
// 测试错误代码枚举
fun testVPNErrorCode() {
    // 测试错误代码范围
    assert(VPNErrorCode.NETWORK_UNREACHABLE.code == 1000)
    assert(VPNErrorCode.AUTH_INVALID_CREDENTIALS.code == 2000)
    assert(VPNErrorCode.TUNNEL_INIT_FAILED.code == 3000)
    
    // 测试错误分类
    assert(VPNErrorCode.NETWORK_UNREACHABLE.category == "Network")
    assert(VPNErrorCode.AUTH_INVALID_CREDENTIALS.category == "Authentication")
    
    // 测试错误严重程度
    assert(VPNErrorCode.NETWORK_UNREACHABLE.severity == ErrorSeverity.HIGH)
    assert(VPNErrorCode.AUTH_INVALID_CREDENTIALS.severity == ErrorSeverity.HIGH)
    
    // 测试从代码获取枚举
    assert(VPNErrorCode.fromCode(1000) == VPNErrorCode.NETWORK_UNREACHABLE)
    assert(VPNErrorCode.fromCode(9999) == VPNErrorCode.UNKNOWN)
    
    // 测试分类查询
    val networkErrors = VPNErrorCode.getNetworkErrors()
    assert(networkErrors.contains(VPNErrorCode.NETWORK_UNREACHABLE))
    assert(networkErrors.contains(VPNErrorCode.NETWORK_TIMEOUT))
    
    println("✅ VPNErrorCode tests passed")
}
```

### 1.2 错误代码兼容性测试

```kotlin
// 测试与Windows版本的兼容性
fun testWindowsCompatibility() {
    // 验证关键错误代码与Windows版本一致
    val windowsErrorCodes = mapOf(
        1000 to "NETWORK_UNREACHABLE",
        1001 to "NETWORK_TIMEOUT", 
        1002 to "NETWORK_DNS_FAILURE",
        2000 to "AUTH_INVALID_CREDENTIALS",
        2001 to "AUTH_EXPIRED_CREDENTIALS",
        2002 to "AUTH_RATE_LIMITED"
    )
    
    windowsErrorCodes.forEach { (code, expectedName) ->
        val errorCode = VPNErrorCode.fromCode(code)
        assert(errorCode.name == expectedName) {
            "Error code $code should map to $expectedName, but got ${errorCode.name}"
        }
    }
    
    println("✅ Windows compatibility tests passed")
}
```

## 2. 本地化功能测试

### 2.1 多语言消息测试

```kotlin
// 测试中英文错误消息
fun testLocalization() {
    // 测试中文本地化
    val chineseLocalizer = VPNErrorLocalizer.getInstance(Locale.CHINESE)
    val chineseMessage = chineseLocalizer.getLocalizedMessage(VPNErrorCode.NETWORK_UNREACHABLE)
    assert(chineseMessage.contains("网络不可达"))
    
    // 测试英文本地化
    val englishLocalizer = VPNErrorLocalizer.getInstance(Locale.ENGLISH)
    val englishMessage = englishLocalizer.getLocalizedMessage(VPNErrorCode.NETWORK_UNREACHABLE)
    assert(englishMessage.contains("Network unreachable"))
    
    // 测试用户友好消息
    val connectionError = VPNServiceError.ConnectionFailed(
        errorMessage = "Connection failed",
        retryCount = 2,
        specificErrorCode = VPNErrorCode.NETWORK_UNREACHABLE
    )
    
    val chineseFriendlyMessage = chineseLocalizer.getLocalizedUserFriendlyMessage(connectionError)
    assert(chineseFriendlyMessage.contains("已重试2次"))
    
    val englishFriendlyMessage = englishLocalizer.getLocalizedUserFriendlyMessage(connectionError)
    assert(englishFriendlyMessage.contains("retried 2 times"))
    
    println("✅ Localization tests passed")
}
```

### 2.2 恢复建议测试

```kotlin
// 测试错误恢复建议
fun testRecoverySuggestions() {
    val localizer = VPNErrorLocalizer.getInstance()
    
    // 测试网络错误建议
    val networkSuggestion = localizer.getRecoverySuggestion(VPNErrorCode.NETWORK_UNREACHABLE)
    assert(networkSuggestion.isNotEmpty())
    
    // 测试认证错误建议
    val authSuggestion = localizer.getRecoverySuggestion(VPNErrorCode.AUTH_INVALID_CREDENTIALS)
    assert(authSuggestion.isNotEmpty())
    
    // 测试权限错误建议
    val permissionSuggestion = localizer.getRecoverySuggestion(VPNErrorCode.PLATFORM_PERMISSION_DENIED)
    assert(permissionSuggestion.isNotEmpty())
    
    println("✅ Recovery suggestion tests passed")
}
```

## 3. 统一错误处理器测试

### 3.1 错误处理流程测试

```kotlin
// 测试统一错误处理流程
suspend fun testUnifiedErrorHandler(context: Context) {
    val errorHandler = UnifiedErrorHandler(context)
    
    // 测试网络错误处理
    val networkError = VPNServiceError.NetworkUnavailable(
        errorMessage = "Network unreachable",
        isTemporary = true,
        specificErrorCode = VPNErrorCode.NETWORK_UNREACHABLE
    )
    
    val result = errorHandler.handleError(
        error = networkError,
        contextInfo = mapOf("test_scenario" to "network_error"),
        shouldNotifyUser = false,
        shouldAttemptRecovery = false
    )
    
    assert(result.errorCode == VPNErrorCode.NETWORK_UNREACHABLE)
    assert(result.canRetry)
    assert(result.userMessage.isNotEmpty())
    
    // 测试认证错误处理
    val authError = VPNServiceError.AuthenticationFailed(
        errorMessage = "Invalid credentials",
        isCredentialIssue = true,
        specificErrorCode = VPNErrorCode.AUTH_INVALID_CREDENTIALS
    )
    
    val authResult = errorHandler.handleError(
        error = authError,
        contextInfo = mapOf("test_scenario" to "auth_error"),
        shouldNotifyUser = false
    )
    
    assert(authResult.errorCode == VPNErrorCode.AUTH_INVALID_CREDENTIALS)
    assert(!authResult.canRetry) // 凭据错误通常不能自动重试
    
    println("✅ Unified error handler tests passed")
}
```

### 3.2 错误恢复策略测试

```kotlin
// 测试错误恢复策略
fun testErrorRecoveryStrategy() {
    val strategy = ErrorRecoveryStrategy()
    
    // 测试网络错误恢复策略
    val networkAction = strategy.getRecoveryAction(VPNErrorCode.NETWORK_TIMEOUT)
    assert(networkAction == RecoveryAction.AUTO_RETRY)
    
    val dnsAction = strategy.getRecoveryAction(VPNErrorCode.NETWORK_DNS_FAILURE)
    assert(dnsAction == RecoveryAction.USER_ACTION)
    
    // 测试认证错误恢复策略
    val credentialAction = strategy.getRecoveryAction(VPNErrorCode.AUTH_INVALID_CREDENTIALS)
    assert(credentialAction == RecoveryAction.USER_ACTION)
    
    val rateLimitAction = strategy.getRecoveryAction(VPNErrorCode.AUTH_RATE_LIMITED)
    assert(rateLimitAction == RecoveryAction.AUTO_RETRY)
    
    // 测试平台错误恢复策略
    val permissionAction = strategy.getRecoveryAction(VPNErrorCode.PLATFORM_PERMISSION_DENIED)
    assert(permissionAction == RecoveryAction.USER_ACTION)
    
    println("✅ Error recovery strategy tests passed")
}
```

## 4. 超时管理器测试

### 4.1 超时配置测试

```kotlin
// 测试超时配置
fun testTimeoutManager() {
    val timeoutManager = TimeoutManager()
    
    // 测试标准超时配置
    assert(timeoutManager.getTimeoutForOperation("connection") == 25_000L)
    assert(timeoutManager.getTimeoutForOperation("authentication") == 15_000L)
    assert(timeoutManager.getTimeoutForOperation("heartbeat") == 45_000L)
    
    // 测试未知操作的默认超时
    assert(timeoutManager.getTimeoutForOperation("unknown") == 30_000L)
    
    // 测试超时验证
    assert(timeoutManager.validateTimeout("connection", 25_000L))
    assert(!timeoutManager.validateTimeout("connection", 1_000L)) // 太短
    assert(!timeoutManager.validateTimeout("connection", 100_000L)) // 太长
    
    println("✅ Timeout manager configuration tests passed")
}
```

### 4.2 超时错误创建测试

```kotlin
// 测试超时错误创建
fun testTimeoutErrorCreation() {
    val timeoutManager = TimeoutManager()
    
    // 测试连接超时错误
    val connectionTimeout = timeoutManager.createTimeoutError(
        operation = "connection",
        duration = 30_000L,
        additionalInfo = mapOf("server" to "test.example.com")
    )
    
    assert(connectionTimeout.operation == "connection")
    assert(connectionTimeout.timeoutDuration == 30_000L)
    assert(connectionTimeout.specificErrorCode == VPNErrorCode.NETWORK_TIMEOUT)
    
    // 测试认证超时错误
    val authTimeout = timeoutManager.createTimeoutError(
        operation = "authentication",
        duration = 20_000L
    )
    
    assert(authTimeout.specificErrorCode == VPNErrorCode.AUTH_TOKEN_EXPIRED)
    
    // 测试DNS超时错误
    val dnsTimeout = timeoutManager.createTimeoutError(
        operation = "dns_resolution",
        duration = 15_000L
    )
    
    assert(dnsTimeout.specificErrorCode == VPNErrorCode.NETWORK_DNS_FAILURE)
    
    println("✅ Timeout error creation tests passed")
}
```

## 5. VPNServiceError集成测试

### 5.1 错误对象创建测试

```kotlin
// 测试VPNServiceError对象创建
fun testVPNServiceErrorCreation() {
    // 测试连接失败错误
    val connectionError = VPNServiceError.ConnectionFailed(
        errorMessage = "Failed to connect to server",
        serverAddress = "***********",
        retryCount = 1,
        networkType = "WiFi",
        specificErrorCode = VPNErrorCode.NETWORK_UNREACHABLE
    )
    
    assert(connectionError.errorCode == VPNErrorCode.NETWORK_UNREACHABLE)
    assert(connectionError.message == "Failed to connect to server")
    assert(connectionError.serverAddress == "***********")
    
    // 测试认证失败错误
    val authError = VPNServiceError.AuthenticationFailed(
        errorMessage = "Invalid username or password",
        serverErrorCode = "401",
        isCredentialIssue = true,
        authMethod = "SDWAN",
        specificErrorCode = VPNErrorCode.AUTH_INVALID_CREDENTIALS
    )
    
    assert(authError.errorCode == VPNErrorCode.AUTH_INVALID_CREDENTIALS)
    assert(authError.isCredentialIssue)
    assert(authError.authMethod == "SDWAN")
    
    println("✅ VPNServiceError creation tests passed")
}
```

### 5.2 Flutter映射测试

```kotlin
// 测试Flutter映射功能
fun testFlutterMapping() {
    val error = VPNServiceError.TimeoutError(
        errorMessage = "Connection timeout",
        operation = "connection",
        timeoutDuration = 25_000L,
        expectedDuration = 25_000L,
        specificErrorCode = VPNErrorCode.NETWORK_TIMEOUT
    )
    
    val errorMap = error.toMap()
    
    // 验证基本字段
    assert(errorMap["type"] == "TIMEOUT_ERROR")
    assert(errorMap["message"] == "Connection timeout")
    assert(errorMap["error_code"] == 1001)
    assert(errorMap["error_category"] == "Network")
    assert(errorMap["error_severity"] == "MEDIUM")
    
    // 验证特定字段
    assert(errorMap["operation"] == "connection")
    assert(errorMap["timeout_duration"] == 25_000L)
    assert(errorMap["expected_duration"] == 25_000L)
    
    // 验证时间戳存在
    assert(errorMap.containsKey("timestamp"))
    assert(errorMap["timestamp"] is Long)
    
    println("✅ Flutter mapping tests passed")
}
```

## 6. ConnectionManager集成测试

### 6.1 认证错误处理测试

```kotlin
// 测试ConnectionManager中的认证错误处理
suspend fun testConnectionManagerAuthError() {
    // 模拟认证失败场景
    // 这需要在实际的ConnectionManager实例中测试
    
    // 验证错误映射功能
    val connectionManager = ConnectionManager(context, serverManager, serviceScope, routingManager)
    
    // 测试服务器错误代码映射
    assert(connectionManager.mapAuthErrorCode("401") == VPNErrorCode.AUTH_INVALID_CREDENTIALS)
    assert(connectionManager.mapAuthErrorCode("403") == VPNErrorCode.AUTH_EXPIRED_CREDENTIALS)
    assert(connectionManager.mapAuthErrorCode("429") == VPNErrorCode.AUTH_RATE_LIMITED)
    
    // 测试凭据错误判断
    assert(connectionManager.isCredentialError("Invalid username or password"))
    assert(connectionManager.isCredentialError("Authentication failed"))
    assert(!connectionManager.isCredentialError("Server temporarily unavailable"))
    
    println("✅ ConnectionManager auth error tests passed")
}
```

## 7. 完整集成测试

### 7.1 端到端错误处理测试

```kotlin
// 完整的端到端错误处理测试
suspend fun testEndToEndErrorHandling(context: Context) {
    val errorHandler = UnifiedErrorHandler(context)
    val timeoutManager = TimeoutManager()
    
    // 模拟完整的错误处理流程
    val originalError = Exception("Network connection failed")
    
    // 1. 创建VPN错误
    val vpnError = VPNServiceError.ConnectionFailed(
        errorMessage = "Connection failed: ${originalError.message}",
        serverAddress = "test.example.com",
        retryCount = 0,
        specificErrorCode = VPNErrorCode.NETWORK_UNREACHABLE
    )
    
    // 2. 处理错误
    val result = errorHandler.handleError(
        error = vpnError,
        contextInfo = mapOf(
            "operation" to "connect_to_vpn",
            "server_id" to "test_server",
            "attempt" to 1
        )
    )
    
    // 3. 验证处理结果
    assert(result.errorCode == VPNErrorCode.NETWORK_UNREACHABLE)
    assert(result.canRetry)
    assert(result.userMessage.isNotEmpty())
    assert(result.recoveryAction.canRetry)
    
    // 4. 验证本地化消息
    val localizedMessage = vpnError.getUserFriendlyMessage()
    assert(localizedMessage.isNotEmpty())
    
    // 5. 验证恢复建议
    val recoverySuggestion = vpnError.getRecoverySuggestion()
    assert(recoverySuggestion.isNotEmpty())
    
    println("✅ End-to-end error handling test passed")
}
```

## 8. 性能测试

### 8.1 错误处理性能测试

```kotlin
// 测试错误处理性能
fun testErrorHandlingPerformance() {
    val startTime = System.currentTimeMillis()
    
    // 创建大量错误对象
    repeat(1000) {
        val error = VPNServiceError.NetworkUnavailable(
            errorMessage = "Test error $it",
            specificErrorCode = VPNErrorCode.NETWORK_UNREACHABLE
        )
        
        // 测试本地化性能
        val message = error.getUserFriendlyMessage()
        assert(message.isNotEmpty())
        
        // 测试映射性能
        val errorMap = error.toMap()
        assert(errorMap.isNotEmpty())
    }
    
    val endTime = System.currentTimeMillis()
    val duration = endTime - startTime
    
    // 性能应该在合理范围内（1000次操作应该在1秒内完成）
    assert(duration < 1000) { "Error handling performance too slow: ${duration}ms" }
    
    println("✅ Error handling performance test passed (${duration}ms for 1000 operations)")
}
```

## 9. 运行所有测试

```kotlin
// 运行所有测试的主函数
suspend fun runAllErrorHandlingTests(context: Context) {
    println("🚀 Starting Android error handling tests...")
    
    try {
        // 基础功能测试
        testVPNErrorCode()
        testWindowsCompatibility()
        testLocalization()
        testRecoverySuggestions()
        
        // 核心组件测试
        testUnifiedErrorHandler(context)
        testErrorRecoveryStrategy()
        testTimeoutManager()
        testTimeoutErrorCreation()
        
        // 集成测试
        testVPNServiceErrorCreation()
        testFlutterMapping()
        testConnectionManagerAuthError()
        testEndToEndErrorHandling(context)
        
        // 性能测试
        testErrorHandlingPerformance()
        
        println("🎉 All error handling tests passed successfully!")
        
    } catch (e: Exception) {
        println("❌ Error handling test failed: ${e.message}")
        throw e
    }
}
```

## 10. 测试结果验证

### 预期测试结果

1. **错误代码兼容性** ✅ - 与Windows版本完全一致
2. **多语言支持** ✅ - 中英文错误消息正确显示
3. **统一错误处理** ✅ - 错误处理流程完整
4. **超时管理** ✅ - 超时配置和处理正确
5. **Flutter集成** ✅ - 错误信息正确传递到Flutter层
6. **性能表现** ✅ - 错误处理性能在可接受范围内

### 测试通过标准

- 所有单元测试通过
- 错误代码与Windows版本100%兼容
- 多语言消息正确显示
- 错误处理性能满足要求
- 无内存泄漏或异常

通过这些测试，我们可以确保Android版本的错误处理机制与Windows版本保持一致，提供统一的用户体验。
