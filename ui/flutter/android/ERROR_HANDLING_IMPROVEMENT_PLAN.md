# Android错误处理机制改进实施方案

## 概述

基于错误处理机制分析报告，本文档提供具体的实施方案，确保Android版本与Windows版本的错误处理保持一致性和规范性。

## 阶段一：统一错误代码体系（高优先级）

### 1.1 创建统一错误代码枚举

**文件位置**: `ui/flutter/android/app/src/main/kotlin/com/panabit/client/infrastructure/error/VPNErrorCode.kt`

```kotlin
/**
 * NAME: VPNErrorCode
 *
 * DESCRIPTION:
 *     统一的VPN错误代码枚举，与Windows版本保持一致
 *     使用数字代码范围：1000-6999
 */
enum class VPNErrorCode(val code: Int, val category: String) {
    // Network errors (1000-1999)
    NETWORK_UNREACHABLE(1000, "Network"),
    NETWORK_TIMEOUT(1001, "Network"),
    NETWORK_DNS_FAILURE(1002, "Network"),
    NETWORK_CONNECTION_RESET(1003, "Network"),
    NETWORK_CONNECTION_CLOSED(1004, "Network"),
    NETWORK_PROXY_ERROR(1005, "Network"),
    NETWORK_TLS_ERROR(1006, "Network"),

    // Authentication errors (2000-2999)
    AUTH_INVALID_CREDENTIALS(2000, "Authentication"),
    AUTH_EXPIRED_CREDENTIALS(2001, "Authentication"),
    AUTH_RATE_LIMITED(2002, "Authentication"),
    AUTH_ACCOUNT_LOCKED(2003, "Authentication"),
    AUTH_TOKEN_INVALID(2004, "Authentication"),
    AUTH_TOKEN_EXPIRED(2005, "Authentication"),
    AUTH_MISSING_CREDENTIALS(2006, "Authentication"),

    // Tunnel errors (3000-3999)
    TUNNEL_INIT_FAILED(3000, "Tunnel"),
    TUNNEL_CLOSED_UNEXPECTED(3001, "Tunnel"),
    TUNNEL_PACKET_DROPPED(3002, "Tunnel"),
    TUNNEL_DEVICE_ERROR(3003, "Tunnel"),
    TUNNEL_ROUTE_ERROR(3004, "Tunnel"),
    TUNNEL_DNS_ERROR(3005, "Tunnel"),
    TUNNEL_ENCRYPTION_ERROR(3006, "Tunnel"),

    // Configuration errors (4000-4999)
    CONFIG_INVALID(4000, "Configuration"),
    CONFIG_MISSING(4001, "Configuration"),
    CONFIG_PERMISSION_DENIED(4002, "Configuration"),
    CONFIG_READ_ERROR(4003, "Configuration"),
    CONFIG_WRITE_ERROR(4004, "Configuration"),
    CONFIG_PARSE_ERROR(4005, "Configuration"),
    CONFIG_VALIDATION_ERROR(4006, "Configuration"),

    // Platform errors (5000-5999)
    PLATFORM_UNSUPPORTED(5000, "Platform"),
    PLATFORM_PERMISSION_DENIED(5001, "Platform"),
    PLATFORM_DRIVER_ERROR(5002, "Platform"),
    PLATFORM_SYSTEM_ERROR(5003, "Platform"),
    PLATFORM_RESOURCE_ERROR(5004, "Platform"),
    PLATFORM_NETWORK_ERROR(5005, "Platform"),
    PLATFORM_FIREWALL_ERROR(5006, "Platform"),

    // Protocol errors (6000-6999)
    PROTOCOL_VERSION_MISMATCH(6000, "Protocol"),
    PROTOCOL_INVALID_FORMAT(6001, "Protocol"),
    PROTOCOL_UNSUPPORTED(6002, "Protocol"),
    PROTOCOL_HANDSHAKE_FAILED(6003, "Protocol"),
    PROTOCOL_ENCRYPTION_ERROR(6004, "Protocol"),
    PROTOCOL_DECRYPTION_ERROR(6005, "Protocol"),
    PROTOCOL_AUTH_ERROR(6006, "Protocol"),

    // General errors (0-999)
    UNKNOWN(0, "General"),
    INTERNAL(1, "General"),
    INVALID_ARGUMENT(2, "General"),
    NOT_FOUND(3, "General"),
    ALREADY_EXISTS(4, "General"),
    PERMISSION_DENIED(5, "General"),
    UNAUTHENTICATED(6, "General"),
    RESOURCE_EXHAUSTED(7, "General"),
    FAILED_PRECONDITION(8, "General"),
    ABORTED(9, "General"),
    OUT_OF_RANGE(10, "General"),
    NOT_IMPLEMENTED(11, "General"),
    UNAVAILABLE(12, "General");

    companion object {
        /**
         * 根据错误代码获取枚举值
         */
        fun fromCode(code: Int): VPNErrorCode {
            return values().find { it.code == code } ?: UNKNOWN
        }

        /**
         * 根据分类获取错误代码列表
         */
        fun getByCategory(category: String): List<VPNErrorCode> {
            return values().filter { it.category == category }
        }
    }
}
```

### 1.2 修改VPNServiceError基类

**文件位置**: `ui/flutter/android/app/src/main/kotlin/com/panabit/client/infrastructure/error/VPNServiceError.kt`

```kotlin
/**
 * NAME: VPNServiceError
 *
 * DESCRIPTION:
 *     统一的VPN服务错误基类，包含错误代码和本地化支持
 */
@Parcelize
sealed class VPNServiceError(
    override val message: String,
    val errorCode: VPNErrorCode,
    override val cause: Throwable? = null
) : Exception(message, cause), Parcelable {

    /**
     * NAME: ConnectionFailed
     *
     * DESCRIPTION:
     *     网络连接建立失败错误
     */
    @Parcelize
    data class ConnectionFailed(
        val errorMessage: String,
        val serverAddress: String? = null,
        val retryCount: Int = 0,
        val specificErrorCode: VPNErrorCode = VPNErrorCode.NETWORK_UNREACHABLE
    ) : VPNServiceError(errorMessage, specificErrorCode)

    /**
     * NAME: AuthenticationFailed
     *
     * DESCRIPTION:
     *     用户认证失败错误
     */
    @Parcelize
    data class AuthenticationFailed(
        val errorMessage: String,
        val serverErrorCode: String? = null,
        val isCredentialIssue: Boolean = true,
        val specificErrorCode: VPNErrorCode = VPNErrorCode.AUTH_INVALID_CREDENTIALS
    ) : VPNServiceError(errorMessage, specificErrorCode)

    /**
     * NAME: NetworkUnavailable
     *
     * DESCRIPTION:
     *     网络不可用错误
     */
    @Parcelize
    data class NetworkUnavailable(
        val errorMessage: String,
        val networkType: String? = null,
        val isTemporary: Boolean = true,
        val specificErrorCode: VPNErrorCode = VPNErrorCode.NETWORK_UNREACHABLE
    ) : VPNServiceError(errorMessage, specificErrorCode)

    /**
     * NAME: TimeoutError
     *
     * DESCRIPTION:
     *     操作超时错误
     */
    @Parcelize
    data class TimeoutError(
        val errorMessage: String,
        val operation: String,
        val timeoutDuration: Long,
        val specificErrorCode: VPNErrorCode = VPNErrorCode.NETWORK_TIMEOUT
    ) : VPNServiceError(errorMessage, specificErrorCode)

    // ... 其他错误类型保持类似结构

    /**
     * NAME: toMap
     *
     * DESCRIPTION:
     *     转换为Flutter兼容的Map格式，包含错误代码
     */
    fun toMap(): Map<String, Any> = buildMap {
        put("type", getErrorType())
        put("message", message ?: "Unknown error")
        put("error_code", errorCode.code)
        put("error_category", errorCode.category)
        put("timestamp", System.currentTimeMillis())
        
        // 添加类型特定属性
        when (this@VPNServiceError) {
            is ConnectionFailed -> {
                serverAddress?.let { put("server_address", it) }
                put("retry_count", retryCount)
            }
            is AuthenticationFailed -> {
                serverErrorCode?.let { put("server_error_code", it) }
                put("is_credential_issue", isCredentialIssue)
            }
            // ... 其他类型
        }
    }

    /**
     * NAME: getErrorType
     *
     * DESCRIPTION:
     *     获取错误类型字符串
     */
    fun getErrorType(): String = when (this) {
        is ConnectionFailed -> "CONNECTION_FAILED"
        is AuthenticationFailed -> "AUTHENTICATION_FAILED"
        is NetworkUnavailable -> "NETWORK_UNAVAILABLE"
        is TimeoutError -> "TIMEOUT_ERROR"
        // ... 其他类型
    }
}
```

## 阶段二：Android原生层国际化支持（高优先级）

### 2.1 创建错误消息本地化器

**文件位置**: `ui/flutter/android/app/src/main/kotlin/com/panabit/client/infrastructure/error/VPNErrorLocalizer.kt`

```kotlin
/**
 * NAME: VPNErrorLocalizer
 *
 * DESCRIPTION:
 *     VPN错误消息本地化器，支持中英文错误消息
 */
class VPNErrorLocalizer(private val locale: Locale = Locale.getDefault()) {

    companion object {
        // 英文错误消息模板
        private val englishMessages = mapOf(
            VPNErrorCode.NETWORK_UNREACHABLE to "Network unreachable, please check network connection",
            VPNErrorCode.NETWORK_TIMEOUT to "Network operation timed out",
            VPNErrorCode.NETWORK_DNS_FAILURE to "DNS resolution failed, please check server address",
            VPNErrorCode.AUTH_INVALID_CREDENTIALS to "Invalid user credentials",
            VPNErrorCode.AUTH_EXPIRED_CREDENTIALS to "Credentials expired, please login again",
            VPNErrorCode.AUTH_RATE_LIMITED to "Authentication requests too frequent, please try again later",
            VPNErrorCode.TUNNEL_INIT_FAILED to "Tunnel initialization failed",
            VPNErrorCode.CONFIG_INVALID to "Invalid configuration",
            VPNErrorCode.PLATFORM_PERMISSION_DENIED to "Permission denied, please run as administrator",
            VPNErrorCode.PROTOCOL_HANDSHAKE_FAILED to "Protocol handshake failed"
        )

        // 中文错误消息模板
        private val chineseMessages = mapOf(
            VPNErrorCode.NETWORK_UNREACHABLE to "网络不可达，请检查网络连接",
            VPNErrorCode.NETWORK_TIMEOUT to "网络操作超时",
            VPNErrorCode.NETWORK_DNS_FAILURE to "DNS解析失败，请检查服务器地址",
            VPNErrorCode.AUTH_INVALID_CREDENTIALS to "无效的用户凭据",
            VPNErrorCode.AUTH_EXPIRED_CREDENTIALS to "凭据已过期，请重新登录",
            VPNErrorCode.AUTH_RATE_LIMITED to "认证请求过于频繁，请稍后重试",
            VPNErrorCode.TUNNEL_INIT_FAILED to "隧道初始化失败",
            VPNErrorCode.CONFIG_INVALID to "无效的配置",
            VPNErrorCode.PLATFORM_PERMISSION_DENIED to "权限被拒绝，请以管理员身份运行",
            VPNErrorCode.PROTOCOL_HANDSHAKE_FAILED to "协议握手失败"
        )
    }

    /**
     * NAME: getLocalizedMessage
     *
     * DESCRIPTION:
     *     获取本地化的错误消息
     *
     * PARAMETERS:
     *     errorCode - 错误代码
     *
     * RETURNS:
     *     String - 本地化的错误消息
     */
    fun getLocalizedMessage(errorCode: VPNErrorCode): String {
        val messages = when (locale.language) {
            "zh" -> chineseMessages
            "en" -> englishMessages
            else -> englishMessages // 默认使用英文
        }

        return messages[errorCode] ?: getDefaultMessage(errorCode)
    }

    /**
     * NAME: getLocalizedUserFriendlyMessage
     *
     * DESCRIPTION:
     *     获取用户友好的本地化错误消息
     *
     * PARAMETERS:
     *     error - VPN服务错误
     *
     * RETURNS:
     *     String - 用户友好的本地化错误消息
     */
    fun getLocalizedUserFriendlyMessage(error: VPNServiceError): String {
        return when (error) {
            is VPNServiceError.ConnectionFailed -> {
                if (locale.language == "zh") {
                    "连接服务器失败，请检查网络连接后重试"
                } else {
                    "Failed to connect to server, please check network connection and try again"
                }
            }
            is VPNServiceError.AuthenticationFailed -> {
                if (error.isCredentialIssue) {
                    if (locale.language == "zh") {
                        "身份验证失败，请检查用户名和密码"
                    } else {
                        "Authentication failed, please check username and password"
                    }
                } else {
                    if (locale.language == "zh") {
                        "服务器认证失败，请稍后重试"
                    } else {
                        "Server authentication failed, please try again later"
                    }
                }
            }
            is VPNServiceError.NetworkUnavailable -> {
                if (error.isTemporary) {
                    if (locale.language == "zh") {
                        "网络暂时不可用，正在尝试重新连接"
                    } else {
                        "Network temporarily unavailable, attempting to reconnect"
                    }
                } else {
                    if (locale.language == "zh") {
                        "网络不可用，请检查网络设置"
                    } else {
                        "Network unavailable, please check network settings"
                    }
                }
            }
            is VPNServiceError.TimeoutError -> {
                if (locale.language == "zh") {
                    "操作超时，请稍后重试"
                } else {
                    "Operation timeout, please try again later"
                }
            }
            else -> getLocalizedMessage(error.errorCode)
        }
    }

    /**
     * NAME: getDefaultMessage
     *
     * DESCRIPTION:
     *     获取默认错误消息
     */
    private fun getDefaultMessage(errorCode: VPNErrorCode): String {
        return if (locale.language == "zh") {
            "未知错误 (代码: ${errorCode.code})"
        } else {
            "Unknown error (code: ${errorCode.code})"
        }
    }
}
```

### 2.2 修改VPNServiceError以支持本地化

```kotlin
// 在VPNServiceError类中添加本地化支持
sealed class VPNServiceError {
    // ... 现有代码

    /**
     * NAME: getUserFriendlyMessage
     *
     * DESCRIPTION:
     *     获取用户友好的本地化错误消息
     *
     * PARAMETERS:
     *     locale - 目标语言环境（可选）
     *
     * RETURNS:
     *     String - 本地化的用户友好错误消息
     */
    fun getUserFriendlyMessage(locale: Locale = Locale.getDefault()): String {
        val localizer = VPNErrorLocalizer(locale)
        return localizer.getLocalizedUserFriendlyMessage(this)
    }

    /**
     * NAME: getLocalizedMessage
     *
     * DESCRIPTION:
     *     获取本地化的技术错误消息
     *
     * PARAMETERS:
     *     locale - 目标语言环境（可选）
     *
     * RETURNS:
     *     String - 本地化的技术错误消息
     */
    fun getLocalizedMessage(locale: Locale = Locale.getDefault()): String {
        val localizer = VPNErrorLocalizer(locale)
        return localizer.getLocalizedMessage(this.errorCode)
    }
}
```

## 阶段三：统一错误处理流程（中优先级）

### 3.1 创建统一错误处理器

**文件位置**: `ui/flutter/android/app/src/main/kotlin/com/panabit/client/infrastructure/error/UnifiedErrorHandler.kt`

```kotlin
/**
 * NAME: UnifiedErrorHandler
 *
 * DESCRIPTION:
 *     统一错误处理器，提供与Windows版本一致的错误处理流程
 */
class UnifiedErrorHandler(
    private val context: Context,
    private val locale: Locale = Locale.getDefault()
) {
    private val localizer = VPNErrorLocalizer(locale)
    private val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

    /**
     * NAME: handleError
     *
     * DESCRIPTION:
     *     统一处理VPN错误
     *
     * PARAMETERS:
     *     error - VPN服务错误
     *     context - 错误上下文信息
     *
     * RETURNS:
     *     ErrorHandlingResult - 错误处理结果
     */
    fun handleError(error: VPNServiceError, context: Map<String, Any> = emptyMap()): ErrorHandlingResult {
        // 1. 记录错误日志（包含错误代码）
        logError(error, context)
        
        // 2. 生成本地化用户消息
        val userMessage = error.getUserFriendlyMessage(locale)
        
        // 3. 确定恢复策略
        val recoveryAction = getRecoveryAction(error.errorCode)
        
        // 4. 显示用户通知
        showErrorNotification(error, userMessage)
        
        // 5. 发送错误事件到Flutter层
        sendErrorEventToFlutter(error, userMessage, recoveryAction, context)
        
        return ErrorHandlingResult(
            userMessage = userMessage,
            recoveryAction = recoveryAction,
            errorCode = error.errorCode,
            canRetry = recoveryAction != RecoveryAction.FATAL
        )
    }

    /**
     * NAME: getRecoveryAction
     *
     * DESCRIPTION:
     *     根据错误代码确定恢复策略
     */
    private fun getRecoveryAction(errorCode: VPNErrorCode): RecoveryAction {
        return when (errorCode.category) {
            "Network" -> when (errorCode) {
                VPNErrorCode.NETWORK_TIMEOUT -> RecoveryAction.RETRY
                VPNErrorCode.NETWORK_UNREACHABLE -> RecoveryAction.RETRY
                VPNErrorCode.NETWORK_DNS_FAILURE -> RecoveryAction.USER_ACTION
                else -> RecoveryAction.RETRY
            }
            "Authentication" -> when (errorCode) {
                VPNErrorCode.AUTH_INVALID_CREDENTIALS -> RecoveryAction.USER_ACTION
                VPNErrorCode.AUTH_EXPIRED_CREDENTIALS -> RecoveryAction.USER_ACTION
                VPNErrorCode.AUTH_RATE_LIMITED -> RecoveryAction.RETRY
                else -> RecoveryAction.RESET
            }
            "Platform" -> RecoveryAction.USER_ACTION
            "Protocol" -> RecoveryAction.RESET
            else -> RecoveryAction.RETRY
        }
    }

    // ... 其他辅助方法
}

/**
 * NAME: ErrorHandlingResult
 *
 * DESCRIPTION:
 *     错误处理结果数据类
 */
data class ErrorHandlingResult(
    val userMessage: String,
    val recoveryAction: RecoveryAction,
    val errorCode: VPNErrorCode,
    val canRetry: Boolean
)

/**
 * NAME: RecoveryAction
 *
 * DESCRIPTION:
 *     错误恢复策略枚举
 */
enum class RecoveryAction {
    RETRY,           // 可以重试
    RESET,           // 需要重置连接
    USER_ACTION,     // 需要用户干预
    FATAL            // 致命错误，需要重启应用
}
```

## 阶段四：超时处理统一化（中优先级）

### 4.1 创建统一超时管理器

**文件位置**: `ui/flutter/android/app/src/main/kotlin/com/panabit/client/infrastructure/error/TimeoutManager.kt`

```kotlin
/**
 * NAME: TimeoutManager
 *
 * DESCRIPTION:
 *     统一超时管理器，与Windows版本保持一致的超时配置
 */
class TimeoutManager {
    companion object {
        // 超时配置（与Windows版本保持一致）
        const val CONNECTION_TIMEOUT = 25_000L        // 25秒
        const val AUTHENTICATION_TIMEOUT = 15_000L    // 15秒
        const val HEARTBEAT_TIMEOUT = 45_000L         // 45秒
        const val DNS_RESOLUTION_TIMEOUT = 10_000L    // 10秒
        const val PROTOCOL_HANDSHAKE_TIMEOUT = 20_000L // 20秒
    }

    /**
     * NAME: createTimeoutError
     *
     * DESCRIPTION:
     *     创建统一的超时错误
     *
     * PARAMETERS:
     *     operation - 超时的操作类型
     *     duration - 超时时长
     *     additionalInfo - 附加信息
     *
     * RETURNS:
     *     VPNServiceError.TimeoutError - 超时错误对象
     */
    fun createTimeoutError(
        operation: String, 
        duration: Long, 
        additionalInfo: Map<String, Any> = emptyMap()
    ): VPNServiceError.TimeoutError {
        val errorCode = when (operation.lowercase()) {
            "connection" -> VPNErrorCode.NETWORK_TIMEOUT
            "authentication", "auth" -> VPNErrorCode.AUTH_TOKEN_EXPIRED
            "heartbeat" -> VPNErrorCode.NETWORK_TIMEOUT
            "dns", "dns_resolution" -> VPNErrorCode.NETWORK_DNS_FAILURE
            "protocol_handshake", "handshake" -> VPNErrorCode.PROTOCOL_HANDSHAKE_FAILED
            else -> VPNErrorCode.NETWORK_TIMEOUT
        }

        val errorMessage = "Operation '$operation' timed out after ${duration}ms"
        
        return VPNServiceError.TimeoutError(
            errorMessage = errorMessage,
            operation = operation,
            timeoutDuration = duration,
            specificErrorCode = errorCode
        )
    }

    /**
     * NAME: getTimeoutForOperation
     *
     * DESCRIPTION:
     *     获取指定操作的超时时长
     */
    fun getTimeoutForOperation(operation: String): Long {
        return when (operation.lowercase()) {
            "connection" -> CONNECTION_TIMEOUT
            "authentication", "auth" -> AUTHENTICATION_TIMEOUT
            "heartbeat" -> HEARTBEAT_TIMEOUT
            "dns", "dns_resolution" -> DNS_RESOLUTION_TIMEOUT
            "protocol_handshake", "handshake" -> PROTOCOL_HANDSHAKE_TIMEOUT
            else -> CONNECTION_TIMEOUT
        }
    }
}
```

## 实施时间表

### 第1周
- ✅ 创建VPNErrorCode枚举
- ✅ 修改VPNServiceError基类
- ✅ 创建VPNErrorLocalizer类

### 第2周
- 🔄 创建UnifiedErrorHandler
- 🔄 修改ConnectionManager使用新的错误处理
- 🔄 创建TimeoutManager

### 第3-4周
- ⏳ 集成到现有代码中
- ⏳ 全面测试多语言支持
- ⏳ 性能优化和错误处理

### 第5-6周
- ⏳ 文档更新
- ⏳ 代码审查和优化
- ⏳ 最终测试和部署

## 测试计划

### 单元测试
1. VPNErrorCode枚举测试
2. VPNErrorLocalizer本地化测试
3. UnifiedErrorHandler错误处理测试
4. TimeoutManager超时管理测试

### 集成测试
1. 错误代码与Windows版本兼容性测试
2. 多语言环境下的错误消息测试
3. 错误恢复策略测试
4. 超时处理一致性测试

### 用户体验测试
1. 错误消息用户友好性测试
2. 语言切换实时性测试
3. 错误通知显示测试
4. 错误恢复流程测试

## 风险缓解

### 兼容性风险
- 保持现有API向后兼容
- 渐进式迁移现有错误处理代码
- 提供兼容性适配器

### 性能风险
- 错误处理逻辑优化
- 本地化消息缓存
- 异步错误处理

### 测试风险
- 自动化测试覆盖
- 多语言环境测试
- 错误场景模拟测试

通过以上实施方案，Android版本的错误处理机制将与Windows版本保持高度一致，提供统一的用户体验和开发体验。
