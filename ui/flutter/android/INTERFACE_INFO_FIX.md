# Android 接口信息上报修复

## 问题描述

在Android VPN连接过程中，发现物理接口名称从连接前的 `rmnet_data2` 变成连接后的 `tun0`，导致统计界面显示的接口和本地IP不正确。

### 问题根因

1. **ConnectivityManager.activeNetwork 在VPN连接后返回VPN接口**
   - VPN连接前：`activeNetwork` 返回物理网络（如 `rmnet_data2`）
   - VPN连接后：`activeNetwork` 返回VPN网络（如 `tun0`）

2. **接口信息查询时机不当**
   - 在VPN连接成功后查询接口信息，此时系统已经将VPN接口设为活动网络
   - `getPhysicalInterfaceViaConnectivityManager` 方法没有过滤VPN接口

## 解决方案

### 1. 预先记录物理接口信息

在VPN连接开始前记录真实的物理接口信息：

```kotlin
// ConnectionManager.kt - connect方法中
// Record physical interface information BEFORE VPN connection
val (physicalInterface, physicalIP) = NetworkUtils.getPhysicalInterfaceInfo(context)
connectionPhysicalInterface = physicalInterface
connectionPhysicalIP = physicalIP
```

### 2. 增强ConnectivityManager检测逻辑

在 `getPhysicalInterfaceViaConnectivityManager` 方法中添加VPN接口过滤：

```kotlin
// 检查是否为VPN网络
if (capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_VPN) == true) {
    // 尝试获取底层物理网络
    val allNetworks = connectivityManager.allNetworks
    for (network in allNetworks) {
        val networkCaps = connectivityManager.getNetworkCapabilities(network)
        if (networkCaps != null && !networkCaps.hasTransport(NetworkCapabilities.TRANSPORT_VPN)) {
            // 找到非VPN网络，获取其接口信息
        }
    }
}
```

### 3. 智能接口信息获取

在 `VPNServiceAdapter.getInterfaceInfo()` 中实现智能选择：

```kotlin
val (physicalInterfaceName, physicalLocalIp) = if (currentState is VPNState.Connected) {
    // VPN连接时使用预先记录的物理接口信息
    val recordedInfo = injectedConnectionManager.getRecordedPhysicalInterfaceInfo()
    if (recordedInfo.first != "unknown") {
        recordedInfo
    } else {
        NetworkUtils.getPhysicalInterfaceInfo(context)
    }
} else {
    // VPN未连接时使用当前检测
    NetworkUtils.getPhysicalInterfaceInfo(context)
}
```

## 修改文件

### 1. ConnectionManager.kt
- 添加 `connectionPhysicalInterface` 和 `connectionPhysicalIP` 属性
- 在 `connect()` 方法中预先记录接口信息
- 在 `disconnect()` 等清理方法中清理记录的信息
- 添加 `getRecordedPhysicalInterfaceInfo()` 公共方法

### 2. NetworkUtils.kt
- 增强 `getPhysicalInterfaceViaConnectivityManager()` 方法
- 添加VPN接口检测和过滤逻辑
- 添加 `isVPNInterface()` 和 `extractInterfaceInfo()` 辅助方法

### 3. VPNServiceAdapter.kt
- 修改 `getInterfaceInfo()` 方法使用预先记录的接口信息
- 根据VPN状态智能选择接口信息来源

## 预期效果

1. **连接前后接口信息一致**
   - 连接前：`rmnet_data2` / `*************`
   - 连接后：`rmnet_data2` / `*************` (使用预先记录的信息)

2. **统计界面显示正确**
   - 接口名称：显示真实物理接口（如 `rmnet_data2`）
   - 本地IP：显示物理接口IP（如 `*************`）
   - 隧道IP：显示VPN分配的IP（如 `*************`）

3. **跨平台一致性**
   - 与iOS实现保持一致的逻辑
   - 在连接建立时记录物理接口信息

## 测试验证

1. **连接前检查**：确认获取到正确的物理接口信息
2. **连接过程中**：验证接口信息记录正确
3. **连接后查询**：确认返回预先记录的物理接口信息
4. **断开连接**：验证记录的信息被正确清理

## 注意事项

1. **线程安全**：使用 `@Volatile` 标记接口信息属性
2. **资源清理**：在所有断开连接的路径中清理记录的信息
3. **兼容性**：保持与现有代码的兼容性
4. **错误处理**：当记录的信息不可用时回退到当前检测
