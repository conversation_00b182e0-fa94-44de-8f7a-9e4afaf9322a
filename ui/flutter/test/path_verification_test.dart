// /**
//  * AUTHOR: wei
//  * HISTORY: 27/06/2025 - Path verification test for Windows storage fix
//  */

import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'package:itforce/services/platform/file_storage_service.dart';
import 'package:itforce/services/platform/file_log_service.dart';

/// MockPathProviderPlatform
class MockPathProviderPlatform extends Fake
    with MockPlatformInterfaceMixin
    implements PathProviderPlatform {
  
  @override
  Future<String?> getApplicationSupportPath() async {
    return 'C:\\Users\\<USER>\\AppData\\Roaming';
  }
}

void main() {
  group('Path Verification Tests', () {
    late MockPathProviderPlatform mockPathProvider;

    setUpAll(() {
      mockPathProvider = MockPathProviderPlatform();
      PathProviderPlatform.instance = mockPathProvider;
    });

    test('FileStorageService should use correct AppData paths on Windows', () async {
      if (!Platform.isWindows) {
        return; // 只在Windows上测试
      }

      final storageService = FileStorageService();
      await storageService.initialize();

      final paths = await storageService.getStoragePaths();
      
      // 验证路径包含正确的AppData结构
      expect(paths['logs'], contains('AppData\\Roaming\\Panabit'));
      expect(paths['config'], contains('AppData\\Roaming\\Panabit'));
      expect(paths['userdata'], contains('AppData\\Roaming\\Panabit'));
      expect(paths['secure'], contains('AppData\\Roaming\\Panabit'));
      expect(paths['shared'], contains('AppData\\Roaming\\Panabit'));

      print('✅ Storage paths verification:');
      paths.forEach((key, value) {
        print('  $key: $value');
      });

      await storageService.dispose();
    });

    test('FileLogService should use correct AppData paths on Windows', () async {
      if (!Platform.isWindows) {
        return; // 只在Windows上测试
      }

      final logService = FileLogService();
      
      try {
        await logService.initialize();
        
        final logFiles = await logService.getLogFiles();
        if (logFiles.isNotEmpty) {
          final logPath = logFiles.first;
          // 验证日志路径包含正确的AppData结构
          expect(logPath, contains('AppData\\Roaming\\ItForce\\logs'));
          print('✅ Log path verification: $logPath');
        }
      } catch (e) {
        print('⚠️  Log service initialization: $e');
        // 在测试环境中可能会失败，这是正常的
      }

      await logService.dispose();
    });

    test('should print expected paths for manual verification', () async {
      if (!Platform.isWindows) {
        return;
      }

      print('\n📁 Expected Windows paths after fix:');
      print('  Logs: C:\\Users\\<USER>\\AppData\\Roaming\\Panabit\\logs');
      print('  Storage: C:\\Users\\<USER>\\AppData\\Roaming\\Panabit\\');
      print('  SharedPrefs: C:\\Users\\<USER>\\AppData\\Roaming\\Panabit\\shared_preferences.json');
      print('  SecureStorage: C:\\Users\\<USER>\\AppData\\Roaming\\Panabit\\secure_storage\\');
      print('  SharedData: C:\\Users\\<USER>\\AppData\\Roaming\\Panabit\\shared_data\\');
    });
  });
}
