// Copyright (c) 2024 Panabit Inc. All rights reserved.

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import '../lib/utils/api_exception.dart';
import '../lib/services/lookup_service.dart';
import '../lib/generated/l10n/app_localizations.dart';

void main() {
  group('Domain Error Handling Tests', () {
    late Widget testApp;
    late BuildContext testContext;

    setUp(() {
      testApp = MaterialApp(
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('en'),
          Locale('zh'),
        ],
        home: Builder(
          builder: (context) {
            testContext = context;
            return const Scaffold(body: Text('Test'));
          },
        ),
      );
    });

    testWidgets('ApiException domain error codes - English', (WidgetTester tester) async {
      await tester.pumpWidget(testApp);
      await tester.pumpAndSettle();

      // Test domain not found error (1101)
      final domainNotFoundError = ApiException(
        'Lookup failed: domain not found: nonexistent.domain',
        1101,
        'domain_not_found'
      );
      final domainNotFoundMessage = domainNotFoundError.getUserFriendlyMessage(testContext);
      expect(domainNotFoundMessage, contains('Domain not found'));

      // Test domain invalid error (1102)
      final domainInvalidError = ApiException(
        'Invalid domain: Domain parameter is required',
        1102,
        'domain_invalid'
      );
      final domainInvalidMessage = domainInvalidError.getUserFriendlyMessage(testContext);
      expect(domainInvalidMessage, contains('Invalid domain format'));

      // Test domain required error (1103)
      final domainRequiredError = ApiException(
        'Domain parameter is required',
        1103,
        'domain_required'
      );
      final domainRequiredMessage = domainRequiredError.getUserFriendlyMessage(testContext);
      expect(domainRequiredMessage, contains('Domain parameter is required'));

      // Test domain lookup timeout error (1104)
      final domainTimeoutError = ApiException(
        'Domain lookup timeout',
        1104,
        'domain_lookup_timeout'
      );
      final domainTimeoutMessage = domainTimeoutError.getUserFriendlyMessage(testContext);
      expect(domainTimeoutMessage, contains('Domain lookup timeout'));

      // Test domain lookup network error (1105)
      final domainNetworkError = ApiException(
        'Network error during domain lookup',
        1105,
        'domain_lookup_network_error'
      );
      final domainNetworkMessage = domainNetworkError.getUserFriendlyMessage(testContext);
      expect(domainNetworkMessage, contains('Network error during domain lookup'));
    });

    testWidgets('ApiException domain error codes - Chinese', (WidgetTester tester) async {
      // Set Chinese locale
      final chineseApp = MaterialApp(
        locale: const Locale('zh'),
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('en'),
          Locale('zh'),
        ],
        home: Builder(
          builder: (context) {
            testContext = context;
            return const Scaffold(body: Text('Test'));
          },
        ),
      );

      await tester.pumpWidget(chineseApp);
      await tester.pumpAndSettle();

      // Test domain not found error (1101) in Chinese
      final domainNotFoundError = ApiException(
        'Lookup failed: domain not found: nonexistent.domain',
        1101,
        'domain_not_found'
      );
      final domainNotFoundMessage = domainNotFoundError.getUserFriendlyMessage(testContext);
      expect(domainNotFoundMessage, contains('域名不存在'));

      // Test domain invalid error (1102) in Chinese
      final domainInvalidError = ApiException(
        'Invalid domain: Domain parameter is required',
        1102,
        'domain_invalid'
      );
      final domainInvalidMessage = domainInvalidError.getUserFriendlyMessage(testContext);
      expect(domainInvalidMessage, contains('域名格式无效'));

      // Test domain required error (1103) in Chinese
      final domainRequiredError = ApiException(
        'Domain parameter is required',
        1103,
        'domain_required'
      );
      final domainRequiredMessage = domainRequiredError.getUserFriendlyMessage(testContext);
      expect(domainRequiredMessage, contains('域名参数不能为空'));

      // Test domain lookup timeout error (1104) in Chinese
      final domainTimeoutError = ApiException(
        'Domain lookup timeout',
        1104,
        'domain_lookup_timeout'
      );
      final domainTimeoutMessage = domainTimeoutError.getUserFriendlyMessage(testContext);
      expect(domainTimeoutMessage, contains('域名查找超时'));

      // Test domain lookup network error (1105) in Chinese
      final domainNetworkError = ApiException(
        'Network error during domain lookup',
        1105,
        'domain_lookup_network_error'
      );
      final domainNetworkMessage = domainNetworkError.getUserFriendlyMessage(testContext);
      expect(domainNetworkMessage, contains('域名查找网络错误'));
    });

    test('LookupError parsing', () {
      // Test LookupError.fromJson
      final errorJson = {
        'code': 1001,
        'message': 'Lookup failed',
        'detail': 'domain not found: nonexistent',
        'field': 'domain'
      };

      final lookupError = LookupError.fromJson(errorJson);
      expect(lookupError.code, equals(1001));
      expect(lookupError.message, equals('Lookup failed'));
      expect(lookupError.detail, equals('domain not found: nonexistent'));
      expect(lookupError.field, equals('domain'));
    });

    test('LookupResponse error parsing', () {
      // Test error response parsing
      final errorResponseJson = {
        'success': false,
        'error': {
          'code': 1001,
          'message': 'Lookup failed',
          'detail': 'domain not found: nonexistent',
          'field': 'domain'
        }
      };

      final lookupResponse = LookupResponse.fromJson(errorResponseJson);
      expect(lookupResponse.success, isFalse);
      expect(lookupResponse.error, isNotNull);
      expect(lookupResponse.error!.code, equals(1001));
      expect(lookupResponse.error!.message, equals('Lookup failed'));
      expect(lookupResponse.error!.detail, equals('domain not found: nonexistent'));
      expect(lookupResponse.error!.field, equals('domain'));
    });

    test('LookupResponse success parsing', () {
      // Test success response parsing
      final successResponseJson = {
        'success': true,
        'data': {
          'type': 'serverlist',
          'address': 'https://example.com',
          'domain': 'test.domain',
          'timestamp': '2024-01-01T00:00:00Z',
          'latency': 100
        }
      };

      final lookupResponse = LookupResponse.fromJson(successResponseJson);
      expect(lookupResponse.success, isTrue);
      expect(lookupResponse.error, isNull);
      expect(lookupResponse.type, equals('serverlist'));
      expect(lookupResponse.address, equals('https://example.com'));
      expect(lookupResponse.domain, equals('test.domain'));
      expect(lookupResponse.latency, equals(100));
    });
  });
}
