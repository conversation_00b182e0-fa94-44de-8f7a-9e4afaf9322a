/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      ping_server_test.dart
///
/// DESCRIPTION :    Tests for Platform Channel pingServer method to verify
///                  the fix for MissingPluginException error
///
/// AUTHOR :         wei
///
/// HISTORY :        30/06/2025 create pingServer method test

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';

void main() {
  group('Platform Channel pingServer Tests', () {

    const MethodChannel channel = MethodChannel('itforce_vpn/methods');

    setUpAll(() {
      // Initialize Flutter binding for tests
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      // Mock the platform channel to simulate Swift backend responses
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        
        switch (methodCall.method) {
          case 'pingServer':
            // Simulate successful ping response
            final arguments = methodCall.arguments;
            final Map<String, dynamic> args;
            if (arguments is Map) {
              args = Map<String, dynamic>.from(arguments);
            } else {
              args = <String, dynamic>{};
            }
            final serverId = args['server_id'] as String?;
            
            if (serverId == null) {
              throw PlatformException(
                code: 'INVALID_ARGUMENTS',
                message: 'Missing server_id',
              );
            }
            
            // Return mock ping values based on server ID
            switch (serverId) {
              case '1':
                return 30; // Good ping
              case '2':
                return 100; // Average ping
              case '3':
                return -1; // Unreachable
              default:
                return -1; // Unknown server
            }
            
          case 'pingServers':
            // Simulate successful ping all servers response
            return null;
            
          default:
            throw MissingPluginException('No implementation found for method ${methodCall.method}');
        }
      });
    });
    
    tearDown(() {
      // Clean up the mock
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, null);
    });
    
    test('pingServer should return valid ping value for existing server', () async {
      final result = await channel.invokeMethod('pingServer', {
        'server_id': '1',
      });
      
      expect(result, equals(30));
    });
    
    test('pingServer should return -1 for unreachable server', () async {
      final result = await channel.invokeMethod('pingServer', {
        'server_id': '3',
      });
      
      expect(result, equals(-1));
    });
    
    test('pingServer should throw error for missing server_id', () async {
      expect(
        () async => await channel.invokeMethod('pingServer', {}),
        throwsA(isA<PlatformException>()),
      );
    });
    
    test('pingServers should complete without error', () async {
      expect(
        () async => await channel.invokeMethod('pingServers'),
        returnsNormally,
      );
    });
    
    test('pingServer with different server IDs should return appropriate values', () async {
      // Test multiple servers
      final results = <String, int>{};
      
      for (final serverId in ['1', '2', '3']) {
        final result = await channel.invokeMethod('pingServer', {
          'server_id': serverId,
        });
        results[serverId] = result as int;
      }
      
      expect(results['1'], equals(30));   // Good ping
      expect(results['2'], equals(100));  // Average ping
      expect(results['3'], equals(-1));   // Unreachable
    });
  });
}
