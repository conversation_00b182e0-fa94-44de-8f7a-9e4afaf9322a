// /**
//  * AUTHOR: wei
//  * HISTORY: 27/06/2025 - Initial implementation of storage migration tests
//  */

import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'package:itforce/services/platform/storage_migration_service.dart';

/// MockPathProviderPlatform
///
/// PURPOSE:
///     Mock implementation of PathProviderPlatform for testing
class MockPathProviderPlatform extends Fake
    with MockPlatformInterfaceMixin
    implements PathProviderPlatform {
  
  @override
  Future<String?> getApplicationSupportPath() async {
    return 'C:\\Users\\<USER>\\AppData\\Roaming';
  }
}

void main() {
  group('StorageMigrationService Tests', () {
    late MockPathProviderPlatform mockPathProvider;

    setUpAll(() {
      mockPathProvider = MockPathProviderPlatform();
      PathProviderPlatform.instance = mockPathProvider;
    });

    test('should return correct old log directory path', () async {
      // 这个测试只在Windows平台上运行
      if (!Platform.isWindows) {
        return;
      }

      final oldLogDir = await StorageMigrationService.getOldLogDirectory();
      expect(oldLogDir, isNotNull);
    });

    test('should ensure directory exists', () async {
      final testDir = 'test_directory_${DateTime.now().millisecondsSinceEpoch}';
      
      // 确保目录不存在
      final dir = Directory(testDir);
      if (await dir.exists()) {
        await dir.delete(recursive: true);
      }

      // 测试创建目录
      final result = await StorageMigrationService.ensureDirectoryExists(testDir);
      expect(result, isTrue);
      expect(await dir.exists(), isTrue);

      // 清理
      if (await dir.exists()) {
        await dir.delete(recursive: true);
      }
    });

    test('should handle migration gracefully when no old data exists', () async {
      // 这个测试验证当没有旧数据时，迁移不会失败
      final result = await StorageMigrationService.migrateStorageIfNeeded();
      expect(result, isTrue);
    });
  });
}
