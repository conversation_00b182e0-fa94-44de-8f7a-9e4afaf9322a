/// AUTHOR: wei
/// HISTORY: 27/06/2025 - Platform adaptation test for backend startup mechanism

import 'package:flutter_test/flutter_test.dart';
import 'package:itforce/services/platform/platform_service_factory.dart';
import 'package:itforce/services/platform/cross_platform_backend_service.dart';

void main() {
  group('Platform Adaptation Tests', () {
    
    test('Platform detection should work correctly', () {
      // Test platform detection capabilities
      final capabilities = PlatformServiceFactory.getPlatformCapabilities();
      
      expect(capabilities, isA<Map<String, dynamic>>());
      expect(capabilities.containsKey('platform'), isTrue);
      expect(capabilities.containsKey('isApplePlatform'), isTrue);
      expect(capabilities.containsKey('isDesktopPlatform'), isTrue);
      expect(capabilities.containsKey('supportsBackgroundExecution'), isTrue);
      expect(capabilities.containsKey('requiresVPNPermission'), isTrue);
    });
    
    test('Backend service factory should create appropriate service', () {
      // This test will create the appropriate backend service based on platform
      expect(() => PlatformServiceFactory.createBackendService(), 
             returnsNormally);
      
      final backendService = PlatformServiceFactory.createBackendService();
      expect(backendService, isA<CrossPlatformBackendService>());
      
      // Verify platform info
      final platformInfo = backendService.getPlatformInfo();
      expect(platformInfo, isA<Map<String, dynamic>>());
      expect(platformInfo.containsKey('platform'), isTrue);
      expect(platformInfo.containsKey('type'), isTrue);
    });
    
    test('API service factory should create appropriate service', () {
      expect(() => PlatformServiceFactory.createApiService(), 
             returnsNormally);
      
      final apiService = PlatformServiceFactory.createApiService();
      expect(apiService, isNotNull);
    });
    
    test('Storage service factory should create appropriate service', () {
      expect(() => PlatformServiceFactory.createStorageService(), 
             returnsNormally);
      
      final storageService = PlatformServiceFactory.createStorageService();
      expect(storageService, isNotNull);
    });
    
    test('Log service factory should create appropriate service', () {
      expect(() => PlatformServiceFactory.createLogService(), 
             returnsNormally);
      
      final logService = PlatformServiceFactory.createLogService();
      expect(logService, isNotNull);
    });
    
    test('Service factory should maintain singleton instances', () {
      final apiService1 = PlatformServiceFactory.createApiService();
      final apiService2 = PlatformServiceFactory.createApiService();
      
      expect(identical(apiService1, apiService2), isTrue);
      
      final backendService1 = PlatformServiceFactory.createBackendService();
      final backendService2 = PlatformServiceFactory.createBackendService();
      
      expect(identical(backendService1, backendService2), isTrue);
    });
    
    test('Reset instances should clear singleton cache', () {
      final apiService1 = PlatformServiceFactory.createApiService();
      
      PlatformServiceFactory.resetInstances();
      
      final apiService2 = PlatformServiceFactory.createApiService();
      
      expect(identical(apiService1, apiService2), isFalse);
    });
  });
  
  group('Platform Channel Backend Service Tests', () {
    
    test('Platform info should be correct for Apple platforms', () {
      // Note: This test assumes we're running on Apple platform
      // In real testing, you'd mock the platform detection
      
      final backendService = PlatformServiceFactory.createBackendService();
      final platformInfo = backendService.getPlatformInfo();
      
      expect(platformInfo['communication'], isNotNull);
      expect(platformInfo['backend_type'], isNotNull);
    });
  });
}
