import 'dart:convert';

// 手动测试lookup响应解析逻辑
void main() {
  print('=== 测试lookup响应解析逻辑 ===');

  // 测试用例1：模糊匹配 - 用户输入"CLIENT2"，lookup返回完整信息
  final testResponse1 = '''
{
  "success": true,
  "data": {
    "type": "serverlist",
    "address": "https://test.example.com",
    "domain": "test.CLIENT2.testforce",
    "serverlistaddress": "https://test.example.com/serverlist/CLIENT2/test/serverlist.json",
    "timestamp": "2025-07-29T16:40:13.971322+08:00",
    "latency": 39
  },
  "timestamp": "2025-07-29T16:40:13.971383+08:00"
}
''';

  testLookupResponse('CLIENT2', testResponse1);

  // 测试用例2：精确匹配
  final testResponse2 = '''
{
  "success": true,
  "data": {
    "type": "serverlist",
    "address": "https://api.example.com",
    "domain": "research.CNG.itforce",
    "serverlistaddress": "https://api.example.com/serverlist/CNG/research/serverlist.json",
    "timestamp": "2025-07-29T16:40:13.971322+08:00",
    "latency": 25
  },
  "timestamp": "2025-07-29T16:40:13.971383+08:00"
}
''';

  testLookupResponse('research.CNG.itforce', testResponse2);
}

void testLookupResponse(String userInput, String jsonResponse) {
  print('\n--- 测试用例 ---');
  print('用户输入: $userInput');

  try {
    // 解析JSON响应
    final responseData = json.decode(jsonResponse) as Map<String, dynamic>;
    final lookupResponse = LookupResponse.fromJson(responseData);

    print('解析结果:');
    print('  success: ${lookupResponse.success}');
    print('  type: ${lookupResponse.type}');
    print('  address: ${lookupResponse.address}');
    print('  domain: ${lookupResponse.domain}');
    print('  serverlistaddress: ${lookupResponse.serverlistaddress}');
    print('  timestamp: ${lookupResponse.timestamp}');
    print('  latency: ${lookupResponse.latency}');

    // 判断是否为模糊匹配
    final isFuzzyMatch = userInput != lookupResponse.domain;
    print('  isFuzzyMatch: $isFuzzyMatch');

    if (isFuzzyMatch) {
      print('✅ 需要替换域名: $userInput → ${lookupResponse.domain}');
    } else {
      print('✅ 精确匹配，无需替换域名');
    }

    print('✅ 最终serverlist URL: ${lookupResponse.serverlistaddress}');

  } catch (e) {
    print('❌ 解析错误: $e');
  }
}

// 模拟LookupResponse类（简化版本）
class LookupResponse {
  final bool success;
  final String type;
  final String address;
  final String domain;
  final String serverlistaddress;
  final String timestamp;
  final int latency;

  LookupResponse({
    required this.success,
    required this.type,
    required this.address,
    required this.domain,
    required this.serverlistaddress,
    required this.timestamp,
    required this.latency,
  });

  factory LookupResponse.fromJson(Map<String, dynamic> json) {
    final success = json['success'] as bool? ?? false;

    if (success) {
      final data = json['data'] as Map<String, dynamic>?;
      if (data == null) {
        throw Exception('Invalid lookup response: missing data field');
      }

      return LookupResponse(
        success: success,
        type: data['type'] as String? ?? '',
        address: data['address'] as String? ?? '',
        domain: data['domain'] as String? ?? '',
        serverlistaddress: data['serverlistaddress'] as String? ?? '',
        timestamp: data['timestamp'] as String? ?? '',
        latency: data['latency'] as int? ?? 0,
      );
    } else {
      throw Exception('Error response not handled in this test');
    }
  }
}

String buildServerListUrl(String baseAddress, String completeDomain) {
  // 解析lookup返回的完整域名
  final domainParts = completeDomain.split('.');

  // 验证域名格式：至少包含两个部分（次顶级域名.顶级域名）
  if (domainParts.length < 2) {
    throw Exception('Invalid complete domain format: $completeDomain');
  }

  // 移除基础地址末尾的斜杠
  final cleanBaseAddress = baseAddress.endsWith('/')
      ? baseAddress.substring(0, baseAddress.length - 1)
      : baseAddress;

  // 去掉顶级域名部分，保留次顶级域名及以下的部分
  // 例如：research.CNG.itforce → [research, CNG]
  final pathParts = domainParts.sublist(0, domainParts.length - 1);

  // 反转路径部分，按照次顶级域名的顺序构建URL
  // 例如：[research, CNG] → [CNG, research]
  final reversedParts = pathParts.reversed.toList();

  // 构建服务器列表URL
  // 格式：{address}/serverlist/{次顶级域名}/{次次顶级域名}/.../{最深层域名}/serverlist.json
  final pathString = reversedParts.join('/');
  final serverListUrl = '$cleanBaseAddress/serverlist/$pathString/serverlist.json';

  print('域名部分: $domainParts');
  print('路径部分: $pathParts');
  print('反转后: $reversedParts');
  print('路径字符串: $pathString');

  return serverListUrl;
}
