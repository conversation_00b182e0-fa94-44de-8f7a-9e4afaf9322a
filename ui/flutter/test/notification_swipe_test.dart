// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      notification_swipe_test.dart
//  *
//  * DESCRIPTION :    测试通知组件的上滑手势关闭功能
//  *
//  * AUTHOR :         wei
//  *
//  *******************************************************************************

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:itforce/widgets/notification_manager.dart';
import 'package:itforce/services/notification_service.dart';

void main() {
  group('NotificationWidget Swipe Gesture Tests', () {
    testWidgets('should dismiss notification on upward swipe', (WidgetTester tester) async {
      bool dismissed = false;
      
      // Create a test notification
      final notification = NotificationInfo(
        type: NotificationType.info,
        message: 'Test notification message',
      );

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: NotificationWidget(
              notification: notification,
              onDismiss: () {
                dismissed = true;
              },
            ),
          ),
        ),
      );

      // Verify the notification is displayed
      expect(find.text('Test notification message'), findsOneWidget);
      expect(dismissed, false);

      // Perform upward swipe gesture
      await tester.drag(
        find.byType(NotificationWidget),
        const Offset(0, -100), // Swipe up by 100 pixels
      );
      
      // Wait for animation to complete
      await tester.pumpAndSettle();

      // Verify the notification was dismissed
      expect(dismissed, true);
    });

    testWidgets('should dismiss notification on fast upward swipe velocity', (WidgetTester tester) async {
      bool dismissed = false;
      
      // Create a test notification
      final notification = NotificationInfo(
        type: NotificationType.success,
        message: 'Success notification',
      );

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: NotificationWidget(
              notification: notification,
              onDismiss: () {
                dismissed = true;
              },
            ),
          ),
        ),
      );

      // Verify the notification is displayed
      expect(find.text('Success notification'), findsOneWidget);
      expect(dismissed, false);

      // Perform fast upward fling gesture
      await tester.fling(
        find.byType(NotificationWidget),
        const Offset(0, -600), // Fast upward velocity
        1000, // High velocity
      );
      
      // Wait for animation to complete
      await tester.pumpAndSettle();

      // Verify the notification was dismissed
      expect(dismissed, true);
    });

    testWidgets('should not dismiss notification on downward swipe', (WidgetTester tester) async {
      bool dismissed = false;
      
      // Create a test notification
      final notification = NotificationInfo(
        type: NotificationType.warning,
        message: 'Warning notification',
      );

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: NotificationWidget(
              notification: notification,
              onDismiss: () {
                dismissed = true;
              },
            ),
          ),
        ),
      );

      // Verify the notification is displayed
      expect(find.text('Warning notification'), findsOneWidget);
      expect(dismissed, false);

      // Perform downward swipe gesture
      await tester.drag(
        find.byType(NotificationWidget),
        const Offset(0, 100), // Swipe down by 100 pixels
      );
      
      // Wait for any potential animation
      await tester.pumpAndSettle();

      // Verify the notification was NOT dismissed
      expect(dismissed, false);
      expect(find.text('Warning notification'), findsOneWidget);
    });

    testWidgets('should dismiss notification on close button tap', (WidgetTester tester) async {
      bool dismissed = false;
      
      // Create a test notification
      final notification = NotificationInfo(
        type: NotificationType.error,
        message: 'Error notification',
      );

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: NotificationWidget(
              notification: notification,
              onDismiss: () {
                dismissed = true;
              },
            ),
          ),
        ),
      );

      // Verify the notification is displayed
      expect(find.text('Error notification'), findsOneWidget);
      expect(dismissed, false);

      // Tap the close button
      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();

      // Verify the notification was dismissed
      expect(dismissed, true);
    });
  });
}
