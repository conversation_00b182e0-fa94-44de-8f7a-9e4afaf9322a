/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      platform_channel_type_conversion_test.dart
///
/// DESCRIPTION :    Tests for Platform Channel type conversion fixes to ensure
///                  Swift [String: Any] data is properly converted to Dart
///                  Map<String, dynamic> for Flutter model compatibility
///
/// AUTHOR :         wei
///
/// HISTORY :        30/06/2025 create Platform Channel type conversion tests

import 'package:flutter_test/flutter_test.dart';
import 'package:itforce/models/server.dart';
import 'package:itforce/models/interface_info.dart';
import 'package:itforce/models/routing_settings.dart';

void main() {
  group('Platform Channel Type Conversion Tests', () {
    
    test('Server.fromJson should handle Map<String, dynamic> conversion', () {
      // Simulate data coming from Swift Platform Channel
      // This would be [String: Any] in Swift, converted to Map in Dart
      final rawData = <String, Object?>{
        'id': '1',
        'name': '测试服务器',
        'name_en': 'Test Server',
        'server_name': 'test.example.com',
        'server_port': 8080,
        'ping': 50,
        'isauto': true,
        'status': 'online',
        'isdefault': false,
      };
      
      // Convert using Map<String, dynamic>.from() as done in the fix
      final convertedData = Map<String, dynamic>.from(rawData);
      
      // This should not throw an exception
      expect(() => Server.fromJson(convertedData), returnsNormally);
      
      final server = Server.fromJson(convertedData);
      expect(server.id, equals('1'));
      expect(server.name, equals('测试服务器'));
      expect(server.nameEn, equals('Test Server'));
      expect(server.serverName, equals('test.example.com'));
      expect(server.serverPort, equals(8080));
      expect(server.ping, equals(50));
      expect(server.isAuto, equals(true));
      expect(server.status, equals('online'));
      expect(server.isDefault, equals(false));
    });
    
    test('InterfaceInfo.fromJson should handle Map<String, dynamic> conversion', () {
      // Simulate data coming from Swift Platform Channel
      final rawData = <String, Object?>{
        'interface_name': 'en0',
        'local_ip': '*************',
        'tun_ip': '********',
        'timestamp': 1640995200, // Unix timestamp
      };
      
      // Convert using Map<String, dynamic>.from() as done in the fix
      final convertedData = Map<String, dynamic>.from(rawData);
      
      // This should not throw an exception
      expect(() => InterfaceInfo.fromJson(convertedData), returnsNormally);
      
      final interfaceInfo = InterfaceInfo.fromJson(convertedData);
      expect(interfaceInfo.interfaceName, equals('en0'));
      expect(interfaceInfo.localIp, equals('*************'));
      expect(interfaceInfo.tunIp, equals('********'));
    });
    
    test('RoutingSettingsModel.fromJson should handle Map<String, dynamic> conversion', () {
      // Simulate data coming from Swift Platform Channel
      final rawData = <String, Object?>{
        'mode': 'custom',
        'custom_routes': '***********/24,10.0.0.0/8',
        'auto_start': true,
      };
      
      // Convert using Map<String, dynamic>.from() as done in the fix
      final convertedData = Map<String, dynamic>.from(rawData);
      
      // This should not throw an exception
      final settings = RoutingSettingsModel();
      expect(() => settings.fromJson(convertedData), returnsNormally);
      
      expect(settings.mode, equals(RoutingMode.custom));
      expect(settings.customRoutes, equals('***********/24,10.0.0.0/8'));
      expect(settings.autoStart, equals(true));
    });
    
    test('Server list conversion should handle List<Map> to List<Server>', () {
      // Simulate server list data coming from Swift Platform Channel
      final rawServerList = [
        <String, Object?>{
          'id': '1',
          'name': '服务器1',
          'name_en': 'Server 1',
          'server_name': 'server1.example.com',
          'server_port': 8080,
          'ping': 30,
          'isauto': false,
          'status': 'online',
          'isdefault': false,
        },
        <String, Object?>{
          'id': '2',
          'name': '服务器2',
          'name_en': 'Server 2',
          'server_name': 'server2.example.com',
          'server_port': 9090,
          'ping': 45,
          'isauto': true,
          'status': 'offline',
          'isdefault': true,
        },
      ];
      
      // Convert using the same logic as in the fix
      final servers = rawServerList.map((serverData) {
        final serverMap = Map<String, dynamic>.from(serverData);
        return Server.fromJson(serverMap);
            }).toList();
      
      expect(servers.length, equals(2));
      
      expect(servers[0].id, equals('1'));
      expect(servers[0].name, equals('服务器1'));
      expect(servers[0].isAuto, equals(false));
      
      expect(servers[1].id, equals('2'));
      expect(servers[1].name, equals('服务器2'));
      expect(servers[1].isAuto, equals(true));
      expect(servers[1].isDefault, equals(true));
    });
    
    test('Type conversion should handle null and invalid data gracefully', () {
      // Test with null data
      expect(() {
        final serverMap = Map<String, dynamic>.from(<String, Object?>{});
        Server.fromJson(serverMap);
      }, returnsNormally);
      
      // Test with missing required fields
      final incompleteData = <String, Object?>{
        'id': '1',
        // Missing other required fields
      };
      
      final convertedData = Map<String, dynamic>.from(incompleteData);
      final server = Server.fromJson(convertedData);
      
      // Should use default values for missing fields
      expect(server.id, equals('1'));
      expect(server.name, equals(''));
      expect(server.serverPort, equals(0));
    });
  });
}
