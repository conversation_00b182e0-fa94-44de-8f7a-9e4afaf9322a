// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.


import 'package:flutter_test/flutter_test.dart';

import 'package:panabit_client/main.dart';
import 'package:panabit_client/services/single_instance_service.dart';
import 'package:panabit_client/services/log_service.dart';

void main() {
  testWidgets('App initialization test', (WidgetTester tester) async {
    // Create a mock log service for testing
    final logService = LogService();

    // Create a mock single instance service for testing
    final singleInstanceService = SingleInstanceService(logService: logService);

    // Build our app and trigger a frame.
    await tester.pumpWidget(MyApp(singleInstanceService: singleInstanceService));

    // Verify that the app loads without errors
    expect(find.byType(MyApp), findsOneWidget);
  });
}
