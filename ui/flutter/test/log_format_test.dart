// /**
//  * AUTHOR: wei
//  * HISTORY: 27/06/2025 - Test for normal text log format
//  */

import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'package:itforce/services/platform/file_log_service.dart';

/// MockPathProviderPlatform
class MockPathProviderPlatform extends Fake
    with MockPlatformInterfaceMixin
    implements PathProviderPlatform {
  
  @override
  Future<String?> getApplicationSupportPath() async {
    return 'C:\\Users\\<USER>\\AppData\\Roaming';
  }
}

void main() {
  group('Log Format Tests', () {
    late MockPathProviderPlatform mockPathProvider;

    setUpAll(() {
      mockPathProvider = MockPathProviderPlatform();
      PathProviderPlatform.instance = mockPathProvider;
    });

    test('should write logs in normal text format instead of JSON', () async {
      final logService = FileLogService();
      
      try {
        // 初始化日志服务
        await logService.initialize();
        
        // 写入不同级别的日志
        logService.info('TestModule', 'This is an info message');
        logService.warning('TestModule', 'This is a warning message');
        logService.error('TestModule', 'This is an error message', 'Sample error');
        
        // 等待一下确保文件写入完成
        await Future.delayed(const Duration(milliseconds: 100));
        
        // 获取日志文件
        final logFiles = await logService.getLogFiles();
        expect(logFiles, isNotEmpty);
        
        // 读取日志文件内容
        final logFile = File(logFiles.first);
        final content = await logFile.readAsString();
        
        print('📝 Log file content:');
        print(content);
        
        // 验证日志格式
        final lines = content.split('\n').where((line) => line.isNotEmpty).toList();
        expect(lines.length, greaterThanOrEqualTo(3));
        
        // 验证每行都符合预期格式: [时间] [级别] [模块] 消息
        for (final line in lines) {
          expect(line, matches(r'^\[.+\] \[.+\] \[.+\] .+$'));
          print('✅ Log line format correct: $line');
        }
        
        // 验证具体内容
        expect(content, contains('[INFO] [TestModule] This is an info message'));
        expect(content, contains('[WARNING] [TestModule] This is a warning message'));
        expect(content, contains('[ERROR] [TestModule] This is an error message'));
        expect(content, contains('Error: Sample error'));
        
        // 验证不是JSON格式
        expect(content, isNot(contains('{"timestamp"')));
        expect(content, isNot(contains('"level"')));
        expect(content, isNot(contains('"module"')));
        
        print('✅ All log format tests passed');
        
      } catch (e) {
        print('❌ Log format test failed: $e');
        rethrow;
      } finally {
        await logService.dispose();
      }
    });

    test('should parse log lines correctly for JSON export', () async {
      final logService = FileLogService();
      
      try {
        await logService.initialize();
        
        // 写入一条日志
        logService.info('TestModule', 'Test message for parsing');
        
        // 等待写入完成
        await Future.delayed(const Duration(milliseconds: 100));
        
        // 测试导出为JSON格式
        final exportPath = await logService.exportLogs(format: 'json');
        expect(exportPath, isNotEmpty);
        
        // 验证导出的JSON文件
        final exportFile = File(exportPath);
        expect(await exportFile.exists(), isTrue);
        
        final jsonContent = await exportFile.readAsString();
        print('📄 Exported JSON content: $jsonContent');
        
        // 验证JSON格式正确
        expect(jsonContent, contains('"level"'));
        expect(jsonContent, contains('"module"'));
        expect(jsonContent, contains('"message"'));
        expect(jsonContent, contains('TestModule'));
        expect(jsonContent, contains('Test message for parsing'));
        
        print('✅ JSON export test passed');
        
      } catch (e) {
        print('❌ JSON export test failed: $e');
        rethrow;
      } finally {
        await logService.dispose();
      }
    });
  });
}
