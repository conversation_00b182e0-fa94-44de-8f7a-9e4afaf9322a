// Mocks generated by <PERSON>cki<PERSON> 5.4.6 from annotations
// in panabit_client/test/tray_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;
import 'dart:io' as _i11;
import 'dart:ui' as _i18;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i10;
import 'package:panabit_client/core/app_state.dart' as _i16;
import 'package:panabit_client/models/interface_info.dart' as _i5;
import 'package:panabit_client/models/log_entry.dart' as _i7;
import 'package:panabit_client/models/routing_settings.dart' as _i14;
import 'package:panabit_client/models/server.dart' as _i13;
import 'package:panabit_client/models/traffic_stats.dart' as _i4;
import 'package:panabit_client/models/user_info.dart' as _i3;
import 'package:panabit_client/services/api_service.dart' as _i12;
import 'package:panabit_client/services/backend_service.dart' as _i15;
import 'package:panabit_client/services/log_service.dart' as _i2;
import 'package:panabit_client/services/platform/cross_platform_log_service.dart'
    as _i9;
import 'package:panabit_client/services/platform/cross_platform_storage_service.dart'
    as _i17;
import 'package:panabit_client/utils/constants.dart' as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeLogService_0 extends _i1.SmartFake implements _i2.LogService {
  _FakeLogService_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUserInfo_1 extends _i1.SmartFake implements _i3.UserInfo {
  _FakeUserInfo_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTrafficStats_2 extends _i1.SmartFake implements _i4.TrafficStats {
  _FakeTrafficStats_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeInterfaceInfo_3 extends _i1.SmartFake implements _i5.InterfaceInfo {
  _FakeInterfaceInfo_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [LogService].
///
/// See the documentation for Mockito's code generation for more information.
class MockLogService extends _i1.Mock implements _i2.LogService {
  MockLogService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Stream<_i7.LogEntry> get logStream => (super.noSuchMethod(
        Invocation.getter(#logStream),
        returnValue: _i6.Stream<_i7.LogEntry>.empty(),
      ) as _i6.Stream<_i7.LogEntry>);

  @override
  List<_i7.LogEntry> get logs => (super.noSuchMethod(
        Invocation.getter(#logs),
        returnValue: <_i7.LogEntry>[],
      ) as List<_i7.LogEntry>);

  @override
  _i8.LogLevel get currentLogLevel => (super.noSuchMethod(
        Invocation.getter(#currentLogLevel),
        returnValue: _i8.LogLevel.debug,
      ) as _i8.LogLevel);

  @override
  set platformLogService(_i9.CrossPlatformLogService? service) =>
      super.noSuchMethod(
        Invocation.setter(
          #platformLogService,
          service,
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setLogLevel(_i8.LogLevel? level) => super.noSuchMethod(
        Invocation.method(
          #setLogLevel,
          [level],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void debug(
    String? module,
    String? message,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #debug,
          [
            module,
            message,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void info(
    String? module,
    String? message,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #info,
          [
            module,
            message,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void warning(
    String? module,
    String? message,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #warning,
          [
            module,
            message,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void error(
    String? module,
    String? message, [
    dynamic error,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #error,
          [
            module,
            message,
            error,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i6.Future<String> getLogFilePath() => (super.noSuchMethod(
        Invocation.method(
          #getLogFilePath,
          [],
        ),
        returnValue: _i6.Future<String>.value(_i10.dummyValue<String>(
          this,
          Invocation.method(
            #getLogFilePath,
            [],
          ),
        )),
      ) as _i6.Future<String>);

  @override
  _i6.Future<List<_i11.File>> getLogFiles() => (super.noSuchMethod(
        Invocation.method(
          #getLogFiles,
          [],
        ),
        returnValue: _i6.Future<List<_i11.File>>.value(<_i11.File>[]),
      ) as _i6.Future<List<_i11.File>>);

  @override
  void clearLogs() => super.noSuchMethod(
        Invocation.method(
          #clearLogs,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  List<_i7.LogEntry> filterByLevel(_i8.LogLevel? level) => (super.noSuchMethod(
        Invocation.method(
          #filterByLevel,
          [level],
        ),
        returnValue: <_i7.LogEntry>[],
      ) as List<_i7.LogEntry>);

  @override
  List<_i7.LogEntry> filterByModule(String? module) => (super.noSuchMethod(
        Invocation.method(
          #filterByModule,
          [module],
        ),
        returnValue: <_i7.LogEntry>[],
      ) as List<_i7.LogEntry>);

  @override
  List<_i7.LogEntry> search(String? keyword) => (super.noSuchMethod(
        Invocation.method(
          #search,
          [keyword],
        ),
        returnValue: <_i7.LogEntry>[],
      ) as List<_i7.LogEntry>);

  @override
  _i6.Future<String> exportLogs() => (super.noSuchMethod(
        Invocation.method(
          #exportLogs,
          [],
        ),
        returnValue: _i6.Future<String>.value(_i10.dummyValue<String>(
          this,
          Invocation.method(
            #exportLogs,
            [],
          ),
        )),
      ) as _i6.Future<String>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [ApiService].
///
/// See the documentation for Mockito's code generation for more information.
class MockApiService extends _i1.Mock implements _i12.ApiService {
  MockApiService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get baseUrl => (super.noSuchMethod(
        Invocation.getter(#baseUrl),
        returnValue: _i10.dummyValue<String>(
          this,
          Invocation.getter(#baseUrl),
        ),
      ) as String);

  @override
  _i2.LogService get logService => (super.noSuchMethod(
        Invocation.getter(#logService),
        returnValue: _FakeLogService_0(
          this,
          Invocation.getter(#logService),
        ),
      ) as _i2.LogService);

  @override
  set logService(_i2.LogService? service) => super.noSuchMethod(
        Invocation.setter(
          #logService,
          service,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i6.Future<bool> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<Map<String, dynamic>> login(
    String? username,
    String? password,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [
            username,
            password,
          ],
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  _i13.Server? getBestServerFromLoginResponse(
          Map<String, dynamic>? loginResponse) =>
      (super.noSuchMethod(Invocation.method(
        #getBestServerFromLoginResponse,
        [loginResponse],
      )) as _i13.Server?);

  @override
  _i6.Future<List<_i13.Server>> getServers() => (super.noSuchMethod(
        Invocation.method(
          #getServers,
          [],
        ),
        returnValue: _i6.Future<List<_i13.Server>>.value(<_i13.Server>[]),
      ) as _i6.Future<List<_i13.Server>>);

  @override
  _i6.Future<void> pingServers() => (super.noSuchMethod(
        Invocation.method(
          #pingServers,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> connect(String? serverId) => (super.noSuchMethod(
        Invocation.method(
          #connect,
          [serverId],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> disconnect() => (super.noSuchMethod(
        Invocation.method(
          #disconnect,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<Map<String, dynamic>> getStatus() => (super.noSuchMethod(
        Invocation.method(
          #getStatus,
          [],
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<bool> healthCheck() => (super.noSuchMethod(
        Invocation.method(
          #healthCheck,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<void> shutdown() => (super.noSuchMethod(
        Invocation.method(
          #shutdown,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<bool> shutdownBackend() => (super.noSuchMethod(
        Invocation.method(
          #shutdownBackend,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<Map<String, dynamic>> getInterfaceInfo() => (super.noSuchMethod(
        Invocation.method(
          #getInterfaceInfo,
          [],
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<Map<String, dynamic>> getRoutingSettings() => (super.noSuchMethod(
        Invocation.method(
          #getRoutingSettings,
          [],
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<void> setRoutingSettings(_i14.RoutingSettingsModel? settings) =>
      (super.noSuchMethod(
        Invocation.method(
          #setRoutingSettings,
          [settings],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> setServerProviderUrl(String? url) => (super.noSuchMethod(
        Invocation.method(
          #setServerProviderUrl,
          [url],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> setDomain(String? domain) => (super.noSuchMethod(
        Invocation.method(
          #setDomain,
          [domain],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);
}

/// A class which mocks [BackendService].
///
/// See the documentation for Mockito's code generation for more information.
class MockBackendService extends _i1.Mock implements _i15.BackendService {
  MockBackendService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.LogService get logService => (super.noSuchMethod(
        Invocation.getter(#logService),
        returnValue: _FakeLogService_0(
          this,
          Invocation.getter(#logService),
        ),
      ) as _i2.LogService);

  @override
  bool get isRunning => (super.noSuchMethod(
        Invocation.getter(#isRunning),
        returnValue: false,
      ) as bool);

  @override
  _i6.Future<bool> start() => (super.noSuchMethod(
        Invocation.method(
          #start,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<bool> startWithAdminPrivileges() => (super.noSuchMethod(
        Invocation.method(
          #startWithAdminPrivileges,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<void> stop() => (super.noSuchMethod(
        Invocation.method(
          #stop,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> stopBackend() => (super.noSuchMethod(
        Invocation.method(
          #stopBackend,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);
}

/// A class which mocks [AppState].
///
/// See the documentation for Mockito's code generation for more information.
class MockAppState extends _i1.Mock implements _i16.AppState {
  MockAppState() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i8.ConnectionStatus get connectionStatus => (super.noSuchMethod(
        Invocation.getter(#connectionStatus),
        returnValue: _i8.ConnectionStatus.disconnected,
      ) as _i8.ConnectionStatus);

  @override
  String get connectionMessage => (super.noSuchMethod(
        Invocation.getter(#connectionMessage),
        returnValue: _i10.dummyValue<String>(
          this,
          Invocation.getter(#connectionMessage),
        ),
      ) as String);

  @override
  _i3.UserInfo get userInfo => (super.noSuchMethod(
        Invocation.getter(#userInfo),
        returnValue: _FakeUserInfo_1(
          this,
          Invocation.getter(#userInfo),
        ),
      ) as _i3.UserInfo);

  @override
  bool get isAuthenticated => (super.noSuchMethod(
        Invocation.getter(#isAuthenticated),
        returnValue: false,
      ) as bool);

  @override
  _i4.TrafficStats get trafficStats => (super.noSuchMethod(
        Invocation.getter(#trafficStats),
        returnValue: _FakeTrafficStats_2(
          this,
          Invocation.getter(#trafficStats),
        ),
      ) as _i4.TrafficStats);

  @override
  _i5.InterfaceInfo get interfaceInfo => (super.noSuchMethod(
        Invocation.getter(#interfaceInfo),
        returnValue: _FakeInterfaceInfo_3(
          this,
          Invocation.getter(#interfaceInfo),
        ),
      ) as _i5.InterfaceInfo);

  @override
  bool get isLoading => (super.noSuchMethod(
        Invocation.getter(#isLoading),
        returnValue: false,
      ) as bool);

  @override
  bool get isWebSocketConnected => (super.noSuchMethod(
        Invocation.getter(#isWebSocketConnected),
        returnValue: false,
      ) as bool);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  void updateConnectionStatus(
    _i8.ConnectionStatus? status, {
    String? message,
    _i13.Server? server,
    DateTime? connectedTime,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #updateConnectionStatus,
          [status],
          {
            #message: message,
            #server: server,
            #connectedTime: connectedTime,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  void selectServer(
    _i13.Server? server, {
    _i16.ServerSelectionSource? source =
        _i16.ServerSelectionSource.userSelection,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #selectServer,
          [server],
          {#source: source},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void updateServerInfo(_i13.Server? server) => super.noSuchMethod(
        Invocation.method(
          #updateServerInfo,
          [server],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void syncCurrentServerPing(
    int? newPing,
    String? newStatus,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #syncCurrentServerPing,
          [
            newPing,
            newStatus,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void updateUserInfo(_i3.UserInfo? userInfo) => super.noSuchMethod(
        Invocation.method(
          #updateUserInfo,
          [userInfo],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void updateAuthenticationStatus(bool? isAuthenticated) => super.noSuchMethod(
        Invocation.method(
          #updateAuthenticationStatus,
          [isAuthenticated],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void updateTrafficStats(_i4.TrafficStats? stats) => super.noSuchMethod(
        Invocation.method(
          #updateTrafficStats,
          [stats],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void updateInterfaceInfo(_i5.InterfaceInfo? info) => super.noSuchMethod(
        Invocation.method(
          #updateInterfaceInfo,
          [info],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void updateLoadingStatus(bool? isLoading) => super.noSuchMethod(
        Invocation.method(
          #updateLoadingStatus,
          [isLoading],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void updateErrorMessage(String? message) => super.noSuchMethod(
        Invocation.method(
          #updateErrorMessage,
          [message],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void updateWebSocketStatus(bool? isConnected) => super.noSuchMethod(
        Invocation.method(
          #updateWebSocketStatus,
          [isConnected],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void clearError() => super.noSuchMethod(
        Invocation.method(
          #clearError,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void reset() => super.noSuchMethod(
        Invocation.method(
          #reset,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i6.Future<void> initializeStorage(
          _i17.CrossPlatformStorageService? storageService) =>
      (super.noSuchMethod(
        Invocation.method(
          #initializeStorage,
          [storageService],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  void addListener(_i18.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i18.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
