/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      manager.go
 *
 * DESCRIPTION :    Goroutine lifecycle manager with leak detection and monitoring
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package goroutine

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: Manager
 *
 * DESCRIPTION:
 *     Goroutine lifecycle manager that tracks, monitors, and manages goroutines
 *
 * FIELDS:
 *     goroutines   - Map of active goroutines
 *     counter      - Atomic counter for goroutine IDs
 *     mutex        - Mutex for thread-safe operations
 *     logger       - Logger instance
 *     ctx          - Manager context
 *     cancel       - Context cancel function
 *     wg           - Wait group for graceful shutdown
 *****************************************************************************/
type Manager struct {
	goroutines map[uint64]*GoroutineInfo
	counter    uint64
	mutex      sync.RWMutex
	logger     logger.Logger
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
}

/*****************************************************************************
 * NAME: GoroutineInfo
 *
 * DESCRIPTION:
 *     Information about a managed goroutine
 *****************************************************************************/
type GoroutineInfo struct {
	ID         uint64
	Name       string
	StartTime  time.Time
	Context    context.Context
	Cancel     context.CancelFunc
	Function   string
	Status     GoroutineStatus
	PanicCount int
	LastPanic  time.Time
	LastError  error
}

/*****************************************************************************
 * NAME: GoroutineStatus
 *
 * DESCRIPTION:
 *     Status of a goroutine
 *****************************************************************************/
type GoroutineStatus int

const (
	StatusRunning GoroutineStatus = iota
	StatusStopping
	StatusStopped
	StatusPanicked
	StatusError
)

/*****************************************************************************
 * NAME: GoroutineFunc
 *
 * DESCRIPTION:
 *     Function signature for managed goroutines
 *****************************************************************************/
type GoroutineFunc func(ctx context.Context) error

/*****************************************************************************
 * NAME: NewManager
 *
 * DESCRIPTION:
 *     Creates a new goroutine manager
 *
 * PARAMETERS:
 *     logger - Logger instance
 *
 * RETURNS:
 *     *Manager - New manager instance
 *****************************************************************************/
func NewManager(logger logger.Logger) *Manager {
	ctx, cancel := context.WithCancel(context.Background())

	return &Manager{
		goroutines: make(map[uint64]*GoroutineInfo),
		logger:     logger.WithModule("goroutine"),
		ctx:        ctx,
		cancel:     cancel,
	}
}

/*****************************************************************************
 * NAME: Start
 *
 * DESCRIPTION:
 *     Starts a new managed goroutine
 *
 * PARAMETERS:
 *     name - Name of the goroutine
 *     fn   - Function to execute
 *
 * RETURNS:
 *     uint64 - Goroutine ID
 *     error  - Start error if any
 *****************************************************************************/
func (m *Manager) Start(name string, fn GoroutineFunc) (uint64, error) {
	//m.mutex.Lock()
	//defer m.mutex.Unlock()

	// Generate unique ID
	id := atomic.AddUint64(&m.counter, 1)

	// Create goroutine context
	ctx, cancel := context.WithCancel(m.ctx)

	// Get caller information
	pc, file, line, _ := runtime.Caller(1)
	function := runtime.FuncForPC(pc).Name()

	// Create goroutine info
	info := &GoroutineInfo{
		ID:        id,
		Name:      name,
		StartTime: time.Now(),
		Context:   ctx,
		Cancel:    cancel,
		Function:  fmt.Sprintf("%s (%s:%d)", function, file, line),
		Status:    StatusRunning,
	}

	m.goroutines[id] = info
	m.wg.Add(1)

	// Start goroutine
	go m.runGoroutine(info, fn)

	m.logger.Debug("Started goroutine",
		logger.Uint64("id", id),
		logger.String("name", name),
		logger.String("function", info.Function))

	return id, nil
}

/*****************************************************************************
 * NAME: Stop
 *
 * DESCRIPTION:
 *     Stops a specific goroutine
 *
 * PARAMETERS:
 *     id - Goroutine ID
 *
 * RETURNS:
 *     error - Stop error if any
 *****************************************************************************/
func (m *Manager) Stop(id uint64) error {
	//m.mutex.Lock()
	info, exists := m.goroutines[id]
	if !exists {
		//m.mutex.Unlock()
		return errors.NewWithCode(errors.CodeNotFound,
			"goroutine %d not found", id).(*errors.DetailedError).
			WithComponent("goroutine").WithOperation("stop")
	}

	if info.Status != StatusRunning {
		//m.mutex.Unlock()
		return errors.NewWithCode(errors.CodeFailedPrecondition,
			"goroutine %d is not running", id).(*errors.DetailedError).
			WithComponent("goroutine").WithOperation("stop")
	}

	info.Status = StatusStopping
	//m.mutex.Unlock()

	// Cancel the goroutine context
	info.Cancel()

	m.logger.Debug("Stopping goroutine",
		logger.Uint64("id", id),
		logger.String("name", info.Name))

	return nil
}

/*****************************************************************************
 * NAME: StopAll
 *
 * DESCRIPTION:
 *     Stops all managed goroutines
 *
 * PARAMETERS:
 *     timeout - Maximum time to wait for goroutines to stop
 *
 * RETURNS:
 *     error - Stop error if any
 *****************************************************************************/
func (m *Manager) StopAll(timeout time.Duration) error {
	m.logger.Info("Stopping all goroutines", logger.Duration("timeout", timeout))

	// Cancel all goroutines
	m.cancel()

	// Wait for all goroutines to finish with timeout
	done := make(chan struct{})
	go func() {
		m.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		m.logger.Info("All goroutines stopped successfully")
		return nil
	case <-time.After(timeout):
		//m.mutex.RLock()
		runningCount := 0
		for _, info := range m.goroutines {
			if info.Status == StatusRunning {
				runningCount++
				m.logger.Warn("Goroutine still running after timeout",
					logger.Uint64("id", info.ID),
					logger.String("name", info.Name),
					logger.Duration("runtime", time.Since(info.StartTime)))
			}
		}
		//m.mutex.RUnlock()

		return errors.NewWithCode(errors.CodeTimeout,
			"timeout waiting for %d goroutines to stop", runningCount).(*errors.DetailedError).
			WithComponent("goroutine").WithOperation("stop_all")
	}
}

/*****************************************************************************
 * NAME: GetStatus
 *
 * DESCRIPTION:
 *     Gets status of all managed goroutines
 *
 * RETURNS:
 *     map[uint64]*GoroutineInfo - Map of goroutine information
 *****************************************************************************/
func (m *Manager) GetStatus() map[uint64]*GoroutineInfo {
	//m.mutex.RLock()
	//defer m.mutex.RUnlock()

	// Create a copy to avoid race conditions
	status := make(map[uint64]*GoroutineInfo)
	for id, info := range m.goroutines {
		// Create a copy of the info
		infoCopy := *info
		status[id] = &infoCopy
	}

	return status
}

/*****************************************************************************
 * NAME: GetMetrics
 *
 * DESCRIPTION:
 *     Gets goroutine metrics
 *
 * RETURNS:
 *     map[string]interface{} - Metrics data
 *****************************************************************************/
func (m *Manager) GetMetrics() map[string]interface{} {
	//m.mutex.RLock()
	//defer m.mutex.RUnlock()

	var running, stopped, panicked, error_count int
	var totalPanics int
	var oldestStart time.Time
	var newestStart time.Time

	for _, info := range m.goroutines {
		switch info.Status {
		case StatusRunning:
			running++
		case StatusStopped:
			stopped++
		case StatusPanicked:
			panicked++
		case StatusError:
			error_count++
		}

		totalPanics += info.PanicCount

		if oldestStart.IsZero() || info.StartTime.Before(oldestStart) {
			oldestStart = info.StartTime
		}
		if newestStart.IsZero() || info.StartTime.After(newestStart) {
			newestStart = info.StartTime
		}
	}

	return map[string]interface{}{
		"total_goroutines":  len(m.goroutines),
		"running":           running,
		"stopped":           stopped,
		"panicked":          panicked,
		"errors":            error_count,
		"total_panics":      totalPanics,
		"oldest_start":      oldestStart,
		"newest_start":      newestStart,
		"system_goroutines": runtime.NumGoroutine(),
	}
}

/*****************************************************************************
 * NAME: runGoroutine
 *
 * DESCRIPTION:
 *     Internal method to run a managed goroutine with panic recovery
 *
 * PARAMETERS:
 *     info - Goroutine information structure
 *     fn   - Function to execute in the goroutine
 *
 * RETURNS:
 *     None (void function)
 *****************************************************************************/
func (m *Manager) runGoroutine(info *GoroutineInfo, fn GoroutineFunc) {
	defer func() {
		if r := recover(); r != nil {
			//m.mutex.Lock()
			info.Status = StatusPanicked
			info.PanicCount++
			info.LastPanic = time.Now()
			info.LastError = fmt.Errorf("panic: %v", r)
			//m.mutex.Unlock()

			// Get stack trace
			buf := make([]byte, 4096)
			n := runtime.Stack(buf, false)
			stackTrace := string(buf[:n])

			m.logger.Error("Goroutine panicked",
				logger.Uint64("id", info.ID),
				logger.String("name", info.Name),
				logger.Any("panic", r),
				logger.String("stack", stackTrace))
		}

		//m.mutex.Lock()
		if info.Status == StatusRunning {
			info.Status = StatusStopped
		}
		//m.mutex.Unlock()

		m.wg.Done()

		m.logger.Debug("Goroutine finished",
			logger.Uint64("id", info.ID),
			logger.String("name", info.Name),
			logger.Duration("runtime", time.Since(info.StartTime)))
	}()

	// Execute the function
	if err := fn(info.Context); err != nil {
		//m.mutex.Lock()
		info.Status = StatusError
		info.LastError = err
		//m.mutex.Unlock()

		m.logger.Error("Goroutine error",
			logger.Uint64("id", info.ID),
			logger.String("name", info.Name),
			logger.ErrorField(err))
	}
}
