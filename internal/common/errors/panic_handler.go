/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      panic_handler.go
 *
 * DESCRIPTION :    Panic堆栈跟踪记录工具
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025  create
 ******************************************************************************/

package errors

import (
	"fmt"
	"os"
	"runtime"
	"runtime/debug"
	"strings"
	"time"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: PanicLogger
 *
 * DESCRIPTION:
 *     Panic logger for capturing and reporting panic information
 *
 * FIELDS:
 *     log        - Logger instance for panic logging
 *     reportPath - Directory path for panic report files
 *     appVersion - Application version string
 *     callDepth  - Maximum call stack depth to capture
 *****************************************************************************/
type PanicLogger struct {
	log        logger.Logger
	reportPath string
	appVersion string
	callDepth  int
}

/*****************************************************************************
 * NAME: NewPanicLogger
 *
 * DESCRIPTION:
 *     Creates a new panic logger instance with configuration
 *
 * PARAMETERS:
 *     log        - Logger instance for panic logging
 *     reportPath - Directory path for panic report files
 *     appVersion - Application version string
 *
 * RETURNS:
 *     *PanicLogger - New panic logger instance
 *****************************************************************************/
func NewPanicLogger(log logger.Logger, reportPath string, appVersion string) *PanicLogger {
	// 确保报告目录存在
	if reportPath != "" {
		os.MkdirAll(reportPath, 0755)
	}

	return &PanicLogger{
		log:        log,
		reportPath: reportPath,
		appVersion: appVersion,
		callDepth:  20,
	}
}

/*****************************************************************************
 * NAME: SetCallDepth
 *
 * DESCRIPTION:
 *     Sets the maximum call stack depth to capture
 *
 * PARAMETERS:
 *     depth - Maximum call stack depth
 *
 * RETURNS:
 *     None
 *****************************************************************************/
func (l *PanicLogger) SetCallDepth(depth int) {
	l.callDepth = depth
}

/*****************************************************************************
 * NAME: EnableDetailedStackTrace
 *
 * DESCRIPTION:
 *     Enables detailed stack trace by setting GOTRACEBACK environment variable
 *
 * RETURNS:
 *     None
 *****************************************************************************/
func EnableDetailedStackTrace() {
	// 设置环境变量以获取更详细的堆栈跟踪
	os.Setenv("GOTRACEBACK", "all")
}

/*****************************************************************************
 * NAME: LogPanic
 *
 * DESCRIPTION:
 *     Logs panic information with detailed stack trace and saves report file
 *
 * PARAMETERS:
 *     r - Panic recovery value
 *
 * RETURNS:
 *     None
 *****************************************************************************/
func (l *PanicLogger) LogPanic(r interface{}) {
	// 获取当前时间
	now := time.Now()

	// 获取调用堆栈
	stack := debug.Stack()

	// 获取更详细的调用堆栈
	var detailedStack strings.Builder
	detailedStack.WriteString(fmt.Sprintf("Panic: %v\n\n", r))
	detailedStack.WriteString("Goroutine stack:\n")
	detailedStack.Write(stack)

	// 获取更多调用帧信息
	if l.callDepth > 0 {
		detailedStack.WriteString("\nDetailed call frames:\n")

		pcs := make([]uintptr, l.callDepth)
		n := runtime.Callers(3, pcs)
		frames := runtime.CallersFrames(pcs[:n])

		for {
			frame, more := frames.Next()
			detailedStack.WriteString(fmt.Sprintf("%s\n\t%s:%d\n", frame.Function, frame.File, frame.Line))
			if !more {
				break
			}
		}
	}

	// 记录panic日志
	l.log.Error("Unhandled panic",
		logger.String("panic", fmt.Sprintf("%v", r)),
		logger.String("stack", string(stack)),
		logger.String("time", now.Format(time.RFC3339)),
		logger.String("app_version", l.appVersion),
	)

	// 保存详细报告
	if l.reportPath != "" {
		reportFile := fmt.Sprintf("%s/panic_%s.log", l.reportPath, now.Format("20060102_150405"))
		err := os.WriteFile(reportFile, []byte(detailedStack.String()), 0644)
		if err != nil {
			l.log.Error("Failed to write panic report",
				logger.ErrorField(err),
				logger.String("report_file", reportFile),
			)
		} else {
			l.log.Info("Panic report saved",
				logger.String("report_file", reportFile),
			)
		}
	}
}
