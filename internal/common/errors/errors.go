/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      errors.go
 *
 * DESCRIPTION :    Enhanced error handling and reporting for the VPN client
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025  create
 ******************************************************************************/

package errors

import (
	"fmt"
	"runtime"
	"strings"
	"time"
)

// ErrorCode represents an error code.
type ErrorCode int

// ErrorCategory is defined in codes.go

// Error codes
const (
	// General errors
	CodeUnknown            ErrorCode = 0
	CodeInternal           ErrorCode = 1
	CodeInvalidArgument    ErrorCode = 2
	CodeNotImplemented     ErrorCode = 3
	CodeUnavailable        ErrorCode = 4
	CodeTimeout            ErrorCode = 5
	CodeCanceled           ErrorCode = 6
	CodeAlreadyExists      ErrorCode = 7
	CodeNotFound           ErrorCode = 8
	CodePermissionDenied   ErrorCode = 9
	CodeResourceExhausted  ErrorCode = 10
	CodeFailedPrecondition ErrorCode = 11
	CodeAborted            ErrorCode = 12
	CodeOutOfRange         ErrorCode = 13
	CodeInvalidState       ErrorCode = 14

	// Network errors
	CodeNetworkError      ErrorCode = 100
	CodeConnectionFailed  ErrorCode = 101
	CodeConnectionClosed  ErrorCode = 102
	CodeConnectionTimeout ErrorCode = 103
	CodeConnectionReset   ErrorCode = 104
	CodeHostUnreachable   ErrorCode = 105
	CodeDNSError          ErrorCode = 106

	// Authentication errors
	CodeAuthError            ErrorCode = 200
	CodeAuthenticationFailed ErrorCode = 201
	CodeUnauthenticated      ErrorCode = 202
	CodeUnauthorized         ErrorCode = 203
	CodeSessionExpired       ErrorCode = 204
	CodeInvalidCredentials   ErrorCode = 205

	// Tunnel errors
	CodeTunnelError        ErrorCode = 300
	CodeTunnelCloseFailed  ErrorCode = 302
	CodeTunnelReadFailed   ErrorCode = 303
	CodeTunnelWriteFailed  ErrorCode = 304
	CodeTunnelConfigFailed ErrorCode = 305

	// Configuration errors
	CodeConfigError       ErrorCode = 400
	CodeConfigNotFound    ErrorCode = 402
	CodeConfigReadFailed  ErrorCode = 403
	CodeConfigWriteFailed ErrorCode = 404
	CodeConfigParseFailed ErrorCode = 405

	// Platform errors
	CodePlatformError        ErrorCode = 500
	CodePlatformNotSupported ErrorCode = 501
	CodePlatformInitFailed   ErrorCode = 502
	CodePlatformIOError      ErrorCode = 503

	// Protocol errors
	CodeProtocolError            ErrorCode = 600
	CodeProtocolInvalid          ErrorCode = 601
	CodeProtocolEncryptionFailed ErrorCode = 605
	CodeProtocolDecryptionFailed ErrorCode = 606
	CodePacketError              ErrorCode = 607
)

// String returns the string representation of the error code
func (c ErrorCode) String() string {
	if name, ok := errorCodeNames[c]; ok {
		return name
	}
	return fmt.Sprintf("ErrorCode(%d)", c)
}

// GetCode returns the error code from an error
func GetCode(err error) ErrorCode {
	if err == nil {
		return CodeUnknown
	}

	// Check if it's our error type
	if e, ok := err.(*DetailedError); ok {
		return e.Code
	}

	// Check if it's a wrapped error
	if e, ok := err.(interface{ Unwrap() error }); ok {
		return GetCode(e.Unwrap())
	}

	return CodeUnknown
}

// GetCategory is defined in codes.go

/*****************************************************************************
 * NAME: ErrorSeverity
 *
 * DESCRIPTION:
 *     Defines the severity level of an error
 *****************************************************************************/
type ErrorSeverity int

const (
	SeverityInfo ErrorSeverity = iota
	SeverityWarning
	SeverityError
	SeverityCritical
	SeverityFatal
)

/*****************************************************************************
 * NAME: DetailedError
 *
 * DESCRIPTION:
 *     Enhanced error with code, context, severity, and detailed stack trace
 *****************************************************************************/
type DetailedError struct {
	Code      ErrorCode              `json:"code"`
	Message   string                 `json:"message"`
	Details   string                 `json:"details,omitempty"`
	Severity  ErrorSeverity          `json:"severity"`
	Context   map[string]interface{} `json:"context,omitempty"`
	Cause     error                  `json:"-"`
	Stack     string                 `json:"stack,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	Component string                 `json:"component,omitempty"`
	Operation string                 `json:"operation,omitempty"`
}

/*****************************************************************************
 * NAME: Error
 *
 * DESCRIPTION:
 *     Returns the error message with enhanced formatting
 *****************************************************************************/
func (e *DetailedError) Error() string {
	var parts []string

	// Add severity prefix
	switch e.Severity {
	case SeverityCritical:
		parts = append(parts, "[CRITICAL]")
	case SeverityFatal:
		parts = append(parts, "[FATAL]")
	case SeverityError:
		parts = append(parts, "[ERROR]")
	case SeverityWarning:
		parts = append(parts, "[WARNING]")
	case SeverityInfo:
		parts = append(parts, "[INFO]")
	}

	// Add component and operation if available
	if e.Component != "" {
		parts = append(parts, fmt.Sprintf("[%s]", e.Component))
	}
	if e.Operation != "" {
		parts = append(parts, fmt.Sprintf("(%s)", e.Operation))
	}

	// Add error code and message
	parts = append(parts, fmt.Sprintf("[%d] %s", e.Code, e.Message))

	// Add details if available
	if e.Details != "" {
		parts = append(parts, fmt.Sprintf(": %s", e.Details))
	}

	// Add cause if available
	if e.Cause != nil {
		parts = append(parts, fmt.Sprintf(" -> %v", e.Cause))
	}

	return strings.Join(parts, " ")
}

/*****************************************************************************
 * NAME: Unwrap
 *
 * DESCRIPTION:
 *     Returns the underlying cause error
 *****************************************************************************/
func (e *DetailedError) Unwrap() error {
	return e.Cause
}

/*****************************************************************************
 * NAME: WithContext
 *
 * DESCRIPTION:
 *     Adds context information to the error
 *****************************************************************************/
func (e *DetailedError) WithContext(key string, value interface{}) *DetailedError {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	e.Context[key] = value
	return e
}

/*****************************************************************************
 * NAME: WithSeverity
 *
 * DESCRIPTION:
 *     Sets the severity level of the error
 *****************************************************************************/
func (e *DetailedError) WithSeverity(severity ErrorSeverity) *DetailedError {
	e.Severity = severity
	return e
}

/*****************************************************************************
 * NAME: WithComponent
 *
 * DESCRIPTION:
 *     Sets the component that generated the error
 *****************************************************************************/
func (e *DetailedError) WithComponent(component string) *DetailedError {
	e.Component = component
	return e
}

/*****************************************************************************
 * NAME: WithOperation
 *
 * DESCRIPTION:
 *     Sets the operation that was being performed when the error occurred
 *****************************************************************************/
func (e *DetailedError) WithOperation(operation string) *DetailedError {
	e.Operation = operation
	return e
}

/*****************************************************************************
 * NAME: WithDetails
 *
 * DESCRIPTION:
 *     Adds detailed information to the error
 *****************************************************************************/
func (e *DetailedError) WithDetails(details string) *DetailedError {
	e.Details = details
	return e
}

/*****************************************************************************
 * NAME: NewWithCode
 *
 * DESCRIPTION:
 *     Creates a new enhanced error with the given code and message
 *****************************************************************************/
func NewWithCode(code ErrorCode, format string, args ...interface{}) error {
	msg := fmt.Sprintf(format, args...)
	stack := captureStack(2)
	return &DetailedError{
		Code:      code,
		Message:   msg,
		Severity:  SeverityError,
		Context:   make(map[string]interface{}),
		Stack:     stack,
		Timestamp: time.Now(),
	}
}

/*****************************************************************************
 * NAME: WrapWithCode
 *
 * DESCRIPTION:
 *     Wraps an existing error with enhanced context and code
 *****************************************************************************/
func WrapWithCode(code ErrorCode, err error, format string, args ...interface{}) error {
	if err == nil {
		return nil
	}
	msg := fmt.Sprintf(format, args...)
	stack := captureStack(2)

	// If it's already a DetailedError, preserve some context
	var severity ErrorSeverity = SeverityError
	var context map[string]interface{}
	if detailedErr, ok := err.(*DetailedError); ok {
		severity = detailedErr.Severity
		context = detailedErr.Context
		if context == nil {
			context = make(map[string]interface{})
		}
	} else {
		context = make(map[string]interface{})
	}

	return &DetailedError{
		Code:      code,
		Message:   msg,
		Details:   err.Error(),
		Severity:  severity,
		Context:   context,
		Cause:     err,
		Stack:     stack,
		Timestamp: time.Now(),
	}
}

/*****************************************************************************
 * NAME: NewCritical
 *
 * DESCRIPTION:
 *     Creates a new critical error
 *****************************************************************************/
func NewCritical(code ErrorCode, format string, args ...interface{}) error {
	err := NewWithCode(code, format, args...).(*DetailedError)
	err.Severity = SeverityCritical
	return err
}

/*****************************************************************************
 * NAME: NewWarning
 *
 * DESCRIPTION:
 *     Creates a new warning error
 *****************************************************************************/
func NewWarning(code ErrorCode, format string, args ...interface{}) error {
	err := NewWithCode(code, format, args...).(*DetailedError)
	err.Severity = SeverityWarning
	return err
}

/*****************************************************************************
 * NAME: IsCode
 *
 * DESCRIPTION:
 *     Checks if an error has a specific error code
 *****************************************************************************/
func IsCode(err error, code ErrorCode) bool {
	return GetCode(err) == code
}

/*****************************************************************************
 * NAME: GetSeverity
 *
 * DESCRIPTION:
 *     Extracts the severity from an error
 *****************************************************************************/
func GetSeverity(err error) ErrorSeverity {
	if detailedErr, ok := err.(*DetailedError); ok {
		return detailedErr.Severity
	}
	return SeverityError
}

/*****************************************************************************
 * NAME: IsCritical
 *
 * DESCRIPTION:
 *     Checks if an error is critical or fatal
 *****************************************************************************/
func IsCritical(err error) bool {
	severity := GetSeverity(err)
	return severity == SeverityCritical || severity == SeverityFatal
}

// captureStack captures the stack trace
func captureStack(skip int) string {
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	stack := string(buf[:n])
	lines := strings.Split(stack, "\n")
	if len(lines) > skip*2+2 {
		lines = lines[skip*2:]
	}
	return strings.Join(lines, "\n")
}

// Error code to string mapping
var errorCodeNames = map[ErrorCode]string{
	CodeUnknown:            "Unknown",
	CodeInternal:           "Internal",
	CodeInvalidArgument:    "InvalidArgument",
	CodeNotImplemented:     "NotImplemented",
	CodeUnavailable:        "Unavailable",
	CodeTimeout:            "Timeout",
	CodeCanceled:           "Canceled",
	CodeAlreadyExists:      "AlreadyExists",
	CodeNotFound:           "NotFound",
	CodePermissionDenied:   "PermissionDenied",
	CodeResourceExhausted:  "ResourceExhausted",
	CodeFailedPrecondition: "FailedPrecondition",
	CodeAborted:            "Aborted",
	CodeOutOfRange:         "OutOfRange",
	CodeInvalidState:       "InvalidState",

	CodeNetworkError:      "NetworkError",
	CodeConnectionFailed:  "ConnectionFailed",
	CodeConnectionClosed:  "ConnectionClosed",
	CodeConnectionTimeout: "ConnectionTimeout",
	CodeConnectionReset:   "ConnectionReset",
	CodeHostUnreachable:   "HostUnreachable",
	CodeDNSError:          "DNSError",

	CodeAuthenticationFailed: "AuthenticationFailed",
	CodeUnauthenticated:      "Unauthenticated",
	CodeUnauthorized:         "Unauthorized",
	CodeSessionExpired:       "SessionExpired",
	CodeInvalidCredentials:   "InvalidCredentials",

	CodeTunnelError:        "TunnelError",
	CodeTunnelCloseFailed:  "TunnelCloseFailed",
	CodeTunnelReadFailed:   "TunnelReadFailed",
	CodeTunnelWriteFailed:  "TunnelWriteFailed",
	CodeTunnelConfigFailed: "TunnelConfigFailed",

	CodeConfigError:       "ConfigError",
	CodeConfigNotFound:    "ConfigNotFound",
	CodeConfigReadFailed:  "ConfigReadFailed",
	CodeConfigWriteFailed: "ConfigWriteFailed",
	CodeConfigParseFailed: "ConfigParseFailed",

	CodePlatformError:        "PlatformError",
	CodePlatformNotSupported: "PlatformNotSupported",
	CodePlatformInitFailed:   "PlatformInitFailed",
	CodePlatformIOError:      "PlatformIOError",

	CodeProtocolError:            "ProtocolError",
	CodeProtocolInvalid:          "ProtocolInvalid",
	CodeProtocolEncryptionFailed: "ProtocolEncryptionFailed",
	CodeProtocolDecryptionFailed: "ProtocolDecryptionFailed",
	CodePacketError:              "PacketError",
}

// Is reports whether err is an Error with the given code.
func Is(err error, code ErrorCode) bool {
	return GetCode(err) == code
}
