/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      api_errors.go
 *
 * DESCRIPTION :    API error codes and definitions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025  create
 ******************************************************************************/

package errors

// API error codes
const (
	// API errors (7000-7999)
	ErrInvalidRequest      ErrorCode = 7000 // Invalid request format or parameters
	ErrInvalidCredentials  ErrorCode = 7001 // Invalid username or password
	ErrInternal            ErrorCode = 7002 // Internal server error
	ErrNotFound            ErrorCode = 7003 // Resource not found
	ErrUnauthorized        ErrorCode = 7004 // Unauthorized access
	ErrForbidden           ErrorCode = 7005 // Forbidden access
	ErrTimeout             ErrorCode = 7006 // Request timeout
	ErrConflict            ErrorCode = 7007 // Resource conflict
	ErrTooManyRequests     ErrorCode = 7008 // Too many requests
	ErrBadGateway          ErrorCode = 7009 // Bad gateway
	ErrServiceUnavailable  ErrorCode = 7010 // Service unavailable
	ErrGatewayTimeout      ErrorCode = 7011 // Gateway timeout
	ErrNoServersAvailable  ErrorCode = 7012 // No servers available
	ErrAuthFailed          ErrorCode = 7013 // Authentication failed
	ErrConnectionFailed    ErrorCode = 7014 // Connection failed
	ErrDisconnectionFailed ErrorCode = 7015 // Disconnection failed
)

// Initialize API error code names
func init() {
	// API errors
	errorCodeNames[ErrInvalidRequest] = "InvalidRequest"
	errorCodeNames[ErrInvalidCredentials] = "InvalidCredentials"
	errorCodeNames[ErrInternal] = "Internal"
	errorCodeNames[ErrNotFound] = "NotFound"
	errorCodeNames[ErrUnauthorized] = "Unauthorized"
	errorCodeNames[ErrForbidden] = "Forbidden"
	errorCodeNames[ErrTimeout] = "Timeout"
	errorCodeNames[ErrConflict] = "Conflict"
	errorCodeNames[ErrTooManyRequests] = "TooManyRequests"
	errorCodeNames[ErrBadGateway] = "BadGateway"
	errorCodeNames[ErrServiceUnavailable] = "ServiceUnavailable"
	errorCodeNames[ErrGatewayTimeout] = "GatewayTimeout"
	errorCodeNames[ErrNoServersAvailable] = "NoServersAvailable"
	errorCodeNames[ErrAuthFailed] = "AuthFailed"
	errorCodeNames[ErrConnectionFailed] = "ConnectionFailed"
	errorCodeNames[ErrDisconnectionFailed] = "DisconnectionFailed"
}
