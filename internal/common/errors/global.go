/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      global.go
 *
 * DESCRIPTION :    全局错误处理工具
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025  create
 ******************************************************************************/

package errors

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"strings"
	"sync"

	"mobile/internal/common/logger"
)

var (
	// Global panic logger instance
	globalPanicLogger *PanicLogger

	// Initialization lock to ensure single initialization
	initLock sync.Once
)

/*****************************************************************************
 * NAME: InitPanicLogger
 *
 * DESCRIPTION:
 *     Initializes the global panic logger with configuration
 *
 * PARAMETERS:
 *     log        - Logger instance for panic logging
 *     reportPath - Directory path for panic report files
 *     appVersion - Application version string
 *
 * RETURNS:
 *     None
 *****************************************************************************/
func InitPanicLogger(log logger.Logger, reportPath string, appVersion string) {
	initLock.Do(func() {
		// 创建报告目录
		if reportPath != "" {
			os.MkdirAll(reportPath, 0755)
		}

		// 初始化全局panic日志记录器
		globalPanicLogger = NewPanicLogger(log, reportPath, appVersion)

		// 启用详细的堆栈跟踪
		EnableDetailedStackTrace()

		log.Info("Panic logger initialized",
			logger.String("report_path", reportPath),
			logger.String("app_version", appVersion),
		)
	})
}

/*****************************************************************************
 * NAME: LogPanic
 *
 * DESCRIPTION:
 *     Logs panic information using global panic logger or provided logger
 *
 * PARAMETERS:
 *     log - Logger instance for fallback logging
 *     r   - Panic recovery value
 *
 * RETURNS:
 *     None
 *****************************************************************************/
func LogPanic(log logger.Logger, r interface{}) {
	// 如果全局日志记录器已初始化，则使用它
	if globalPanicLogger != nil {
		globalPanicLogger.LogPanic(r)
		return
	}

	// 否则使用提供的日志记录器
	stack := debug.Stack()
	log.Error("Unhandled panic",
		logger.Any("panic", r),
		logger.String("stack", string(stack)),
	)
}

/*****************************************************************************
 * NAME: GetCallerInfo
 *
 * DESCRIPTION:
 *     Gets caller information from the call stack
 *
 * PARAMETERS:
 *     skip - Number of stack frames to skip
 *
 * RETURNS:
 *     file     - Source file name
 *     line     - Line number in source file
 *     funcName - Function name
 *****************************************************************************/
func GetCallerInfo(skip int) (file string, line int, funcName string) {
	pc, file, line, ok := runtime.Caller(skip + 1)
	if !ok {
		return "unknown", 0, "unknown"
	}

	funcName = runtime.FuncForPC(pc).Name()
	if lastSlash := strings.LastIndexByte(funcName, '/'); lastSlash >= 0 {
		funcName = funcName[lastSlash+1:]
	}

	return filepath.Base(file), line, funcName
}

/*****************************************************************************
 * NAME: GetCallStack
 *
 * DESCRIPTION:
 *     Gets formatted call stack information
 *
 * PARAMETERS:
 *     skip  - Number of stack frames to skip
 *     depth - Maximum depth of call stack to retrieve
 *
 * RETURNS:
 *     string - Formatted call stack string
 *****************************************************************************/
func GetCallStack(skip int, depth int) string {
	var sb strings.Builder

	for i := skip + 1; i < skip+depth+1; i++ {
		file, line, funcName := GetCallerInfo(i)
		if file == "unknown" {
			break
		}

		sb.WriteString(fmt.Sprintf("%s:%d %s\n", file, line, funcName))
	}

	return sb.String()
}

/*****************************************************************************
 * NAME: SetupPanicLogging
 *
 * DESCRIPTION:
 *     Sets up panic logging for main function with defer recovery
 *
 * PARAMETERS:
 *     log        - Logger instance for panic logging
 *     reportPath - Directory path for panic report files
 *     appVersion - Application version string
 *
 * RETURNS:
 *     None
 *****************************************************************************/
func SetupPanicLogging(log logger.Logger, reportPath string, appVersion string) {
	// 初始化全局panic日志记录器
	InitPanicLogger(log, reportPath, appVersion)

	// 设置defer函数来捕获main函数中的panic
	defer func() {
		if r := recover(); r != nil {
			LogPanic(log, r)
			// 不恢复执行，让程序正常退出
			os.Exit(1)
		}
	}()
}
