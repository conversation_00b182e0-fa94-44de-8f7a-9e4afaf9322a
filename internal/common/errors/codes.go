/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      codes.go
 *
 * DESCRIPTION :    Error codes and messages
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025  create
 ******************************************************************************/

package errors

/*****************************************************************************
 * NAME: ErrorCategory
 *
 * DESCRIPTION:
 *     Defines categories for error codes
 *****************************************************************************/
type ErrorCategory int

// Error categories
const (
	CategoryGeneral ErrorCategory = iota
	CategoryNetwork
	CategoryAuth
	CategoryTunnel
	CategoryConfig
	CategoryPlatform
	CategoryProtocol
)

// Error category to string mapping
var errorCategoryNames = map[ErrorCategory]string{
	CategoryGeneral:  "General",
	CategoryNetwork:  "Network",
	CategoryAuth:     "Authentication",
	CategoryTunnel:   "Tunnel",
	CategoryConfig:   "Configuration",
	CategoryPlatform: "Platform",
	CategoryProtocol: "Protocol",
}

/*****************************************************************************
 * NAME: String
 *
 * DESCRIPTION:
 *     Returns the string representation of the error category
 *
 * RETURNS:
 *     string - String representation of the error category
 *****************************************************************************/
func (c ErrorCategory) String() string {
	if name, ok := errorCategoryNames[c]; ok {
		return name
	}
	return "Unknown"
}

/*****************************************************************************
 * NAME: GetCategory
 *
 * DESCRIPTION:
 *     Gets the category of an error code
 *
 * PARAMETERS:
 *     code - Error code
 *
 * RETURNS:
 *     ErrorCategory - Error category
 *****************************************************************************/
func GetCategory(code ErrorCode) ErrorCategory {
	switch code {
	case CodeConnectionFailed, CodeNetworkError:
		return CategoryNetwork
	case CodeAuthenticationFailed, CodeUnauthenticated:
		return CategoryAuth
	case CodeTunnelError:
		return CategoryTunnel
	case CodeConfigError:
		return CategoryConfig
	case CodePlatformError:
		return CategoryPlatform
	case CodeProtocolError:
		return CategoryProtocol
	default:
		return CategoryGeneral
	}
}

// Detailed error codes for specific scenarios
const (
	// Network errors (1000-1999)
	CodeNetworkUnreachable      ErrorCode = 1000
	CodeNetworkTimeout          ErrorCode = 1001
	CodeNetworkDNSFailure       ErrorCode = 1002
	CodeNetworkConnectionReset  ErrorCode = 1003
	CodeNetworkConnectionClosed ErrorCode = 1004
	CodeNetworkProxyError       ErrorCode = 1005
	CodeNetworkTLSError         ErrorCode = 1006

	// Authentication errors (2000-2999)
	CodeAuthInvalidCredentials ErrorCode = 2000
	CodeAuthExpiredCredentials ErrorCode = 2001
	CodeAuthRateLimited        ErrorCode = 2002
	CodeAuthAccountLocked      ErrorCode = 2003
	CodeAuthTokenInvalid       ErrorCode = 2004
	CodeAuthTokenExpired       ErrorCode = 2005
	CodeAuthMissingCredentials ErrorCode = 2006

	// Tunnel errors (3000-3999)
	CodeTunnelInitFailed       ErrorCode = 3000
	CodeTunnelClosedUnexpected ErrorCode = 3001
	CodeTunnelPacketDropped    ErrorCode = 3002
	CodeTunnelDeviceError      ErrorCode = 3003
	CodeTunnelRouteError       ErrorCode = 3004
	CodeTunnelDNSError         ErrorCode = 3005
	CodeTunnelEncryptionError  ErrorCode = 3006

	// Configuration errors (4000-4999)
	CodeConfigInvalid          ErrorCode = 4000
	CodeConfigMissing          ErrorCode = 4001
	CodeConfigPermissionDenied ErrorCode = 4002
	CodeConfigReadError        ErrorCode = 4003
	CodeConfigWriteError       ErrorCode = 4004
	CodeConfigParseError       ErrorCode = 4005
	CodeConfigValidationError  ErrorCode = 4006

	// Platform errors (5000-5999)
	CodePlatformUnsupported      ErrorCode = 5000
	CodePlatformPermissionDenied ErrorCode = 5001
	CodePlatformDriverError      ErrorCode = 5002
	CodePlatformSystemError      ErrorCode = 5003
	CodePlatformResourceError    ErrorCode = 5004
	CodePlatformNetworkError     ErrorCode = 5005
	CodePlatformFirewallError    ErrorCode = 5006

	// Protocol errors (6000-6999)
	CodeProtocolVersionMismatch ErrorCode = 6000
	CodeProtocolInvalidFormat   ErrorCode = 6001
	CodeProtocolUnsupported     ErrorCode = 6002
	CodeProtocolHandshakeFailed ErrorCode = 6003
	CodeProtocolEncryptionError ErrorCode = 6004
	CodeProtocolDecryptionError ErrorCode = 6005
	CodeProtocolAuthError       ErrorCode = 6006
)

// Initialize detailed error code names
func init() {
	// Network errors
	errorCodeNames[CodeNetworkUnreachable] = "NetworkUnreachable"
	errorCodeNames[CodeNetworkTimeout] = "NetworkTimeout"
	errorCodeNames[CodeNetworkDNSFailure] = "NetworkDNSFailure"
	errorCodeNames[CodeNetworkConnectionReset] = "NetworkConnectionReset"
	errorCodeNames[CodeNetworkConnectionClosed] = "NetworkConnectionClosed"
	errorCodeNames[CodeNetworkProxyError] = "NetworkProxyError"
	errorCodeNames[CodeNetworkTLSError] = "NetworkTLSError"

	// Authentication errors
	errorCodeNames[CodeAuthInvalidCredentials] = "AuthInvalidCredentials"
	errorCodeNames[CodeAuthExpiredCredentials] = "AuthExpiredCredentials"
	errorCodeNames[CodeAuthRateLimited] = "AuthRateLimited"
	errorCodeNames[CodeAuthAccountLocked] = "AuthAccountLocked"
	errorCodeNames[CodeAuthTokenInvalid] = "AuthTokenInvalid"
	errorCodeNames[CodeAuthTokenExpired] = "AuthTokenExpired"
	errorCodeNames[CodeAuthMissingCredentials] = "AuthMissingCredentials"

	// Tunnel errors
	errorCodeNames[CodeTunnelInitFailed] = "TunnelInitFailed"
	errorCodeNames[CodeTunnelClosedUnexpected] = "TunnelClosedUnexpected"
	errorCodeNames[CodeTunnelPacketDropped] = "TunnelPacketDropped"
	errorCodeNames[CodeTunnelDeviceError] = "TunnelDeviceError"
	errorCodeNames[CodeTunnelRouteError] = "TunnelRouteError"
	errorCodeNames[CodeTunnelDNSError] = "TunnelDNSError"
	errorCodeNames[CodeTunnelEncryptionError] = "TunnelEncryptionError"

	// Configuration errors
	errorCodeNames[CodeConfigInvalid] = "ConfigInvalid"
	errorCodeNames[CodeConfigMissing] = "ConfigMissing"
	errorCodeNames[CodeConfigPermissionDenied] = "ConfigPermissionDenied"
	errorCodeNames[CodeConfigReadError] = "ConfigReadError"
	errorCodeNames[CodeConfigWriteError] = "ConfigWriteError"
	errorCodeNames[CodeConfigParseError] = "ConfigParseError"
	errorCodeNames[CodeConfigValidationError] = "ConfigValidationError"

	// Platform errors
	errorCodeNames[CodePlatformUnsupported] = "PlatformUnsupported"
	errorCodeNames[CodePlatformPermissionDenied] = "PlatformPermissionDenied"
	errorCodeNames[CodePlatformDriverError] = "PlatformDriverError"
	errorCodeNames[CodePlatformSystemError] = "PlatformSystemError"
	errorCodeNames[CodePlatformResourceError] = "PlatformResourceError"
	errorCodeNames[CodePlatformNetworkError] = "PlatformNetworkError"
	errorCodeNames[CodePlatformFirewallError] = "PlatformFirewallError"

	// Protocol errors
	errorCodeNames[CodeProtocolVersionMismatch] = "ProtocolVersionMismatch"
	errorCodeNames[CodeProtocolInvalidFormat] = "ProtocolInvalidFormat"
	errorCodeNames[CodeProtocolUnsupported] = "ProtocolUnsupported"
	errorCodeNames[CodeProtocolHandshakeFailed] = "ProtocolHandshakeFailed"
	errorCodeNames[CodeProtocolEncryptionError] = "ProtocolEncryptionError"
	errorCodeNames[CodeProtocolDecryptionError] = "ProtocolDecryptionError"
	errorCodeNames[CodeProtocolAuthError] = "ProtocolAuthError"
}
