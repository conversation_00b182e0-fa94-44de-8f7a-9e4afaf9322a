/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      doc.go
 *
 * DESCRIPTION :    Error handling package documentation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025  create
 ******************************************************************************/

/*
Package errors provides a comprehensive error handling system for the VPN client.

It includes:
- Error codes and categories for different types of errors
- Error wrapping with context information
- Stack trace capture
- Internationalization support for error messages
- Integration with the logger package

Basic Usage:

	// Create a new error with a code
	err := errors.NewWithCode(errors.CodeNetworkTimeout, "Connection timed out")

	// Wrap an existing error with additional context
	err = errors.Wrap(err, "Failed to connect to server")

	// Add context information
	err = err.WithContext("server", "example.com").WithContext("port", 443)

	// Check error code
	if errors.IsCode(err, errors.CodeNetworkTimeout) {
	    // Handle timeout error
	}

	// Get localized error message
	message := errors.GetLocalizedMessage(err, errors.LangChinese)

	// Log the error
	errors.LogError(logger, err, "Connection failed")

Error Codes:

The package defines a set of error codes organized by category:
- General errors (CodeUnknown, CodeInternal, etc.)
- Network errors (CodeNetworkTimeout, CodeNetworkDNSFailure, etc.)
- Authentication errors (CodeAuthInvalidCredentials, CodeAuthTokenExpired, etc.)
- Tunnel errors (CodeTunnelInitFailed, CodeTunnelDeviceError, etc.)
- Configuration errors (CodeConfigInvalid, CodeConfigParseError, etc.)
- Platform errors (CodePlatformUnsupported, CodePlatformDriverError, etc.)
- Protocol errors (CodeProtocolVersionMismatch, CodeProtocolHandshakeFailed, etc.)

Internationalization:

The package supports internationalized error messages in multiple languages:
- English (LangEnglish)
- Chinese (LangChinese)

You can register additional message templates for new languages or override existing ones:

	errors.RegisterMessageTemplates(errors.LangJapanese, map[errors.ErrorCode]string{
	    errors.CodeNetworkTimeout: "接続がタイムアウトしました",
	    // ...
	})

	errors.SetDefaultLanguage(errors.LangJapanese)

Logger Integration:

The package integrates with the logger package to provide structured error logging:

	// Log an error
	errors.LogError(logger, err, "Operation failed")

	// Log an error with a specific level
	errors.LogErrorWithLevel(logger, logger.LevelWarn, err, "Operation failed")

	// Log an error with a formatted message
	errors.LogErrorf(logger, err, "Failed to connect to %s", server)
*/
package errors
