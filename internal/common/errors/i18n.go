/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      i18n.go
 *
 * DESCRIPTION :    Internationalization support for error messages
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025  create
 ******************************************************************************/

package errors

import (
	"fmt"
	"sync"
)

// Language code type
type Language string

// Supported languages
const (
	LangEnglish Language = "en"
	LangChinese Language = "zh"
)

// Default language
var defaultLanguage = LangEnglish

// Message templates by language and error code
var (
	messageTemplates = make(map[Language]map[ErrorCode]string)
	templatesMutex   sync.RWMutex
)

// Initialize default message templates
func init() {
	// English messages
	RegisterMessageTemplates(LangEnglish, map[ErrorCode]string{
		// General errors
		CodeUnknown:            "Unknown error",
		CodeInternal:           "Internal error",
		CodeInvalidArgument:    "Invalid argument",
		CodeNotFound:           "Resource not found",
		CodeAlreadyExists:      "Resource already exists",
		CodePermissionDenied:   "Permission denied",
		CodeUnauthenticated:    "Unauthenticated",
		CodeResourceExhausted:  "Resource exhausted",
		CodeFailedPrecondition: "Failed precondition",
		CodeAborted:            "Operation aborted",
		CodeOutOfRange:         "Value out of range",
		CodeNotImplemented:     "Feature not implemented",
		CodeUnavailable:        "Service unavailable",

		// Network errors
		CodeConnectionFailed:        "Connection failed",
		CodeNetworkError:            "Network error",
		CodeNetworkUnreachable:      "Network is unreachable",
		CodeNetworkTimeout:          "Network operation timed out",
		CodeNetworkDNSFailure:       "DNS resolution failed",
		CodeNetworkConnectionReset:  "Connection reset by peer",
		CodeNetworkConnectionClosed: "Connection closed unexpectedly",
		CodeNetworkProxyError:       "Proxy server error",
		CodeNetworkTLSError:         "TLS handshake failed",

		// Authentication errors
		CodeAuthenticationFailed:   "Authentication failed",
		CodeAuthInvalidCredentials: "Invalid username or password",
		CodeAuthExpiredCredentials: "Credentials have expired",
		CodeAuthRateLimited:        "Too many authentication attempts",
		CodeAuthAccountLocked:      "Account has been locked",
		CodeAuthTokenInvalid:       "Invalid authentication token",
		CodeAuthTokenExpired:       "Authentication token has expired",
		CodeAuthMissingCredentials: "Missing credentials",

		// Tunnel errors
		CodeTunnelError:            "VPN tunnel error",
		CodeTunnelInitFailed:       "Failed to initialize VPN tunnel",
		CodeTunnelClosedUnexpected: "VPN tunnel closed unexpectedly",
		CodeTunnelPacketDropped:    "Packet dropped in VPN tunnel",
		CodeTunnelDeviceError:      "VPN tunnel device error",
		CodeTunnelRouteError:       "Failed to configure VPN routes",
		CodeTunnelDNSError:         "Failed to configure VPN DNS",
		CodeTunnelEncryptionError:  "VPN tunnel encryption error",

		// Configuration errors
		CodeConfigError:            "Configuration error",
		CodeConfigInvalid:          "Invalid configuration",
		CodeConfigMissing:          "Missing configuration",
		CodeConfigPermissionDenied: "Permission denied for configuration file",
		CodeConfigReadError:        "Failed to read configuration",
		CodeConfigWriteError:       "Failed to write configuration",
		CodeConfigParseError:       "Failed to parse configuration",
		CodeConfigValidationError:  "Configuration validation failed",

		// Platform errors
		CodePlatformError:            "Platform error",
		CodePlatformUnsupported:      "Platform not supported",
		CodePlatformPermissionDenied: "Platform permission denied",
		CodePlatformDriverError:      "Platform driver error",
		CodePlatformSystemError:      "Platform system error",
		CodePlatformResourceError:    "Platform resource error",
		CodePlatformNetworkError:     "Platform network error",
		CodePlatformFirewallError:    "Platform firewall error",

		// Protocol errors
		CodeProtocolError:           "Protocol error",
		CodeProtocolVersionMismatch: "Protocol version mismatch",
		CodeProtocolInvalidFormat:   "Invalid protocol format",
		CodeProtocolUnsupported:     "Protocol not supported",
		CodeProtocolHandshakeFailed: "Protocol handshake failed",
		CodeProtocolEncryptionError: "Protocol encryption error",
		CodeProtocolDecryptionError: "Protocol decryption error",
		CodeProtocolAuthError:       "Protocol authentication error",

		// API errors
		ErrInvalidRequest:     "Invalid request format or parameters",
		ErrInvalidCredentials: "Invalid username or password",
		ErrInternal:           "Internal server error",
		ErrNotFound:           "Resource not found",
		ErrUnauthorized:       "Unauthorized access",
		ErrForbidden:          "Forbidden access",
		ErrTimeout:            "Request timeout",
		ErrConflict:           "Resource conflict",
		ErrTooManyRequests:    "Too many requests",
		ErrBadGateway:         "Bad gateway",
		ErrServiceUnavailable: "Service unavailable",
		ErrGatewayTimeout:     "Gateway timeout",
	})

	// Chinese messages
	RegisterMessageTemplates(LangChinese, map[ErrorCode]string{
		// General errors
		CodeUnknown:            "未知错误",
		CodeInternal:           "内部错误",
		CodeInvalidArgument:    "无效参数",
		CodeNotFound:           "资源未找到",
		CodeAlreadyExists:      "资源已存在",
		CodePermissionDenied:   "权限被拒绝",
		CodeUnauthenticated:    "未认证",
		CodeResourceExhausted:  "资源耗尽",
		CodeFailedPrecondition: "前置条件失败",
		CodeAborted:            "操作已中止",
		CodeOutOfRange:         "值超出范围",
		CodeNotImplemented:     "功能未实现",
		CodeUnavailable:        "服务不可用",

		// Network errors
		CodeConnectionFailed:        "连接失败",
		CodeNetworkError:            "网络错误",
		CodeNetworkUnreachable:      "网络不可达",
		CodeNetworkTimeout:          "网络操作超时",
		CodeNetworkDNSFailure:       "DNS解析失败",
		CodeNetworkConnectionReset:  "连接被对方重置",
		CodeNetworkConnectionClosed: "连接意外关闭",
		CodeNetworkProxyError:       "代理服务器错误",
		CodeNetworkTLSError:         "TLS握手失败",

		// Authentication errors
		CodeAuthenticationFailed:   "认证失败",
		CodeAuthInvalidCredentials: "用户名或密码无效",
		CodeAuthExpiredCredentials: "凭证已过期",
		CodeAuthRateLimited:        "认证尝试次数过多",
		CodeAuthAccountLocked:      "账户已被锁定",
		CodeAuthTokenInvalid:       "认证令牌无效",
		CodeAuthTokenExpired:       "认证令牌已过期",
		CodeAuthMissingCredentials: "缺少凭证",

		// Tunnel errors
		CodeTunnelError:            "VPN隧道错误",
		CodeTunnelInitFailed:       "VPN隧道初始化失败",
		CodeTunnelClosedUnexpected: "VPN隧道意外关闭",
		CodeTunnelPacketDropped:    "VPN隧道数据包丢失",
		CodeTunnelDeviceError:      "VPN隧道设备错误",
		CodeTunnelRouteError:       "VPN路由配置失败",
		CodeTunnelDNSError:         "VPN DNS配置失败",
		CodeTunnelEncryptionError:  "VPN隧道加密错误",

		// Configuration errors
		CodeConfigError:            "配置错误",
		CodeConfigInvalid:          "无效配置",
		CodeConfigMissing:          "缺少配置",
		CodeConfigPermissionDenied: "配置文件权限被拒绝",
		CodeConfigReadError:        "读取配置失败",
		CodeConfigWriteError:       "写入配置失败",
		CodeConfigParseError:       "解析配置失败",
		CodeConfigValidationError:  "配置验证失败",

		// Platform errors
		CodePlatformError:            "平台错误",
		CodePlatformUnsupported:      "平台不支持",
		CodePlatformPermissionDenied: "平台权限被拒绝",
		CodePlatformDriverError:      "平台驱动错误",
		CodePlatformSystemError:      "平台系统错误",
		CodePlatformResourceError:    "平台资源错误",
		CodePlatformNetworkError:     "平台网络错误",
		CodePlatformFirewallError:    "平台防火墙错误",

		// Protocol errors
		CodeProtocolError:           "协议错误",
		CodeProtocolVersionMismatch: "协议版本不匹配",
		CodeProtocolInvalidFormat:   "无效协议格式",
		CodeProtocolUnsupported:     "协议不支持",
		CodeProtocolHandshakeFailed: "协议握手失败",
		CodeProtocolEncryptionError: "协议加密错误",
		CodeProtocolDecryptionError: "协议解密错误",
		CodeProtocolAuthError:       "协议认证错误",

		// API errors
		ErrInvalidRequest:     "无效的请求格式或参数",
		ErrInvalidCredentials: "无效的用户名或密码",
		ErrInternal:           "内部服务器错误",
		ErrNotFound:           "资源未找到",
		ErrUnauthorized:       "未授权访问",
		ErrForbidden:          "禁止访问",
		ErrTimeout:            "请求超时",
		ErrConflict:           "资源冲突",
		ErrTooManyRequests:    "请求过多",
		ErrBadGateway:         "错误的网关",
		ErrServiceUnavailable: "服务不可用",
		ErrGatewayTimeout:     "网关超时",
	})
}

/*****************************************************************************
 * NAME: RegisterMessageTemplates
 *
 * DESCRIPTION:
 *     Registers message templates for a language
 *
 * PARAMETERS:
 *     lang      - Language code
 *     templates - Map of error codes to message templates
 *****************************************************************************/
func RegisterMessageTemplates(lang Language, templates map[ErrorCode]string) {
	//templatesMutex.Lock()
	//defer templatesMutex.Unlock()

	if _, ok := messageTemplates[lang]; !ok {
		messageTemplates[lang] = make(map[ErrorCode]string)
	}

	for code, template := range templates {
		messageTemplates[lang][code] = template
	}
}

/*****************************************************************************
 * NAME: SetDefaultLanguage
 *
 * DESCRIPTION:
 *     Sets the default language for error messages
 *
 * PARAMETERS:
 *     lang - Language code
 *****************************************************************************/
func SetDefaultLanguage(lang Language) {
	//templatesMutex.Lock()
	//defer templatesMutex.Unlock()
	defaultLanguage = lang
}

/*****************************************************************************
 * NAME: GetDefaultLanguage
 *
 * DESCRIPTION:
 *     Gets the default language for error messages
 *
 * RETURNS:
 *     Language - Default language code
 *****************************************************************************/
func GetDefaultLanguage() Language {
	//templatesMutex.RLock()
	//defer templatesMutex.RUnlock()
	return defaultLanguage
}

/*****************************************************************************
 * NAME: GetMessageTemplate
 *
 * DESCRIPTION:
 *     Gets the message template for an error code in a specific language
 *
 * PARAMETERS:
 *     code - Error code
 *     lang - Language code
 *
 * RETURNS:
 *     string - Message template
 *****************************************************************************/
func GetMessageTemplate(code ErrorCode, lang Language) string {
	//templatesMutex.RLock()
	//defer templatesMutex.RUnlock()

	// Try to get the template for the specified language
	if templates, ok := messageTemplates[lang]; ok {
		if template, ok := templates[code]; ok {
			return template
		}
	}

	// Fall back to English if the template is not available in the specified language
	if lang != LangEnglish {
		if templates, ok := messageTemplates[LangEnglish]; ok {
			if template, ok := templates[code]; ok {
				return template
			}
		}
	}

	// Fall back to a generic message if no template is available
	return fmt.Sprintf("Error code %d", code)
}

/*****************************************************************************
 * NAME: GetLocalizedMessage
 *
 * DESCRIPTION:
 *     Gets a localized message for an error
 *
 * PARAMETERS:
 *     err  - Error
 *     lang - Language code (optional, uses default if not specified)
 *
 * RETURNS:
 *     string - Localized message
 *****************************************************************************/
func GetLocalizedMessage(err error, lang ...Language) string {
	if err == nil {
		return ""
	}

	// Use the specified language or the default language
	selectedLang := defaultLanguage
	if len(lang) > 0 {
		selectedLang = lang[0]
	}

	// Get the error code
	code := GetCode(err)

	// Get the message template
	template := GetMessageTemplate(code, selectedLang)

	// Return the message
	return template
}
