/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      application.go
 *
 * DESCRIPTION :    Application lifecycle manager with dependency injection
 *
 * AUTHOR :         wei
 *
 * HISTORY :        2025-05-22  Initial implementation
 ******************************************************************************/

package app

import (
	"context"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"mobile/internal/common/container"
	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
	"mobile/internal/interfaces"
)

/*****************************************************************************
 * NAME: Application
 *
 * DESCRIPTION:
 *     Main application manager that handles dependency injection,
 *     component lifecycle, and graceful shutdown
 *
 * FIELDS:
 *     container    - Dependency injection container
 *     logger       - Application logger
 *     components   - List of managed components
 *     ctx          - Application context
 *     cancel       - Context cancel function
 *     shutdownCh   - Shutdown signal channel
 *     wg           - Wait group for graceful shutdown
 *     config       - Application configuration
 *****************************************************************************/
type Application struct {
	container  *container.Container
	logger     logger.Logger
	components []interfaces.Component
	ctx        context.Context
	cancel     context.CancelFunc
	shutdownCh chan os.Signal
	wg         sync.WaitGroup
	config     *Config
	started    bool
	mutex      sync.RWMutex
}

/*****************************************************************************
 * NAME: Config
 *
 * DESCRIPTION:
 *     Application configuration
 *****************************************************************************/
type Config struct {
	Name            string        `yaml:"name"`
	Version         string        `yaml:"version"`
	ShutdownTimeout time.Duration `yaml:"shutdown_timeout"`
	LogLevel        string        `yaml:"log_level"`
	LogFile         string        `yaml:"log_file"`
}

/*****************************************************************************
 * NAME: NewApplication
 *
 * DESCRIPTION:
 *     Creates a new application instance
 *
 * PARAMETERS:
 *     config - Application configuration
 *     logger - Logger instance
 *
 * RETURNS:
 *     *Application - New application instance
 *****************************************************************************/
func NewApplication(config *Config, logger logger.Logger) *Application {
	ctx, cancel := context.WithCancel(context.Background())

	app := &Application{
		container:  container.NewContainer(logger),
		logger:     logger.WithModule("app"),
		components: make([]interfaces.Component, 0),
		ctx:        ctx,
		cancel:     cancel,
		shutdownCh: make(chan os.Signal, 1),
		config:     config,
	}

	// Register for shutdown signals
	signal.Notify(app.shutdownCh, os.Interrupt, syscall.SIGTERM)

	return app
}

/*****************************************************************************
 * NAME: RegisterComponent
 *
 * DESCRIPTION:
 *     Registers a component for lifecycle management
 *
 * PARAMETERS:
 *     component - Component to register
 *
 * RETURNS:
 *     error - Registration error if any
 *****************************************************************************/
func (a *Application) RegisterComponent(component interfaces.Component) error {
	// 	a.mutex.Lock()
	// 	defer a.mutex.Unlock()

	if a.started {
		return errors.NewWithCode(errors.CodeInvalidState,
			"cannot register component after application has started")
	}

	a.components = append(a.components, component)
	a.logger.Debug("Registered component", logger.String("name", component.Name()))

	return nil
}

/*****************************************************************************
 * NAME: RegisterService
 *
 * DESCRIPTION:
 *     Registers a service in the dependency injection container
 *
 * PARAMETERS:
 *     serviceInterface - Interface type
 *     implementation   - Implementation type
 *     singleton        - Whether to register as singleton
 *
 * RETURNS:
 *     error - Registration error if any
 *****************************************************************************/
func (a *Application) RegisterService(serviceInterface, implementation interface{}, singleton bool) error {
	if singleton {
		return a.container.RegisterSingleton(serviceInterface, implementation)
	}
	return a.container.RegisterTransient(serviceInterface, implementation)
}

/*****************************************************************************
 * NAME: RegisterFactory
 *
 * DESCRIPTION:
 *     Registers a service factory in the dependency injection container
 *
 * PARAMETERS:
 *     serviceInterface - Interface type
 *     factory         - Factory function
 *     singleton       - Whether to register as singleton
 *
 * RETURNS:
 *     error - Registration error if any
 *****************************************************************************/
func (a *Application) RegisterFactory(serviceInterface interface{},
	factory container.FactoryFunc, singleton bool) error {

	lifetime := container.Transient
	if singleton {
		lifetime = container.Singleton
	}

	return a.container.RegisterFactory(serviceInterface, factory, lifetime)
}

/*****************************************************************************
 * NAME: GetService
 *
 * DESCRIPTION:
 *     Resolves a service from the dependency injection container
 *
 * PARAMETERS:
 *     serviceInterface - Interface to resolve
 *
 * RETURNS:
 *     interface{} - Service instance
 *     error      - Resolution error if any
 *****************************************************************************/
func (a *Application) GetService(serviceInterface interface{}) (interface{}, error) {
	return a.container.ResolveByInterface(a.ctx, serviceInterface)
}

/*****************************************************************************
 * NAME: Start
 *
 * DESCRIPTION:
 *     Starts the application and all registered components
 *
 * RETURNS:
 *     error - Start error if any
 *****************************************************************************/
func (a *Application) Start() error {
	// 	a.mutex.Lock()
	// 	defer a.mutex.Unlock()

	if a.started {
		return errors.NewWithCode(errors.CodeInvalidState, "application already started")
	}

	a.logger.Info("Starting application",
		logger.String("name", a.config.Name),
		logger.String("version", a.config.Version))

	// Initialize all components
	for _, component := range a.components {
		a.logger.Debug("Initializing component", logger.String("name", component.Name()))

		if err := component.Initialize(a.ctx); err != nil {
			return errors.WrapWithCode(errors.CodeInitializationError, err,
				"failed to initialize component %s", component.Name()).
				WithComponent("app").WithOperation("start")
		}
	}

	// Start all components
	for _, component := range a.components {
		a.logger.Debug("Starting component", logger.String("name", component.Name()))

		if err := component.Start(a.ctx); err != nil {
			return errors.WrapWithCode(errors.CodeServiceStartFailed, err,
				"failed to start component %s", component.Name()).
				WithComponent("app").WithOperation("start")
		}
	}

	a.started = true
	a.logger.Info("Application started successfully")

	return nil
}

/*****************************************************************************
 * NAME: Run
 *
 * DESCRIPTION:
 *     Runs the application and waits for shutdown signal
 *
 * RETURNS:
 *     error - Runtime error if any
 *****************************************************************************/
func (a *Application) Run() error {
	if err := a.Start(); err != nil {
		return err
	}

	// Start shutdown handler
	a.wg.Add(1)
	go a.handleShutdown()

	a.logger.Info("Application is running, waiting for shutdown signal...")

	// Wait for shutdown
	a.wg.Wait()

	return nil
}

/*****************************************************************************
 * NAME: Stop
 *
 * DESCRIPTION:
 *     Stops the application and all components gracefully
 *
 * RETURNS:
 *     error - Stop error if any
 *****************************************************************************/
func (a *Application) Stop() error {
	// 	a.mutex.Lock()
	// 	defer a.mutex.Unlock()

	if !a.started {
		return nil
	}

	a.logger.Info("Stopping application...")

	// Create shutdown context with timeout
	shutdownCtx, shutdownCancel := context.WithTimeout(
		context.Background(), a.config.ShutdownTimeout)
	defer shutdownCancel()

	// Stop components in reverse order
	for i := len(a.components) - 1; i >= 0; i-- {
		component := a.components[i]
		a.logger.Debug("Stopping component", logger.String("name", component.Name()))

		if err := component.Stop(shutdownCtx); err != nil {
			a.logger.Error("Failed to stop component",
				logger.String("name", component.Name()),
				logger.ErrorField(err))
		}
	}

	// Cancel application context
	a.cancel()

	// Shutdown container
	if err := a.container.Shutdown(shutdownCtx); err != nil {
		a.logger.Error("Failed to shutdown container", logger.ErrorField(err))
	}

	a.started = false
	a.logger.Info("Application stopped")

	return nil
}

/*****************************************************************************
 * NAME: handleShutdown
 *
 * DESCRIPTION:
 *     Handles shutdown signals gracefully
 *****************************************************************************/
func (a *Application) handleShutdown() {
	defer a.wg.Done()

	select {
	case sig := <-a.shutdownCh:
		a.logger.Info("Received shutdown signal", logger.String("signal", sig.String()))
		if err := a.Stop(); err != nil {
			a.logger.Error("Error during shutdown", logger.ErrorField(err))
		}
	case <-a.ctx.Done():
		a.logger.Info("Application context cancelled")
	}
}

/*****************************************************************************
 * NAME: GetContext
 *
 * DESCRIPTION:
 *     Returns the application context
 *
 * RETURNS:
 *     context.Context - Application context
 *****************************************************************************/
func (a *Application) GetContext() context.Context {
	return a.ctx
}

/*****************************************************************************
 * NAME: IsStarted
 *
 * DESCRIPTION:
 *     Returns whether the application is started
 *
 * RETURNS:
 *     bool - True if started
 *****************************************************************************/
func (a *Application) IsStarted() bool {
	// 	a.mutex.RLock()
	// 	defer a.mutex.RUnlock()
	return a.started
}
