/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      connection_pool.go
 *
 * DESCRIPTION :    Network connection pool for efficient connection reuse
 *
 * AUTHOR :         wei
 *
 * HISTORY :        2025-05-22  Initial implementation
 ******************************************************************************/

package netpool

import (
	"context"
	"net"
	"sync"
	"sync/atomic"
	"time"

	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: ConnectionPool
 *
 * DESCRIPTION:
 *     Pool for managing network connections with automatic cleanup
 *
 * FIELDS:
 *     connections  - Available connections
 *     inUse        - Connections currently in use
 *     factory      - Connection factory function
 *     config       - Pool configuration
 *     logger       - Logger instance
 *     mutex        - Mutex for thread-safe operations
 *     closed       - Whether the pool is closed
 *     metrics      - Pool metrics
 *****************************************************************************/
type ConnectionPool struct {
	connections chan *PooledConnection
	inUse       map[*PooledConnection]time.Time
	factory     ConnectionFactory
	config      *PoolConfig
	logger      logger.Logger
	mutex       sync.RWMutex
	closed      bool
	metrics     *PoolMetrics
}

/*****************************************************************************
 * NAME: PooledConnection
 *
 * DESCRIPTION:
 *     Wrapper for pooled connections with metadata
 *****************************************************************************/
type PooledConnection struct {
	net.Conn
	pool      *ConnectionPool
	createdAt time.Time
	lastUsed  time.Time
	useCount  uint64
	isHealthy bool
}

/*****************************************************************************
 * NAME: ConnectionFactory
 *
 * DESCRIPTION:
 *     Function signature for creating new connections
 *****************************************************************************/
type ConnectionFactory func(ctx context.Context) (net.Conn, error)

/*****************************************************************************
 * NAME: PoolConfig
 *
 * DESCRIPTION:
 *     Configuration for connection pool
 *****************************************************************************/
type PoolConfig struct {
	MaxConnections      int           `yaml:"max_connections"`
	MinConnections      int           `yaml:"min_connections"`
	MaxIdleTime         time.Duration `yaml:"max_idle_time"`
	MaxLifetime         time.Duration `yaml:"max_lifetime"`
	HealthCheckInterval time.Duration `yaml:"health_check_interval"`
	ConnectTimeout      time.Duration `yaml:"connect_timeout"`
	ReadTimeout         time.Duration `yaml:"read_timeout"`
	WriteTimeout        time.Duration `yaml:"write_timeout"`
}

/*****************************************************************************
 * NAME: PoolMetrics
 *
 * DESCRIPTION:
 *     Metrics for connection pool
 *****************************************************************************/
type PoolMetrics struct {
	TotalConnections   int32     `json:"total_connections"`
	ActiveConnections  int32     `json:"active_connections"`
	IdleConnections    int32     `json:"idle_connections"`
	ConnectionsCreated uint64    `json:"connections_created"`
	ConnectionsClosed  uint64    `json:"connections_closed"`
	ConnectionsReused  uint64    `json:"connections_reused"`
	HealthChecksPassed uint64    `json:"health_checks_passed"`
	HealthChecksFailed uint64    `json:"health_checks_failed"`
	LastHealthCheck    time.Time `json:"last_health_check"`
}

/*****************************************************************************
 * NAME: NewConnectionPool
 *
 * DESCRIPTION:
 *     Creates a new connection pool
 *
 * PARAMETERS:
 *     factory - Connection factory function
 *     config  - Pool configuration
 *     logger  - Logger instance
 *
 * RETURNS:
 *     *ConnectionPool - New connection pool
 *****************************************************************************/
func NewConnectionPool(factory ConnectionFactory, config *PoolConfig, logger logger.Logger) *ConnectionPool {
	pool := &ConnectionPool{
		connections: make(chan *PooledConnection, config.MaxConnections),
		inUse:       make(map[*PooledConnection]time.Time),
		factory:     factory,
		config:      config,
		logger:      logger.WithModule("netpool"),
		metrics:     &PoolMetrics{},
	}

	// Start background maintenance
	go pool.maintenance()

	return pool
}

/*****************************************************************************
 * NAME: Get
 *
 * DESCRIPTION:
 *     Gets a connection from the pool
 *
 * PARAMETERS:
 *     ctx - Context for the operation
 *
 * RETURNS:
 *     *PooledConnection - Pooled connection
 *     error            - Error if any
 *****************************************************************************/
func (p *ConnectionPool) Get(ctx context.Context) (*PooledConnection, error) {
// 	p.mutex.Lock()
// 	defer p.mutex.Unlock()

	if p.closed {
		return nil, errors.NewWithCode(errors.CodeInvalidState,
			"connection pool is closed").(*errors.DetailedError).
			WithComponent("netpool").WithOperation("get")
	}

	// Try to get an existing connection
	select {
	case conn := <-p.connections:
		if p.isConnectionHealthy(conn) {
			conn.lastUsed = time.Now()
			atomic.AddUint64(&conn.useCount, 1)
			p.inUse[conn] = time.Now()
			atomic.AddUint64(&p.metrics.ConnectionsReused, 1)
			atomic.AddInt32(&p.metrics.ActiveConnections, 1)
			atomic.AddInt32(&p.metrics.IdleConnections, -1)

			p.logger.Debug("Reused connection from pool",
				logger.Uint64("use_count", conn.useCount))

			return conn, nil
		} else {
			// Connection is not healthy, close it
			conn.Conn.Close()
			atomic.AddUint64(&p.metrics.ConnectionsClosed, 1)
			atomic.AddInt32(&p.metrics.TotalConnections, -1)
		}
	default:
		// No connections available
	}

	// Create new connection if under limit
	if atomic.LoadInt32(&p.metrics.TotalConnections) >= int32(p.config.MaxConnections) {
		return nil, errors.NewWithCode(errors.CodeResourceExhausted,
			"connection pool limit reached").(*errors.DetailedError).
			WithComponent("netpool").WithOperation("get")
	}

	// Create new connection
	conn, err := p.createConnection(ctx)
	if err != nil {
		return nil, errors.WrapWithCode(errors.CodeConnectionFailed, err,
			"failed to create new connection").(*errors.DetailedError).
			WithComponent("netpool").WithOperation("get")
	}

	return conn, nil
}

/*****************************************************************************
 * NAME: Put
 *
 * DESCRIPTION:
 *     Returns a connection to the pool
 *
 * PARAMETERS:
 *     conn - Connection to return
 *****************************************************************************/
func (p *ConnectionPool) Put(conn *PooledConnection) {
	if conn == nil {
		return
	}

// 	p.mutex.Lock()
// 	defer p.mutex.Unlock()

	if p.closed {
		conn.Conn.Close()
		return
	}

	// Remove from in-use map
	delete(p.inUse, conn)
	atomic.AddInt32(&p.metrics.ActiveConnections, -1)

	// Check if connection is still healthy and not expired
	if p.isConnectionHealthy(conn) && p.isConnectionValid(conn) {
		select {
		case p.connections <- conn:
			atomic.AddInt32(&p.metrics.IdleConnections, 1)
			p.logger.Debug("Returned connection to pool")
		default:
			// Pool is full, close the connection
			conn.Conn.Close()
			atomic.AddUint64(&p.metrics.ConnectionsClosed, 1)
			atomic.AddInt32(&p.metrics.TotalConnections, -1)
		}
	} else {
		// Connection is not healthy or expired, close it
		conn.Conn.Close()
		atomic.AddUint64(&p.metrics.ConnectionsClosed, 1)
		atomic.AddInt32(&p.metrics.TotalConnections, -1)
	}
}

/*****************************************************************************
 * NAME: Close
 *
 * DESCRIPTION:
 *     Closes the connection pool and all connections
 *
 * RETURNS:
 *     error - Close error if any
 *****************************************************************************/
func (p *ConnectionPool) Close() error {
// 	p.mutex.Lock()
// 	defer p.mutex.Unlock()

	if p.closed {
		return nil
	}

	p.closed = true
	p.logger.Info("Closing connection pool")

	// Close all idle connections
	close(p.connections)
	for conn := range p.connections {
		conn.Conn.Close()
		atomic.AddUint64(&p.metrics.ConnectionsClosed, 1)
	}

	// Close all in-use connections
	for conn := range p.inUse {
		conn.Conn.Close()
		atomic.AddUint64(&p.metrics.ConnectionsClosed, 1)
	}

	atomic.StoreInt32(&p.metrics.TotalConnections, 0)
	atomic.StoreInt32(&p.metrics.ActiveConnections, 0)
	atomic.StoreInt32(&p.metrics.IdleConnections, 0)

	p.logger.Info("Connection pool closed")
	return nil
}

/*****************************************************************************
 * NAME: GetMetrics
 *
 * DESCRIPTION:
 *     Gets pool metrics
 *
 * RETURNS:
 *     *PoolMetrics - Pool metrics
 *****************************************************************************/
func (p *ConnectionPool) GetMetrics() *PoolMetrics {
// 	p.mutex.RLock()
// 	defer p.mutex.RUnlock()

	// Create a copy to avoid race conditions
	metrics := *p.metrics
	return &metrics
}

/*****************************************************************************
 * NAME: createConnection
 *
 * DESCRIPTION:
 *     Creates a new pooled connection
 *****************************************************************************/
func (p *ConnectionPool) createConnection(ctx context.Context) (*PooledConnection, error) {
	// Create connection with timeout
	connectCtx, cancel := context.WithTimeout(ctx, p.config.ConnectTimeout)
	defer cancel()

	conn, err := p.factory(connectCtx)
	if err != nil {
		return nil, err
	}

	pooledConn := &PooledConnection{
		Conn:      conn,
		pool:      p,
		createdAt: time.Now(),
		lastUsed:  time.Now(),
		useCount:  1,
		isHealthy: true,
	}

	p.inUse[pooledConn] = time.Now()
	atomic.AddUint64(&p.metrics.ConnectionsCreated, 1)
	atomic.AddInt32(&p.metrics.TotalConnections, 1)
	atomic.AddInt32(&p.metrics.ActiveConnections, 1)

	p.logger.Debug("Created new connection")
	return pooledConn, nil
}

/*****************************************************************************
 * NAME: isConnectionHealthy
 *
 * DESCRIPTION:
 *     Checks if a connection is healthy
 *****************************************************************************/
func (p *ConnectionPool) isConnectionHealthy(conn *PooledConnection) bool {
	if conn == nil || conn.Conn == nil {
		return false
	}

	// Set a short deadline for health check
	if err := conn.SetReadDeadline(time.Now().Add(100 * time.Millisecond)); err != nil {
		return false
	}

	// Try to read 0 bytes to check if connection is alive
	buffer := make([]byte, 0)
	_, err := conn.Read(buffer)

	// Reset deadline
	conn.SetReadDeadline(time.Time{})

	// Connection is healthy if no error or if it's a timeout (expected for 0-byte read)
	if err == nil {
		atomic.AddUint64(&p.metrics.HealthChecksPassed, 1)
		return true
	}

	if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
		atomic.AddUint64(&p.metrics.HealthChecksPassed, 1)
		return true
	}

	atomic.AddUint64(&p.metrics.HealthChecksFailed, 1)
	return false
}

/*****************************************************************************
 * NAME: isConnectionValid
 *
 * DESCRIPTION:
 *     Checks if a connection is still valid (not expired)
 *****************************************************************************/
func (p *ConnectionPool) isConnectionValid(conn *PooledConnection) bool {
	now := time.Now()

	// Check max lifetime
	if p.config.MaxLifetime > 0 && now.Sub(conn.createdAt) > p.config.MaxLifetime {
		return false
	}

	// Check max idle time
	if p.config.MaxIdleTime > 0 && now.Sub(conn.lastUsed) > p.config.MaxIdleTime {
		return false
	}

	return true
}

/*****************************************************************************
 * NAME: maintenance
 *
 * DESCRIPTION:
 *     Background maintenance routine
 *****************************************************************************/
func (p *ConnectionPool) maintenance() {
	ticker := time.NewTicker(p.config.HealthCheckInterval)
	defer ticker.Stop()

	for range ticker.C {
// 		p.mutex.Lock()
		if p.closed {
// 			p.mutex.Unlock()
			return
		}

		// Clean up expired connections
		p.cleanupExpiredConnections()
		p.metrics.LastHealthCheck = time.Now()

// 		p.mutex.Unlock()
	}
}

/*****************************************************************************
 * NAME: cleanupExpiredConnections
 *
 * DESCRIPTION:
 *     Cleans up expired connections (must be called with lock held)
 *****************************************************************************/
func (p *ConnectionPool) cleanupExpiredConnections() {
	// Clean up idle connections
	var validConnections []*PooledConnection

	// Drain the channel and check each connection
	for {
		select {
		case conn := <-p.connections:
			if p.isConnectionValid(conn) && p.isConnectionHealthy(conn) {
				validConnections = append(validConnections, conn)
			} else {
				conn.Conn.Close()
				atomic.AddUint64(&p.metrics.ConnectionsClosed, 1)
				atomic.AddInt32(&p.metrics.TotalConnections, -1)
				atomic.AddInt32(&p.metrics.IdleConnections, -1)
			}
		default:
			// Channel is empty
			goto done
		}
	}

done:
	// Put valid connections back
	for _, conn := range validConnections {
		select {
		case p.connections <- conn:
		default:
			// Pool is full, close the connection
			conn.Conn.Close()
			atomic.AddUint64(&p.metrics.ConnectionsClosed, 1)
			atomic.AddInt32(&p.metrics.TotalConnections, -1)
			atomic.AddInt32(&p.metrics.IdleConnections, -1)
		}
	}

	// Clean up in-use connections that have been idle too long
	now := time.Now()
	for conn, lastActivity := range p.inUse {
		if now.Sub(lastActivity) > p.config.MaxIdleTime {
			delete(p.inUse, conn)
			conn.Conn.Close()
			atomic.AddUint64(&p.metrics.ConnectionsClosed, 1)
			atomic.AddInt32(&p.metrics.TotalConnections, -1)
			atomic.AddInt32(&p.metrics.ActiveConnections, -1)
		}
	}
}

/*****************************************************************************
 * NAME: Close (PooledConnection)
 *
 * DESCRIPTION:
 *     Closes the pooled connection (returns it to pool)
 *****************************************************************************/
func (pc *PooledConnection) Close() error {
	pc.pool.Put(pc)
	return nil
}
