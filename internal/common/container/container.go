/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      container.go
 *
 * DESCRIPTION :    Dependency injection container for managing component lifecycle
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package container

import (
	"context"
	"fmt"
	"reflect"
	"sync"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: Container
 *
 * DESCRIPTION:
 *     Dependency injection container that manages component lifecycle
 *     and provides automatic dependency resolution
 *
 * FIELDS:
 *     services     - Map of registered services by type
 *     instances    - Map of singleton instances
 *     mutex        - Mutex for thread-safe operations
 *     logger       - Logger instance
 *****************************************************************************/
type Container struct {
	services  map[reflect.Type]ServiceDescriptor
	instances map[reflect.Type]interface{}
	mutex     sync.RWMutex
	logger    logger.Logger
}

/*****************************************************************************
 * NAME: ServiceDescriptor
 *
 * DESCRIPTION:
 *     Describes how a service should be created and managed
 *
 * FIELDS:
 *     ServiceType  - The interface type of the service
 *     ImplType     - The concrete implementation type
 *     Lifetime     - Service lifetime (Singleton, Transient, Scoped)
 *     Factory      - Optional factory function
 *****************************************************************************/
type ServiceDescriptor struct {
	ServiceType reflect.Type
	ImplType    reflect.Type
	Lifetime    ServiceLifetime
	Factory     FactoryFunc
}

/*****************************************************************************
 * NAME: ServiceLifetime
 *
 * DESCRIPTION:
 *     Defines the lifetime of a service instance
 *****************************************************************************/
type ServiceLifetime int

const (
	Singleton ServiceLifetime = iota // Single instance for the entire application
	Transient                        // New instance every time
	Scoped                           // Single instance per scope (request/operation)
)

/*****************************************************************************
 * NAME: FactoryFunc
 *
 * DESCRIPTION:
 *     Function signature for creating service instances
 *****************************************************************************/
type FactoryFunc func(ctx context.Context, container *Container) (interface{}, error)

/*****************************************************************************
 * NAME: NewContainer
 *
 * DESCRIPTION:
 *     Creates a new dependency injection container
 *
 * PARAMETERS:
 *     logger - Logger instance for container operations
 *
 * RETURNS:
 *     *Container - New container instance
 *****************************************************************************/
func NewContainer(logger logger.Logger) *Container {
	return &Container{
		services:  make(map[reflect.Type]ServiceDescriptor),
		instances: make(map[reflect.Type]interface{}),
		logger:    logger.WithModule("container"),
	}
}

/*****************************************************************************
 * NAME: RegisterSingleton
 *
 * DESCRIPTION:
 *     Registers a service as singleton with automatic constructor injection
 *
 * PARAMETERS:
 *     serviceInterface - Interface type to register
 *     implementation   - Concrete implementation type
 *
 * RETURNS:
 *     error - Registration error if any
 *****************************************************************************/
func (c *Container) RegisterSingleton(serviceInterface, implementation interface{}) error {
	//c.mutex.Lock()
	//defer c.mutex.Unlock()

	serviceType := reflect.TypeOf(serviceInterface).Elem()
	implType := reflect.TypeOf(implementation).Elem()

	// Validate that implementation satisfies the interface
	if !implType.Implements(serviceType) {
		return fmt.Errorf("implementation %v does not implement interface %v", implType, serviceType)
	}

	c.services[serviceType] = ServiceDescriptor{
		ServiceType: serviceType,
		ImplType:    implType,
		Lifetime:    Singleton,
	}

	c.logger.Debug("Registered singleton service",
		logger.String("interface", serviceType.String()),
		logger.String("implementation", implType.String()))

	return nil
}

/*****************************************************************************
 * NAME: RegisterTransient
 *
 * DESCRIPTION:
 *     Registers a service as transient (new instance each time)
 *
 * PARAMETERS:
 *     serviceInterface - Interface type to register
 *     implementation   - Concrete implementation type
 *
 * RETURNS:
 *     error - Registration error if any
 *****************************************************************************/
func (c *Container) RegisterTransient(serviceInterface, implementation interface{}) error {
	//c.mutex.Lock()
	//defer c.mutex.Unlock()

	serviceType := reflect.TypeOf(serviceInterface).Elem()
	implType := reflect.TypeOf(implementation).Elem()

	if !implType.Implements(serviceType) {
		return fmt.Errorf("implementation %v does not implement interface %v", implType, serviceType)
	}

	c.services[serviceType] = ServiceDescriptor{
		ServiceType: serviceType,
		ImplType:    implType,
		Lifetime:    Transient,
	}

	c.logger.Debug("Registered transient service",
		logger.String("interface", serviceType.String()),
		logger.String("implementation", implType.String()))

	return nil
}

/*****************************************************************************
 * NAME: RegisterFactory
 *
 * DESCRIPTION:
 *     Registers a service with a custom factory function
 *
 * PARAMETERS:
 *     serviceInterface - Interface type to register
 *     factory         - Factory function to create instances
 *     lifetime        - Service lifetime
 *
 * RETURNS:
 *     error - Registration error if any
 *****************************************************************************/
func (c *Container) RegisterFactory(serviceInterface interface{}, factory FactoryFunc, lifetime ServiceLifetime) error {
	//c.mutex.Lock()
	//defer c.mutex.Unlock()

	serviceType := reflect.TypeOf(serviceInterface).Elem()

	c.services[serviceType] = ServiceDescriptor{
		ServiceType: serviceType,
		Lifetime:    lifetime,
		Factory:     factory,
	}

	c.logger.Debug("Registered factory service",
		logger.String("interface", serviceType.String()),
		logger.String("lifetime", fmt.Sprintf("%d", lifetime)))

	return nil
}

/*****************************************************************************
 * NAME: Resolve
 *
 * DESCRIPTION:
 *     Resolves a service instance by type with dependency injection
 *
 * PARAMETERS:
 *     ctx          - Context for the resolution
 *     serviceType  - Type of service to resolve
 *
 * RETURNS:
 *     interface{} - Service instance
 *     error      - Resolution error if any
 *****************************************************************************/
func (c *Container) Resolve(ctx context.Context, serviceType reflect.Type) (interface{}, error) {
	//c.mutex.RLock()
	descriptor, exists := c.services[serviceType]
	//c.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("service %v not registered", serviceType)
	}

	// Check for singleton instance
	if descriptor.Lifetime == Singleton {
		//c.mutex.RLock()
		if instance, exists := c.instances[serviceType]; exists {
			//c.mutex.RUnlock()
			return instance, nil
		}
		//c.mutex.RUnlock()
	}

	// Create new instance
	instance, err := c.createInstance(ctx, descriptor)
	if err != nil {
		return nil, fmt.Errorf("failed to create instance of %v: %w", serviceType, err)
	}

	// Store singleton instance
	if descriptor.Lifetime == Singleton {
		//c.mutex.Lock()
		c.instances[serviceType] = instance
		//c.mutex.Unlock()
	}

	return instance, nil
}

/*****************************************************************************
 * NAME: ResolveByInterface
 *
 * DESCRIPTION:
 *     Resolves a service instance by interface type
 *
 * PARAMETERS:
 *     ctx             - Context for the resolution
 *     serviceInterface - Interface to resolve
 *
 * RETURNS:
 *     interface{} - Service instance
 *     error      - Resolution error if any
 *****************************************************************************/
func (c *Container) ResolveByInterface(ctx context.Context, serviceInterface interface{}) (interface{}, error) {
	serviceType := reflect.TypeOf(serviceInterface).Elem()
	return c.Resolve(ctx, serviceType)
}

/*****************************************************************************
 * NAME: createInstance
 *
 * DESCRIPTION:
 *     Creates a new instance of a service with dependency injection
 *
 * PARAMETERS:
 *     ctx        - Context for the creation
 *     descriptor - Service descriptor
 *
 * RETURNS:
 *     interface{} - Created instance
 *     error      - Creation error if any
 *****************************************************************************/
func (c *Container) createInstance(ctx context.Context, descriptor ServiceDescriptor) (interface{}, error) {
	// Use factory function if provided
	if descriptor.Factory != nil {
		return descriptor.Factory(ctx, c)
	}

	// Use reflection to create instance with constructor injection
	implType := descriptor.ImplType
	if implType.Kind() != reflect.Struct {
		return nil, fmt.Errorf("implementation type %v must be a struct", implType)
	}

	// Create instance using reflection
	instance := reflect.New(implType).Interface()

	// Perform field injection if the instance supports it
	if err := c.injectFields(instance); err != nil {
		return nil, fmt.Errorf("failed to inject fields: %w", err)
	}

	c.logger.Debug("Created service instance",
		logger.String("type", descriptor.ServiceType.String()))

	return instance, nil
}

/*****************************************************************************
 * NAME: injectFields
 *
 * DESCRIPTION:
 *     Performs field injection on the given instance using reflection
 *     and inject tags to resolve dependencies
 *
 * PARAMETERS:
 *     instance - Instance to inject dependencies into
 *
 * RETURNS:
 *     error - Injection error if any
 *****************************************************************************/
func (c *Container) injectFields(instance interface{}) error {
	if instance == nil {
		return nil
	}

	value := reflect.ValueOf(instance)
	if value.Kind() == reflect.Ptr {
		value = value.Elem()
	}

	if value.Kind() != reflect.Struct {
		return nil // Nothing to inject for non-struct types
	}

	structType := value.Type()
	for i := 0; i < value.NumField(); i++ {
		field := value.Field(i)
		fieldType := structType.Field(i)

		// Skip unexported fields
		if !field.CanSet() {
			continue
		}

		// Check for inject tag
		injectTag := fieldType.Tag.Get("inject")
		if injectTag == "" {
			continue
		}

		// Try to resolve the dependency
		var dependency interface{}
		var err error

		if injectTag == "true" || injectTag == "" {
			// Use field type as service type
			dependency, err = c.Resolve(context.Background(), fieldType.Type)
		} else {
			// Use tag value as service name (not implemented yet)
			continue
		}

		if err != nil {
			c.logger.Debug("Failed to inject field dependency",
				logger.String("field", fieldType.Name),
				logger.String("type", fieldType.Type.String()),
				logger.ErrorField(err))
			continue
		}

		// Set the field value
		if dependency != nil {
			depValue := reflect.ValueOf(dependency)
			if depValue.Type().AssignableTo(field.Type()) {
				field.Set(depValue)
				c.logger.Debug("Injected field dependency",
					logger.String("field", fieldType.Name),
					logger.String("type", fieldType.Type.String()))
			}
		}
	}

	return nil
}

/*****************************************************************************
 * NAME: Shutdown
 *
 * DESCRIPTION:
 *     Shuts down the container and cleans up resources
 *
 * PARAMETERS:
 *     ctx - Context for shutdown
 *
 * RETURNS:
 *     error - Shutdown error if any
 *****************************************************************************/
func (c *Container) Shutdown(ctx context.Context) error {
	//c.mutex.Lock()
	//defer c.mutex.Unlock()

	c.logger.Info("Shutting down dependency injection container")

	// Clear all instances and services
	c.instances = make(map[reflect.Type]interface{})
	c.services = make(map[reflect.Type]ServiceDescriptor)

	c.logger.Info("Container shutdown completed")
	return nil
}
