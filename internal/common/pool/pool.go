/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      pool.go
 *
 * DESCRIPTION :    Object pool manager for memory optimization and GC pressure reduction
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025  create
 ******************************************************************************/

package pool

import (
	"sync"
	"sync/atomic"
	"time"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: Manager
 *
 * DESCRIPTION:
 *     Object pool manager that manages multiple pools and provides metrics
 *
 * FIELDS:
 *     pools    - Map of registered pools
 *     mutex    - Mutex for thread-safe operations
 *     logger   - Logger instance
 *     metrics  - Pool metrics
 *****************************************************************************/
type Manager struct {
	pools   map[string]Pool
	mutex   sync.RWMutex
	logger  logger.Logger
	metrics *Metrics
}

/*****************************************************************************
 * NAME: Pool
 *
 * DESCRIPTION:
 *     Interface for object pools
 *****************************************************************************/
type Pool interface {
	Get() interface{}
	Put(interface{})
	Size() int
	Reset()
	GetMetrics() PoolMetrics
}

/*****************************************************************************
 * NAME: PoolMetrics
 *
 * DESCRIPTION:
 *     Metrics for a single pool
 *****************************************************************************/
type PoolMetrics struct {
	Name       string    `json:"name"`
	Size       int       `json:"size"`
	Gets       uint64    `json:"gets"`
	Puts       uint64    `json:"puts"`
	Hits       uint64    `json:"hits"`
	Misses     uint64    `json:"misses"`
	Created    uint64    `json:"created"`
	LastAccess time.Time `json:"last_access"`
	HitRatio   float64   `json:"hit_ratio"`
}

/*****************************************************************************
 * NAME: Metrics
 *
 * DESCRIPTION:
 *     Overall pool manager metrics
 *****************************************************************************/
type Metrics struct {
	TotalPools      int           `json:"total_pools"`
	TotalObjects    int           `json:"total_objects"`
	TotalGets       uint64        `json:"total_gets"`
	TotalPuts       uint64        `json:"total_puts"`
	TotalHits       uint64        `json:"total_hits"`
	TotalMisses     uint64        `json:"total_misses"`
	OverallHitRatio float64       `json:"overall_hit_ratio"`
	Pools           []PoolMetrics `json:"pools"`
}

/*****************************************************************************
 * NAME: BytePool
 *
 * DESCRIPTION:
 *     Pool for byte slices with configurable size
 *****************************************************************************/
type BytePool struct {
	pool       sync.Pool
	size       int
	name       string
	gets       uint64
	puts       uint64
	hits       uint64
	misses     uint64
	created    uint64
	lastAccess time.Time
	mutex      sync.RWMutex
}

/*****************************************************************************
 * NAME: NewManager
 *
 * DESCRIPTION:
 *     Creates a new pool manager
 *
 * PARAMETERS:
 *     logger - Logger instance
 *
 * RETURNS:
 *     *Manager - New pool manager
 *****************************************************************************/
func NewManager(logger logger.Logger) *Manager {
	return &Manager{
		pools:   make(map[string]Pool),
		logger:  logger.WithModule("pool"),
		metrics: &Metrics{},
	}
}

/*****************************************************************************
 * NAME: RegisterBytePool
 *
 * DESCRIPTION:
 *     Registers a new byte pool
 *
 * PARAMETERS:
 *     name - Pool name
 *     size - Buffer size
 *
 * RETURNS:
 *     Pool - Registered pool
 *****************************************************************************/
func (m *Manager) RegisterBytePool(name string, size int) Pool {
	//m.mutex.Lock()
	//defer m.mutex.Unlock()

	pool := NewBytePool(name, size)
	m.pools[name] = pool

	m.logger.Debug("Registered byte pool",
		logger.String("name", name),
		logger.Int("size", size))

	return pool
}

/*****************************************************************************
 * NAME: GetPool
 *
 * DESCRIPTION:
 *     Gets a pool by name
 *
 * PARAMETERS:
 *     name - Pool name
 *
 * RETURNS:
 *     Pool - Pool instance or nil if not found
 *****************************************************************************/
func (m *Manager) GetPool(name string) Pool {
	//m.mutex.RLock()
	//defer m.mutex.RUnlock()

	return m.pools[name]
}

/*****************************************************************************
 * NAME: GetMetrics
 *
 * DESCRIPTION:
 *     Gets overall pool metrics
 *
 * RETURNS:
 *     *Metrics - Pool metrics
 *****************************************************************************/
func (m *Manager) GetMetrics() *Metrics {
	//m.mutex.RLock()
	//defer m.mutex.RUnlock()

	metrics := &Metrics{
		TotalPools: len(m.pools),
		Pools:      make([]PoolMetrics, 0, len(m.pools)),
	}

	for _, pool := range m.pools {
		poolMetrics := pool.GetMetrics()
		metrics.Pools = append(metrics.Pools, poolMetrics)

		metrics.TotalObjects += poolMetrics.Size
		metrics.TotalGets += poolMetrics.Gets
		metrics.TotalPuts += poolMetrics.Puts
		metrics.TotalHits += poolMetrics.Hits
		metrics.TotalMisses += poolMetrics.Misses
	}

	if metrics.TotalGets > 0 {
		metrics.OverallHitRatio = float64(metrics.TotalHits) / float64(metrics.TotalGets)
	}

	return metrics
}

/*****************************************************************************
 * NAME: ResetAll
 *
 * DESCRIPTION:
 *     Resets all pools
 *****************************************************************************/
func (m *Manager) ResetAll() {
	//m.mutex.RLock()
	//defer m.mutex.RUnlock()

	for name, pool := range m.pools {
		pool.Reset()
		m.logger.Debug("Reset pool", logger.String("name", name))
	}
}

/*****************************************************************************
 * NAME: NewBytePool
 *
 * DESCRIPTION:
 *     Creates a new byte pool
 *
 * PARAMETERS:
 *     name - Pool name
 *     size - Buffer size
 *
 * RETURNS:
 *     *BytePool - New byte pool
 *****************************************************************************/
func NewBytePool(name string, size int) *BytePool {
	bp := &BytePool{
		size: size,
		name: name,
	}

	bp.pool = sync.Pool{
		New: func() interface{} {
			atomic.AddUint64(&bp.created, 1)
			return make([]byte, size)
		},
	}

	return bp
}

/*****************************************************************************
 * NAME: Get
 *
 * DESCRIPTION:
 *     Gets a byte slice from the pool
 *
 * RETURNS:
 *     interface{} - Byte slice
 *****************************************************************************/
func (bp *BytePool) Get() interface{} {
	atomic.AddUint64(&bp.gets, 1)
	//bp.mutex.Lock()
	bp.lastAccess = time.Now()
	//bp.mutex.Unlock()

	obj := bp.pool.Get()
	if obj != nil {
		atomic.AddUint64(&bp.hits, 1)
		// Reset the slice to ensure clean state
		if buf, ok := obj.([]byte); ok {
			for i := range buf {
				buf[i] = 0
			}
		}
	} else {
		atomic.AddUint64(&bp.misses, 1)
	}

	return obj
}

/*****************************************************************************
 * NAME: Put
 *
 * DESCRIPTION:
 *     Returns a byte slice to the pool
 *
 * PARAMETERS:
 *     obj - Object to return
 *****************************************************************************/
func (bp *BytePool) Put(obj interface{}) {
	if obj == nil {
		return
	}

	// Validate object type and size
	if buf, ok := obj.([]byte); ok && len(buf) == bp.size {
		atomic.AddUint64(&bp.puts, 1)
		bp.pool.Put(obj)
	}
}

/*****************************************************************************
 * NAME: Size
 *
 * DESCRIPTION:
 *     Returns the buffer size for this pool
 *
 * RETURNS:
 *     int - Buffer size
 *****************************************************************************/
func (bp *BytePool) Size() int {
	return bp.size
}

/*****************************************************************************
 * NAME: Reset
 *
 * DESCRIPTION:
 *     Resets the pool metrics
 *****************************************************************************/
func (bp *BytePool) Reset() {
	//bp.mutex.Lock()
	//defer bp.mutex.Unlock()

	atomic.StoreUint64(&bp.gets, 0)
	atomic.StoreUint64(&bp.puts, 0)
	atomic.StoreUint64(&bp.hits, 0)
	atomic.StoreUint64(&bp.misses, 0)
	atomic.StoreUint64(&bp.created, 0)
	bp.lastAccess = time.Time{}
}

/*****************************************************************************
 * NAME: GetMetrics
 *
 * DESCRIPTION:
 *     Gets pool metrics
 *
 * RETURNS:
 *     PoolMetrics - Pool metrics
 *****************************************************************************/
func (bp *BytePool) GetMetrics() PoolMetrics {
	//bp.mutex.RLock()
	//defer bp.mutex.RUnlock()

	gets := atomic.LoadUint64(&bp.gets)
	puts := atomic.LoadUint64(&bp.puts)
	hits := atomic.LoadUint64(&bp.hits)
	misses := atomic.LoadUint64(&bp.misses)

	var hitRatio float64
	if gets > 0 {
		hitRatio = float64(hits) / float64(gets)
	}

	return PoolMetrics{
		Name:       bp.name,
		Size:       bp.size,
		Gets:       gets,
		Puts:       puts,
		Hits:       hits,
		Misses:     misses,
		Created:    atomic.LoadUint64(&bp.created),
		LastAccess: bp.lastAccess,
		HitRatio:   hitRatio,
	}
}

/*****************************************************************************
 * GLOBAL POOLS
 *
 * DESCRIPTION:
 *     Pre-defined global pools for common use cases
 *****************************************************************************/

var (
	// Global pool manager
	globalManager *Manager

	// Common buffer sizes
	SmallBufferPool  Pool // 1KB buffers
	MediumBufferPool Pool // 4KB buffers
	LargeBufferPool  Pool // 64KB buffers
	PacketBufferPool Pool // 2KB buffers for network packets
)

/*****************************************************************************
 * NAME: InitGlobalPools
 *
 * DESCRIPTION:
 *     Initializes global pools
 *
 * PARAMETERS:
 *     logger - Logger instance
 *****************************************************************************/
func InitGlobalPools(logger logger.Logger) {
	globalManager = NewManager(logger)

	SmallBufferPool = globalManager.RegisterBytePool("small", 1024)   // 1KB
	MediumBufferPool = globalManager.RegisterBytePool("medium", 4096) // 4KB
	LargeBufferPool = globalManager.RegisterBytePool("large", 65536)  // 64KB
	PacketBufferPool = globalManager.RegisterBytePool("packet", 2048) // 2KB
}

/*****************************************************************************
 * NAME: GetGlobalManager
 *
 * DESCRIPTION:
 *     Gets the global pool manager
 *
 * RETURNS:
 *     *Manager - Global pool manager
 *****************************************************************************/
func GetGlobalManager() *Manager {
	return globalManager
}
