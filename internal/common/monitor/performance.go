/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      performance.go
 *
 * DESCRIPTION :    Performance monitoring and metrics collection system
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package monitor

import (
	"context"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: PerformanceMonitor
 *
 * DESCRIPTION:
 *     System for monitoring application performance and resource usage
 *
 * FIELDS:
 *     metrics      - Current metrics
 *     logger       - Logger instance
 *     collectors   - Registered metric collectors
 *     interval     - Collection interval
 *     ctx          - Monitor context
 *     cancel       - Context cancel function
 *     mutex        - Mutex for thread-safe operations
 *     running      - Whether monitor is running
 *****************************************************************************/
type PerformanceMonitor struct {
	metrics    *Metrics
	logger     logger.Logger
	collectors []MetricCollector
	interval   time.Duration
	ctx        context.Context
	cancel     context.CancelFunc
	mutex      sync.RWMutex
	running    bool
}

/*****************************************************************************
 * NAME: Metrics
 *
 * DESCRIPTION:
 *     Performance metrics data
 *****************************************************************************/
type Metrics struct {
	// System metrics
	Timestamp time.Time     `json:"timestamp"`
	Uptime    time.Duration `json:"uptime"`

	// Memory metrics
	MemoryUsed    uint64        `json:"memory_used"`
	MemoryTotal   uint64        `json:"memory_total"`
	MemoryPercent float64       `json:"memory_percent"`
	HeapUsed      uint64        `json:"heap_used"`
	HeapTotal     uint64        `json:"heap_total"`
	GCCount       uint32        `json:"gc_count"`
	GCPauseTotal  time.Duration `json:"gc_pause_total"`
	GCPauseAvg    time.Duration `json:"gc_pause_avg"`

	// CPU metrics
	CPUPercent     float64 `json:"cpu_percent"`
	GoroutineCount int     `json:"goroutine_count"`

	// Network metrics
	NetworkBytesIn    uint64 `json:"network_bytes_in"`
	NetworkBytesOut   uint64 `json:"network_bytes_out"`
	NetworkPacketsIn  uint64 `json:"network_packets_in"`
	NetworkPacketsOut uint64 `json:"network_packets_out"`

	// Application metrics
	RequestCount    uint64        `json:"request_count"`
	ErrorCount      uint64        `json:"error_count"`
	ResponseTimeAvg time.Duration `json:"response_time_avg"`
	ResponseTimeP95 time.Duration `json:"response_time_p95"`
	ResponseTimeP99 time.Duration `json:"response_time_p99"`

	// Custom metrics
	Custom map[string]interface{} `json:"custom"`
}

/*****************************************************************************
 * NAME: MetricCollector
 *
 * DESCRIPTION:
 *     Interface for custom metric collectors
 *****************************************************************************/
type MetricCollector interface {
	Collect() map[string]interface{}
	Name() string
}

/*****************************************************************************
 * NAME: RequestTracker
 *
 * DESCRIPTION:
 *     Tracks request performance metrics
 *****************************************************************************/
type RequestTracker struct {
	startTime time.Time
	monitor   *PerformanceMonitor
	operation string
}

/*****************************************************************************
 * NAME: NewPerformanceMonitor
 *
 * DESCRIPTION:
 *     Creates a new performance monitor
 *
 * PARAMETERS:
 *     logger   - Logger instance
 *     interval - Collection interval
 *
 * RETURNS:
 *     *PerformanceMonitor - New performance monitor
 *****************************************************************************/
func NewPerformanceMonitor(logger logger.Logger, interval time.Duration) *PerformanceMonitor {
	return &PerformanceMonitor{
		metrics: &Metrics{
			Custom: make(map[string]interface{}),
		},
		logger:     logger.WithModule("monitor"),
		collectors: make([]MetricCollector, 0),
		interval:   interval,
	}
}

/*****************************************************************************
 * NAME: Start
 *
 * DESCRIPTION:
 *     Starts the performance monitor
 *
 * RETURNS:
 *     error - Start error if any
 *****************************************************************************/
func (pm *PerformanceMonitor) Start() error {
	//pm.mutex.Lock()
	//defer pm.mutex.Unlock()

	if pm.running {
		return nil
	}

	pm.logger.Info("Starting performance monitor",
		logger.Duration("interval", pm.interval))

	pm.ctx, pm.cancel = context.WithCancel(context.Background())
	pm.running = true

	// Start collection goroutine
	go pm.collectLoop()

	return nil
}

/*****************************************************************************
 * NAME: Stop
 *
 * DESCRIPTION:
 *     Stops the performance monitor
 *
 * RETURNS:
 *     error - Stop error if any
 *****************************************************************************/
func (pm *PerformanceMonitor) Stop() error {
	//pm.mutex.Lock()
	//defer pm.mutex.Unlock()

	if !pm.running {
		return nil
	}

	pm.logger.Info("Stopping performance monitor")

	pm.cancel()
	pm.running = false

	return nil
}

/*****************************************************************************
 * NAME: RegisterCollector
 *
 * DESCRIPTION:
 *     Registers a custom metric collector
 *
 * PARAMETERS:
 *     collector - Metric collector to register
 *****************************************************************************/
func (pm *PerformanceMonitor) RegisterCollector(collector MetricCollector) {
	//pm.mutex.Lock()
	//defer pm.mutex.Unlock()

	pm.collectors = append(pm.collectors, collector)
	pm.logger.Debug("Registered metric collector",
		logger.String("name", collector.Name()))
}

/*****************************************************************************
 * NAME: GetMetrics
 *
 * DESCRIPTION:
 *     Gets current performance metrics
 *
 * RETURNS:
 *     *Metrics - Current metrics
 *****************************************************************************/
func (pm *PerformanceMonitor) GetMetrics() *Metrics {
	//pm.mutex.RLock()
	//defer pm.mutex.RUnlock()

	// Create a copy to avoid race conditions
	metrics := *pm.metrics
	metrics.Custom = make(map[string]interface{})
	for k, v := range pm.metrics.Custom {
		metrics.Custom[k] = v
	}

	return &metrics
}

/*****************************************************************************
 * NAME: TrackRequest
 *
 * DESCRIPTION:
 *     Starts tracking a request
 *
 * PARAMETERS:
 *     operation - Operation name
 *
 * RETURNS:
 *     *RequestTracker - Request tracker
 *****************************************************************************/
func (pm *PerformanceMonitor) TrackRequest(operation string) *RequestTracker {
	return &RequestTracker{
		startTime: time.Now(),
		monitor:   pm,
		operation: operation,
	}
}

/*****************************************************************************
 * NAME: IncrementCounter
 *
 * DESCRIPTION:
 *     Increments a custom counter metric
 *
 * PARAMETERS:
 *     name - Counter name
 *     value - Value to add
 *****************************************************************************/
func (pm *PerformanceMonitor) IncrementCounter(name string, value uint64) {
	//pm.mutex.Lock()
	//defer pm.mutex.Unlock()

	if current, exists := pm.metrics.Custom[name]; exists {
		if counter, ok := current.(uint64); ok {
			pm.metrics.Custom[name] = counter + value
		} else {
			pm.metrics.Custom[name] = value
		}
	} else {
		pm.metrics.Custom[name] = value
	}
}

/*****************************************************************************
 * NAME: SetGauge
 *
 * DESCRIPTION:
 *     Sets a custom gauge metric
 *
 * PARAMETERS:
 *     name  - Gauge name
 *     value - Value to set
 *****************************************************************************/
func (pm *PerformanceMonitor) SetGauge(name string, value interface{}) {
	//pm.mutex.Lock()
	//defer pm.mutex.Unlock()

	pm.metrics.Custom[name] = value
}

/*****************************************************************************
 * NAME: collectLoop
 *
 * DESCRIPTION:
 *     Main collection loop
 *****************************************************************************/
func (pm *PerformanceMonitor) collectLoop() {
	ticker := time.NewTicker(pm.interval)
	defer ticker.Stop()

	startTime := time.Now()

	for {
		select {
		case <-pm.ctx.Done():
			return
		case <-ticker.C:
			pm.collectMetrics(startTime)
		}
	}
}

/*****************************************************************************
 * NAME: collectMetrics
 *
 * DESCRIPTION:
 *     Collects all performance metrics
 *****************************************************************************/
func (pm *PerformanceMonitor) collectMetrics(startTime time.Time) {
	//pm.mutex.Lock()
	//defer pm.mutex.Unlock()

	now := time.Now()
	pm.metrics.Timestamp = now
	pm.metrics.Uptime = now.Sub(startTime)

	// Collect memory metrics
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	pm.metrics.MemoryUsed = memStats.Alloc
	pm.metrics.MemoryTotal = memStats.Sys
	pm.metrics.MemoryPercent = float64(memStats.Alloc) / float64(memStats.Sys) * 100
	pm.metrics.HeapUsed = memStats.HeapAlloc
	pm.metrics.HeapTotal = memStats.HeapSys
	pm.metrics.GCCount = memStats.NumGC

	// Calculate GC pause metrics
	if memStats.NumGC > 0 {
		pm.metrics.GCPauseTotal = time.Duration(memStats.PauseTotalNs)
		pm.metrics.GCPauseAvg = time.Duration(memStats.PauseTotalNs / uint64(memStats.NumGC))
	}

	// Collect goroutine count
	pm.metrics.GoroutineCount = runtime.NumGoroutine()

	// Collect custom metrics from registered collectors
	for _, collector := range pm.collectors {
		customMetrics := collector.Collect()
		for key, value := range customMetrics {
			pm.metrics.Custom[collector.Name()+"_"+key] = value
		}
	}

	pm.logger.Debug("Collected performance metrics",
		logger.Uint64("memory_used", pm.metrics.MemoryUsed),
		logger.Int("goroutines", pm.metrics.GoroutineCount),
		logger.Uint32("gc_count", pm.metrics.GCCount))
}

/*****************************************************************************
 * NAME: Finish (RequestTracker)
 *
 * DESCRIPTION:
 *     Finishes tracking a request
 *
 * PARAMETERS:
 *     success - Whether the request was successful
 *****************************************************************************/
func (rt *RequestTracker) Finish(success bool) {
	duration := time.Since(rt.startTime)

	// 	rt.monitor.mutex.Lock()
	// 	defer rt.monitor.mutex.Unlock()

	// Update request count
	atomic.AddUint64(&rt.monitor.metrics.RequestCount, 1)

	// Update error count if failed
	if !success {
		atomic.AddUint64(&rt.monitor.metrics.ErrorCount, 1)
	}

	// Update response time metrics (simplified)
	// In a real implementation, you'd want to use a proper histogram
	//rt.monitor.mutex.Lock()
	rt.monitor.metrics.ResponseTimeAvg = duration
	//rt.monitor.mutex.Unlock()

}

/*****************************************************************************
 * NAME: GetSummary
 *
 * DESCRIPTION:
 *     Gets a summary of key performance metrics
 *
 * RETURNS:
 *     map[string]interface{} - Summary metrics
 *****************************************************************************/
func (pm *PerformanceMonitor) GetSummary() map[string]interface{} {
	metrics := pm.GetMetrics()

	return map[string]interface{}{
		"uptime":            metrics.Uptime.String(),
		"memory_used_mb":    metrics.MemoryUsed / 1024 / 1024,
		"memory_percent":    metrics.MemoryPercent,
		"goroutines":        metrics.GoroutineCount,
		"gc_count":          metrics.GCCount,
		"request_count":     metrics.RequestCount,
		"error_count":       metrics.ErrorCount,
		"error_rate":        float64(metrics.ErrorCount) / float64(metrics.RequestCount) * 100,
		"avg_response_time": metrics.ResponseTimeAvg.String(),
	}
}

/*****************************************************************************
 * GLOBAL PERFORMANCE MONITOR
 *****************************************************************************/

var globalMonitor *PerformanceMonitor

/*****************************************************************************
 * NAME: InitGlobalMonitor
 *
 * DESCRIPTION:
 *     Initializes the global performance monitor
 *
 * PARAMETERS:
 *     logger   - Logger instance
 *     interval - Collection interval
 *****************************************************************************/
func InitGlobalMonitor(logger logger.Logger, interval time.Duration) {
	globalMonitor = NewPerformanceMonitor(logger, interval)
}

/*****************************************************************************
 * NAME: GetGlobalMonitor
 *
 * DESCRIPTION:
 *     Gets the global performance monitor
 *
 * RETURNS:
 *     *PerformanceMonitor - Global monitor
 *****************************************************************************/
func GetGlobalMonitor() *PerformanceMonitor {
	return globalMonitor
}

/*****************************************************************************
 * NAME: TrackGlobalRequest
 *
 * DESCRIPTION:
 *     Tracks a request using the global monitor
 *
 * PARAMETERS:
 *     operation - Operation name
 *
 * RETURNS:
 *     *RequestTracker - Request tracker
 *****************************************************************************/
func TrackGlobalRequest(operation string) *RequestTracker {
	if globalMonitor != nil {
		return globalMonitor.TrackRequest(operation)
	}
	return nil
}
