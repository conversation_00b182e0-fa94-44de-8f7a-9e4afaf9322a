/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      doc.go
 *
 * DESCRIPTION :    Logger package documentation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

/*
Package logger provides a flexible, high-performance structured logging system.

Basic Usage:

	// Create logger configuration
	config := logger.Config{
	    Level:  logger.LevelInfo,
	    Format: logger.FormatJSON,
	    Outputs: []logger.Output{
	        {
	            Type:   logger.TypeFile,
	            File:   "logs/app.log",
	            MaxSize: 10,
	            MaxAge: 7,
	            MaxBackups: 3,
	            Compress: true,
	        },
	        {
	            Type: logger.TypeConsole,
	        },
	    },
	    IncludeCaller: true,
	    StacktraceLevel: logger.LevelError,
	}

	// Create logger
	log, err := logger.New(config)
	if err != nil {
	    panic(err)
	}
	defer log.Close()

	// Log messages
	log.Info("Application started", logger.String("version", "1.0.0"))

	// Create module-specific logger
	moduleLogger := log.WithModule("auth")
	moduleLogger.Debug("User authentication attempt",
	    logger.String("username", "user123"),
	    logger.String("ip", "***********"),
	)

	// Trace method execution time
	defer log.TraceMethod("processRequest")()

	// Trace errors
	if err := doSomething(); err != nil {
	    log.TraceError(err, "Failed to do something")
	}

Using the Default Logger:

	// Initialize default logger
	err := logger.InitDefault(config)
	if err != nil {
	    panic(err)
	}
	defer logger.Close()

	// Use global functions
	logger.Info("Application started", logger.String("version", "1.0.0"))

	// Create module-specific logger
	authLogger := logger.WithModule("auth")
	authLogger.Debug("User authentication attempt")

Log Levels:

  - LevelDebug: Debug information for development and troubleshooting
  - LevelInfo: General information indicating normal operation
  - LevelWarn: Warning information indicating potential issues
  - LevelError: Error information indicating problems that allow continued operation
  - LevelFatal: Fatal error information, after which the application will exit

Log Formats:

  - FormatJSON: Structured JSON format suitable for machine processing
  - FormatText: Human-readable text format suitable for development and debugging

Output Types:

  - TypeFile: Output to file with log rotation support
  - TypeConsole: Output to console (stdout or stderr)

Field Types:

	logger.String("key", "value")
	logger.Int("key", 123)
	logger.Bool("key", true)
	logger.Error(err)
	// See documentation for more field types
*/
package logger
