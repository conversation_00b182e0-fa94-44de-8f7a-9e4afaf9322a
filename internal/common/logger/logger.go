/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      logger.go
 *
 * DESCRIPTION :    Logger interface and implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Level defines the log level
type Level string

// Log level constants
const (
	LevelDebug Level = "DEBUG"
	LevelInfo  Level = "INFO"
	LevelWarn  Level = "WARN"
	LevelError Level = "ERROR"
	LevelFatal Level = "FATAL"
)

// Format defines the log format
type Format string

// Log format constants
const (
	FormatText Format = "text"
	FormatJSON Format = "json"
)

// OutputType defines the output type
type OutputType string

// Output type constants
const (
	TypeFile    OutputType = "file"
	TypeConsole OutputType = "console"
)

/*****************************************************************************
 * NAME: Field
 *
 * DESCRIPTION:
 *     Defines a log field for structured logging
 *
 * FIELDS:
 *     Key   - Field name
 *     Value - Field value, can be any type
 *****************************************************************************/
type Field struct {
	Key   string
	Value interface{}
}

/*****************************************************************************
 * NAME: Output
 *
 * DESCRIPTION:
 *     Defines log output configuration
 *
 * FIELDS:
 *     Type       - Output type (file or console)
 *     File       - Log file path (only for file output)
 *     MaxSize    - Maximum size of a single log file in MB
 *     MaxAge     - Maximum number of days to retain log files
 *     MaxBackups - Maximum number of old log files to retain
 *     Compress   - Whether to compress old log files
 *     Stderr     - Whether to output to stderr (only for console output)
 *****************************************************************************/
type Output struct {
	Type       OutputType `yaml:"type"`
	File       string     `yaml:"file"`
	MaxSize    int        `yaml:"maxSize"`
	MaxAge     int        `yaml:"maxAge"`
	MaxBackups int        `yaml:"maxBackups"`
	Compress   bool       `yaml:"compress"`
	Stderr     bool       `yaml:"stderr"`
}

/*****************************************************************************
 * NAME: Config
 *
 * DESCRIPTION:
 *     Defines logger configuration
 *
 * FIELDS:
 *     Level           - Log level
 *     Format          - Log format
 *     Outputs         - List of output configurations
 *     IncludeCaller   - Whether to include caller information
 *     StacktraceLevel - Minimum level to record stack traces
 *****************************************************************************/
type Config struct {
	Level           Level    `yaml:"level"`
	Format          Format   `yaml:"format"`
	Outputs         []Output `yaml:"outputs"`
	IncludeCaller   bool     `yaml:"includeCaller"`
	StacktraceLevel Level    `yaml:"stacktraceLevel"`
}

/*****************************************************************************
 * NAME: Logger
 *
 * DESCRIPTION:
 *     Defines the logger interface for structured logging
 *****************************************************************************/
type Logger interface {
	// Basic logging methods
	Debug(msg string, fields ...Field)
	Info(msg string, fields ...Field)
	Warn(msg string, fields ...Field)
	Error(msg string, fields ...Field)
	Fatal(msg string, fields ...Field)

	// Context-aware logging methods
	WithFields(fields ...Field) Logger
	WithModule(module string) Logger
	WithRequestID(requestID string) Logger
	WithContext(ctx map[string]interface{}) Logger

	// Performance tracking methods
	TraceMethod(methodName string) func()
	TraceError(err error, msg string, fields ...Field)

	// Synchronization and cleanup
	Sync() error
	Close() error
}

// zapLogger implements the Logger interface
type zapLogger struct {
	logger *zap.Logger
	fields []Field
}

// Global atomic level for dynamic log level adjustment
var globalAtomicLevel = zap.NewAtomicLevel()

/*****************************************************************************
 * NAME: New
 *
 * DESCRIPTION:
 *     Creates a new logger instance
 *
 * PARAMETERS:
 *     config - Logger configuration
 *
 * RETURNS:
 *     Logger - Logger interface
 *     error  - Error if creation fails
 *****************************************************************************/
func New(config Config) (Logger, error) {
	// Set initial log level
	globalAtomicLevel.SetLevel(toZapLevel(config.Level))

	// Create encoder
	var encoder zapcore.Encoder
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "timestamp",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "message",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.StringDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	switch config.Format {
	case FormatText:
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	case FormatJSON:
		fallthrough
	default:
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	}

	// Create outputs
	var cores []zapcore.Core
	for _, out := range config.Outputs {
		var core zapcore.Core
		switch out.Type {
		case TypeFile:
			if out.File == "" {
				continue
			}
			// Ensure directory exists
			if err := os.MkdirAll(filepath.Dir(out.File), 0o755); err != nil {
				return nil, fmt.Errorf("failed to create output directory: %w", err)
			}

			// Configure log rotation
			writer := &lumberjack.Logger{
				Filename:   out.File,
				MaxSize:    out.MaxSize,
				MaxAge:     out.MaxAge,
				MaxBackups: out.MaxBackups,
				Compress:   out.Compress,
			}
			core = zapcore.NewCore(encoder, zapcore.AddSync(writer), globalAtomicLevel)
		case TypeConsole:
			writer := os.Stdout
			if out.Stderr {
				writer = os.Stderr
			}
			core = zapcore.NewCore(encoder, zapcore.AddSync(writer), globalAtomicLevel)
		default:
			continue
		}
		cores = append(cores, core)
	}

	// If no valid outputs, default to stdout
	if len(cores) == 0 {
		fmt.Fprintf(os.Stderr, "Warning: no valid outputs specified, falling back to stdout\n")
		core := zapcore.NewCore(encoder, zapcore.AddSync(os.Stdout), globalAtomicLevel)
		cores = append(cores, core)
	}

	// Combine all cores
	core := zapcore.NewTee(cores...)

	// Create options
	opts := []zap.Option{
		zap.Fields(zap.Int("pid", os.Getpid())),
	}

	// Add caller information
	if config.IncludeCaller {
		opts = append(opts, zap.AddCaller(), zap.AddCallerSkip(1))
	}

	// Add stack traces
	if config.StacktraceLevel != "" {
		opts = append(opts, zap.AddStacktrace(toZapLevel(config.StacktraceLevel)))
	}

	// Create zap logger
	zapLog := zap.New(core, opts...)

	return &zapLogger{
		logger: zapLog,
		fields: []Field{},
	}, nil
}

/*****************************************************************************
 * NAME: Debug
 *
 * DESCRIPTION:
 *     Logs a message at debug level
 *
 * PARAMETERS:
 *     msg    - Log message
 *     fields - Additional fields
 *****************************************************************************/
func (l *zapLogger) Debug(msg string, fields ...Field) {
	if ce := l.logger.Check(zapcore.DebugLevel, msg); ce != nil {
		ce.Write(convertFields(append(l.fields, fields...))...)
	}
}

/*****************************************************************************
 * NAME: Info
 *
 * DESCRIPTION:
 *     Logs a message at info level
 *
 * PARAMETERS:
 *     msg    - Log message
 *     fields - Additional fields
 *****************************************************************************/
func (l *zapLogger) Info(msg string, fields ...Field) {
	if ce := l.logger.Check(zapcore.InfoLevel, msg); ce != nil {
		ce.Write(convertFields(append(l.fields, fields...))...)
	}
}

/*****************************************************************************
 * NAME: Warn
 *
 * DESCRIPTION:
 *     Logs a message at warn level
 *
 * PARAMETERS:
 *     msg    - Log message
 *     fields - Additional fields
 *****************************************************************************/
func (l *zapLogger) Warn(msg string, fields ...Field) {
	if ce := l.logger.Check(zapcore.WarnLevel, msg); ce != nil {
		ce.Write(convertFields(append(l.fields, fields...))...)
	}
}

/*****************************************************************************
 * NAME: Error
 *
 * DESCRIPTION:
 *     Logs a message at error level
 *
 * PARAMETERS:
 *     msg    - Log message
 *     fields - Additional fields
 *****************************************************************************/
func (l *zapLogger) Error(msg string, fields ...Field) {
	if ce := l.logger.Check(zapcore.ErrorLevel, msg); ce != nil {
		ce.Write(convertFields(append(l.fields, fields...))...)
	}
}

/*****************************************************************************
 * NAME: Fatal
 *
 * DESCRIPTION:
 *     Logs a message at fatal level, then the program will exit
 *
 * PARAMETERS:
 *     msg    - Log message
 *     fields - Additional fields
 *****************************************************************************/
func (l *zapLogger) Fatal(msg string, fields ...Field) {
	if ce := l.logger.Check(zapcore.FatalLevel, msg); ce != nil {
		ce.Write(convertFields(append(l.fields, fields...))...)
	}
}

/*****************************************************************************
 * NAME: WithFields
 *
 * DESCRIPTION:
 *     Creates a logger with additional fields
 *
 * PARAMETERS:
 *     fields - Fields to add
 *
 * RETURNS:
 *     Logger - New logger with added fields
 *****************************************************************************/
func (l *zapLogger) WithFields(fields ...Field) Logger {
	return &zapLogger{
		logger: l.logger,
		fields: append(l.fields, fields...),
	}
}

/*****************************************************************************
 * NAME: WithModule
 *
 * DESCRIPTION:
 *     Creates a logger with module name
 *
 * PARAMETERS:
 *     module - Module name
 *
 * RETURNS:
 *     Logger - New logger with module field
 *****************************************************************************/
func (l *zapLogger) WithModule(module string) Logger {
	return l.WithFields(Field{Key: "module", Value: module})
}

/*****************************************************************************
 * NAME: WithRequestID
 *
 * DESCRIPTION:
 *     Creates a logger with request ID
 *
 * PARAMETERS:
 *     requestID - Request ID
 *
 * RETURNS:
 *     Logger - New logger with request ID field
 *****************************************************************************/
func (l *zapLogger) WithRequestID(requestID string) Logger {
	return l.WithFields(Field{Key: "request_id", Value: requestID})
}

/*****************************************************************************
 * NAME: WithContext
 *
 * DESCRIPTION:
 *     Creates a logger with context map
 *
 * PARAMETERS:
 *     ctx - Context map
 *
 * RETURNS:
 *     Logger - New logger with context fields
 *****************************************************************************/
func (l *zapLogger) WithContext(ctx map[string]interface{}) Logger {
	fields := make([]Field, 0, len(ctx))
	for k, v := range ctx {
		fields = append(fields, Field{Key: k, Value: v})
	}
	return l.WithFields(fields...)
}

/*****************************************************************************
 * NAME: TraceMethod
 *
 * DESCRIPTION:
 *     Traces method execution time, returns a function that logs the duration
 *     when called
 *
 * PARAMETERS:
 *     methodName - Method name
 *
 * RETURNS:
 *     func() - Function to call at the end of the method
 *****************************************************************************/
func (l *zapLogger) TraceMethod(methodName string) func() {
	start := time.Now()
	return func() {
		l.Debug("Method execution completed",
			Field{Key: "method", Value: methodName},
			Field{Key: "duration_ms", Value: time.Since(start).Milliseconds()},
		)
	}
}

/*****************************************************************************
 * NAME: TraceError
 *
 * DESCRIPTION:
 *     Traces an error, logs error information and caller information
 *
 * PARAMETERS:
 *     err    - Error object
 *     msg    - Error message
 *     fields - Additional fields
 *****************************************************************************/
func (l *zapLogger) TraceError(err error, msg string, fields ...Field) {
	if err == nil {
		return
	}

	// Add error information
	errorFields := append(fields, Field{Key: "error", Value: err.Error()})

	// Add caller information
	_, file, line, ok := runtime.Caller(1)
	if ok {
		errorFields = append(errorFields,
			Field{Key: "caller_file", Value: filepath.Base(file)},
			Field{Key: "caller_line", Value: line},
		)
	}

	l.Error(msg, errorFields...)
}

/*****************************************************************************
 * NAME: Sync
 *
 * DESCRIPTION:
 *     Syncs the logger, ensuring all logs are written
 *
 * RETURNS:
 *     error - Error if sync fails
 *****************************************************************************/
func (l *zapLogger) Sync() error {
	return l.logger.Sync()
}

/*****************************************************************************
 * NAME: Close
 *
 * DESCRIPTION:
 *     Closes the logger, releasing resources
 *
 * RETURNS:
 *     error - Error if close fails
 *****************************************************************************/
func (l *zapLogger) Close() error {
	return l.Sync()
}

/*****************************************************************************
 * NAME: SetLevel
 *
 * DESCRIPTION:
 *     Sets the global log level
 *
 * PARAMETERS:
 *     level - New log level
 *
 * RETURNS:
 *     error - Error if level is invalid
 *****************************************************************************/
func SetLevel(level Level) error {
	// Validate log level
	switch strings.ToUpper(string(level)) {
	case "DEBUG", "INFO", "WARN", "ERROR", "FATAL":
		globalAtomicLevel.SetLevel(toZapLevel(level))
		return nil
	default:
		return fmt.Errorf("invalid log level: %s", level)
	}
}

/*****************************************************************************
 * NAME: GetLevel
 *
 * DESCRIPTION:
 *     Gets the current global log level
 *
 * RETURNS:
 *     Level - Current log level
 *****************************************************************************/
func GetLevel() Level {
	zapLevel := globalAtomicLevel.Level()
	switch zapLevel {
	case zapcore.DebugLevel:
		return LevelDebug
	case zapcore.InfoLevel:
		return LevelInfo
	case zapcore.WarnLevel:
		return LevelWarn
	case zapcore.ErrorLevel:
		return LevelError
	case zapcore.FatalLevel:
		return LevelFatal
	default:
		return LevelInfo
	}
}

// Helper functions

/*****************************************************************************
 * NAME: toZapLevel
 *
 * DESCRIPTION:
 *     Converts Level to zapcore.Level
 *
 * PARAMETERS:
 *     level - Log level
 *
 * RETURNS:
 *     zapcore.Level - Corresponding zap log level
 *****************************************************************************/
func toZapLevel(level Level) zapcore.Level {
	switch strings.ToUpper(string(level)) {
	case "DEBUG":
		return zapcore.DebugLevel
	case "INFO":
		return zapcore.InfoLevel
	case "WARN":
		return zapcore.WarnLevel
	case "ERROR":
		return zapcore.ErrorLevel
	case "FATAL":
		return zapcore.FatalLevel
	default:
		return zapcore.InfoLevel
	}
}

/*****************************************************************************
 * NAME: convertFields
 *
 * DESCRIPTION:
 *     Converts Field slice to zap.Field slice
 *
 * PARAMETERS:
 *     fields - Field slice
 *
 * RETURNS:
 *     []zap.Field - zap.Field slice
 *****************************************************************************/
func convertFields(fields []Field) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))
	for _, f := range fields {
		switch v := f.Value.(type) {
		case string:
			zapFields = append(zapFields, zap.String(f.Key, v))
		case int:
			zapFields = append(zapFields, zap.Int(f.Key, v))
		case int64:
			zapFields = append(zapFields, zap.Int64(f.Key, v))
		case uint32:
			zapFields = append(zapFields, zap.Uint32(f.Key, v))
		case uint64:
			zapFields = append(zapFields, zap.Uint64(f.Key, v))
		case float64:
			zapFields = append(zapFields, zap.Float64(f.Key, v))
		case bool:
			zapFields = append(zapFields, zap.Bool(f.Key, v))
		case time.Time:
			zapFields = append(zapFields, zap.Time(f.Key, v))
		case time.Duration:
			zapFields = append(zapFields, zap.Duration(f.Key, v))
		case error:
			zapFields = append(zapFields, zap.Error(v))
		default:
			zapFields = append(zapFields, zap.Any(f.Key, v))
		}
	}
	return zapFields
}

// Convenience functions for creating common fields

/*****************************************************************************
 * NAME: String
 *
 * DESCRIPTION:
 *     Creates a string field
 *
 * PARAMETERS:
 *     key   - Field name
 *     value - String value
 *
 * RETURNS:
 *     Field - Field object
 *****************************************************************************/
func String(key string, value string) Field {
	return Field{Key: key, Value: value}
}

/*****************************************************************************
 * NAME: Int
 *
 * DESCRIPTION:
 *     Creates an integer field
 *
 * PARAMETERS:
 *     key   - Field name
 *     value - Integer value
 *
 * RETURNS:
 *     Field - Field object
 *****************************************************************************/
func Int(key string, value int) Field {
	return Field{Key: key, Value: value}
}

/*****************************************************************************
 * NAME: Int64
 *
 * DESCRIPTION:
 *     Creates a 64-bit integer field
 *
 * PARAMETERS:
 *     key   - Field name
 *     value - 64-bit integer value
 *
 * RETURNS:
 *     Field - Field object
 *****************************************************************************/
func Int64(key string, value int64) Field {
	return Field{Key: key, Value: value}
}

/*****************************************************************************
 * NAME: Uint32
 *
 * DESCRIPTION:
 *     Creates a 32-bit unsigned integer field
 *
 * PARAMETERS:
 *     key   - Field name
 *     value - 32-bit unsigned integer value
 *
 * RETURNS:
 *     Field - Field object
 *****************************************************************************/
func Uint32(key string, value uint32) Field {
	return Field{Key: key, Value: value}
}

/*****************************************************************************
 * NAME: Uint64
 *
 * DESCRIPTION:
 *     Creates a 64-bit unsigned integer field
 *
 * PARAMETERS:
 *     key   - Field name
 *     value - 64-bit unsigned integer value
 *
 * RETURNS:
 *     Field - Field object
 *****************************************************************************/
func Uint64(key string, value uint64) Field {
	return Field{Key: key, Value: value}
}

/*****************************************************************************
 * NAME: Float64
 *
 * DESCRIPTION:
 *     Creates a floating-point field
 *
 * PARAMETERS:
 *     key   - Field name
 *     value - Floating-point value
 *
 * RETURNS:
 *     Field - Field object
 *****************************************************************************/
func Float64(key string, value float64) Field {
	return Field{Key: key, Value: value}
}

/*****************************************************************************
 * NAME: Bool
 *
 * DESCRIPTION:
 *     Creates a boolean field
 *
 * PARAMETERS:
 *     key   - Field name
 *     value - Boolean value
 *
 * RETURNS:
 *     Field - Field object
 *****************************************************************************/
func Bool(key string, value bool) Field {
	return Field{Key: key, Value: value}
}

/*****************************************************************************
 * NAME: Time
 *
 * DESCRIPTION:
 *     Creates a time field
 *
 * PARAMETERS:
 *     key   - Field name
 *     value - Time value
 *
 * RETURNS:
 *     Field - Field object
 *****************************************************************************/
func Time(key string, value time.Time) Field {
	return Field{Key: key, Value: value}
}

/*****************************************************************************
 * NAME: Duration
 *
 * DESCRIPTION:
 *     Creates a duration field
 *
 * PARAMETERS:
 *     key   - Field name
 *     value - Duration value
 *
 * RETURNS:
 *     Field - Field object
 *****************************************************************************/
func Duration(key string, value time.Duration) Field {
	return Field{Key: key, Value: value}
}

/*****************************************************************************
 * NAME: Error
 *
 * DESCRIPTION:
 *     Creates an error field
 *
 * PARAMETERS:
 *     err - Error object
 *
 * RETURNS:
 *     Field - Field object
 *****************************************************************************/
func ErrorField(err error) Field {
	return Field{Key: "error", Value: err}
}

/*****************************************************************************
 * NAME: Any
 *
 * DESCRIPTION:
 *     Creates a field of any type
 *
 * PARAMETERS:
 *     key   - Field name
 *     value - Any value
 *
 * RETURNS:
 *     Field - Field object
 *****************************************************************************/
func Any(key string, value interface{}) Field {
	return Field{Key: key, Value: value}
}
