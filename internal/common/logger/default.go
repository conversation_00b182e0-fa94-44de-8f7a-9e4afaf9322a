/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      default.go
 *
 * DESCRIPTION :    Default logger implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package logger

import (
	"fmt"
	"os"
	"sync"
)

// Default logger variables
var (
	defaultLogger     Logger
	defaultLoggerOnce sync.Once
	defaultLoggerMu   sync.RWMutex
)

// Default configuration
var defaultConfig = Config{
	Level:           LevelInfo,
	Format:          FormatJSON,
	IncludeCaller:   true,
	StacktraceLevel: LevelError,
	Outputs: []Output{
		{
			Type:   TypeConsole,
			Stderr: false,
		},
	},
}

/*****************************************************************************
 * NAME: Default
 *
 * DESCRIPTION:
 *     Gets the default logger, creating one if it doesn't exist
 *
 * RETURNS:
 *     Logger - Default logger
 *****************************************************************************/
func Default() Logger {
	defaultLoggerOnce.Do(func() {
		logger, err := New(defaultConfig)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Failed to create default logger: %v\n", err)
			os.Exit(1)
		}
		defaultLogger = logger
	})

	//defaultLoggerMu.RLock()
	//defer defaultLoggerMu.RUnlock()
	return defaultLogger
}

/*****************************************************************************
 * NAME: SetDefault
 *
 * DESCRIPTION:
 *     Sets the default logger
 *
 * PARAMETERS:
 *     logger - Logger to set as default
 *****************************************************************************/
func SetDefault(logger Logger) {
	//defaultLoggerMu.Lock()
	//defer defaultLoggerMu.Unlock()
	defaultLogger = logger
}

/*****************************************************************************
 * NAME: InitDefault
 *
 * DESCRIPTION:
 *     Initializes the default logger with the specified configuration
 *
 * PARAMETERS:
 *     config - Logger configuration
 *
 * RETURNS:
 *     error - Error if initialization fails
 *****************************************************************************/
func InitDefault(config Config) error {
	logger, err := New(config)
	if err != nil {
		return err
	}

	SetDefault(logger)
	return nil
}

// Global convenience functions

/*****************************************************************************
 * NAME: Debug
 *
 * DESCRIPTION:
 *     Logs a message at debug level using the default logger
 *
 * PARAMETERS:
 *     msg    - Log message
 *     fields - Additional fields
 *****************************************************************************/
func Debug(msg string, fields ...Field) {
	Default().Debug(msg, fields...)
}

/*****************************************************************************
 * NAME: Info
 *
 * DESCRIPTION:
 *     Logs a message at info level using the default logger
 *
 * PARAMETERS:
 *     msg    - Log message
 *     fields - Additional fields
 *****************************************************************************/
func Info(msg string, fields ...Field) {
	Default().Info(msg, fields...)
}

/*****************************************************************************
 * NAME: Warn
 *
 * DESCRIPTION:
 *     Logs a message at warn level using the default logger
 *
 * PARAMETERS:
 *     msg    - Log message
 *     fields - Additional fields
 *****************************************************************************/
func Warn(msg string, fields ...Field) {
	Default().Warn(msg, fields...)
}

/*****************************************************************************
 * NAME: Error
 *
 * DESCRIPTION:
 *     Logs a message at error level using the default logger
 *
 * PARAMETERS:
 *     msg    - Log message
 *     fields - Additional fields
 *****************************************************************************/
func Error(msg string, fields ...Field) {
	Default().Error(msg, fields...)
}

/*****************************************************************************
 * NAME: Fatal
 *
 * DESCRIPTION:
 *     Logs a message at fatal level using the default logger
 *
 * PARAMETERS:
 *     msg    - Log message
 *     fields - Additional fields
 *****************************************************************************/
func Fatal(msg string, fields ...Field) {
	Default().Fatal(msg, fields...)
}

/*****************************************************************************
 * NAME: WithFields
 *
 * DESCRIPTION:
 *     Creates a logger with additional fields using the default logger
 *
 * PARAMETERS:
 *     fields - Fields to add
 *
 * RETURNS:
 *     Logger - New logger with added fields
 *****************************************************************************/
func WithFields(fields ...Field) Logger {
	return Default().WithFields(fields...)
}

/*****************************************************************************
 * NAME: WithModule
 *
 * DESCRIPTION:
 *     Creates a logger with module name using the default logger
 *
 * PARAMETERS:
 *     module - Module name
 *
 * RETURNS:
 *     Logger - New logger with module field
 *****************************************************************************/
func WithModule(module string) Logger {
	return Default().WithModule(module)
}

/*****************************************************************************
 * NAME: WithRequestID
 *
 * DESCRIPTION:
 *     Creates a logger with request ID using the default logger
 *
 * PARAMETERS:
 *     requestID - Request ID
 *
 * RETURNS:
 *     Logger - New logger with request ID field
 *****************************************************************************/
func WithRequestID(requestID string) Logger {
	return Default().WithRequestID(requestID)
}

/*****************************************************************************
 * NAME: WithContext
 *
 * DESCRIPTION:
 *     Creates a logger with context map using the default logger
 *
 * PARAMETERS:
 *     ctx - Context map
 *
 * RETURNS:
 *     Logger - New logger with context fields
 *****************************************************************************/
func WithContext(ctx map[string]interface{}) Logger {
	return Default().WithContext(ctx)
}

/*****************************************************************************
 * NAME: TraceMethod
 *
 * DESCRIPTION:
 *     Traces method execution time using the default logger
 *
 * PARAMETERS:
 *     methodName - Method name
 *
 * RETURNS:
 *     func() - Function to call at the end of the method
 *****************************************************************************/
func TraceMethod(methodName string) func() {
	return Default().TraceMethod(methodName)
}

/*****************************************************************************
 * NAME: TraceError
 *
 * DESCRIPTION:
 *     Traces an error using the default logger
 *
 * PARAMETERS:
 *     err    - Error object
 *     msg    - Error message
 *     fields - Additional fields
 *****************************************************************************/
func TraceError(err error, msg string, fields ...Field) {
	Default().TraceError(err, msg, fields...)
}

/*****************************************************************************
 * NAME: Sync
 *
 * DESCRIPTION:
 *     Syncs the default logger
 *
 * RETURNS:
 *     error - Error if sync fails
 *****************************************************************************/
func Sync() error {
	return Default().Sync()
}

/*****************************************************************************
 * NAME: Close
 *
 * DESCRIPTION:
 *     Closes the default logger
 *
 * RETURNS:
 *     error - Error if close fails
 *****************************************************************************/
func Close() error {
	return Default().Close()
}
