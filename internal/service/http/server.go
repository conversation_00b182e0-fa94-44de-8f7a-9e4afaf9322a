/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      server.go
 *
 * DESCRIPTION :    HTTP server implementation for the VPN client
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

// Package http provides HTTP server implementation for the VPN client.
package http

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gorilla/mux"

	"mobile/internal/common/logger"
	"mobile/internal/service/websocket"
)

/*****************************************************************************
 * NAME: Server
 *
 * DESCRIPTION:
 *     Represents an HTTP server
 *
 * FIELDS:
 *     server - HTTP server instance
 *     router - HTTP router
 *     config - Server configuration
 *     log    - Logger instance
 *     deps   - Service dependencies
 *     ws     - WebSocket handler for sending notifications
 *****************************************************************************/
type Server struct {
	server *http.Server
	router *mux.Router
	config *Config
	log    logger.Logger
	deps   *Dependencies
	ws     *websocket.Handler // WebSocket handler for sending notifications
}

/*****************************************************************************
 * NAME: Config
 *
 * DESCRIPTION:
 *     Represents the configuration for the HTTP server
 *
 * FIELDS:
 *     Host            - Server host address
 *     Port            - Server port number
 *     ReadTimeout     - Request read timeout
 *     WriteTimeout    - Response write timeout
 *     ShutdownTimeout - Server shutdown timeout
 *****************************************************************************/
type Config struct {
	Host            string
	Port            int
	ReadTimeout     time.Duration
	WriteTimeout    time.Duration
	ShutdownTimeout time.Duration
}

/*****************************************************************************
 * NAME: DefaultConfig
 *
 * DESCRIPTION:
 *     Returns the default configuration for the HTTP server
 *
 * PARAMETERS:
 *     None
 *
 * RETURNS:
 *     *Config - Default server configuration
 *****************************************************************************/
func DefaultConfig() *Config {
	return &Config{
		Host:            "localhost",
		Port:            8080,
		ReadTimeout:     15 * time.Second,
		WriteTimeout:    15 * time.Second,
		ShutdownTimeout: 5 * time.Second,
	}
}

/*****************************************************************************
 * NAME: NewServer
 *
 * DESCRIPTION:
 *     Creates a new HTTP server with the given configuration
 *
 * PARAMETERS:
 *     config    - Server configuration
 *     log       - Logger instance
 *     wsHandler - WebSocket handler
 *
 * RETURNS:
 *     *Server - New HTTP server instance
 *****************************************************************************/
func NewServer(config *Config, log logger.Logger, wsHandler *websocket.Handler) *Server {
	if config == nil {
		config = DefaultConfig()
	}

	router := mux.NewRouter()

	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", config.Host, config.Port),
		Handler:      router,
		ReadTimeout:  config.ReadTimeout,
		WriteTimeout: config.WriteTimeout,
	}

	return &Server{
		server: server,
		router: router,
		config: config,
		log:    log,
		ws:     wsHandler,
	}
}

/*****************************************************************************
 * NAME: Start
 *
 * DESCRIPTION:
 *     Starts the HTTP server
 *
 * PARAMETERS:
 *     s - HTTP server instance
 *
 * RETURNS:
 *     error - Error if server fails to start
 *****************************************************************************/
func (s *Server) Start() error {
	s.setupRoutes()
	s.log.Info("HTTP server listening", logger.String("address", s.server.Addr))
	s.log.Info("HTTP API is ready to handle UI requests")
	return s.server.ListenAndServe()
}

/*****************************************************************************
 * NAME: Stop
 *
 * DESCRIPTION:
 *     Stops the HTTP server
 *
 * PARAMETERS:
 *     s   - HTTP server instance
 *     ctx - Context for shutdown timeout
 *
 * RETURNS:
 *     error - Error if server fails to stop gracefully
 *****************************************************************************/
func (s *Server) Stop(ctx context.Context) error {
	s.log.Info("Stopping HTTP server")
	return s.server.Shutdown(ctx)
}

/*****************************************************************************
 * NAME: setupRoutes
 *
 * DESCRIPTION:
 *     Sets up the routes for the HTTP server
 *
 * PARAMETERS:
 *     s - HTTP server instance
 *****************************************************************************/
func (s *Server) setupRoutes() {
	// Apply middleware
	s.router.Use(s.loggingMiddleware)
	s.router.Use(s.recoveryMiddleware)
	s.router.Use(s.corsMiddleware)

	// API routes
	api := s.router.PathPrefix("/api").Subrouter()

	// Authentication routes
	api.HandleFunc("/login", s.handleLogin).Methods("POST")

	// Server management routes
	api.HandleFunc("/servers", s.handleGetServers).Methods("GET")
	api.HandleFunc("/servers/ping", s.handlePingServers).Methods("POST")

	// Connection management routes
	api.HandleFunc("/connect", s.handleConnect).Methods("POST")
	api.HandleFunc("/disconnect", s.handleDisconnect).Methods("POST")
	api.HandleFunc("/status", s.handleGetStatus).Methods("GET")
	api.HandleFunc("/interface", s.handleGetInterface).Methods("GET")
	// Note: Traffic statistics are pushed via WebSocket in real-time, no HTTP API provided

	// Settings management routes
	api.HandleFunc("/settings/routing", s.handleGetRoutingSettings).Methods("GET")
	api.HandleFunc("/settings/routing", s.handleSetRoutingSettings).Methods("POST")
	api.HandleFunc("/servers/provider", s.handleSetServerProvider).Methods("POST")

	// System management routes
	api.HandleFunc("/health", s.handleHealthCheck).Methods("GET")
	api.HandleFunc("/shutdown", s.handleShutdown).Methods("POST")
}

/*****************************************************************************
 * NAME: Router
 *
 * DESCRIPTION:
 *     Returns the router for the HTTP server
 *
 * PARAMETERS:
 *     s - HTTP server instance
 *
 * RETURNS:
 *     *mux.Router - HTTP router instance
 *****************************************************************************/
func (s *Server) Router() *mux.Router {
	return s.router
}
