/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      middleware.go
 *
 * DESCRIPTION :    HTTP middleware functions for logging, recovery, and CORS
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package http

import (
	"net/http"
	"runtime/debug"
	"time"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: loggingMiddleware
 *
 * DESCRIPTION:
 *     Logs information about each HTTP request
 *
 * PARAMETERS:
 *     s    - HTTP server instance
 *     next - Next handler in the chain
 *
 * RETURNS:
 *     http.Handler - Middleware handler
 *****************************************************************************/
func (s *Server) loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		// Create a custom response writer to capture the status code
		rw := newResponseWriter(w)

		// Call the next handler
		next.ServeHTTP(rw, r)

		// Log the request
		s.log.Info("HTTP request",
			logger.String("method", r.Method),
			logger.String("path", r.URL.Path),
			logger.Int("status", rw.status),
			logger.String("duration", time.Since(start).String()),
			logger.String("remote_addr", r.RemoteAddr),
			logger.String("user_agent", r.UserAgent()),
		)
	})
}

/*****************************************************************************
 * NAME: recoveryMiddleware
 *
 * DESCRIPTION:
 *     Recovers from panics and logs the error
 *
 * PARAMETERS:
 *     s    - HTTP server instance
 *     next - Next handler in the chain
 *
 * RETURNS:
 *     http.Handler - Middleware handler
 *****************************************************************************/
func (s *Server) recoveryMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if err := recover(); err != nil {
				s.log.Error("Panic in HTTP handler",
					logger.Any("error", err),
					logger.String("stack", string(debug.Stack())),
					logger.String("path", r.URL.Path),
					logger.String("method", r.Method),
				)

				// Return a 500 Internal Server Error
				http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			}
		}()

		next.ServeHTTP(w, r)
	})
}

/*****************************************************************************
 * NAME: corsMiddleware
 *
 * DESCRIPTION:
 *     Adds CORS headers to the response
 *
 * PARAMETERS:
 *     s    - HTTP server instance
 *     next - Next handler in the chain
 *
 * RETURNS:
 *     http.Handler - Middleware handler
 *****************************************************************************/
func (s *Server) corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Set CORS headers
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		// Handle preflight requests
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

/*****************************************************************************
 * NAME: responseWriter
 *
 * DESCRIPTION:
 *     Custom response writer that captures the status code
 *
 * FIELDS:
 *     ResponseWriter - Embedded HTTP response writer
 *     status         - HTTP status code
 *****************************************************************************/
type responseWriter struct {
	http.ResponseWriter
	status int
}

/*****************************************************************************
 * NAME: newResponseWriter
 *
 * DESCRIPTION:
 *     Creates a new responseWriter
 *
 * PARAMETERS:
 *     w - HTTP response writer to wrap
 *
 * RETURNS:
 *     *responseWriter - New response writer instance
 *****************************************************************************/
func newResponseWriter(w http.ResponseWriter) *responseWriter {
	return &responseWriter{
		ResponseWriter: w,
		status:         http.StatusOK,
	}
}

/*****************************************************************************
 * NAME: WriteHeader
 *
 * DESCRIPTION:
 *     Captures the status code and calls the underlying WriteHeader
 *
 * PARAMETERS:
 *     rw   - Response writer instance
 *     code - HTTP status code
 *****************************************************************************/
func (rw *responseWriter) WriteHeader(code int) {
	rw.status = code
	rw.ResponseWriter.WriteHeader(code)
}
