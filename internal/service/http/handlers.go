/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      handlers.go
 *
 * DESCRIPTION :    HTTP request handlers for the VPN service API
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package http

import (
	"encoding/json"
	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
	"mobile/internal/connection/manager"
	"mobile/internal/connection/server"
	"mobile/internal/platform/tun"
	"mobile/internal/service/api"
	"mobile/internal/service/config" // Still need config package for DecryptCredential function
	"net/http"
	"os"
	"time"
)

/*****************************************************************************
 * NAME: Dependencies
 *
 * DESCRIPTION:
 *     Represents the dependencies for the HTTP handlers
 *
 * FIELDS:
 *     ConnectionManager - Connection manager instance
 *     ServerManager     - Server manager instance
 *****************************************************************************/
type Dependencies struct {
	ConnectionManager *manager.Manager
	ServerManager     *server.Manager
}

/*****************************************************************************
 * NAME: SetDependencies
 *
 * DESCRIPTION:
 *     Sets the dependencies for the HTTP handlers
 *
 * PARAMETERS:
 *     deps - Dependencies structure containing required managers
 *****************************************************************************/
func (s *Server) SetDependencies(deps *Dependencies) {
	s.deps = deps
}

// deps holds the dependencies for the HTTP handlers.
var deps *Dependencies

/*****************************************************************************
 * NAME: writeJSON
 *
 * DESCRIPTION:
 *     Writes JSON response to HTTP response stream
 *
 * PARAMETERS:
 *     w      - HTTP response writer
 *     status - HTTP status code
 *     data   - Data to serialize
 *****************************************************************************/
func writeJSON(w http.ResponseWriter, status int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)

	if err := json.NewEncoder(w).Encode(data); err != nil {
		// If we can't encode the response, log the error and write a plain text error
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
	}
}

/*****************************************************************************
 * NAME: readJSON
 *
 * DESCRIPTION:
 *     Reads JSON data from HTTP request body into specified structure
 *
 * PARAMETERS:
 *     r - HTTP request
 *     v - Target structure pointer
 *
 * RETURNS:
 *     error - Parsing error
 *****************************************************************************/
func readJSON(r *http.Request, v interface{}) error {
	return json.NewDecoder(r.Body).Decode(v)
}

/*****************************************************************************
 * NAME: validateDependencies
 *
 * DESCRIPTION:
 *     Validates whether service dependencies are initialized
 *
 * PARAMETERS:
 *     s              - HTTP server instance
 *     needConnection - Whether connection manager is needed
 *     needServer     - Whether server manager is needed
 *
 * RETURNS:
 *     error - Validation error
 *****************************************************************************/
func (s *Server) validateDependencies(needConnection, needServer bool) error {
	if s.deps == nil {
		return errors.NewWithCode(errors.CodeInternal, "dependencies not initialized")
	}

	if needConnection && s.deps.ConnectionManager == nil {
		return errors.NewWithCode(errors.CodeInternal, "connection manager not initialized")
	}

	if needServer && s.deps.ServerManager == nil {
		return errors.NewWithCode(errors.CodeInternal, "server manager not initialized")
	}

	return nil
}

/*****************************************************************************
 * NAME: writeErrorResponse
 *
 * DESCRIPTION:
 *     Writes error response
 *
 * PARAMETERS:
 *     w         - HTTP response writer
 *     status    - HTTP status code
 *     code      - Error code
 *     errorType - Error type
 *     message   - Error message
 *****************************************************************************/
func writeErrorResponse(w http.ResponseWriter, status int, code errors.ErrorCode, errorType, message string) {
	writeJSON(w, status, api.NewErrorResponse(
		&api.Error{
			Code:    int(code),
			Type:    errorType,
			Message: message,
		},
		message,
	))
}

/*****************************************************************************
 * NAME: convertServerToAPI
 *
 * DESCRIPTION:
 *     Converts internal server model to API model
 *
 * PARAMETERS:
 *     server - Internal server model
 *
 * RETURNS:
 *     *api.ServerInfo - API server information
 *****************************************************************************/
func convertServerToAPI(server *server.Server) *api.ServerInfo {
	if server == nil {
		return nil
	}

	return &api.ServerInfo{
		ID:         server.ID,
		Name:       server.Name,
		NameEn:     server.NameEn,
		ServerName: server.ServerName,
		ServerPort: server.ServerPort,
		Ping:       server.Ping,
		IsAuto:     server.IsAuto,
		Status:     server.Status,
		IsDefault:  false,
	}
}

/*****************************************************************************
 * NAME: convertServersToAPI
 *
 * DESCRIPTION:
 *     Converts internal server list to API model list
 *
 * PARAMETERS:
 *     servers - Internal server list
 *
 * RETURNS:
 *     []*api.ServerInfo - API server information list
 *****************************************************************************/
func convertServersToAPI(servers []server.Server) []*api.ServerInfo {
	serverInfos := make([]*api.ServerInfo, 0, len(servers))
	for _, srv := range servers {
		serverInfos = append(serverInfos, convertServerToAPI(&srv))
	}
	return serverInfos
}

/*****************************************************************************
 * NAME: broadcastStatusEvent
 *
 * DESCRIPTION:
 *     Broadcasts status event to WebSocket clients
 *
 * PARAMETERS:
 *     s      - HTTP server instance
 *     status - Status information
 *****************************************************************************/
func (s *Server) broadcastStatusEvent(status *api.StatusInfo) {
	if s.ws != nil {
		s.ws.Broadcast(api.NewStatusEvent(status))
	}
}

/*****************************************************************************
 * NAME: broadcastErrorEvent
 *
 * DESCRIPTION:
 *     Broadcasts error event to WebSocket clients
 *
 * PARAMETERS:
 *     s         - HTTP server instance
 *     code      - Error code
 *     message   - Error message
 *     errorType - Error type
 *****************************************************************************/
func (s *Server) broadcastErrorEvent(code int, message, errorType string) {
	if s.ws != nil {
		s.ws.Broadcast(api.NewErrorEvent(code, message, errorType))
	}
}

/*****************************************************************************
 * NAME: handleLogin
 *
 * DESCRIPTION:
 *     Handles user login request, including authentication and auto-connection
 *
 * PARAMETERS:
 *     w - HTTP response writer
 *     r - HTTP request
 *****************************************************************************/
func (s *Server) handleLogin(w http.ResponseWriter, r *http.Request) {
	// Parse and validate request
	req, err := s.parseAndValidateLoginRequest(r)
	if err != nil {
		s.handleLoginError(w, err, "Failed to parse login request")
		return
	}

	// Validate dependencies
	if err := s.validateDependencies(true, true); err != nil {
		s.log.Error("Dependencies not initialized", logger.ErrorField(err))
		writeErrorResponse(w, http.StatusInternalServerError, errors.CodeInternal, "internal_error", "Internal server error")
		return
	}

	// Execute ping operation before selecting best server and wait for completion
	s.log.Info("Starting ping operation before selecting best auto server")
	pingTimeout := 30 * time.Second // Set 30 second timeout
	if err := s.deps.ServerManager.PingServersAndWait(pingTimeout); err != nil {
		s.log.Warn("Ping operation failed or timed out, but continuing with server selection", logger.ErrorField(err))
		// Don't return error, continue with server selection even if ping fails
	} else {
		s.log.Info("Ping operation completed successfully")
	}

	// Get best auto routing server (server with lowest latency in smart routing)
	server, err := s.deps.ServerManager.SelectBestAutoServer()
	if err != nil {
		s.log.Error("Failed to get best auto server", logger.ErrorField(err))
		// Use more specific error code and type
		writeErrorResponse(w, http.StatusServiceUnavailable, errors.CodeNotFound, "no_auto_servers", "No auto servers available")
		return
	}

	// Convert to API model
	bestServer := convertServerToAPI(server)
	if bestServer == nil {
		s.log.Warn("No best server available for login")
		writeErrorResponse(w, http.StatusInternalServerError, errors.CodeNotFound, "no_servers", "No servers available")
		return
	}

	// Authenticate first, don't establish connection
	s.log.Info("Authenticating user", logger.String("username", req.Username), logger.String("server", server.ServerName))

	// Send authenticating WebSocket notification
	s.broadcastStatusEvent(&api.StatusInfo{
		Status:  api.StatusConnecting,
		Message: "Authenticating with " + bestServer.Name,
		Server:  bestServer,
	})

	// Execute authentication
	_, err = s.performAuthentication(req, server)
	if err != nil {
		// Send authentication failed WebSocket notification
		s.broadcastStatusEvent(&api.StatusInfo{
			Status:  api.StatusError,
			Message: "Authentication failed: " + err.Error(),
			Server:  bestServer,
		})
		s.broadcastErrorEvent(int(errors.GetCode(err)), err.Error(), "auth_failed")

		s.handleLoginError(w, err, "Authentication failed")
		return
	}

	// Authentication successful
	s.log.Info("Authentication successful", logger.String("username", req.Username), logger.String("server", server.ServerName))

	// Prepare response
	resp := api.LoginResponse{
		BestServer: bestServer,
	}

	// Send login successful WebSocket notification
	s.broadcastStatusEvent(&api.StatusInfo{
		Status:  api.StatusDisconnected,
		Message: "Authentication successful, preparing to connect",
		Server:  bestServer,
	})

	// Return login successful response
	writeJSON(w, http.StatusOK, api.NewResponse(resp, "Login successful"))

	// Note: Auto-connect removed to maintain consistency with iOS/Android platforms
	// UI will initiate connection after login using the returned best_server information
}

/*****************************************************************************
 * NAME: handleGetServers
 *
 * DESCRIPTION:
 *     Handles get server list request
 *
 * PARAMETERS:
 *     w - HTTP response writer
 *     r - HTTP request
 *****************************************************************************/
func (s *Server) handleGetServers(w http.ResponseWriter, r *http.Request) {
	// Validate dependencies
	if err := s.validateDependencies(false, true); err != nil {
		s.log.Error("Dependencies validation failed", logger.ErrorField(err))
		writeErrorResponse(w, http.StatusInternalServerError, errors.CodeInternal, "internal_error", "Internal server error")
		return
	}

	// Get server list
	servers := s.deps.ServerManager.GetServers()

	// Convert to API model
	serverInfos := convertServersToAPI(servers)

	writeJSON(w, http.StatusOK, api.NewResponse(serverInfos, ""))
}

/*****************************************************************************
 * NAME: handlePingServers
 *
 * DESCRIPTION:
 *     Handles server latency test request
 *
 * PARAMETERS:
 *     w - HTTP response writer
 *     r - HTTP request
 *****************************************************************************/
func (s *Server) handlePingServers(w http.ResponseWriter, r *http.Request) {
	// Validate dependencies
	if err := s.validateDependencies(false, true); err != nil {
		s.log.Error("Dependencies validation failed", logger.ErrorField(err))
		writeErrorResponse(w, http.StatusInternalServerError, errors.CodeInternal, "internal_error", "Internal server error")
		return
	}

	// Check if ping operation is already in progress
	if s.deps.ServerManager.IsPingInProgress() {
		s.log.Debug("Ping operation already in progress, ignoring duplicate request")
		writeJSON(w, http.StatusOK, api.NewResponse(nil, "Ping operation already in progress"))
		return
	}

	// Send server latency testing WebSocket notification
	if s.ws != nil {
		s.ws.Broadcast(api.NewWebSocketEvent(api.EventPingStart, nil))
	}

	// Start server latency test in background
	go s.handlePingServersBackground()

	writeJSON(w, http.StatusOK, api.NewResponse(nil, "Server ping started"))
}

/*****************************************************************************
 * NAME: handleConnect
 *
 * DESCRIPTION:
 *     Handles the connect request (simplified version without authentication)
 *
 * PARAMETERS:
 *     w - HTTP response writer
 *     r - HTTP request
 *****************************************************************************/
func (s *Server) handleConnect(w http.ResponseWriter, r *http.Request) {
	if s.deps == nil || s.deps.ConnectionManager == nil {
		writeJSON(w, http.StatusInternalServerError, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInternal),
				Type:    "internal_error",
				Message: "Connection manager not initialized",
			},
			"Internal server error",
		))
		return
	}

	var req api.ConnectRequest
	if err := readJSON(r, &req); err != nil {
		s.log.Error("Failed to decode connect request", logger.ErrorField(err))
		writeJSON(w, http.StatusBadRequest, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInvalidRequest),
				Type:    "invalid_request",
				Message: "Invalid request body",
			},
			"Invalid request body",
		))
		return
	}

	// Validate the request
	if req.ServerID == "" {
		writeJSON(w, http.StatusBadRequest, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInvalidRequest),
				Type:    "invalid_request",
				Message: "Server ID is required",
			},
			"Server ID is required",
		))
		return
	}

	if req.Username == "" {
		writeJSON(w, http.StatusBadRequest, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInvalidRequest),
				Type:    "invalid_request",
				Message: "Username is required",
			},
			"Username is required",
		))
		return
	}

	if req.Password == "" {
		writeJSON(w, http.StatusBadRequest, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInvalidRequest),
				Type:    "invalid_request",
				Message: "Password is required",
			},
			"Password is required",
		))
		return
	}

	// Get server information
	server, err := s.deps.ServerManager.GetServer(req.ServerID)
	if err != nil {
		s.log.Error("Failed to get server info", logger.ErrorField(err), logger.String("server_id", req.ServerID))
		writeJSON(w, http.StatusBadRequest, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.CodeNotFound),
				Type:    "server_not_found",
				Message: "Server not found",
			},
			"Server not found",
		))
		return
	}

	// Send connecting WebSocket notification
	if s.ws != nil {
		// Convert to API model
		serverInfo := &api.ServerInfo{
			ID:         server.ID,
			Name:       server.Name,
			NameEn:     server.NameEn,
			ServerName: server.ServerName,
			ServerPort: server.ServerPort,
			Ping:       server.Ping,
			IsAuto:     server.IsAuto,
			Status:     server.Status,
			IsDefault:  false,
		}

		s.ws.Broadcast(api.NewStatusEvent(&api.StatusInfo{
			Status:  api.StatusConnecting,
			Message: "connecting",
			Server:  serverInfo,
		}))
	}

	// Set current server information first, ensure connection manager has complete server information
	s.deps.ConnectionManager.SetCurrentServer(server)

	// Decrypt password (Windows sends encrypted password, iOS/Android send plain text)
	decryptedPassword, err := config.SimpleDecryptCredential(req.Password)
	if err != nil {
		s.log.Error("Failed to decrypt password for connect", logger.ErrorField(err))
		writeJSON(w, http.StatusBadRequest, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInvalidRequest),
				Type:    "invalid_request",
				Message: "Invalid password format",
			},
			"Invalid password format",
		))
		return
	}

	// Set credentials in connection manager
	s.deps.ConnectionManager.SetCredentials(req.Username, decryptedPassword)

	// Perform authentication and connection using provided credentials
	s.log.Info("Connecting using provided credentials",
		logger.String("username", req.Username),
		logger.String("server_id", req.ServerID))

	// Use AuthAndConnect (it will use the credentials we just set)
	if err := s.deps.ConnectionManager.AuthAndConnect(server.ServerName, server.ServerPort); err != nil {
		s.log.Error("Failed to connect", logger.ErrorField(err), logger.String("server_id", req.ServerID))

		// Send connection failed WebSocket notification
		if s.ws != nil {
			// Convert to API model
			serverInfo := &api.ServerInfo{
				ID:         server.ID,
				Name:       server.Name,
				NameEn:     server.NameEn,
				ServerName: server.ServerName,
				ServerPort: server.ServerPort,
				Ping:       server.Ping,
				IsAuto:     server.IsAuto,
				Status:     server.Status,
				IsDefault:  false,
			}

			// Send error status directly, don't send disconnected
			s.ws.Broadcast(api.NewStatusEvent(&api.StatusInfo{
				Status:  api.StatusError,
				Message: "connection_failed",
				Server:  serverInfo,
			}))

			s.ws.Broadcast(api.NewErrorEvent(int(errors.ErrConnectionFailed), err.Error(), "connection_failed"))
		}

		// Return error response
		writeJSON(w, http.StatusInternalServerError, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrConnectionFailed),
				Type:    "connection_failed",
				Message: err.Error(),
			},
			"Connection failed",
		))
		return
	}

	s.log.Info("Successfully connected", logger.String("server_id", req.ServerID))

	// Get interface information after successful connection
	interfaceInfo, err := s.deps.ConnectionManager.GetInterfaceInfo()
	if err != nil {
		s.log.Warn("Failed to get interface info after connection", logger.ErrorField(err))
		// Don't fail the entire response for interface info error
	}

	// Convert to API model
	serverInfo := &api.ServerInfo{
		ID:         server.ID,
		Name:       server.Name,
		NameEn:     server.NameEn,
		ServerName: server.ServerName,
		ServerPort: server.ServerPort,
		Ping:       server.Ping,
		IsAuto:     server.IsAuto,
		Status:     server.Status,
		IsDefault:  false,
	}

	// Create connect response with interface information
	var apiInterfaceInfo *api.InterfaceInfo
	if interfaceInfo != nil {
		apiInterfaceInfo = &api.InterfaceInfo{
			InterfaceName:  interfaceInfo.InterfaceName,
			LocalIP:        interfaceInfo.LocalIP,
			TunIP:          interfaceInfo.TunIP,
			InterfaceIndex: interfaceInfo.InterfaceIndex,
			InterfaceLUID:  interfaceInfo.InterfaceLUID,
		}
	}

	connectResponse := &api.ConnectResponse{
		Server:        serverInfo,
		InterfaceInfo: apiInterfaceInfo,
	}

	// Send connected status WebSocket notification
	if s.ws != nil {
		// Get current status
		status := s.deps.ConnectionManager.GetStatus()

		// Calculate connection time (Unix timestamp)
		var connectedTime int64
		if !status.ConnectedTime.IsZero() {
			connectedTime = status.ConnectedTime.Unix()
		}

		// Create status information and broadcast
		s.ws.Broadcast(api.NewStatusEvent(&api.StatusInfo{
			Status:        api.StatusConnected,
			Message:       "connected",
			Server:        serverInfo,
			ConnectedTime: connectedTime,
		}))
	}

	writeJSON(w, http.StatusOK, api.NewResponse(connectResponse, "Connection successful"))
}

/*****************************************************************************
 * NAME: handleDisconnect
 *
 * DESCRIPTION:
 *     Handles the disconnect request by initiating VPN disconnection
 *
 * PARAMETERS:
 *     w - HTTP response writer
 *     r - HTTP request
 *****************************************************************************/
func (s *Server) handleDisconnect(w http.ResponseWriter, r *http.Request) {
	if s.deps == nil || s.deps.ConnectionManager == nil {
		writeJSON(w, http.StatusInternalServerError, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInternal),
				Type:    "internal_error",
				Message: "Connection manager not initialized",
			},
			"Internal server error",
		))
		return
	}

	// Send disconnecting WebSocket notification
	if s.ws != nil {
		// Get current status
		status := s.deps.ConnectionManager.GetStatus()

		// Convert to API model
		statusInfo := &api.StatusInfo{
			Status:  api.StatusDisconnecting,
			Message: "Disconnecting",
		}

		if status.Server != nil {
			statusInfo.Server = &api.ServerInfo{
				ID:         status.Server.ID,
				Name:       status.Server.Name,
				NameEn:     status.Server.NameEn,
				ServerName: status.Server.ServerName,
				ServerPort: status.Server.ServerPort,
			}
		}

		s.ws.Broadcast(api.NewStatusEvent(statusInfo))
	}

	// Start disconnecting in the background
	go func() {
		if err := s.deps.ConnectionManager.Disconnect(); err != nil {
			s.log.Error("Failed to disconnect", logger.ErrorField(err))

			// Send disconnection failed WebSocket notification
			if s.ws != nil {
				s.ws.Broadcast(api.NewStatusEvent(&api.StatusInfo{
					Status:  api.StatusError,
					Message: "Failed to disconnect: " + err.Error(),
				}))

				s.ws.Broadcast(api.NewErrorEvent(int(errors.ErrDisconnectionFailed), err.Error(), "disconnection_failed"))
			}
		} else {
			s.log.Info("Successfully disconnected")

			// Send disconnection successful WebSocket notification
			if s.ws != nil {
				s.ws.Broadcast(api.NewStatusEvent(&api.StatusInfo{
					Status:  api.StatusDisconnected,
					Message: "user_disconnect", // Explicitly identify as user-initiated disconnect
				}))
			}
		}
	}()

	writeJSON(w, http.StatusOK, api.NewResponse(nil, "Disconnection started"))
}

/*****************************************************************************
 * NAME: handleGetStatus
 *
 * DESCRIPTION:
 *     Handles the get status request by returning current VPN connection status
 *
 * PARAMETERS:
 *     w - HTTP response writer
 *     r - HTTP request
 *****************************************************************************/
func (s *Server) handleGetStatus(w http.ResponseWriter, r *http.Request) {
	if s.deps == nil || s.deps.ConnectionManager == nil {
		writeJSON(w, http.StatusInternalServerError, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInternal),
				Type:    "internal_error",
				Message: "Connection manager not initialized",
			},
			"Internal server error",
		))
		return
	}

	status := s.deps.ConnectionManager.GetStatus()

	// Convert to API model
	var connectedTime int64
	if !status.ConnectedTime.IsZero() {
		connectedTime = status.ConnectedTime.Unix()
	}

	statusInfo := &api.StatusInfo{
		Status:        status.Status,
		Message:       status.Message,
		ConnectedTime: connectedTime,
	}

	if status.Server != nil {
		statusInfo.Server = &api.ServerInfo{
			ID:         status.Server.ID,
			Name:       status.Server.Name,
			NameEn:     status.Server.NameEn,
			ServerName: status.Server.ServerName,
			ServerPort: status.Server.ServerPort,
		}
	}

	writeJSON(w, http.StatusOK, api.NewResponse(statusInfo, ""))
}

/*****************************************************************************
 * NAME: handleGetInterface
 *
 * DESCRIPTION:
 *     Handles the get interface info request by returning network interface information
 *
 * PARAMETERS:
 *     w - HTTP response writer
 *     r - HTTP request
 *****************************************************************************/
func (s *Server) handleGetInterface(w http.ResponseWriter, r *http.Request) {
	// Debug: Log API call
	s.log.Error("API: handleGetInterface called")

	if s.deps == nil || s.deps.ConnectionManager == nil {
		s.log.Error("API: Connection manager not available")
		writeJSON(w, http.StatusInternalServerError, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInternal),
				Type:    "internal_error",
				Message: "Connection manager not initialized",
			},
			"Internal server error",
		))
		return
	}

	// Get interface information
	info, err := s.deps.ConnectionManager.GetInterfaceInfo()
	if err != nil {
		s.log.Error("API: Failed to get interface info", logger.ErrorField(err))
		writeJSON(w, http.StatusInternalServerError, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInternal),
				Type:    "internal_error",
				Message: "Failed to get interface info: " + err.Error(),
			},
			"Failed to get interface info",
		))
		return
	}

	// Debug: Log interface info details
	s.log.Error("API: Interface info retrieved",
		logger.String("interface_name", info.InterfaceName),
		logger.String("local_ip", info.LocalIP),
		logger.String("tun_ip", info.TunIP),
		logger.Int("interface_index", info.InterfaceIndex),
		logger.Uint64("interface_luid", info.InterfaceLUID))

	writeJSON(w, http.StatusOK, api.NewResponse(info, ""))
}

/*****************************************************************************
 * NAME: handleGetRoutingSettings
 *
 * DESCRIPTION:
 *     Handles the get routing settings request by returning current routing configuration
 *
 * PARAMETERS:
 *     w - HTTP response writer
 *     r - HTTP request
 *****************************************************************************/
func (s *Server) handleGetRoutingSettings(w http.ResponseWriter, r *http.Request) {
	if s.deps == nil || s.deps.ConnectionManager == nil {
		writeJSON(w, http.StatusInternalServerError, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInternal),
				Type:    "internal_error",
				Message: "Connection manager not initialized",
			},
			"Internal server error",
		))
		return
	}

	// Get routing settings
	settings := s.deps.ConnectionManager.GetRoutingSettings()

	writeJSON(w, http.StatusOK, api.NewResponse(settings, ""))
}

/*****************************************************************************
 * NAME: handleSetRoutingSettings
 *
 * DESCRIPTION:
 *     Handles the set routing settings request by updating routing configuration
 *
 * PARAMETERS:
 *     w - HTTP response writer
 *     r - HTTP request
 *****************************************************************************/
func (s *Server) handleSetRoutingSettings(w http.ResponseWriter, r *http.Request) {
	if s.deps == nil || s.deps.ConnectionManager == nil {
		writeJSON(w, http.StatusInternalServerError, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInternal),
				Type:    "internal_error",
				Message: "Connection manager not initialized",
			},
			"Internal server error",
		))
		return
	}

	// Parse request
	var settings manager.RoutingSettings
	if err := readJSON(r, &settings); err != nil {
		s.log.Error("Failed to parse routing settings", logger.ErrorField(err))
		writeJSON(w, http.StatusBadRequest, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInvalidRequest),
				Type:    "invalid_request",
				Message: "Invalid routing settings",
			},
			"Invalid routing settings",
		))
		return
	}

	// Validate routing mode
	if settings.Mode != manager.RoutingModeAll && settings.Mode != manager.RoutingModeCustom {
		writeJSON(w, http.StatusBadRequest, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInvalidRequest),
				Type:    "invalid_request",
				Message: "Invalid routing mode",
			},
			"Invalid routing mode",
		))
		return
	}

	// Set routing settings
	err := s.deps.ConnectionManager.SetRoutingSettings(settings)
	if err != nil {
		s.log.Error("Failed to set routing settings", logger.ErrorField(err))
		writeJSON(w, http.StatusInternalServerError, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInternal),
				Type:    "internal_error",
				Message: "Failed to set routing settings: " + err.Error(),
			},
			"Failed to set routing settings",
		))
		return
	}

	writeJSON(w, http.StatusOK, api.NewResponse(nil, "Routing settings updated"))
}

/*****************************************************************************
 * NAME: handleSetServerProvider
 *
 * DESCRIPTION:
 *     Handles the set server provider request by updating server list URL
 *
 * PARAMETERS:
 *     w - HTTP response writer
 *     r - HTTP request
 *****************************************************************************/
func (s *Server) handleSetServerProvider(w http.ResponseWriter, r *http.Request) {
	if s.deps == nil || s.deps.ServerManager == nil {
		writeJSON(w, http.StatusInternalServerError, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInternal),
				Type:    "internal_error",
				Message: "Server manager not initialized",
			},
			"Internal server error",
		))
		return
	}

	// Parse request
	var req struct {
		URL string `json:"url"`
	}
	if err := readJSON(r, &req); err != nil {
		s.log.Error("Failed to parse server provider request", logger.ErrorField(err))
		writeJSON(w, http.StatusBadRequest, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInvalidRequest),
				Type:    "invalid_request",
				Message: "Invalid server provider request",
			},
			"Invalid server provider request",
		))
		return
	}

	// Set server list URL
	err := s.deps.ServerManager.SetServerListURL(req.URL)
	if err != nil {
		s.log.Error("Failed to set server list URL", logger.ErrorField(err))
		writeJSON(w, http.StatusInternalServerError, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInternal),
				Type:    "internal_error",
				Message: "Failed to set server list URL: " + err.Error(),
			},
			"Failed to set server list URL",
		))
		return
	}

	writeJSON(w, http.StatusOK, api.NewResponse(nil, "Server provider URL updated"))
}

/*****************************************************************************
 * NAME: handleHealthCheck
 *
 * DESCRIPTION:
 *     Handles the health check request. This endpoint responds quickly to
 *     indicate the service is running, even if some components are still initializing
 *
 * PARAMETERS:
 *     w - HTTP response writer
 *     r - HTTP request
 *****************************************************************************/
func (s *Server) handleHealthCheck(w http.ResponseWriter, r *http.Request) {
	// Always return OK status for health check to indicate the service is running
	// This ensures the UI can detect that the backend is available
	status := "ok"
	vpnStatus := api.StatusDisconnected

	// Only check connection status if dependencies are fully initialized
	// This prevents delays in health check response during startup
	if s.deps != nil && s.deps.ConnectionManager != nil {
		connStatus := s.deps.ConnectionManager.GetStatus()
		vpnStatus = connStatus.Status
	}

	healthInfo := &api.HealthInfo{
		Status:    status,
		Version:   getApplicationVersion(),
		VPNStatus: vpnStatus,
	}

	writeJSON(w, http.StatusOK, api.NewResponse(healthInfo, ""))
}

/*****************************************************************************
 * NAME: getApplicationVersion
 *
 * DESCRIPTION:
 *     Returns the current application version from environment variable or default
 *
 * PARAMETERS:
 *     None
 *
 * RETURNS:
 *     string - Application version string
 *****************************************************************************/
func getApplicationVersion() string {
	// Try to get version from environment variable first
	if version := os.Getenv("APP_VERSION"); version != "" {
		return version
	}

	// Default version
	return "1.0.1"
}

/*****************************************************************************
 * NAME: handleShutdown
 *
 * DESCRIPTION:
 *     Handles the shutdown request by initiating graceful application shutdown
 *
 * PARAMETERS:
 *     w - HTTP response writer
 *     r - HTTP request
 *****************************************************************************/
func (s *Server) handleShutdown(w http.ResponseWriter, r *http.Request) {
	// Check if dependencies are initialized
	if s.deps == nil {
		s.log.Error("Dependencies not initialized")
		writeJSON(w, http.StatusInternalServerError, api.NewErrorResponse(
			&api.Error{
				Code:    int(errors.ErrInternal),
				Type:    "internal_error",
				Message: "Internal server error",
			},
			"Internal server error",
		))
		return
	}

	// Send WebSocket notification about shutdown
	if s.ws != nil {
		s.ws.Broadcast(api.NewWebSocketEvent("shutdown_initiated", map[string]string{
			"message": "Service is shutting down",
		}))
	}

	// Send success response before shutting down
	writeJSON(w, http.StatusOK, api.NewResponse(nil, "Service is shutting down"))

	// Start shutdown in the background
	go func() {
		s.log.Info("Shutdown requested via API, initiating graceful shutdown")

		// Release resources in the same order as main program
		// 1. First disconnect VPN connection
		if s.deps.ConnectionManager != nil {
			s.log.Info("Disconnecting VPN if connected...")
			if err := s.deps.ConnectionManager.Disconnect(); err != nil {
				s.log.Error("Failed to disconnect VPN during API shutdown", logger.ErrorField(err))
				// Continue execution, don't interrupt shutdown process due to disconnection failure
			} else {
				s.log.Info("VPN disconnected successfully")
			}
		}

		// 2. Immediately stop server manager to clean up server routes
		if s.deps.ServerManager != nil {
			s.log.Info("Stopping server manager...")
			if err := s.deps.ServerManager.Stop(); err != nil {
				s.log.Error("Failed to stop server manager during API shutdown", logger.ErrorField(err))
			} else {
				s.log.Info("Server manager stopped successfully")
			}
		}

		// 3. Ensure TUN resources are released
		s.log.Info("Ensuring TUN resources are released...")
		if err := tun.Shutdown(); err != nil {
			s.log.Error("Failed to shutdown TUN subsystem during API shutdown", logger.ErrorField(err))
		} else {
			s.log.Info("TUN resources released successfully")
		}

		// 4. Send final WebSocket notification
		if s.ws != nil {
			s.log.Info("Sending final WebSocket notification...")
			s.ws.Broadcast(api.NewWebSocketEvent("shutdown_complete", map[string]string{
				"message": "Service shutdown complete",
			}))
			// Give clients a moment to receive the notification
			time.Sleep(100 * time.Millisecond)
		}

		// 5. Exit the application
		s.log.Info("API shutdown complete, exiting application")
		os.Exit(0)
	}()
}

/*****************************************************************************
 * NAME: parseAndValidateLoginRequest
 *
 * DESCRIPTION:
 *     Parses and validates login request
 *
 * PARAMETERS:
 *     r - HTTP request
 *
 * RETURNS:
 *     *api.LoginRequest - Parsed login request
 *     error             - Parsing or validation error
 *****************************************************************************/
func (s *Server) parseAndValidateLoginRequest(r *http.Request) (*api.LoginRequest, error) {
	var req api.LoginRequest
	if err := readJSON(r, &req); err != nil {
		s.log.Error("Failed to decode login request", logger.ErrorField(err))
		return nil, errors.NewWithCode(errors.CodeInvalidArgument, "Invalid request body")
	}

	// Validate request fields
	if req.Username == "" || req.Password == "" {
		return nil, errors.NewWithCode(errors.CodeInvalidArgument, "Username and password are required")
	}

	return &req, nil
}

/*****************************************************************************
 * NAME: handleLoginError
 *
 * DESCRIPTION:
 *     Handles errors during login process
 *
 * PARAMETERS:
 *     w       - HTTP response writer
 *     err     - Error information
 *     context - Error context
 *****************************************************************************/
func (s *Server) handleLoginError(w http.ResponseWriter, err error, context string) {
	s.log.Error(context, logger.ErrorField(err))

	// Determine HTTP status code and error type based on error type
	var status int
	var errorType string

	if detailedErr, ok := err.(*errors.DetailedError); ok {
		switch detailedErr.Code {
		case errors.CodeInvalidArgument:
			status = http.StatusBadRequest
			errorType = "invalid_request"
		case errors.CodeUnauthenticated:
			status = http.StatusUnauthorized
			errorType = "auth_failed"
		default:
			status = http.StatusInternalServerError
			errorType = "internal_error"
		}
	} else {
		status = http.StatusInternalServerError
		errorType = "internal_error"
	}

	writeErrorResponse(w, status, errors.GetCode(err), errorType, err.Error())
}

/*****************************************************************************
 * NAME: performAuthentication
 *
 * DESCRIPTION:
 *     Executes user authentication
 *
 * PARAMETERS:
 *     req    - Login request
 *     server - Target server
 *
 * RETURNS:
 *     *manager.AuthResult - Authentication result
 *     error               - Authentication error
 *****************************************************************************/
func (s *Server) performAuthentication(req *api.LoginRequest, server *server.Server) (*manager.AuthResult, error) {
	// Decrypt password
	decryptedPassword, err := config.SimpleDecryptCredential(req.Password)
	if err != nil {
		return nil, errors.WrapWithCode(errors.CodeUnauthenticated, err, "Failed to decrypt password")
	}

	// Perform authentication
	authResult, err := s.deps.ConnectionManager.Authenticate(req.Username, decryptedPassword, server.ServerName, server.ServerPort)
	if err != nil {
		return nil, errors.WrapWithCode(errors.CodeUnauthenticated, err, "Authentication failed")
	}

	// Check authentication result
	if !authResult.Success {
		return authResult, errors.NewWithCode(errors.CodeUnauthenticated, "Authentication rejected: "+authResult.ErrorMessage)
	}

	return authResult, nil
}

// handleAutoConnect method removed - auto-connection after login is no longer supported
// to maintain consistency with iOS/Android platforms where UI initiates connection

/*****************************************************************************
 * NAME: broadcastConnectedStatus
 *
 * DESCRIPTION:
 *     Broadcasts connected status
 *
 * PARAMETERS:
 *     server - Server information
 *****************************************************************************/
func (s *Server) broadcastConnectedStatus(server *api.ServerInfo) {
	// Get current status
	status := s.deps.ConnectionManager.GetStatus()

	// Ensure status is connected
	if status.State == manager.StateData {
		// Calculate connection time (Unix timestamp)
		var connectedTime int64
		if !status.ConnectedTime.IsZero() {
			connectedTime = status.ConnectedTime.Unix()
		}

		// Create status information and broadcast
		// Let frontend handle server name display based on language settings
		s.broadcastStatusEvent(&api.StatusInfo{
			Status:        api.StatusConnected,
			Message:       "connected",
			Server:        server,
			ConnectedTime: connectedTime,
		})
	}
}

/*****************************************************************************
 * NAME: handlePingServersBackground
 *
 * DESCRIPTION:
 *     Handles server latency testing in background
 *****************************************************************************/
func (s *Server) handlePingServersBackground() {
	if err := s.deps.ServerManager.PingServers(); err != nil {
		s.log.Error("Failed to ping servers", logger.ErrorField(err))

		// Send test failed WebSocket notification
		s.broadcastErrorEvent(int(errors.CodeNetworkUnreachable), "Failed to ping servers: "+err.Error(), "ping_failed")
	} else {
		// Get updated server list
		servers := s.deps.ServerManager.GetServers()

		// Convert to API model
		serverInfos := convertServersToAPI(servers)

		// Send server list update WebSocket notification
		if s.ws != nil {
			s.ws.Broadcast(api.NewServersEvent(serverInfos))
			// Send test completed WebSocket notification
			s.ws.Broadcast(api.NewWebSocketEvent(api.EventPingComplete, nil))
		}
	}
}
