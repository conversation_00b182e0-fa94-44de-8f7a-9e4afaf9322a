/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      api.go
 *
 * DESCRIPTION :    API definitions and data structures for the VPN client
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package api

import "time"

/*****************************************************************************
 * NAME: Response
 *
 * DESCRIPTION:
 *     Standard response format for all API endpoints
 *
 * FIELDS:
 *     Success - Indicates if the request was successful
 *     Message - Optional response message
 *     Data    - Optional response data
 *     Error   - Optional error information
 *****************************************************************************/
type Response struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   *Error      `json:"error,omitempty"`
}

/*****************************************************************************
 * NAME: Error
 *
 * DESCRIPTION:
 *     Represents an error in the API response
 *
 * FIELDS:
 *     Code    - Error code
 *     Type    - Error type
 *     Message - Error message
 *****************************************************************************/
type Error struct {
	Code    int    `json:"error_code"`
	Type    string `json:"error_type"`
	Message string `json:"message"`
}

/*****************************************************************************
 * NAME: LoginRequest
 *
 * DESCRIPTION:
 *     Represents a login request
 *
 * FIELDS:
 *     Username - User login name
 *     Password - User password
 *****************************************************************************/
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

/*****************************************************************************
 * NAME: LoginResponse
 *
 * DESCRIPTION:
 *     Represents a login response
 *
 * FIELDS:
 *     BestServer - Information about the best available server
 *****************************************************************************/
type LoginResponse struct {
	BestServer *ServerInfo `json:"best_server,omitempty"`
}

/*****************************************************************************
 * NAME: ConnectRequest
 *
 * DESCRIPTION:
 *     Represents a connect request with user credentials
 *
 * FIELDS:
 *     ServerID - ID of the server to connect to
 *     Username - User login name
 *     Password - User password (encrypted for Windows, plain text for iOS/Android)
 *****************************************************************************/
type ConnectRequest struct {
	ServerID string `json:"server_id"`
	Username string `json:"username"`
	Password string `json:"password"`
}

/*****************************************************************************
 * NAME: ConnectResponse
 *
 * DESCRIPTION:
 *     Represents a connect response with interface information
 *
 * FIELDS:
 *     Server        - Information about the connected server
 *     InterfaceInfo - Network interface information obtained during connection
 *****************************************************************************/
type ConnectResponse struct {
	Server        *ServerInfo    `json:"server"`
	InterfaceInfo *InterfaceInfo `json:"interface_info"`
}

/*****************************************************************************
 * NAME: InterfaceInfo
 *
 * DESCRIPTION:
 *     Represents network interface information
 *
 * FIELDS:
 *     InterfaceName  - Physical interface name
 *     LocalIP        - Local IP address (physical interface IP)
 *     TunIP          - TUN device IP address
 *     InterfaceIndex - Physical interface index
 *     InterfaceLUID  - Physical interface LUID (Windows only)
 *****************************************************************************/
type InterfaceInfo struct {
	InterfaceName  string `json:"interface_name"`
	LocalIP        string `json:"local_ip"`
	TunIP          string `json:"tun_ip"`
	InterfaceIndex int    `json:"interface_index"`
	InterfaceLUID  uint64 `json:"interface_luid"`
}

/*****************************************************************************
 * NAME: ServerInfo
 *
 * DESCRIPTION:
 *     Represents information about a VPN server
 *
 * FIELDS:
 *     ID         - Unique server identifier
 *     Name       - Server display name
 *     NameEn     - Server name in English
 *     ServerName - Server hostname or address
 *     ServerPort - Server port number
 *     Ping       - Server ping latency in milliseconds
 *     IsAuto     - Whether this is an auto-selected server
 *     Status     - Current server status
 *     IsDefault  - Whether this is the default server
 *****************************************************************************/
type ServerInfo struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	NameEn     string `json:"name_en"`
	ServerName string `json:"server_name"`
	ServerPort int    `json:"server_port"`
	Ping       int    `json:"ping"`
	IsAuto     bool   `json:"isauto"`
	Status     string `json:"status"`
	IsDefault  bool   `json:"isdefault"`
}

/*****************************************************************************
 * NAME: StatusInfo
 *
 * DESCRIPTION:
 *     Represents the status of the VPN connection
 *
 * FIELDS:
 *     Status        - Current connection status
 *     Message       - Optional status message
 *     Reason        - Reason for disconnection (if applicable)
 *     Server        - Information about connected server
 *     ConnectedTime - Timestamp when connection was established
 *****************************************************************************/
type StatusInfo struct {
	Status        string      `json:"status"`
	Message       string      `json:"message,omitempty"`
	Reason        string      `json:"reason,omitempty"`
	Server        *ServerInfo `json:"server,omitempty"`
	ConnectedTime int64       `json:"connected_time,omitempty"`
}

/*****************************************************************************
 * NAME: HealthInfo
 *
 * DESCRIPTION:
 *     Represents the health status of the VPN service
 *
 * FIELDS:
 *     Status    - Overall service health status
 *     Version   - Service version information
 *     VPNStatus - Current VPN connection status
 *****************************************************************************/
type HealthInfo struct {
	Status    string `json:"status"`
	Version   string `json:"version"`
	VPNStatus string `json:"vpn_status"`
}

/*****************************************************************************
 * NAME: WebSocketEvent
 *
 * DESCRIPTION:
 *     Represents an event sent over WebSocket
 *
 * FIELDS:
 *     Event - Event type identifier
 *     Data  - Event payload data
 *****************************************************************************/
type WebSocketEvent struct {
	Event string      `json:"event"`
	Data  interface{} `json:"data"`
}

// Constants for connection status.
const (
	StatusDisconnected  = "disconnected"
	StatusConnecting    = "connecting"
	StatusConnected     = "connected"
	StatusDisconnecting = "disconnecting"
	StatusError         = "error"
)

// Constants for disconnection reasons.
const (
	ReasonConnectionFailed     = "connection_failed"
	ReasonAuthenticationFailed = "authentication_failed"
	ReasonTimeout              = "timeout"
	ReasonUserDisconnect       = "user_disconnect"
	ReasonManualDisconnect     = "manual_disconnect"
	ReasonServerError          = "server_error"
	ReasonNetworkError         = "network_error"
)

// Constants for reconnect reasons.
const (
	ReconnectReasonNetworkInterfaceChange = "network_interface_change"
	ReconnectReasonHeartbeatTimeout       = "heartbeat_timeout"
	ReconnectReasonConnectionLost         = "connection_lost"
)

// Constants for WebSocket event types.
const (
	EventStatus        = "status"
	EventError         = "error"
	EventServers       = "servers"
	EventHeartbeat     = "heartbeat"
	EventPingStart     = "ping_start"
	EventPingComplete  = "ping_complete"
	EventPingResults   = "ping_results"
	EventConnServer    = "conn_server"
	EventInterfaceInfo = "interface_info"
	EventTraffic       = "traffic"
	// New event for network connection management
	EventReconnectRequired = "reconnect_required"
)

/*****************************************************************************
 * NAME: HeartbeatData
 *
 * DESCRIPTION:
 *     Represents the data for a heartbeat event
 *
 * FIELDS:
 *     Timestamp - Unix timestamp when heartbeat was generated
 *****************************************************************************/
type HeartbeatData struct {
	Timestamp int64 `json:"timestamp"`
}

/*****************************************************************************
 * NAME: ErrorData
 *
 * DESCRIPTION:
 *     Represents the data for an error event
 *
 * FIELDS:
 *     Code    - Error code
 *     Message - Error message
 *     Type    - Error type
 *****************************************************************************/
type ErrorData struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Type    string `json:"type"`
}

/*****************************************************************************
 * NAME: PingResultsData
 *
 * DESCRIPTION:
 *     Represents the data for a ping results event
 *
 * FIELDS:
 *     Servers   - List of servers with ping results
 *     Timestamp - Unix timestamp when ping results were generated
 *****************************************************************************/
type PingResultsData struct {
	Servers   []*ServerInfo `json:"servers"`
	Timestamp int64         `json:"timestamp"`
}

/*****************************************************************************
 * NAME: ConnServerData
 *
 * DESCRIPTION:
 *     Represents the data for a connection server event
 *
 * FIELDS:
 *     Server    - Information about the connected server
 *     Timestamp - Unix timestamp when connection was established
 *****************************************************************************/
type ConnServerData struct {
	Server    *ServerInfo `json:"server"`
	Timestamp int64       `json:"timestamp"`
}

/*****************************************************************************
 * NAME: TrafficStatsData
 *
 * DESCRIPTION:
 *     Represents the data for a traffic statistics event
 *
 * FIELDS:
 *     UploadSpeed   - Current upload speed in bytes per second
 *     DownloadSpeed - Current download speed in bytes per second
 *     TotalUpload   - Total bytes uploaded since connection started
 *     TotalDownload - Total bytes downloaded since connection started
 *     Timestamp     - Unix timestamp when statistics were collected
 *****************************************************************************/
type TrafficStatsData struct {
	UploadSpeed   int64 `json:"upload_speed"`
	DownloadSpeed int64 `json:"download_speed"`
	TotalUpload   int64 `json:"total_upload"`
	TotalDownload int64 `json:"total_download"`
	Timestamp     int64 `json:"timestamp"`
}

/*****************************************************************************
 * NAME: ReconnectRequiredData
 *
 * DESCRIPTION:
 *     Represents the data for a reconnect required event
 *
 * FIELDS:
 *     Reason    - Reason for requiring reconnect (network_interface_change, heartbeat_timeout, etc.)
 *     Message   - Detailed message describing the reason
 *     Timestamp - Unix timestamp when the event occurred
 *****************************************************************************/
type ReconnectRequiredData struct {
	Reason    string `json:"reason"`
	Message   string `json:"message"`
	Timestamp int64  `json:"timestamp"`
}

/*****************************************************************************
 * NAME: NewResponse
 *
 * DESCRIPTION:
 *     Creates a new successful API response
 *
 * PARAMETERS:
 *     data    - Response data
 *     message - Response message
 *
 * RETURNS:
 *     *Response - Successful response structure
 *****************************************************************************/
func NewResponse(data interface{}, message string) *Response {
	return &Response{
		Success: true,
		Message: message,
		Data:    data,
	}
}

/*****************************************************************************
 * NAME: NewErrorResponse
 *
 * DESCRIPTION:
 *     Creates a new error API response
 *
 * PARAMETERS:
 *     err     - Error information
 *     message - Error message
 *
 * RETURNS:
 *     *Response - Error response structure
 *****************************************************************************/
func NewErrorResponse(err *Error, message string) *Response {
	return &Response{
		Success: false,
		Message: message,
		Error:   err,
	}
}

/*****************************************************************************
 * NAME: NewWebSocketEvent
 *
 * DESCRIPTION:
 *     Creates a new WebSocket event
 *
 * PARAMETERS:
 *     event - Event type identifier
 *     data  - Event payload data
 *
 * RETURNS:
 *     *WebSocketEvent - New WebSocket event structure
 *****************************************************************************/
func NewWebSocketEvent(event string, data interface{}) *WebSocketEvent {
	return &WebSocketEvent{
		Event: event,
		Data:  data,
	}
}

/*****************************************************************************
 * NAME: NewHeartbeatEvent
 *
 * DESCRIPTION:
 *     Creates a new heartbeat event
 *
 * PARAMETERS:
 *     None
 *
 * RETURNS:
 *     *WebSocketEvent - New heartbeat event with current timestamp
 *****************************************************************************/
func NewHeartbeatEvent() *WebSocketEvent {
	return NewWebSocketEvent(EventHeartbeat, HeartbeatData{
		Timestamp: time.Now().Unix(),
	})
}

/*****************************************************************************
 * NAME: NewStatusEvent
 *
 * DESCRIPTION:
 *     Creates a new status event
 *
 * PARAMETERS:
 *     status - Status information to include in the event
 *
 * RETURNS:
 *     *WebSocketEvent - New status event structure
 *****************************************************************************/
func NewStatusEvent(status *StatusInfo) *WebSocketEvent {
	return NewWebSocketEvent(EventStatus, status)
}

/*****************************************************************************
 * NAME: NewErrorEvent
 *
 * DESCRIPTION:
 *     Creates a new error event
 *
 * PARAMETERS:
 *     code      - Error code
 *     message   - Error message
 *     errorType - Error type
 *
 * RETURNS:
 *     *WebSocketEvent - New error event structure
 *****************************************************************************/
func NewErrorEvent(code int, message, errorType string) *WebSocketEvent {
	return NewWebSocketEvent(EventError, ErrorData{
		Code:    code,
		Message: message,
		Type:    errorType,
	})
}

/*****************************************************************************
 * NAME: NewServersEvent
 *
 * DESCRIPTION:
 *     Creates a new servers event
 *
 * PARAMETERS:
 *     servers - List of server information
 *
 * RETURNS:
 *     *WebSocketEvent - New servers event structure
 *****************************************************************************/
func NewServersEvent(servers []*ServerInfo) *WebSocketEvent {
	return NewWebSocketEvent(EventServers, servers)
}

/*****************************************************************************
 * NAME: NewPingResultsEvent
 *
 * DESCRIPTION:
 *     Creates a new ping results event
 *
 * PARAMETERS:
 *     servers - List of servers with ping results
 *
 * RETURNS:
 *     *WebSocketEvent - New ping results event with current timestamp
 *****************************************************************************/
func NewPingResultsEvent(servers []*ServerInfo) *WebSocketEvent {
	return NewWebSocketEvent(EventPingResults, PingResultsData{
		Servers:   servers,
		Timestamp: time.Now().Unix(),
	})
}

/*****************************************************************************
 * NAME: NewConnServerEvent
 *
 * DESCRIPTION:
 *     Creates a new connection server event
 *
 * PARAMETERS:
 *     server - Information about the connected server
 *
 * RETURNS:
 *     *WebSocketEvent - New connection server event with current timestamp
 *****************************************************************************/
func NewConnServerEvent(server *ServerInfo) *WebSocketEvent {
	return NewWebSocketEvent(EventConnServer, ConnServerData{
		Server:    server,
		Timestamp: time.Now().Unix(),
	})
}

/*****************************************************************************
 * NAME: NewInterfaceInfoEvent
 *
 * DESCRIPTION:
 *     Creates a new interface info event
 *
 * PARAMETERS:
 *     interfaceInfo - Network interface information
 *
 * RETURNS:
 *     *WebSocketEvent - New interface info event structure
 *****************************************************************************/
func NewInterfaceInfoEvent(interfaceInfo interface{}) *WebSocketEvent {
	return NewWebSocketEvent(EventInterfaceInfo, interfaceInfo)
}

/*****************************************************************************
 * NAME: NewTrafficEvent
 *
 * DESCRIPTION:
 *     Creates a new traffic statistics event
 *
 * PARAMETERS:
 *     uploadSpeed   - Current upload speed in bytes per second
 *     downloadSpeed - Current download speed in bytes per second
 *     totalUpload   - Total bytes uploaded since connection started
 *     totalDownload - Total bytes downloaded since connection started
 *
 * RETURNS:
 *     *WebSocketEvent - New traffic statistics event with current timestamp
 *****************************************************************************/
func NewTrafficEvent(uploadSpeed, downloadSpeed, totalUpload, totalDownload int64) *WebSocketEvent {
	return NewWebSocketEvent(EventTraffic, &TrafficStatsData{
		UploadSpeed:   uploadSpeed,
		DownloadSpeed: downloadSpeed,
		TotalUpload:   totalUpload,
		TotalDownload: totalDownload,
		Timestamp:     time.Now().Unix(),
	})
}

/*****************************************************************************
 * NAME: NewReconnectRequiredEvent
 *
 * DESCRIPTION:
 *     Creates a new reconnect required event
 *
 * PARAMETERS:
 *     reason  - Reason for requiring reconnect
 *     message - Detailed message describing the reason
 *
 * RETURNS:
 *     *WebSocketEvent - New reconnect required event with current timestamp
 *****************************************************************************/
func NewReconnectRequiredEvent(reason, message string) *WebSocketEvent {
	return NewWebSocketEvent(EventReconnectRequired, &ReconnectRequiredData{
		Reason:    reason,
		Message:   message,
		Timestamp: time.Now().Unix(),
	})
}
