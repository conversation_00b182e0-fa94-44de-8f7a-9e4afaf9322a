/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      manager.go
 *
 * DESCRIPTION :    Enhanced configuration manager with validation and hot reload
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"gopkg.in/yaml.v2"

	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
	"mobile/internal/interfaces"
)

/*****************************************************************************
 * NAME: Manager
 *
 * DESCRIPTION:
 *     Enhanced configuration manager with validation, hot reload, and type safety
 *
 * FIELDS:
 *     config       - Current configuration data
 *     configPath   - Path to configuration file
 *     logger       - Logger instance
 *     mutex        - Mutex for thread-safe operations
 *     watchers     - Configuration change watchers
 *     validator    - Configuration validator
 *****************************************************************************/
type Manager struct {
	config     map[string]interface{}
	configPath string
	logger     logger.Logger
	mutex      sync.RWMutex
	watchers   []ConfigWatcher
	validator  ConfigValidator
}

/*****************************************************************************
 * NAME: ConfigWatcher
 *
 * DESCRIPTION:
 *     Interface for configuration change notifications
 *
 * METHODS:
 *     OnConfigChanged - Called when configuration value changes
 *****************************************************************************/
type ConfigWatcher interface {
	OnConfigChanged(key string, oldValue, newValue interface{})
}

/*****************************************************************************
 * NAME: ConfigValidator
 *
 * DESCRIPTION:
 *     Interface for configuration validation
 *
 * METHODS:
 *     Validate - Validates configuration data
 *****************************************************************************/
type ConfigValidator interface {
	Validate(config map[string]interface{}) error
}

/*****************************************************************************
 * NAME: NewManager
 *
 * DESCRIPTION:
 *     Creates a new configuration manager
 *
 * PARAMETERS:
 *     logger - Logger instance
 *
 * RETURNS:
 *     interfaces.ConfigManager - Configuration manager instance
 *****************************************************************************/
func NewManager(logger logger.Logger) interfaces.ConfigManager {
	return &Manager{
		config:   make(map[string]interface{}),
		logger:   logger.WithModule("config"),
		watchers: make([]ConfigWatcher, 0),
	}
}

/*****************************************************************************
 * NAME: Load
 *
 * DESCRIPTION:
 *     Loads configuration from file
 *
 * PARAMETERS:
 *     configPath - Path to configuration file
 *
 * RETURNS:
 *     error - Load error if any
 *****************************************************************************/
func (m *Manager) Load(configPath string) error {
	// 	m.mutex.Lock()
	// 	defer m.mutex.Unlock()

	m.logger.Info("Loading configuration", logger.String("path", configPath))

	// Check if file exists
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return errors.NewWithCode(errors.CodeConfigNotFound,
			"configuration file not found: %s", configPath).(*errors.DetailedError).
			WithComponent("config").WithOperation("load")
	}

	// Read file
	data, err := os.ReadFile(configPath)
	if err != nil {
		return errors.WrapWithCode(errors.CodeConfigReadFailed, err,
			"failed to read configuration file: %s", configPath).(*errors.DetailedError).
			WithComponent("config").WithOperation("load")
	}

	// Parse YAML
	var config map[string]interface{}
	if err := yaml.Unmarshal(data, &config); err != nil {
		return errors.WrapWithCode(errors.CodeConfigParseFailed, err,
			"failed to parse configuration file: %s", configPath).(*errors.DetailedError).
			WithComponent("config").WithOperation("load")
	}

	// Validate configuration
	if m.validator != nil {
		if err := m.validator.Validate(config); err != nil {
			return errors.WrapWithCode(errors.CodeConfigInvalid, err,
				"configuration validation failed").(*errors.DetailedError).
				WithComponent("config").WithOperation("load")
		}
	}

	// Store configuration
	m.config = config
	m.configPath = configPath

	m.logger.Info("Configuration loaded successfully",
		logger.String("path", configPath),
		logger.Int("keys", len(config)))

	return nil
}

/*****************************************************************************
 * NAME: Save
 *
 * DESCRIPTION:
 *     Saves configuration to file
 *
 * PARAMETERS:
 *     configPath - Path to save configuration (optional, uses loaded path if empty)
 *
 * RETURNS:
 *     error - Save error if any
 *****************************************************************************/
func (m *Manager) Save(configPath string) error {
	//m.mutex.RLock()
	//defer m.mutex.RUnlock()

	if configPath == "" {
		configPath = m.configPath
	}

	if configPath == "" {
		return errors.NewWithCode(errors.CodeInvalidArgument,
			"no configuration path specified").(*errors.DetailedError).
			WithComponent("config").WithOperation("save")
	}

	m.logger.Info("Saving configuration", logger.String("path", configPath))

	// Validate configuration before saving
	if m.validator != nil {
		if err := m.validator.Validate(m.config); err != nil {
			return errors.WrapWithCode(errors.CodeConfigInvalid, err,
				"configuration validation failed before save").(*errors.DetailedError).
				WithComponent("config").WithOperation("save")
		}
	}

	// Marshal to YAML
	data, err := yaml.Marshal(m.config)
	if err != nil {
		return errors.WrapWithCode(errors.CodeConfigParseFailed, err,
			"failed to marshal configuration").(*errors.DetailedError).
			WithComponent("config").WithOperation("save")
	}

	// Create directory if it doesn't exist
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return errors.WrapWithCode(errors.CodeConfigWriteFailed, err,
			"failed to create configuration directory: %s", dir).(*errors.DetailedError).
			WithComponent("config").WithOperation("save")
	}

	// Write file
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return errors.WrapWithCode(errors.CodeConfigWriteFailed, err,
			"failed to write configuration file: %s", configPath).(*errors.DetailedError).
			WithComponent("config").WithOperation("save")
	}

	m.logger.Info("Configuration saved successfully", logger.String("path", configPath))
	return nil
}

/*****************************************************************************
 * NAME: Get
 *
 * DESCRIPTION:
 *     Gets a configuration value by key
 *
 * PARAMETERS:
 *     key - Configuration key (supports dot notation)
 *
 * RETURNS:
 *     interface{} - Configuration value
 *****************************************************************************/
func (m *Manager) Get(key string) interface{} {
	//m.mutex.RLock()
	//defer m.mutex.RUnlock()

	return m.getValue(key)
}

/*****************************************************************************
 * NAME: Set
 *
 * DESCRIPTION:
 *     Sets a configuration value by key
 *
 * PARAMETERS:
 *     key   - Configuration key (supports dot notation)
 *     value - Value to set
 *****************************************************************************/
func (m *Manager) Set(key string, value interface{}) {
	// 	m.mutex.Lock()
	// 	defer m.mutex.Unlock()

	oldValue := m.getValue(key)
	m.setValue(key, value)

	// Notify watchers
	for _, watcher := range m.watchers {
		watcher.OnConfigChanged(key, oldValue, value)
	}

	m.logger.Debug("Configuration value updated",
		logger.String("key", key),
		logger.Any("old_value", oldValue),
		logger.Any("new_value", value))
}

/*****************************************************************************
 * NAME: GetString
 *
 * DESCRIPTION:
 *     Gets a string configuration value
 *
 * PARAMETERS:
 *     key - Configuration key
 *
 * RETURNS:
 *     string - String value or empty string if not found
 *****************************************************************************/
func (m *Manager) GetString(key string) string {
	value := m.Get(key)
	if value == nil {
		return ""
	}
	if str, ok := value.(string); ok {
		return str
	}
	return fmt.Sprintf("%v", value)
}

/*****************************************************************************
 * NAME: GetInt
 *
 * DESCRIPTION:
 *     Gets an integer configuration value
 *
 * PARAMETERS:
 *     key - Configuration key
 *
 * RETURNS:
 *     int - Integer value or 0 if not found or invalid
 *****************************************************************************/
func (m *Manager) GetInt(key string) int {
	value := m.Get(key)
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int:
		return v
	case int64:
		return int(v)
	case float64:
		return int(v)
	case string:
		if i, err := strconv.Atoi(v); err == nil {
			return i
		}
	}
	return 0
}

/*****************************************************************************
 * NAME: GetBool
 *
 * DESCRIPTION:
 *     Gets a boolean configuration value
 *
 * PARAMETERS:
 *     key - Configuration key
 *
 * RETURNS:
 *     bool - Boolean value or false if not found or invalid
 *****************************************************************************/
func (m *Manager) GetBool(key string) bool {
	value := m.Get(key)
	if value == nil {
		return false
	}

	switch v := value.(type) {
	case bool:
		return v
	case string:
		return strings.ToLower(v) == "true" || v == "1"
	case int:
		return v != 0
	case float64:
		return v != 0
	}
	return false
}

/*****************************************************************************
 * NAME: GetDuration
 *
 * DESCRIPTION:
 *     Gets a duration configuration value
 *
 * PARAMETERS:
 *     key - Configuration key
 *
 * RETURNS:
 *     time.Duration - Duration value or 0 if not found or invalid
 *****************************************************************************/
func (m *Manager) GetDuration(key string) time.Duration {
	value := m.Get(key)
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case string:
		if d, err := time.ParseDuration(v); err == nil {
			return d
		}
	case int:
		return time.Duration(v) * time.Second
	case int64:
		return time.Duration(v) * time.Second
	case float64:
		return time.Duration(v) * time.Second
	}
	return 0
}

/*****************************************************************************
 * NAME: getValue
 *
 * DESCRIPTION:
 *     Internal method to get value with dot notation support
 *
 * PARAMETERS:
 *     key - Configuration key with dot notation support
 *
 * RETURNS:
 *     interface{} - Configuration value or nil if not found
 *****************************************************************************/
func (m *Manager) getValue(key string) interface{} {
	keys := strings.Split(key, ".")
	current := m.config

	for i, k := range keys {
		if i == len(keys)-1 {
			return current[k]
		}

		if next, ok := current[k].(map[string]interface{}); ok {
			current = next
		} else {
			return nil
		}
	}

	return nil
}

/*****************************************************************************
 * NAME: setValue
 *
 * DESCRIPTION:
 *     Internal method to set value with dot notation support
 *
 * PARAMETERS:
 *     key   - Configuration key with dot notation support
 *     value - Value to set
 *****************************************************************************/
func (m *Manager) setValue(key string, value interface{}) {
	keys := strings.Split(key, ".")
	current := m.config

	for i, k := range keys {
		if i == len(keys)-1 {
			current[k] = value
			return
		}

		if next, ok := current[k].(map[string]interface{}); ok {
			current = next
		} else {
			next = make(map[string]interface{})
			current[k] = next
			current = next
		}
	}
}
