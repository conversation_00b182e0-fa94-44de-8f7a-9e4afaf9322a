/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      simple_encryption.go
 *
 * DESCRIPTION :    Simple XOR-based encryption utilities for frontend compatibility
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package config

import (
	"crypto/sha256"
	"encoding/base64"
	"errors"
)

// Application key, consistent with frontend
var simpleAppKey = []byte("VPNClientAppKey-2025")

/*****************************************************************************
 * NAME: SimpleDecryptCredential
 *
 * DESCRIPTION:
 *     Decrypts credentials using simple XOR method for frontend compatibility
 *
 * PARAMETERS:
 *     encoded - Base64 encoded encrypted credential from frontend
 *
 * RETURNS:
 *     string - Decrypted plain text credential
 *     error  - Error if decryption fails
 *****************************************************************************/
func SimpleDecryptCredential(encoded string) (string, error) {
	// 1. Decode Base64
	encryptedBytes, err := base64.StdEncoding.DecodeString(encoded)
	if err != nil {
		return "", err
	}

	// 2. Use fixed device ID to ensure frontend-backend consistency
	// This is not the most secure method, but ensures consistent encryption/decryption
	deviceId := "fixed-device-id-for-encryption"

	// 3. Generate key
	salt := append([]byte(deviceId), simpleAppKey...)
	input := append([]byte(deviceId), salt...)
	digest := sha256.Sum256(input)
	key := digest[:]

	// 4. Decrypt using XOR
	decryptedBytes := make([]byte, len(encryptedBytes))
	for i := 0; i < len(encryptedBytes); i++ {
		decryptedBytes[i] = encryptedBytes[i] ^ key[i%len(key)]
	}

	// 5. Convert to string
	return string(decryptedBytes), nil
}

/*****************************************************************************
 * NAME: SimpleEncryptCredential
 *
 * DESCRIPTION:
 *     Encrypts credentials using simple XOR method for frontend compatibility
 *
 * PARAMETERS:
 *     plaintext - Plain text credential to encrypt
 *
 * RETURNS:
 *     string - Base64 encoded encrypted credential
 *     error  - Error if encryption fails
 *****************************************************************************/
func SimpleEncryptCredential(plaintext string) (string, error) {
	if plaintext == "" {
		return "", errors.New("plaintext is empty")
	}

	// 1. Use fixed device ID to ensure frontend-backend consistency
	deviceId := "fixed-device-id-for-encryption"

	// 2. Generate key
	salt := append([]byte(deviceId), simpleAppKey...)
	input := append([]byte(deviceId), salt...)
	digest := sha256.Sum256(input)
	key := digest[:]

	// 3. Convert plaintext to byte array
	plaintextBytes := []byte(plaintext)

	// 4. Encrypt using XOR
	encryptedBytes := make([]byte, len(plaintextBytes))
	for i := 0; i < len(plaintextBytes); i++ {
		encryptedBytes[i] = plaintextBytes[i] ^ key[i%len(key)]
	}

	// 5. Encode to Base64
	encoded := base64.StdEncoding.EncodeToString(encryptedBytes)

	return encoded, nil
}
