/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      config.go
 *
 * DESCRIPTION :    Configuration management for the VPN client
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package config

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v2"
)

/*****************************************************************************
 * NAME: Config
 *
 * DESCRIPTION:
 *     Represents the VPN client configuration structure
 *
 * FIELDS:
 *     API     - API server settings
 *     Logging - Logging configuration
 *     VPN     - VPN-specific settings
 *****************************************************************************/
type Config struct {
	// API server settings
	API struct {
		Host string `yaml:"host"`
		Port int    `yaml:"port"`
	} `yaml:"api"`

	// Logging settings
	Logging struct {
		Level      string `yaml:"level"`       // debug, info, warn, error
		File       string `yaml:"file"`        // Log file path
		MaxSize    int    `yaml:"max_size"`    // Maximum size of a single log file (MB)
		MaxBackups int    `yaml:"max_backups"` // Maximum number of backup files
		MaxAge     int    `yaml:"max_age"`     // Log file retention days
	} `yaml:"logging"`

	// VPN settings
	VPN struct {
		// Tunnel settings
		Tunnel struct {
			Encrypt          bool `yaml:"encrypt"`           // Whether to enable encryption
			RouteMode        int  `yaml:"route_mode"`        // Route mode (0: local config, 1: server config, 2: all default)
			RouteForce       bool `yaml:"route_force"`       // Force routing (if enabled, clear default routes)
			DuplicatePackets int  `yaml:"duplicate_packets"` // Packet duplication (0: disabled, 1: enabled)
			MTU              int  `yaml:"mtu"`               // Maximum transmission unit
		} `yaml:"tunnel"`

		// Server list settings (choose either ServerListURL or ServerListFile)
		ServerListURL  string `yaml:"server_list_url"`  // Server list URL
		ServerListFile string `yaml:"server_list_file"` // Local JSON file path for server list

		// TLS settings for HTTPS server list URLs
		TLS struct {
			SkipVerify bool `yaml:"skip_verify"` // Skip TLS certificate verification (default: false)
		} `yaml:"tls"`
	} `yaml:"vpn"`

	// Note: User credentials are now managed by frontend, no longer saved to config file
}

/*****************************************************************************
 * NAME: DefaultConfig
 *
 * DESCRIPTION:
 *     Returns a configuration structure with default values
 *
 * RETURNS:
 *     *Config - Configuration with default settings
 *****************************************************************************/
func DefaultConfig() *Config {
	cfg := &Config{}

	// API server settings
	cfg.API.Host = "localhost"
	cfg.API.Port = 56543

	// Logging settings
	cfg.Logging.Level = "info"
	cfg.Logging.File = "logs/backend.log"
	cfg.Logging.MaxSize = 10
	cfg.Logging.MaxBackups = 3
	cfg.Logging.MaxAge = 28

	// VPN settings
	cfg.VPN.Tunnel.Encrypt = true
	cfg.VPN.Tunnel.RouteMode = 0
	cfg.VPN.Tunnel.RouteForce = false
	cfg.VPN.Tunnel.DuplicatePackets = 0
	cfg.VPN.Tunnel.MTU = 1400

	// Server list settings
	cfg.VPN.ServerListURL = "" // No default URL - should be set by UI
	cfg.VPN.ServerListFile = ""

	// TLS settings
	cfg.VPN.TLS.SkipVerify = true // Default to skip verification for compatibility

	// Note: User credentials are now managed by frontend, no longer saved to config file

	return cfg
}

/*****************************************************************************
 * NAME: LoadConfig
 *
 * DESCRIPTION:
 *     Loads configuration from the specified file path. If the file doesn't
 *     exist, returns default configuration.
 *
 * PARAMETERS:
 *     path - Path to the configuration file
 *
 * RETURNS:
 *     *Config - Loaded configuration
 *     error   - Error if loading fails
 *****************************************************************************/
func LoadConfig(path string) (*Config, error) {
	// Check if file exists
	if _, err := os.Stat(path); os.IsNotExist(err) {
		// File does not exist, return default configuration
		return DefaultConfig(), nil
	}

	// Read file content
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// Parse YAML
	cfg := &Config{}
	if err := yaml.Unmarshal(data, cfg); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	return cfg, nil
}

/*****************************************************************************
 * NAME: SaveConfig
 *
 * DESCRIPTION:
 *     Saves configuration to the specified file path
 *
 * PARAMETERS:
 *     cfg  - Configuration to save
 *     path - Path where to save the configuration file
 *
 * RETURNS:
 *     error - Error if saving fails
 *****************************************************************************/
func SaveConfig(cfg *Config, path string) error {
	// Ensure directory exists
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	// Serialize to YAML
	data, err := yaml.Marshal(cfg)
	if err != nil {
		return fmt.Errorf("failed to serialize config: %w", err)
	}

	// Write to file
	if err := os.WriteFile(path, data, 0600); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}
