/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      server.go
 *
 * DESCRIPTION :    WebSocket server implementation for the VPN client
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

// Package websocket provides WebSocket server implementation for the VPN client.
package websocket

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: Server
 *
 * DESCRIPTION:
 *     Represents a WebSocket server for handling real-time communication
 *
 * FIELDS:
 *     server  - HTTP server instance
 *     config  - Server configuration
 *     log     - Logger instance
 *     handler - WebSocket handler for managing connections
 *****************************************************************************/
type Server struct {
	server  *http.Server
	config  *ServerConfig
	log     logger.Logger
	handler *Handler
}

/*****************************************************************************
 * NAME: ServerConfig
 *
 * DESCRIPTION:
 *     Configuration structure for the WebSocket server
 *
 * FIELDS:
 *     Host            - Server host address
 *     Port            - Server port number
 *     ReadTimeout     - Request read timeout
 *     WriteTimeout    - Response write timeout
 *     ShutdownTimeout - Server shutdown timeout
 *****************************************************************************/
type ServerConfig struct {
	Host            string
	Port            int
	ReadTimeout     time.Duration
	WriteTimeout    time.Duration
	ShutdownTimeout time.Duration
}

/*****************************************************************************
 * NAME: DefaultServerConfig
 *
 * DESCRIPTION:
 *     Returns the default configuration for the WebSocket server
 *
 * PARAMETERS:
 *     None
 *
 * RETURNS:
 *     *ServerConfig - Default server configuration
 *****************************************************************************/
func DefaultServerConfig() *ServerConfig {
	return &ServerConfig{
		Host:            "localhost",
		Port:            8081, // Different port from HTTP server
		ReadTimeout:     15 * time.Second,
		WriteTimeout:    15 * time.Second,
		ShutdownTimeout: 5 * time.Second,
	}
}

/*****************************************************************************
 * NAME: NewServer
 *
 * DESCRIPTION:
 *     Creates a new WebSocket server with the given configuration and handler
 *
 * PARAMETERS:
 *     config  - Server configuration (uses default if nil)
 *     handler - WebSocket handler for managing connections
 *     log     - Logger instance
 *
 * RETURNS:
 *     *Server - New WebSocket server instance
 *****************************************************************************/
func NewServer(config *ServerConfig, handler *Handler, log logger.Logger) *Server {
	if config == nil {
		config = DefaultServerConfig()
	}

	mux := http.NewServeMux()

	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", config.Host, config.Port),
		Handler:      mux,
		ReadTimeout:  config.ReadTimeout,
		WriteTimeout: config.WriteTimeout,
	}

	s := &Server{
		server:  server,
		config:  config,
		log:     log,
		handler: handler,
	}

	// Register WebSocket handler
	mux.Handle("/ws", handler)

	return s
}

/*****************************************************************************
 * NAME: Start
 *
 * DESCRIPTION:
 *     Starts the WebSocket server and begins listening for connections
 *
 * PARAMETERS:
 *     None
 *
 * RETURNS:
 *     error - Error if server fails to start
 *****************************************************************************/
func (s *Server) Start() error {
	s.log.Info("WebSocket server listening", logger.String("address", s.server.Addr))
	s.log.Info("WebSocket server is ready for real-time updates")
	return s.server.ListenAndServe()
}

/*****************************************************************************
 * NAME: Stop
 *
 * DESCRIPTION:
 *     Stops the WebSocket server gracefully
 *
 * PARAMETERS:
 *     ctx - Context for shutdown timeout
 *
 * RETURNS:
 *     error - Error if server fails to stop gracefully
 *****************************************************************************/
func (s *Server) Stop(ctx context.Context) error {
	s.log.Info("Stopping WebSocket server")
	return s.server.Shutdown(ctx)
}
