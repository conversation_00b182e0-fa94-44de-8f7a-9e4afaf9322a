/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************∂ç
 * FILE NAME :      interface.go
 *
 * DESCRIPTION :    Network interface related common functions and structures
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package network

import (
	"fmt"
	"net"
	"runtime"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: PhysicalInterfaceInfo
 *
 * DESCRIPTION:
 *     Physical network interface information structure
 *
 * FIELDS:
 *     Name - Interface name
 *     Index - Interface index
 *     LUID - Interface LUID (Locally Unique Identifier)
 *     Gateway - Default gateway IP address
 *     LocalIP - Local IP address of the interface
 *****************************************************************************/
type PhysicalInterfaceInfo struct {
	Name    string // Interface name
	Index   int    // Interface index
	LUID    uint64 // Interface LUID
	Gateway net.IP // Default gateway
	LocalIP net.IP // Local IP address
}

/*****************************************************************************
 * NAME: GetPhysicalInterfaceInfo
 *
 * DESCRIPTION:
 *     Gets physical interface information including gateway, interface name, IP, etc.
 *     This avoids the overhead of repeatedly traversing interfaces.
 *
 * PARAMETERS:
 *     log - Logger instance for logging operations
 *
 * RETURNS:
 *     *PhysicalInterfaceInfo - Physical interface information structure
 *     error - Error if interface information cannot be retrieved
 *****************************************************************************/
func GetPhysicalInterfaceInfo(log logger.Logger) (*PhysicalInterfaceInfo, error) {
	return GetDefaultPhysicalInterfaceInfo(log)
}

/*****************************************************************************
 * NAME: GetDefaultPhysicalGateway
 *
 * DESCRIPTION:
 *     Gets the default gateway IP address of the physical interface.
 *     Uses Windows API for accurate and reliable detection.
 *
 * PARAMETERS:
 *     log - Logger instance for logging operations
 *
 * RETURNS:
 *     net.IP - Default gateway IP address
 *     error - Error if gateway cannot be retrieved
 *****************************************************************************/
func GetDefaultPhysicalGateway(log logger.Logger) (net.IP, error) {
	if runtime.GOOS == "windows" {
		return getDefaultGatewayViaAPI(log)
	}
	return nil, fmt.Errorf("unsupported platform: %s", runtime.GOOS)
}

/*****************************************************************************
 * NAME: GetDefaultPhysicalInterfaceInfo
 *
 * DESCRIPTION:
 *     Gets complete information of the physical interface with the default route.
 *     Uses Windows API for accurate and reliable detection.
 *
 * PARAMETERS:
 *     log - Logger instance for logging operations
 *
 * RETURNS:
 *     *PhysicalInterfaceInfo - Complete physical interface information
 *     error - Error if interface information cannot be retrieved
 *****************************************************************************/
func GetDefaultPhysicalInterfaceInfo(log logger.Logger) (*PhysicalInterfaceInfo, error) {
	if runtime.GOOS == "windows" {
		return getDefaultRouteViaAPI(log)
	}
	return nil, fmt.Errorf("unsupported platform: %s", runtime.GOOS)
}
