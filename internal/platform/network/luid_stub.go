//go:build !windows

/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      luid_stub.go
 *
 * DESCRIPTION :    Stub implementation of NetworkInterface for non-Windows
 *                  platforms. Provides no-op implementations to maintain
 *                  API compatibility.
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package network

import (
	"fmt"
	"net/netip"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: stubNetworkInterface
 *
 * DESCRIPTION:
 *     Stub implementation of NetworkInterface for non-Windows platforms
 *****************************************************************************/
type stubNetworkInterface struct {
	luid uint64
	name string
	log  logger.Logger
}

/*****************************************************************************
 * NAME: newPlatformNetworkInterface
 *
 * DESCRIPTION:
 *     Creates a new stub NetworkInterface implementation for non-Windows platforms
 *
 * PARAMETERS:
 *     luid - Locally Unique Identifier (unused on non-Windows)
 *     name - Human-readable name of the interface
 *     log  - Logger instance for operation logging
 *
 * RETURNS:
 *     NetworkInterface - Stub NetworkInterface implementation
 *     error           - Error if interface creation fails
 *****************************************************************************/
func newPlatformNetworkInterface(luid uint64, name string, log logger.Logger) (NetworkInterface, error) {
	return &stubNetworkInterface{
		luid: luid,
		name: name,
		log:  log.WithModule("network-interface-stub"),
	}, nil
}

/*****************************************************************************
 * NAME: getLUIDFromIndexPlatform
 *
 * DESCRIPTION:
 *     Stub implementation for getting LUID from interface index
 *
 * PARAMETERS:
 *     ifaceIndex - Interface index (unused on non-Windows)
 *
 * RETURNS:
 *     uint64 - Always returns 0 on non-Windows platforms
 *     error  - Always returns error indicating unsupported platform
 *****************************************************************************/
func getLUIDFromIndexPlatform(ifaceIndex uint32) (uint64, error) {
	return 0, fmt.Errorf("LUID operations not supported on non-Windows platforms")
}

// NetworkInterface implementation methods

func (s *stubNetworkInterface) LUID() uint64 {
	return s.luid
}

func (s *stubNetworkInterface) Name() string {
	return s.name
}

func (s *stubNetworkInterface) IsValid() bool {
	return false // Always invalid on non-Windows platforms
}

func (s *stubNetworkInterface) FlushIPAddresses() error {
	s.log.Warn("FlushIPAddresses not supported on non-Windows platforms")
	return fmt.Errorf("FlushIPAddresses not supported on this platform")
}

func (s *stubNetworkInterface) FlushRoutes() error {
	s.log.Warn("FlushRoutes not supported on non-Windows platforms")
	return fmt.Errorf("FlushRoutes not supported on this platform")
}

func (s *stubNetworkInterface) FlushDNS() error {
	s.log.Warn("FlushDNS not supported on non-Windows platforms")
	return fmt.Errorf("FlushDNS not supported on this platform")
}

func (s *stubNetworkInterface) AddIPAddress(prefix netip.Prefix) error {
	s.log.Warn("AddIPAddress not supported on non-Windows platforms",
		logger.String("prefix", prefix.String()))
	return fmt.Errorf("AddIPAddress not supported on this platform")
}

func (s *stubNetworkInterface) DeleteIPAddress(prefix netip.Prefix) error {
	s.log.Warn("DeleteIPAddress not supported on non-Windows platforms",
		logger.String("prefix", prefix.String()))
	return fmt.Errorf("DeleteIPAddress not supported on this platform")
}

func (s *stubNetworkInterface) AddRoute(destination netip.Prefix, gateway netip.Addr, metric uint32) error {
	s.log.Warn("AddRoute not supported on non-Windows platforms",
		logger.String("destination", destination.String()),
		logger.String("gateway", gateway.String()),
		logger.Uint32("metric", metric))
	return fmt.Errorf("AddRoute not supported on this platform")
}

func (s *stubNetworkInterface) DeleteRoute(destination netip.Prefix, gateway netip.Addr) error {
	s.log.Warn("DeleteRoute not supported on non-Windows platforms",
		logger.String("destination", destination.String()),
		logger.String("gateway", gateway.String()))
	return fmt.Errorf("DeleteRoute not supported on this platform")
}

func (s *stubNetworkInterface) SetDNS(servers []netip.Addr) error {
	s.log.Warn("SetDNS not supported on non-Windows platforms",
		logger.Int("server_count", len(servers)))
	return fmt.Errorf("SetDNS not supported on this platform")
}
