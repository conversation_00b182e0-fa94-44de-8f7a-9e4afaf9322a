/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      platform.go
 *
 * DESCRIPTION :    Platform-specific network interface function declarations
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package network

/*****************************************************************************
 * NAME: GetLUIDFromIndex
 *
 * DESCRIPTION:
 *     Gets LUID from interface index (platform-specific implementation)
 *
 * PARAMETERS:
 *     ifaceIndex - Interface index to convert to LUID
 *
 * RETURNS:
 *     uint64 - Interface LUID value
 *     error - Error if LUID cannot be retrieved
 *****************************************************************************/
func GetLUIDFromIndex(ifaceIndex uint32) (uint64, error) {
	return getLUIDFromIndexPlatform(ifaceIndex)
}
