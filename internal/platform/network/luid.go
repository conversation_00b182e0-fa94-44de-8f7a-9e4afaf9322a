/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      luid.go
 *
 * DESCRIPTION :    LUID (Locally Unique Identifier) interface abstraction for
 *                  Windows network configuration management. Provides a clean
 *                  platform-agnostic API for network interface operations.
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package network

import (
	"net/netip"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: NetworkInterface
 *
 * DESCRIPTION:
 *     Interface for network interface configuration operations.
 *     Provides platform-agnostic API for managing IP addresses, routes,
 *     and DNS settings on network interfaces.
 *****************************************************************************/
type NetworkInterface interface {
	// Bulk flush operations - efficient cleanup methods
	FlushIPAddresses() error
	FlushRoutes() error
	FlushDNS() error

	// Individual IP address operations
	AddIPAddress(prefix netip.Prefix) error
	DeleteIPAddress(prefix netip.Prefix) error

	// Individual route operations
	AddRoute(destination netip.Prefix, gateway netip.Addr, metric uint32) error
	DeleteRoute(destination netip.Prefix, gateway netip.Addr) error

	// DNS operations
	SetDNS(servers []netip.Addr) error

	// Interface information
	LUID() uint64
	Name() string
	IsValid() bool
}

/*****************************************************************************
 * NAME: NewNetworkInterface
 *
 * DESCRIPTION:
 *     Creates a new NetworkInterface instance for the given LUID.
 *     Returns platform-specific implementation.
 *
 * PARAMETERS:
 *     luid - Locally Unique Identifier of the network interface
 *     name - Human-readable name of the interface
 *     log  - Logger instance for operation logging
 *
 * RETURNS:
 *     NetworkInterface - Platform-specific network interface implementation
 *     error           - Error if interface creation fails
 *****************************************************************************/
func NewNetworkInterface(luid uint64, name string, log logger.Logger) (NetworkInterface, error) {
	return newPlatformNetworkInterface(luid, name, log)
}

/*****************************************************************************
 * NAME: NewNetworkInterfaceFromIndex
 *
 * DESCRIPTION:
 *     Creates a new NetworkInterface instance from interface index.
 *     Converts interface index to LUID and creates appropriate implementation.
 *
 * PARAMETERS:
 *     index - Network interface index
 *     name  - Human-readable name of the interface
 *     log   - Logger instance for operation logging
 *
 * RETURNS:
 *     NetworkInterface - Platform-specific network interface implementation
 *     error           - Error if interface creation fails
 *****************************************************************************/
func NewNetworkInterfaceFromIndex(index uint32, name string, log logger.Logger) (NetworkInterface, error) {
	luid, err := getLUIDFromIndexPlatform(index)
	if err != nil {
		return nil, err
	}
	return NewNetworkInterface(luid, name, log)
}
