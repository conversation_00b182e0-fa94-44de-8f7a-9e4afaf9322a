//go:build windows
// +build windows

/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      luid_windows.go
 *
 * DESCRIPTION :    Windows platform-specific LUID network interface functions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package network

import (
	"fmt"
	"mobile/internal/common/logger"
	"net/netip"
	"strings"

	"golang.org/x/sys/windows"
	"golang.zx2c4.com/wireguard/windows/tunnel/winipcfg"
)

/*****************************************************************************
 * NAME: getLUIDFromIndexPlatform
 *
 * DESCRIPTION:
 *     Gets LUID from interface index (Windows implementation)
 *
 * PARAMETERS:
 *     ifaceIndex - Interface index to convert to LUID
 *
 * RETURNS:
 *     uint64 - Interface LUID value
 *     error - Error if LUID cannot be retrieved
 *****************************************************************************/
func getLUIDFromIndexPlatform(ifaceIndex uint32) (uint64, error) {
	luid, err := winipcfg.LUIDFromIndex(ifaceIndex)
	if err != nil {
		return 0, fmt.Errorf("failed to get LUID from index %d: %w", ifaceIndex, err)
	}
	return uint64(luid), nil
}

/*****************************************************************************
 * NAME: windowsNetworkInterface
 *
 * DESCRIPTION:
 *     Windows-specific implementation of NetworkInterface using LUID
 *****************************************************************************/
type windowsNetworkInterface struct {
	luid winipcfg.LUID
	name string
	log  logger.Logger
}

/*****************************************************************************
 * NAME: newPlatformNetworkInterface
 *
 * DESCRIPTION:
 *     Creates a new Windows-specific NetworkInterface implementation
 *
 * PARAMETERS:
 *     luid - Locally Unique Identifier of the network interface
 *     name - Human-readable name of the interface
 *     log  - Logger instance for operation logging
 *
 * RETURNS:
 *     NetworkInterface - Windows NetworkInterface implementation
 *     error           - Error if interface creation fails
 *****************************************************************************/
func newPlatformNetworkInterface(luid uint64, name string, log logger.Logger) (NetworkInterface, error) {
	return &windowsNetworkInterface{
		luid: winipcfg.LUID(luid),
		name: name,
		log:  log.WithModule("network-interface"),
	}, nil
}

/*****************************************************************************
 * NAME: LUID
 *
 * DESCRIPTION:
 *     Returns the LUID of the network interface
 *
 * RETURNS:
 *     uint64 - LUID value
 *****************************************************************************/
func (w *windowsNetworkInterface) LUID() uint64 {
	return uint64(w.luid)
}

/*****************************************************************************
 * NAME: Name
 *
 * DESCRIPTION:
 *     Returns the name of the network interface
 *
 * RETURNS:
 *     string - Interface name
 *****************************************************************************/
func (w *windowsNetworkInterface) Name() string {
	return w.name
}

/*****************************************************************************
 * NAME: IsValid
 *
 * DESCRIPTION:
 *     Checks if the network interface is valid
 *
 * RETURNS:
 *     bool - True if interface is valid
 *****************************************************************************/
func (w *windowsNetworkInterface) IsValid() bool {
	return w.luid != 0
}

/*****************************************************************************
 * NAME: FlushIPAddresses
 *
 * DESCRIPTION:
 *     Removes all IP addresses from the network interface.
 *     This is more efficient than deleting addresses one by one.
 *
 * RETURNS:
 *     error - Error if flush operation fails
 *****************************************************************************/
func (w *windowsNetworkInterface) FlushIPAddresses() error {
	w.log.Info("Flushing all IP addresses from interface",
		logger.String("interface", w.name),
		logger.Uint64("luid", uint64(w.luid)))

	err := w.luid.FlushIPAddresses(winipcfg.AddressFamily(windows.AF_INET))
	if err != nil {
		w.log.Error("Failed to flush IPv4 addresses", logger.ErrorField(err))
		return fmt.Errorf("failed to flush IPv4 addresses: %w", err)
	}

	err = w.luid.FlushIPAddresses(winipcfg.AddressFamily(windows.AF_INET6))
	if err != nil {
		w.log.Warn("Failed to flush IPv6 addresses", logger.ErrorField(err))
		// Don't return error for IPv6 failure as it's less critical
	}

	w.log.Info("Successfully flushed IP addresses from interface")
	return nil
}

/*****************************************************************************
 * NAME: FlushRoutes
 *
 * DESCRIPTION:
 *     Removes all routes associated with the network interface.
 *     This is more efficient than deleting routes one by one.
 *
 * RETURNS:
 *     error - Error if flush operation fails
 *****************************************************************************/
func (w *windowsNetworkInterface) FlushRoutes() error {
	w.log.Info("Flushing all routes from interface",
		logger.String("interface", w.name),
		logger.Uint64("luid", uint64(w.luid)))

	err := w.luid.FlushRoutes(winipcfg.AddressFamily(windows.AF_INET))
	if err != nil {
		w.log.Error("Failed to flush IPv4 routes", logger.ErrorField(err))
		return fmt.Errorf("failed to flush IPv4 routes: %w", err)
	}

	err = w.luid.FlushRoutes(winipcfg.AddressFamily(windows.AF_INET6))
	if err != nil {
		w.log.Warn("Failed to flush IPv6 routes", logger.ErrorField(err))
		// Don't return error for IPv6 failure as it's less critical
	}

	w.log.Info("Successfully flushed routes from interface")
	return nil
}

/*****************************************************************************
 * NAME: FlushDNS
 *
 * DESCRIPTION:
 *     Clears all DNS settings for the network interface.
 *     Sets DNS servers to empty list.
 *
 * RETURNS:
 *     error - Error if DNS flush operation fails
 *****************************************************************************/
func (w *windowsNetworkInterface) FlushDNS() error {
	w.log.Info("Flushing DNS settings from interface",
		logger.String("interface", w.name),
		logger.Uint64("luid", uint64(w.luid)))

	// Clear IPv4 DNS
	err := w.luid.SetDNS(winipcfg.AddressFamily(windows.AF_INET), nil, nil)
	if err != nil {
		w.log.Error("Failed to flush IPv4 DNS", logger.ErrorField(err))
		return fmt.Errorf("failed to flush IPv4 DNS: %w", err)
	}

	// Clear IPv6 DNS
	err = w.luid.SetDNS(winipcfg.AddressFamily(windows.AF_INET6), nil, nil)
	if err != nil {
		w.log.Warn("Failed to flush IPv6 DNS", logger.ErrorField(err))
		// Don't return error for IPv6 failure as it's less critical
	}

	w.log.Info("Successfully flushed DNS settings from interface")
	return nil
}

/*****************************************************************************
 * NAME: AddIPAddress
 *
 * DESCRIPTION:
 *     Adds an IP address to the network interface
 *
 * PARAMETERS:
 *     prefix - IP address prefix to add
 *
 * RETURNS:
 *     error - Error if operation fails
 *****************************************************************************/
func (w *windowsNetworkInterface) AddIPAddress(prefix netip.Prefix) error {
	w.log.Debug("Adding IP address to interface",
		logger.String("interface", w.name),
		logger.String("prefix", prefix.String()))

	err := w.luid.AddIPAddress(prefix)
	if err != nil {
		w.log.Error("Failed to add IP address",
			logger.String("prefix", prefix.String()),
			logger.ErrorField(err))
		return fmt.Errorf("failed to add IP address %s: %w", prefix.String(), err)
	}

	w.log.Info("Successfully added IP address",
		logger.String("prefix", prefix.String()))
	return nil
}

/*****************************************************************************
 * NAME: DeleteIPAddress
 *
 * DESCRIPTION:
 *     Removes an IP address from the network interface
 *
 * PARAMETERS:
 *     prefix - IP address prefix to remove
 *
 * RETURNS:
 *     error - Error if operation fails
 *****************************************************************************/
func (w *windowsNetworkInterface) DeleteIPAddress(prefix netip.Prefix) error {
	w.log.Debug("Removing IP address from interface",
		logger.String("interface", w.name),
		logger.String("prefix", prefix.String()))

	err := w.luid.DeleteIPAddress(prefix)
	if err != nil {
		w.log.Error("Failed to remove IP address",
			logger.String("prefix", prefix.String()),
			logger.ErrorField(err))
		return fmt.Errorf("failed to remove IP address %s: %w", prefix.String(), err)
	}

	w.log.Info("Successfully removed IP address",
		logger.String("prefix", prefix.String()))
	return nil
}

/*****************************************************************************
 * NAME: AddRoute
 *
 * DESCRIPTION:
 *     Adds a route to the network interface
 *
 * PARAMETERS:
 *     destination - Destination network prefix
 *     gateway     - Gateway IP address
 *     metric      - Route metric
 *
 * RETURNS:
 *     error - Error if operation fails
 *****************************************************************************/
func (w *windowsNetworkInterface) AddRoute(destination netip.Prefix, gateway netip.Addr, metric uint32) error {
	w.log.Debug("Adding route to interface",
		logger.String("interface", w.name),
		logger.String("destination", destination.String()),
		logger.String("gateway", gateway.String()),
		logger.Uint32("metric", metric))

	route := &winipcfg.RouteData{
		Destination: destination,
		NextHop:     gateway,
		Metric:      metric,
	}

	err := w.luid.AddRoute(route.Destination, route.NextHop, route.Metric)
	if err != nil {
		// Check if the error is "The object already exists"
		if strings.Contains(err.Error(), "already exists") ||
			strings.Contains(err.Error(), "The object already exists") {
			w.log.Info("Route already exists, continuing",
				logger.String("destination", destination.String()),
				logger.String("gateway", gateway.String()),
				logger.Uint32("metric", metric))
			return nil
		}

		w.log.Error("Failed to add route",
			logger.String("destination", destination.String()),
			logger.String("gateway", gateway.String()),
			logger.Uint32("metric", metric),
			logger.ErrorField(err))
		return fmt.Errorf("failed to add route %s via %s: %w", destination.String(), gateway.String(), err)
	}

	w.log.Info("Successfully added route",
		logger.String("destination", destination.String()),
		logger.String("gateway", gateway.String()),
		logger.Uint32("metric", metric))
	return nil
}

/*****************************************************************************
 * NAME: DeleteRoute
 *
 * DESCRIPTION:
 *     Removes a route from the network interface
 *
 * PARAMETERS:
 *     destination - Destination network prefix
 *     gateway     - Gateway IP address
 *
 * RETURNS:
 *     error - Error if operation fails
 *****************************************************************************/
func (w *windowsNetworkInterface) DeleteRoute(destination netip.Prefix, gateway netip.Addr) error {
	w.log.Debug("Removing route from interface",
		logger.String("interface", w.name),
		logger.String("destination", destination.String()),
		logger.String("gateway", gateway.String()))

	err := w.luid.DeleteRoute(destination, gateway)
	if err != nil {
		// Check if the error is "Element not found"
		if strings.Contains(err.Error(), "Element not found") ||
			strings.Contains(err.Error(), "not found") {
			w.log.Info("Route not found, continuing",
				logger.String("destination", destination.String()),
				logger.String("gateway", gateway.String()))
			return nil
		}

		w.log.Error("Failed to remove route",
			logger.String("destination", destination.String()),
			logger.String("gateway", gateway.String()),
			logger.ErrorField(err))
		return fmt.Errorf("failed to remove route %s via %s: %w", destination.String(), gateway.String(), err)
	}

	w.log.Info("Successfully removed route",
		logger.String("destination", destination.String()),
		logger.String("gateway", gateway.String()))
	return nil
}

/*****************************************************************************
 * NAME: SetDNS
 *
 * DESCRIPTION:
 *     Sets DNS servers for the network interface
 *
 * PARAMETERS:
 *     servers - List of DNS server IP addresses
 *
 * RETURNS:
 *     error - Error if operation fails
 *****************************************************************************/
func (w *windowsNetworkInterface) SetDNS(servers []netip.Addr) error {
	w.log.Debug("Setting DNS servers for interface",
		logger.String("interface", w.name),
		logger.Int("server_count", len(servers)))

	// Convert netip.Addr to string slice for logging
	serverStrs := make([]string, len(servers))
	for i, server := range servers {
		serverStrs[i] = server.String()
	}

	// Set IPv4 DNS servers
	err := w.luid.SetDNS(winipcfg.AddressFamily(windows.AF_INET), servers, nil)
	if err != nil {
		w.log.Error("Failed to set IPv4 DNS servers",
			logger.String("servers", strings.Join(serverStrs, ", ")),
			logger.ErrorField(err))
		return fmt.Errorf("failed to set IPv4 DNS servers: %w", err)
	}

	w.log.Info("Successfully set DNS servers",
		logger.String("servers", strings.Join(serverStrs, ", ")))
	return nil
}
