//go:build windows
// +build windows

/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      api_windows.go
 *
 * DESCRIPTION :    Windows-specific API implementations for network interface operations
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package network

import (
	"fmt"
	"net"
	"strings"

	"mobile/internal/common/logger"

	"golang.zx2c4.com/wireguard/windows/tunnel/winipcfg"
)

/*****************************************************************************
 * NAME: getDefaultRouteViaAPI
 *
 * DESCRIPTION:
 *     Gets default route information using Windows API (GetIPForwardTable2).
 *     This is more reliable and faster than parsing route print command output.
 *     However, if API metric values seem unreliable, falls back to route print.
 *
 * PARAMETERS:
 *     log - Logger instance for logging operations
 *
 * RETURNS:
 *     *PhysicalInterfaceInfo - Physical interface information with default route
 *     error - Error if route cannot be retrieved via API
 *****************************************************************************/
func getDefaultRouteViaAPI(log logger.Logger) (*PhysicalInterfaceInfo, error) {
	log.Debug("Attempting to get default route via Windows API")

	// Get IPv4 forward table using Windows API
	routes, err := winipcfg.GetIPForwardTable2(winipcfg.AddressFamily(2)) // AF_INET = 2
	if err != nil {
		log.Debug("Failed to get IP forward table via API", logger.ErrorField(err))
		return nil, fmt.Errorf("failed to get IP forward table: %w", err)
	}

	log.Debug("Retrieved IP forward table", logger.Int("route_count", len(routes)))

	var bestInfo *PhysicalInterfaceInfo
	var bestMetric uint32 = ^uint32(0) // Max uint32 value

	// Look for default routes (0.0.0.0/0)
	for _, route := range routes {
		// Check if this is a default route (destination 0.0.0.0/0)
		destPrefix := route.DestinationPrefix.Prefix()
		if !destPrefix.Addr().IsUnspecified() || destPrefix.Bits() != 0 {
			continue
		}

		// Get next hop (gateway) address
		nextHop := route.NextHop.Addr()
		if !nextHop.IsValid() || nextHop.IsLoopback() {
			continue
		}

		// Get interface information
		interfaceIndex := route.InterfaceIndex
		interfaceLUID := uint64(route.InterfaceLUID)

		log.Debug("Processing default route via API",
			logger.Uint32("interface_index", interfaceIndex),
			logger.String("gateway", nextHop.String()),
			logger.Uint32("metric", route.Metric),
			logger.Uint64("luid", interfaceLUID))

		// Get interface details
		interfaces, err := net.Interfaces()
		if err != nil {
			log.Debug("Failed to get interfaces", logger.ErrorField(err))
			continue
		}

		var interfaceName string
		var localIP net.IP
		found := false

		for _, iface := range interfaces {
			if uint32(iface.Index) == interfaceIndex {
				interfaceName = iface.Name

				log.Debug("Found matching interface",
					logger.String("interface", interfaceName),
					logger.Uint32("interface_index", interfaceIndex))

				// 只排除我们自己的VPN接口，避免路由循环
				if isOurVPNInterface(interfaceName) {
					log.Debug("Skipping our own VPN interface to avoid routing loop",
						logger.String("interface", interfaceName),
						logger.String("gateway", nextHop.String()),
						logger.Uint32("metric", route.Metric))
					found = false
					break
				}

				log.Debug("Found interface for default route",
					logger.String("interface", interfaceName),
					logger.String("gateway", nextHop.String()),
					logger.Uint32("metric", route.Metric))

				// Get interface IP address
				addrs, err := iface.Addrs()
				if err != nil {
					log.Debug("Failed to get interface addresses",
						logger.String("interface", interfaceName),
						logger.ErrorField(err))
					continue
				}

				for _, addr := range addrs {
					if ipNet, ok := addr.(*net.IPNet); ok && ipNet.IP.To4() != nil {
						if !ipNet.IP.IsLinkLocalUnicast() {
							localIP = ipNet.IP
							found = true
							log.Debug("Found valid IP for interface",
								logger.String("interface", interfaceName),
								logger.String("local_ip", localIP.String()))
							break
						}
					}
				}
				break
			}
		}

		if !found || localIP == nil {
			log.Debug("Interface not found or no valid IP",
				logger.Uint32("interface_index", interfaceIndex),
				logger.Bool("found", found),
				logger.Bool("has_ip", localIP != nil))
			continue
		}

		// Get the interface metric to calculate the complete metric
		interfaceMetric, err := getInterfaceMetric(interfaceLUID, log)
		if err != nil {
			log.Debug("Failed to get interface metric, using route metric only",
				logger.String("interface", interfaceName),
				logger.ErrorField(err))
			interfaceMetric = 0
		}

		// Calculate the complete metric (interface metric + route metric offset)
		completeMetric := interfaceMetric + route.Metric

		log.Debug("Calculated complete metric",
			logger.String("interface", interfaceName),
			logger.Uint32("interface_metric", interfaceMetric),
			logger.Uint32("route_metric_offset", route.Metric),
			logger.Uint32("complete_metric", completeMetric))

		log.Debug("Comparing complete metrics",
			logger.String("interface", interfaceName),
			logger.Uint32("current_complete_metric", completeMetric),
			logger.Uint32("best_metric", bestMetric))

		// Select route with smallest complete metric
		if completeMetric < bestMetric {
			// Convert netip.Addr to net.IP
			gatewayBytes := nextHop.AsSlice()
			gateway := net.IP(gatewayBytes)

			bestInfo = &PhysicalInterfaceInfo{
				Name:    interfaceName,
				Index:   int(interfaceIndex),
				LUID:    interfaceLUID,
				Gateway: gateway,
				LocalIP: localIP,
			}
			bestMetric = completeMetric

			log.Debug("Found better default route via API",
				logger.String("interface", interfaceName),
				logger.String("gateway", gateway.String()),
				logger.String("local_ip", localIP.String()),
				logger.Uint32("route_metric_offset", route.Metric),
				logger.Uint32("interface_metric", interfaceMetric),
				logger.Uint32("complete_metric", completeMetric),
				logger.Uint64("luid", interfaceLUID))
		} else {
			log.Debug("Route metric not better than current best",
				logger.String("interface", interfaceName),
				logger.Uint32("current_complete_metric", completeMetric),
				logger.Uint32("best_metric", bestMetric))
		}
	}

	if bestInfo != nil {
		log.Info("Successfully retrieved default route via Windows API",
			logger.String("interface", bestInfo.Name),
			logger.String("gateway", bestInfo.Gateway.String()),
			logger.String("local_ip", bestInfo.LocalIP.String()),
			logger.Int("index", bestInfo.Index),
			logger.Uint64("luid", bestInfo.LUID),
			logger.Uint32("metric", bestMetric))
		return bestInfo, nil
	}

	return nil, fmt.Errorf("no suitable default route found via API")
}

/*****************************************************************************
 * NAME: getDefaultGatewayViaAPI
 *
 * DESCRIPTION:
 *     Gets default gateway IP address using Windows API.
 *     This is a simplified version that only returns the gateway IP.
 *
 * PARAMETERS:
 *     log - Logger instance for logging operations
 *
 * RETURNS:
 *     net.IP - Default gateway IP address
 *     error - Error if gateway cannot be retrieved via API
 *****************************************************************************/
func getDefaultGatewayViaAPI(log logger.Logger) (net.IP, error) {
	info, err := getDefaultRouteViaAPI(log)
	if err != nil {
		return nil, err
	}
	return info.Gateway, nil
}

/*****************************************************************************
 * NAME: getInterfaceMetric
 *
 * DESCRIPTION:
 *     Gets the interface metric for a specific interface using Windows API.
 *     This is needed because route.Metric from GetIPForwardTable2 is only
 *     the route metric offset, not the complete metric.
 *
 * PARAMETERS:
 *     interfaceLUID - Interface LUID
 *     log - Logger instance for logging operations
 *
 * RETURNS:
 *     uint32 - Interface metric value
 *     error - Error if metric cannot be retrieved
 *****************************************************************************/
func getInterfaceMetric(interfaceLUID uint64, log logger.Logger) (uint32, error) {
	// Convert LUID to winipcfg.LUID type
	luid := winipcfg.LUID(interfaceLUID)

	// Get IPv4 interface entry
	ipInterface, err := luid.IPInterface(winipcfg.AddressFamily(2)) // AF_INET = 2
	if err != nil {
		log.Debug("Failed to get IP interface entry",
			logger.Uint64("luid", interfaceLUID),
			logger.ErrorField(err))
		return 0, fmt.Errorf("failed to get IP interface entry: %w", err)
	}

	log.Debug("Retrieved interface metric",
		logger.Uint64("luid", interfaceLUID),
		logger.Uint32("interface_metric", ipInterface.Metric))

	return ipInterface.Metric, nil
}

/*****************************************************************************
 * NAME: isOurVPNInterface
 *
 * DESCRIPTION:
 *     Checks if the interface is our own VPN interface to avoid routing loops.
 *     Only checks for interfaces that we create ourselves.
 *
 * PARAMETERS:
 *     interfaceName - Name of the network interface to check
 *
 * RETURNS:
 *     bool - True if this is our VPN interface, false otherwise
 *****************************************************************************/
func isOurVPNInterface(interfaceName string) bool {
	name := strings.ToLower(interfaceName)

	// 只检查我们自己创建的VPN接口
	ourVPNPatterns := []string{
		"unitun",     // ItForce默认TUN接口名称前缀
		"panabittun", // PanabitTun接口
		"itforce",    // ItForce相关接口
		"panabit",    // Panabit相关接口
	}

	for _, pattern := range ourVPNPatterns {
		if strings.Contains(name, pattern) {
			return true
		}
	}

	return false
}
