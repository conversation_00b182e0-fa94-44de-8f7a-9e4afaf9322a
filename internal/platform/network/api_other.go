//go:build !windows
// +build !windows

/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      api_other.go
 *
 * DESCRIPTION :    Non-Windows platform stubs for network interface API operations
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package network

import (
	"fmt"
	"net"
	"runtime"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: getDefaultRouteViaAPI
 *
 * DESCRIPTION:
 *     Stub implementation for non-Windows platforms.
 *     Always returns an error indicating API is not available.
 *
 * PARAMETERS:
 *     log - Logger instance for logging operations
 *
 * RETURNS:
 *     *PhysicalInterfaceInfo - Always nil
 *     error - Error indicating API not available on this platform
 *****************************************************************************/
func getDefaultRouteViaAPI(log logger.Logger) (*PhysicalInterfaceInfo, error) {
	return nil, fmt.Errorf("Windows API not available on %s", runtime.GOOS)
}

/*****************************************************************************
 * NAME: getDefaultGatewayViaAPI
 *
 * DESCRIPTION:
 *     Stub implementation for non-Windows platforms.
 *     Always returns an error indicating API is not available.
 *
 * PARAMETERS:
 *     log - Logger instance for logging operations
 *
 * RETURNS:
 *     net.IP - Always nil
 *     error - Error indicating API not available on this platform
 *****************************************************************************/
func getDefaultGatewayViaAPI(log logger.Logger) (net.IP, error) {
	return nil, fmt.Errorf("Windows API not available on %s", runtime.GOOS)
}
