/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      manager.go
 *
 * DESCRIPTION :    TUN device manager implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package tun

import (
	"fmt"
	"net"
	"sync"

	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
)

var (
	// Global device instance
	device Device

	// Mutex for thread-safe access to the device
	deviceMutex sync.RWMutex

	// Logger instance
	log logger.Logger

	// Initialization flag
	initialized bool
)

/*****************************************************************************
 * NAME: Initialize
 *
 * DESCRIPTION:
 *     Initializes the TUN subsystem
 *
 * PARAMETERS:
 *     logger - Logger instance
 *
 * RETURNS:
 *     error - Error if initialization fails
 *****************************************************************************/
func Initialize(logger logger.Logger) error {
	//deviceMutex.Lock()
	//defer deviceMutex.Unlock()

	if initialized {
		return nil
	}

	log = logger.WithModule("tun-manager")
	log.Info("Initializing TUN subsystem")

	initialized = true
	return nil
}

/*****************************************************************************
 * NAME: CreateDevice
 *
 * DESCRIPTION:
 *     Creates a new TUN device or returns the existing one with proper locking
 *
 * RETURNS:
 *     Device - TUN device
 *     error - Error if device creation fails
 *****************************************************************************/
func CreateDevice() (Device, error) {
	//deviceMutex.Lock()
	//defer deviceMutex.Unlock()

	if !initialized {
		err := errors.NewWithCode(errors.CodeTunnelDeviceError,
			"TUN subsystem not initialized").(*errors.DetailedError)
		return nil, err.WithComponent("tun").WithOperation("create")
	}

	if device != nil {
		log.Debug("Returning existing TUN device")
		return device, nil
	}

	log.Info("Creating new TUN device")

	var err error
	// Create platform-specific device
	device, err = createPlatformDevice(log)
	if err != nil {
		log.Error("Failed to create TUN device", logger.ErrorField(err))
		wrappedErr := errors.WrapWithCode(errors.CodeTunnelDeviceError, err,
			"failed to create TUN device").(*errors.DetailedError)
		return nil, wrappedErr.WithComponent("tun").WithOperation("create")
	}

	log.Info("TUN device created successfully")
	return device, nil
}

/*****************************************************************************
 * NAME: GetDevice
 *
 * DESCRIPTION:
 *     Returns the existing TUN device or nil if none exists with proper locking
 *
 * RETURNS:
 *     Device - TUN device or nil
 *****************************************************************************/
func GetDevice() Device {
	//deviceMutex.RLock()
	//defer deviceMutex.RUnlock()

	return device
}

/*****************************************************************************
 * NAME: CloseDevice
 *
 * DESCRIPTION:
 *     Closes the TUN device with proper locking and error handling
 *
 * RETURNS:
 *     error - Error if device closure fails
 *****************************************************************************/
func CloseDevice() error {
	//deviceMutex.Lock()
	//defer deviceMutex.Unlock()

	if device == nil {
		log.Debug("No TUN device to close")
		return nil
	}

	log.Info("Closing TUN device")

	err := device.Close()
	if err != nil {
		log.Error("Failed to close TUN device", logger.ErrorField(err))
		wrappedErr := errors.WrapWithCode(errors.CodeTunnelCloseFailed, err,
			"failed to close TUN device").(*errors.DetailedError)
		return wrappedErr.WithComponent("tun").WithOperation("close")
	}

	device = nil
	log.Info("TUN device closed successfully")
	return nil
}

/*****************************************************************************
 * NAME: Shutdown
 *
 * DESCRIPTION:
 *     Shuts down the TUN subsystem with proper locking and error handling
 *
 * RETURNS:
 *     error - Error if shutdown fails
 *****************************************************************************/
func Shutdown() error {
	//deviceMutex.Lock()
	//defer deviceMutex.Unlock()

	if !initialized {
		return nil
	}

	log.Info("Shutting down TUN subsystem")

	if device != nil {
		err := device.Close()
		if err != nil {
			log.Error("Failed to close TUN device during shutdown", logger.ErrorField(err))
			wrappedErr := errors.WrapWithCode(errors.CodeTunnelCloseFailed, err,
				"failed to close TUN device during shutdown").(*errors.DetailedError)
			return wrappedErr.WithComponent("tun").WithOperation("shutdown")
		}
		device = nil
	}

	initialized = false
	log.Info("TUN subsystem shut down successfully")
	return nil
}

// Convenience methods - Network configuration

/*****************************************************************************
 * NAME: ConfigureDevice
 *
 * DESCRIPTION:
 *     Configures the global TUN device with proper locking
 *
 * PARAMETERS:
 *     config - Device configuration
 *
 * RETURNS:
 *     error - Configuration error if any
 *****************************************************************************/
func ConfigureDevice(config DeviceConfig) error {
	//deviceMutex.RLock()
	//defer deviceMutex.RUnlock()

	if device == nil {
		err := errors.NewWithCode(errors.CodeTunnelDeviceError,
			"no TUN device available").(*errors.DetailedError)
		return err.WithComponent("tun").WithOperation("configure")
	}

	log.Info("Configuring global TUN device",
		logger.String("name", config.Name),
		logger.Int("mtu", config.MTU),
		logger.Any("addresses", config.Addresses))

	if err := device.Configure(config); err != nil {
		wrappedErr := errors.WrapWithCode(errors.CodeTunnelConfigFailed, err,
			"failed to configure TUN device").(*errors.DetailedError)
		return wrappedErr.WithComponent("tun").WithOperation("configure")
	}

	return nil
}

/*****************************************************************************
 * NAME: StartDevice
 *
 * DESCRIPTION:
 *     Starts the global TUN device with proper locking
 *
 * RETURNS:
 *     error - Start error if any
 *****************************************************************************/
func StartDevice() error {
	//deviceMutex.RLock()
	if device == nil {
		//deviceMutex.RUnlock()
		err := errors.NewWithCode(errors.CodeTunnelDeviceError,
			"no TUN device available").(*errors.DetailedError)
		return err.WithComponent("tun").WithOperation("start")
	}

	// Get local reference to device to minimize lock time
	localDevice := device
	//deviceMutex.RUnlock()

	log.Info("Starting global TUN device")

	// Add detailed logging
	log.Debug("Device details before starting",
		logger.String("device_type", fmt.Sprintf("%T", localDevice)),
		logger.String("device_name", localDevice.Name()),
		logger.Int("device_mtu", localDevice.MTU()),
		logger.Bool("device_is_up", localDevice.IsUp()))

	// Start device outside of lock to avoid long lock holding
	err := localDevice.Start()
	if err != nil {
		log.Error("Failed to start TUN device", logger.ErrorField(err))
		wrappedErr := errors.WrapWithCode(errors.CodeTunnelError, err,
			"failed to start TUN device").(*errors.DetailedError)
		return wrappedErr.WithComponent("tun").WithOperation("start")
	}

	log.Info("TUN device started successfully")
	return nil
}

/*****************************************************************************
 * NAME: StopDevice
 *
 * DESCRIPTION:
 *     Stops the global TUN device
 *
 * RETURNS:
 *     error - Stop error if any
 *****************************************************************************/
func StopDevice() error {
	//deviceMutex.RLock()
	if device == nil {
		//deviceMutex.RUnlock()
		return errors.NewWithCode(errors.CodeTunnelDeviceError,
			"no TUN device available")
	}

	// Get local reference to device to minimize lock time
	localDevice := device
	//deviceMutex.RUnlock()

	log.Info("Stopping global TUN device")

	// Stop device outside of lock to avoid long lock holding
	return localDevice.Stop()
}

/*****************************************************************************
 * NAME: GetDeviceName
 *
 * DESCRIPTION:
 *     Gets the global TUN device name
 *
 * RETURNS:
 *     string - Device name
 *     error - Error if device is not available
 *****************************************************************************/
func GetDeviceName() (string, error) {
	//deviceMutex.RLock()
	//defer deviceMutex.RUnlock()

	if device == nil {
		return "", errors.NewWithCode(errors.CodeTunnelDeviceError,
			"no TUN device available")
	}

	return device.Name(), nil
}

/*****************************************************************************
 * NAME: GetDeviceMTU
 *
 * DESCRIPTION:
 *     Gets the global TUN device MTU
 *
 * RETURNS:
 *     int - MTU value
 *     error - Error if device is not available
 *****************************************************************************/
func GetDeviceMTU() (int, error) {
	//deviceMutex.RLock()
	//defer deviceMutex.RUnlock()

	if device == nil {
		return 0, errors.NewWithCode(errors.CodeTunnelDeviceError,
			"no TUN device available")
	}

	return device.MTU(), nil
}

/*****************************************************************************
 * NAME: SetDeviceMTU
 *
 * DESCRIPTION:
 *     Sets the global TUN device MTU
 *
 * PARAMETERS:
 *     mtu - MTU value
 *
 * RETURNS:
 *     error - Error if setting fails
 *****************************************************************************/
func SetDeviceMTU(mtu int) error {
	//deviceMutex.Lock()
	//defer deviceMutex.Unlock()

	if device == nil {
		return errors.NewWithCode(errors.CodeTunnelDeviceError,
			"no TUN device available")
	}

	log.Info("Setting global TUN device MTU", logger.Int("mtu", mtu))
	return device.SetMTU(mtu)
}

/*****************************************************************************
 * NAME: GetDeviceAddresses
 *
 * DESCRIPTION:
 *     Gets the global TUN device IP addresses
 *
 * RETURNS:
 *     []net.IPNet - IP address list
 *     error - Error if device is not available
 *****************************************************************************/
func GetDeviceAddresses() ([]net.IPNet, error) {
	//deviceMutex.RLock()
	//defer deviceMutex.RUnlock()

	if device == nil {
		return nil, errors.NewWithCode(errors.CodeTunnelDeviceError,
			"no TUN device available")
	}

	return device.Addresses(), nil
}

/*****************************************************************************
 * NAME: SetDeviceAddresses
 *
 * DESCRIPTION:
 *     Sets the global TUN device IP addresses
 *
 * PARAMETERS:
 *     addresses - IP address list
 *
 * RETURNS:
 *     error - Error if setting fails
 *****************************************************************************/
func SetDeviceAddresses(addresses []net.IPNet) error {
	//deviceMutex.Lock()
	//defer deviceMutex.Unlock()

	if device == nil {
		return errors.NewWithCode(errors.CodeTunnelDeviceError,
			"no TUN device available")
	}

	log.Info("Setting global TUN device addresses", logger.Any("addresses", addresses))
	return device.SetAddresses(addresses)
}

/*****************************************************************************
 * NAME: AddDeviceRoute
 *
 * DESCRIPTION:
 *     Adds a route to the global TUN device
 *
 * PARAMETERS:
 *     destination - Destination network
 *     gateway - Gateway address
 *     metric - Route metric value
 *
 * RETURNS:
 *     error - Error if adding route fails
 *****************************************************************************/
func AddDeviceRoute(destination net.IPNet, gateway net.IP, metric int) error {
	//deviceMutex.RLock()
	//defer deviceMutex.RUnlock()

	if device == nil {
		return errors.NewWithCode(errors.CodeTunnelDeviceError,
			"no TUN device available")
	}

	log.Info("Adding route to global TUN device",
		logger.String("destination", destination.String()),
		logger.String("gateway", gateway.String()),
		logger.Int("metric", metric))

	return device.AddRoute(destination, gateway, metric)
}

/*****************************************************************************
 * NAME: DeleteDeviceRoute
 *
 * DESCRIPTION:
 *     Deletes a route from the global TUN device
 *
 * PARAMETERS:
 *     destination - Destination network
 *     gateway - Gateway address (optional, if nil will try empty gateway)
 *
 * RETURNS:
 *     error - Error if deleting route fails
 *****************************************************************************/
func DeleteDeviceRoute(destination net.IPNet, gateway net.IP) error {
	//deviceMutex.RLock()
	//defer deviceMutex.RUnlock()

	if device == nil {
		return errors.NewWithCode(errors.CodeTunnelDeviceError,
			"no TUN device available")
	}

	gatewayStr := "nil"
	if gateway != nil {
		gatewayStr = gateway.String()
	}
	log.Info("Deleting route from global TUN device",
		logger.String("destination", destination.String()),
		logger.String("gateway", gatewayStr))

	return device.DeleteRoute(destination, gateway)
}

/*****************************************************************************
 * NAME: SetDeviceDNS
 *
 * DESCRIPTION:
 *     Sets DNS servers for the global TUN device
 *
 * PARAMETERS:
 *     servers - DNS server IP address list
 *
 * RETURNS:
 *     error - Error if setting DNS fails
 *****************************************************************************/
func SetDeviceDNS(servers []net.IP) error {
	//deviceMutex.RLock()
	//defer deviceMutex.RUnlock()

	if device == nil {
		return errors.NewWithCode(errors.CodeTunnelDeviceError,
			"no TUN device available")
	}

	log.Info("Setting DNS servers for global TUN device",
		logger.Any("servers", servers))

	return device.SetDNS(servers)
}

/*****************************************************************************
 * NAME: SetDeviceDefaultGateway
 *
 * DESCRIPTION:
 *     Sets the default gateway for the global TUN device
 *
 * PARAMETERS:
 *     gateway - Default gateway IP address
 *
 * RETURNS:
 *     error - Error if setting gateway fails
 *****************************************************************************/
func SetDeviceDefaultGateway(gateway net.IP) error {
	//deviceMutex.RLock()
	//defer deviceMutex.RUnlock()

	if device == nil {
		return errors.NewWithCode(errors.CodeTunnelDeviceError,
			"no TUN device available")
	}

	log.Info("Setting default gateway for global TUN device",
		logger.String("gateway", gateway.String()))

	return device.SetDefaultGateway(gateway)
}

/*****************************************************************************
 * NAME: IsDeviceUp
 *
 * DESCRIPTION:
 *     Checks if the global TUN device is up
 *
 * RETURNS:
 *     bool - Whether the device is up
 *     error - Error if check fails
 *****************************************************************************/
func IsDeviceUp() (bool, error) {
	//deviceMutex.RLock()
	//defer deviceMutex.RUnlock()

	if device == nil {
		return false, errors.NewWithCode(errors.CodeTunnelDeviceError,
			"no TUN device available")
	}

	return device.IsUp(), nil
}

/*****************************************************************************
 * NAME: FlushDeviceConfiguration
 *
 * DESCRIPTION:
 *     Flushes all configuration (IP addresses, routes, DNS) from the global TUN device
 *     without stopping the device. Uses individual operations to clear configuration.
 *
 * RETURNS:
 *     error - Error if flush operations fail
 *****************************************************************************/
func FlushDeviceConfiguration() error {
	//deviceMutex.RLock()
	//defer deviceMutex.RUnlock()

	if device == nil {
		return errors.NewWithCode(errors.CodeTunnelDeviceError,
			"no TUN device available")
	}

	log.Info("Flushing global TUN device configuration (IP, routes, DNS)")

	// Use device's FlushConfiguration method which uses NetworkInterface bulk operations when available
	if err := device.FlushConfiguration(); err != nil {
		log.Warn("Device FlushConfiguration failed, using fallback method", logger.ErrorField(err))

		// Fallback: Clear configuration using individual device methods
		log.Info("Using fallback method for device configuration flush")

		// Clear IP addresses by setting empty list
		if err := device.SetAddresses([]net.IPNet{}); err != nil {
			log.Error("Failed to clear IP addresses", logger.ErrorField(err))
			return fmt.Errorf("failed to clear IP addresses: %w", err)
		}

		// Clear DNS by setting empty list
		if err := device.SetDNS([]net.IP{}); err != nil {
			log.Error("Failed to clear DNS", logger.ErrorField(err))
			return fmt.Errorf("failed to clear DNS: %w", err)
		}

		log.Info("TUN device configuration flushed successfully using fallback method")
		return nil
	}

	log.Info("TUN device configuration flushed successfully using bulk operations")
	return nil
}
