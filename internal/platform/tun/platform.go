/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      platform.go
 *
 * DESCRIPTION :    Platform-specific TUN device creation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package tun

import (
	"runtime"

	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: createPlatformDevice
 *
 * DESCRIPTION:
 *     Creates a platform-specific TUN device
 *
 * PARAMETERS:
 *     log - Logger instance
 *
 * RETURNS:
 *     Device - TUN device
 *     error - Error if device creation fails
 *****************************************************************************/
func createPlatformDevice(log logger.Logger) (Device, error) {
	switch runtime.GOOS {
	case "windows":
		return newWindowsDevice(log)
	case "darwin":
		return nil, errors.NewWithCode(errors.CodePlatformUnsupported,
			"macOS TUN device not yet implemented")
	case "linux":
		return nil, errors.NewWithCode(errors.CodePlatformUnsupported,
			"Linux TUN device not yet implemented")
	default:
		return nil, errors.NewWithCode(errors.CodePlatformUnsupported,
			"unsupported platform: "+runtime.GOOS)
	}
}
