//go:build !windows
// +build !windows

/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      platform_stub.go
 *
 * DESCRIPTION :    Platform stub for non-Windows platforms
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package tun

import (
	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: newWindowsDevice
 *
 * DESCRIPTION:
 *     Creates a stub for non-Windows platforms
 *
 * PARAMETERS:
 *     log - Logger instance (unused in stub)
 *
 * RETURNS:
 *     Device - Always nil for non-Windows platforms
 *     error - Platform unsupported error
 *****************************************************************************/
func newWindowsDevice(log logger.Logger) (Device, error) {
	return nil, errors.NewWithCode(errors.CodePlatformUnsupported,
		"Windows TUN device not available on this platform")
}
