//go:build windows
// +build windows

/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      windows.go
 *
 * DESCRIPTION :    Windows-specific TUN implementation using direct WinTun API
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package tun

import (
	"fmt"
	"net"
	"net/netip"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"syscall"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
	"golang.zx2c4.com/wireguard/windows/tunnel/winipcfg"

	"mobile/internal/common/logger"
	"mobile/internal/platform/network"
)

const (
	// WintunDLLName is the name of the Wintun DLL
	WintunDLLName = "wintun.dll"

	// DefaultNamePrefix is the default prefix for TUN device names
	DefaultNamePrefix = "PanabitTun"

	// DefaultMTU is the default MTU for TUN devices
	DefaultMTU = 1420
)

// WinTun DLL function types
type (
	createAdapterFunc           func(pool, name string, requestedGUID *windows.GUID, rebootRequired *bool) uintptr
	openAdapterFunc             func(name string) uintptr
	closeAdapterFunc            func(adapter uintptr)
	deleteAdapterFunc           func(adapter uintptr, rebootRequired *bool) bool
	getAdapterLUIDFunc          func(adapter uintptr) uint64
	getRunningDriverVersionFunc func() uint32
	setLoggerFunc               func(newLogger uintptr)
	startSessionFunc            func(adapter uintptr, capacity uint32) uintptr
	endSessionFunc              func(session uintptr)
	getReadWaitEventFunc        func(session uintptr) windows.Handle
	receivePacketFunc           func(session uintptr, packetSize *uint32) uintptr
	releaseReceivePacketFunc    func(session uintptr, packet uintptr)
	allocateSendPacketFunc      func(session uintptr, packetSize uint32) uintptr
	sendPacketFunc              func(session uintptr, packet uintptr)
)

// WinTun represents the WinTun DLL and its functions
type WinTun struct {
	dll                     *windows.DLL
	createAdapter           *windows.Proc
	openAdapter             *windows.Proc
	closeAdapter            *windows.Proc
	deleteAdapter           *windows.Proc
	getAdapterLUID          *windows.Proc
	getRunningDriverVersion *windows.Proc
	setLogger               *windows.Proc
	startSession            *windows.Proc
	endSession              *windows.Proc
	getReadWaitEvent        *windows.Proc
	receivePacket           *windows.Proc
	releaseReceivePacket    *windows.Proc
	allocateSendPacket      *windows.Proc
	sendPacket              *windows.Proc
}

// windowsDevice implements the Device interface for Windows
type windowsDevice struct {
	name         string
	mtu          int
	addresses    []net.IPNet
	isUp         bool
	handle       uintptr
	wintun       *WinTun
	session      uintptr
	readWait     windows.Handle
	luid         winipcfg.LUID
	netInterface network.NetworkInterface // New LUID interface abstraction
	mutex        sync.RWMutex
	log          logger.Logger
}

/*****************************************************************************
 * NAME: newWindowsDevice
 *
 * DESCRIPTION:
 *     Creates a new Windows TUN device instance using WinTun driver
 *
 * PARAMETERS:
 *     log - Logger instance for device operations
 *
 * RETURNS:
 *     Device - TUN device interface implementation
 *     error - Error if device creation fails
 *****************************************************************************/
func newWindowsDevice(log logger.Logger) (Device, error) {
	deviceLog := log.WithModule("win-tun")
	deviceLog.Debug("Creating Windows TUN device")

	// Create a new device with default values
	device := &windowsDevice{
		name: DefaultNamePrefix,
		mtu:  DefaultMTU,
		isUp: false,
		log:  deviceLog,
	}

	return device, nil
}

/*****************************************************************************
 * NAME: NewWinTun
 *
 * DESCRIPTION:
 *     Creates a new WinTun instance by loading the WinTun DLL and initializing
 *     all required function pointers for TUN operations
 *
 * RETURNS:
 *     *WinTun - WinTun instance with loaded function pointers
 *     error - Error if DLL loading or function resolution fails
 *****************************************************************************/
func NewWinTun() (*WinTun, error) {
	// Find wintun.dll
	dllPath, err := findWintunDLL()
	if err != nil {
		return nil, fmt.Errorf("failed to find wintun.dll: %w", err)
	}

	// Load the DLL
	dll, err := windows.LoadDLL(dllPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load wintun.dll from %s: %w", dllPath, err)
	}

	// Load the functions
	wt := &WinTun{dll: dll}

	// Load required functions
	var lastErr error
	wt.createAdapter, lastErr = dll.FindProc("WintunCreateAdapter")
	if lastErr != nil {
		dll.Release()
		return nil, fmt.Errorf("failed to find WintunCreateAdapter: %w", lastErr)
	}

	wt.openAdapter, lastErr = dll.FindProc("WintunOpenAdapter")
	if lastErr != nil {
		dll.Release()
		return nil, fmt.Errorf("failed to find WintunOpenAdapter: %w", lastErr)
	}

	wt.closeAdapter, lastErr = dll.FindProc("WintunCloseAdapter")
	if lastErr != nil {
		dll.Release()
		return nil, fmt.Errorf("failed to find WintunCloseAdapter: %w", lastErr)
	}

	wt.getAdapterLUID, lastErr = dll.FindProc("WintunGetAdapterLUID")
	if lastErr != nil {
		dll.Release()
		return nil, fmt.Errorf("failed to find WintunGetAdapterLUID: %w", lastErr)
	}

	wt.getRunningDriverVersion, lastErr = dll.FindProc("WintunGetRunningDriverVersion")
	if lastErr != nil {
		dll.Release()
		return nil, fmt.Errorf("failed to find WintunGetRunningDriverVersion: %w", lastErr)
	}

	wt.startSession, lastErr = dll.FindProc("WintunStartSession")
	if lastErr != nil {
		dll.Release()
		return nil, fmt.Errorf("failed to find WintunStartSession: %w", lastErr)
	}

	wt.endSession, lastErr = dll.FindProc("WintunEndSession")
	if lastErr != nil {
		dll.Release()
		return nil, fmt.Errorf("failed to find WintunEndSession: %w", lastErr)
	}

	wt.getReadWaitEvent, lastErr = dll.FindProc("WintunGetReadWaitEvent")
	if lastErr != nil {
		dll.Release()
		return nil, fmt.Errorf("failed to find WintunGetReadWaitEvent: %w", lastErr)
	}

	wt.receivePacket, lastErr = dll.FindProc("WintunReceivePacket")
	if lastErr != nil {
		dll.Release()
		return nil, fmt.Errorf("failed to find WintunReceivePacket: %w", lastErr)
	}

	wt.releaseReceivePacket, lastErr = dll.FindProc("WintunReleaseReceivePacket")
	if lastErr != nil {
		dll.Release()
		return nil, fmt.Errorf("failed to find WintunReleaseReceivePacket: %w", lastErr)
	}

	wt.allocateSendPacket, lastErr = dll.FindProc("WintunAllocateSendPacket")
	if lastErr != nil {
		dll.Release()
		return nil, fmt.Errorf("failed to find WintunAllocateSendPacket: %w", lastErr)
	}

	wt.sendPacket, lastErr = dll.FindProc("WintunSendPacket")
	if lastErr != nil {
		dll.Release()
		return nil, fmt.Errorf("failed to find WintunSendPacket: %w", lastErr)
	}

	// Load optional functions (these may not be available in all WinTun versions)
	// WintunDeleteAdapter - optional function for deleting adapters
	wt.deleteAdapter, _ = dll.FindProc("WintunDeleteAdapter")

	// WintunSetLogger - optional function for setting custom logger
	wt.setLogger, _ = dll.FindProc("WintunSetLogger")

	return wt, nil
}

/*****************************************************************************
 * NAME: CreateAdapter
 *
 * DESCRIPTION:
 *     Creates a new WinTun adapter with specified pool and name
 *
 * PARAMETERS:
 *     pool - Pool name for the adapter
 *     name - Name of the adapter
 *     requestedGUID - Requested GUID for the adapter (can be nil)
 *
 * RETURNS:
 *     uintptr - Handle to the created adapter
 *     error - Error if adapter creation fails
 *****************************************************************************/
func (w *WinTun) CreateAdapter(pool, name string, requestedGUID *windows.GUID) (uintptr, error) {
	poolPtr, err := syscall.UTF16PtrFromString(pool)
	if err != nil {
		return 0, fmt.Errorf("failed to convert pool to UTF16: %w", err)
	}

	namePtr, err := syscall.UTF16PtrFromString(name)
	if err != nil {
		return 0, fmt.Errorf("failed to convert name to UTF16: %w", err)
	}

	var rebootRequired bool

	adapter, _, err := w.createAdapter.Call(
		uintptr(unsafe.Pointer(poolPtr)),
		uintptr(unsafe.Pointer(namePtr)),
		uintptr(unsafe.Pointer(requestedGUID)),
		uintptr(unsafe.Pointer(&rebootRequired)),
	)

	if adapter == 0 {
		return 0, fmt.Errorf("failed to create adapter: %w", err)
	}

	return adapter, nil
}

/*****************************************************************************
 * NAME: OpenAdapter
 *
 * DESCRIPTION:
 *     Opens an existing WinTun adapter by name
 *
 * PARAMETERS:
 *     name - Name of the adapter to open
 *
 * RETURNS:
 *     uintptr - Handle to the opened adapter
 *     error - Error if adapter opening fails
 *****************************************************************************/
func (w *WinTun) OpenAdapter(name string) (uintptr, error) {
	namePtr, err := syscall.UTF16PtrFromString(name)
	if err != nil {
		return 0, fmt.Errorf("failed to convert name to UTF16: %w", err)
	}

	adapter, _, err := w.openAdapter.Call(uintptr(unsafe.Pointer(namePtr)))
	if adapter == 0 {
		return 0, fmt.Errorf("failed to open adapter: %w", err)
	}

	return adapter, nil
}

/*****************************************************************************
 * NAME: CloseAdapter
 *
 * DESCRIPTION:
 *     Closes a WinTun adapter and releases its resources
 *
 * PARAMETERS:
 *     adapter - Handle to the adapter to close
 *****************************************************************************/
func (w *WinTun) CloseAdapter(adapter uintptr) {
	w.closeAdapter.Call(adapter)
}

/*****************************************************************************
 * NAME: GetAdapterLUID
 *
 * DESCRIPTION:
 *     Gets the LUID (Locally Unique Identifier) of a WinTun adapter
 *
 * PARAMETERS:
 *     adapter - Handle to the adapter
 *
 * RETURNS:
 *     uint64 - LUID of the adapter
 *****************************************************************************/
func (w *WinTun) GetAdapterLUID(adapter uintptr) uint64 {
	var luid uint64
	w.getAdapterLUID.Call(adapter, uintptr(unsafe.Pointer(&luid)))
	return luid
}

/*****************************************************************************
 * NAME: GetRunningDriverVersion
 *
 * DESCRIPTION:
 *     Gets the version of the running WinTun driver
 *
 * RETURNS:
 *     uint32 - Driver version (high 16 bits: major, low 16 bits: minor)
 *****************************************************************************/
func (w *WinTun) GetRunningDriverVersion() uint32 {
	version, _, _ := w.getRunningDriverVersion.Call()
	return uint32(version)
}

/*****************************************************************************
 * NAME: StartSession
 *
 * DESCRIPTION:
 *     Starts a session on a WinTun adapter for packet I/O operations
 *
 * PARAMETERS:
 *     adapter - Handle to the adapter
 *     capacity - Ring buffer capacity in bytes
 *
 * RETURNS:
 *     uintptr - Handle to the started session
 *     error - Error if session start fails
 *****************************************************************************/
func (w *WinTun) StartSession(adapter uintptr, capacity uint32) (uintptr, error) {
	session, _, err := w.startSession.Call(adapter, uintptr(capacity))
	if session == 0 {
		return 0, fmt.Errorf("failed to start session: %w", err)
	}
	return session, nil
}

/*****************************************************************************
 * NAME: EndSession
 *
 * DESCRIPTION:
 *     Ends a WinTun session and releases its resources
 *
 * PARAMETERS:
 *     session - Handle to the session to end
 *****************************************************************************/
func (w *WinTun) EndSession(session uintptr) {
	w.endSession.Call(session)
}

/*****************************************************************************
 * NAME: GetReadWaitEvent
 *
 * DESCRIPTION:
 *     Gets the read wait event handle for a WinTun session
 *
 * PARAMETERS:
 *     session - Handle to the session
 *
 * RETURNS:
 *     windows.Handle - Event handle for waiting on read operations
 *     error - Error if getting event handle fails
 *****************************************************************************/
func (w *WinTun) GetReadWaitEvent(session uintptr) (windows.Handle, error) {
	event, _, err := w.getReadWaitEvent.Call(session)
	if event == 0 {
		return 0, fmt.Errorf("failed to get read wait event: %w", err)
	}
	return windows.Handle(event), nil
}

/*****************************************************************************
 * NAME: ReceivePacket
 *
 * DESCRIPTION:
 *     Receives a packet from a WinTun session and copies it to a Go slice.
 *     Automatically handles memory management by releasing the original packet.
 *
 * PARAMETERS:
 *     session - Handle to the session
 *
 * RETURNS:
 *     []byte - Packet data as Go slice (independent copy)
 *     error - Error if packet reception fails
 *****************************************************************************/
func (w *WinTun) ReceivePacket(session uintptr) ([]byte, error) {
	var packetSize uint32
	packet, _, err := w.receivePacket.Call(session, uintptr(unsafe.Pointer(&packetSize)))
	if packet == 0 {
		return nil, fmt.Errorf("failed to receive packet: %w", err)
	}

	// Copy the packet to a Go slice
	data := make([]byte, packetSize)
	copy(data, unsafe.Slice((*byte)(unsafe.Pointer(packet)), packetSize))

	// Release the packet
	w.releaseReceivePacket.Call(session, packet)

	return data, nil
}

/*****************************************************************************
 * NAME: SendPacket
 *
 * DESCRIPTION:
 *     Sends a packet to a WinTun session. Automatically handles memory management
 *     by allocating send buffer and releasing it after sending.
 *
 * PARAMETERS:
 *     session - Handle to the session
 *     data - Packet data to send
 *
 * RETURNS:
 *     error - Error if packet sending fails
 *****************************************************************************/
func (w *WinTun) SendPacket(session uintptr, data []byte) error {
	packet, _, err := w.allocateSendPacket.Call(session, uintptr(len(data)))
	if packet == 0 {
		return fmt.Errorf("failed to allocate send packet: %w", err)
	}

	// Copy the data to the packet
	copy(unsafe.Slice((*byte)(unsafe.Pointer(packet)), len(data)), data)

	// Send the packet
	w.sendPacket.Call(session, packet)

	return nil
}

/*****************************************************************************
 * NAME: Start
 *
 * DESCRIPTION:
 *     Starts the TUN device by creating WinTun instance, adapter, and session
 *
 * RETURNS:
 *     error - Error if device start fails
 *****************************************************************************/
func (d *windowsDevice) Start() error {
	//d.mutex.Lock()
	//defer d.mutex.Unlock()

	if d.isUp {
		d.log.Warn("TUN device already started")
		return nil
	}

	// Create WinTun instance
	d.log.Debug("About to create WinTun instance")

	wt, err := NewWinTun()
	d.log.Debug("WinTun instance creation completed", logger.Bool("success", err == nil))

	if err != nil {
		d.log.Error("Failed to create WinTun instance", logger.ErrorField(err))
		return fmt.Errorf("failed to create WinTun instance: %w", err)
	}

	// Log driver version if available
	version := wt.GetRunningDriverVersion()
	d.log.Info("WinTun driver version",
		logger.Int("major", int((version>>16)&0xff)),
		logger.Int("minor", int(version&0xff)))

	// Create adapter
	d.log.Debug("Creating WinTun adapter",
		logger.String("pool", "Panabit"),
		logger.String("name", d.name))

	// First try to open an existing adapter with the same name
	handle, err := wt.OpenAdapter(d.name)
	if err != nil {
		d.log.Debug("Could not open existing adapter, will create a new one",
			logger.String("name", d.name),
			logger.ErrorField(err))

		// If opening failed, try to create a new adapter
		d.log.Debug("About to call WinTun CreateAdapter",
			logger.String("pool", "Panabit"),
			logger.String("name", d.name))

		handle, err = wt.CreateAdapter("Panabit", d.name, nil)
		d.log.Debug("CreateAdapter call completed",
			logger.Bool("success", err == nil),
			logger.Uint64("handle", uint64(handle)))

		if err != nil {
			// Get Windows error code
			// Use type assertion to convert err to windows.Errno
			var winErrCode uint32
			if errno, ok := err.(windows.Errno); ok {
				winErrCode = uint32(errno)
			} else {
				// If conversion fails, use default error code
				winErrCode = 0
			}

			d.log.Error("Failed to create adapter",
				logger.ErrorField(err),
				logger.Uint32("error_code", winErrCode))

			// Check for specific error codes
			switch windows.Errno(winErrCode) {
			case windows.ERROR_ACCESS_DENIED:
				return fmt.Errorf("failed to create adapter: access denied (administrator privileges required): %w", err)
			case windows.ERROR_FILE_NOT_FOUND:
				return fmt.Errorf("failed to create adapter: wintun driver not installed or found: %w", err)
			default:
				return fmt.Errorf("failed to create adapter: %w (error code: %d)", err, winErrCode)
			}
		}
	} else {
		d.log.Debug("Successfully opened existing adapter", logger.String("name", d.name))
	}

	// Start session
	session, err := wt.StartSession(handle, 0x800000) // 20MB ring buffer
	if err != nil {
		wt.CloseAdapter(handle)
		d.log.Error("Failed to start session", logger.ErrorField(err))
		return fmt.Errorf("failed to start session: %w", err)
	}

	// Get read wait event
	readWait, err := wt.GetReadWaitEvent(session)
	if err != nil {
		wt.EndSession(session)
		wt.CloseAdapter(handle)
		d.log.Error("Failed to get read wait event", logger.ErrorField(err))
		return fmt.Errorf("failed to get read wait event: %w", err)
	}

	// Get adapter LUID
	luid := winipcfg.LUID(wt.GetAdapterLUID(handle))
	d.log.Debug("Adapter LUID", logger.Uint64("luid", uint64(luid)))

	// Create NetworkInterface abstraction
	netInterface, err := network.NewNetworkInterface(uint64(luid), d.name, d.log)
	if err != nil {
		wt.EndSession(session)
		wt.CloseAdapter(handle)
		d.log.Error("Failed to create network interface", logger.ErrorField(err))
		return fmt.Errorf("failed to create network interface: %w", err)
	}

	// Store the device state
	d.handle = handle
	d.wintun = wt
	d.session = session
	d.readWait = readWait
	d.luid = luid
	d.netInterface = netInterface
	d.isUp = true

	d.log.Info("TUN device started successfully")
	return nil
}

/*****************************************************************************
 * NAME: Stop
 *
 * DESCRIPTION:
 *     Stops the TUN device by ending session and closing adapter
 *
 * RETURNS:
 *     error - Error if device stop fails
 *****************************************************************************/
func (d *windowsDevice) Stop() error {
	//d.mutex.Lock()
	//defer d.mutex.Unlock()

	if !d.isUp {
		d.log.Warn("TUN device already stopped")
		return nil
	}

	d.log.Info("Stopping TUN device", logger.String("name", d.name))

	// Clean up network configuration using bulk operations
	if d.netInterface != nil {
		d.log.Debug("Cleaning up network configuration")

		// Use bulk flush operations for efficient cleanup
		if err := d.netInterface.FlushIPAddresses(); err != nil {
			d.log.Warn("Failed to flush IP addresses during cleanup", logger.ErrorField(err))
		}

		if err := d.netInterface.FlushRoutes(); err != nil {
			d.log.Warn("Failed to flush routes during cleanup", logger.ErrorField(err))
		}

		if err := d.netInterface.FlushDNS(); err != nil {
			d.log.Warn("Failed to flush DNS during cleanup", logger.ErrorField(err))
		}

		d.netInterface = nil
	}

	// End session
	if d.session != 0 {
		d.wintun.EndSession(d.session)
		d.session = 0
	}

	// Close adapter
	if d.handle != 0 {
		d.wintun.CloseAdapter(d.handle)
		d.handle = 0
	}

	d.isUp = false
	d.log.Info("TUN device stopped successfully")
	return nil
}

/*****************************************************************************
 * NAME: Close
 *
 * DESCRIPTION:
 *     Closes the TUN device (alias for Stop method)
 *
 * RETURNS:
 *     error - Error if device close fails
 *****************************************************************************/
func (d *windowsDevice) Close() error {
	return d.Stop()
}

/*****************************************************************************
 * NAME: Read
 *
 * DESCRIPTION:
 *     Reads a packet from the TUN device. This method will wait until a packet
 *     is successfully read before returning. Handles WinTun memory management
 *     automatically.
 *
 * PARAMETERS:
 *     packet - Buffer to store the received packet data
 *
 * RETURNS:
 *     int - Number of bytes read
 *     error - Error if device is not up or read fails
 *****************************************************************************/
func (d *windowsDevice) Read(packet []byte) (int, error) {
	if !d.isUp || d.session == 0 {
		return 0, fmt.Errorf("device is not up")
	}

	for {
		data, err := d.wintun.ReceivePacket(d.session)
		if err != nil {
			// Check if error message contains "No more data is available"
			// This is the error returned by WinTun driver when no packets are available to read
			if strings.Contains(err.Error(), "No more data is available") {
				// This is normal, just wait a bit and retry
				time.Sleep(1 * time.Millisecond)
				continue
			}

			// Other errors, log but continue trying
			// Don't return error here, continue trying to read to make Read method more robust
			d.log.Debug("Non-critical error receiving packet, will retry",
				logger.ErrorField(err))
			time.Sleep(5 * time.Millisecond)
			continue
		}

		if len(data) == 0 {
			// Defensive check, should not normally happen
			// If empty packet received, continue trying
			d.log.Debug("Received empty packet, will retry")
			time.Sleep(1 * time.Millisecond)
			continue
		}

		// Successfully read packet
		n := copy(packet, data)
		return n, nil
	}
}

/*****************************************************************************
 * NAME: Write
 *
 * DESCRIPTION:
 *     Writes a packet to the TUN device. Handles WinTun memory management
 *     automatically by allocating send buffer and releasing after send.
 *
 * PARAMETERS:
 *     packet - Packet data to send
 *
 * RETURNS:
 *     int - Number of bytes written
 *     error - Error if device is not up or write fails
 *****************************************************************************/
func (d *windowsDevice) Write(packet []byte) (int, error) {
	//d.mutex.RLock()
	//defer d.mutex.RUnlock()

	if !d.isUp || d.session == 0 {
		return 0, fmt.Errorf("device is not up")
	}

	// Send packet
	if err := d.wintun.SendPacket(d.session, packet); err != nil {
		return 0, fmt.Errorf("failed to send packet: %w", err)
	}

	return len(packet), nil
}

/*****************************************************************************
 * NAME: Name
 *
 * DESCRIPTION:
 *     Returns the name of the TUN device
 *
 * RETURNS:
 *     string - Device name
 *****************************************************************************/
func (d *windowsDevice) Name() string {
	//d.mutex.RLock()
	//defer d.mutex.RUnlock()
	return d.name
}

/*****************************************************************************
 * NAME: MTU
 *
 * DESCRIPTION:
 *     Returns the MTU (Maximum Transmission Unit) of the TUN device
 *
 * RETURNS:
 *     int - MTU value in bytes
 *****************************************************************************/
func (d *windowsDevice) MTU() int {
	//d.mutex.RLock()
	//defer d.mutex.RUnlock()
	return d.mtu
}

/*****************************************************************************
 * NAME: SetMTU
 *
 * DESCRIPTION:
 *     Sets the MTU (Maximum Transmission Unit) of the TUN device
 *
 * PARAMETERS:
 *     mtu - MTU value in bytes
 *
 * RETURNS:
 *     error - Error if setting MTU fails
 *****************************************************************************/
func (d *windowsDevice) SetMTU(mtu int) error {
	//d.mutex.Lock()
	//defer d.mutex.Unlock()

	d.log.Debug("Setting MTU", logger.Int("mtu", mtu))
	d.mtu = mtu
	return nil
}

/*****************************************************************************
 * NAME: Addresses
 *
 * DESCRIPTION:
 *     Returns the IP addresses assigned to the TUN device
 *
 * RETURNS:
 *     []net.IPNet - List of IP addresses with network masks
 *****************************************************************************/
func (d *windowsDevice) Addresses() []net.IPNet {
	//d.mutex.RLock()
	//defer d.mutex.RUnlock()
	return d.addresses
}

/*****************************************************************************
 * NAME: SetAddresses
 *
 * DESCRIPTION:
 *     Sets the IP addresses for the TUN device. Removes existing addresses
 *     and adds new ones using Windows network interface APIs.
 *
 * PARAMETERS:
 *     addresses - List of IP addresses with network masks to assign
 *
 * RETURNS:
 *     error - Error if setting addresses fails
 *****************************************************************************/
func (d *windowsDevice) SetAddresses(addresses []net.IPNet) error {
	//d.mutex.Lock()
	//defer d.mutex.Unlock()

	d.log.Debug("Setting IP addresses", logger.Int("count", len(addresses)))

	// Use bulk flush operation for efficient cleanup
	if d.netInterface != nil && len(d.addresses) > 0 {
		d.log.Debug("Flushing existing IP addresses using bulk operation")
		err := d.netInterface.FlushIPAddresses()
		if err != nil {
			d.log.Warn("Failed to flush IP addresses", logger.ErrorField(err))
			// Continue with individual removal as fallback
			for _, addr := range d.addresses {
				prefix, _ := addr.Mask.Size()
				ipAddr, parseErr := netip.ParseAddr(addr.IP.String())
				if parseErr != nil {
					continue
				}
				netPrefix := netip.PrefixFrom(ipAddr, prefix)
				d.netInterface.DeleteIPAddress(netPrefix)
			}
		}
	}

	// Add new addresses using NetworkInterface
	if d.netInterface != nil {
		for _, addr := range addresses {
			prefix, _ := addr.Mask.Size()
			d.log.Debug("Adding IP address",
				logger.String("ip", addr.IP.String()),
				logger.Int("prefix", prefix))

			// Convert to netip.Prefix
			ipAddr, err := netip.ParseAddr(addr.IP.String())
			if err != nil {
				d.log.Error("Failed to parse IP address",
					logger.String("ip", addr.IP.String()),
					logger.ErrorField(err))
				return fmt.Errorf("failed to parse IP address: %w", err)
			}

			netPrefix := netip.PrefixFrom(ipAddr, prefix)
			err = d.netInterface.AddIPAddress(netPrefix)
			if err != nil {
				// Check if the error is "The object already exists"
				if strings.Contains(err.Error(), "already exists") {
					d.log.Info("IP address already exists, continuing",
						logger.String("ip", addr.IP.String()),
						logger.Int("prefix", prefix))
				} else {
					d.log.Error("Failed to add IP address",
						logger.String("ip", addr.IP.String()),
						logger.Int("prefix", prefix),
						logger.ErrorField(err))
					return fmt.Errorf("failed to add IP address: %w", err)
				}
			}
		}
	}

	d.addresses = addresses
	return nil
}

/*****************************************************************************
 * NAME: Configure
 *
 * DESCRIPTION:
 *     Configures the TUN device with specified settings including name,
 *     MTU, and IP addresses
 *
 * PARAMETERS:
 *     config - Device configuration settings
 *
 * RETURNS:
 *     error - Error if configuration fails
 *****************************************************************************/
func (d *windowsDevice) Configure(config DeviceConfig) error {
	//d.mutex.Lock()
	//defer d.mutex.Unlock()

	d.log.Info("Configuring TUN device",
		logger.String("name", d.name),
		logger.Int("mtu", config.MTU))

	// Set name
	if config.Name != "" && config.Name != d.name {
		d.name = config.Name
	}

	// Set MTU
	if config.MTU > 0 && config.MTU != d.mtu {
		d.mtu = config.MTU
	}

	// Set addresses
	if len(config.Addresses) > 0 {
		err := d.SetAddresses(config.Addresses)
		if err != nil {
			return fmt.Errorf("failed to set IP addresses: %w", err)
		}

		d.addresses = config.Addresses
	}

	return nil
}

/*****************************************************************************
 * NAME: SetDefaultGateway
 *
 * DESCRIPTION:
 *     Sets the default gateway for the TUN device by adding a default route
 *
 * PARAMETERS:
 *     gateway - Gateway IP address
 *
 * RETURNS:
 *     error - Error if setting gateway fails
 *****************************************************************************/
func (d *windowsDevice) SetDefaultGateway(gateway net.IP) error {
	//d.mutex.RLock()
	//defer d.mutex.RUnlock()

	if !d.isUp {
		return fmt.Errorf("device is not up")
	}

	d.log.Info("Setting default gateway for TUN device",
		logger.String("gateway", gateway.String()))

	// Add default route (0.0.0.0/0) pointing to gateway
	defaultRoute := net.IPNet{
		IP:   net.IPv4zero,
		Mask: net.CIDRMask(0, 32),
	}

	// Add default route with lowest metric (1) to ensure highest priority
	return d.AddRoute(defaultRoute, gateway, 1)
}

/*****************************************************************************
 * NAME: AddRoute
 *
 * DESCRIPTION:
 *     Adds a route to the routing table for the TUN device
 *
 * PARAMETERS:
 *     destination - Destination network
 *     gateway - Gateway IP address
 *     metric - Route metric value (lower values have higher priority)
 *
 * RETURNS:
 *     error - Error if adding route fails
 *****************************************************************************/
func (d *windowsDevice) AddRoute(destination net.IPNet, gateway net.IP, metric int) error {
	//d.mutex.RLock()
	//defer d.mutex.RUnlock()

	if !d.isUp {
		return fmt.Errorf("device is not up")
	}

	d.log.Debug("Adding route",
		logger.String("destination", destination.String()),
		logger.String("gateway", gateway.String()),
		logger.Int("metric", metric))

	luid := d.luid

	// Convert destination network to netip.Prefix
	ones, _ := destination.Mask.Size()
	destIP, err := netip.ParseAddr(destination.IP.String())
	if err != nil {
		d.log.Error("Failed to parse destination IP",
			logger.String("ip", destination.IP.String()),
			logger.ErrorField(err))
		return fmt.Errorf("failed to parse destination IP: %w", err)
	}
	destPrefix := netip.PrefixFrom(destIP, ones)

	// Convert gateway to netip.Addr
	gwAddr, err := netip.ParseAddr(gateway.String())
	if err != nil {
		d.log.Error("Failed to parse gateway IP",
			logger.String("ip", gateway.String()),
			logger.ErrorField(err))
		return fmt.Errorf("failed to parse gateway IP: %w", err)
	}

	// Log detailed route information
	d.log.Debug("Adding route with detailed parameters",
		logger.String("destination_ip", destination.IP.String()),
		logger.String("destination_mask", destination.Mask.String()),
		logger.String("gateway_ip", gateway.String()),
		logger.Int("metric", metric),
		logger.Uint64("interface_luid", uint64(luid)))

	err = luid.AddRoute(destPrefix, gwAddr, uint32(metric))
	if err != nil {
		// Check if the error is "The object already exists"
		if strings.Contains(err.Error(), "already exists") ||
			strings.Contains(err.Error(), "The object already exists") {
			d.log.Info("Route already exists, continuing",
				logger.String("destination", destination.String()),
				logger.String("gateway", gateway.String()),
				logger.Int("metric", metric))
			return nil
		}

		d.log.Error("Failed to add route",
			logger.String("destination", destination.String()),
			logger.String("gateway", gateway.String()),
			logger.Int("metric", metric),
			logger.ErrorField(err))
		return fmt.Errorf("failed to add route: %w", err)
	}

	return nil
}

/*****************************************************************************
 * NAME: DeleteRoute
 *
 * DESCRIPTION:
 *     Deletes a route from the routing table for the TUN device
 *
 * PARAMETERS:
 *     destination - Destination network to remove
 *     gateway - Gateway IP address (can be nil for any gateway)
 *
 * RETURNS:
 *     error - Error if deleting route fails
 *****************************************************************************/
func (d *windowsDevice) DeleteRoute(destination net.IPNet, gateway net.IP) error {
	//d.mutex.RLock()
	//defer d.mutex.RUnlock()

	if !d.isUp {
		return fmt.Errorf("device is not up")
	}

	gatewayStr := "nil"
	if gateway != nil {
		gatewayStr = gateway.String()
	}
	d.log.Debug("Deleting route",
		logger.String("destination", destination.String()),
		logger.String("gateway", gatewayStr))

	if d.netInterface == nil {
		return fmt.Errorf("network interface not available")
	}

	// Convert destination network to netip.Prefix
	ones, _ := destination.Mask.Size()
	destIP, err := netip.ParseAddr(destination.IP.String())
	if err != nil {
		d.log.Error("Failed to parse destination IP",
			logger.String("ip", destination.IP.String()),
			logger.ErrorField(err))
		return fmt.Errorf("failed to parse destination IP: %w", err)
	}
	destPrefix := netip.PrefixFrom(destIP, ones)

	// Parse gateway address
	var gatewayAddr netip.Addr
	if gateway != nil {
		gatewayAddr, err = netip.ParseAddr(gateway.String())
		if err != nil {
			d.log.Error("Failed to parse gateway IP",
				logger.String("gateway", gateway.String()),
				logger.ErrorField(err))
			// Continue with empty gateway
			d.log.Warn("Using empty gateway for route deletion")
		}
	}

	// Use NetworkInterface to delete route
	err = d.netInterface.DeleteRoute(destPrefix, gatewayAddr)
	if err != nil {
		// If deletion with specified gateway fails, try with empty gateway
		if gateway != nil {
			d.log.Warn("Failed to delete route with specified gateway, trying with empty gateway",
				logger.String("destination", destination.String()),
				logger.String("gateway", gateway.String()),
				logger.ErrorField(err))

			var emptyGateway netip.Addr
			err = d.netInterface.DeleteRoute(destPrefix, emptyGateway)
			if err != nil {
				d.log.Error("Failed to delete route with empty gateway",
					logger.String("destination", destination.String()),
					logger.ErrorField(err))
				return fmt.Errorf("failed to delete route: %w", err)
			}
		} else {
			d.log.Error("Failed to delete route",
				logger.String("destination", destination.String()),
				logger.ErrorField(err))
			return fmt.Errorf("failed to delete route: %w", err)
		}
	}

	d.log.Debug("Route deleted",
		logger.String("destination", destination.String()))
	return nil
}

/*****************************************************************************
 * NAME: SetDNS
 *
 * DESCRIPTION:
 *     Sets DNS servers for the TUN device interface
 *
 * PARAMETERS:
 *     servers - List of DNS server IP addresses
 *
 * RETURNS:
 *     error - Error if setting DNS servers fails
 *****************************************************************************/
func (d *windowsDevice) SetDNS(servers []net.IP) error {
	//d.mutex.RLock()
	//defer d.mutex.RUnlock()

	if !d.isUp {
		return fmt.Errorf("device is not up")
	}

	d.log.Debug("Setting DNS servers", logger.Int("count", len(servers)))

	// Convert net.IP to netip.Addr
	var dnsAddrs []netip.Addr
	for _, server := range servers {
		addr, err := netip.ParseAddr(server.String())
		if err != nil {
			d.log.Warn("Failed to parse DNS server address",
				logger.String("server", server.String()),
				logger.ErrorField(err))
			continue
		}
		dnsAddrs = append(dnsAddrs, addr)
	}

	if len(dnsAddrs) == 0 {
		d.log.Warn("No valid DNS servers to set")
		return nil
	}

	// Set DNS servers using NetworkInterface
	if d.netInterface == nil {
		return fmt.Errorf("network interface not available")
	}

	err := d.netInterface.SetDNS(dnsAddrs)
	if err != nil {
		d.log.Error("Failed to set DNS servers", logger.ErrorField(err))
		return fmt.Errorf("failed to set DNS servers: %w", err)
	}

	return nil
}

/*****************************************************************************
 * NAME: IsUp
 *
 * DESCRIPTION:
 *     Returns whether the TUN device is currently up and running
 *
 * RETURNS:
 *     bool - True if device is up, false otherwise
 *****************************************************************************/
func (d *windowsDevice) IsUp() bool {
	//d.mutex.RLock()
	//defer d.mutex.RUnlock()
	return d.isUp
}

/*****************************************************************************
 * NAME: FlushConfiguration
 *
 * DESCRIPTION:
 *     Flushes all device configuration (IP addresses, routes, DNS) using
 *     efficient bulk NetworkInterface operations
 *
 * RETURNS:
 *     error - Error if flush operations fail
 *****************************************************************************/
func (d *windowsDevice) FlushConfiguration() error {
	//d.mutex.Lock()
	//defer d.mutex.Unlock()

	if !d.isUp {
		return fmt.Errorf("device is not up")
	}

	// Use NetworkInterface for efficient bulk operations
	if d.netInterface != nil {
		// Flush IP addresses
		if err := d.netInterface.FlushIPAddresses(); err != nil {
			d.log.Error("Failed to flush IP addresses", logger.ErrorField(err))
			return fmt.Errorf("failed to flush IP addresses: %w", err)
		}

		// Flush routes
		if err := d.netInterface.FlushRoutes(); err != nil {
			d.log.Error("Failed to flush routes", logger.ErrorField(err))
			return fmt.Errorf("failed to flush routes: %w", err)
		}

		// Flush DNS
		if err := d.netInterface.FlushDNS(); err != nil {
			d.log.Error("Failed to flush DNS", logger.ErrorField(err))
			return fmt.Errorf("failed to flush DNS: %w", err)
		}

		d.log.Info("Successfully flushed TUN device configuration using bulk operations")

		// Clear the stored addresses since they've been flushed
		d.addresses = []net.IPNet{}

		return nil
	}

	return fmt.Errorf("NetworkInterface not available for bulk flush operations")
}

/*****************************************************************************
 * NAME: findWintunDLL
 *
 * DESCRIPTION:
 *     Finds the wintun.dll file in various standard locations
 *
 * RETURNS:
 *     string - Path to the wintun.dll file
 *     error - Error if DLL file is not found
 *****************************************************************************/
func findWintunDLL() (string, error) {
	// Check environment variable
	if path := os.Getenv("WINTUN_DLL_PATH"); path != "" {
		if _, err := os.Stat(path); err == nil {
			return path, nil
		}
	}

	// Check executable directory
	exePath, err := os.Executable()
	if err == nil {
		exeDir := filepath.Dir(exePath)
		path := filepath.Join(exeDir, WintunDLLName)
		if _, err := os.Stat(path); err == nil {
			return path, nil
		}
	}

	// Check current directory
	currentDir, err := os.Getwd()
	if err == nil {
		path := filepath.Join(currentDir, WintunDLLName)
		if _, err := os.Stat(path); err == nil {
			return path, nil
		}
	}

	// Check installation directory
	installDir := "C:\\Program Files (x86)\\Panabit"
	path := filepath.Join(installDir, WintunDLLName)
	if _, err := os.Stat(path); err == nil {
		return path, nil
	}

	// Check system directory
	systemDir := os.Getenv("SystemRoot") + "\\System32"
	path = filepath.Join(systemDir, WintunDLLName)
	if _, err := os.Stat(path); err == nil {
		return path, nil
	}

	return "", fmt.Errorf("wintun.dll not found")
}
