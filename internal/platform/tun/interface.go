/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      interface.go
 *
 * DESCRIPTION :    TUN device interface definition
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package tun

import (
	"io"
	"net"
)

/*****************************************************************************
 * NAME: Device
 *
 * DESCRIPTION:
 *     Interface for TUN device operations
 *****************************************************************************/
type Device interface {
	// Provides io.ReadWriteCloser interface for packet I/O
	io.ReadWriteCloser

	// Configure configures the TUN device with the given configuration
	Configure(config DeviceConfig) error

	// Start starts the TUN device
	Start() error

	// Stop stops the TUN device
	Stop() error

	// Name returns the name of the TUN device
	Name() string

	// MTU returns the current MTU of the device
	MTU() int

	// SetMTU sets the MTU of the device
	SetMTU(mtu int) error

	// Addresses returns the IP addresses assigned to the device
	Addresses() []net.IPNet

	// SetAddresses sets the IP addresses for the device
	SetAddresses(addresses []net.IPNet) error

	// AddRoute adds a route to the routing table
	AddRoute(destination net.IPNet, gateway net.IP, metric int) error

	// DeleteRoute deletes a route from the routing table
	DeleteRoute(destination net.IPNet, gateway net.IP) error

	// SetDNS sets the DNS servers
	SetDNS(servers []net.IP) error

	// SetDefaultGateway sets the default gateway for the device
	SetDefaultGateway(gateway net.IP) error

	// IsUp returns whether the device is up
	IsUp() bool

	// FlushConfiguration flushes all device configuration (IP, routes, DNS) using bulk operations when available
	FlushConfiguration() error
}

/*****************************************************************************
 * NAME: DeviceConfig
 *
 * DESCRIPTION:
 *     Configuration for TUN device
 *
 * FIELDS:
 *     Name - Requested name for the TUN device (may be ignored by some platforms)
 *     MTU - Requested MTU for the device
 *     Addresses - IP addresses to assign to the device
 *****************************************************************************/
type DeviceConfig struct {
	Name      string
	MTU       int
	Addresses []net.IPNet
}
