//go:build windows

/*****************************************************************************
 * FILE: internal/platform/service/windows.go
 *
 * DESCRIPTION:
 *     Windows 服务控制管理器（SCM）集成实现
 *     提供 Windows 服务的安装、卸载、启动、停止等功能
 *
 * AUTHOR: wei
 * HISTORY: 31/07/2025 create
 *****************************************************************************/

package service

import (
	"context"
	"fmt"
	"time"

	"golang.org/x/sys/windows/svc"
	"golang.org/x/sys/windows/svc/debug"
	"golang.org/x/sys/windows/svc/eventlog"
	"golang.org/x/sys/windows/svc/mgr"

	"mobile/internal/common/logger"
)

const (
	// 服务配置常量
	serviceName        = "PanabitService"
	serviceDisplayName = "Panabit Client WAN Service"
	serviceDescription = "Panabit Client WAN 网络服务"
)

/*****************************************************************************
 * NAME: WindowsService
 *
 * DESCRIPTION:
 *     Windows 服务实现，实现 svc.Handler 接口
 *****************************************************************************/
type WindowsService struct {
	log        logger.Logger
	mainFunc   func(context.Context) error
	stopCh     chan struct{}
	ctx        context.Context
	cancelFunc context.CancelFunc
}

/*****************************************************************************
 * NAME: NewWindowsService
 *
 * DESCRIPTION:
 *     创建新的 Windows 服务实例
 *
 * PARAMETERS:
 *     log      - 日志记录器
 *     mainFunc - 主程序逻辑函数（接收上下文）
 *
 * RETURNS:
 *     *WindowsService - Windows 服务实例
 *****************************************************************************/
func NewWindowsService(log logger.Logger, mainFunc func(context.Context) error) *WindowsService {
	ctx, cancel := context.WithCancel(context.Background())
	return &WindowsService{
		log:        log,
		mainFunc:   mainFunc,
		stopCh:     make(chan struct{}),
		ctx:        ctx,
		cancelFunc: cancel,
	}
}

/*****************************************************************************
 * NAME: Execute
 *
 * DESCRIPTION:
 *     实现 svc.Handler 接口，处理 SCM 命令
 *
 * PARAMETERS:
 *     args    - 服务参数
 *     r       - 服务请求通道
 *     changes - 服务状态变化通道
 *
 * RETURNS:
 *     bool  - 是否需要重启
 *     uint32 - 退出代码
 *****************************************************************************/
func (ws *WindowsService) Execute(args []string, r <-chan svc.ChangeRequest, changes chan<- svc.Status) (bool, uint32) {
	const cmdsAccepted = svc.AcceptStop | svc.AcceptShutdown | svc.AcceptPauseAndContinue

	// 报告服务启动中
	changes <- svc.Status{State: svc.StartPending}

	// 启动主程序逻辑
	go func() {
		defer func() {
			if r := recover(); r != nil {
				ws.log.Error("Service main function panic",
					logger.Any("panic", r))
			}
			close(ws.stopCh)
		}()

		if err := ws.mainFunc(ws.ctx); err != nil {
			ws.log.Error("Service main function error",
				logger.ErrorField(err))
		}
	}()

	// 等待主程序启动完成（简单延迟，实际应该有更好的同步机制）
	time.Sleep(2 * time.Second)

	// 报告服务运行中
	changes <- svc.Status{State: svc.Running, Accepts: cmdsAccepted}
	ws.log.Info("Windows service started successfully")

	// 处理 SCM 命令
	for {
		select {
		case c := <-r:
			switch c.Cmd {
			case svc.Interrogate:
				changes <- c.CurrentStatus
				// 测试延迟
				time.Sleep(100 * time.Millisecond)
				changes <- c.CurrentStatus

			case svc.Stop, svc.Shutdown:
				ws.log.Info("Received stop/shutdown signal from SCM")
				// 报告停止中
				changes <- svc.Status{State: svc.StopPending}

				// 取消上下文，通知主程序停止
				ws.cancelFunc()

				// 等待主程序停止
				select {
				case <-ws.stopCh:
					ws.log.Info("Main function stopped gracefully")
				case <-time.After(30 * time.Second):
					ws.log.Warn("Main function stop timeout")
				}

				return false, 0

			case svc.Pause:
				ws.log.Info("Received pause signal from SCM")
				// 报告暂停中
				changes <- svc.Status{State: svc.PausePending, Accepts: cmdsAccepted}
				// TODO: 实现暂停逻辑（如果需要）
				// 报告已暂停
				changes <- svc.Status{State: svc.Paused, Accepts: cmdsAccepted}

			case svc.Continue:
				ws.log.Info("Received continue signal from SCM")
				// 报告继续中
				changes <- svc.Status{State: svc.ContinuePending, Accepts: cmdsAccepted}
				// TODO: 实现继续逻辑（如果需要）
				// 报告运行中
				changes <- svc.Status{State: svc.Running, Accepts: cmdsAccepted}

			default:
				ws.log.Warn("Unexpected service command",
					logger.Uint32("command", uint32(c.Cmd)))
			}

		case <-ws.stopCh:
			ws.log.Info("Main function stopped, stopping service")
			return false, 0
		}
	}
}

/*****************************************************************************
 * NAME: GetContext
 *
 * DESCRIPTION:
 *     获取服务上下文，用于主程序监听停止信号
 *
 * RETURNS:
 *     context.Context - 服务上下文
 *****************************************************************************/
func (ws *WindowsService) GetContext() context.Context {
	return ws.ctx
}

/*****************************************************************************
 * NAME: IsWindowsService
 *
 * DESCRIPTION:
 *     检查当前是否在 Windows 服务模式下运行
 *
 * RETURNS:
 *     bool - 是否为服务模式
 *****************************************************************************/
func IsWindowsService() bool {
	isService, err := svc.IsWindowsService()
	if err != nil {
		return false
	}
	return isService
}

/*****************************************************************************
 * NAME: RunService
 *
 * DESCRIPTION:
 *     运行 Windows 服务
 *
 * PARAMETERS:
 *     log      - 日志记录器
 *     mainFunc - 主程序逻辑函数（接收上下文）
 *
 * RETURNS:
 *     error - 运行错误
 *****************************************************************************/
func RunService(log logger.Logger, mainFunc func(context.Context) error) error {
	// 设置事件日志
	elog, err := eventlog.Open(serviceName)
	if err != nil {
		log.Warn("Failed to open event log, using debug log",
			logger.ErrorField(err))
		return debug.Run(serviceName, NewWindowsService(log, mainFunc))
	}
	defer elog.Close()

	elog.Info(1, fmt.Sprintf("%s service starting", serviceDisplayName))
	defer elog.Info(1, fmt.Sprintf("%s service stopped", serviceDisplayName))

	return svc.Run(serviceName, NewWindowsService(log, mainFunc))
}

/*****************************************************************************
 * NAME: InstallService
 *
 * DESCRIPTION:
 *     安装 Windows 服务
 *
 * PARAMETERS:
 *     exePath    - 可执行文件路径
 *     configPath - 配置文件路径
 *
 * RETURNS:
 *     error - 安装错误
 *****************************************************************************/
func InstallService(exePath, configPath string) error {
	m, err := mgr.Connect()
	if err != nil {
		return fmt.Errorf("failed to connect to service manager: %v", err)
	}
	defer m.Disconnect()

	// 检查服务是否已存在
	s, err := m.OpenService(serviceName)
	if err == nil {
		s.Close()
		return fmt.Errorf("service %s already exists", serviceName)
	}

	// 构建服务启动参数
	args := []string{exePath}
	if configPath != "" {
		args = append(args, "--config", configPath)
	}

	// 创建服务
	s, err = m.CreateService(serviceName, exePath, mgr.Config{
		DisplayName:      serviceDisplayName,
		Description:      serviceDescription,
		StartType:        mgr.StartAutomatic,
		ServiceStartName: "LocalSystem",
		Dependencies:     []string{},
		BinaryPathName:   fmt.Sprintf(`"%s" --config "%s"`, exePath, configPath),
	})
	if err != nil {
		return fmt.Errorf("failed to create service: %v", err)
	}
	defer s.Close()

	// 设置服务恢复选项
	err = s.SetRecoveryActions([]mgr.RecoveryAction{
		{Type: mgr.ServiceRestart, Delay: 5 * time.Second},
		{Type: mgr.ServiceRestart, Delay: 10 * time.Second},
		{Type: mgr.NoAction},
	}, uint32((24 * time.Hour).Seconds()))
	if err != nil {
		// 恢复选项设置失败不是致命错误
		fmt.Printf("Warning: failed to set recovery actions: %v\n", err)
	}

	// 创建事件日志源
	err = eventlog.InstallAsEventCreate(serviceName, eventlog.Error|eventlog.Warning|eventlog.Info)
	if err != nil {
		// 事件日志创建失败不是致命错误
		fmt.Printf("Warning: failed to install event log: %v\n", err)
	}

	fmt.Printf("Service %s installed successfully\n", serviceName)
	return nil
}

/*****************************************************************************
 * NAME: RemoveService
 *
 * DESCRIPTION:
 *     卸载 Windows 服务
 *
 * RETURNS:
 *     error - 卸载错误
 *****************************************************************************/
func RemoveService() error {
	m, err := mgr.Connect()
	if err != nil {
		return fmt.Errorf("failed to connect to service manager: %v", err)
	}
	defer m.Disconnect()

	s, err := m.OpenService(serviceName)
	if err != nil {
		return fmt.Errorf("service %s is not installed", serviceName)
	}
	defer s.Close()

	// 停止服务（如果正在运行）
	status, err := s.Query()
	if err == nil && status.State == svc.Running {
		_, err = s.Control(svc.Stop)
		if err != nil {
			return fmt.Errorf("failed to stop service: %v", err)
		}

		// 等待服务停止
		for i := 0; i < 30; i++ {
			status, err = s.Query()
			if err != nil || status.State == svc.Stopped {
				break
			}
			time.Sleep(1 * time.Second)
		}
	}

	// 删除服务
	err = s.Delete()
	if err != nil {
		return fmt.Errorf("failed to delete service: %v", err)
	}

	// 删除事件日志源
	err = eventlog.Remove(serviceName)
	if err != nil {
		// 事件日志删除失败不是致命错误
		fmt.Printf("Warning: failed to remove event log: %v\n", err)
	}

	fmt.Printf("Service %s removed successfully\n", serviceName)
	return nil
}
