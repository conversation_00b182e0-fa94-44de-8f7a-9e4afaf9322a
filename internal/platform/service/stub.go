//go:build !windows

/*****************************************************************************
 * FILE: internal/platform/service/stub.go
 *
 * DESCRIPTION:
 *     非 Windows 平台的服务管理存根实现
 *
 * AUTHOR: wei
 * HISTORY: 31/07/2025 create
 *****************************************************************************/

package service

import (
	"context"
	"fmt"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: IsWindowsService
 *
 * DESCRIPTION:
 *     检查当前是否在 Windows 服务模式下运行（非 Windows 平台总是返回 false）
 *
 * RETURNS:
 *     bool - 是否为服务模式
 *****************************************************************************/
func IsWindowsService() bool {
	return false
}

/*****************************************************************************
 * NAME: RunService
 *
 * DESCRIPTION:
 *     运行 Windows 服务（非 Windows 平台不支持）
 *
 * PARAMETERS:
 *     log      - 日志记录器
 *     mainFunc - 主程序逻辑函数
 *
 * RETURNS:
 *     error - 运行错误
 *****************************************************************************/
func RunService(log logger.Logger, mainFunc func(context.Context) error) error {
	return fmt.Errorf("Windows service is not supported on this platform")
}

/*****************************************************************************
 * NAME: HandleServiceCommand
 *
 * DESCRIPTION:
 *     处理服务管理命令（非 Windows 平台不支持）
 *
 * PARAMETERS:
 *     command - 服务命令
 *     args    - 命令参数
 *
 * RETURNS:
 *     error - 处理错误
 *****************************************************************************/
func HandleServiceCommand(command string, args []string) error {
	return fmt.Errorf("Windows service commands are not supported on this platform")
}

/*****************************************************************************
 * NAME: PrintServiceUsage
 *
 * DESCRIPTION:
 *     打印服务管理命令的使用说明（非 Windows 平台）
 *****************************************************************************/
func PrintServiceUsage() {
	fmt.Println("Windows service commands are not supported on this platform")
}

/*****************************************************************************
 * NAME: GetCurrentExecutablePath
 *
 * DESCRIPTION:
 *     获取当前可执行文件的完整路径（非 Windows 平台）
 *
 * RETURNS:
 *     string - 可执行文件路径
 *     error  - 获取错误
 *****************************************************************************/
func GetCurrentExecutablePath() (string, error) {
	return "", fmt.Errorf("service management is not supported on this platform")
}

/*****************************************************************************
 * NAME: GetServiceContext
 *
 * DESCRIPTION:
 *     获取服务上下文（非 Windows 平台返回背景上下文）
 *
 * RETURNS:
 *     context.Context - 服务上下文
 *****************************************************************************/
func GetServiceContext() context.Context {
	return context.Background()
}
