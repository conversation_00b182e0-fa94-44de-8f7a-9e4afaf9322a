//go:build windows

/*****************************************************************************
 * FILE: internal/platform/service/manager.go
 *
 * DESCRIPTION:
 *     Windows 服务管理器，提供服务的启动、停止、状态查询等功能
 *
 * AUTHOR: wei
 * HISTORY: 31/07/2025 create
 *****************************************************************************/

package service

import (
	"fmt"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"time"

	"golang.org/x/sys/windows/svc"
	"golang.org/x/sys/windows/svc/mgr"
)

/*****************************************************************************
 * NAME: StartService
 *
 * DESCRIPTION:
 *     启动 Windows 服务
 *
 * RETURNS:
 *     error - 启动错误
 *****************************************************************************/
func StartService() error {
	m, err := mgr.Connect()
	if err != nil {
		return fmt.Errorf("failed to connect to service manager: %v", err)
	}
	defer m.Disconnect()

	s, err := m.OpenService(serviceName)
	if err != nil {
		return fmt.Errorf("service %s is not installed", serviceName)
	}
	defer s.Close()

	// 检查服务状态
	status, err := s.Query()
	if err != nil {
		return fmt.Errorf("failed to query service status: %v", err)
	}

	if status.State == svc.Running {
		fmt.Printf("Service %s is already running\n", serviceName)
		return nil
	}

	// 启动服务
	err = s.Start()
	if err != nil {
		return fmt.Errorf("failed to start service: %v", err)
	}

	// 等待服务启动
	for i := 0; i < 30; i++ {
		status, err = s.Query()
		if err != nil {
			return fmt.Errorf("failed to query service status: %v", err)
		}

		if status.State == svc.Running {
			fmt.Printf("Service %s started successfully\n", serviceName)
			return nil
		}

		if status.State == svc.Stopped {
			return fmt.Errorf("service failed to start")
		}

		time.Sleep(1 * time.Second)
	}

	return fmt.Errorf("service start timeout")
}

/*****************************************************************************
 * NAME: StopService
 *
 * DESCRIPTION:
 *     停止 Windows 服务
 *
 * RETURNS:
 *     error - 停止错误
 *****************************************************************************/
func StopService() error {
	m, err := mgr.Connect()
	if err != nil {
		return fmt.Errorf("failed to connect to service manager: %v", err)
	}
	defer m.Disconnect()

	s, err := m.OpenService(serviceName)
	if err != nil {
		return fmt.Errorf("service %s is not installed", serviceName)
	}
	defer s.Close()

	// 检查服务状态
	status, err := s.Query()
	if err != nil {
		return fmt.Errorf("failed to query service status: %v", err)
	}

	if status.State == svc.Stopped {
		fmt.Printf("Service %s is already stopped\n", serviceName)
		return nil
	}

	// 停止服务
	_, err = s.Control(svc.Stop)
	if err != nil {
		return fmt.Errorf("failed to stop service: %v", err)
	}

	// 等待服务停止
	for i := 0; i < 30; i++ {
		status, err = s.Query()
		if err != nil {
			return fmt.Errorf("failed to query service status: %v", err)
		}

		if status.State == svc.Stopped {
			fmt.Printf("Service %s stopped successfully\n", serviceName)
			return nil
		}

		time.Sleep(1 * time.Second)
	}

	// 如果服务停止超时，尝试强制停止
	fmt.Printf("Service stop timeout, attempting force stop...\n")
	return forceStopService()
}

/*****************************************************************************
 * NAME: forceStopService
 *
 * DESCRIPTION:
 *     强制停止卡住的 Windows 服务
 *
 * RETURNS:
 *     error - 强制停止错误
 *****************************************************************************/
func forceStopService() error {
	// 1. 尝试通过 HTTP API 优雅关闭
	if err := tryHTTPShutdown(); err == nil {
		fmt.Printf("Service stopped via HTTP API\n")
		return nil
	}

	// 2. 再次尝试服务停止命令
	if err := retryServiceStop(); err != nil {
		fmt.Printf("Warning: retry service stop failed: %v\n", err)
	}

	// 3. 等待一段时间让进程完全终止
	time.Sleep(3 * time.Second)

	// 4. 验证服务是否已停止
	m, err := mgr.Connect()
	if err != nil {
		return fmt.Errorf("failed to connect to service manager: %v", err)
	}
	defer m.Disconnect()

	s, err := m.OpenService(serviceName)
	if err != nil {
		// 服务不存在，认为已停止
		fmt.Printf("Service %s stopped (not found)\n", serviceName)
		return nil
	}
	defer s.Close()

	status, err := s.Query()
	if err != nil {
		// 查询失败，可能服务已停止
		fmt.Printf("Service %s stopped (query failed)\n", serviceName)
		return nil
	}

	if status.State == svc.Stopped {
		fmt.Printf("Service %s stopped successfully after force stop\n", serviceName)
		return nil
	}

	return fmt.Errorf("service still running after force stop attempt")
}

/*****************************************************************************
 * NAME: QueryServiceStatus
 *
 * DESCRIPTION:
 *     查询 Windows 服务状态
 *
 * RETURNS:
 *     string - 服务状态描述
 *     error  - 查询错误
 *****************************************************************************/
func QueryServiceStatus() (string, error) {
	m, err := mgr.Connect()
	if err != nil {
		return "", fmt.Errorf("failed to connect to service manager: %v", err)
	}
	defer m.Disconnect()

	s, err := m.OpenService(serviceName)
	if err != nil {
		return "Not Installed", nil
	}
	defer s.Close()

	status, err := s.Query()
	if err != nil {
		return "", fmt.Errorf("failed to query service status: %v", err)
	}

	switch status.State {
	case svc.Stopped:
		return "Stopped", nil
	case svc.StartPending:
		return "Starting", nil
	case svc.StopPending:
		return "Stopping", nil
	case svc.Running:
		return "Running", nil
	case svc.ContinuePending:
		return "Continue Pending", nil
	case svc.PausePending:
		return "Pause Pending", nil
	case svc.Paused:
		return "Paused", nil
	default:
		return fmt.Sprintf("Unknown (%d)", status.State), nil
	}
}

/*****************************************************************************
 * NAME: HandleServiceCommand
 *
 * DESCRIPTION:
 *     处理服务管理命令
 *
 * PARAMETERS:
 *     command - 服务命令 (install/remove/start/stop/status)
 *     args    - 命令参数
 *
 * RETURNS:
 *     error - 处理错误
 *****************************************************************************/
func HandleServiceCommand(command string, args []string) error {
	switch command {
	case "install":
		if len(args) < 1 {
			return fmt.Errorf("install command requires executable path")
		}
		exePath := args[0]
		configPath := ""
		if len(args) > 1 {
			configPath = args[1]
		} else {
			// 默认配置文件路径
			exeDir := filepath.Dir(exePath)
			configPath = filepath.Join(exeDir, "config.yaml")
		}
		return InstallService(exePath, configPath)

	case "remove", "uninstall":
		return RemoveService()

	case "start":
		return StartService()

	case "stop":
		return StopService()

	case "status":
		status, err := QueryServiceStatus()
		if err != nil {
			return err
		}
		fmt.Printf("Service %s status: %s\n", serviceName, status)
		return nil

	default:
		return fmt.Errorf("unknown service command: %s", command)
	}
}

/*****************************************************************************
 * NAME: GetCurrentExecutablePath
 *
 * DESCRIPTION:
 *     获取当前可执行文件的完整路径
 *
 * RETURNS:
 *     string - 可执行文件路径
 *     error  - 获取错误
 *****************************************************************************/
func GetCurrentExecutablePath() (string, error) {
	exePath, err := os.Executable()
	if err != nil {
		return "", fmt.Errorf("failed to get executable path: %v", err)
	}

	// 解析符号链接
	exePath, err = filepath.EvalSymlinks(exePath)
	if err != nil {
		return "", fmt.Errorf("failed to resolve executable path: %v", err)
	}

	return exePath, nil
}

/*****************************************************************************
 * NAME: PrintServiceUsage
 *
 * DESCRIPTION:
 *     打印服务管理命令的使用说明
 *****************************************************************************/
func PrintServiceUsage() {
	fmt.Println("Service management commands:")
	fmt.Println("  install [config-path]  - Install the service")
	fmt.Println("  remove                 - Remove the service")
	fmt.Println("  start                  - Start the service")
	fmt.Println("  stop                   - Stop the service")
	fmt.Println("  status                 - Query service status")
	fmt.Println("")
	fmt.Println("Examples:")
	fmt.Println("  panabit-service.exe install")
	fmt.Println("  panabit-service.exe install C:\\ProgramData\\Panabit\\config.yaml")
	fmt.Println("  panabit-service.exe start")
	fmt.Println("  panabit-service.exe stop")
	fmt.Println("  panabit-service.exe remove")
}

/*****************************************************************************
 * NAME: tryHTTPShutdown
 *
 * DESCRIPTION:
 *     尝试通过 HTTP API 优雅关闭服务
 *
 * RETURNS:
 *     error - HTTP 关闭错误
 *****************************************************************************/
func tryHTTPShutdown() error {
	// 简单的 HTTP POST 请求到 /api/shutdown
	// 这里使用基本的 net/http 客户端
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	resp, err := client.Post("http://127.0.0.1:56543/api/shutdown", "application/json", nil)
	if err != nil {
		return fmt.Errorf("HTTP shutdown request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		return nil
	}

	return fmt.Errorf("HTTP shutdown returned status: %d", resp.StatusCode)
}

/*****************************************************************************
 * NAME: retryServiceStop
 *
 * DESCRIPTION:
 *     重试服务停止命令
 *
 * RETURNS:
 *     error - 重试停止错误
 *****************************************************************************/
func retryServiceStop() error {
	// 使用 sc stop 命令再次尝试停止服务
	cmd := exec.Command("sc", "stop", "PanabitService")
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("failed to retry service stop: %v", err)
	}

	// 等待一段时间
	time.Sleep(2 * time.Second)
	return nil
}
