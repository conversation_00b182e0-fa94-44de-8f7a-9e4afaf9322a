/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      interfaces.go
 *
 * DESCRIPTION :    Core interfaces for dependency injection and abstraction
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package interfaces

import (
	"context"
	"net"
	"time"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: AuthResult
 *
 * DESCRIPTION:
 *     Authentication result structure
 *
 * FIELDS:
 *     Success      - Authentication success flag
 *     ErrorCode    - Error code
 *     ErrorMessage - Error message
 *     SessionID    - Session ID
 *     Token        - Authentication token
 *     Config       - Configuration information
 *****************************************************************************/
type AuthResult struct {
	Success      bool              // Authentication success flag
	ErrorCode    int               // Error code
	ErrorMessage string            // Error message
	SessionID    uint16            // Session ID
	Token        uint32            // Authentication token
	Config       map[string]string // Configuration information
}

/*****************************************************************************
 * NAME: InterfaceInfo
 *
 * DESCRIPTION:
 *     Network interface information
 *
 * FIELDS:
 *     InterfaceName  - Physical interface name
 *     LocalIP        - Local IP address (physical interface IP)
 *     TunIP          - TUN device IP address
 *     InterfaceIndex - Physical interface index
 *****************************************************************************/
type InterfaceInfo struct {
	InterfaceName  string `json:"interface_name"`  // Physical interface name
	LocalIP        string `json:"local_ip"`        // Local IP address (physical interface IP)
	TunIP          string `json:"tun_ip"`          // TUN device IP address
	InterfaceIndex int    `json:"interface_index"` // Physical interface index
}

/*****************************************************************************
 * NAME: TrafficStats
 *
 * DESCRIPTION:
 *     Traffic statistics structure
 *
 * FIELDS:
 *     TotalUpload   - Total uploaded bytes
 *     TotalDownload - Total downloaded bytes
 *     UploadSpeed   - Upload speed (bytes/sec)
 *     DownloadSpeed - Download speed (bytes/sec)
 *     LastUpdate    - Last update time
 *****************************************************************************/
type TrafficStats struct {
	TotalUpload   int64     // Total uploaded bytes
	TotalDownload int64     // Total downloaded bytes
	UploadSpeed   int64     // Upload speed (bytes/sec)
	DownloadSpeed int64     // Download speed (bytes/sec)
	LastUpdate    time.Time // Last update time
}

/*****************************************************************************
 * NAME: Status
 *
 * DESCRIPTION:
 *     Internal connection status structure
 *
 * FIELDS:
 *     State         - Current state
 *     StateString   - State string
 *     SessionID     - Session ID
 *     Token         - Authentication token
 *     LastHeartbeat - Last heartbeat time
 *     ConnectedTime - Connection time
 *     Config        - Configuration information
 *     ErrorCode     - Error code
 *     ErrorMessage  - Error message
 *     Status        - API status (disconnected, connecting, connected, error)
 *     Message       - Status message
 *     Server        - Currently connected server
 *****************************************************************************/
type Status struct {
	State         uint8             `json:"state"`          // Current state
	StateString   string            `json:"state_string"`   // State string
	SessionID     uint16            `json:"session_id"`     // Session ID
	Token         uint32            `json:"token"`          // Authentication token
	LastHeartbeat time.Time         `json:"last_heartbeat"` // Last heartbeat time
	ConnectedTime time.Time         `json:"connected_time"` // Connection time
	Config        map[string]string `json:"config"`         // Configuration information
	ErrorCode     int               `json:"error_code"`     // Error code
	ErrorMessage  string            `json:"error_message"`  // Error message
	Status        string            `json:"status"`         // API status (disconnected, connecting, connected, error)
	Message       string            `json:"message"`        // Status message
	Server        *ServerInfo       `json:"server"`         // Currently connected server
}

/*****************************************************************************
 * NAME: StatusInfo
 *
 * DESCRIPTION:
 *     Connection status information for API responses
 *
 * FIELDS:
 *     Status        - Connection status
 *     Message       - Status message
 *     Server        - Server information
 *     ConnectedTime - Connection timestamp
 *****************************************************************************/
type StatusInfo struct {
	Status        string      `json:"status"`
	Message       string      `json:"message,omitempty"`
	Server        *ServerInfo `json:"server,omitempty"`
	ConnectedTime int64       `json:"connected_time,omitempty"`
}

/*****************************************************************************
 * NAME: ServerInfo
 *
 * DESCRIPTION:
 *     Server information structure
 *
 * FIELDS:
 *     ID         - Server unique identifier
 *     Name       - Server display name
 *     NameEn     - Server English name
 *     ServerName - Server hostname
 *     ServerPort - Server port number
 *     Ping       - Server ping latency
 *     IsAuto     - Auto selection flag
 *     Status     - Server status
 *     IsDefault  - Default server flag
 *****************************************************************************/
type ServerInfo struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	NameEn     string `json:"name_en"`
	ServerName string `json:"server_name"`
	ServerPort int    `json:"server_port"`
	Ping       int    `json:"ping"`
	IsAuto     bool   `json:"isauto"`
	Status     string `json:"status"`
	IsDefault  bool   `json:"is_default"`
}

/*****************************************************************************
 * NAME: ConnectionManager
 *
 * DESCRIPTION:
 *     Interface for managing VPN connections and their lifecycle
 *****************************************************************************/
type ConnectionManager interface {
	// Lifecycle management
	Start() error
	Stop() error

	// Authentication and connection
	Authenticate(username, password, serverAddress string, serverPort int) (*AuthResult, error)
	AuthAndConnect(serverAddress string, serverPort int) error
	Disconnect() error

	// Status and information
	GetStatus() Status
	GetInterfaceInfo() (*InterfaceInfo, error)

	// Traffic management
	SendData(data []byte) error
	UpdateTrafficSpeed()
	GetTrafficStats() TrafficStats

	// TUN device management
	StartTunReader() error
}

/*****************************************************************************
 * NAME: ServerManager
 *
 * DESCRIPTION:
 *     Interface for managing VPN servers and server selection
 *****************************************************************************/
type ServerManager interface {
	// Lifecycle management
	Initialize() error
	Start() error
	Stop() error

	// Server operations
	GetServers() []Server
	GetBestServer() (*Server, error)
	GetDefaultServer() (*Server, error)
	SetDefaultServer(serverID string) error

	// Server testing and monitoring
	PingServers() error
	SelectBestAutoServer() (*Server, error)

	// Provider management
	AddProvider(provider ServerProvider)

	// Callbacks
	RegisterUpdateCallback(callback func([]Server))
	RegisterStatusCallback(callback func(string, string))
}

/*****************************************************************************
 * NAME: ServerProvider
 *
 * DESCRIPTION:
 *     Interface for providing server lists from different sources
 *****************************************************************************/
type ServerProvider interface {
	GetServerList() ([]Server, error)
	GetName() string
	GetDescription() string
}

/*****************************************************************************
 * NAME: Server
 *
 * DESCRIPTION:
 *     Represents a VPN server with its properties and status
 *
 * FIELDS:
 *     ID         - Server unique identifier
 *     Name       - Server display name
 *     NameEn     - Server English name
 *     ServerName - Server hostname
 *     ServerPort - Server port number
 *     IsAuto     - Auto selection flag
 *     Ping       - Server ping latency
 *     Status     - Server status
 *     LastCheck  - Last health check time
 *****************************************************************************/
type Server struct {
	ID         string    `json:"id"`
	Name       string    `json:"name"`
	NameEn     string    `json:"name_en"`
	ServerName string    `json:"server_name"`
	ServerPort int       `json:"server_port"`
	IsAuto     bool      `json:"isauto"`
	Ping       int       `json:"ping"`
	Status     string    `json:"status"`
	LastCheck  time.Time `json:"last_check"`
}

/*****************************************************************************
 * NAME: TunDevice
 *
 * DESCRIPTION:
 *     Interface for TUN device operations
 *****************************************************************************/
type TunDevice interface {
	// Basic I/O operations
	Read([]byte) (int, error)
	Write([]byte) (int, error)
	Close() error

	// Configuration
	Configure(config TunDeviceConfig) error
	Start() error
	Stop() error

	// Properties
	Name() string
	MTU() int
	SetMTU(mtu int) error
	IsUp() bool

	// Network configuration
	Addresses() []net.IPNet
	SetAddresses(addresses []net.IPNet) error
	AddRoute(destination net.IPNet, gateway net.IP, metric int) error
	DeleteRoute(destination net.IPNet, gateway net.IP) error
	SetDNS(servers []net.IP) error
	SetDefaultGateway(gateway net.IP) error
}

/*****************************************************************************
 * NAME: TunDeviceConfig
 *
 * DESCRIPTION:
 *     Configuration for TUN device
 *
 * FIELDS:
 *     Name      - TUN device name
 *     MTU       - Maximum transmission unit
 *     Addresses - IP addresses assigned to device
 *****************************************************************************/
type TunDeviceConfig struct {
	Name      string
	MTU       int
	Addresses []net.IPNet
}

/*****************************************************************************
 * NAME: Encryptor
 *
 * DESCRIPTION:
 *     Interface for data encryption and decryption
 *****************************************************************************/
type Encryptor interface {
	Encrypt(data []byte) ([]byte, error)
	Decrypt(data []byte) ([]byte, error)
	EncryptPacket(packet Packet) (Packet, error)
	DecryptPacket(packet Packet) (Packet, error)
	Method() uint8
}

/*****************************************************************************
 * NAME: Packet
 *
 * DESCRIPTION:
 *     Interface for network packets
 *****************************************************************************/
type Packet interface {
	Header() PacketHeader
	Data() []byte
	SetData(data []byte)
	Serialize() ([]byte, error)
}

/*****************************************************************************
 * NAME: PacketHeader
 *
 * DESCRIPTION:
 *     Interface for packet headers
 *****************************************************************************/
type PacketHeader interface {
	Type() uint8
	SetType(packetType uint8)
	SessionID() uint16
	SetSessionID(sid uint16)
	Token() uint32
	SetToken(token uint32)
	EncryptMethod() uint8
	SetEncryptMethod(method uint8)
}

/*****************************************************************************
 * NAME: ConfigManager
 *
 * DESCRIPTION:
 *     Interface for configuration management
 *****************************************************************************/
type ConfigManager interface {
	Load(configPath string) error
	Save(configPath string) error
	Get(key string) interface{}
	Set(key string, value interface{})
	GetString(key string) string
	GetInt(key string) int
	GetBool(key string) bool
	GetDuration(key string) time.Duration
}

/*****************************************************************************
 * NAME: HTTPServer
 *
 * DESCRIPTION:
 *     Interface for HTTP server operations
 *****************************************************************************/
type HTTPServer interface {
	Start() error
	Stop() error
	RegisterHandler(path string, handler interface{})
	SetDependencies(deps *ServiceDependencies)
}

/*****************************************************************************
 * NAME: WebSocketHandler
 *
 * DESCRIPTION:
 *     Interface for WebSocket operations
 *****************************************************************************/
type WebSocketHandler interface {
	Start()
	Stop()
	Broadcast(event interface{})
	SetConnectionManager(cm ConnectionManager)
	SetServerManager(sm ServerManager)
}

/*****************************************************************************
 * NAME: ServiceDependencies
 *
 * DESCRIPTION:
 *     Container for service dependencies
 *
 * FIELDS:
 *     ConnectionManager - Connection management service
 *     ServerManager     - Server management service
 *     ConfigManager     - Configuration management service
 *     Logger            - Logging service
 *****************************************************************************/
type ServiceDependencies struct {
	ConnectionManager ConnectionManager
	ServerManager     ServerManager
	ConfigManager     ConfigManager
	Logger            logger.Logger
}

/*****************************************************************************
 * NAME: Component
 *
 * DESCRIPTION:
 *     Base interface for all components that can be managed by the container
 *****************************************************************************/
type Component interface {
	Initialize(ctx context.Context) error
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
	Name() string
}

/*****************************************************************************
 * NAME: HealthChecker
 *
 * DESCRIPTION:
 *     Interface for components that can report their health status
 *****************************************************************************/
type HealthChecker interface {
	HealthCheck(ctx context.Context) error
	IsHealthy() bool
}

/*****************************************************************************
 * NAME: Configurable
 *
 * DESCRIPTION:
 *     Interface for components that can be configured
 *****************************************************************************/
type Configurable interface {
	Configure(config interface{}) error
	GetConfig() interface{}
}

/*****************************************************************************
 * NAME: Metrics
 *
 * DESCRIPTION:
 *     Interface for components that can provide metrics
 *****************************************************************************/
type Metrics interface {
	GetMetrics() map[string]interface{}
	ResetMetrics()
}

/*****************************************************************************
 * NAME: EventPublisher
 *
 * DESCRIPTION:
 *     Interface for publishing events
 *****************************************************************************/
type EventPublisher interface {
	Publish(event interface{}) error
	Subscribe(eventType string, handler func(event interface{})) error
	Unsubscribe(eventType string, handler func(event interface{})) error
}

/*****************************************************************************
 * NAME: EventSubscriber
 *
 * DESCRIPTION:
 *     Interface for subscribing to events
 *****************************************************************************/
type EventSubscriber interface {
	Subscribe(eventType string, handler func(event interface{})) error
	Unsubscribe(eventType string, handler func(event interface{})) error
}
