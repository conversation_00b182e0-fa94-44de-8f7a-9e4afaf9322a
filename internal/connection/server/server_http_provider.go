/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      server_http_provider.go
 *
 * DESCRIPTION :    HTTP-based server list provider implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package server

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: HTTPProvider
 *
 * DESCRIPTION:
 *     HTTP-based server list provider that fetches server information
 *     from remote HTTP endpoints
 *
 * FIELDS:
 *     url     - Server list URL
 *     timeout - Request timeout duration
 *     client  - HTTP client instance
 *     logger  - Logger instance
 *****************************************************************************/
type HTTPProvider struct {
	url     string        // Server list URL
	timeout time.Duration // Request timeout duration
	client  *http.Client  // HTTP client instance
	logger  logger.Logger // Logger instance
}

/*****************************************************************************
 * NAME: NewHTTPProvider
 *
 * DESCRIPTION:
 *     Creates a new HTTP-based server list provider instance
 *
 * PARAMETERS:
 *     url           - Server list URL
 *     timeout       - Request timeout duration
 *     tlsSkipVerify - Skip TLS certificate verification
 *     log           - Logger instance
 *
 * RETURNS:
 *     *HTTPProvider - New HTTP provider instance
 *****************************************************************************/
func NewHTTPProvider(url string, timeout time.Duration, tlsSkipVerify bool, log logger.Logger) *HTTPProvider {
	// Create HTTP client with TLS configuration
	client := &http.Client{
		Timeout: timeout,
	}

	// Configure TLS settings for HTTPS requests
	if tlsSkipVerify {
		transport := &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		}
		client.Transport = transport
		log.Warn("TLS certificate verification disabled for HTTP provider",
			logger.String("url", url))
	}

	return &HTTPProvider{
		url:     url,
		timeout: timeout,
		client:  client,
		logger:  log.WithModule("http-provider"),
	}
}

/*****************************************************************************
 * NAME: GetServerList
 *
 * DESCRIPTION:
 *     Retrieves server list from HTTP endpoint by sending GET request
 *     and parsing the JSON response
 *
 * RETURNS:
 *     []Server - List of servers from HTTP response
 *     error    - Error if request fails or response cannot be parsed
 *****************************************************************************/
func (p *HTTPProvider) GetServerList() ([]Server, error) {
	p.logger.Info("Fetching server list", logger.String("url", p.url))

	// Send HTTP request
	resp, err := p.client.Get(p.url)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch server list: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("server returned non-OK status: %d", resp.StatusCode)
	}

	// Read response content
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Parse server list
	return p.parseServerList(body)
}

/*****************************************************************************
 * NAME: GetName
 *
 * DESCRIPTION:
 *     Returns the provider's name
 *
 * RETURNS:
 *     string - Provider name
 *****************************************************************************/
func (p *HTTPProvider) GetName() string {
	return "HTTP Provider"
}

/*****************************************************************************
 * NAME: GetDescription
 *
 * DESCRIPTION:
 *     Returns the provider's description including the URL
 *
 * RETURNS:
 *     string - Provider description
 *****************************************************************************/
func (p *HTTPProvider) GetDescription() string {
	return fmt.Sprintf("HTTP server list provider (%s)", p.url)
}

/*****************************************************************************
 * NAME: parseServerList
 *
 * DESCRIPTION:
 *     Parses server list from JSON data, handling different response formats
 *     and setting default values for missing fields
 *
 * PARAMETERS:
 *     data - JSON data bytes to parse
 *
 * RETURNS:
 *     []Server - Parsed list of servers
 *     error    - Error if parsing fails
 *****************************************************************************/
func (p *HTTPProvider) parseServerList(data []byte) ([]Server, error) {
	// Parse JSON format
	var jsonResponse struct {
		Version    string `json:"version"`
		ServerList []struct {
			ID         int    `json:"id"`
			IsAuto     bool   `json:"isauto"`
			Name       string `json:"name"`
			NameEn     string `json:"name_en"`
			ServerName string `json:"serverName"`
			ServerPort int    `json:"serverPort"`
		} `json:"serverlist"`
	}

	if err := json.Unmarshal(data, &jsonResponse); err != nil {
		return nil, fmt.Errorf("failed to parse server list: %w", err)
	}

	if len(jsonResponse.ServerList) == 0 {
		return nil, fmt.Errorf("no servers found in response")
	}

	p.logger.Info("Parsed server list",
		logger.String("version", jsonResponse.Version),
		logger.Int("count", len(jsonResponse.ServerList)))

	servers := make([]Server, 0, len(jsonResponse.ServerList))
	for _, s := range jsonResponse.ServerList {
		serverPort := s.ServerPort
		if serverPort == 0 {
			serverPort = 8000 // Default port
		}
		servers = append(servers, Server{
			ID:         fmt.Sprintf("%d", s.ID),
			IsAuto:     s.IsAuto,
			Name:       s.Name,
			NameEn:     s.NameEn,
			ServerName: s.ServerName,
			ServerPort: serverPort,
			Ping:       0, // Will be updated by PingServers
			Status:     "unknown",
			LastCheck:  time.Time{},
		})
	}
	return servers, nil
}
