/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      server_ping.go
 *
 * DESCRIPTION :    Server ping functionality for latency measurement
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package server

import (
	"context"
	"fmt"
	"net"
	"sort"
	"strings"
	"sync"
	"time"

	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
	"mobile/internal/protocol/tunnel"
)

// PingResultsCallback defines callback function type for ping results
type PingResultsCallback func([]Server)

// pingResultsCallbacks stores ping results callback functions
var pingResultsCallbacks []PingResultsCallback

// pingResult represents the result of a ping operation
type pingResult struct {
	serverID     string
	pingTime     int
	err          error
	statusChange string
}

/*****************************************************************************
 * NAME: IsPingInProgress
 *
 * DESCRIPTION:
 *     Checks if a ping operation is currently in progress
 *
 * RETURNS:
 *     bool - True if ping operation is in progress
 *****************************************************************************/
func (m *Manager) IsPingInProgress() bool {
	//m.pingMutex.Lock()
	//defer m.pingMutex.Unlock()
	return m.pingInProgress
}

/*****************************************************************************
 * NAME: WaitForPingCompletion
 *
 * DESCRIPTION:
 *     Waits for ping operation to complete
 *
 * PARAMETERS:
 *     timeout - Timeout duration for waiting
 *
 * RETURNS:
 *     error - Error if timeout occurs
 *****************************************************************************/
func (m *Manager) WaitForPingCompletion(timeout time.Duration) error {
	m.logger.Debug("Waiting for ping completion", logger.Duration("timeout", timeout))

	start := time.Now()
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if !m.IsPingInProgress() {
				m.logger.Debug("Ping operation completed", logger.Duration("waited", time.Since(start)))
				return nil
			}

			if time.Since(start) > timeout {
				m.logger.Warn("Timeout waiting for ping completion", logger.Duration("timeout", timeout))
				return fmt.Errorf("timeout waiting for ping completion after %v", timeout)
			}
		}
	}
}

/*****************************************************************************
 * NAME: PingServersAndWait
 *
 * DESCRIPTION:
 *     Executes ping operation and waits for completion
 *
 * PARAMETERS:
 *     timeout - Timeout duration for ping completion
 *
 * RETURNS:
 *     error - Error if ping operation fails
 *****************************************************************************/
func (m *Manager) PingServersAndWait(timeout time.Duration) error {
	m.logger.Debug("Starting ping servers and wait", logger.Duration("timeout", timeout))

	// Start ping operation
	if err := m.PingServers(); err != nil {
		return fmt.Errorf("failed to start ping operation: %w", err)
	}

	// Wait for ping completion
	return m.WaitForPingCompletion(timeout)
}

/*****************************************************************************
 * NAME: RegisterPingResultsCallback
 *
 * DESCRIPTION:
 *     Registers a ping results callback function
 *
 * PARAMETERS:
 *     callback - Callback function to register
 *****************************************************************************/
func (m *Manager) RegisterPingResultsCallback(callback PingResultsCallback) {
	pingResultsCallbacks = append(pingResultsCallbacks, callback)
}

/*****************************************************************************
 * NAME: notifyPingResultsCallbacks
 *
 * DESCRIPTION:
 *     Notifies all registered ping results callbacks
 *****************************************************************************/
func (m *Manager) notifyPingResultsCallbacks() {
	servers := m.GetServers()
	for _, callback := range pingResultsCallbacks {
		go callback(servers)
	}
}

/*****************************************************************************
 * NAME: pingServer
 *
 * DESCRIPTION:
 *     Measures server latency by sending 3 OPEN packets concurrently and measuring response time.
 *     Returns the lowest latency from successful responses for improved accuracy.
 *     Uses existing CreateOpenPacket function to avoid code duplication.
 *
 * PARAMETERS:
 *     address - Server address (domain or IP)
 *     port    - Server port number
 *
 * RETURNS:
 *     int   - Lowest latency in milliseconds from successful responses
 *     error - Error if all ping attempts fail
 *****************************************************************************/
func (m *Manager) pingServer(address string, port int) (int, error) {
	var targetAddress string
	var useResolvedIP bool

	// Try to resolve IP using unified ResolveServerIP method
	if resolvedIP := m.ResolveServerIP(address); resolvedIP != "" {
		targetAddress = resolvedIP
		useResolvedIP = true
		m.logger.Debug("Using resolved IP for 3-packet ping test",
			logger.String("hostname", address),
			logger.String("resolved_ip", resolvedIP),
			logger.Bool("from_cache", true))
	} else {
		// Fallback to hostname - network layer will handle DNS resolution
		targetAddress = address
		useResolvedIP = false
		m.logger.Debug("Using hostname fallback for 3-packet ping test",
			logger.String("hostname", address),
			logger.String("fallback_note", "DNS resolution will occur in network layer"))
	}

	m.logger.Debug("Starting 3-packet concurrent ping test",
		logger.String("hostname", address),
		logger.String("target_address", targetAddress),
		logger.Bool("use_resolved_ip", useResolvedIP))

	// Create OPEN packet once for all attempts
	openPacket, err := tunnel.CreateOpenPacket("notExist", "mPassword", 1420, 0)
	if err != nil {
		return 0, fmt.Errorf("failed to create ping packet: %w", err)
	}
	packetBytes := openPacket.Bytes()

	// Execute 3 concurrent ping attempts
	type pingResult struct {
		latency int
		success bool
		index   int
	}

	resultCh := make(chan pingResult, 3)
	var wg sync.WaitGroup

	// Create context with timeout for all attempts
	ctx, cancel := context.WithTimeout(context.Background(), m.config.PingTimeout+2*time.Second)
	defer cancel()

	// Launch 3 concurrent ping attempts
	for i := 1; i <= 3; i++ {
		wg.Add(1)
		go func(packetIndex int) {
			defer wg.Done()
			latency, success := m.performSinglePingAttempt(ctx, targetAddress, port, packetBytes, packetIndex, address)

			select {
			case resultCh <- pingResult{latency: latency, success: success, index: packetIndex}:
			case <-ctx.Done():
			}
		}(i)
	}

	// Wait for all attempts to complete
	go func() {
		wg.Wait()
		close(resultCh)
	}()

	// Collect results
	var successfulLatencies []int
	for result := range resultCh {
		if result.success && result.latency > 0 {
			successfulLatencies = append(successfulLatencies, result.latency)
		}
	}

	// Process results: return minimum latency from successful attempts
	if len(successfulLatencies) > 0 {
		minLatency := successfulLatencies[0]
		for _, latency := range successfulLatencies[1:] {
			if latency < minLatency {
				minLatency = latency
			}
		}

		// Convert latencies to string for logging
		latencyStrings := make([]string, len(successfulLatencies))
		for i, lat := range successfulLatencies {
			latencyStrings[i] = fmt.Sprintf("%dms", lat)
		}

		m.logger.Debug("3-packet ping test completed successfully",
			logger.String("server", address),
			logger.Int("port", port),
			logger.Int("successful_packets", len(successfulLatencies)),
			logger.String("latencies", strings.Join(latencyStrings, ", ")),
			logger.Int("min_latency_ms", minLatency))

		return minLatency, nil
	} else {
		m.logger.Debug("3-packet ping test failed - no successful responses",
			logger.String("server", address),
			logger.Int("port", port),
			logger.String("failed_packets", "3/3"))

		return 0, fmt.Errorf("no response from server after 3 attempts")
	}
}

/*****************************************************************************
 * NAME: performSinglePingAttempt
 *
 * DESCRIPTION:
 *     Performs a single ping attempt as part of the 3-packet concurrent test.
 *     Creates an independent UDP connection and measures round-trip time.
 *
 * PARAMETERS:
 *     ctx         - Context for timeout control
 *     address     - Target server address
 *     port        - Target server port
 *     packetBytes - Pre-created ping packet data
 *     packetIndex - Index of this packet (1-3) for logging
 *     serverName  - Server name for logging
 *
 * RETURNS:
 *     int  - Latency in milliseconds
 *     bool - Success flag
 *****************************************************************************/
func (m *Manager) performSinglePingAttempt(ctx context.Context, address string, port int, packetBytes []byte, packetIndex int, serverName string) (int, bool) {
	// Create UDP connection for this attempt
	serverAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf("%s:%d", address, port))
	if err != nil {
		m.logger.Debug("Failed to resolve server address for packet",
			logger.String("server", serverName),
			logger.Int("packet", packetIndex),
			logger.ErrorField(err))
		return 0, false
	}

	conn, err := net.DialUDP("udp", nil, serverAddr)
	if err != nil {
		m.logger.Debug("Failed to create UDP connection for packet",
			logger.String("server", serverName),
			logger.Int("packet", packetIndex),
			logger.ErrorField(err))
		return 0, false
	}
	defer conn.Close()

	// Set timeout for this attempt
	deadline, ok := ctx.Deadline()
	if ok {
		conn.SetDeadline(deadline)
	} else {
		conn.SetDeadline(time.Now().Add(m.config.PingTimeout))
	}

	// Send packet
	if _, err := conn.Write(packetBytes); err != nil {
		m.logger.Debug("Failed to send ping packet",
			logger.String("server", serverName),
			logger.Int("packet", packetIndex),
			logger.ErrorField(err))
		return 0, false
	}

	// Record start time immediately after packet is sent (actual network measurement start)
	startTime := time.Now()

	// Receive response
	respPacket := make([]byte, 1024)
	_, err = conn.Read(respPacket)
	if err != nil {
		m.logger.Debug("Failed to receive ping response",
			logger.String("server", serverName),
			logger.Int("packet", packetIndex),
			logger.ErrorField(err))
		return 0, false
	}

	// Calculate latency
	latency := int(time.Since(startTime).Milliseconds())

	m.logger.Debug("Packet successful",
		logger.String("server", serverName),
		logger.Int("packet", packetIndex),
		logger.Int("latency_ms", latency))

	return latency, true
}

/*****************************************************************************
 * NAME: PingServers
 *
 * DESCRIPTION:
 *     Pings all servers in parallel to measure latency and update status
 *
 * RETURNS:
 *     error - Error if ping operation fails
 *****************************************************************************/
func (m *Manager) PingServers() error {
	// Check if ping operation is already in progress
	//m.pingMutex.Lock()
	if m.pingInProgress {
		//m.pingMutex.Unlock()
		m.logger.Debug("Ping operation already in progress, ignoring duplicate request")
		return nil
	}
	m.pingInProgress = true
	//m.pingMutex.Unlock()

	// Ensure ping flag is reset when function exits
	defer func() {
		//m.pingMutex.Lock()
		m.pingInProgress = false
		//m.pingMutex.Unlock()
	}()

	m.logger.Debug("Pinging servers (parallel execution)")

	// Get server list copy
	//m.mutex.RLock()
	servers := make([]Server, len(m.servers))
	copy(servers, m.servers)
	//m.mutex.RUnlock()

	if len(servers) == 0 {
		m.logger.Warn("No servers to ping")
		return nil
	}

	// Create wait group and result channel
	var wg sync.WaitGroup
	resultCh := make(chan pingResult, len(servers))

	// Create context with overall timeout for parallel execution
	ctx, cancel := context.WithTimeout(context.Background(), m.config.PingTimeout+5*time.Second)
	defer cancel()

	// Execute ping operations in parallel
	for _, server := range servers {
		wg.Add(1)
		go func(s Server) {
			defer func() {
				if r := recover(); r != nil {
					m.logger.Error("PANIC in ping server operation",
						logger.String("server", s.Name),
						logger.Any("panic", r))
					errors.LogPanic(m.logger, r)

					// Send error result
					select {
					case resultCh <- pingResult{serverID: s.ID, pingTime: 0, err: fmt.Errorf("panic occurred")}:
					case <-ctx.Done():
					}
				}
				wg.Done()
			}()

			// Check for context cancellation
			select {
			case <-ctx.Done():
				m.logger.Warn("Ping operation timed out",
					logger.String("server", s.Name))
				resultCh <- pingResult{serverID: s.ID, pingTime: 0, err: fmt.Errorf("context timeout")}
				return
			default:
			}

			// Measure latency using configured timeout
			pingTime, err := m.pingServer(s.ServerName, s.ServerPort)

			result := pingResult{
				serverID: s.ID,
				pingTime: pingTime,
				err:      err,
			}

			if err != nil {
				if s.Status != "offline" {
					result.statusChange = "offline"
				}
				m.logger.Debug("Server ping failed",
					logger.String("server", s.Name),
					logger.String("address", s.ServerName),
					logger.ErrorField(err))
			} else {
				if s.Status != "online" && pingTime > 0 {
					result.statusChange = "online"
				}
				m.logger.Debug("Server ping result",
					logger.String("server", s.Name),
					logger.String("address", s.ServerName),
					logger.Int("ping", pingTime))
			}

			// Send result
			select {
			case resultCh <- result:
			case <-ctx.Done():
			}
		}(server)
	}

	// Wait for all ping operations to complete
	go func() {
		wg.Wait()
		close(resultCh)
	}()

	// Collect results
	results := make(map[string]int)
	statusChanges := make(map[string]string)
	for result := range resultCh {
		if result.err != nil {
			results[result.serverID] = 0 // 0 indicates ping failure
		} else {
			results[result.serverID] = result.pingTime
		}
		if result.statusChange != "" {
			statusChanges[result.serverID] = result.statusChange
		}
	}

	m.logger.Debug("All ping operations completed successfully (parallel execution)")

	// Update server list with ping results (using write lock)
	//m.mutex.Lock()
	for i := range m.servers {
		if pingTime, ok := results[m.servers[i].ID]; ok {
			m.servers[i].Ping = pingTime
			m.servers[i].LastCheck = time.Now()

			// Update status
			if pingTime > 0 {
				m.servers[i].Status = "online"
			} else {
				m.servers[i].Status = "offline"
			}
		}
	}

	// Sort servers by latency (online servers first, then by ping time)
	sort.Slice(m.servers, func(i, j int) bool {
		// Online servers first
		if m.servers[i].Status == "online" && m.servers[j].Status != "online" {
			return true
		}
		if m.servers[i].Status != "online" && m.servers[j].Status == "online" {
			return false
		}

		// If both have same status, sort by latency
		if m.servers[i].Status == "online" && m.servers[j].Status == "online" {
			// Sort by latency
			if m.servers[i].Ping == 0 {
				return false
			}
			if m.servers[j].Ping == 0 {
				return true
			}
			return m.servers[i].Ping < m.servers[j].Ping
		}

		return false
	})
	//m.mutex.Unlock()

	// Unified notification: send notifications once after all ping operations complete
	// Notify status changes (execute outside lock to avoid potential deadlock in callbacks)
	for serverID, newStatus := range statusChanges {
		m.notifyStatusCallback(serverID, newStatus)
	}

	// Notify ping results callbacks (if any)
	m.notifyPingResultsCallbacks()

	m.logger.Debug("Ping results notification sent", logger.Int("servers_count", len(m.servers)))

	return nil
}
