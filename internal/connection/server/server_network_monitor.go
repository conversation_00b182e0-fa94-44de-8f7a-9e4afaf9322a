/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      server_network_monitor.go
 *
 * DESCRIPTION :    Network interface monitoring functionality for VPN server manager
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package server

import (
	"fmt"

	"mobile/internal/common/logger"
	"mobile/internal/platform/network"
)

/*****************************************************************************
 * NAME: GetPhysicalInterfaceInfo
 *
 * DESCRIPTION:
 *     Gets current physical interface information. This method is provided
 *     for use by other packages to avoid duplicate network interface detection.
 *
 * RETURNS:
 *     *PhysicalInterfaceInfo - Currently cached physical interface information
 *****************************************************************************/
func (m *Manager) GetPhysicalInterfaceInfo() *PhysicalInterfaceInfo {
	// Return currently cached physical interface information
	// This information is continuously monitored and updated by server manager, more accurate than temporary acquisition
	return m.physicalIfaceInfo
}

/*****************************************************************************
 * NAME: initializePhysicalInterface
 *
 * DESCRIPTION:
 *     Initializes physical interface information by getting current network
 *     interface details and creating NetworkInterface abstraction
 *
 * RETURNS:
 *     error - Error if initialization fails
 *****************************************************************************/
func (m *Manager) initializePhysicalInterface() error {
	m.logger.Info("Initializing physical interface information")

	// Get physical interface information
	physicalInfo, err := network.GetPhysicalInterfaceInfo(m.logger)
	if err != nil {
		return fmt.Errorf("failed to get physical interface info: %w", err)
	}

	// Create NetworkInterface abstraction
	netInterface, err := network.NewNetworkInterface(physicalInfo.LUID, physicalInfo.Name, m.logger)
	if err != nil {
		return fmt.Errorf("failed to create network interface: %w", err)
	}

	// Save physical interface information
	m.physicalIfaceInfo = physicalInfo
	m.physicalGateway = physicalInfo.Gateway
	m.physicalNetInterface = netInterface

	m.logger.Info("Physical interface initialized successfully",
		logger.String("interface_name", physicalInfo.Name),
		logger.Int("interface_index", physicalInfo.Index),
		logger.Uint64("interface_luid", physicalInfo.LUID),
		logger.String("gateway", physicalInfo.Gateway.String()),
		logger.String("local_ip", physicalInfo.LocalIP.String()))

	return nil
}

/*****************************************************************************
 * NAME: checkNetworkChanges
 *
 * DESCRIPTION:
 *     Checks network configuration changes by comparing current physical
 *     interface information with cached information
 *
 * RETURNS:
 *     error - Error if check operation fails
 *****************************************************************************/
func (m *Manager) checkNetworkChanges() error {
	// Get current physical interface information (reuse existing function)
	currentInterface, err := network.GetPhysicalInterfaceInfo(m.logger)
	if err != nil {
		return fmt.Errorf("failed to get current physical interface info: %w", err)
	}

	// If current physical interface information is not initialized, update directly
	if m.physicalIfaceInfo == nil {
		m.logger.Info("Physical interface info not initialized, setting current interface")
		m.physicalIfaceInfo = currentInterface
		m.physicalGateway = currentInterface.Gateway
		return nil
	}

	// Check if there are changes
	hasChanges := m.hasPhysicalInterfaceChanged(m.physicalIfaceInfo, currentInterface)
	if !hasChanges {
		m.logger.Debug("No network configuration changes detected")
		return nil
	}

	m.logger.Info("Network configuration changes detected, updating routes and interface info")

	// Handle network configuration changes
	err = m.handlePhysicalInterfaceChange(currentInterface)
	if err != nil {
		m.logger.Error("Failed to handle network config change", logger.ErrorField(err))
		return err
	}

	m.logger.Info("Network configuration change handling completed successfully")
	return nil
}

/*****************************************************************************
 * NAME: hasPhysicalInterfaceChanged
 *
 * DESCRIPTION:
 *     Checks if physical interface has changed by comparing baseline and
 *     current interface information
 *
 * PARAMETERS:
 *     baseline - Baseline physical interface information
 *     current  - Current physical interface information
 *
 * RETURNS:
 *     bool - True if interface has changed
 *****************************************************************************/
func (m *Manager) hasPhysicalInterfaceChanged(baseline, current *PhysicalInterfaceInfo) bool {
	// Check if gateway has changed
	if !baseline.Gateway.Equal(current.Gateway) {
		m.logger.Info("Physical interface gateway changed",
			logger.String("interface", current.Name),
			logger.String("old_gateway", baseline.Gateway.String()),
			logger.String("new_gateway", current.Gateway.String()))
		return true
	}

	// Check if IP address has changed
	if !baseline.LocalIP.Equal(current.LocalIP) {
		m.logger.Info("Physical interface IP address changed",
			logger.String("interface", current.Name),
			logger.String("old_ip", baseline.LocalIP.String()),
			logger.String("new_ip", current.LocalIP.String()))
		return true
	}

	// Check if interface name has changed
	if baseline.Name != current.Name {
		m.logger.Info("Physical interface name changed",
			logger.String("old_name", baseline.Name),
			logger.String("new_name", current.Name))
		return true
	}

	// Check if LUID has changed
	if baseline.LUID != current.LUID {
		m.logger.Info("Physical interface LUID changed",
			logger.String("interface", current.Name),
			logger.Uint64("old_luid", baseline.LUID),
			logger.Uint64("new_luid", current.LUID))
		return true
	}

	// Check if interface index has changed
	if baseline.Index != current.Index {
		m.logger.Info("Physical interface index changed",
			logger.String("interface", current.Name),
			logger.Int("old_index", baseline.Index),
			logger.Int("new_index", current.Index))
		return true
	}

	return false
}

/*****************************************************************************
 * NAME: handlePhysicalInterfaceChange
 *
 * DESCRIPTION:
 *     Handles physical interface changes by removing old routes, updating
 *     interface information, and adding new routes
 *
 * PARAMETERS:
 *     newInterface - New physical interface information
 *
 * RETURNS:
 *     error - Error if handling fails
 *****************************************************************************/
func (m *Manager) handlePhysicalInterfaceChange(newInterface *PhysicalInterfaceInfo) error {
	m.logger.Info("Handling physical interface change",
		logger.String("old_interface", m.physicalIfaceInfo.Name),
		logger.String("old_gateway", m.physicalIfaceInfo.Gateway.String()),
		logger.String("old_ip", m.physicalIfaceInfo.LocalIP.String()),
		logger.String("new_interface", newInterface.Name),
		logger.String("new_gateway", newInterface.Gateway.String()),
		logger.String("new_ip", newInterface.LocalIP.String()))

	// Save old physical interface information for deleting old routes
	oldInterface := m.physicalIfaceInfo

	// Get server list
	//m.mutex.RLock()
	servers := make([]Server, len(m.servers))
	copy(servers, m.servers)
	//m.mutex.RUnlock()

	if len(servers) > 0 {
		m.logger.Info("Removing old server routes with old network configuration",
			logger.Int("server_count", len(servers)))

		// 1. First use old physical interface information to delete all old routes
		m.removeServerRoutesWithInterface(oldInterface)
	}

	// 2. Update physical interface information
	m.logger.Info("Updating physical interface information")
	m.physicalIfaceInfo = newInterface
	m.physicalGateway = newInterface.Gateway

	// Create new NetworkInterface abstraction
	newNetInterface, err := network.NewNetworkInterface(newInterface.LUID, newInterface.Name, m.logger)
	if err != nil {
		m.logger.Error("Failed to create new network interface", logger.ErrorField(err))
		return fmt.Errorf("failed to create new network interface: %w", err)
	}
	m.physicalNetInterface = newNetInterface

	if len(servers) > 0 {
		// 3. Add new server routes using new physical interface information
		m.logger.Info("Adding server routes with new network configuration",
			logger.Int("server_count", len(servers)))
		m.addServerRoutes(servers)
	}

	// 4. Notify network change callbacks
	m.notifyNetworkChange()

	m.logger.Info("Physical interface change handling completed successfully")
	return nil
}
