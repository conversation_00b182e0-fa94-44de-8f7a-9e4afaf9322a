/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      server_route_manager.go
 *
 * DESCRIPTION :    Server route management functionality for VPN server routes
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package server

import (
	"fmt"
	"net"
	"net/netip"
	"strings"

	"mobile/internal/common/logger"
	"mobile/internal/platform/network"
)

/*****************************************************************************
 * NAME: addServerRoutes
 *
 * DESCRIPTION:
 *     Adds routes for all servers to physical interface to ensure proper routing
 *
 * PARAMETERS:
 *     servers - List of servers to add routes for
 *****************************************************************************/
func (m *Manager) addServerRoutes(servers []Server) {
	m.logger.Info("Adding server routes to physical interface", logger.Int("server_count", len(servers)))

	// Check if physical interface information is initialized
	if m.physicalIfaceInfo == nil {
		m.logger.Error("Physical interface not initialized, cannot add server routes")
		return
	}

	//m.routeMutex.Lock()
	//defer m.routeMutex.Unlock()

	// First clean existing server routes (except default routes)
	m.logger.Info("Cleaning existing server routes before adding new ones")
	m.cleanExistingServerRoutes()

	// Clear old route records
	m.serverRoutes = make(map[string][]net.IP)

	// Add route for each server, using saved physical interface information
	for _, server := range servers {
		m.addServerRoute(server, m.physicalIfaceInfo.Gateway)
	}

	m.logger.Info("Server routes added successfully",
		logger.Int("total_servers", len(servers)),
		logger.String("physical_gateway", m.physicalIfaceInfo.Gateway.String()),
		logger.Uint64("physical_interface_luid", m.physicalIfaceInfo.LUID))
}

/*****************************************************************************
 * NAME: addServerRoute
 *
 * DESCRIPTION:
 *     Adds route for single server to ensure traffic goes through physical interface
 *
 * PARAMETERS:
 *     server          - Server to add route for
 *     physicalGateway - Physical gateway IP address
 *****************************************************************************/
func (m *Manager) addServerRoute(server Server, physicalGateway net.IP) {
	// Resolve server address
	ips, err := net.LookupIP(server.ServerName)
	if err != nil {
		m.logger.Warn("Failed to resolve server address",
			logger.String("server", server.Name),
			logger.String("address", server.ServerName),
			logger.ErrorField(err))
		return
	}

	if len(ips) == 0 {
		m.logger.Warn("No IP addresses found for server",
			logger.String("server", server.Name),
			logger.String("address", server.ServerName))
		return
	}

	// Record server IP addresses
	var serverIPs []net.IP
	for _, ip := range ips {
		// Only handle IPv4 addresses
		if ip.To4() != nil {
			serverIPs = append(serverIPs, ip)
		}
	}

	if len(serverIPs) == 0 {
		m.logger.Warn("No IPv4 addresses found for server",
			logger.String("server", server.Name),
			logger.String("address", server.ServerName))
		return
	}

	// Save server IP addresses
	m.serverRoutes[server.ID] = serverIPs

	// Add route for each IP address
	for _, serverIP := range serverIPs {
		// Create route network for server IP (using /32 mask, i.e., single IP)
		serverRoute := net.IPNet{
			IP:   serverIP,
			Mask: net.CIDRMask(32, 32),
		}

		// Directly use NetworkInterface to add route
		err := m.addServerRouteToInterface(serverRoute, physicalGateway)
		if err != nil {
			m.logger.Warn("Failed to add server route",
				logger.String("server", server.Name),
				logger.String("server_ip", serverIP.String()),
				logger.String("gateway", physicalGateway.String()),
				logger.ErrorField(err))
		} else {
			m.logger.Debug("Server route added successfully",
				logger.String("server", server.Name),
				logger.String("server_ip", serverIP.String()),
				logger.String("gateway", physicalGateway.String()),
				logger.Int("metric", 5))
		}
	}
}

/*****************************************************************************
 * NAME: addServerRouteToInterface
 *
 * DESCRIPTION:
 *     Adds server route to physical interface using NetworkInterface abstraction
 *
 * PARAMETERS:
 *     destination - Destination network for the route
 *     gateway     - Gateway IP address for the route
 *
 * RETURNS:
 *     error - Error if route addition fails
 *****************************************************************************/
func (m *Manager) addServerRouteToInterface(destination net.IPNet, gateway net.IP) error {
	if m.physicalNetInterface == nil {
		return fmt.Errorf("physical network interface not available")
	}

	// Convert net.IPNet to netip.Prefix
	ones, _ := destination.Mask.Size()
	destPrefix, err := netip.ParsePrefix(fmt.Sprintf("%s/%d", destination.IP.String(), ones))
	if err != nil {
		return fmt.Errorf("failed to parse destination prefix: %w", err)
	}

	// Convert net.IP to netip.Addr
	gatewayAddr, err := netip.ParseAddr(gateway.String())
	if err != nil {
		return fmt.Errorf("failed to parse gateway address: %w", err)
	}

	// Add route using NetworkInterface with metric 5
	err = m.physicalNetInterface.AddRoute(destPrefix, gatewayAddr, 5)
	if err == nil {
		m.logger.Debug("Server route added successfully",
			logger.String("destination", destination.String()),
			logger.String("gateway", gateway.String()))
		return nil
	}

	// Check if it's "object already exists" error
	if strings.Contains(err.Error(), "object already exists") ||
		strings.Contains(err.Error(), "The object already exists") ||
		strings.Contains(err.Error(), "already exists") {
		m.logger.Info("Server route already exists, continuing",
			logger.String("destination", destination.String()),
			logger.String("gateway", gateway.String()))
		return nil
	}

	m.logger.Error("Failed to add server route",
		logger.String("destination", destination.String()),
		logger.String("gateway", gateway.String()),
		logger.ErrorField(err))
	return fmt.Errorf("failed to add server route: %w", err)
}

/*****************************************************************************
 * NAME: removeServerRoutes
 *
 * DESCRIPTION:
 *     Removes all server routes from physical interface, with bulk cleanup
 *     fallback to individual route removal if needed
 *****************************************************************************/
func (m *Manager) removeServerRoutes() {
	m.logger.Info("Removing all server routes")

	// Check if physical interface information is initialized
	if m.physicalIfaceInfo == nil {
		m.logger.Error("Physical interface not initialized, cannot remove server routes")
		// Try to reinitialize physical interface information for cleanup
		if err := m.initializePhysicalInterface(); err != nil {
			m.logger.Error("Failed to reinitialize physical interface for cleanup", logger.ErrorField(err))
			return
		}
		m.logger.Info("Physical interface reinitialized for route cleanup")
	}

	//m.routeMutex.Lock()
	//defer m.routeMutex.Unlock()

	// Always use individual route removal to avoid deleting system default routes
	// FlushRoutes() would delete ALL routes on the interface, including 0.0.0.0/0 default route
	// which causes network disconnection. We only want to remove specific VPN server routes.
	m.removeServerRoutesIndividually()
}

/*****************************************************************************
 * NAME: removeServerRoutesIndividually
 *
 * DESCRIPTION:
 *     Removes server routes individually using NetworkInterface abstraction.
 *     This is a fallback method when bulk cleanup fails.
 *****************************************************************************/
func (m *Manager) removeServerRoutesIndividually() {
	if m.physicalNetInterface == nil {
		m.logger.Error("Physical network interface not available for individual route removal")
		return
	}

	m.logger.Info("Removing server routes individually using NetworkInterface")

	// Statistics for cleanup results
	totalRoutes := 0
	successCount := 0

	// Remove all recorded server routes, directly using NetworkInterface
	for serverID, serverIPs := range m.serverRoutes {
		for _, serverIP := range serverIPs {
			totalRoutes++

			// Convert to netip format
			destPrefix, err := netip.ParsePrefix(fmt.Sprintf("%s/32", serverIP.String()))
			if err != nil {
				m.logger.Warn("Failed to parse server IP for route removal",
					logger.String("server_id", serverID),
					logger.String("server_ip", serverIP.String()),
					logger.ErrorField(err))
				continue
			}

			gatewayAddr, err := netip.ParseAddr(m.physicalIfaceInfo.Gateway.String())
			if err != nil {
				m.logger.Warn("Failed to parse gateway for route removal",
					logger.String("gateway", m.physicalIfaceInfo.Gateway.String()),
					logger.ErrorField(err))
				continue
			}

			// Directly use NetworkInterface to delete route
			err = m.physicalNetInterface.DeleteRoute(destPrefix, gatewayAddr)
			if err != nil {
				// Check if it's "not found" error
				if strings.Contains(err.Error(), "not found") ||
					strings.Contains(err.Error(), "element not found") ||
					strings.Contains(err.Error(), "The system cannot find") {
					m.logger.Debug("Server route not found, already removed",
						logger.String("server_id", serverID),
						logger.String("server_ip", serverIP.String()))
					successCount++ // Consider as success, because route already doesn't exist
				} else {
					m.logger.Warn("Failed to remove server route",
						logger.String("server_id", serverID),
						logger.String("server_ip", serverIP.String()),
						logger.String("gateway", m.physicalIfaceInfo.Gateway.String()),
						logger.ErrorField(err))
				}
			} else {
				successCount++
				m.logger.Debug("Server route removed successfully",
					logger.String("server_id", serverID),
					logger.String("server_ip", serverIP.String()),
					logger.String("gateway", m.physicalIfaceInfo.Gateway.String()))
			}
		}
	}

	// Clear route records
	m.serverRoutes = make(map[string][]net.IP)

	m.logger.Info("Individual server routes removal completed",
		logger.Int("total_routes", totalRoutes),
		logger.Int("success_count", successCount))
}

/*****************************************************************************
 * NAME: cleanExistingServerRoutes
 *
 * DESCRIPTION:
 *     Cleans existing server routes (except default routes) before adding
 *     new routes to prevent conflicts
 *****************************************************************************/
func (m *Manager) cleanExistingServerRoutes() {
	if m.physicalNetInterface == nil {
		m.logger.Warn("Physical network interface not available for existing route cleanup")
		return
	}

	// Remove all recorded server routes, directly using NetworkInterface
	for serverID, serverIPs := range m.serverRoutes {
		for _, serverIP := range serverIPs {
			// Convert to netip format
			destPrefix, err := netip.ParsePrefix(fmt.Sprintf("%s/32", serverIP.String()))
			if err != nil {
				m.logger.Debug("Failed to parse server IP for existing route cleanup",
					logger.String("server_id", serverID),
					logger.String("server_ip", serverIP.String()),
					logger.ErrorField(err))
				continue
			}

			gatewayAddr, err := netip.ParseAddr(m.physicalIfaceInfo.Gateway.String())
			if err != nil {
				m.logger.Debug("Failed to parse gateway for existing route cleanup",
					logger.String("gateway", m.physicalIfaceInfo.Gateway.String()),
					logger.ErrorField(err))
				continue
			}

			// Directly use NetworkInterface to delete route
			err = m.physicalNetInterface.DeleteRoute(destPrefix, gatewayAddr)
			if err != nil {
				m.logger.Debug("Failed to remove existing server route (may not exist)",
					logger.String("server_id", serverID),
					logger.String("server_ip", serverIP.String()),
					logger.String("gateway", m.physicalIfaceInfo.Gateway.String()),
					logger.ErrorField(err))
			} else {
				m.logger.Debug("Existing server route removed successfully",
					logger.String("server_id", serverID),
					logger.String("server_ip", serverIP.String()),
					logger.String("gateway", m.physicalIfaceInfo.Gateway.String()))
			}
		}
	}
}

/*****************************************************************************
 * NAME: removeServerRoutesWithInterface
 *
 * DESCRIPTION:
 *     Removes all server routes using specified physical interface information.
 *     Used when physical interface changes to clean up old routes.
 *
 * PARAMETERS:
 *     physicalInterface - Physical interface information to use for cleanup
 *****************************************************************************/
func (m *Manager) removeServerRoutesWithInterface(physicalInterface *PhysicalInterfaceInfo) {
	m.logger.Info("Removing server routes with specific interface",
		logger.String("interface", physicalInterface.Name),
		logger.String("gateway", physicalInterface.Gateway.String()),
		logger.Uint64("luid", physicalInterface.LUID))

	// Create temporary NetworkInterface for old interface
	tempNetInterface, err := network.NewNetworkInterface(physicalInterface.LUID, physicalInterface.Name, m.logger)
	if err != nil {
		m.logger.Error("Failed to create temporary network interface for old interface cleanup",
			logger.String("interface", physicalInterface.Name),
			logger.ErrorField(err))
		return
	}

	//m.routeMutex.Lock()
	//defer m.routeMutex.Unlock()

	// Statistics for cleanup results
	totalRoutes := 0
	successCount := 0

	// Remove all recorded server routes, using temporary NetworkInterface
	for serverID, serverIPs := range m.serverRoutes {
		for _, serverIP := range serverIPs {
			totalRoutes++

			// Convert to netip format
			destPrefix, err := netip.ParsePrefix(fmt.Sprintf("%s/32", serverIP.String()))
			if err != nil {
				m.logger.Warn("Failed to parse server IP for old interface route removal",
					logger.String("server_id", serverID),
					logger.String("server_ip", serverIP.String()),
					logger.ErrorField(err))
				continue
			}

			gatewayAddr, err := netip.ParseAddr(physicalInterface.Gateway.String())
			if err != nil {
				m.logger.Warn("Failed to parse gateway for old interface route removal",
					logger.String("gateway", physicalInterface.Gateway.String()),
					logger.ErrorField(err))
				continue
			}

			// Use temporary NetworkInterface to delete route
			err = tempNetInterface.DeleteRoute(destPrefix, gatewayAddr)
			if err != nil {
				// Check if it's "not found" error
				if strings.Contains(err.Error(), "not found") ||
					strings.Contains(err.Error(), "element not found") ||
					strings.Contains(err.Error(), "The system cannot find") {
					m.logger.Debug("Server route not found on old interface, already removed",
						logger.String("server_id", serverID),
						logger.String("server_ip", serverIP.String()))
					successCount++ // Consider as success, because route already doesn't exist
				} else {
					m.logger.Warn("Failed to remove server route from old interface",
						logger.String("server_id", serverID),
						logger.String("server_ip", serverIP.String()),
						logger.String("gateway", physicalInterface.Gateway.String()),
						logger.ErrorField(err))
				}
			} else {
				successCount++
				m.logger.Debug("Server route removed from old interface successfully",
					logger.String("server_id", serverID),
					logger.String("server_ip", serverIP.String()),
					logger.String("gateway", physicalInterface.Gateway.String()))
			}
		}
	}

	m.logger.Info("Server routes removal from old interface completed",
		logger.String("interface", physicalInterface.Name),
		logger.Int("total_routes", totalRoutes),
		logger.Int("success_count", successCount))
}
