/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      server_background_tasks.go
 *
 * DESCRIPTION :    Background task management functionality for VPN server manager
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package server

import (
	"fmt"
	"time"

	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: backgroundTask
 *
 * DESCRIPTION:
 *     Runs background tasks including server list updates, ping operations,
 *     and network monitoring. Handles failures with retry logic.
 *****************************************************************************/
func (m *Manager) backgroundTask() {
	// Record consecutive failure counts
	updateFailures := 0
	pingFailures := 0

	// Create mutex to prevent task overlap execution
	//var taskMutex sync.Mutex

	for {
		select {
		case <-m.updateTicker.C:
			// Use mutex to ensure tasks don't overlap
			// Originally was: if !taskMutex.TryLock() { ... continue }
			// Now we always execute the task

			// Execute in goroutine to avoid blocking ticker
			go func() {
				// Add panic capture
				defer func() {
					if r := recover(); r != nil {
						m.logger.Error("PANIC in server list update goroutine",
							logger.Any("panic", r))
						errors.LogPanic(m.logger, r)
					}
				}()

				// Update server list
				if err := m.UpdateServerList(); err != nil {
					updateFailures++
					m.logger.Error("Failed to update server list",
						logger.ErrorField(err),
						logger.Int("consecutive_failures", updateFailures))

					// If consecutive failures exceed 3 times, reduce log level to avoid large log files
					if updateFailures > 3 {
						m.logger.Warn("Multiple consecutive server list update failures, reducing log frequency")
					}
				} else {
					// Reset failure count
					updateFailures = 0
				}
			}()

		case <-m.pingTicker.C:
			// Use mutex to ensure tasks don't overlap
			// Originally was: if !taskMutex.TryLock() { ... continue }
			// Now we always execute the task

			// Execute in goroutine to avoid blocking ticker
			go func() {
				// Add panic capture
				defer func() {
					if r := recover(); r != nil {
						m.logger.Error("PANIC in ping servers goroutine",
							logger.Any("panic", r))
						errors.LogPanic(m.logger, r)
					}
				}()

				// Measure server latency
				if err := m.PingServers(); err != nil {
					pingFailures++
					m.logger.Error("Failed to ping servers",
						logger.ErrorField(err),
						logger.Int("consecutive_failures", pingFailures))

					// If consecutive failures exceed 3 times, reduce log level to avoid large log files
					if pingFailures > 3 {
						m.logger.Warn("Multiple consecutive ping failures, reducing log frequency")
					}
				} else {
					// Reset failure count
					pingFailures = 0

					// After successful ping, notify all callback functions
					m.notifyUpdateCallbacks()
				}
			}()

		case <-m.networkTicker.C:
			// Network interface monitoring task
			go func() {
				// Add panic capture
				defer func() {
					if r := recover(); r != nil {
						m.logger.Error("PANIC in network monitor goroutine",
							logger.Any("panic", r))
						errors.LogPanic(m.logger, r)
					}
				}()

				// Check network configuration changes
				if err := m.checkNetworkChanges(); err != nil {
					m.logger.Error("Failed to check network changes",
						logger.ErrorField(err))
				}
			}()

		case <-m.retryTicker.C:
			// Server list retry task
			go func() {
				// Add panic capture
				defer func() {
					if r := recover(); r != nil {
						m.logger.Error("PANIC in server list retry goroutine",
							logger.Any("panic", r))
						errors.LogPanic(m.logger, r)
					}
				}()

				// Try to get server list
				if err := m.retryServerListUpdate(); err != nil {
					m.logger.Debug("Server list retry failed, will continue retrying",
						logger.ErrorField(err))
				}
			}()

		case <-m.stopChan:
			// Stop task
			return
		}
	}
}

/*****************************************************************************
 * NAME: startServerListRetry
 *
 * DESCRIPTION:
 *     Starts server list retry mechanism with 30-second intervals
 *****************************************************************************/
func (m *Manager) startServerListRetry() {
	m.logger.Info("Starting server list retry mechanism")

	// Stop existing timer
	if m.retryTicker != nil {
		m.retryTicker.Stop()
	}

	// Recreate retry timer, retry every 30 seconds
	m.retryTicker = time.NewTicker(30 * time.Second)
}

/*****************************************************************************
 * NAME: stopServerListRetry
 *
 * DESCRIPTION:
 *     Stops server list retry mechanism and cleans up timer
 *****************************************************************************/
func (m *Manager) stopServerListRetry() {
	m.logger.Info("Stopping server list retry mechanism")

	if m.retryTicker != nil {
		m.retryTicker.Stop()
	}
}

/*****************************************************************************
 * NAME: retryServerListUpdate
 *
 * DESCRIPTION:
 *     Retries server list update and stops retry mechanism if successful
 *
 * RETURNS:
 *     error - Error if retry fails
 *****************************************************************************/
func (m *Manager) retryServerListUpdate() error {
	m.logger.Debug("Retrying server list update")

	// Try to update server list
	err := m.UpdateServerList()
	if err != nil {
		return fmt.Errorf("retry server list update failed: %w", err)
	}

	// If successful, stop retry mechanism
	if m.hasServerList {
		m.stopServerListRetry()
		m.logger.Info("Server list retry successful, stopping retry mechanism")
	}

	return nil
}
