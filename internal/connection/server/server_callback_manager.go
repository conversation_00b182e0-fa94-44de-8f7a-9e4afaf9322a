/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      server_callback_manager.go
 *
 * DESCRIPTION :    Callback management functionality for VPN server manager
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package server

import (
	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: RegisterUpdateCallback
 *
 * DESCRIPTION:
 *     Registers a callback function for server list updates
 *
 * PARAMETERS:
 *     callback - Callback function to register
 *****************************************************************************/
func (m *Manager) RegisterUpdateCallback(callback func([]Server)) {
	// m.mutex.Lock()
	// defer m.mutex.Unlock()
	m.updateCallbacks = append(m.updateCallbacks, callback)
}

/*****************************************************************************
 * NAME: RegisterStatusCallback
 *
 * DESCRIPTION:
 *     Registers a callback function for server status changes
 *
 * PARAMETERS:
 *     callback - Callback function to register
 *****************************************************************************/
func (m *Manager) RegisterStatusCallback(callback func(string, string)) {
	// m.mutex.Lock()
	// defer m.mutex.Unlock()
	m.statusCallbacks = append(m.statusCallbacks, callback)
}

/*****************************************************************************
 * NAME: RegisterNetworkChangeCallback
 *
 * DESCRIPTION:
 *     Registers a callback function for network configuration changes
 *
 * PARAMETERS:
 *     callback - Callback function to register
 *****************************************************************************/
func (m *Manager) RegisterNetworkChangeCallback(callback func()) {
	m.networkChangeCallbacks = append(m.networkChangeCallbacks, callback)
}

/*****************************************************************************
 * NAME: notifyUpdateCallbacks
 *
 * DESCRIPTION:
 *     Notifies all registered server list update callbacks with current server list
 *****************************************************************************/
func (m *Manager) notifyUpdateCallbacks() {
	servers := m.GetServers()
	for _, callback := range m.updateCallbacks {
		go func(cb func([]Server)) {
			defer func() {
				if r := recover(); r != nil {
					m.logger.Error("PANIC in update callback goroutine",
						logger.Any("panic", r))
					errors.LogPanic(m.logger, r)
				}
			}()
			cb(servers)
		}(callback)
	}
}

/*****************************************************************************
 * NAME: notifyStatusCallback
 *
 * DESCRIPTION:
 *     Notifies all registered server status change callbacks
 *
 * PARAMETERS:
 *     id     - Server ID that changed status
 *     status - New status of the server
 *****************************************************************************/
func (m *Manager) notifyStatusCallback(id string, status string) {
	for _, callback := range m.statusCallbacks {
		go func(cb func(string, string)) {
			defer func() {
				if r := recover(); r != nil {
					m.logger.Error("PANIC in status callback goroutine",
						logger.String("server_id", id),
						logger.String("status", status),
						logger.Any("panic", r))
					errors.LogPanic(m.logger, r)
				}
			}()
			cb(id, status)
		}(callback)
	}
}

/*****************************************************************************
 * NAME: notifyNetworkChange
 *
 * DESCRIPTION:
 *     Notifies all registered network change callbacks
 *****************************************************************************/
func (m *Manager) notifyNetworkChange() {
	for _, callback := range m.networkChangeCallbacks {
		go func(cb func()) {
			defer func() {
				if r := recover(); r != nil {
					m.logger.Error("PANIC in network change callback goroutine",
						logger.Any("panic", r))
					errors.LogPanic(m.logger, r)
				}
			}()
			cb()
		}(callback)
	}
}
