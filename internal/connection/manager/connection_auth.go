/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      connection_auth.go
 *
 * DESCRIPTION :    Connection authentication management
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package manager

import (
	"context"
	"fmt"
	"time"

	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
	"mobile/internal/protocol/tunnel"
)

/*****************************************************************************
 * NAME: authenticate
 *
 * DESCRIPTION:
 *     Perform authentication with the VPN server
 *
 * PARAMETERS:
 *     None (uses manager's config for credentials)
 *
 * RETURNS:
 *     error - Authentication error if failed
 *****************************************************************************/
func (m *Manager) authenticate() error {
	// Log current configuration username for debugging
	m.log.Info("Authenticating with credentials",
		logger.String("username", m.config.Username),
		logger.String("server", m.config.ServerAddress))

	// Create authentication packet
	openPacket, err := tunnel.CreateOpenPacket(
		m.config.Username, m.config.Password, m.config.MTU, uint8(m.encryptor.Method()))
	if err != nil {
		return fmt.Errorf("failed to create authentication packet: %w", err)
	}

	// Send authentication packet
	err = m.sendPacket(openPacket)
	if err != nil {
		return fmt.Errorf("failed to send authentication packet: %w", err)
	}

	// Create authentication timeout channel
	authCh := make(chan error, 1)
	authCtx, authCancel := context.WithTimeout(m.ctx, m.config.Timeout)
	defer authCancel()

	// Start authentication result listener goroutine
	_, err = m.goroutineManager.Start("auth-result-listener", func(ctx context.Context) error {
		// Wait for state to become data transmission or authentication failure
		for {
			state := m.stateMachine.GetState()
			if state == StateData {
				// Authentication successful
				authCh <- nil
				return nil
			} else if state == StateAuthFail {
				// Authentication failed
				authCh <- errors.NewWithCode(errors.CodeAuthError,
					"authentication failed: %s", m.stateMachine.GetStateString())
				return nil
			}

			// Check if context is cancelled
			select {
			case <-authCtx.Done():
				return nil
			case <-ctx.Done():
				return nil
			default:
				// Wait a moment before checking again
				time.Sleep(100 * time.Millisecond)
			}
		}
	})
	if err != nil {
		return fmt.Errorf("failed to start auth result listener: %w", err)
	}

	// Retry sending authentication packet
	retryTicker := time.NewTicker(m.config.RetryInterval)
	defer retryTicker.Stop()

	retryCount := 0
	for {
		select {
		case err := <-authCh:
			// Authentication completed
			return err

		case <-retryTicker.C:
			// Increment retry count
			retryCount++
			if retryCount >= m.config.RetryCount {
				// Exceeded maximum retry attempts
				return errors.NewWithCode(errors.CodeAuthError,
					"authentication failed after %d attempts", m.config.RetryCount)
			}

			// Resend authentication packet
			m.log.Info("Authentication retry",
				logger.Int("attempt", retryCount),
				logger.Int("max_attempts", m.config.RetryCount))

			err = m.sendPacket(openPacket)
			if err != nil {
				return fmt.Errorf("failed to send authentication packet: %w", err)
			}

		case <-authCtx.Done():
			// Timeout
			return fmt.Errorf("authentication timeout after %v", m.config.Timeout)
		}
	}
}
