/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      connection_routing.go
 *
 * DESCRIPTION :    Connection routing management
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package manager

import (
	"fmt"
	"net"
	"sort"
	"strings"

	"mobile/internal/common/logger"
	"mobile/internal/platform/tun"
)

/*****************************************************************************
 * NAME: GetRoutingSettings
 *
 * DESCRIPTION:
 *     Get current routing settings
 *
 * RETURNS:
 *     RoutingSettings - Current routing settings
 *****************************************************************************/
func (m *Manager) GetRoutingSettings() RoutingSettings {
	return m.routingSettings
}

/*****************************************************************************
 * NAME: SetRoutingSettings
 *
 * DESCRIPTION:
 *     Set routing settings, if TUN device is up and tunnel is in data state, apply new routing settings immediately
 *
 * PARAMETERS:
 *     settings - Routing settings to apply
 *
 * RETURNS:
 *     error - Error if setting fails
 *****************************************************************************/
func (m *Manager) SetRoutingSettings(settings RoutingSettings) error {
	// Save old routing settings for comparison
	oldSettings := m.routingSettings

	// Check if settings actually changed
	if m.isRoutingSettingsEqual(oldSettings, settings) {
		m.log.Info("Routing settings unchanged, skipping update",
			logger.String("mode", string(settings.Mode)),
			logger.String("custom_routes", settings.CustomRoutes))
		return nil
	}

	// Update routing settings
	m.routingSettings = settings
	m.log.Info("Routing settings updated",
		logger.String("old_mode", string(oldSettings.Mode)),
		logger.String("new_mode", string(settings.Mode)),
		logger.String("old_custom_routes", oldSettings.CustomRoutes),
		logger.String("new_custom_routes", settings.CustomRoutes))

	// Check if TUN device is up and tunnel is in data state
	if m.tunDevice != nil && m.stateMachine != nil && m.stateMachine.GetState() == StateData {
		m.log.Info("TUN device is up and tunnel is in data state, applying new routing settings immediately")

		// Get correct gateway IP - from config, not TUN device IP
		config := m.stateMachine.GetConfig()
		var gateway net.IP

		// Get gateway from config
		if gatewayStr, ok := config["gateway"]; ok && gatewayStr != "" {
			gateway = net.ParseIP(gatewayStr)
			m.log.Debug("Using gateway from config for routing update",
				logger.String("gateway", gatewayStr))
		} else {
			// If no explicit gateway, use TUN device IP network address + 1 as default gateway
			if len(m.tunDevice.Addresses()) == 0 {
				return fmt.Errorf("TUN device has no addresses")
			}
			tunIP := m.tunDevice.Addresses()[0].IP
			// Copy IP address
			defaultGateway := make(net.IP, len(tunIP))
			copy(defaultGateway, tunIP)
			// Set last byte to 1
			defaultGateway[len(defaultGateway)-1] = 1
			gateway = defaultGateway
			m.log.Debug("Using default gateway based on TUN IP for routing update",
				logger.String("tun_ip", tunIP.String()),
				logger.String("gateway", gateway.String()))
		}

		if gateway == nil {
			return fmt.Errorf("failed to determine gateway for routing update")
		}

		// Apply routing changes
		if err := m.applyRoutingChanges(oldSettings, settings, gateway); err != nil {
			m.log.Error("Failed to apply routing changes", logger.ErrorField(err))
			// Don't return error, as routing failure shouldn't cause entire config failure
		}
	} else {
		m.log.Info("TUN device is not up or tunnel is not in data state, routing settings will be applied on next connection")
	}

	return nil
}

/*****************************************************************************
 * NAME: isRoutingSettingsEqual
 *
 * DESCRIPTION:
 *     Compare two routing settings for equality
 *
 * PARAMETERS:
 *     old - Old routing settings
 *     new - New routing settings
 *
 * RETURNS:
 *     bool - True if settings are equal
 *****************************************************************************/
func (m *Manager) isRoutingSettingsEqual(old, new RoutingSettings) bool {
	// Compare routing mode
	if old.Mode != new.Mode {
		return false
	}

	// Normalize custom routes strings for comparison
	oldRoutes := m.normalizeCustomRoutes(old.CustomRoutes)
	newRoutes := m.normalizeCustomRoutes(new.CustomRoutes)

	return oldRoutes == newRoutes
}

/*****************************************************************************
 * NAME: normalizeCustomRoutes
 *
 * DESCRIPTION:
 *     Normalize custom routes string - remove whitespace, sort, ensure comparison consistency
 *
 * PARAMETERS:
 *     routes - Custom routes string to normalize
 *
 * RETURNS:
 *     string - Normalized routes string
 *****************************************************************************/
func (m *Manager) normalizeCustomRoutes(routes string) string {
	if routes == "" {
		return ""
	}

	// Split routes
	routeList := strings.Split(routes, ",")
	var validRoutes []string

	for _, route := range routeList {
		route = strings.TrimSpace(route)
		if route == "" {
			continue
		}

		// Validate CIDR format
		if _, _, err := net.ParseCIDR(route); err == nil {
			validRoutes = append(validRoutes, route)
		}
	}

	// Sort to ensure comparison consistency
	sort.Strings(validRoutes)

	return strings.Join(validRoutes, ",")
}

/*****************************************************************************
 * NAME: applyRoutingChanges
 *
 * DESCRIPTION:
 *     Apply routing changes
 *
 * PARAMETERS:
 *     oldSettings - Old routing settings
 *     newSettings - New routing settings
 *     gateway - Gateway IP address
 *
 * RETURNS:
 *     error - Error if application fails
 *****************************************************************************/
func (m *Manager) applyRoutingChanges(oldSettings, newSettings RoutingSettings, gateway net.IP) error {
	m.log.Info("Applying routing changes",
		logger.String("gateway", gateway.String()),
		logger.String("old_mode", string(oldSettings.Mode)),
		logger.String("new_mode", string(newSettings.Mode)))

	// 1. Remove old routes
	if err := m.removeOldRoutes(oldSettings, gateway); err != nil {
		m.log.Warn("Failed to remove old routes", logger.ErrorField(err))
		// Continue execution, don't return error
	}

	// 2. Add new routes
	if err := m.addNewRoutes(newSettings, gateway); err != nil {
		m.log.Warn("Failed to add new routes", logger.ErrorField(err))
		return err
	}

	m.log.Info("Routing changes applied successfully")
	return nil
}

/*****************************************************************************
 * NAME: removeOldRoutes
 *
 * DESCRIPTION:
 *     Remove old routes
 *
 * PARAMETERS:
 *     oldSettings - Old routing settings
 *     gateway - Gateway IP address
 *
 * RETURNS:
 *     error - Error if removal fails
 *****************************************************************************/
func (m *Manager) removeOldRoutes(oldSettings RoutingSettings, gateway net.IP) error {
	m.log.Info("Removing old routes",
		logger.String("mode", string(oldSettings.Mode)),
		logger.String("custom_routes", oldSettings.CustomRoutes))

	switch oldSettings.Mode {
	case RoutingModeAll:
		// Remove default route
		return m.removeDefaultRoute(gateway)

	case RoutingModeCustom:
		// Remove custom routes
		return m.removeCustomRoutes(oldSettings.CustomRoutes, gateway)

	default:
		m.log.Debug("No routes to remove for mode", logger.String("mode", string(oldSettings.Mode)))
		return nil
	}
}

/*****************************************************************************
 * NAME: addNewRoutes
 *
 * DESCRIPTION:
 *     Add new routes
 *
 * PARAMETERS:
 *     newSettings - New routing settings
 *     gateway - Gateway IP address
 *
 * RETURNS:
 *     error - Error if addition fails
 *****************************************************************************/
func (m *Manager) addNewRoutes(newSettings RoutingSettings, gateway net.IP) error {
	m.log.Info("Adding new routes",
		logger.String("mode", string(newSettings.Mode)),
		logger.String("custom_routes", newSettings.CustomRoutes))

	switch newSettings.Mode {
	case RoutingModeAll:
		// Add default route
		return m.addDefaultRoute(gateway)

	case RoutingModeCustom:
		// Add custom routes
		return m.addCustomRoutes(newSettings.CustomRoutes, gateway)

	default:
		m.log.Debug("No routes to add for mode", logger.String("mode", string(newSettings.Mode)))
		return nil
	}
}

/*****************************************************************************
 * NAME: removeDefaultRoute
 *
 * DESCRIPTION:
 *     Remove default route
 *
 * PARAMETERS:
 *     gateway - Gateway IP address
 *
 * RETURNS:
 *     error - Error if removal fails
 *****************************************************************************/
func (m *Manager) removeDefaultRoute(gateway net.IP) error {
	defaultRoute := net.IPNet{
		IP:   net.IPv4zero,
		Mask: net.CIDRMask(0, 32),
	}

	if err := tun.DeleteDeviceRoute(defaultRoute, gateway); err != nil {
		m.log.Warn("Failed to remove default route",
			logger.String("gateway", gateway.String()),
			logger.ErrorField(err))
		return err
	}

	m.log.Info("Default route removed successfully",
		logger.String("gateway", gateway.String()))
	return nil
}

/*****************************************************************************
 * NAME: addDefaultRoute
 *
 * DESCRIPTION:
 *     Add default route
 *
 * PARAMETERS:
 *     gateway - Gateway IP address
 *
 * RETURNS:
 *     error - Error if addition fails
 *****************************************************************************/
func (m *Manager) addDefaultRoute(gateway net.IP) error {
	defaultRoute := net.IPNet{
		IP:   net.IPv4zero,
		Mask: net.CIDRMask(0, 32),
	}

	if err := tun.AddDeviceRoute(defaultRoute, gateway, 10); err != nil {
		m.log.Warn("Failed to add default route",
			logger.String("gateway", gateway.String()),
			logger.Int("metric", 10),
			logger.ErrorField(err))
		return err
	}

	m.log.Info("Default route added successfully",
		logger.String("gateway", gateway.String()),
		logger.Int("metric", 10))
	return nil
}

/*****************************************************************************
 * NAME: removeCustomRoutes
 *
 * DESCRIPTION:
 *     Remove custom routes
 *
 * PARAMETERS:
 *     customRoutes - Custom routes string
 *     gateway - Gateway IP address
 *
 * RETURNS:
 *     error - Error if removal fails
 *****************************************************************************/
func (m *Manager) removeCustomRoutes(customRoutes string, gateway net.IP) error {
	if customRoutes == "" {
		m.log.Debug("No custom routes to remove")
		return nil
	}

	// Parse custom routes
	routes := m.parseCustomRoutes(customRoutes)
	if len(routes) == 0 {
		m.log.Debug("No valid custom routes to remove")
		return nil
	}

	var errors []string
	successCount := 0

	for _, route := range routes {
		if err := tun.DeleteDeviceRoute(route, gateway); err != nil {
			errorMsg := fmt.Sprintf("failed to remove route %s: %v", route.String(), err)
			errors = append(errors, errorMsg)
			m.log.Warn("Failed to remove custom route",
				logger.String("route", route.String()),
				logger.String("gateway", gateway.String()),
				logger.ErrorField(err))
		} else {
			successCount++
			m.log.Info("Custom route removed successfully",
				logger.String("route", route.String()),
				logger.String("gateway", gateway.String()))
		}
	}

	m.log.Info("Custom routes removal completed",
		logger.Int("total", len(routes)),
		logger.Int("success", successCount),
		logger.Int("failed", len(errors)))

	if len(errors) > 0 {
		return fmt.Errorf("failed to remove %d routes: %s", len(errors), strings.Join(errors, "; "))
	}

	return nil
}

/*****************************************************************************
 * NAME: addCustomRoutes
 *
 * DESCRIPTION:
 *     Add custom routes
 *
 * PARAMETERS:
 *     customRoutes - Custom routes string
 *     gateway - Gateway IP address
 *
 * RETURNS:
 *     error - Error if addition fails
 *****************************************************************************/
func (m *Manager) addCustomRoutes(customRoutes string, gateway net.IP) error {
	if customRoutes == "" {
		m.log.Debug("No custom routes to add")
		return nil
	}

	// Parse custom routes
	routes := m.parseCustomRoutes(customRoutes)
	if len(routes) == 0 {
		m.log.Warn("No valid custom routes to add")
		return fmt.Errorf("no valid routes found in custom routes: %s", customRoutes)
	}

	var errors []string
	successCount := 0

	for _, route := range routes {
		if err := tun.AddDeviceRoute(route, gateway, 10); err != nil {
			errorMsg := fmt.Sprintf("failed to add route %s: %v", route.String(), err)
			errors = append(errors, errorMsg)
			m.log.Warn("Failed to add custom route",
				logger.String("route", route.String()),
				logger.String("gateway", gateway.String()),
				logger.Int("metric", 10),
				logger.ErrorField(err))
		} else {
			successCount++
			m.log.Info("Custom route added successfully",
				logger.String("route", route.String()),
				logger.String("gateway", gateway.String()),
				logger.Int("metric", 10))
		}
	}

	m.log.Info("Custom routes addition completed",
		logger.Int("total", len(routes)),
		logger.Int("success", successCount),
		logger.Int("failed", len(errors)))

	if len(errors) > 0 {
		return fmt.Errorf("failed to add %d routes: %s", len(errors), strings.Join(errors, "; "))
	}

	return nil
}

/*****************************************************************************
 * NAME: parseCustomRoutes
 *
 * DESCRIPTION:
 *     Parse custom routes string, return list of valid network routes
 *
 * PARAMETERS:
 *     customRoutes - Custom routes string to parse
 *
 * RETURNS:
 *     []net.IPNet - List of valid network routes
 *****************************************************************************/
func (m *Manager) parseCustomRoutes(customRoutes string) []net.IPNet {
	if customRoutes == "" {
		return nil
	}

	// Split route string
	routeStrings := strings.Split(customRoutes, ",")
	var routes []net.IPNet

	for i, routeStr := range routeStrings {
		routeStr = strings.TrimSpace(routeStr)
		if routeStr == "" {
			continue
		}

		// Parse CIDR
		_, ipNet, err := net.ParseCIDR(routeStr)
		if err != nil {
			// Try to parse as IP address and add appropriate CIDR suffix
			ip := net.ParseIP(routeStr)
			if ip == nil {
				m.log.Warn("Invalid CIDR format and invalid IP address in custom routes",
					logger.Int("index", i),
					logger.String("route", routeStr),
					logger.ErrorField(err))
				continue
			}

			// Add appropriate CIDR suffix based on IP version
			var cidrStr string
			if ip.To4() != nil {
				// IPv4 address, add /32
				cidrStr = routeStr + "/32"
			} else {
				// IPv6 address, add /128
				cidrStr = routeStr + "/128"
			}

			// Parse the corrected CIDR
			_, ipNet, err = net.ParseCIDR(cidrStr)
			if err != nil {
				m.log.Warn("Failed to parse corrected CIDR in custom routes",
					logger.Int("index", i),
					logger.String("route", routeStr),
					logger.String("corrected_cidr", cidrStr),
					logger.ErrorField(err))
				continue
			}

			m.log.Info("Auto-corrected IP address to CIDR format",
				logger.Int("index", i),
				logger.String("original", routeStr),
				logger.String("corrected", cidrStr))
		}

		// Validate network address
		if ipNet.IP.To4() == nil {
			m.log.Warn("IPv6 routes not supported",
				logger.Int("index", i),
				logger.String("route", routeStr))
			continue
		}

		routes = append(routes, *ipNet)
		m.log.Debug("Parsed custom route",
			logger.Int("index", i),
			logger.String("route", routeStr),
			logger.String("network", ipNet.String()))
	}

	m.log.Info("Custom routes parsed",
		logger.String("input", customRoutes),
		logger.Int("total_input", len(routeStrings)),
		logger.Int("valid_routes", len(routes)))

	return routes
}
