/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      connection_tun.go
 *
 * DESCRIPTION :    Connection TUN device management
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package manager

import (
	"context"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
	"mobile/internal/platform/tun"
)

/*****************************************************************************
 * NAME: configureTunDevice
 *
 * DESCRIPTION:
 *     Configure TUN device with network settings
 *
 * PARAMETERS:
 *     config - Configuration map containing network settings
 *
 * RETURNS:
 *     error - Configuration error if failed
 *****************************************************************************/
func (m *Manager) configureTunDevice(config map[string]string) error {
	// Parse configuration
	var (
		ipStr      = config["ip"]
		netmaskStr = config["netmask"]
		gatewayStr = config["gateway"]
		dnsStr     = config["dns"]
		dns2Str    = config["dns2"]
		mtuStr     = config["mtu"]
	)

	// Log configuration
	m.log.Info("Configuring TUN device",
		logger.String("ip", ipStr),
		logger.String("netmask", netmaskStr),
		logger.String("gateway", gatewayStr),
		logger.String("dns", dnsStr),
		logger.String("mtu", mtuStr))

	// Parse IP address
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return errors.NewWithCode(errors.CodeConfigError,
			"invalid IP address: %s", ipStr)
	}

	// Parse subnet mask
	var netmask net.IP
	var prefixLen int

	if netmaskStr == "" {
		// If netmask is empty, use default /32 mask
		m.log.Warn("Netmask is empty, using default /32 netmask")
		netmask = net.ParseIP("***************")
		prefixLen = 32
	} else {
		netmask = net.ParseIP(netmaskStr)
		if netmask == nil {
			return errors.NewWithCode(errors.CodeConfigError,
				"invalid netmask: %s", netmaskStr)
		}
		// Calculate CIDR prefix length
		prefixLen, _ = net.IPMask(netmask.To4()).Size()
	}

	// Create IP network
	ipNet := net.IPNet{
		IP:   ip,
		Mask: net.CIDRMask(prefixLen, 32),
	}

	// Parse gateway
	var gateway net.IP

	gateway = net.ParseIP(gatewayStr)
	if gateway == nil {
		return errors.NewWithCode(errors.CodeConfigError,
			"invalid gateway: %s", gatewayStr)
	}

	// Parse DNS servers
	var dnsServers []net.IP

	// Parse primary DNS server
	if dnsStr != "" {
		dns := net.ParseIP(dnsStr)
		if dns != nil {
			dnsServers = append(dnsServers, dns)
			m.log.Info("Adding primary DNS server", logger.String("dns", dnsStr))
		}
	}

	// Parse secondary DNS server
	if dns2Str != "" {
		dns2 := net.ParseIP(dns2Str)
		if dns2 != nil {
			dnsServers = append(dnsServers, dns2)
			m.log.Info("Adding secondary DNS server", logger.String("dns2", dns2Str))
		}
	}

	// If DNS server list is empty, add default DNS servers
	if len(dnsServers) == 0 {
		m.log.Warn("No DNS servers specified, using default DNS servers")
		// Add Google DNS servers
		dnsServers = append(dnsServers, net.ParseIP("*******"))
		dnsServers = append(dnsServers, net.ParseIP("*******"))
		m.log.Info("Using default DNS servers",
			logger.String("primary", "*******"),
			logger.String("secondary", "*******"))
	}

	// Parse MTU
	mtu := 1420 // Default MTU
	if mtuStr != "" {
		parsedMTU, err := strconv.Atoi(mtuStr)
		if err == nil && parsedMTU > 0 {
			mtu = parsedMTU
		}
	}

	// Configure TUN device
	err := tun.ConfigureDevice(tun.DeviceConfig{
		Addresses: []net.IPNet{ipNet},
		MTU:       mtu,
	})
	if err != nil {
		return fmt.Errorf("failed to configure TUN device: %w", err)
	}

	// Set routing based on routing mode
	if m.routingSettings.Mode == RoutingModeCustom && m.routingSettings.CustomRoutes != "" {
		// Custom routing mode
		m.log.Info("Using custom routing mode",
			logger.String("custom_routes", m.routingSettings.CustomRoutes))

		// Parse custom routes
		routes := strings.Split(m.routingSettings.CustomRoutes, ",")
		for _, route := range routes {
			route = strings.TrimSpace(route)
			if route == "" {
				continue
			}

			// Parse CIDR
			_, ipNet, err := net.ParseCIDR(route)
			if err != nil {
				// Try to parse as IP address and add appropriate CIDR suffix
				ip := net.ParseIP(route)
				if ip == nil {
					m.log.Warn("Failed to parse custom route as CIDR or IP address",
						logger.String("route", route),
						logger.ErrorField(err))
					continue
				}

				// Add appropriate CIDR suffix based on IP version
				var cidrStr string
				if ip.To4() != nil {
					// IPv4 address, add /32
					cidrStr = route + "/32"
				} else {
					// IPv6 address, add /128
					cidrStr = route + "/128"
				}

				// Parse the corrected CIDR
				_, ipNet, err = net.ParseCIDR(cidrStr)
				if err != nil {
					m.log.Warn("Failed to parse corrected CIDR for custom route",
						logger.String("route", route),
						logger.String("corrected_cidr", cidrStr),
						logger.ErrorField(err))
					continue
				}

				m.log.Info("Auto-corrected IP address to CIDR format for custom route",
					logger.String("original", route),
					logger.String("corrected", cidrStr))
			}

			// Add custom route
			err = tun.AddDeviceRoute(*ipNet, gateway, 10)
			if err != nil {
				m.log.Warn("Failed to add custom route",
					logger.String("route", route),
					logger.String("gateway", gateway.String()),
					logger.Int("metric", 10),
					logger.ErrorField(err))
			} else {
				m.log.Info("Custom route added successfully",
					logger.String("route", route),
					logger.String("gateway", gateway.String()),
					logger.Int("metric", 10))
			}
		}
	} else {
		// Global routing mode (default)
		m.log.Info("Using global tunneling mode, routing all traffic through VPN")

		// Add default route, route all traffic to VPN gateway
		defaultRoute := net.IPNet{
			IP:   net.IPv4zero,
			Mask: net.CIDRMask(0, 32),
		}
		err = tun.AddDeviceRoute(defaultRoute, gateway, 10) // Use metric 10 for moderate priority
		if err != nil {
			m.log.Warn("Failed to add default route",
				logger.String("gateway", gateway.String()),
				logger.Int("metric", 10),
				logger.ErrorField(err))
			// Don't return error, routing failure shouldn't cause entire configuration to fail
		} else {
			m.log.Info("Default route added successfully",
				logger.String("gateway", gateway.String()),
				logger.Int("metric", 10))
		}
	}

	// Add route to gateway to ensure gateway is reachable
	gatewayRoute := net.IPNet{
		IP:   gateway,
		Mask: net.CIDRMask(32, 32),
	}
	err = tun.AddDeviceRoute(gatewayRoute, ip, 1) // Use TUN device IP as next hop
	if err != nil {
		m.log.Warn("Failed to add gateway route",
			logger.String("gateway", gateway.String()),
			logger.String("next_hop", ip.String()),
			logger.ErrorField(err))
		// Don't return error
	} else {
		m.log.Info("Gateway route added successfully",
			logger.String("gateway", gateway.String()),
			logger.String("next_hop", ip.String()),
			logger.Int("metric", 1))
	}

	// Set DNS servers
	if len(dnsServers) > 0 {
		err = tun.SetDeviceDNS(dnsServers)
		if err != nil {
			m.log.Warn("Failed to set DNS servers", logger.ErrorField(err))
			// Don't return error, DNS setting failure shouldn't cause entire configuration to fail
		}
	}

	return nil
}

/*****************************************************************************
 * NAME: StartTunReader
 *
 * DESCRIPTION:
 *     Start TUN device reader goroutine
 *
 * RETURNS:
 *     error - Start error if failed
 *****************************************************************************/
func (m *Manager) StartTunReader() error {
	if m.tunDevice == nil {
		return fmt.Errorf("TUN device not available, this is a critical error")
	}

	if m.stateMachine == nil || m.stateMachine.GetState() != StateData {
		return fmt.Errorf("not connected")
	}

	// Start TUN device reader goroutine
	_, err := m.goroutineManager.Start("tun-reader-manual", func(ctx context.Context) error {
		m.runTunReader(ctx)
		return nil
	})
	if err != nil {
		return fmt.Errorf("failed to start TUN reader: %w", err)
	}

	m.log.Info("TUN reader started")
	return nil
}

/*****************************************************************************
 * NAME: runTunReader
 *
 * DESCRIPTION:
 *     Run TUN device reader goroutine
 *
 * PARAMETERS:
 *     ctx - Context for cancellation
 *
 * RETURNS:
 *     error - Runtime error if failed
 *****************************************************************************/
func (m *Manager) runTunReader(ctx context.Context) error {
	if m.tunDevice == nil {
		return fmt.Errorf("TUN device not available, this is a critical error")
	}

	if m.stateMachine == nil || m.stateMachine.GetState() != StateData {
		return fmt.Errorf("not connected")
	}

	// Buffer size explanation:
	// - Standard Ethernet MTU: 1500 bytes
	// - VPN typical MTU: 1420 bytes
	// - Add some headroom for headers: 1500 + 100 = 1600 bytes
	// - Round up to power of 4 for better memory alignment: 4096 bytes
	buffer := make([]byte, 4096)

	// Get TUN device subnet mask for broadcast address filtering
	var subnetMask net.IPMask
	if m.stateMachine != nil {
		config := m.stateMachine.GetConfig()
		if netmaskStr, ok := config["netmask"]; ok && netmaskStr != "" {
			netmask := net.ParseIP(netmaskStr)
			if netmask != nil {
				subnetMask = net.IPMask(netmask.To4())
			}
		}
	}

	// If no subnet mask obtained, use default /32 mask
	if subnetMask == nil {
		subnetMask = net.CIDRMask(32, 32)
		m.log.Debug("Using default /32 subnet mask for broadcast filtering")
	}

	for {
		// Check if context is cancelled
		select {
		case <-ctx.Done():
			m.log.Debug("TUN reader context cancelled, stopping")
			return nil
		default:
			// Continue
		}

		// Check connection state, exit if disconnected
		if m.stateMachine == nil || m.stateMachine.GetState() != StateData || m.conn == nil {
			m.log.Debug("Connection state changed, TUN reader stopping")
			return nil
		}

		// Read data from TUN device
		n, err := m.tunDevice.Read(buffer)

		if err != nil {
			// Since we've modified TUN device Read method to not return "No more data is available" error
			// If we receive error here, it means other more serious errors
			m.log.Warn("Error reading from TUN device, will retry", logger.ErrorField(err))

			// Short sleep to avoid CPU spinning in error conditions
			time.Sleep(10 * time.Millisecond)
			continue
		}
		/*
			// Check if packet should be filtered using tunnel package implementation
			shouldFilter, _ := tunnel.ShouldFilterPacket(buffer[:n], subnetMask)
			if shouldFilter {
				// For broadcast and multicast packets, consider writing back to TUN device
				// Here we choose not to handle them as most cases don't need these packets through VPN tunnel

				// Local network and reserved address packets usually don't need VPN tunnel
				// Here we choose to drop them directly

				// Other types of filtered packets, drop directly

				// Skip subsequent processing
				continue
			}
		*/

		// Check connection state, exit if disconnected
		if m.stateMachine == nil || m.stateMachine.GetState() != StateData || m.conn == nil {
			m.log.Debug("Connection state changed, TUN reader stopping")
			return nil
		}

		// Send data directly from buffer
		err = m.SendData(buffer[:n])
		if err != nil {
			// Check if error is due to connection being closed
			if strings.Contains(err.Error(), "not connected") {
				m.log.Debug("Connection closed, TUN reader stopping")
				return nil
			}
			m.log.Error("Failed to send data from TUN device",
				logger.ErrorField(err),
				logger.Int("size", n))
		}
	}
}

/*****************************************************************************
 * NAME: ensureTunDeviceReady
 *
 * DESCRIPTION:
 *     Ensure TUN device is ready for use, wait up to timeout duration
 *
 * PARAMETERS:
 *     timeout - Maximum time to wait for TUN device to be ready
 *
 * RETURNS:
 *     error - Error if TUN device is not ready within timeout
 *****************************************************************************/
func (m *Manager) ensureTunDeviceReady(timeout time.Duration) error {
	if m.tunDevice == nil {
		return fmt.Errorf("TUN device not available")
	}

	// If device is already up, return immediately
	if m.tunDevice.IsUp() {
		m.log.Debug("TUN device is already up")
		return nil
	}

	// Wait for device to be ready, up to timeout duration
	m.log.Info("Waiting for TUN device to be ready",
		logger.Duration("timeout", timeout))

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	ticker := time.NewTicker(100 * time.Millisecond) // Check every 100ms
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("timeout waiting for TUN device to be ready after %v", timeout)
		case <-ticker.C:
			if m.tunDevice.IsUp() {
				m.log.Info("TUN device is now ready")
				return nil
			}
			m.log.Debug("TUN device not ready yet, continuing to wait...")
		}
	}
}

/*****************************************************************************
 * NAME: cleanupTunDeviceConfiguration
 *
 * DESCRIPTION:
 *     Clean up TUN device configuration including routes and DNS settings
 *****************************************************************************/
func (m *Manager) cleanupTunDeviceConfiguration() {
	if m.tunDevice == nil {
		m.log.Debug("No TUN device to cleanup")
		return
	}

	m.log.Info("Cleaning up TUN device configuration (flush IP, DNS, routes)")

	// Use TUN package's FlushDeviceConfiguration for efficient cleanup
	err := tun.FlushDeviceConfiguration()
	if err != nil {
		m.log.Error("Failed to flush TUN device configuration", logger.ErrorField(err))

		// Fallback: try individual cleanup if bulk cleanup fails
		m.log.Info("Attempting fallback cleanup with individual operations")
		m.fallbackCleanupTunDevice()
	} else {
		m.log.Info("TUN device configuration flushed successfully")
	}

	m.log.Info("TUN device configuration cleanup completed")
}

/*****************************************************************************
 * NAME: fallbackCleanupTunDevice
 *
 * DESCRIPTION:
 *     Fallback cleanup method using individual operations when bulk cleanup fails
 *****************************************************************************/
func (m *Manager) fallbackCleanupTunDevice() {
	if m.stateMachine == nil {
		m.log.Debug("No state machine available for fallback cleanup")
		return
	}

	// Get configuration from state machine for route cleanup
	var gateway net.IP
	config := m.stateMachine.GetConfig()
	if gatewayStr, ok := config["gateway"]; ok && gatewayStr != "" {
		gateway = net.ParseIP(gatewayStr)
		m.log.Debug("Using gateway from config for fallback cleanup",
			logger.String("gateway", gatewayStr))
	}

	// Clean up routes based on current routing settings
	if gateway != nil {
		m.log.Info("Cleaning up routes using individual operations",
			logger.String("mode", string(m.routingSettings.Mode)),
			logger.String("custom_routes", m.routingSettings.CustomRoutes),
			logger.String("gateway", gateway.String()))

		// Remove routes based on routing mode
		if m.routingSettings.Mode == RoutingModeCustom && m.routingSettings.CustomRoutes != "" {
			// Remove custom routes
			routes := strings.Split(m.routingSettings.CustomRoutes, ",")
			for _, route := range routes {
				route = strings.TrimSpace(route)
				if route == "" {
					continue
				}

				_, ipNet, err := net.ParseCIDR(route)
				if err != nil {
					// Try to parse as IP address and add appropriate CIDR suffix
					ip := net.ParseIP(route)
					if ip == nil {
						m.log.Warn("Failed to parse custom route for cleanup as CIDR or IP address",
							logger.String("route", route),
							logger.ErrorField(err))
						continue
					}

					// Add appropriate CIDR suffix based on IP version
					var cidrStr string
					if ip.To4() != nil {
						// IPv4 address, add /32
						cidrStr = route + "/32"
					} else {
						// IPv6 address, add /128
						cidrStr = route + "/128"
					}

					// Parse the corrected CIDR
					_, ipNet, err = net.ParseCIDR(cidrStr)
					if err != nil {
						m.log.Warn("Failed to parse corrected CIDR for custom route cleanup",
							logger.String("route", route),
							logger.String("corrected_cidr", cidrStr),
							logger.ErrorField(err))
						continue
					}

					m.log.Debug("Auto-corrected IP address to CIDR format for cleanup",
						logger.String("original", route),
						logger.String("corrected", cidrStr))
				}

				err = tun.DeleteDeviceRoute(*ipNet, gateway)
				if err != nil {
					m.log.Warn("Failed to remove custom route",
						logger.String("route", route),
						logger.String("gateway", gateway.String()),
						logger.ErrorField(err))
				} else {
					m.log.Debug("Custom route removed successfully",
						logger.String("route", route),
						logger.String("gateway", gateway.String()))
				}
			}
		} else {
			// Remove default route
			defaultRoute := net.IPNet{
				IP:   net.IPv4zero,
				Mask: net.CIDRMask(0, 32),
			}
			err := tun.DeleteDeviceRoute(defaultRoute, gateway)
			if err != nil {
				m.log.Warn("Failed to remove default route",
					logger.String("gateway", gateway.String()),
					logger.ErrorField(err))
			} else {
				m.log.Debug("Default route removed successfully",
					logger.String("gateway", gateway.String()))
			}
		}
	}

	// Clear DNS settings
	err := tun.SetDeviceDNS(nil)
	if err != nil {
		m.log.Warn("Failed to clear DNS settings", logger.ErrorField(err))
	} else {
		m.log.Debug("DNS settings cleared successfully")
	}
}
