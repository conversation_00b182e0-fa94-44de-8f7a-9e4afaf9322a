/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      connection_interface.go
 *
 * DESCRIPTION :    Connection network interface information management
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package manager

import (
	"fmt"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: GetInterfaceInfo
 *
 * DESCRIPTION:
 *     Get network interface information for the connection.
 *     Returns cached interface info obtained during connect operation.
 *
 * RETURNS:
 *     *InterfaceInfo - Interface information structure
 *     error          - Error if unable to get interface info
 *****************************************************************************/
func (m *Manager) GetInterfaceInfo() (*InterfaceInfo, error) {
	m.log.Debug("ConnectionManager: GetInterfaceInfo called")

	// First, try to return cached interface information from connect
	m.mutex.RLock()
	cachedInfo := m.physicalIface
	m.mutex.RUnlock()

	if cachedInfo != nil {
		m.log.Debug("ConnectionManager: Returning cached interface information",
			logger.String("interface_name", cachedInfo.InterfaceName),
			logger.String("local_ip", cachedInfo.LocalIP),
			logger.String("tun_ip", cachedInfo.TunIP),
			logger.Int("interface_index", cachedInfo.InterfaceIndex),
			logger.Uint64("interface_luid", cachedInfo.InterfaceLUID))
		return cachedInfo, nil
	}

	m.log.Debug("ConnectionManager: No cached interface info, attempting fallback")

	// Fallback: Get TUN device IP address if available
	tunIP := ""
	if m.tunDevice != nil && len(m.tunDevice.Addresses()) > 0 {
		tunIP = m.tunDevice.Addresses()[0].IP.String()
		m.log.Debug("ConnectionManager: TUN device found", logger.String("tun_ip", tunIP))
	} else {
		m.log.Debug("ConnectionManager: No TUN device or addresses available")
	}

	// If we have tunnel IP, return basic info
	if tunIP != "" {
		basicInfo := &InterfaceInfo{
			InterfaceName:  "unknown",
			LocalIP:        "unknown",
			TunIP:          tunIP,
			InterfaceIndex: 0,
			InterfaceLUID:  0,
		}

		m.log.Debug("ConnectionManager: Returning basic interface information with tunnel IP",
			logger.String("tun_ip", tunIP))

		return basicInfo, nil
	}

	// No interface information available
	m.log.Debug("ConnectionManager: No interface information available")
	return nil, fmt.Errorf("no interface information available")
}

/*****************************************************************************
 * NAME: cacheInterfaceInfoOnConnect
 *
 * DESCRIPTION:
 *     Cache interface information upon successful VPN connection.
 *     Uses the physical interface info already determined during connect process
 *     and combines it with tunnel IP from OPENACK config.
 *
 * PARAMETERS:
 *     config - Configuration map from OPENACK packet containing tunnel IP
 *
 * RETURNS:
 *     error - Error if unable to cache interface info
 *****************************************************************************/
func (m *Manager) cacheInterfaceInfoOnConnect(config map[string]string) error {
	m.log.Info("Caching interface information on connect")

	// Get tunnel IP from OPENACK configuration
	tunIP := config["ip"]
	if tunIP == "" {
		m.log.Warn("No tunnel IP found in OPENACK configuration")
		tunIP = "unknown"
	}

	// Use the physical interface information already determined by ServerManager
	// during initialization. This is the interface that was used for the connection.
	var physicalInfo *InterfaceInfo
	if m.serverManager != nil {
		m.log.Debug("Using physical interface info from server manager (determined during connect)")
		physicalInterfaceInfo := m.serverManager.GetPhysicalInterfaceInfo()
		if physicalInterfaceInfo != nil {
			physicalInfo = &InterfaceInfo{
				InterfaceName:  physicalInterfaceInfo.Name,
				LocalIP:        physicalInterfaceInfo.LocalIP.String(),
				TunIP:          tunIP,
				InterfaceIndex: physicalInterfaceInfo.Index,
				InterfaceLUID:  physicalInterfaceInfo.LUID,
			}

			m.log.Info("Successfully cached interface information on connect",
				logger.String("interface_name", physicalInfo.InterfaceName),
				logger.String("local_ip", physicalInfo.LocalIP),
				logger.String("tun_ip", physicalInfo.TunIP),
				logger.Int("interface_index", physicalInfo.InterfaceIndex),
				logger.Uint64("interface_luid", physicalInfo.InterfaceLUID))
		} else {
			m.log.Warn("Server manager returned nil physical interface info")
		}
	} else {
		m.log.Warn("Server manager not available for physical interface info")
	}

	// If we couldn't get physical interface info, create basic info with tunnel IP only
	if physicalInfo == nil {
		physicalInfo = &InterfaceInfo{
			InterfaceName:  "unknown",
			LocalIP:        "unknown",
			TunIP:          tunIP,
			InterfaceIndex: 0,
			InterfaceLUID:  0,
		}

		m.log.Info("Using basic interface information with tunnel IP only",
			logger.String("tun_ip", tunIP))
	}

	// Cache the interface information for later retrieval
	m.mutex.Lock()
	m.physicalIface = physicalInfo
	m.mutex.Unlock()

	m.log.Info("Interface information cached successfully on connect")
	return nil
}
