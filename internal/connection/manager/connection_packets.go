/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      connection_packets.go
 *
 * DESCRIPTION :    Connection packet processing management
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package manager

import (
	"context"
	"fmt"
	"net"
	"strings"
	"sync/atomic"
	"time"

	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
	"mobile/internal/protocol/common"
	"mobile/internal/protocol/tunnel"
)

/*****************************************************************************
 * NAME: receivePackets
 *
 * DESCRIPTION:
 *     Receive packets from the network connection
 *
 * PARAMETERS:
 *     ctx - Context for cancellation
 *
 * RETURNS:
 *     error - Error if packet reception fails
 *****************************************************************************/
func (m *Manager) receivePackets(ctx context.Context) error {
	buffer := make([]byte, tunnel.MaxPacketSize)

	// Set read timeout to periodically check if context is cancelled
	if err := m.conn.SetReadDeadline(time.Now().Add(1 * time.Second)); err != nil {
		return fmt.Errorf("failed to set read deadline: %w", err)
	}

	for {
		// Check if context is cancelled
		select {
		case <-ctx.Done():
			return nil
		default:
			// Continue
		}

		// Read packet
		n, err := m.conn.Read(buffer)

		if err != nil {
			if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
				// Timeout, reset timeout and continue
				if err := m.conn.SetReadDeadline(time.Now().Add(1 * time.Second)); err != nil {
					return fmt.Errorf("failed to reset read deadline: %w", err)
				}
				continue
			}

			// Check if it's a connection closed error
			if strings.Contains(err.Error(), "use of closed network connection") ||
				strings.Contains(err.Error(), "connection closed") {
				m.log.Debug("Network connection closed, packet receiver stopping")
				return nil // Normal exit, don't set error state
			}

			// Other errors
			m.log.Error("Failed to read packet from network", logger.ErrorField(err))
			m.stateMachine.SetError(tunnel.ErrorNetworkError,
				"failed to read packet: "+err.Error())
			return err
		}

		// Update download traffic statistics
		m.updateDownloadStats(int64(n))

		// Update atomic counters
		atomic.AddUint64(&m.packetsReceived, 1)
		atomic.AddUint64(&m.bytesReceived, uint64(n))

		// Reset read timeout
		if err := m.conn.SetReadDeadline(time.Now().Add(1 * time.Second)); err != nil {
			return fmt.Errorf("failed to reset read deadline: %w", err)
		}

		// Parse packet
		packet, err := tunnel.ParsePacket(buffer[:n])
		if err != nil {
			m.log.Error("Failed to parse packet", logger.ErrorField(err))
			continue
		}

		// Verify packet signature
		if packet.NeedsSignatureVerification() {
			// Non-data packets need signature verification
			if len(packet.Data) >= 16 {
				// Extract signature
				signature := packet.Data[:16]

				// Verify signature
				if !tunnel.VerifySignature(packet, signature) {
					m.log.Error("Invalid packet signature",
						logger.Int("type", int(packet.Header.Type)),
						logger.String("type_name", getPacketTypeName(packet.Header.Type)),
						logger.String("packet", packet.String()),
						logger.String("signature_hex", fmt.Sprintf("%x", signature)))
					continue
				}
			} else {
				m.log.Error("Non-data packet too short for signature verification",
					logger.Int("type", int(packet.Header.Type)),
					logger.String("type_name", getPacketTypeName(packet.Header.Type)),
					logger.Int("length", len(packet.Data)))
				continue
			}
		}

		// Check session state
		if m.stateMachine.GetState() == StateData {
			// In data transmission state, check session ID and token
			sessionID, token := m.stateMachine.GetSessionInfo()
			if packet.Header.SID != sessionID || packet.Header.Token != token {
				m.log.Warn("Invalid session ID or token",
					logger.Int("expected_sid", int(sessionID)),
					logger.Int("received_sid", int(packet.Header.SID)),
					logger.Uint32("expected_token", token),
					logger.Uint32("received_token", packet.Header.Token))
				continue
			}
		}

		// Update last activity time
		m.stateMachine.UpdateHeartbeat()

		// Process packet based on type
		switch packet.Header.Type {
		case tunnel.PacketTypeData, tunnel.PacketTypeData6:
			// Handle normal data packets
			m.handleDataPacket(packet)

		case tunnel.PacketTypeIPFrag, tunnel.PacketTypeIPFrag6:
			// Handle fragment packets
			m.handleFragmentPacket(packet)

		case tunnel.PacketTypeDataEncrypt, tunnel.PacketTypeDataEncDup:
			// Handle encrypted data packets
			m.handleEncryptedDataPacket(packet)

		case tunnel.PacketTypeOpenAck:
			// Handle authentication success response
			err = m.stateMachine.HandlePacket(packet)
			if err != nil {
				m.log.Error("Failed to handle OpenAck packet", logger.ErrorField(err))
			}

		case tunnel.PacketTypeOpenReject:
			// Handle authentication failure response
			err = m.stateMachine.HandlePacket(packet)
			if err != nil {
				m.log.Error("Failed to handle OpenReject packet", logger.ErrorField(err))
			}

		case tunnel.PacketTypeEchoRequest:
			// Handle heartbeat request
			err = m.stateMachine.HandlePacket(packet)
			if err != nil {
				m.log.Error("Failed to handle EchoRequest packet", logger.ErrorField(err))
			}

		case tunnel.PacketTypeEchoResponse:
			// Handle heartbeat response
			err = m.stateMachine.HandlePacket(packet)
			if err != nil {
				m.log.Error("Failed to handle EchoResponse packet", logger.ErrorField(err))
			}

		case tunnel.PacketTypeClose:
			// Handle connection close request
			m.log.Info("Received Close packet, disconnecting")
			return nil // Normal exit, external will call Disconnect

		default:
			// Unknown packet type
			m.log.Warn("Unknown packet type",
				logger.Int("type", int(packet.Header.Type)),
				logger.String("packet", packet.String()))
		}
	}
}

/*****************************************************************************
 * NAME: handleDataPacket
 *
 * DESCRIPTION:
 *     Handle normal data packets
 *
 * PARAMETERS:
 *     packet - Data packet to handle
 *****************************************************************************/
func (m *Manager) handleDataPacket(packet *tunnel.Packet) {
	// Write data to TUN device
	if m.tunDevice != nil {
		// Check TUN device status
		if !m.tunDevice.IsUp() {
			m.log.Error("TUN device is not up, cannot write packet",
				logger.Int("size", len(packet.Data)),
				logger.Int("type", int(packet.Header.Type)))
			return
		}

		// Write to TUN device
		_, err := m.tunDevice.Write(packet.Data)
		if err != nil {
			m.log.Error("Failed to write packet to TUN device",
				logger.ErrorField(err),
				logger.Int("size", len(packet.Data)),
				logger.Int("type", int(packet.Header.Type)),
				logger.String("type_name", getPacketTypeName(packet.Header.Type)))
			return
		}

	}
}

/*****************************************************************************
 * NAME: handleFragmentPacket
 *
 * DESCRIPTION:
 *     Handle fragment data packets
 *
 * PARAMETERS:
 *     packet - Fragment packet to handle
 *****************************************************************************/
func (m *Manager) handleFragmentPacket(packet *tunnel.Packet) {
	// Parse fragment
	fragment, err := tunnel.ParseFragment(packet.Data)
	if err != nil {
		m.log.Error("Failed to parse fragment packet",
			logger.ErrorField(err),
			logger.Int("type", int(packet.Header.Type)),
			logger.String("type_name", getPacketTypeName(packet.Header.Type)),
			logger.Int("data_length", len(packet.Data)))
		return
	}

	// Add fragment and try to reassemble
	reassembled, complete := m.fragmentQueue.AddFragment(fragment)
	if complete {
		// Write reassembled data to TUN device
		if m.tunDevice != nil && m.tunDevice.IsUp() {
			_, err := m.tunDevice.Write(reassembled)
			if err != nil {
				m.log.Error("Failed to write reassembled packet to TUN device",
					logger.ErrorField(err),
					logger.Int("length", len(reassembled)))
			}
		}
	}
}

/*****************************************************************************
 * NAME: handleEncryptedDataPacket
 *
 * DESCRIPTION:
 *     Handle encrypted data packets
 *
 * PARAMETERS:
 *     packet - Encrypted packet to handle
 *****************************************************************************/
func (m *Manager) handleEncryptedDataPacket(packet *tunnel.Packet) {
	// Decrypt packet
	decryptedPacket, err := m.encryptor.DecryptPacket(packet)
	if err != nil {
		m.log.Error("Failed to decrypt packet", logger.ErrorField(err))
		return
	}

	// Handle decrypted packet
	m.handleDataPacket(decryptedPacket)
}

/*****************************************************************************
 * NAME: getPacketTypeName
 *
 * DESCRIPTION:
 *     Get string representation of packet type
 *
 * PARAMETERS:
 *     packetType - Packet type to convert
 *
 * RETURNS:
 *     string - String representation of packet type
 *****************************************************************************/
func getPacketTypeName(packetType common.PacketType) string {
	switch packetType {
	case common.PacketTypeOpenReject:
		return "OPENREJECT"
	case common.PacketTypeOpenAck:
		return "OPENACK"
	case common.PacketTypeOpen:
		return "OPEN"
	case common.PacketTypeData:
		return "DATA"
	case common.PacketTypeEchoRequest:
		return "ECHOREQUEST"
	case common.PacketTypeEchoResponse:
		return "ECHORESPONSE"
	case common.PacketTypeClose:
		return "CLOSE"
	case common.PacketTypeDataEncrypt:
		return "DATAENCRYPT"
	case common.PacketTypeDataDup:
		return "DATADUP"
	case common.PacketTypeDataEncDup:
		return "DATAENCDUP"
	case common.PacketTypeIPFrag:
		return "IPFRAG"
	case common.PacketTypeData6:
		return "DATA6"
	case common.PacketTypeIPFrag6:
		return "IPFRAG6"
	case common.PacketTypeSegRT:
		return "SEGRT"
	case common.PacketTypePingRequest:
		return "PINGREQUEST"
	case common.PacketTypePingResponse:
		return "PINGRESPONSE"
	default:
		return fmt.Sprintf("UNKNOWN(0x%02X)", packetType)
	}
}

/*****************************************************************************
 * NAME: registerPacketHandlers
 *
 * DESCRIPTION:
 *     Register packet handlers for different packet types
 *****************************************************************************/
func (m *Manager) registerPacketHandlers() {
	// Register heartbeat response handler
	m.stateMachine.RegisterPacketHandler(uint8(tunnel.PacketTypeEchoResponse),
		func(packet *tunnel.Packet) error {
			// Update heartbeat time
			m.stateMachine.UpdateHeartbeat()
			return nil
		})

	// Register heartbeat request handler
	m.stateMachine.RegisterPacketHandler(uint8(tunnel.PacketTypeEchoRequest),
		func(packet *tunnel.Packet) error {
			// Update heartbeat time
			m.stateMachine.UpdateHeartbeat()

			// Send heartbeat response
			sessionID, token := m.stateMachine.GetSessionInfo()
			responsePacket := tunnel.NewPacket(
				tunnel.PacketTypeEchoResponse,
				packet.Header.Encrypt,
				sessionID,
				token,
				packet.Data,
			)

			return m.sendPacket(responsePacket)
		})

	// Register close handler
	m.stateMachine.RegisterPacketHandler(uint8(tunnel.PacketTypeClose),
		func(packet *tunnel.Packet) error {
			// Disconnect
			m.Disconnect()
			return nil
		})

	// Register OpenAck handler
	m.stateMachine.RegisterPacketHandler(uint8(tunnel.PacketTypeOpenAck),
		func(packet *tunnel.Packet) error {
			// Only handle OpenAck in authentication state
			if m.stateMachine.GetState() != StateAuth {
				m.log.Warn("Received OpenAck in invalid state",
					logger.String("state", m.stateMachine.GetStateString()))
				return nil
			}

			// Log detailed packet information for debugging
			m.log.Debug("Received OpenAck packet",
				logger.String("packet", packet.String()),
				logger.Int("data_length", len(packet.Data)),
				logger.String("data_hex", fmt.Sprintf("%x", packet.Data)))

			// If packet length is sufficient, try to parse MD5 signature
			if len(packet.Data) >= 16 {
				m.log.Debug("OpenAck packet MD5 signature",
					logger.String("md5_hex", fmt.Sprintf("%x", packet.Data[:16])),
					logger.String("tlv_data_hex", fmt.Sprintf("%x", packet.Data[16:])))
			}

			// Parse authentication response
			result, err := tunnel.ParseOpenAckPacket(packet)
			if err != nil {
				m.log.Error("Failed to parse OpenAck packet",
					logger.ErrorField(err),
					logger.Int("data_length", len(packet.Data)),
					logger.String("data_hex", fmt.Sprintf("%x", packet.Data)))
				return fmt.Errorf("failed to parse authentication success response: %w", err)
			}

			// Log successfully parsed configuration information
			m.log.Info("Successfully parsed OpenAck packet",
				logger.Int("config_count", len(result.Config)))

			// Save session information
			m.stateMachine.SetSessionInfo(result.SessionID, result.Token)
			m.stateMachine.SetConfig(result.Config)

			// Cache interface information upon successful connection
			err = m.cacheInterfaceInfoOnConnect(result.Config)
			if err != nil {
				m.log.Warn("Failed to cache interface information on connect", logger.ErrorField(err))
				// Don't fail the connection for interface info caching failure
			}

			// Set state to data transmission
			m.stateMachine.SetState(StateData)

			// Notify status change
			status := m.GetStatus()
			m.notifyStatusChange(&status)

			return nil
		})

	// Register OpenReject handler
	m.stateMachine.RegisterPacketHandler(uint8(tunnel.PacketTypeOpenReject),
		func(packet *tunnel.Packet) error {
			// Only handle OpenReject in authentication state
			if m.stateMachine.GetState() != StateAuth {
				m.log.Warn("Received OpenReject in invalid state",
					logger.String("state", m.stateMachine.GetStateString()))
				return nil
			}

			// Parse authentication failure response
			result, err := tunnel.ParseOpenRejectPacket(packet)
			if err != nil {
				return fmt.Errorf("failed to parse authentication failure response: %w", err)
			}

			// Set state to authentication failure
			m.stateMachine.SetState(StateAuthFail)
			m.stateMachine.SetError(result.ErrorCode, result.ErrorMessage)

			// Notify status change
			status := m.GetStatus()
			m.notifyStatusChange(&status)

			return errors.NewWithCode(errors.CodeAuthError,
				"authentication rejected: %s", result.ErrorMessage)
		})
}
