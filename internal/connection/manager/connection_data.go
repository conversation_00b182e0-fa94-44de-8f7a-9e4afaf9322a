/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      connection_data.go
 *
 * DESCRIPTION :    Connection data transmission management
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package manager

import (
	"fmt"
	"sync/atomic"

	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
	"mobile/internal/protocol/common"
	"mobile/internal/protocol/encryption"
	"mobile/internal/protocol/tunnel"
)

/*****************************************************************************
 * NAME: SendData
 *
 * DESCRIPTION:
 *     Send data through the VPN connection
 *
 * PARAMETERS:
 *     data - Data bytes to send
 *
 * RETURNS:
 *     error - Send error if failed
 *****************************************************************************/
func (m *Manager) SendData(data []byte) error {
	// Check connection state and network connection
	if m.stateMachine == nil || m.stateMachine.GetState() != StateData {
		return fmt.Errorf("not connected")
	}

	// Check if connection is available
	if m.conn == nil {
		return fmt.Errorf("connection is closed")
	}

	// Create data packet
	sessionID, token := m.stateMachine.GetSessionInfo()
	var packetType common.PacketType
	if m.config.Encryption != encryption.MethodNone {
		packetType = common.PacketTypeDataEncrypt
	} else {
		packetType = common.PacketTypeData
	}

	// Optimization: directly use the passed data buffer to avoid extra memory allocation and copying
	packet := tunnel.NewPacket(packetType, common.EncryptionMethod(m.config.Encryption),
		sessionID, token, data)

	// Encrypt packet if needed
	if m.config.Encryption != encryption.MethodNone {
		encryptedPacket, err := m.encryptor.EncryptPacket(packet)
		if err != nil {
			m.log.Error("Failed to encrypt packet", logger.ErrorField(err))
			return fmt.Errorf("failed to encrypt packet: %w", err)
		}
		packet = encryptedPacket
	}

	// Get packet bytes
	packetData := packet.Bytes()

	// Send packet directly
	n, err := m.conn.Write(packetData)
	if err != nil {
		m.log.Error("Failed to send packet to network",
			logger.ErrorField(err),
			logger.Int("packet_size", len(packetData)))
		return fmt.Errorf("failed to send packet: %w", err)
	}

	// Update upload traffic statistics
	m.updateUploadStats(int64(n))

	// Update atomic counters
	atomic.AddUint64(&m.packetsSent, 1)
	atomic.AddUint64(&m.bytesSent, uint64(n))

	return nil
}

/*****************************************************************************
 * NAME: sendPacket
 *
 * DESCRIPTION:
 *     Send a packet through the connection
 *
 * PARAMETERS:
 *     packet - Packet to send
 *
 * RETURNS:
 *     error - Send error if failed
 *****************************************************************************/
func (m *Manager) sendPacket(packet *tunnel.Packet) error {
	// Check if connection is available
	if m.conn == nil {
		return fmt.Errorf("connection is closed")
	}

	// Get packet bytes
	data := packet.Bytes()

	// Send packet
	n, err := m.conn.Write(data)
	if err != nil {
		m.log.Error("Failed to send packet", logger.ErrorField(err))
		return fmt.Errorf("failed to send packet: %w", err)
	}

	// Update atomic counters (for performance monitoring)
	atomic.AddUint64(&m.packetsSent, 1)
	atomic.AddUint64(&m.bytesSent, uint64(n))

	return nil
}

/*****************************************************************************
 * NAME: sendPacketInternal
 *
 * DESCRIPTION:
 *     Internal packet sending with enhanced error handling
 *
 * PARAMETERS:
 *     packet - Packet to send
 *
 * RETURNS:
 *     error - Send error if failed
 *****************************************************************************/
func (m *Manager) sendPacketInternal(packet *tunnel.Packet) error {
	if m.conn == nil {
		return errors.NewWithCode(errors.CodeConnectionClosed,
			"connection is closed").(*errors.DetailedError).
			WithComponent("connection").WithOperation("send_packet")
	}

	// Get packet bytes
	data := packet.Bytes()

	// Send packet with error handling
	n, err := m.conn.Write(data)
	if err != nil {
		m.log.Error("Failed to send packet internally",
			logger.ErrorField(err),
			logger.Int("packet_size", len(data)),
			logger.String("packet_type", fmt.Sprintf("0x%02X", packet.Header.Type)))
		return errors.WrapWithCode(errors.CodeNetworkError, err,
			"failed to send packet").(*errors.DetailedError).
			WithComponent("connection").WithOperation("send_packet")
	}

	// Update atomic counters (for performance monitoring)
	atomic.AddUint64(&m.packetsSent, 1)
	atomic.AddUint64(&m.bytesSent, uint64(n))

	m.log.Debug("Packet sent successfully",
		logger.Int("bytes_sent", n),
		logger.String("packet_type", fmt.Sprintf("0x%02X", packet.Header.Type)))

	return nil
}
