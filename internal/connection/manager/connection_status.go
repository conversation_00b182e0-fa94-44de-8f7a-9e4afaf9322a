/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      connection_status.go
 *
 * DESCRIPTION :    Connection status management
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package manager

// No additional imports needed for this file

/*****************************************************************************
 * NAME: GetStatus
 *
 * DESCRIPTION:
 *     Get current connection status
 *
 * RETURNS:
 *     Status - Current connection status
 *****************************************************************************/
func (m *Manager) GetStatus() Status {
	if m.stateMachine == nil {
		return Status{
			State:       StateClosed,
			StateString: "CLOSED",
			Status:      "disconnected",
			Message:     "Not connected",
		}
	}

	sessionID, token := m.stateMachine.GetSessionInfo()
	status := Status{
		State:         m.stateMachine.GetState(),
		StateString:   m.stateMachine.GetStateString(),
		SessionID:     sessionID,
		Token:         token,
		LastHeartbeat: m.stateMachine.GetLastHeartbeat(),
		ConnectedTime: m.stateMachine.GetStateChangedTime(),
		Config:        m.stateMachine.GetConfig(),
		Server:        m.currentServer,
	}

	// Set API status based on state machine state
	switch m.stateMachine.GetState() {
	case StateInit, StateInit1, StateAuth:
		status.Status = "connecting"
		status.Message = "connecting"
	case StateData:
		status.Status = "connected"
		status.Message = "connected"
	case StateClosed:
		status.Status = "disconnected"
		status.Message = "disconnected"
	case StateAuthFail:
		// Authentication failure maps directly to error state, not through disconnected
		status.Status = "error"
		status.Message = "authentication_failed"
		if m.stateMachine.GetErrorMessage() != "" {
			status.Message = m.stateMachine.GetErrorMessage()
		}

	default:
		status.Status = "unknown"
		status.Message = "unknown_state"
	}

	return status
}

/*****************************************************************************
 * NAME: SetStatusNotifier
 *
 * DESCRIPTION:
 *     Set status notification handler
 *
 * PARAMETERS:
 *     notifier - Status notification interface
 *****************************************************************************/
func (m *Manager) SetStatusNotifier(notifier StatusNotifier) {
	m.statusNotifier = notifier

	// Send current status
	if notifier != nil {
		status := m.GetStatus()
		notifier.NotifyStatusChange(&status)
	}
}

/*****************************************************************************
 * NAME: notifyStatusChange
 *
 * DESCRIPTION:
 *     Notify status change to registered notifier
 *
 * PARAMETERS:
 *     status - New status to notify
 *****************************************************************************/
func (m *Manager) notifyStatusChange(status *Status) {
	notifier := m.statusNotifier

	if notifier != nil {
		notifier.NotifyStatusChange(status)
	}
}
