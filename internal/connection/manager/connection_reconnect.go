/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      connection_reconnect.go
 *
 * DESCRIPTION :    Connection auto-reconnect management
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package manager

import (
	"fmt"
	"time"

	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: EnableAutoReconnect
 *
 * DESCRIPTION:
 *     Enable auto-reconnect functionality
 *****************************************************************************/
func (m *Manager) EnableAutoReconnect() {
	m.autoReconnectEnabled = true
	m.log.Info("Auto-reconnect enabled")
}

/*****************************************************************************
 * NAME: DisableAutoReconnect
 *
 * DESCRIPTION:
 *     Disable auto-reconnect functionality
 *****************************************************************************/
func (m *Manager) DisableAutoReconnect() {
	m.autoReconnectEnabled = false
	m.log.Info("Auto-reconnect disabled")
}

/*****************************************************************************
 * NAME: handleNetworkChange
 *
 * DESCRIPTION:
 *     Handle network changes and notify UI for reconnect instead of auto-reconnect
 *****************************************************************************/
func (m *Manager) handleNetworkChange() {
	// Check if currently connected
	status := m.GetStatus()
	if status.Status != "connected" {
		m.log.Debug("VPN is not connected, skipping network change notification",
			logger.String("current_status", status.Status))
		return
	}

	m.log.Info("Network change detected, notifying UI for reconnect")

	// Notify UI about reconnect requirement instead of auto-reconnect
	if m.statusNotifier != nil {
		reason := "network_interface_change" // Use constant from api package
		message := "Network interface configuration has changed, reconnection required"
		m.statusNotifier.NotifyReconnectRequired(reason, message)
		m.log.Info("Sent reconnect required notification to UI",
			logger.String("reason", reason),
			logger.String("message", message))
		return
	}

	// Fallback to old auto-reconnect behavior if no status notifier
	m.log.Warn("No status notifier available, falling back to auto-reconnect")
	if !m.autoReconnectEnabled {
		m.log.Debug("Auto-reconnect is disabled, skipping network change handling")
		return
	}

	m.log.Info("Network change detected, performing auto-reconnect (fallback)")

	// Execute reconnect in new goroutine to avoid blocking network monitoring
	go func() {
		defer func() {
			if r := recover(); r != nil {
				m.log.Error("Panic during auto-reconnect",
					logger.Any("panic", r))
				errors.LogPanic(m.log, r)
			}
		}()

		// Increment reconnect attempt count
		m.autoReconnectAttempts++
		m.log.Info("Starting auto-reconnect attempt",
			logger.Int("attempt", m.autoReconnectAttempts))

		// Get current server information
		currentServer := m.GetCurrentServer()
		if currentServer == nil {
			m.log.Error("No current server information available for auto-reconnect")
			// Set error state
			if m.stateMachine != nil {
				m.stateMachine.SetError(0, "No server information available for auto-reconnect")
				m.stateMachine.SetState(StateClosed)
			}
			return
		}

		// Disconnect first
		if err := m.Disconnect(); err != nil {
			m.log.Error("Failed to disconnect during auto-reconnect",
				logger.ErrorField(err))
			// Set error state
			if m.stateMachine != nil {
				m.stateMachine.SetError(0, "Failed to disconnect: "+err.Error())
				m.stateMachine.SetState(StateClosed)
			}
			return
		}

		// Wait a moment to ensure disconnection is complete
		time.Sleep(2 * time.Second)

		// Reconnect to the same server
		if err := m.AuthAndConnect(currentServer.ServerName, currentServer.ServerPort); err != nil {
			m.log.Error("Auto-reconnect failed",
				logger.ErrorField(err),
				logger.Int("attempt", m.autoReconnectAttempts))
			// Set error state
			if m.stateMachine != nil {
				m.stateMachine.SetError(0, fmt.Sprintf("Auto-reconnect failed: %s", err.Error()))
				m.stateMachine.SetState(StateAuthFail)
			}
		} else {
			m.log.Info("Auto-reconnect completed successfully",
				logger.Int("attempt", m.autoReconnectAttempts))
			// Reset reconnect attempt count
			m.autoReconnectAttempts = 0
			// Connection successful, state will automatically become StateData, no need to set manually
		}
	}()
}
