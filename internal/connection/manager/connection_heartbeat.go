/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      connection_heartbeat.go
 *
 * DESCRIPTION :    Connection heartbeat management
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package manager

import (
	"context"
	"fmt"
	"time"

	"mobile/internal/common/logger"
	"mobile/internal/protocol/tunnel"
)

/*****************************************************************************
 * NAME: sendHeartbeats
 *
 * DESCRIPTION:
 *     Send heartbeat packets to maintain connection
 *
 * PARAMETERS:
 *     ctx - Context for cancellation
 *
 * RETURNS:
 *     error - Error if heartbeat sending fails
 *****************************************************************************/
func (m *Manager) sendHeartbeats(ctx context.Context) error {
	if m.heartbeatTicker == nil {
		m.heartbeatTicker = time.NewTicker(m.config.HeartbeatInterval)
	}

	defer m.heartbeatTicker.Stop()

	for {
		select {
		case <-ctx.Done():
			return nil
		case <-m.heartbeatTicker.C:
			// Check state
			if m.stateMachine.GetState() != StateData {
				continue
			}

			// Send heartbeat
			sessionID, token := m.stateMachine.GetSessionInfo()
			packet, err := tunnel.CreateEchoRequestPacket(
				sessionID, token, 0, 0, 0, uint8(m.encryptor.Method()))
			if err != nil {
				m.log.Error("Failed to create heartbeat packet", logger.ErrorField(err))
				continue
			}

			err = m.sendPacket(packet)
			if err != nil {
				m.log.Error("Failed to send heartbeat packet", logger.ErrorField(err))
			}
		}
	}
}

/*****************************************************************************
 * NAME: monitorHeartbeat
 *
 * DESCRIPTION:
 *     Monitor heartbeat timeout and trigger disconnect if needed
 *
 * PARAMETERS:
 *     ctx - Context for cancellation
 *
 * RETURNS:
 *     error - Error if monitoring fails
 *****************************************************************************/
func (m *Manager) monitorHeartbeat(ctx context.Context) error {
	// Heartbeat timeout: 3 times the heartbeat interval (45 seconds)
	heartbeatTimeout := m.config.HeartbeatInterval * 3

	// Check heartbeat status every 5 seconds
	checkInterval := 5 * time.Second
	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop()

	m.log.Info("Heartbeat monitor started",
		logger.Duration("heartbeat_interval", m.config.HeartbeatInterval),
		logger.Duration("timeout_threshold", heartbeatTimeout),
		logger.Duration("check_interval", checkInterval))

	for {
		select {
		case <-ctx.Done():
			m.log.Debug("Heartbeat monitor stopping due to context cancellation")
			return nil
		case <-ticker.C:
			// Only check heartbeat in data transmission state
			if m.stateMachine.GetState() != StateData {
				continue
			}

			// Check heartbeat timeout
			lastHeartbeat := m.stateMachine.GetLastHeartbeat()
			timeSinceLastHeartbeat := time.Since(lastHeartbeat)

			if timeSinceLastHeartbeat > heartbeatTimeout {
				m.log.Error("Heartbeat timeout detected",
					logger.Duration("time_since_last_heartbeat", timeSinceLastHeartbeat),
					logger.Duration("timeout_threshold", heartbeatTimeout),
					logger.Time("last_heartbeat", lastHeartbeat))

				// Notify UI about reconnect requirement instead of direct disconnect
				if m.statusNotifier != nil {
					reason := "heartbeat_timeout" // Use constant from api package
					message := fmt.Sprintf("heartbeat timeout: no response for %v", timeSinceLastHeartbeat)
					m.statusNotifier.NotifyReconnectRequired(reason, message)
					m.log.Info("Sent reconnect required notification to UI",
						logger.String("reason", reason),
						logger.String("message", message))
				} else {
					m.log.Warn("No status notifier available, falling back to direct disconnect")
					// Fallback: Set error state and disconnect
					m.stateMachine.SetError(tunnel.ErrorTimeout,
						fmt.Sprintf("heartbeat timeout: no response for %v", timeSinceLastHeartbeat))

					// Trigger disconnect
					go func() {
						m.log.Info("Triggering disconnect due to heartbeat timeout (fallback)")
						m.disconnectInternal()
					}()
				}

				return nil
			}

			// Log heartbeat status (debug mode only)
			m.log.Debug("Heartbeat status check",
				logger.Duration("time_since_last_heartbeat", timeSinceLastHeartbeat),
				logger.Duration("timeout_threshold", heartbeatTimeout),
				logger.Time("last_heartbeat", lastHeartbeat))
		}
	}
}
