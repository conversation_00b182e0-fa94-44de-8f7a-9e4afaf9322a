/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      authenticate.go
 *
 * DESCRIPTION :    Authentication functions for connection manager
 *
 * AUTHOR :         wei
 *
 * HISTORY :        2025-05-22  Initial implementation
 ******************************************************************************/

package manager

import (
	"context"
	"fmt"
	"net"
	"time"

	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
	"mobile/internal/protocol/encryption"
	"mobile/internal/protocol/tunnel"
)

// AuthResult 认证结果
type AuthResult struct {
	Success      bool              // 是否成功
	ErrorCode    int               // 错误码
	ErrorMessage string            // 错误消息
	SessionID    uint16            // 会话ID
	Token        uint32            // 认证令牌
	Config       map[string]string // 配置信息
}

/*****************************************************************************
 * NAME: Authenticate
 *
 * DESCRIPTION:
 *     单独进行认证，不建立连接
 *
 * PARAMETERS:
 *     username - 用户名
 *     password - 密码
 *     serverAddress - 服务器地址
 *     serverPort - 服务器端口（可选，如果为0则使用默认端口）
 *
 * RETURNS:
 *     *AuthResult - 认证结果
 *     error - 认证错误
 *****************************************************************************/
func (m *Manager) Authenticate(username, password, serverAddress string, serverPort int) (*AuthResult, error) {
	// 创建临时上下文
	ctx, cancel := context.WithTimeout(context.Background(), m.config.Timeout)
	defer cancel()

	m.config.Username = username
	m.config.Password = password

	// 检查连接管理器是否已启动
	if m.stateMachine == nil {
		return nil, fmt.Errorf("connection manager not started, call Start() first")
	}

	// 创建加密器
	encryptor, err := encryption.NewEncryptor(m.config.Encryption, username, password, m.log)
	if err != nil {
		return nil, fmt.Errorf("failed to create encryptor: %w", err)
	}

	// 如果端口为0，则使用默认端口
	port := serverPort
	if port == 0 {
		port = m.config.ServerPort
	}

	// Resolve server IP using ServerManager's IP cache before authentication
	var targetAddress string
	var useResolvedIP bool

	if m.serverManager != nil {
		if resolvedIP := m.serverManager.ResolveServerIP(serverAddress); resolvedIP != "" {
			targetAddress = resolvedIP
			useResolvedIP = true
			m.log.Info("Using resolved IP for authentication",
				logger.String("hostname", serverAddress),
				logger.String("resolved_ip", resolvedIP),
				logger.Bool("from_cache", true))
		} else {
			targetAddress = serverAddress
			useResolvedIP = false
			m.log.Info("Failed to resolve server IP, using hostname fallback",
				logger.String("hostname", serverAddress),
				logger.String("fallback_note", "DNS resolution will occur in network layer"))
		}
	} else {
		targetAddress = serverAddress
		useResolvedIP = false
		m.log.Info("ServerManager not available, using hostname directly",
			logger.String("hostname", serverAddress))
	}

	// 构建服务器地址
	serverAddr := net.JoinHostPort(targetAddress, fmt.Sprintf("%d", port))
	m.log.Info("Resolving server address for authentication",
		logger.String("target_address", targetAddress),
		logger.String("full_address", serverAddr),
		logger.Int("port", port),
		logger.Bool("use_resolved_ip", useResolvedIP),
		logger.String("fallback_note", func() string {
			if useResolvedIP {
				return "Using resolved IP"
			}
			return "Using hostname fallback - DNS resolution will occur in network layer"
		}()))

	// 解析服务器地址
	addr, err := net.ResolveUDPAddr("udp", serverAddr)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve server address: %w", err)
	}

	// 连接到服务器
	m.log.Info("Connecting to server for authentication", logger.String("address", addr.String()))
	conn, err := net.DialUDP("udp", nil, addr)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to server: %w", err)
	}
	defer conn.Close()

	// 创建认证数据包
	openPacket, err := tunnel.CreateOpenPacket(
		username, password, m.config.MTU, uint8(encryptor.Method()))
	if err != nil {
		return nil, fmt.Errorf("failed to create authentication packet: %w", err)
	}

	// 输出调试日志，显示数据包内容
	m.log.Debug("Authentication packet created",
		logger.String("packet", openPacket.String()),
		logger.String("hex", fmt.Sprintf("%x", openPacket.Bytes())),
		logger.Int("size", len(openPacket.Bytes())))

	// 发送认证数据包
	_, err = conn.Write(openPacket.Bytes())
	if err != nil {
		return nil, fmt.Errorf("failed to send authentication packet: %w", err)
	}

	// 创建结果通道
	resultCh := make(chan *AuthResult, 1)
	errCh := make(chan error, 1)

	// 启动接收协程
	go func() {
		// 添加panic捕获
		defer func() {
			if r := recover(); r != nil {
				m.log.Error("PANIC in authentication receiver goroutine",
					logger.Any("panic", r))
				// 记录到全局panic日志
				errors.LogPanic(m.log, r)
				// 发送错误到错误通道
				errCh <- fmt.Errorf("panic in authentication receiver: %v", r)
			}
		}()

		buffer := make([]byte, tunnel.MaxPacketSize)

		// 设置读取超时
		conn.SetReadDeadline(time.Now().Add(m.config.Timeout))

		// 读取响应
		n, err := conn.Read(buffer)
		if err != nil {
			errCh <- fmt.Errorf("failed to read authentication response: %w", err)
			return
		}

		// 记录原始响应数据
		m.log.Debug("Received authentication response",
			logger.Int("bytes_received", n),
			logger.String("raw_hex", fmt.Sprintf("%x", buffer[:n])))

		// 解析数据包
		packet, err := tunnel.ParsePacket(buffer[:n])
		if err != nil {
			errCh <- fmt.Errorf("failed to parse authentication response: %w", err)
			return
		}

		// 记录解析后的数据包信息
		m.log.Debug("Parsed authentication response packet",
			logger.String("packet", packet.String()),
			logger.Int("data_length", len(packet.Data)),
			logger.String("data_hex", fmt.Sprintf("%x", packet.Data)))

		// 根据数据包类型处理
		switch packet.Header.Type {
		case tunnel.PacketTypeOpenAck:
			// 认证成功
			m.log.Info("Authentication successful, parsing OpenAck packet")
			result, err := tunnel.ParseOpenAckPacket(packet)
			if err != nil {
				m.log.Error("Failed to parse OpenAck packet",
					logger.ErrorField(err),
					logger.Int("data_length", len(packet.Data)),
					logger.String("data_hex", fmt.Sprintf("%x", packet.Data)))
				errCh <- fmt.Errorf("failed to parse authentication success response: %w", err)
				return
			}

			// 记录解析成功的配置信息
			m.log.Info("Successfully parsed OpenAck packet",
				logger.Int("config_count", len(result.Config)),
				logger.String("session_id", fmt.Sprintf("0x%04X", result.SessionID)),
				logger.String("token", fmt.Sprintf("0x%08X", result.Token)))

			// 返回认证结果
			resultCh <- &AuthResult{
				Success:   true,
				SessionID: result.SessionID,
				Token:     result.Token,
				Config:    result.Config,
			}

		case tunnel.PacketTypeOpenReject:
			// 认证失败
			result, err := tunnel.ParseOpenRejectPacket(packet)
			if err != nil {
				errCh <- fmt.Errorf("failed to parse authentication failure response: %w", err)
				return
			}

			// 返回认证结果
			resultCh <- &AuthResult{
				Success:      false,
				ErrorCode:    result.ErrorCode,
				ErrorMessage: result.ErrorMessage,
			}

		default:
			// 未知响应
			errCh <- fmt.Errorf("unexpected authentication response type: %d", packet.Header.Type)
		}
	}()

	// 等待结果或超时
	select {
	case result := <-resultCh:
		// 保存用户名和密码，以便后续连接使用
		//m.mutex.Lock()
		if serverAddress != "" {
			m.config.ServerAddress = serverAddress
		}
		//m.mutex.Unlock()

		// 记录凭据已保存，用于调试
		m.log.Info("Credentials saved in Authenticate function",
			logger.String("username", username),
			logger.String("server", serverAddress))

		return result, nil
	case err := <-errCh:
		return nil, err
	case <-ctx.Done():
		return nil, fmt.Errorf("authentication timeout after %v", m.config.Timeout)
	}
}
