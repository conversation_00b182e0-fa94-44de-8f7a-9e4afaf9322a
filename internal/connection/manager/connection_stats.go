/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      connection_stats.go
 *
 * DESCRIPTION :    Connection traffic statistics management
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package manager

import (
	"context"
	"sync/atomic"
	"time"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: updateUploadStats
 *
 * DESCRIPTION:
 *     Update upload traffic statistics
 *
 * PARAMETERS:
 *     bytes - Number of bytes uploaded
 *****************************************************************************/
func (m *Manager) updateUploadStats(bytes int64) {
	// 	m.mutex.Lock()
	// 	defer m.mutex.Unlock()
	m.trafficStats.TotalUpload += bytes
	m.log.Debug("Updated upload stats",
		logger.Int64("bytes", bytes),
		logger.Int64("total_upload", m.trafficStats.TotalUpload))
}

/*****************************************************************************
 * NAME: updateDownloadStats
 *
 * DESCRIPTION:
 *     Update download traffic statistics
 *
 * PARAMETERS:
 *     bytes - Number of bytes downloaded
 *****************************************************************************/
func (m *Manager) updateDownloadStats(bytes int64) {
	// 	m.mutex.Lock()
	// 	defer m.mutex.Unlock()
	m.trafficStats.TotalDownload += bytes
	m.log.Debug("Updated download stats",
		logger.Int64("bytes", bytes),
		logger.Int64("total_download", m.trafficStats.TotalDownload))
}

/*****************************************************************************
 * NAME: UpdateTrafficSpeed
 *
 * DESCRIPTION:
 *     Update traffic speed statistics (should be called periodically)
 *****************************************************************************/
func (m *Manager) UpdateTrafficSpeed() {
	// 	m.mutex.Lock()
	// 	defer m.mutex.Unlock()

	now := time.Now()
	if !m.trafficStats.LastUpdate.IsZero() {
		duration := now.Sub(m.trafficStats.LastUpdate).Seconds()
		if duration > 0 {
			// Calculate speed (bytes/second)
			uploadDiff := m.trafficStats.TotalUpload - m.lastUpload
			downloadDiff := m.trafficStats.TotalDownload - m.lastDownload

			m.trafficStats.UploadSpeed = int64(float64(uploadDiff) / duration)
			m.trafficStats.DownloadSpeed = int64(float64(downloadDiff) / duration)

			m.log.Debug("Updated traffic speed",
				logger.Float64("duration", duration),
				logger.Int64("upload_diff", uploadDiff),
				logger.Int64("download_diff", downloadDiff),
				logger.Int64("upload_speed", m.trafficStats.UploadSpeed),
				logger.Int64("download_speed", m.trafficStats.DownloadSpeed))
		}
	}

	// Update records
	m.lastUpload = m.trafficStats.TotalUpload
	m.lastDownload = m.trafficStats.TotalDownload
	m.trafficStats.LastUpdate = now
}

/*****************************************************************************
 * NAME: GetTrafficStats
 *
 * DESCRIPTION:
 *     Get current traffic statistics
 *
 * RETURNS:
 *     TrafficStats - Current traffic statistics
 *****************************************************************************/
func (m *Manager) GetTrafficStats() TrafficStats {
	m.log.Debug("Getting traffic stats",
		logger.Int64("total_upload", m.trafficStats.TotalUpload),
		logger.Int64("total_download", m.trafficStats.TotalDownload),
		logger.Int64("upload_speed", m.trafficStats.UploadSpeed),
		logger.Int64("download_speed", m.trafficStats.DownloadSpeed))
	return m.trafficStats
}

/*****************************************************************************
 * NAME: resetTrafficStats
 *
 * DESCRIPTION:
 *     Reset traffic statistics
 *****************************************************************************/
func (m *Manager) resetTrafficStats() {
	// 	m.mutex.Lock()
	// 	defer m.mutex.Unlock()

	// Record statistics before reset
	m.log.Debug("Resetting traffic stats",
		logger.Int64("prev_total_upload", m.trafficStats.TotalUpload),
		logger.Int64("prev_total_download", m.trafficStats.TotalDownload),
		logger.Int64("prev_upload_speed", m.trafficStats.UploadSpeed),
		logger.Int64("prev_download_speed", m.trafficStats.DownloadSpeed))

	m.trafficStats = TrafficStats{}
	m.lastUpload = 0
	m.lastDownload = 0

	m.log.Info("Traffic statistics reset successfully")
}

/*****************************************************************************
 * NAME: runTrafficStatsUpdater
 *
 * DESCRIPTION:
 *     Run traffic statistics updater goroutine, periodically update traffic speed statistics
 *
 * PARAMETERS:
 *     ctx - Context for controlling goroutine lifecycle
 *
 * RETURNS:
 *     error - Goroutine execution error
 *****************************************************************************/
func (m *Manager) runTrafficStatsUpdater(ctx context.Context) error {
	ticker := time.NewTicker(2 * time.Second) // Update traffic statistics every 2 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			m.log.Debug("Traffic stats updater stopped")
			return nil
		case <-ticker.C:
			m.UpdateTrafficSpeed()

			// Update performance monitoring metrics
			m.perfMonitor.SetGauge("bytes_sent", atomic.LoadUint64(&m.bytesSent))
			m.perfMonitor.SetGauge("bytes_received", atomic.LoadUint64(&m.bytesReceived))
			m.perfMonitor.SetGauge("packets_sent", atomic.LoadUint64(&m.packetsSent))
			m.perfMonitor.SetGauge("packets_received", atomic.LoadUint64(&m.packetsReceived))

			// Update connection status
			if m.connected {
				m.perfMonitor.SetGauge("connection_status", 1)
			} else {
				m.perfMonitor.SetGauge("connection_status", 0)
			}
		}
	}
}
