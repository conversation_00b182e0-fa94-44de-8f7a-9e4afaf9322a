/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      connection_server.go
 *
 * DESCRIPTION :    Connection server management
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package manager

import (
	"mobile/internal/common/logger"
	"mobile/internal/connection/server"
)

/*****************************************************************************
 * NAME: SetCurrentServer
 *
 * DESCRIPTION:
 *     Set current connected server information
 *
 * PARAMETERS:
 *     server - Server information to set
 *****************************************************************************/
func (m *Manager) SetCurrentServer(server *server.Server) {
	if server == nil {
		m.log.Warn("Attempted to set nil server")
		return
	}

	// Create server copy to avoid concurrency issues
	serverCopy := *server
	m.currentServer = &serverCopy

	m.log.Info("Updated current server information",
		logger.String("server_id", server.ID),
		logger.String("server_name", server.Name),
		logger.String("server_address", server.ServerName),
		logger.Int("server_port", server.ServerPort),
		logger.Int("ping", server.Ping),
		logger.String("status", server.Status))

	// Update connection configuration
	m.config.ServerAddress = server.ServerName
	m.config.ServerPort = server.ServerPort

	// Notify status change
	status := m.GetStatus()
	m.notifyStatusChange(&status)
}

/*****************************************************************************
 * NAME: GetCurrentServer
 *
 * DESCRIPTION:
 *     Get current connected server information
 *
 * RETURNS:
 *     *server.Server - Current server information copy, nil if none
 *****************************************************************************/
func (m *Manager) GetCurrentServer() *server.Server {
	if m.currentServer == nil {
		return nil
	}
	// Return copy to avoid concurrency issues
	serverCopy := *m.currentServer
	return &serverCopy
}
