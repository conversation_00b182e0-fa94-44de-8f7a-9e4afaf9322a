/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      state_machine.go
 *
 * DESCRIPTION :    Connection state machine implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        2025-05-15  Initial implementation
 ******************************************************************************/

package manager

import (
	"fmt"
	"sync"
	"time"

	"mobile/internal/common/logger"
	"mobile/internal/protocol/tunnel"
)

// 客户端状态常量 - 与SDWAN VPN协议规范一致
const (
	StateInit     = 2 // 初始化（DNS解析中）
	StateInit1    = 3 // 已获取服务器IP
	StateAuth     = 4 // 认证中
	StateData     = 5 // 数据传输
	StateClosed   = 6 // 已关闭
	StateAuthFail = 7 // 认证失败
)

// StateChangeCallback 状态变化回调函数类型
type StateChangeCallback func(oldState, newState uint8)

// ErrorCallback 错误回调函数类型
type ErrorCallback func(err error)

// PacketHandler 数据包处理函数类型
type PacketHandler func(packet *tunnel.Packet) error

// StateMachine 连接状态机
type StateMachine struct {
	state            uint8                   // 当前状态
	sessionID        uint16                  // 会话ID
	token            uint32                  // 认证令牌
	lastHeartbeat    time.Time               // 最后心跳时间
	stateChangedTime time.Time               // 状态变化时间
	mutex            sync.RWMutex            // 读写锁
	packetHandlers   map[uint8]PacketHandler // 数据包处理函数
	stateCallbacks   []StateChangeCallback   // 状态变化回调函数
	errorCallbacks   []ErrorCallback         // 错误回调函数
	log              logger.Logger           // 日志记录器
	config           map[string]string       // 配置信息
	errorCode        int                     // 错误码
	errorMessage     string                  // 错误消息
}

/*****************************************************************************
 * NAME: NewStateMachine
 *
 * DESCRIPTION:
 *     创建新的连接状态机
 *
 * PARAMETERS:
 *     log - 日志记录器
 *
 * RETURNS:
 *     *StateMachine - 创建的状态机
 *****************************************************************************/
func NewStateMachine(log logger.Logger) *StateMachine {
	sm := &StateMachine{
		state:            StateInit,
		lastHeartbeat:    time.Now(),
		stateChangedTime: time.Now(),
		packetHandlers:   make(map[uint8]PacketHandler),
		stateCallbacks:   make([]StateChangeCallback, 0),
		errorCallbacks:   make([]ErrorCallback, 0),
		log:              log.WithModule("connection-state"),
		config:           make(map[string]string),
	}

	return sm
}

/*****************************************************************************
 * NAME: RegisterPacketHandler
 *
 * DESCRIPTION:
 *     注册数据包处理函数
 *
 * PARAMETERS:
 *     packetType - 数据包类型
 *     handler - 处理函数
 *****************************************************************************/
func (sm *StateMachine) RegisterPacketHandler(packetType uint8, handler PacketHandler) {
	//sm.mutex.Lock()
	//defer sm.mutex.Unlock()

	sm.packetHandlers[packetType] = handler
}

/*****************************************************************************
 * NAME: RegisterStateChangeCallback
 *
 * DESCRIPTION:
 *     注册状态变化回调函数
 *
 * PARAMETERS:
 *     callback - 回调函数
 *****************************************************************************/
func (sm *StateMachine) RegisterStateChangeCallback(callback StateChangeCallback) {
	//sm.mutex.Lock()
	//defer sm.mutex.Unlock()

	sm.stateCallbacks = append(sm.stateCallbacks, callback)
}

/*****************************************************************************
 * NAME: RegisterErrorCallback
 *
 * DESCRIPTION:
 *     注册错误回调函数
 *
 * PARAMETERS:
 *     callback - 回调函数
 *****************************************************************************/
func (sm *StateMachine) RegisterErrorCallback(callback ErrorCallback) {
	//sm.mutex.Lock()
	//defer sm.mutex.Unlock()

	sm.errorCallbacks = append(sm.errorCallbacks, callback)
}

/*****************************************************************************
 * NAME: GetState
 *
 * DESCRIPTION:
 *     获取当前状态
 *
 * RETURNS:
 *     uint8 - 当前状态
 *****************************************************************************/
func (sm *StateMachine) GetState() uint8 {
	//sm.mutex.RLock()
	//defer sm.mutex.RUnlock()

	return sm.state
}

/*****************************************************************************
 * NAME: GetStateString
 *
 * DESCRIPTION:
 *     获取当前状态字符串
 *
 * RETURNS:
 *     string - 当前状态字符串
 *****************************************************************************/
func (sm *StateMachine) GetStateString() string {
	//sm.mutex.RLock()
	//defer sm.mutex.RUnlock()

	switch sm.state {
	case StateInit:
		return "INIT"
	case StateInit1:
		return "INIT1"
	case StateAuth:
		return "AUTH"
	case StateData:
		return "DATA"
	case StateClosed:
		return "CLOSED"
	case StateAuthFail:
		return fmt.Sprintf("AUTHFAIL(%s)", sm.errorMessage)
	default:
		return fmt.Sprintf("UNKNOWN(%d)", sm.state)
	}
}

/*****************************************************************************
 * NAME: SetState
 *
 * DESCRIPTION:
 *     设置当前状态
 *
 * PARAMETERS:
 *     state - 新状态
 *****************************************************************************/
func (sm *StateMachine) SetState(state uint8) {
	//sm.mutex.Lock()
	oldState := sm.state
	if oldState == state {
		//sm.mutex.Unlock()
		return
	}

	sm.state = state
	sm.stateChangedTime = time.Now()
	callbacks := make([]StateChangeCallback, len(sm.stateCallbacks))
	copy(callbacks, sm.stateCallbacks)
	//sm.mutex.Unlock()

	sm.log.Info("State changed",
		logger.Int("old_state", int(oldState)),
		logger.Int("new_state", int(state)),
		logger.String("old_state_str", sm.stateToString(oldState)),
		logger.String("new_state_str", sm.stateToString(state)))

	// 调用状态变化回调函数
	for _, callback := range callbacks {
		callback(oldState, state)
	}
}

/*****************************************************************************
 * NAME: SetError
 *
 * DESCRIPTION:
 *     设置错误信息
 *
 * PARAMETERS:
 *     code - 错误码
 *     message - 错误消息
 *****************************************************************************/
func (sm *StateMachine) SetError(code int, message string) {
	//sm.mutex.Lock()
	sm.errorCode = code
	sm.errorMessage = message
	callbacks := make([]ErrorCallback, len(sm.errorCallbacks))
	copy(callbacks, sm.errorCallbacks)
	err := fmt.Errorf("%s", message)
	//sm.mutex.Unlock()

	sm.log.Error("Connection error",
		logger.Int("code", code),
		logger.String("message", message))

	// 调用错误回调函数
	for _, callback := range callbacks {
		callback(err)
	}
}

/*****************************************************************************
 * NAME: GetErrorMessage
 *
 * DESCRIPTION:
 *     获取错误消息
 *
 * RETURNS:
 *     string - 错误消息
 *****************************************************************************/
func (sm *StateMachine) GetErrorMessage() string {
	//sm.mutex.RLock()
	//defer sm.mutex.RUnlock()
	return sm.errorMessage
}

/*****************************************************************************
 * NAME: GetSessionInfo
 *
 * DESCRIPTION:
 *     获取会话信息
 *
 * RETURNS:
 *     uint16 - 会话ID
 *     uint32 - 认证令牌
 *****************************************************************************/
func (sm *StateMachine) GetSessionInfo() (uint16, uint32) {
	//sm.mutex.RLock()
	//defer sm.mutex.RUnlock()

	return sm.sessionID, sm.token
}

/*****************************************************************************
 * NAME: SetSessionInfo
 *
 * DESCRIPTION:
 *     设置会话信息
 *
 * PARAMETERS:
 *     sessionID - 会话ID
 *     token - 认证令牌
 *****************************************************************************/
func (sm *StateMachine) SetSessionInfo(sessionID uint16, token uint32) {
	//sm.mutex.Lock()
	//defer sm.mutex.Unlock()

	sm.sessionID = sessionID
	sm.token = token
}

/*****************************************************************************
 * NAME: UpdateHeartbeat
 *
 * DESCRIPTION:
 *     更新心跳时间
 *****************************************************************************/
func (sm *StateMachine) UpdateHeartbeat() {
	//sm.mutex.Lock()
	//defer sm.mutex.Unlock()

	sm.lastHeartbeat = time.Now()
}

/*****************************************************************************
 * NAME: GetLastHeartbeat
 *
 * DESCRIPTION:
 *     获取最后心跳时间
 *
 * RETURNS:
 *     time.Time - 最后心跳时间
 *****************************************************************************/
func (sm *StateMachine) GetLastHeartbeat() time.Time {
	//sm.mutex.RLock()
	//defer sm.mutex.RUnlock()

	return sm.lastHeartbeat
}

/*****************************************************************************
 * NAME: GetStateChangedTime
 *
 * DESCRIPTION:
 *     获取状态变化时间
 *
 * RETURNS:
 *     time.Time - 状态变化时间
 *****************************************************************************/
func (sm *StateMachine) GetStateChangedTime() time.Time {
	//sm.mutex.RLock()
	//defer sm.mutex.RUnlock()

	return sm.stateChangedTime
}

/*****************************************************************************
 * NAME: SetConfig
 *
 * DESCRIPTION:
 *     设置配置信息
 *
 * PARAMETERS:
 *     config - 配置信息
 *****************************************************************************/
func (sm *StateMachine) SetConfig(config map[string]string) {
	//sm.mutex.Lock()
	//defer sm.mutex.Unlock()

	for k, v := range config {
		sm.config[k] = v
	}
}

/*****************************************************************************
 * NAME: GetConfig
 *
 * DESCRIPTION:
 *     获取配置信息
 *
 * RETURNS:
 *     map[string]string - 配置信息
 *****************************************************************************/
func (sm *StateMachine) GetConfig() map[string]string {
	//sm.mutex.RLock()
	//defer sm.mutex.RUnlock()

	config := make(map[string]string)
	for k, v := range sm.config {
		config[k] = v
	}

	return config
}

/*****************************************************************************
 * NAME: GetConfigValue
 *
 * DESCRIPTION:
 *     获取配置值
 *
 * PARAMETERS:
 *     key - 配置键
 *     defaultValue - 默认值
 *
 * RETURNS:
 *     string - 配置值
 *****************************************************************************/
func (sm *StateMachine) GetConfigValue(key, defaultValue string) string {
	//sm.mutex.RLock()
	//defer sm.mutex.RUnlock()

	if value, ok := sm.config[key]; ok {
		return value
	}

	return defaultValue
}

/*****************************************************************************
 * NAME: HandlePacket
 *
 * DESCRIPTION:
 *     处理数据包
 *
 * PARAMETERS:
 *     packet - 数据包
 *
 * RETURNS:
 *     error - 处理错误
 *****************************************************************************/
func (sm *StateMachine) HandlePacket(packet *tunnel.Packet) error {
	//sm.mutex.RLock()
	handler, ok := sm.packetHandlers[uint8(packet.Header.Type)]
	//sm.mutex.RUnlock()

	if !ok {
		return fmt.Errorf("no handler for packet type: 0x%02X", packet.Header.Type)
	}

	return handler(packet)
}

/*****************************************************************************
 * NAME: stateToString
 *
 * DESCRIPTION:
 *     将状态转换为字符串
 *
 * PARAMETERS:
 *     state - 状态
 *
 * RETURNS:
 *     string - 状态字符串
 *****************************************************************************/
func (sm *StateMachine) stateToString(state uint8) string {
	switch state {
	case StateInit:
		return "INIT"
	case StateInit1:
		return "INIT1"
	case StateAuth:
		return "AUTH"
	case StateData:
		return "DATA"
	case StateClosed:
		return "CLOSED"
	case StateAuthFail:
		return "AUTHFAIL"
	default:
		return fmt.Sprintf("UNKNOWN(%d)", state)
	}
}
