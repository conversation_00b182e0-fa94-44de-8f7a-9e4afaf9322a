/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      packet.go
 *
 * DESCRIPTION :    IP packet processing implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package packet

import (
	"encoding/binary"
	"fmt"
	"net"

	"mobile/internal/common/errors"
)

// IP protocol numbers
const (
	ProtocolICMP   = 1
	ProtocolTCP    = 6
	ProtocolUDP    = 17
	ProtocolICMPv6 = 58
)

// IP versions
const (
	IPv4 = 4
	IPv6 = 6
)

/*****************************************************************************
 * NAME: IPPacket
 *
 * DESCRIPTION:
 *     Represents an IP packet with parsed header fields and payload.
 *     Supports both IPv4 and IPv6 packet parsing and manipulation.
 *
 * FIELDS:
 *     data       - Raw packet data
 *     version    - IP version (4 or 6)
 *     headerLen  - Header length in 32-bit words
 *     tos        - Type of Service/DSCP+ECN
 *     length     - Total packet length
 *     id         - Identification field
 *     flags      - Flags field
 *     fragOffset - Fragment offset
 *     ttl        - Time to Live/Hop Limit
 *     protocol   - Protocol/Next Header
 *     checksum   - Header checksum
 *     srcIP      - Source IP address
 *     dstIP      - Destination IP address
 *     options    - Options field (IPv4 only)
 *     payload    - Packet payload
 *****************************************************************************/
type IPPacket struct {
	data       []byte // Raw packet data
	version    uint8  // IP version (4 or 6)
	headerLen  uint8  // Header length in 32-bit words
	tos        uint8  // Type of Service/DSCP+ECN
	length     uint16 // Total packet length
	id         uint16 // Identification field
	flags      uint8  // Flags field
	fragOffset uint16 // Fragment offset
	ttl        uint8  // Time to Live/Hop Limit
	protocol   uint8  // Protocol/Next Header
	checksum   uint16 // Header checksum
	srcIP      net.IP // Source IP address
	dstIP      net.IP // Destination IP address
	options    []byte // Options field (IPv4 only)
	payload    []byte // Packet payload
}

/*****************************************************************************
 * NAME: Parse
 *
 * DESCRIPTION:
 *     Parses IP packet data and returns an IPPacket structure.
 *     Automatically detects IPv4 or IPv6 based on version field.
 *
 * PARAMETERS:
 *     data - Raw packet data bytes
 *
 * RETURNS:
 *     *IPPacket - Parsed IP packet structure
 *     error     - Parsing error if any
 *****************************************************************************/
func Parse(data []byte) (*IPPacket, error) {
	if len(data) < 20 {
		return nil, errors.NewWithCode(errors.CodePacketError,
			"packet too short: %d bytes (minimum 20 bytes)", len(data))
	}

	version := data[0] >> 4

	switch version {
	case IPv4:
		return parseIPv4(data)
	case IPv6:
		return parseIPv6(data)
	default:
		return nil, errors.NewWithCode(errors.CodePacketError,
			"unsupported IP version: %d", version)
	}
}

/*****************************************************************************
 * NAME: parseIPv4
 *
 * DESCRIPTION:
 *     Parses IPv4 packet data and extracts header fields.
 *     Validates packet structure and length consistency.
 *
 * PARAMETERS:
 *     data - Raw IPv4 packet data
 *
 * RETURNS:
 *     *IPPacket - Parsed IPv4 packet structure
 *     error     - Parsing error if any
 *****************************************************************************/
func parseIPv4(data []byte) (*IPPacket, error) {
	if len(data) < 20 {
		return nil, errors.NewWithCode(errors.CodePacketError,
			"IPv4 packet too short: %d bytes (minimum 20 bytes)", len(data))
	}

	headerLen := (data[0] & 0x0F) * 4 // In bytes
	if headerLen < 20 {
		return nil, errors.NewWithCode(errors.CodePacketError,
			"invalid IPv4 header length: %d bytes (minimum 20 bytes)", headerLen)
	}

	if len(data) < int(headerLen) {
		return nil, errors.NewWithCode(errors.CodePacketError,
			"IPv4 packet too short for header: %d bytes (header %d bytes)",
			len(data), headerLen)
	}

	totalLen := binary.BigEndian.Uint16(data[2:4])
	if len(data) < int(totalLen) {
		return nil, errors.NewWithCode(errors.CodePacketError,
			"IPv4 packet too short for total length: %d bytes (expected %d bytes)",
			len(data), totalLen)
	}

	packet := &IPPacket{
		data:       data[:totalLen], // Keep only actual packet length
		version:    IPv4,
		headerLen:  headerLen / 4, // In 32-bit words
		tos:        data[1],
		length:     totalLen,
		id:         binary.BigEndian.Uint16(data[4:6]),
		flags:      data[6] >> 5,
		fragOffset: binary.BigEndian.Uint16(data[6:8]) & 0x1FFF,
		ttl:        data[8],
		protocol:   data[9],
		checksum:   binary.BigEndian.Uint16(data[10:12]),
		srcIP:      net.IP(data[12:16]),
		dstIP:      net.IP(data[16:20]),
	}

	// Handle options
	if headerLen > 20 {
		packet.options = make([]byte, headerLen-20)
		copy(packet.options, data[20:headerLen])
	}

	// Set payload
	if totalLen > uint16(headerLen) {
		packet.payload = make([]byte, totalLen-uint16(headerLen))
		copy(packet.payload, data[headerLen:totalLen])
	}

	return packet, nil
}

/*****************************************************************************
 * NAME: parseIPv6
 *
 * DESCRIPTION:
 *     Parses IPv6 packet data and extracts header fields.
 *     IPv6 has fixed 40-byte header with different field layout.
 *
 * PARAMETERS:
 *     data - Raw IPv6 packet data
 *
 * RETURNS:
 *     *IPPacket - Parsed IPv6 packet structure
 *     error     - Parsing error if any
 *****************************************************************************/
func parseIPv6(data []byte) (*IPPacket, error) {
	if len(data) < 40 {
		return nil, errors.NewWithCode(errors.CodePacketError,
			"IPv6 packet too short: %d bytes (minimum 40 bytes)", len(data))
	}

	payloadLen := binary.BigEndian.Uint16(data[4:6])
	totalLen := payloadLen + 40 // IPv6 header is fixed at 40 bytes

	if len(data) < int(totalLen) {
		return nil, errors.NewWithCode(errors.CodePacketError,
			"IPv6 packet too short for total length: %d bytes (expected %d bytes)",
			len(data), totalLen)
	}

	packet := &IPPacket{
		data:       data[:totalLen], // Keep only actual packet length
		version:    IPv6,
		headerLen:  10,                                 // IPv6 header is fixed at 40 bytes, 10 in 32-bit words
		tos:        (data[0]&0x0F)<<4 | (data[1] >> 4), // Traffic Class
		length:     totalLen,
		id:         0,       // IPv6 has no ID field
		flags:      0,       // IPv6 has no flags field
		fragOffset: 0,       // IPv6 has no fragment offset field
		ttl:        data[7], // Hop Limit
		protocol:   data[6], // Next Header
		checksum:   0,       // IPv6 has no checksum field
		srcIP:      net.IP(data[8:24]),
		dstIP:      net.IP(data[24:40]),
	}

	// Set payload
	if payloadLen > 0 {
		packet.payload = make([]byte, payloadLen)
		copy(packet.payload, data[40:40+payloadLen])
	}

	return packet, nil
}

/*****************************************************************************
 * NAME: Version
 *
 * DESCRIPTION:
 *     Returns the IP version (4 or 6).
 *
 * RETURNS:
 *     uint8 - IP version
 *****************************************************************************/
func (p *IPPacket) Version() uint8 {
	return p.version
}

/*****************************************************************************
 * NAME: HeaderLength
 *
 * DESCRIPTION:
 *     Returns the header length in bytes.
 *
 * RETURNS:
 *     uint8 - Header length in bytes
 *****************************************************************************/
func (p *IPPacket) HeaderLength() uint8 {
	return p.headerLen * 4
}

/*****************************************************************************
 * NAME: TOS
 *
 * DESCRIPTION:
 *     Returns the Type of Service/DSCP+ECN field.
 *
 * RETURNS:
 *     uint8 - Type of Service value
 *****************************************************************************/
func (p *IPPacket) TOS() uint8 {
	return p.tos
}

/*****************************************************************************
 * NAME: Length
 *
 * DESCRIPTION:
 *     Returns the total packet length.
 *
 * RETURNS:
 *     uint16 - Total packet length
 *****************************************************************************/
func (p *IPPacket) Length() uint16 {
	return p.length
}

/*****************************************************************************
 * NAME: ID
 *
 * DESCRIPTION:
 *     Returns the identification field.
 *
 * RETURNS:
 *     uint16 - Identification value
 *****************************************************************************/
func (p *IPPacket) ID() uint16 {
	return p.id
}

/*****************************************************************************
 * NAME: Flags
 *
 * DESCRIPTION:
 *     Returns the flags field.
 *
 * RETURNS:
 *     uint8 - Flags value
 *****************************************************************************/
func (p *IPPacket) Flags() uint8 {
	return p.flags
}

/*****************************************************************************
 * NAME: FragmentOffset
 *
 * DESCRIPTION:
 *     Returns the fragment offset.
 *
 * RETURNS:
 *     uint16 - Fragment offset value
 *****************************************************************************/
func (p *IPPacket) FragmentOffset() uint16 {
	return p.fragOffset
}

/*****************************************************************************
 * NAME: TTL
 *
 * DESCRIPTION:
 *     Returns the Time to Live/Hop Limit.
 *
 * RETURNS:
 *     uint8 - TTL/Hop Limit value
 *****************************************************************************/
func (p *IPPacket) TTL() uint8 {
	return p.ttl
}

/*****************************************************************************
 * NAME: Protocol
 *
 * DESCRIPTION:
 *     Returns the protocol/next header field.
 *
 * RETURNS:
 *     uint8 - Protocol identifier
 *****************************************************************************/
func (p *IPPacket) Protocol() uint8 {
	return p.protocol
}

/*****************************************************************************
 * NAME: Checksum
 *
 * DESCRIPTION:
 *     Returns the header checksum.
 *
 * RETURNS:
 *     uint16 - Header checksum value
 *****************************************************************************/
func (p *IPPacket) Checksum() uint16 {
	return p.checksum
}

/*****************************************************************************
 * NAME: SourceIP
 *
 * DESCRIPTION:
 *     Returns the source IP address.
 *
 * RETURNS:
 *     net.IP - Source IP address
 *****************************************************************************/
func (p *IPPacket) SourceIP() net.IP {
	return p.srcIP
}

/*****************************************************************************
 * NAME: DestinationIP
 *
 * DESCRIPTION:
 *     Returns the destination IP address.
 *
 * RETURNS:
 *     net.IP - Destination IP address
 *****************************************************************************/
func (p *IPPacket) DestinationIP() net.IP {
	return p.dstIP
}

/*****************************************************************************
 * NAME: Options
 *
 * DESCRIPTION:
 *     Returns the options field (IPv4 only).
 *
 * RETURNS:
 *     []byte - Options data
 *****************************************************************************/
func (p *IPPacket) Options() []byte {
	return p.options
}

/*****************************************************************************
 * NAME: Payload
 *
 * DESCRIPTION:
 *     Returns the packet payload.
 *
 * RETURNS:
 *     []byte - Packet payload data
 *****************************************************************************/
func (p *IPPacket) Payload() []byte {
	return p.payload
}

/*****************************************************************************
 * NAME: SetTTL
 *
 * DESCRIPTION:
 *     Sets the Time to Live/Hop Limit value.
 *     Updates both internal field and raw packet data.
 *
 * PARAMETERS:
 *     ttl - Time to Live/Hop Limit value
 *****************************************************************************/
func (p *IPPacket) SetTTL(ttl uint8) {
	p.ttl = ttl
	if p.version == IPv4 {
		p.data[8] = ttl
	} else if p.version == IPv6 {
		p.data[7] = ttl
	}
}

/*****************************************************************************
 * NAME: SetSourceIP
 *
 * DESCRIPTION:
 *     Sets the source IP address.
 *     Updates both internal field and raw packet data.
 *
 * PARAMETERS:
 *     ip - Source IP address to set
 *
 * RETURNS:
 *     error - Error if IP address is invalid for packet version
 *****************************************************************************/
func (p *IPPacket) SetSourceIP(ip net.IP) error {
	if p.version == IPv4 {
		if ip4 := ip.To4(); ip4 != nil {
			p.srcIP = ip4
			copy(p.data[12:16], ip4)
			return nil
		}
		return errors.NewWithCode(errors.CodePacketError, "invalid IPv4 address")
	} else if p.version == IPv6 {
		if ip6 := ip.To16(); ip6 != nil {
			p.srcIP = ip6
			copy(p.data[8:24], ip6)
			return nil
		}
		return errors.NewWithCode(errors.CodePacketError, "invalid IPv6 address")
	}
	return errors.NewWithCode(errors.CodePacketError, "unsupported IP version")
}

/*****************************************************************************
 * NAME: SetDestinationIP
 *
 * DESCRIPTION:
 *     Sets the destination IP address.
 *     Updates both internal field and raw packet data.
 *
 * PARAMETERS:
 *     ip - Destination IP address to set
 *
 * RETURNS:
 *     error - Error if IP address is invalid for packet version
 *****************************************************************************/
func (p *IPPacket) SetDestinationIP(ip net.IP) error {
	if p.version == IPv4 {
		if ip4 := ip.To4(); ip4 != nil {
			p.dstIP = ip4
			copy(p.data[16:20], ip4)
			return nil
		}
		return errors.NewWithCode(errors.CodePacketError, "invalid IPv4 address")
	} else if p.version == IPv6 {
		if ip6 := ip.To16(); ip6 != nil {
			p.dstIP = ip6
			copy(p.data[24:40], ip6)
			return nil
		}
		return errors.NewWithCode(errors.CodePacketError, "invalid IPv6 address")
	}
	return errors.NewWithCode(errors.CodePacketError, "unsupported IP version")
}

/*****************************************************************************
 * NAME: UpdateChecksum
 *
 * DESCRIPTION:
 *     Updates the header checksum (IPv4 only).
 *     IPv6 packets do not have header checksums.
 *****************************************************************************/
func (p *IPPacket) UpdateChecksum() {
	if p.version != IPv4 {
		return // IPv6 has no checksum field
	}

	// Clear current checksum
	p.data[10] = 0
	p.data[11] = 0

	// Calculate new checksum
	checksum := calculateChecksum(p.data[:p.HeaderLength()])
	p.checksum = checksum
	p.data[10] = byte(checksum >> 8)
	p.data[11] = byte(checksum)
}

/*****************************************************************************
 * NAME: Bytes
 *
 * DESCRIPTION:
 *     Returns the raw packet data bytes.
 *
 * RETURNS:
 *     []byte - Raw packet data
 *****************************************************************************/
func (p *IPPacket) Bytes() []byte {
	return p.data
}

/*****************************************************************************
 * NAME: String
 *
 * DESCRIPTION:
 *     Returns a string representation of the packet.
 *
 * RETURNS:
 *     string - Packet string representation
 *****************************************************************************/
func (p *IPPacket) String() string {
	if p.version == IPv4 {
		return fmt.Sprintf("IPv4 packet: %s -> %s, proto=%d, len=%d",
			p.srcIP, p.dstIP, p.protocol, p.length)
	} else if p.version == IPv6 {
		return fmt.Sprintf("IPv6 packet: %s -> %s, proto=%d, len=%d",
			p.srcIP, p.dstIP, p.protocol, p.length)
	}
	return "Unknown packet"
}

/*****************************************************************************
 * NAME: IsFragment
 *
 * DESCRIPTION:
 *     Checks if the packet is a fragment.
 *     For IPv4, checks MF flag or non-zero fragment offset.
 *
 * RETURNS:
 *     bool - True if packet is a fragment
 *****************************************************************************/
func (p *IPPacket) IsFragment() bool {
	if p.version == IPv4 {
		// Check MF flag or non-zero fragment offset
		return (p.flags&0x1 != 0) || (p.fragOffset != 0)
	}
	// IPv6 fragmentation requires checking extension headers, simplified here
	return false
}

/*****************************************************************************
 * NAME: IsFirstFragment
 *
 * DESCRIPTION:
 *     Checks if the packet is the first fragment.
 *     First fragment has zero offset.
 *
 * RETURNS:
 *     bool - True if packet is the first fragment
 *****************************************************************************/
func (p *IPPacket) IsFirstFragment() bool {
	if p.version == IPv4 {
		// First fragment has zero offset
		return p.IsFragment() && p.fragOffset == 0
	}
	// IPv6 fragmentation requires checking extension headers, simplified here
	return false
}

/*****************************************************************************
 * NAME: IsLastFragment
 *
 * DESCRIPTION:
 *     Checks if the packet is the last fragment.
 *     Last fragment has MF flag cleared.
 *
 * RETURNS:
 *     bool - True if packet is the last fragment
 *****************************************************************************/
func (p *IPPacket) IsLastFragment() bool {
	if p.version == IPv4 {
		// Last fragment has MF flag cleared
		return p.IsFragment() && (p.flags&0x1 == 0)
	}
	// IPv6 fragmentation requires checking extension headers, simplified here
	return false
}

/*****************************************************************************
 * NAME: GetTransportProtocol
 *
 * DESCRIPTION:
 *     Returns the transport layer protocol identifier.
 *
 * RETURNS:
 *     uint8 - Transport layer protocol
 *****************************************************************************/
func (p *IPPacket) GetTransportProtocol() uint8 {
	return p.protocol
}

/*****************************************************************************
 * NAME: GetSourcePort
 *
 * DESCRIPTION:
 *     Returns the source port for TCP/UDP packets.
 *     Only works for TCP and UDP protocols.
 *
 * RETURNS:
 *     uint16 - Source port number
 *     error  - Error if not TCP/UDP or payload too short
 *****************************************************************************/
func (p *IPPacket) GetSourcePort() (uint16, error) {
	if p.protocol != ProtocolTCP && p.protocol != ProtocolUDP {
		return 0, errors.NewWithCode(errors.CodePacketError,
			"not a TCP/UDP packet: protocol=%d", p.protocol)
	}

	if len(p.payload) < 2 {
		return 0, errors.NewWithCode(errors.CodePacketError,
			"payload too short for source port")
	}

	return binary.BigEndian.Uint16(p.payload[0:2]), nil
}

/*****************************************************************************
 * NAME: GetDestinationPort
 *
 * DESCRIPTION:
 *     Returns the destination port for TCP/UDP packets.
 *     Only works for TCP and UDP protocols.
 *
 * RETURNS:
 *     uint16 - Destination port number
 *     error  - Error if not TCP/UDP or payload too short
 *****************************************************************************/
func (p *IPPacket) GetDestinationPort() (uint16, error) {
	if p.protocol != ProtocolTCP && p.protocol != ProtocolUDP {
		return 0, errors.NewWithCode(errors.CodePacketError,
			"not a TCP/UDP packet: protocol=%d", p.protocol)
	}

	if len(p.payload) < 4 {
		return 0, errors.NewWithCode(errors.CodePacketError,
			"payload too short for destination port")
	}

	return binary.BigEndian.Uint16(p.payload[2:4]), nil
}

/*****************************************************************************
 * NAME: calculateChecksum
 *
 * DESCRIPTION:
 *     Calculates the Internet checksum for the given data.
 *     Uses standard RFC 1071 algorithm.
 *
 * PARAMETERS:
 *     data - Data to calculate checksum for
 *
 * RETURNS:
 *     uint16 - Calculated checksum
 *****************************************************************************/
func calculateChecksum(data []byte) uint16 {
	var sum uint32
	length := len(data)
	for i := 0; i < length-1; i += 2 {
		sum += uint32(data[i])<<8 | uint32(data[i+1])
	}
	if length%2 == 1 {
		sum += uint32(data[length-1]) << 8
	}
	for sum > 0xffff {
		sum = (sum >> 16) + (sum & 0xffff)
	}
	return ^uint16(sum)
}
