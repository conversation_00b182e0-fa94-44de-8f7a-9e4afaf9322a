/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      types.go
 *
 * DESCRIPTION :    Common types and constants for protocol packages
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package common

// PacketType represents packet type
type PacketType uint8

// Packet type constants - consistent with SDWAN VPN protocol specification
const (
	PacketTypeOpenReject   PacketType = 0x11 // Server rejects client connection
	PacketTypeOpenAck      PacketType = 0x12 // Server acknowledges client connection
	PacketTypeOpen         PacketType = 0x13 // Client requests connection
	PacketTypeData         PacketType = 0x14 // IPv4 data packet
	PacketTypeEchoRequest  PacketType = 0x15 // Echo request (keepalive)
	PacketTypeEchoResponse PacketType = 0x16 // Echo response (keepalive)
	PacketTypeClose        PacketType = 0x17 // Connection close
	PacketTypeDataEncrypt  PacketType = 0x18 // Encrypted IPv4 data packet
	PacketTypeDataDup      PacketType = 0x19 // Duplicate IPv4 data packet
	PacketTypeDataEncDup   PacketType = 0x20 // Encrypted duplicate IPv4 data packet
	PacketTypeIPFrag       PacketType = 0x22 // IPv4 fragmented data packet
	PacketTypeData6        PacketType = 0x23 // IPv6 data packet
	PacketTypeIPFrag6      PacketType = 0x24 // IPv6 fragmented data packet
	PacketTypeSegRT        PacketType = 0x28 // Segment routing data packet
	PacketTypePingRequest  PacketType = 0x29 // Ping request
	PacketTypePingResponse PacketType = 0x2A // Ping response
)

// EncryptionMethod represents encryption method
type EncryptionMethod uint8

// Encryption method constants
const (
	EncryptionNone EncryptionMethod = 0x00 // No encryption
	EncryptionXOR  EncryptionMethod = 0x01 // XOR encryption
	EncryptionAES  EncryptionMethod = 0x02 // AES encryption
)

// AttributeType represents TLV attribute type
type AttributeType uint8

// TLV attribute ID constants - consistent with SDWAN VPN protocol specification
const (
	AttributeUsername AttributeType = 0x01 // Username
	AttributePassword AttributeType = 0x02 // Password
	AttributeMTU      AttributeType = 0x03 // MTU
	AttributeIP       AttributeType = 0x04 // IP address
	AttributeDNS      AttributeType = 0x05 // DNS server
	AttributeGateway  AttributeType = 0x06 // Gateway
	AttributeNetmask  AttributeType = 0x07 // Subnet mask
	AttributeEncrypt  AttributeType = 0x08 // Encryption method
	AttributeDupPkt   AttributeType = 0x09 // Packet duplication
	AttributeLink     AttributeType = 0x0A // Link
	AttributeIP6      AttributeType = 0x0B // IPv6 address
	AttributeDNS6     AttributeType = 0x0C // IPv6 DNS server
	AttributeGateway6 AttributeType = 0x0D // IPv6 gateway
)

// Other constants
const (
	DefaultMTU           = 1400       // Default MTU
	DefaultTimeout       = 10000      // Default timeout (milliseconds)
	DefaultRetryCount    = 5          // Default retry count
	DefaultRetryInterval = 1000       // Default retry interval (milliseconds)
	DefaultHeartbeat     = 15000      // Default heartbeat interval (milliseconds)
	HeaderSize           = 8          // Header size (bytes)
	MaxPacketSize        = 2000       // Maximum packet size (bytes)
	EchoPacketSize       = 32         // Echo packet size (bytes)
	MD5Size              = 16         // MD5 hash size (bytes)
	DefaultPort          = 443        // Default port
	MagicNumber          = 0x5A5A5A5A // Magic number
	ProtocolVersion      = 1          // Protocol version
)

/*****************************************************************************
 * NAME: PacketHeader
 *
 * DESCRIPTION:
 *     Represents tunnel protocol packet header - consistent with SDWAN VPN protocol specification.
 *     Contains packet type, encryption method, session ID, and authentication token.
 *
 * FIELDS:
 *     Type    - Packet type identifier
 *     Encrypt - Encryption method used for the packet
 *     SID     - Session ID for connection tracking
 *     Token   - Authentication token for security
 *****************************************************************************/
type PacketHeader struct {
	Type    PacketType       // Packet type identifier
	Encrypt EncryptionMethod // Encryption method used for the packet
	SID     uint16           // Session ID for connection tracking
	Token   uint32           // Authentication token for security
}
