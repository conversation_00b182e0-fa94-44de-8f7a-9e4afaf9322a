/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      encryption.go
 *
 * DESCRIPTION :    Encryption service implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package encryption

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"fmt"
	"unsafe"

	"mobile/internal/common/logger"
	"mobile/internal/protocol/common"
	"mobile/internal/protocol/tunnel"
)

// Define aliases for compatibility with existing code
const (
	MethodNone = uint8(common.EncryptionNone) // No encryption
	MethodXOR  = uint8(common.EncryptionXOR)  // XOR encryption
	MethodAES  = uint8(common.EncryptionAES)  // AES encryption
)

/*****************************************************************************
 * NAME: Encryptor
 *
 * DESCRIPTION:
 *     Interface for encryption and decryption operations.
 *     Provides methods for encrypting/decrypting raw data and packets.
 *
 * METHODS:
 *     Encrypt       - Encrypts raw data
 *     Decrypt       - Decrypts raw data
 *     EncryptPacket - Encrypts packet data
 *     DecryptPacket - Decrypts packet data
 *     Method        - Returns encryption method identifier
 *****************************************************************************/
type Encryptor interface {
	// Encrypt encrypts raw data
	Encrypt(data []byte) ([]byte, error)

	// Decrypt decrypts raw data
	Decrypt(data []byte) ([]byte, error)

	// EncryptPacket encrypts packet data
	EncryptPacket(packet *tunnel.Packet) (*tunnel.Packet, error)

	// DecryptPacket decrypts packet data
	DecryptPacket(packet *tunnel.Packet) (*tunnel.Packet, error)

	// Method returns encryption method identifier
	Method() uint8
}

/*****************************************************************************
 * NAME: baseEncryptor
 *
 * DESCRIPTION:
 *     Base encryptor implementation providing common functionality.
 *     Used as foundation for specific encryption methods.
 *
 * FIELDS:
 *     method - Encryption method identifier
 *     key    - Encryption key bytes
 *     log    - Logger instance for encryption operations
 *****************************************************************************/
type baseEncryptor struct {
	method uint8         // Encryption method identifier
	key    []byte        // Encryption key bytes
	log    logger.Logger // Logger instance for encryption operations
}

/*****************************************************************************
 * NAME: xorEncryptor
 *
 * DESCRIPTION:
 *     XOR encryption implementation.
 *     Provides XOR-based encryption and decryption operations.
 *
 * FIELDS:
 *     baseEncryptor - Base encryptor functionality
 *****************************************************************************/
type xorEncryptor struct {
	baseEncryptor // Base encryptor functionality
}

/*****************************************************************************
 * NAME: aesEncryptor
 *
 * DESCRIPTION:
 *     AES encryption implementation using ECB mode.
 *     Provides AES-based encryption and decryption operations.
 *
 * FIELDS:
 *     baseEncryptor - Base encryptor functionality
 *     block         - AES cipher block for encryption operations
 *****************************************************************************/
type aesEncryptor struct {
	baseEncryptor              // Base encryptor functionality
	block         cipher.Block // AES cipher block for encryption operations
}

/*****************************************************************************
 * NAME: NewEncryptor
 *
 * DESCRIPTION:
 *     Creates a new encryptor instance based on the specified method.
 *     Generates encryption key from username and password using MD5 hash.
 *
 * PARAMETERS:
 *     method   - Encryption method identifier
 *     username - Username for key generation
 *     password - Password for key generation
 *     log      - Logger instance for encryption operations
 *
 * RETURNS:
 *     Encryptor - Created encryptor instance
 *     error     - Error if creation fails
 *****************************************************************************/
func NewEncryptor(method uint8, username, password string, log logger.Logger) (Encryptor, error) {
	// Create encryption key
	// Use username + password as input for MD5 calculation
	// Consistent with Android and pa_mobile implementations
	key := md5.Sum([]byte(username + password))

	// Create base encryptor
	base := baseEncryptor{
		method: method,
		key:    key[:],
		log:    log.WithModule("encryption"),
	}

	// Create specific encryptor based on method
	switch method {
	case MethodNone:
		return &baseEncryptor{
			method: MethodNone,
			log:    log.WithModule("encryption"),
		}, nil
	case MethodXOR:
		return &xorEncryptor{
			baseEncryptor: base,
		}, nil
	case MethodAES:
		// Create AES cipher block
		block, err := aes.NewCipher(key[:])
		if err != nil {
			return nil, fmt.Errorf("failed to create AES cipher: %w", err)
		}
		return &aesEncryptor{
			baseEncryptor: base,
			block:         block,
		}, nil
	default:
		return nil, fmt.Errorf("unsupported encryption method: %d", method)
	}
}

/*****************************************************************************
 * NAME: Method
 *
 * DESCRIPTION:
 *     Returns the encryption method identifier.
 *
 * RETURNS:
 *     uint8 - Encryption method identifier
 *****************************************************************************/
func (e *baseEncryptor) Method() uint8 {
	return e.method
}

/*****************************************************************************
 * NAME: Encrypt
 *
 * DESCRIPTION:
 *     Encrypts data (base encryptor performs no encryption).
 *
 * PARAMETERS:
 *     data - Raw data to encrypt
 *
 * RETURNS:
 *     []byte - Encrypted data (unchanged for base encryptor)
 *     error  - Encryption error
 *****************************************************************************/
func (e *baseEncryptor) Encrypt(data []byte) ([]byte, error) {
	// No encryption, return original data directly
	return data, nil
}

/*****************************************************************************
 * NAME: Decrypt
 *
 * DESCRIPTION:
 *     Decrypts data (base encryptor performs no decryption).
 *
 * PARAMETERS:
 *     data - Encrypted data to decrypt
 *
 * RETURNS:
 *     []byte - Decrypted data (unchanged for base encryptor)
 *     error  - Decryption error
 *****************************************************************************/
func (e *baseEncryptor) Decrypt(data []byte) ([]byte, error) {
	// No decryption, return original data directly
	return data, nil
}

/*****************************************************************************
 * NAME: EncryptPacket
 *
 * DESCRIPTION:
 *     Encrypts packet data (base encryptor performs no encryption).
 *
 * PARAMETERS:
 *     packet - Original packet to encrypt
 *
 * RETURNS:
 *     *tunnel.Packet - Encrypted packet (unchanged for base encryptor)
 *     error          - Encryption error
 *****************************************************************************/
func (e *baseEncryptor) EncryptPacket(packet *tunnel.Packet) (*tunnel.Packet, error) {
	// No encryption, return original packet directly
	return packet, nil
}

/*****************************************************************************
 * NAME: DecryptPacket
 *
 * DESCRIPTION:
 *     Decrypts packet data (base encryptor performs no decryption).
 *
 * PARAMETERS:
 *     packet - Encrypted packet to decrypt
 *
 * RETURNS:
 *     *tunnel.Packet - Decrypted packet (unchanged for base encryptor)
 *     error          - Decryption error
 *****************************************************************************/
func (e *baseEncryptor) DecryptPacket(packet *tunnel.Packet) (*tunnel.Packet, error) {
	// No decryption, return original packet directly
	return packet, nil
}

/*****************************************************************************
 * NAME: Encrypt
 *
 * DESCRIPTION:
 *     Encrypts data using XOR algorithm.
 *     Uses same XOR encryption method as pa_mobile implementation.
 *
 * PARAMETERS:
 *     data - Raw data to encrypt
 *
 * RETURNS:
 *     []byte - Encrypted data
 *     error  - Encryption error
 *****************************************************************************/
func (e *xorEncryptor) Encrypt(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return data, nil
	}

	// Create result array
	result := make([]byte, len(data))
	copy(result, data)

	// Use same XOR encryption method as pa_mobile
	// Process 8 bytes at a time for XOR operation
	for i := 0; i < len(result)/8; i++ {
		// Treat data and key as uint32 arrays
		data32 := (*[2]uint32)(unsafe.Pointer(&result[i*8]))
		key32 := (*[2]uint32)(unsafe.Pointer(&e.key[0]))

		// XOR operation
		data32[0] ^= key32[0]
		data32[1] ^= key32[1]
	}

	// Process remaining bytes
	remain := len(result) % 8
	if remain > 0 {
		for i := 0; i < remain; i++ {
			result[len(result)-remain+i] ^= e.key[i]
		}
	}

	return result, nil
}

/*****************************************************************************
 * NAME: Decrypt
 *
 * DESCRIPTION:
 *     Decrypts data using XOR algorithm.
 *     XOR encryption and decryption operations are identical.
 *
 * PARAMETERS:
 *     data - Encrypted data to decrypt
 *
 * RETURNS:
 *     []byte - Decrypted data
 *     error  - Decryption error
 *****************************************************************************/
func (e *xorEncryptor) Decrypt(data []byte) ([]byte, error) {
	// XOR encryption and decryption operations are identical
	return e.Encrypt(data)
}

/*****************************************************************************
 * NAME: EncryptPacket
 *
 * DESCRIPTION:
 *     Encrypts packet data using XOR algorithm.
 *     Only encrypts the data portion of the packet.
 *
 * PARAMETERS:
 *     packet - Original packet to encrypt
 *
 * RETURNS:
 *     *tunnel.Packet - Encrypted packet
 *     error          - Encryption error
 *****************************************************************************/
func (e *xorEncryptor) EncryptPacket(packet *tunnel.Packet) (*tunnel.Packet, error) {
	// Only encrypt data portion
	encryptedData, err := e.Encrypt(packet.Data)
	if err != nil {
		return nil, err
	}

	// Create new packet
	encryptedPacket := &tunnel.Packet{
		Header: packet.Header,
		Data:   encryptedData,
	}

	// Set encryption flag
	encryptedPacket.Header.Encrypt = common.EncryptionMethod(e.method)

	return encryptedPacket, nil
}

/*****************************************************************************
 * NAME: DecryptPacket
 *
 * DESCRIPTION:
 *     Decrypts packet data using XOR algorithm.
 *     Validates encryption method before decryption.
 *
 * PARAMETERS:
 *     packet - Encrypted packet to decrypt
 *
 * RETURNS:
 *     *tunnel.Packet - Decrypted packet
 *     error          - Decryption error
 *****************************************************************************/
func (e *xorEncryptor) DecryptPacket(packet *tunnel.Packet) (*tunnel.Packet, error) {
	// Check encryption method
	if packet.Header.Encrypt != common.EncryptionMethod(e.method) {
		return nil, fmt.Errorf(
			"packet encryption method mismatch: expected %d, got %d",
			e.method, packet.Header.Encrypt)
	}

	// Only decrypt data portion
	decryptedData, err := e.Decrypt(packet.Data)
	if err != nil {
		return nil, err
	}

	// Create new packet
	decryptedPacket := &tunnel.Packet{
		Header: packet.Header,
		Data:   decryptedData,
	}

	return decryptedPacket, nil
}

/*****************************************************************************
 * NAME: Encrypt
 *
 * DESCRIPTION:
 *     Encrypts data using AES-ECB mode (consistent with Android implementation).
 *     Uses zero padding instead of PKCS#7 for compatibility.
 *
 * PARAMETERS:
 *     data - Raw data to encrypt
 *
 * RETURNS:
 *     []byte - Encrypted data
 *     error  - Encryption error
 *****************************************************************************/
func (e *aesEncryptor) Encrypt(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return data, nil
	}

	// Ensure data length is multiple of block size
	padding := aes.BlockSize - (len(data) % aes.BlockSize)
	if padding == aes.BlockSize {
		padding = 0
	}

	// Pad data
	paddedData := make([]byte, len(data)+padding)
	copy(paddedData, data)

	// Do not use PKCS#7 padding, consistent with Android implementation
	// Remaining portion stays as zero

	// Create result array
	result := make([]byte, len(paddedData))

	// ECB mode encryption (each block encrypted independently)
	for i := 0; i < len(paddedData); i += aes.BlockSize {
		e.block.Encrypt(result[i:i+aes.BlockSize], paddedData[i:i+aes.BlockSize])
	}

	return result, nil
}

/*****************************************************************************
 * NAME: Decrypt
 *
 * DESCRIPTION:
 *     Decrypts data using AES-ECB mode (consistent with Android implementation).
 *     Does not handle padding removal, caller must handle padding.
 *
 * PARAMETERS:
 *     data - Encrypted data to decrypt
 *
 * RETURNS:
 *     []byte - Decrypted data
 *     error  - Decryption error
 *****************************************************************************/
func (e *aesEncryptor) Decrypt(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return data, nil
	}

	// Check if data length is multiple of block size
	if len(data)%aes.BlockSize != 0 {
		return nil, fmt.Errorf(
			"encrypted data length must be a multiple of %d bytes, got %d bytes",
			aes.BlockSize, len(data))
	}

	// Create result array
	result := make([]byte, len(data))

	// ECB mode decryption (each block decrypted independently)
	for i := 0; i < len(data); i += aes.BlockSize {
		e.block.Decrypt(result[i:i+aes.BlockSize], data[i:i+aes.BlockSize])
	}

	// Do not handle padding, consistent with Android implementation
	// Return complete decrypted data, caller handles padding

	return result, nil
}

/*****************************************************************************
 * NAME: EncryptPacket
 *
 * DESCRIPTION:
 *     Encrypts packet data using AES algorithm.
 *     Only encrypts the data portion of the packet.
 *
 * PARAMETERS:
 *     packet - Original packet to encrypt
 *
 * RETURNS:
 *     *tunnel.Packet - Encrypted packet
 *     error          - Encryption error
 *****************************************************************************/
func (e *aesEncryptor) EncryptPacket(packet *tunnel.Packet) (*tunnel.Packet, error) {
	// Only encrypt data portion
	encryptedData, err := e.Encrypt(packet.Data)
	if err != nil {
		return nil, err
	}

	// Create new packet
	encryptedPacket := &tunnel.Packet{
		Header: packet.Header,
		Data:   encryptedData,
	}

	// Set encryption flag
	encryptedPacket.Header.Encrypt = common.EncryptionMethod(e.method)

	return encryptedPacket, nil
}

/*****************************************************************************
 * NAME: DecryptPacket
 *
 * DESCRIPTION:
 *     Decrypts packet data using AES algorithm.
 *     Validates encryption method before decryption.
 *
 * PARAMETERS:
 *     packet - Encrypted packet to decrypt
 *
 * RETURNS:
 *     *tunnel.Packet - Decrypted packet
 *     error          - Decryption error
 *****************************************************************************/
func (e *aesEncryptor) DecryptPacket(packet *tunnel.Packet) (*tunnel.Packet, error) {
	// Check encryption method
	if packet.Header.Encrypt != common.EncryptionMethod(e.method) {
		return nil, fmt.Errorf(
			"packet encryption method mismatch: expected %d, got %d",
			e.method, packet.Header.Encrypt)
	}

	// Only decrypt data portion
	decryptedData, err := e.Decrypt(packet.Data)
	if err != nil {
		return nil, err
	}

	// Create new packet
	decryptedPacket := &tunnel.Packet{
		Header: packet.Header,
		Data:   decryptedData,
	}

	return decryptedPacket, nil
}
