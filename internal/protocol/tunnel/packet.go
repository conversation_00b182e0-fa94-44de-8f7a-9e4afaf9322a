/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      packet.go
 *
 * DESCRIPTION :    Tunnel protocol packet implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package tunnel

import (
	"bytes"
	"crypto/md5"
	"encoding/binary"
	"fmt"

	"mobile/internal/protocol/common"
)

// Use shared PacketHeader type from common package
type PacketHeader = common.PacketHeader

/*****************************************************************************
 * NAME: Packet
 *
 * DESCRIPTION:
 *     Represents a complete tunnel protocol packet with header and payload.
 *     Provides the main data structure for tunnel protocol communication.
 *
 * FIELDS:
 *     Header - Packet header containing type, encryption, session ID, and token
 *     Data   - Packet payload data
 *****************************************************************************/
type Packet struct {
	Header PacketHeader // Packet header containing type, encryption, session ID, and token
	Data   []byte       // Packet payload data
}

/*****************************************************************************
 * NAME: NewPacket
 *
 * DESCRIPTION:
 *     Creates a new tunnel protocol packet with specified parameters.
 *     Initializes packet header and payload data.
 *
 * PARAMETERS:
 *     packetType - Packet type identifier
 *     encrypt    - Encryption method to use
 *     sid        - Session ID for connection tracking
 *     token      - Authentication token
 *     data       - Packet payload data
 *
 * RETURNS:
 *     *Packet - Created packet instance
 *****************************************************************************/
func NewPacket(packetType common.PacketType, encrypt common.EncryptionMethod, sid uint16, token uint32, data []byte) *Packet {
	return &Packet{
		Header: PacketHeader{
			Type:    packetType,
			Encrypt: encrypt,
			SID:     sid,
			Token:   token,
		},
		Data: data,
	}
}

/*****************************************************************************
 * NAME: ParsePacketHeader
 *
 * DESCRIPTION:
 *     Parses tunnel protocol packet header from raw bytes.
 *     Extracts packet type, encryption method, session ID, and token.
 *
 * PARAMETERS:
 *     data - Raw packet header bytes
 *
 * RETURNS:
 *     *PacketHeader - Parsed packet header
 *     error         - Parsing error if any
 *****************************************************************************/
func ParsePacketHeader(data []byte) (*PacketHeader, error) {
	if len(data) < HeaderSize {
		return nil, fmt.Errorf(
			"packet too short for header: %d bytes (minimum %d bytes)",
			len(data), HeaderSize)
	}

	header := &PacketHeader{
		Type:    common.PacketType(data[0]),
		Encrypt: common.EncryptionMethod(data[1]),
		SID:     binary.BigEndian.Uint16(data[2:4]),
		Token:   binary.BigEndian.Uint32(data[4:8]),
	}

	return header, nil
}

/*****************************************************************************
 * NAME: ParsePacket
 *
 * DESCRIPTION:
 *     Parses complete tunnel protocol packet from raw bytes.
 *     Extracts both header and payload data.
 *
 * PARAMETERS:
 *     data - Raw packet bytes
 *
 * RETURNS:
 *     *Packet - Parsed packet instance
 *     error   - Parsing error if any
 *****************************************************************************/
func ParsePacket(data []byte) (*Packet, error) {
	header, err := ParsePacketHeader(data)
	if err != nil {
		return nil, err
	}

	packet := &Packet{
		Header: *header,
		Data:   make([]byte, len(data)-HeaderSize),
	}

	if len(data) > HeaderSize {
		copy(packet.Data, data[HeaderSize:])
	}

	return packet, nil
}

/*****************************************************************************
 * NAME: VerifySignature
 *
 * DESCRIPTION:
 *     Verifies packet signature using MD5 hash calculation.
 *     Compares calculated signature with provided signature.
 *
 * PARAMETERS:
 *     packet    - Packet to verify
 *     signature - Expected signature bytes
 *
 * RETURNS:
 *     bool - True if signature is valid
 *****************************************************************************/
func VerifySignature(packet *Packet, signature []byte) bool {
	// Create packet header bytes
	headerBytes := encodeHeader(&packet.Header)

	// Calculate MD5 hash
	md5Hash := md5.New()
	md5Hash.Write(headerBytes)

	// Add "mw" salt
	md5Hash.Write([]byte{109, 119}) // 'm', 'w'

	// Calculate hash value
	calculatedSignature := md5Hash.Sum(nil)

	// Compare signatures
	return bytes.Equal(calculatedSignature, signature)
}

/*****************************************************************************
 * NAME: encodeHeader
 *
 * DESCRIPTION:
 *     Encodes tunnel protocol packet header to bytes.
 *     Converts header fields to binary format.
 *
 * PARAMETERS:
 *     h - Packet header to encode
 *
 * RETURNS:
 *     []byte - Encoded packet header bytes
 *****************************************************************************/
func encodeHeader(h *PacketHeader) []byte {
	data := make([]byte, HeaderSize)
	data[0] = uint8(h.Type)
	data[1] = uint8(h.Encrypt)
	binary.BigEndian.PutUint16(data[2:4], h.SID)
	binary.BigEndian.PutUint32(data[4:8], h.Token)
	return data
}

/*****************************************************************************
 * NAME: Bytes
 *
 * DESCRIPTION:
 *     Returns complete tunnel protocol packet as bytes.
 *     Combines header and payload data into single byte array.
 *
 * RETURNS:
 *     []byte - Complete packet bytes
 *****************************************************************************/
func (p *Packet) Bytes() []byte {
	headerBytes := encodeHeader(&p.Header)
	data := make([]byte, len(headerBytes)+len(p.Data))
	copy(data, headerBytes)
	copy(data[len(headerBytes):], p.Data)
	return data
}

/*****************************************************************************
 * NAME: String
 *
 * DESCRIPTION:
 *     Returns string representation of the packet.
 *     Includes packet type, encryption method, session ID, token, and data length.
 *
 * RETURNS:
 *     string - Packet string representation
 *****************************************************************************/
func (p *Packet) String() string {
	var typeStr string
	switch p.Header.Type {
	case common.PacketTypeOpen:
		typeStr = "OPEN"
	case common.PacketTypeOpenAck:
		typeStr = "OPENACK"
	case common.PacketTypeOpenReject:
		typeStr = "OPENREJ"
	case common.PacketTypeData:
		typeStr = "DATA"
	case common.PacketTypeDataEncrypt:
		typeStr = "DATAENC"
	case common.PacketTypeEchoRequest:
		typeStr = "ECHOREQ"
	case common.PacketTypeEchoResponse:
		typeStr = "ECHORES"
	case common.PacketTypeClose:
		typeStr = "CLOSE"
	default:
		typeStr = fmt.Sprintf("UNKNOWN(0x%02X)", p.Header.Type)
	}

	var encryptStr string
	switch p.Header.Encrypt {
	case common.EncryptionNone:
		encryptStr = "NONE"
	case common.EncryptionXOR:
		encryptStr = "XOR"
	case common.EncryptionAES:
		encryptStr = "AES"
	default:
		encryptStr = fmt.Sprintf("UNKNOWN(0x%02X)", p.Header.Encrypt)
	}

	return fmt.Sprintf("Packet{Type=%s, Encrypt=%s, SID=0x%04X, Token=0x%08X, DataLen=%d}",
		typeStr, encryptStr, p.Header.SID, p.Header.Token, len(p.Data))
}

/*****************************************************************************
 * NAME: IsIPv4
 *
 * DESCRIPTION:
 *     Checks if packet contains IPv4 data.
 *     Examines packet type and IP version field.
 *
 * RETURNS:
 *     bool - True if packet contains IPv4 data
 *****************************************************************************/
func (p *Packet) IsIPv4() bool {
	return (p.Header.Type == common.PacketTypeData ||
		p.Header.Type == common.PacketTypeDataEncrypt ||
		p.Header.Type == common.PacketTypeDataDup ||
		p.Header.Type == common.PacketTypeDataEncDup) &&
		len(p.Data) > 0 && (p.Data[0]>>4) == 4
}

/*****************************************************************************
 * NAME: IsIPv6
 *
 * DESCRIPTION:
 *     Checks if packet contains IPv6 data.
 *     Examines packet type and IP version field.
 *
 * RETURNS:
 *     bool - True if packet contains IPv6 data
 *****************************************************************************/
func (p *Packet) IsIPv6() bool {
	return (p.Header.Type == common.PacketTypeData6 ||
		p.Header.Type == common.PacketTypeIPFrag6) &&
		len(p.Data) > 0 && (p.Data[0]>>4) == 6
}

/*****************************************************************************
 * NAME: NeedsSignatureVerification
 *
 * DESCRIPTION:
 *     Checks if packet requires signature verification.
 *     Data packets and fragment packets do not need signature verification.
 *
 * RETURNS:
 *     bool - True if packet needs signature verification
 *****************************************************************************/
func (p *Packet) NeedsSignatureVerification() bool {
	// Data packets and fragment packets do not need signature verification
	return p.Header.Type != common.PacketTypeData &&
		p.Header.Type != common.PacketTypeData6 &&
		p.Header.Type != common.PacketTypeDataEncrypt &&
		p.Header.Type != common.PacketTypeDataEncDup &&
		p.Header.Type != common.PacketTypeIPFrag &&
		p.Header.Type != common.PacketTypeIPFrag6
}

/*****************************************************************************
 * NAME: IsEncrypted
 *
 * DESCRIPTION:
 *     Checks if packet is encrypted.
 *     Examines encryption method in packet header.
 *
 * RETURNS:
 *     bool - True if packet is encrypted
 *****************************************************************************/
func (p *Packet) IsEncrypted() bool {
	return p.Header.Encrypt != common.EncryptionNone
}

/*****************************************************************************
 * NAME: IsControl
 *
 * DESCRIPTION:
 *     Checks if packet is a control packet.
 *     Control packets handle connection management and heartbeat.
 *
 * RETURNS:
 *     bool - True if packet is a control packet
 *****************************************************************************/
func (p *Packet) IsControl() bool {
	return p.Header.Type == common.PacketTypeOpen ||
		p.Header.Type == common.PacketTypeOpenAck ||
		p.Header.Type == common.PacketTypeOpenReject ||
		p.Header.Type == common.PacketTypeEchoRequest ||
		p.Header.Type == common.PacketTypeEchoResponse ||
		p.Header.Type == common.PacketTypeClose
}

/*****************************************************************************
 * NAME: IsData
 *
 * DESCRIPTION:
 *     Checks if packet is a data packet.
 *     Data packets carry IP traffic through the tunnel.
 *
 * RETURNS:
 *     bool - True if packet is a data packet
 *****************************************************************************/
func (p *Packet) IsData() bool {
	return p.Header.Type == common.PacketTypeData ||
		p.Header.Type == common.PacketTypeDataEncrypt ||
		p.Header.Type == common.PacketTypeDataDup ||
		p.Header.Type == common.PacketTypeDataEncDup ||
		p.Header.Type == common.PacketTypeData6 ||
		p.Header.Type == common.PacketTypeIPFrag ||
		p.Header.Type == common.PacketTypeIPFrag6
}
