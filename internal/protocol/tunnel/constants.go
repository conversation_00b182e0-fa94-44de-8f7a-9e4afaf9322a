/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      constants.go
 *
 * DESCRIPTION :    Tunnel protocol constants
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package tunnel

// Use shared PacketType constants from common package
import (
	"mobile/internal/protocol/common"
)

// Define aliases for compatibility with existing code
const (
	PacketTypeOpenReject   = common.PacketTypeOpenReject
	PacketTypeOpenAck      = common.PacketTypeOpenAck
	PacketTypeOpen         = common.PacketTypeOpen
	PacketTypeData         = common.PacketTypeData
	PacketTypeEchoRequest  = common.PacketTypeEchoRequest
	PacketTypeEchoResponse = common.PacketTypeEchoResponse
	PacketTypeClose        = common.PacketTypeClose
	PacketTypeDataEncrypt  = common.PacketTypeDataEncrypt
	PacketTypeDataDup      = common.PacketTypeDataDup
	PacketTypeDataEncDup   = common.PacketTypeDataEncDup
	PacketTypeIPFrag       = common.PacketTypeIPFrag
	PacketTypeData6        = common.PacketTypeData6
	PacketTypeIPFrag6      = common.PacketTypeIPFrag6
	PacketTypeSegRT        = common.PacketTypeSegRT
	PacketTypePingRequest  = common.PacketTypePingRequest
	PacketTypePingResponse = common.PacketTypePingResponse
)

// Define aliases for compatibility with existing code
const (
	EncryptionNone = common.EncryptionNone
	EncryptionXOR  = common.EncryptionXOR
	EncryptionAES  = common.EncryptionAES
)

// Define aliases for compatibility with existing code
const (
	AttributeUsername = common.AttributeUsername
	AttributePassword = common.AttributePassword
	AttributeMTU      = common.AttributeMTU
	AttributeIP       = common.AttributeIP
	AttributeDNS      = common.AttributeDNS
	AttributeGateway  = common.AttributeGateway
	AttributeNetmask  = common.AttributeNetmask
	AttributeEncrypt  = common.AttributeEncrypt
	AttributeDupPkt   = common.AttributeDupPkt
	AttributeLink     = common.AttributeLink
	AttributeIP6      = common.AttributeIP6
	AttributeDNS6     = common.AttributeDNS6
	AttributeGateway6 = common.AttributeGateway6
)

// Client state constants - consistent with SDWAN VPN protocol specification
const (
	ClientStateInit     = 2 // Initializing (DNS resolution in progress)
	ClientStateInit1    = 3 // Server IP obtained
	ClientStateAuth     = 4 // Authentication in progress
	ClientStateData     = 5 // Data transmission
	ClientStateClosed   = 6 // Closed
	ClientStateAuthFail = 7 // Authentication failed
)

// Error code constants
const (
	ErrorNone           = 0  // No error
	ErrorInvalidPacket  = 1  // Invalid packet
	ErrorAuthFailed     = 2  // Authentication failed
	ErrorConnectFailed  = 3  // Connection failed
	ErrorTimeout        = 4  // Timeout
	ErrorServerRejected = 5  // Server rejected
	ErrorInternal       = 6  // Internal error
	ErrorDisconnected   = 7  // Disconnected
	ErrorInvalidState   = 8  // Invalid state
	ErrorInvalidConfig  = 9  // Invalid configuration
	ErrorNetworkError   = 10 // Network error
)

// Define aliases for compatibility with existing code
const (
	DefaultMTU           = common.DefaultMTU
	DefaultTimeout       = common.DefaultTimeout
	DefaultRetryCount    = common.DefaultRetryCount
	DefaultRetryInterval = common.DefaultRetryInterval
	DefaultHeartbeat     = common.DefaultHeartbeat
	HeaderSize           = common.HeaderSize
	MaxPacketSize        = common.MaxPacketSize
	EchoPacketSize       = common.EchoPacketSize
	MD5Size              = common.MD5Size
	DefaultPort          = common.DefaultPort
	MagicNumber          = common.MagicNumber
	ProtocolVersion      = common.ProtocolVersion
)
