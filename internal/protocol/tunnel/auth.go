/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      auth.go
 *
 * DESCRIPTION :    Authentication related packet handling
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package tunnel

import (
	"crypto/aes"
	"crypto/md5"
	"encoding/binary"
	"fmt"
	"log"
	"net"
	"time"
	"unsafe"

	"mobile/internal/protocol/common"
)

// Authentication rejection reasons
const (
	RejectReasonUnknown      = 0 // Unknown reason
	RejectReasonInvalidUser  = 1 // Invalid username
	RejectReasonInvalidPass  = 2 // Invalid password
	RejectReasonServerFull   = 3 // Server is full
	RejectReasonServerError  = 4 // Server error
	RejectReasonUnsupported  = 5 // Unsupported feature
	RejectReasonExpired      = 6 // Account expired
	RejectReasonDisabled     = 7 // Account disabled
	RejectReasonMaxSessions  = 8 // Maximum sessions reached
	RejectReasonInvalidToken = 9 // Invalid token
)

/*****************************************************************************
 * NAME: AuthResult
 *
 * DESCRIPTION:
 *     Represents authentication result with success status and configuration.
 *     Contains session information and server-provided configuration.
 *
 * FIELDS:
 *     Success      - Whether authentication was successful
 *     ErrorCode    - Error code if authentication failed
 *     ErrorMessage - Error message description
 *     SessionID    - Session ID assigned by server
 *     Token        - Authentication token for subsequent requests
 *     Config       - Configuration parameters from server
 *****************************************************************************/
type AuthResult struct {
	Success      bool              // Whether authentication was successful
	ErrorCode    int               // Error code if authentication failed
	ErrorMessage string            // Error message description
	SessionID    uint16            // Session ID assigned by server
	Token        uint32            // Authentication token for subsequent requests
	Config       map[string]string // Configuration parameters from server
}

/*****************************************************************************
 * NAME: CreateOpenPacket
 *
 * DESCRIPTION:
 *     Creates an Open packet for authentication request.
 *     Constructs packet with username, encrypted password, MTU, and encryption method.
 *     Implementation consistent with Android client.
 *
 * PARAMETERS:
 *     username      - Username for authentication
 *     password      - Password for authentication
 *     mtu           - Maximum Transmission Unit value
 *     encryptMethod - Encryption method to use
 *
 * RETURNS:
 *     *Packet - Created Open packet
 *     error   - Creation error if any
 *****************************************************************************/
func CreateOpenPacket(username, password string, mtu int, encryptMethod uint8) (*Packet, error) {
	// Create data buffer - consistent with Android implementation
	buffer := make([]byte, 1024)
	pos := 0

	// Calculate MD5 hash
	header := make([]byte, 8)
	header[0] = byte(PacketTypeOpen)
	header[1] = encryptMethod
	// header[2:8] are 0 (SID and Token)

	// Calculate MD5 hash (header + "mw")
	salt := []byte{109, 119} // 'm', 'w'
	md5Hash := calculateMD5(header, salt)

	// Add MD5 hash
	copy(buffer[pos:], md5Hash)
	pos += len(md5Hash)

	// Add MTU attribute (must be first attribute) - consistent with Android implementation
	buffer[pos] = byte(AttributeMTU)
	buffer[pos+1] = 4 // Length (including type and length bytes)
	// Note: MTU value uses big-endian (high byte first)
	buffer[pos+2] = byte((mtu >> 8) & 0xff) // MTU high byte
	buffer[pos+3] = byte(mtu & 0xff)        // MTU low byte
	pos += 4

	// Add username attribute (must be second attribute) - consistent with Android implementation
	usernameBytes := []byte(username)
	if len(usernameBytes) > 255 {
		return nil, fmt.Errorf("username too long: %d bytes (maximum 255)", len(usernameBytes))
	}
	buffer[pos] = byte(AttributeUsername)
	buffer[pos+1] = byte(len(usernameBytes) + 2) // Length includes type and length bytes
	pos += 2
	copy(buffer[pos:], usernameBytes)
	pos += len(usernameBytes)

	// Add password attribute - consistent with Android implementation
	buffer[pos] = byte(AttributePassword)
	buffer[pos+1] = 0x12 // Fixed length (16 bytes MD5 + 2 bytes header)
	pos += 2

	// Calculate password hash
	// 1. Calculate MD5 hash of "mw" + username
	passwordSalt := calculateMD5(salt, usernameBytes)

	// 2. Use AES-ECB to encrypt password
	encryptedPassword, err := encryptPasswordAES(password, passwordSalt)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt password: %w", err)
	}

	// Add encrypted password - consistent with Android implementation
	copy(buffer[pos:], encryptedPassword)
	pos += len(encryptedPassword)

	// Add encryption attribute (if encryption enabled) - consistent with Android implementation
	if encryptMethod != uint8(common.EncryptionNone) {
		buffer[pos] = byte(AttributeEncrypt)
		buffer[pos+1] = 3 // Length
		buffer[pos+2] = encryptMethod
		pos += 3
	}

	// Create packet - consistent with Android implementation
	packet := NewPacket(
		common.PacketTypeOpen,
		common.EncryptionMethod(encryptMethod),
		0, // SID is 0
		0, // Token is 0
		buffer[:pos],
	)

	return packet, nil
}

/*****************************************************************************
 * NAME: ParseOpenAckPacket
 *
 * DESCRIPTION:
 *     Parses OpenAck packet to extract authentication result and configuration.
 *     Extracts TLV attributes including IP, DNS, gateway, and other settings.
 *
 * PARAMETERS:
 *     packet - OpenAck packet to parse
 *
 * RETURNS:
 *     *AuthResult - Authentication result with configuration
 *     error       - Parsing error if any
 *****************************************************************************/
func ParseOpenAckPacket(packet *Packet) (*AuthResult, error) {
	// Check packet type
	if packet.Header.Type != PacketTypeOpenAck {
		return nil, fmt.Errorf(
			"invalid packet type: expected %d, got %d",
			PacketTypeOpenAck, packet.Header.Type)
	}

	// Create authentication result
	result := &AuthResult{
		Success:   true,
		SessionID: packet.Header.SID,
		Token:     packet.Header.Token,
		Config:    make(map[string]string),
	}

	// Parse TLV attributes
	data := packet.Data
	pos := 0

	// Check if packet contains MD5 signature
	// According to Android implementation, first 16 bytes of OpenAck packet are MD5 signature
	if len(data) >= 16 {
		// Skip MD5 signature
		pos = 16
	}
	for pos < len(data) {
		// Check if there's enough data to read type and length
		if pos+2 > len(data) {
			break
		}

		// Get attribute type and length
		attrType := data[pos]
		attrLen := int(data[pos+1])

		// Check if length is reasonable (at least includes type and length fields)
		if attrLen < 2 {
			// Invalid length field, try to skip this attribute
			pos++
			continue
		}

		// Check if length exceeds remaining data
		if pos+attrLen > len(data) {
			// Try to determine expected length based on attribute type
			expectedLen := getExpectedAttributeLength(common.AttributeType(attrType))
			if expectedLen > 0 && pos+expectedLen <= len(data) {
				// Use expected length instead of declared length
				attrLen = expectedLen
			} else {
				// Cannot fix, skip remaining data
				break
			}
		}

		// Parse attribute value
		switch common.AttributeType(attrType) {
		case common.AttributeIP:
			if attrLen >= 6 { // Type(1) + Length(1) + IP(4)
				ip := net.IPv4(data[pos+2], data[pos+3], data[pos+4], data[pos+5])
				result.Config["ip"] = ip.String()
			}
		case common.AttributeNetmask:
			if attrLen >= 6 { // Type(1) + Length(1) + Mask(4)
				mask := net.IPv4(data[pos+2], data[pos+3], data[pos+4], data[pos+5])
				result.Config["netmask"] = mask.String()
			}
		case common.AttributeGateway:
			if attrLen >= 6 { // Type(1) + Length(1) + Gateway(4)
				gateway := net.IPv4(data[pos+2], data[pos+3], data[pos+4], data[pos+5])
				result.Config["gateway"] = gateway.String()
			}
		case common.AttributeDNS:
			// DNS attribute may contain one or two IP addresses
			if attrLen >= 6 { // At least one IP address
				dns1 := net.IPv4(data[pos+2], data[pos+3], data[pos+4], data[pos+5])
				result.Config["dns"] = dns1.String()

				// Log first DNS server
				log.Printf("Parsed primary DNS server: %s", dns1.String())

				// Check if there's a second DNS address
				if attrLen >= 10 && pos+10 <= len(data) {
					dns2 := net.IPv4(data[pos+6], data[pos+7], data[pos+8], data[pos+9])
					result.Config["dns2"] = dns2.String()

					// Log second DNS server
					log.Printf("Parsed secondary DNS server: %s", dns2.String())
				}
			}
		case common.AttributeMTU:
			if attrLen >= 4 && pos+4 <= len(data) { // Type(1) + Length(1) + MTU(2)
				mtu := binary.BigEndian.Uint16(data[pos+2 : pos+4])
				result.Config["mtu"] = fmt.Sprintf("%d", mtu)
			}
		case common.AttributeIP6:
			if attrLen >= 18 && pos+18 <= len(data) { // Type(1) + Length(1) + IPv6(16)
				ip6 := net.IP(data[pos+2 : pos+18])
				result.Config["ip6"] = ip6.String()
			}
		case common.AttributeDNS6:
			if attrLen >= 18 && pos+18 <= len(data) { // Type(1) + Length(1) + IPv6 DNS(16)
				dns6 := net.IP(data[pos+2 : pos+18])
				result.Config["dns6"] = dns6.String()
			}
		case common.AttributeGateway6:
			if attrLen >= 18 && pos+18 <= len(data) { // Type(1) + Length(1) + IPv6 Gateway(16)
				gateway6 := net.IP(data[pos+2 : pos+18])
				result.Config["gateway6"] = gateway6.String()
			}
		case common.AttributeEncrypt:
			if attrLen >= 3 && pos+3 <= len(data) { // Type(1) + Length(1) + Encryption method(1)
				result.Config["encrypt"] = fmt.Sprintf("%d", data[pos+2])
			}
		case common.AttributeDupPkt:
			if attrLen >= 3 && pos+3 <= len(data) { // Type(1) + Length(1) + Duplicate packet flag(1)
				result.Config["duppkt"] = fmt.Sprintf("%d", data[pos+2])
			}
		}

		// Move to next attribute
		pos += attrLen
	}

	return result, nil
}

/*****************************************************************************
 * NAME: ParseOpenRejectPacket
 *
 * DESCRIPTION:
 *     Parses OpenReject packet to extract rejection reason.
 *     Extracts error code and converts it to human-readable message.
 *
 * PARAMETERS:
 *     packet - OpenReject packet to parse
 *
 * RETURNS:
 *     *AuthResult - Authentication result with error information
 *     error       - Parsing error if any
 *****************************************************************************/
func ParseOpenRejectPacket(packet *Packet) (*AuthResult, error) {
	// Check packet type
	if packet.Header.Type != PacketTypeOpenReject {
		return nil, fmt.Errorf(
			"invalid packet type: expected %d, got %d",
			PacketTypeOpenReject, packet.Header.Type)
	}

	// Create authentication result
	result := &AuthResult{
		Success:   false,
		SessionID: packet.Header.SID,
		Token:     packet.Header.Token,
		Config:    make(map[string]string),
	}

	// Check if packet contains MD5 signature
	// According to Android implementation, first 16 bytes of OpenReject packet are MD5 signature
	pos := 0
	if len(packet.Data) >= 16 {
		// Skip MD5 signature
		pos = 16
	}

	// Parse rejection reason
	if pos < len(packet.Data) {
		reason := packet.Data[pos]
		result.ErrorCode = int(reason)
		result.ErrorMessage = getRejectReasonString(reason)
	} else {
		result.ErrorCode = RejectReasonUnknown
		result.ErrorMessage = "Unknown rejection reason"
	}

	return result, nil
}

/*****************************************************************************
 * NAME: CreateClosePacket
 *
 * DESCRIPTION:
 *     Creates a Close packet to terminate connection.
 *     Used to gracefully close the tunnel connection.
 *
 * PARAMETERS:
 *     sessionID     - Session ID for the connection
 *     token         - Authentication token
 *     encryptMethod - Encryption method to use
 *
 * RETURNS:
 *     *Packet - Created Close packet
 *     error   - Creation error if any
 *****************************************************************************/
func CreateClosePacket(sessionID uint16, token uint32, encryptMethod uint8) (*Packet, error) {
	// Create Close packet
	packet := NewPacket(
		common.PacketTypeClose,
		common.EncryptionMethod(encryptMethod),
		sessionID,
		token,
		nil, // No data
	)

	return packet, nil
}

/*****************************************************************************
 * NAME: CreateEchoRequestPacket
 *
 * DESCRIPTION:
 *     Creates an EchoRequest packet for heartbeat/keepalive.
 *     Includes timestamp and delay information for latency measurement.
 *
 * PARAMETERS:
 *     sessionID     - Session ID for the connection
 *     token         - Authentication token
 *     curDelay      - Current delay measurement
 *     minDelay      - Minimum delay observed
 *     maxDelay      - Maximum delay observed
 *     encryptMethod - Encryption method to use
 *
 * RETURNS:
 *     *Packet - Created EchoRequest packet
 *     error   - Creation error if any
 *****************************************************************************/
func CreateEchoRequestPacket(sessionID uint16, token uint32, curDelay, minDelay, maxDelay uint32, encryptMethod uint8) (*Packet, error) {
	// Create data buffer
	buffer := make([]byte, 1024)
	pos := 0

	// Create packet header
	header := make([]byte, 8)
	header[0] = byte(PacketTypeEchoRequest)
	header[1] = encryptMethod
	binary.BigEndian.PutUint16(header[2:4], sessionID)
	binary.BigEndian.PutUint32(header[4:8], token)

	// Calculate MD5 signature (header + "mw")
	salt := []byte{109, 119} // 'm', 'w'
	md5Hash := calculateMD5(header, salt)

	// Add MD5 signature
	copy(buffer[pos:], md5Hash)
	pos += len(md5Hash)

	// Add timestamp (microseconds)
	binary.BigEndian.PutUint64(buffer[pos:pos+8], uint64(time.Now().UnixNano()/1000))
	pos += 8

	// Add delay information
	binary.BigEndian.PutUint32(buffer[pos:pos+4], curDelay)
	pos += 4
	binary.BigEndian.PutUint32(buffer[pos:pos+4], minDelay)
	pos += 4
	binary.BigEndian.PutUint32(buffer[pos:pos+4], maxDelay)
	pos += 4

	// Add SDRT tag
	buffer[pos] = 'S'
	buffer[pos+1] = 'D'
	buffer[pos+2] = 'R'
	buffer[pos+3] = 'T'
	pos += 4

	// Create packet
	packet := NewPacket(
		common.PacketTypeEchoRequest,
		common.EncryptionMethod(encryptMethod),
		sessionID,
		token,
		buffer[:pos],
	)

	return packet, nil
}

/*****************************************************************************
 * NAME: ParseEchoResponsePacket
 *
 * DESCRIPTION:
 *     Parses EchoResponse packet to extract timing and delay information.
 *     Used for calculating round-trip time and network performance metrics.
 *
 * PARAMETERS:
 *     packet - EchoResponse packet to parse
 *
 * RETURNS:
 *     uint64 - Timestamp from the echo request
 *     uint32 - Current delay measurement
 *     uint32 - Minimum delay observed
 *     uint32 - Maximum delay observed
 *     error  - Parsing error if any
 *****************************************************************************/
func ParseEchoResponsePacket(packet *Packet) (uint64, uint32, uint32, uint32, error) {
	// Check packet type
	if packet.Header.Type != PacketTypeEchoResponse {
		return 0, 0, 0, 0, fmt.Errorf(
			"invalid packet type: expected %d, got %d",
			PacketTypeEchoResponse, packet.Header.Type)
	}

	// Check data length (includes 16-byte MD5 signature)
	if len(packet.Data) < 36 {
		return 0, 0, 0, 0, fmt.Errorf(
			"echo response packet too short: %d bytes (minimum 36 bytes)",
			len(packet.Data))
	}

	// Skip 16-byte MD5 signature
	// Parse data
	timestamp := binary.BigEndian.Uint64(packet.Data[16:24])
	curDelay := binary.BigEndian.Uint32(packet.Data[24:28])
	minDelay := binary.BigEndian.Uint32(packet.Data[28:32])
	maxDelay := binary.BigEndian.Uint32(packet.Data[32:36])

	return timestamp, curDelay, minDelay, maxDelay, nil
}

/*****************************************************************************
 * NAME: calculateMD5
 *
 * DESCRIPTION:
 *     Calculates MD5 hash of concatenated data.
 *     Used for packet signature generation and password encryption.
 *
 * PARAMETERS:
 *     data - Variable number of byte arrays to hash
 *
 * RETURNS:
 *     []byte - MD5 hash result
 *****************************************************************************/
func calculateMD5(data ...[]byte) []byte {
	hash := md5.New()
	for _, d := range data {
		hash.Write(d)
	}
	return hash.Sum(nil)
}

/*****************************************************************************
 * NAME: encryptPasswordXOR
 *
 * DESCRIPTION:
 *     Encrypts password using XOR algorithm.
 *     Uses same XOR encryption method as pa_mobile implementation.
 *     Note: This function is currently unused but kept for compatibility.
 *
 * PARAMETERS:
 *     password - Password to encrypt
 *     key      - Encryption key
 *
 * RETURNS:
 *     []byte - Encrypted password
 *     error  - Encryption error if any
 *****************************************************************************/
func encryptPasswordXOR(password string, key []byte) ([]byte, error) {
	// Pad password to 16 bytes
	paddedPassword := make([]byte, 16)
	copy(paddedPassword, []byte(password))

	// Encrypt password
	encryptedPassword := make([]byte, 16)
	copy(encryptedPassword, paddedPassword)

	// Use same XOR encryption method as pa_mobile
	// Process 8 bytes at a time for XOR operation
	for i := 0; i < len(encryptedPassword)/8; i++ {
		// Treat data and key as uint32 arrays
		data32 := (*[2]uint32)(unsafe.Pointer(&encryptedPassword[i*8]))
		key32 := (*[2]uint32)(unsafe.Pointer(&key[0]))

		// XOR operation
		data32[0] ^= key32[0]
		data32[1] ^= key32[1]
	}

	// Process remaining bytes
	remain := len(encryptedPassword) % 8
	if remain > 0 {
		for i := 0; i < remain; i++ {
			encryptedPassword[len(encryptedPassword)-remain+i] ^= key[i]
		}
	}

	return encryptedPassword, nil
}

/*****************************************************************************
 * NAME: encryptPasswordAES
 *
 * DESCRIPTION:
 *     Encrypts password using AES-ECB algorithm.
 *     Implementation consistent with Android client using AES/ECB/NoPadding.
 *
 * PARAMETERS:
 *     password - Password to encrypt
 *     key      - AES encryption key
 *
 * RETURNS:
 *     []byte - Encrypted password (first 16 bytes)
 *     error  - Encryption error if any
 *****************************************************************************/
func encryptPasswordAES(password string, key []byte) ([]byte, error) {
	// Create AES cipher block
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	// Pad password to 32 bytes, consistent with Android implementation
	// Note: Android implementation uses Arrays.copyOf(password.getBytes(StandardCharsets.US_ASCII), 32)
	passwordBytes := []byte(password)
	paddedPassword := make([]byte, 32)
	copy(paddedPassword, passwordBytes)

	// Encrypt password
	encryptedPassword := make([]byte, 32)

	// AES-ECB mode, block encryption
	// Note: Android implementation uses Cipher.getInstance("AES/ECB/NoPadding")
	for i := 0; i < len(paddedPassword); i += 16 {
		block.Encrypt(encryptedPassword[i:i+16], paddedPassword[i:i+16])
	}

	// Return only first 16 bytes, consistent with Android implementation
	// Note: Android implementation uses buffer.put(s2, 0, 16)
	return encryptedPassword[:16], nil
}

/*****************************************************************************
 * NAME: getRejectReasonString
 *
 * DESCRIPTION:
 *     Converts rejection reason code to human-readable string.
 *     Provides descriptive error messages for authentication failures.
 *
 * PARAMETERS:
 *     reason - Rejection reason code
 *
 * RETURNS:
 *     string - Human-readable rejection reason
 *****************************************************************************/
func getRejectReasonString(reason uint8) string {
	switch reason {
	case RejectReasonInvalidUser:
		return "Invalid username"
	case RejectReasonInvalidPass:
		return "Invalid password"
	case RejectReasonServerFull:
		return "Server is full"
	case RejectReasonServerError:
		return "Server error"
	case RejectReasonUnsupported:
		return "Unsupported feature"
	case RejectReasonExpired:
		return "Account expired"
	case RejectReasonDisabled:
		return "Account disabled"
	case RejectReasonMaxSessions:
		return "Maximum sessions reached"
	case RejectReasonInvalidToken:
		return "Invalid token"
	default:
		return fmt.Sprintf("Unknown reason (%d)", reason)
	}
}

/*****************************************************************************
 * NAME: getExpectedAttributeLength
 *
 * DESCRIPTION:
 *     Returns expected length for TLV attribute types.
 *     Used for packet parsing error recovery.
 *
 * PARAMETERS:
 *     attrType - TLV attribute type
 *
 * RETURNS:
 *     int - Expected length in bytes, 0 if unknown
 *****************************************************************************/
func getExpectedAttributeLength(attrType common.AttributeType) int {
	switch attrType {
	case common.AttributeIP, common.AttributeNetmask, common.AttributeGateway:
		return 6 // Type(1) + Length(1) + IPv4(4)
	case common.AttributeDNS:
		return 10 // Type(1) + Length(1) + IPv4(4) * 2
	case common.AttributeMTU:
		return 4 // Type(1) + Length(1) + MTU(2)
	case common.AttributeIP6, common.AttributeDNS6, common.AttributeGateway6:
		return 18 // Type(1) + Length(1) + IPv6(16)
	case common.AttributeEncrypt, common.AttributeDupPkt:
		return 3 // Type(1) + Length(1) + Value(1)
	default:
		return 0 // Unknown type
	}
}
