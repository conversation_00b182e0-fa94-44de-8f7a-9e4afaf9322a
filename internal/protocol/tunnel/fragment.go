/*****************************************************************************
 * FILE NAME :    fragment.go
 *
 * DESCRIPTION :  IP packet fragmentation and reassembly functionality.
 *                Handles fragmented IP packets for tunnel protocol.
 *
 * AUTHOR :       wei
 * HISTORY :      10/06/2025 create
 *****************************************************************************/

package tunnel

import (
	"encoding/binary"
	"errors"
	"sync"
	"time"

	"mobile/internal/common/logger"
)

// Error definitions
var (
	ErrInvalidPacket = errors.New("invalid packet")
	ErrInvalidLength = errors.New("invalid length")
	ErrTimeout       = errors.New("fragment timeout")
)

// Fragment processing constants
const (
	FragQueueCapacity = 8    // Fragment queue capacity
	FragTimeout       = 100  // Fragment timeout (milliseconds)
	MaxFragDataSize   = 2048 // Maximum fragment data size
)

/*****************************************************************************
 * NAME: IPFragment
 *
 * DESCRIPTION:
 *     Represents an IP packet fragment with metadata.
 *     Contains fragment identification, position, and timing information.
 *
 * FIELDS:
 *     ID         - Fragment identifier for grouping related fragments
 *     EOP        - End of Packet flag (1 if last fragment, 0 otherwise)
 *     Offset     - Fragment offset within the original packet
 *     Length     - Length of fragment data
 *     Data       - Fragment payload data
 *     InsertTime - Time when fragment was added to queue
 *****************************************************************************/
type IPFragment struct {
	ID         uint32    // Fragment identifier for grouping related fragments
	EOP        uint8     // End of Packet flag (1 if last fragment, 0 otherwise)
	Offset     uint16    // Fragment offset within the original packet
	Length     uint16    // Length of fragment data
	Data       []byte    // Fragment payload data
	InsertTime time.Time // Time when fragment was added to queue
}

/*****************************************************************************
 * NAME: FragmentQueue
 *
 * DESCRIPTION:
 *     Queue for managing IP packet fragments and reassembly.
 *     Provides thread-safe fragment storage with timeout handling.
 *
 * FIELDS:
 *     Fragments - Array of fragment lists for different packet IDs
 *     mu        - Mutex for thread-safe access
 *     logger    - Logger instance for fragment operations
 *****************************************************************************/
type FragmentQueue struct {
	Fragments [FragQueueCapacity][]*IPFragment // Array of fragment lists for different packet IDs
	mu        sync.Mutex                       // Mutex for thread-safe access
	logger    logger.Logger                    // Logger instance for fragment operations
}

/*****************************************************************************
 * NAME: NewFragmentQueue
 *
 * DESCRIPTION:
 *     Creates a new fragment queue for IP packet reassembly.
 *     Initializes queue with empty fragment slots and logger.
 *
 * PARAMETERS:
 *     log - Logger instance for fragment operations
 *
 * RETURNS:
 *     *FragmentQueue - New fragment queue instance
 *****************************************************************************/
func NewFragmentQueue(log logger.Logger) *FragmentQueue {
	return &FragmentQueue{
		Fragments: [FragQueueCapacity][]*IPFragment{},
		logger:    log.WithModule("fragment-queue"),
	}
}

/*****************************************************************************
 * NAME: ParseFragment
 *
 * DESCRIPTION:
 *     Parses fragment data from raw bytes.
 *     Based on Android implementation, does not strictly check data length.
 *
 * PARAMETERS:
 *     data - Raw fragment bytes to parse
 *
 * RETURNS:
 *     *IPFragment - Parsed fragment structure
 *     error       - Parsing error if data is invalid
 *****************************************************************************/
func ParseFragment(data []byte) (*IPFragment, error) {
	if len(data) < 8 {
		return nil, ErrInvalidPacket
	}

	// Parse fragment header
	id := binary.BigEndian.Uint32(data[0:4])
	fragInfo := binary.BigEndian.Uint32(data[4:8])

	// Extract fragment information
	// Note: Bit operations consistent with Java implementation
	eop := uint8((fragInfo >> 31) & 0x01)
	offset := uint16((fragInfo >> 18) & 0x1FFF)
	length := uint16((fragInfo >> 7) & 0x7FF)

	// Check if length field is reasonable
	if length == 0 || length > MaxFragDataSize {
		return nil, ErrInvalidLength
	}

	// Create fragment object
	fragment := &IPFragment{
		ID:         id,
		EOP:        eop,
		Offset:     offset,
		Length:     length,
		Data:       make([]byte, length),
		InsertTime: time.Now(),
	}

	// Copy data, ensuring no buffer overflow
	// If actual data is insufficient, only copy available portion
	actualDataLength := len(data) - 8
	if actualDataLength <= 0 {
		// No available data
		return fragment, nil
	}

	// Copy available data
	copyLength := actualDataLength
	if copyLength > int(length) {
		copyLength = int(length)
	}

	copy(fragment.Data, data[8:8+copyLength])

	return fragment, nil
}

/*****************************************************************************
 * NAME: AddFragment
 *
 * DESCRIPTION:
 *     Adds a fragment to the queue and attempts reassembly.
 *     Returns complete IP packet data if reassembly is successful.
 *
 * PARAMETERS:
 *     fragment - IP fragment to add to the queue
 *
 * RETURNS:
 *     []byte - Complete reassembled packet data (nil if not ready)
 *     bool   - True if reassembly was successful
 *****************************************************************************/
func (q *FragmentQueue) AddFragment(fragment *IPFragment) ([]byte, bool) {
	// 	q.mu.Lock()
	// 	defer q.mu.Unlock()

	// Find matching fragment group
	for i := 0; i < FragQueueCapacity; i++ {
		fragments := q.Fragments[i]

		// If this slot has fragments, check if ID matches
		if len(fragments) > 0 {
			if fragments[0].ID == fragment.ID {
				// Check timeout
				if time.Since(fragments[0].InsertTime).Milliseconds() <= FragTimeout {
					// Add new fragment
					if fragment.EOP == 0 {
						// Non-ending fragment, add to beginning
						q.Fragments[i] = append([]*IPFragment{fragment}, fragments...)
					} else {
						// Ending fragment, add to end
						q.Fragments[i] = append(fragments, fragment)
					}

					// Attempt reassembly
					return q.reassembleFragments(i)
				}

				// Timeout, clear this slot
				q.Fragments[i] = nil
				// Add new fragment to this slot
				q.Fragments[i] = []*IPFragment{fragment}
				return nil, false
			}
		}
	}

	// No matching fragment group found, look for empty slot
	freeIndex := -1

	for i := 0; i < FragQueueCapacity; i++ {
		fragments := q.Fragments[i]
		if len(fragments) == 0 {
			// Found empty slot
			freeIndex = i
			break
		} else if time.Since(fragments[0].InsertTime).Milliseconds() > FragTimeout {
			// Found timeout slot
			q.Fragments[i] = nil
			freeIndex = i
			break
		}
	}

	// If empty slot found, add new fragment
	if freeIndex >= 0 {
		q.Fragments[freeIndex] = []*IPFragment{fragment}
	} else {
		q.logger.Warn("Fragment queue is full, dropping fragment",
			logger.Int("id", int(fragment.ID)),
			logger.Int("eop", int(fragment.EOP)),
			logger.Int("offset", int(fragment.Offset)),
			logger.Int("length", int(fragment.Length)))
	}

	return nil, false
}

/*****************************************************************************
 * NAME: reassembleFragments
 *
 * DESCRIPTION:
 *     Reassembles fragments into complete IP packet.
 *     Returns reassembled packet if all fragments are received.
 *
 * PARAMETERS:
 *     index - Index of fragment slot to reassemble
 *
 * RETURNS:
 *     []byte - Reassembled complete IP packet data
 *     bool   - True if reassembly was successful
 *****************************************************************************/
func (q *FragmentQueue) reassembleFragments(index int) ([]byte, bool) {
	fragments := q.Fragments[index]
	if len(fragments) == 0 {
		return nil, false
	}

	// Calculate total length
	totalLength := 0
	for _, frag := range fragments {
		totalLength += int(frag.Length)
	}

	// Create buffer
	reassembled := make([]byte, totalLength)
	offset := 0

	// Copy data in order
	for _, frag := range fragments {
		copy(reassembled[offset:], frag.Data[:frag.Length])
		offset += int(frag.Length)
	}

	// Clear this slot
	q.Fragments[index] = nil

	q.logger.Debug("Fragment reassembly completed",
		logger.Int("fragments", len(fragments)),
		logger.Int("total_length", totalLength),
		logger.Int("id", int(fragments[0].ID)))

	return reassembled, true
}
