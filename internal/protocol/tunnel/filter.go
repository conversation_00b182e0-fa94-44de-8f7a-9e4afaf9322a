/*****************************************************************************
 * FILE NAME :    filter.go
 *
 * DESCRIPTION :  Packet filtering functionality for tunnel protocol.
 *                Provides IP packet parsing and filtering based on broadcast/multicast.
 *
 * AUTHOR :       wei
 * HISTORY :      10/06/2025 create
 *****************************************************************************/

package tunnel

import (
	"encoding/binary"
	"fmt"
	"net"
)

/*****************************************************************************
 * NAME: IPv4Header
 *
 * DESCRIPTION:
 *     Represents an IPv4 packet header with all standard fields.
 *     Used for parsing and analyzing IPv4 packets.
 *
 * FIELDS:
 *     Version        - IP version (always 4 for IPv4)
 *     IHL            - Internet Header Length in 32-bit words
 *     TOS            - Type of Service field
 *     TotalLength    - Total packet length including header and data
 *     ID             - Identification field for fragmentation
 *     Flags          - Control flags (DF, MF, etc.)
 *     FragmentOffset - Fragment offset for reassembly
 *     TTL            - Time to Live
 *     Protocol       - Next level protocol identifier
 *     Checksum       - Header checksum
 *     SourceIP       - Source IP address
 *     DestinationIP  - Destination IP address
 *****************************************************************************/
type IPv4Header struct {
	Version        uint8  // IP version (always 4 for IPv4)
	IHL            uint8  // Internet Header Length in 32-bit words
	TOS            uint8  // Type of Service field
	TotalLength    uint16 // Total packet length including header and data
	ID             uint16 // Identification field for fragmentation
	Flags          uint8  // Control flags (DF, MF, etc.)
	FragmentOffset uint16 // Fragment offset for reassembly
	TTL            uint8  // Time to Live
	Protocol       uint8  // Next level protocol identifier
	Checksum       uint16 // Header checksum
	SourceIP       net.IP // Source IP address
	DestinationIP  net.IP // Destination IP address
}

/*****************************************************************************
 * NAME: ParseIPv4Header
 *
 * DESCRIPTION:
 *     Parses IPv4 header from raw packet bytes.
 *     Extracts all standard IPv4 header fields.
 *
 * PARAMETERS:
 *     data - Raw packet bytes containing IPv4 header
 *
 * RETURNS:
 *     *IPv4Header - Parsed IPv4 header structure
 *     error       - Parsing error if data is too short
 *****************************************************************************/
func ParseIPv4Header(data []byte) (*IPv4Header, error) {
	if len(data) < 20 {
		return nil, fmt.Errorf("data too short for IPv4 header")
	}

	header := &IPv4Header{
		Version:        data[0] >> 4,
		IHL:            data[0] & 0x0F,
		TOS:            data[1],
		TotalLength:    binary.BigEndian.Uint16(data[2:4]),
		ID:             binary.BigEndian.Uint16(data[4:6]),
		Flags:          data[6] >> 5,
		FragmentOffset: binary.BigEndian.Uint16(data[6:8]) & 0x1FFF,
		TTL:            data[8],
		Protocol:       data[9],
		Checksum:       binary.BigEndian.Uint16(data[10:12]),
		SourceIP:       net.IP(data[12:16]),
		DestinationIP:  net.IP(data[16:20]),
	}

	return header, nil
}

/*****************************************************************************
 * NAME: IPv6Header
 *
 * DESCRIPTION:
 *     Represents an IPv6 packet header with all standard fields.
 *     Used for parsing and analyzing IPv6 packets.
 *
 * FIELDS:
 *     Version       - IP version (always 6 for IPv6)
 *     TrafficClass  - Traffic class for QoS
 *     FlowLabel     - Flow label for packet flow identification
 *     PayloadLength - Length of payload following the header
 *     NextHeader    - Next header protocol identifier
 *     HopLimit      - Maximum number of hops
 *     SourceIP      - Source IP address (128-bit)
 *     DestinationIP - Destination IP address (128-bit)
 *****************************************************************************/
type IPv6Header struct {
	Version       uint8  // IP version (always 6 for IPv6)
	TrafficClass  uint8  // Traffic class for QoS
	FlowLabel     uint32 // Flow label for packet flow identification
	PayloadLength uint16 // Length of payload following the header
	NextHeader    uint8  // Next header protocol identifier
	HopLimit      uint8  // Maximum number of hops
	SourceIP      net.IP // Source IP address (128-bit)
	DestinationIP net.IP // Destination IP address (128-bit)
}

/*****************************************************************************
 * NAME: ParseIPv6Header
 *
 * DESCRIPTION:
 *     Parses IPv6 header from raw packet bytes.
 *     Extracts all standard IPv6 header fields.
 *
 * PARAMETERS:
 *     data - Raw packet bytes containing IPv6 header
 *
 * RETURNS:
 *     *IPv6Header - Parsed IPv6 header structure
 *     error       - Parsing error if data is too short
 *****************************************************************************/
func ParseIPv6Header(data []byte) (*IPv6Header, error) {
	if len(data) < 40 {
		return nil, fmt.Errorf("data too short for IPv6 header")
	}

	header := &IPv6Header{
		Version:       data[0] >> 4,
		TrafficClass:  ((data[0] & 0x0F) << 4) | (data[1] >> 4),
		FlowLabel:     uint32(data[1]&0x0F)<<16 | uint32(data[2])<<8 | uint32(data[3]),
		PayloadLength: binary.BigEndian.Uint16(data[4:6]),
		NextHeader:    data[6],
		HopLimit:      data[7],
		SourceIP:      net.IP(data[8:24]),
		DestinationIP: net.IP(data[24:40]),
	}

	return header, nil
}

/*****************************************************************************
 * NAME: IsIPv4Broadcast
 *
 * DESCRIPTION:
 *     Checks if IPv4 address is a broadcast address.
 *     Broadcast addresses include:
 *     - *************** (global broadcast)
 *     - Subnet broadcast addresses (e.g., ************* for ***********/24)
 *
 * PARAMETERS:
 *     ip         - IPv4 address to check
 *     subnetMask - Subnet mask, if nil only checks global broadcast
 *
 * RETURNS:
 *     bool - True if address is a broadcast address
 *****************************************************************************/
func IsIPv4Broadcast(ip net.IP, subnetMask net.IPMask) bool {
	// Check if it's an IPv4 address
	ipv4 := ip.To4()
	if ipv4 == nil {
		return false
	}

	// Check if it's global broadcast address ***************
	if ipv4.Equal(net.IPv4bcast) {
		return true
	}

	// If subnet mask is provided, check if it's subnet broadcast address
	if subnetMask != nil {
		// Calculate subnet broadcast address
		// Subnet broadcast address = network address | ~subnet mask
		network := make(net.IP, len(ipv4))
		broadcast := make(net.IP, len(ipv4))

		for i := 0; i < len(ipv4); i++ {
			network[i] = ipv4[i] & subnetMask[i]
			broadcast[i] = network[i] | ^subnetMask[i]
		}

		// Check if it's subnet broadcast address
		return ipv4.Equal(broadcast)
	}

	return false
}

/*****************************************************************************
 * NAME: IsIPv4Multicast
 *
 * DESCRIPTION:
 *     Checks if IPv4 address is a multicast address.
 *     IPv4 multicast address range: ********* - ***************
 *
 * PARAMETERS:
 *     ip - IPv4 address to check
 *
 * RETURNS:
 *     bool - True if address is a multicast address
 *****************************************************************************/
func IsIPv4Multicast(ip net.IP) bool {
	return ip.IsMulticast()
}

/*****************************************************************************
 * NAME: IsIPv6Multicast
 *
 * DESCRIPTION:
 *     Checks if IPv6 address is a multicast address.
 *     IPv6 multicast address prefix: ff00::/8
 *
 * PARAMETERS:
 *     ip - IPv6 address to check
 *
 * RETURNS:
 *     bool - True if address is a multicast address
 *****************************************************************************/
func IsIPv6Multicast(ip net.IP) bool {
	return ip.IsMulticast()
}

/*****************************************************************************
 * NAME: ShouldFilterPacket
 *
 * DESCRIPTION:
 *     Determines if a packet should be filtered based on destination address.
 *     Filtering criteria:
 *     - IPv4 broadcast packets
 *     - IPv4/IPv6 multicast packets
 *
 * PARAMETERS:
 *     packet     - Raw packet bytes to analyze
 *     subnetMask - Current TUN interface subnet mask for subnet broadcast detection
 *
 * RETURNS:
 *     bool   - True if packet should be filtered
 *     string - Filtering reason, empty string if not filtered
 *****************************************************************************/
func ShouldFilterPacket(packet []byte, subnetMask net.IPMask) (bool, string) {
	// Check packet length
	if len(packet) < 20 { // IPv4 header minimum length is 20 bytes
		return false, ""
	}

	// Check IP version
	version := packet[0] >> 4

	if version == 4 { // IPv4
		// Parse IPv4 header
		header, err := ParseIPv4Header(packet[:20])
		if err != nil {
			return false, ""
		}

		// Check if it's broadcast address
		if IsIPv4Broadcast(header.DestinationIP, subnetMask) {
			return true, "IPv4 broadcast"
		}

		// Check if it's multicast address
		if IsIPv4Multicast(header.DestinationIP) {
			return true, "IPv4 multicast"
		}
	} else if version == 6 { // IPv6
		// Ensure packet length is sufficient for IPv6 header parsing
		if len(packet) < 40 { // IPv6 header length is 40 bytes
			return false, ""
		}

		// Parse IPv6 header
		header, err := ParseIPv6Header(packet[:40])
		if err != nil {
			return false, ""
		}

		// Check if it's multicast address
		if IsIPv6Multicast(header.DestinationIP) {
			return true, "IPv6 multicast"
		}
	}

	return false, ""
}
