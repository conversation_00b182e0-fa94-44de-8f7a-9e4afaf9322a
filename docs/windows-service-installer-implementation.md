# Windows 服务化部署 - Inno Setup 安装程序实现

## 概述

本文档描述了 Phase 4（Inno Setup 安装程序集成）的完整实现，包括 Windows 服务的自动安装、配置和管理。

## 实现的核心功能

### 1. 安装程序权限设置

```ini
[Setup]
PrivilegesRequired=admin
```

- **目的**：确保安装程序具有管理员权限，能够注册和管理 Windows 服务
- **必要性**：Windows 服务管理需要管理员权限

### 2. 服务管理函数

#### `StopWindowsServiceViaHTTP()`
- **优雅关闭**：通过 HTTP POST `http://127.0.0.1:56543/api/shutdown` 优雅关闭后端服务
- **重要说明**：HTTP `/api/shutdown` 端点只关闭后端服务，不关闭 UI 应用程序
- **降级策略**：HTTP 失败 → 服务命令 → 强制终止进程
- **超时设置**：5秒 HTTP 超时，3秒等待时间

#### `StopRunningApplicationViaHTTP()`
- **优雅关闭**：通过 HTTP POST `http://127.0.0.1:56543/api/shutdown` 优雅关闭运行中的应用程序
- **适用场景**：用户正在运行普通UI程序（非服务模式）时的关闭处理
- **降级策略**：HTTP 失败 → 强制终止UI和后端进程
- **超时设置**：3秒 HTTP 超时，2秒等待时间

#### `RemoveExistingService()`
- **执行顺序**：
  1. 停止服务进程（HTTP 优雅关闭）
  2. 强制终止残留进程
  3. 从注册表删除服务注册
- **目的**：完全清理现有服务，为新服务安装做准备

#### `InstallAndStartWindowsService()`
- **服务注册**：使用 `sc.exe create` 命令
- **服务配置**：
  - 服务名：`PanabitService`
  - 显示名：`Panabit Client WAN Service`
  - 启动类型：`auto`（自动启动）
  - 运行账户：`LocalSystem`
- **启动服务**：注册完成后立即启动服务

### 3. 安装流程

#### 升级场景处理（`InitializeSetup`）
```
检测现有安装 → 询问用户确认 → HTTP 优雅关闭后端服务 → 强制关闭UI程序 → 运行卸载程序 → 继续新安装
```

#### 安装完成处理（`CurStepChanged`）
```
创建用户配置目录 → 保存语言配置 → 移除旧服务 → 安装并启动新服务 → 显示结果提示
```

### 4. 卸载流程

#### 卸载前处理（`InitializeUninstall`）
```
HTTP 优雅关闭后端服务 → 强制关闭UI应用程序 → 等待进程终止
```

#### 卸载运行时（`[UninstallRun]`）
```
强制清理残留进程（作为备用清理）
```

#### 卸载完成处理（`CurUninstallStepChanged`）
```
询问是否删除用户数据 → 清理用户配置目录
```

## 服务注册命令详解

### 创建服务
```batch
sc create "PanabitService" 
  binPath= "C:\Program Files\Panabit\panabit-service.exe --config \"C:\Program Files\Panabit\config.yaml\"" 
  DisplayName= "Panabit Client WAN Service" 
  start= auto 
  obj= LocalSystem
```

### 设置服务描述
```batch
sc description "PanabitService" "Panabit Client WAN 网络服务"
```

### 启动服务
```batch
sc start "PanabitService"
```

### 删除服务
```batch
sc delete "PanabitService"
```

## 错误处理和用户反馈

### 服务安装成功
- **中文**：`Panabit 服务已成功安装并启动。应用程序将以系统服务模式运行，无需管理员权限。`
- **英文**：`Panabit service has been successfully installed and started. The application will run in system service mode without requiring administrator privileges.`

### 服务安装失败
- **中文**：`警告：Panabit 服务安装失败。应用程序将以管理员权限模式运行。`
- **英文**：`Warning: Panabit service installation failed. The application will run in administrator privilege mode.`

## 关键设计决策

### 1. 优雅降级策略
- **HTTP 优先**：首先尝试通过 HTTP API 优雅关闭
- **服务命令备用**：HTTP 失败时使用服务管理命令
- **强制终止兜底**：确保进程完全清理

### 2. 安装顺序优化
- **先移除后安装**：避免服务名冲突
- **进程完全终止**：确保文件可以被替换
- **充分等待时间**：给服务操作足够的完成时间

### 3. 错误容错设计
- **多层清理**：`InitializeUninstall` + `[UninstallRun]` 双重保障
- **用户友好提示**：明确告知服务安装状态
- **降级兼容**：服务安装失败时仍可正常使用

## 测试要点

1. **全新安装**：验证服务正确注册和启动
2. **升级安装**：验证旧服务正确移除，新服务正确安装
3. **卸载测试**：验证服务完全清理
4. **权限测试**：验证 LocalSystem 账户权限
5. **端口测试**：验证 HTTP API 端点可访问
6. **启动测试**：验证服务自动启动功能

## 与 UI 应用的集成

安装完成后，UI 应用将：
1. 首先尝试连接系统服务（健康检查）
2. 如果服务可用，直接使用系统服务
3. 如果服务不可用，回退到管理员权限启动

这确保了无缝的用户体验和向后兼容性。
