# ITforce WAN 项目重构实施方案

## 📋 文档概述

**创建日期**: 2025-01-18
**实施目标**: 基于架构分析结果执行具体的重构操作
**实施范围**: 四平台(iOS、Android、macOS、Windows) + 完整OEM定制系统
**预计工期**: 12-15个工作日

## 🎯 重构目标和策略

### 1.1 核心目标

1. **平台分离**: 将四个平台代码迁移到独立目录结构
2. **OEM定制**: 实现完整的品牌定制和自动化构建系统
3. **构建优化**: 创建模块化、可扩展的构建流程
4. **架构清晰**: 建立清晰的职责分离和依赖关系

### 1.2 实施策略

- **渐进式迁移**: 分阶段实施，每步验证后再继续
- **功能保持**: 确保重构过程中所有平台功能完整性
- **向后兼容**: 保留原构建系统作为备份
- **风险控制**: 完整备份和快速回滚机制

## 🚀 P0阶段：核心基础重构 (5-7天)

### 2.1 目录结构重组

**新目录架构**:
```
mobile/
├── platforms/                    # 平台特定代码
│   ├── backend/                  # Go后端服务
│   │   ├── cmd/                  # 主程序入口
│   │   ├── internal/             # 内部包
│   │   ├── go.mod               # Go模块定义
│   │   └── Makefile             # 后端构建
│   ├── shared/                   # 跨平台共享代码
│   │   ├── flutter/              # Flutter共享代码
│   │   │   ├── lib/              # Dart代码
│   │   │   ├── pubspec.yaml      # Flutter配置
│   │   │   └── Makefile          # Flutter构建
│   │   └── native/               # 原生共享代码
│   │       └── itforce-core/     # iOS/macOS共享库
│   ├── mobile/                   # 移动端平台
│   │   ├── ios/                  # iOS应用
│   │   └── android/              # Android应用
│   └── desktop/                  # 桌面端平台
│       ├── macos/                # macOS应用
│       └── windows/              # Windows应用
├── assets/                       # 全局资源文件
│   └── branding/                 # 品牌资源管理
│       ├── default/              # 默认ITforce品牌
│       ├── oem-template/         # OEM模板
│       └── custom-brands/        # 自定义品牌
├── configs/                      # 全局配置
│   ├── backend/                  # 后端配置
│   ├── branding/                 # 品牌配置
│   └── build/                    # 构建配置
├── scripts/                      # 全局构建脚本
│   ├── build/                    # 构建脚本
│   ├── oem/                      # OEM定制脚本
│   └── version/                  # 版本管理
└── Makefile                      # 根级构建入口
```

**实施步骤**:

1. **创建新目录结构**
```bash
# 创建主要目录
mkdir -p platforms/{backend,shared,mobile,desktop}
mkdir -p platforms/shared/{flutter,native}
mkdir -p platforms/mobile/{ios,android}
mkdir -p platforms/desktop/{macos,windows}

# 创建资源和配置目录
mkdir -p assets/branding/{default,oem-template,custom-brands}
mkdir -p configs/{backend,branding,build}
mkdir -p scripts/{build,oem,version}
```

2. **迁移Go后端代码**
```bash
# 迁移核心代码
mv cmd/ platforms/backend/
mv internal/ platforms/backend/
mv go.mod platforms/backend/
mv go.sum platforms/backend/

# 更新模块路径
cd platforms/backend
go mod edit -module platforms/backend
find . -name "*.go" -exec sed -i 's|mobile/internal|platforms/backend/internal|g' {} \;
go mod tidy
```

3. **迁移Flutter共享代码**
```bash
# 迁移Flutter核心代码
mv ui/flutter/lib/ platforms/shared/flutter/
mv ui/flutter/pubspec.yaml platforms/shared/flutter/
mv ui/flutter/analysis_options.yaml platforms/shared/flutter/

# 迁移ItForceCore
mv ui/flutter/ItForceCore/ platforms/shared/native/itforce-core/
```

### 2.2 构建系统重构

**新根级Makefile**:
```makefile
# ITforce WAN 根级构建配置
.PHONY: all clean build-all build-backend build-mobile build-desktop

# 版本管理
VERSION := $(shell cat VERSION 2>/dev/null || echo "1.0.0")
TIMESTAMP := $(shell date +%Y%m%d%H%M%S)
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_ID := $(VERSION)-$(GIT_COMMIT)-$(TIMESTAMP)

# 默认构建所有平台
all: build-all

# 构建所有组件
build-all: build-backend build-mobile build-desktop

# 构建后端
build-backend:
	@echo "Building backend..."
	@$(MAKE) -C platforms/backend VERSION=$(BUILD_ID)

# 构建移动端
build-mobile:
	@echo "Building mobile platforms..."
	@$(MAKE) -C platforms/mobile/ios VERSION=$(BUILD_ID)
	@$(MAKE) -C platforms/mobile/android VERSION=$(BUILD_ID)

# 构建桌面端
build-desktop:
	@echo "Building desktop platforms..."
	@$(MAKE) -C platforms/desktop/macos VERSION=$(BUILD_ID)
	@$(MAKE) -C platforms/desktop/windows VERSION=$(BUILD_ID)

# OEM定制构建
build-oem:
	@echo "Building OEM version for brand: $(BRAND)"
	@scripts/oem/build-oem.sh $(BRAND) $(PLATFORM)

# 清理所有构建产物
clean:
	@echo "Cleaning all build artifacts..."
	@$(MAKE) -C platforms/backend clean
	@$(MAKE) -C platforms/mobile/ios clean
	@$(MAKE) -C platforms/mobile/android clean
	@$(MAKE) -C platforms/desktop/macos clean
	@$(MAKE) -C platforms/desktop/windows clean
	@rm -rf build/*
```

**平台特定Makefile模板**:
```makefile
# platforms/backend/Makefile
include ../../scripts/build/common.mk

.PHONY: build clean test

build:
	@echo "Building Go backend..."
	go build -o ../../build/backend/itforce-service \
		-ldflags "-X main.version=$(VERSION)" \
		./cmd/vpn-service

clean:
	@echo "Cleaning backend build..."
	rm -rf ../../build/backend/*

test:
	@echo "Running backend tests..."
	go test ./...
```

## 🔧 P1阶段：平台代码分离 (4-5天)

### 3.1 iOS平台迁移

**迁移步骤**:
```bash
# 迁移iOS代码
mv ui/flutter/ios/* platforms/mobile/ios/

# 更新Xcode项目配置
# 需要手动更新project.pbxproj中的路径引用
# 更新Podfile中的相对路径

# 创建iOS Makefile
cat > platforms/mobile/ios/Makefile << 'EOF'
include ../../../scripts/build/ios.mk

.PHONY: build clean

build:
	@echo "Building iOS application..."
	cd ../../../platforms/shared/flutter && flutter build ios --release
	
clean:
	@echo "Cleaning iOS build..."
	cd ../../../platforms/shared/flutter && flutter clean
EOF
```

**配置更新**:
- 更新`Info.plist`中的路径引用
- 更新Xcode项目中的Flutter路径
- 更新CocoaPods配置

### 3.2 Android平台迁移

**迁移步骤**:
```bash
# 迁移Android代码
mv ui/flutter/android/* platforms/mobile/android/

# 更新Gradle配置
# 修改build.gradle.kts中的Flutter路径
# 更新settings.gradle中的项目路径

# 创建Android Makefile
cat > platforms/mobile/android/Makefile << 'EOF'
include ../../../scripts/build/android.mk

.PHONY: build clean

build:
	@echo "Building Android application..."
	cd ../../../platforms/shared/flutter && flutter build apk --release
	
clean:
	@echo "Cleaning Android build..."
	cd ../../../platforms/shared/flutter && flutter clean
EOF
```

### 3.3 桌面平台迁移

**macOS迁移**:
```bash
# 迁移macOS代码
mv ui/flutter/macos/* platforms/desktop/macos/

# 更新配置文件
# 修改Xcode项目配置
# 更新Podfile路径
```

**Windows迁移**:
```bash
# 迁移Windows代码
mv ui/flutter/windows/* platforms/desktop/windows/

# 更新CMake配置
# 修改CMakeLists.txt中的Flutter路径
```

## 🎨 P2阶段：OEM定制系统 (3-4天)

### 4.1 品牌配置系统设计

**默认品牌配置** (`assets/branding/default/brand-config.yaml`):
```yaml
metadata:
  version: "1.0"
  created: "2025-01-18"
  brand_id: "itforce-default"

brand:
  name: "ITforce WAN"
  display_name: "ITforce WAN Client"
  company: "ITforce Corporation"
  website: "https://itforce.com"

identifiers:
  ios_bundle_id: "com.itforce.itForceClient"
  android_package: "com.itforce.wan"
  windows_identity: "ItForce.ItForce"
  macos_bundle_id: "com.itforce.itForceClient"

assets:
  app_icon: "icons/app_icon.png"
  app_icon_ico: "icons/app_icon.ico"
  logo: "images/ITFORCE_Logo.svg"
  connected_icon: "images/connected.svg"
  disconnected_icon: "images/disconnected.svg"

colors:
  primary: "#1976D2"
  primary_dark: "#1565C0"
  accent: "#FF5722"
  success: "#4CAF50"
  warning: "#FF9800"
  error: "#F44336"

api:
  base_url: "https://api.itforce.com"
  server_list_url: "https://api.itforce.com/servers"
  auth_url: "https://api.itforce.com/auth"

features:
  enable_analytics: true
  enable_crash_reporting: true
  enable_auto_update: true
  enable_beta_features: false
```

### 4.2 OEM构建工具链

**主构建脚本** (`scripts/oem/build-oem.sh`):
```bash
#!/bin/bash
set -e

BRAND_NAME="$1"
PLATFORM="$2"
BUILD_TYPE="${3:-release}"

if [ -z "$BRAND_NAME" ]; then
    echo "Error: Brand name is required"
    echo "Usage: $0 <brand-name> <platform> [build-type]"
    exit 1
fi

BRAND_DIR="assets/branding/$BRAND_NAME"
if [ ! -d "$BRAND_DIR" ]; then
    echo "Error: Brand directory not found: $BRAND_DIR"
    exit 1
fi

echo "Building OEM version for brand: $BRAND_NAME"
echo "Platform: $PLATFORM"

# 1. 验证品牌配置
scripts/oem/validate-brand.sh "$BRAND_NAME"

# 2. 生成平台特定配置
scripts/oem/generate-configs.sh "$BRAND_NAME" "$PLATFORM"

# 3. 复制品牌资源
scripts/oem/copy-assets.sh "$BRAND_NAME" "$PLATFORM"

# 4. 执行构建
case "$PLATFORM" in
    "ios")
        make -C platforms/mobile/ios BRAND="$BRAND_NAME"
        ;;
    "android")
        make -C platforms/mobile/android BRAND="$BRAND_NAME"
        ;;
    "macos")
        make -C platforms/desktop/macos BRAND="$BRAND_NAME"
        ;;
    "windows")
        make -C platforms/desktop/windows BRAND="$BRAND_NAME"
        ;;
    "all")
        make build-all BRAND="$BRAND_NAME"
        ;;
    *)
        echo "Error: Unsupported platform: $PLATFORM"
        exit 1
        ;;
esac

echo "OEM build completed successfully!"
```

### 4.3 资源管理和配置生成

**配置生成脚本** (`scripts/oem/generate-configs.sh`):
```bash
#!/bin/bash
BRAND_NAME="$1"
PLATFORM="$2"
BRAND_CONFIG="assets/branding/$BRAND_NAME/brand-config.yaml"

# 解析品牌配置
APP_NAME=$(yq eval '.brand.display_name' "$BRAND_CONFIG")
BUNDLE_ID=$(yq eval '.identifiers.ios_bundle_id' "$BRAND_CONFIG")
PACKAGE_NAME=$(yq eval '.identifiers.android_package' "$BRAND_CONFIG")

# 生成iOS配置
if [ "$PLATFORM" = "ios" ] || [ "$PLATFORM" = "all" ]; then
    # 更新Info.plist
    # 更新Xcode项目配置
    # 生成新的Bundle ID
fi

# 生成Android配置
if [ "$PLATFORM" = "android" ] || [ "$PLATFORM" = "all" ]; then
    # 更新AndroidManifest.xml
    # 更新build.gradle.kts
    # 生成新的包名
fi
```

## 📊 验收标准和测试计划

### 5.1 功能完整性验收

- ✅ 所有平台应用正常构建和运行
- ✅ VPN连接功能在所有平台正常工作
- ✅ 跨平台功能一致性保持
- ✅ 原有API和接口保持兼容

### 5.2 构建系统验收

- ✅ 根级`make build-all`成功构建所有平台
- ✅ 单平台构建命令正常工作
- ✅ OEM定制构建功能正常
- ✅ 版本管理和依赖解析正确

### 5.3 OEM定制验收

- ✅ 品牌配置文件正确解析
- ✅ 资源文件正确替换
- ✅ 应用标识符正确更新
- ✅ 多品牌构建测试通过

## 🛡️ 风险控制和应急预案

### 6.1 备份策略

```bash
# 创建完整备份
git tag backup-before-restructure-$(date +%Y%m%d)
cp -r . ../mobile-backup-$(date +%Y%m%d)
```

### 6.2 回滚计划

```bash
#!/bin/bash
# scripts/rollback.sh
echo "🚨 执行紧急回滚..."

# 停止所有构建进程
pkill -f "make\|flutter\|go build" || true

# 恢复备份
if [ -d "../mobile-backup-$(date +%Y%m%d)" ]; then
    echo "📦 从备份恢复..."
    rm -rf platforms/ assets/branding/ configs/backend/
    cp -R ../mobile-backup-$(date +%Y%m%d)/* .
    echo "✅ 回滚完成"
else
    echo "❌ 备份未找到，使用Git回滚"
    git reset --hard backup-before-restructure-$(date +%Y%m%d)
fi
```

### 6.3 风险监控指标

- 📊 构建成功率 > 95%
- ⏱️ 构建时间增长 < 20%
- 🐛 新引入Bug数量 = 0
- 👥 团队适应时间 < 3天

## 🎯 下一步行动计划

1. **获得团队确认**: 与各平台开发团队确认实施计划
2. **准备环境**: 创建备份和准备迁移工具
3. **开始P0阶段**: 按照详细步骤执行目录重组
4. **逐步推进**: 完成P1和P2阶段实施
5. **全面测试**: 验证所有功能和OEM定制能力
