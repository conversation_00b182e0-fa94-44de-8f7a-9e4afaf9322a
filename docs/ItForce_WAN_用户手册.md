# ItForce WAN 客户端用户手册

## 目录

1. [产品概述](#产品概述)
2. [系统要求](#系统要求)
3. [安装指南](#安装指南)
4. [快速开始](#快速开始)
5. [功能详解](#功能详解)
6. [设置配置](#设置配置)
7. [故障排除](#故障排除)
8. [技术支持](#技术支持)

---

## 产品概述

### 什么是 ItForce WAN

ItForce WAN 是一个基于 SDWAN ZZVPN 协议的企业级 WAN 客户端，为用户提供安全、稳定、高性能的网络连接服务。采用现代化的分层架构设计，支持跨平台部署，具备智能服务器选择、实时状态监控等先进功能。

### 核心特性

- **SDWAN ZZVPN 协议**：完整实现 SDWAN ZZVPN 协议栈，支持多种加密方式
- **智能服务器选择**：自动选择最佳服务器，支持延迟测试和健康检查
- **跨平台支持**：支持 Windows、macOS、Linux 等主流操作系统
- **现代化界面**：基于 Flutter 的响应式用户界面，操作简单直观
- **实时通信**：WebSocket 实时状态更新和事件通知
- **安全可靠**：企业级安全设计，支持凭据加密存储
- **多语言支持**：支持中文和英文界面切换

---

## 系统要求

### 最低配置要求

- **操作系统**：Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **内存**：2GB RAM
- **存储空间**：500MB 可用空间
- **网络**：稳定的互联网连接
- **权限**：管理员权限（用于 TUN 设备创建和网络配置）

### 推荐配置

- **操作系统**：最新版本操作系统
- **内存**：4GB+ RAM
- **存储空间**：1GB+ 可用空间
- **网络**：高速稳定网络连接

### 特殊要求

- **Windows 系统**：需要管理员权限创建 TUN 设备
- **防火墙设置**：确保应用程序可以访问网络
- **安全软件**：某些安全软件可能需要添加信任规则

---

## 安装指南

### 下载安装包

1. 从官方渠道获取最新版本的 ItForce WAN 安装包
2. 安装包命名格式：`ItForce-1.0.0-Setup.exe`（版本号可能不同）

### 安装步骤

1. **运行安装程序**
   - 右键点击安装包，选择"以管理员身份运行"
   - 如果系统提示用户账户控制，点击"是"

2. **选择安装语言**
   - 安装程序启动后，首先选择安装界面语言
   - 支持中文和英文

3. **阅读用户协议**
   - 仔细阅读《用户服务条款》和《隐私政策》
   - 点击相应链接可在浏览器中查看详细内容
   - 勾选"我接受协议"复选框

4. **选择安装位置**
   - 默认安装到 `C:\Program Files\ItForce WAN\`
   - 可点击"浏览"按钮选择其他位置

5. **完成安装**
   - 点击"安装"按钮开始安装
   - 安装完成后，可选择立即启动应用程序

### 卸载方法

1. **通过控制面板卸载**
   - 打开"控制面板" → "程序和功能"
   - 找到"ItForce WAN"，点击"卸载"

2. **通过设置应用卸载**（Windows 10/11）
   - 打开"设置" → "应用"
   - 搜索"ItForce WAN"，点击"卸载"

---

## 快速开始

### 首次启动

1. **启动应用程序**
   - 双击桌面快捷方式或从开始菜单启动
   - 应用程序需要管理员权限，请允许相关提示

2. **登录界面**
   - 输入用户名和密码
   - 可勾选"记住用户名和密码"以便下次自动填充
   - 点击"登录"按钮

### 建立连接

1. **选择服务器**
   - 登录成功后进入连接界面
   - 点击服务器选择下拉框
   - 选择延迟最低的服务器（显示为绿色）

2. **开始连接**
   - 点击中央的连接按钮
   - 等待连接建立（按钮会显示连接进度）
   - 连接成功后按钮变为已连接状态

3. **查看连接状态**
   - 连接成功后可查看连接时长
   - 实时显示上传/下载速度
   - 底部显示当前连接的服务器信息

### 断开连接

- 点击连接按钮即可断开当前连接
- 断开连接后，所有路由规则会自动清理

---

## 功能详解

### 登录界面

**主要功能：**
- 用户身份验证
- 凭据记忆功能
- 多语言界面支持

**操作说明：**
- 输入用户名和密码进行登录
- 勾选"记住用户名和密码"可在下次启动时自动填充
- 登录失败时会显示具体错误信息
- 支持中英文界面切换

**安全特性：**
- 密码加密存储
- 退出时自动清除密码字段
- 支持安全的凭据管理

### 连接界面

**主要功能：**
- WAN 连接管理
- 服务器选择
- 实时状态监控
- 连接统计信息

**服务器选择：**
- 显示所有可用服务器列表
- 延迟颜色编码：
  - 绿色：低延迟（推荐）
  - 黄色：中等延迟
  - 红色：高延迟
  - 灰色：不可达服务器
- 支持手动选择和自动选择最佳服务器

**连接状态：**
- 断开连接：显示连接按钮
- 连接中：显示连接进度
- 已连接：显示连接时长和实时速度
- 连接超时：30秒后自动超时并显示错误

**实时信息：**
- 连接时长计时器
- 实时上传/下载速度（仅文字显示）
- 当前连接服务器信息

### 统计界面

**显示信息：**
- **状态**：当前连接状态（已连接/未连接）
- **接口**：网络接口名称（如"无线局域网 WLAN"）
- **上行**：总上传流量统计
- **下行**：总下载流量统计
- **本地IP**：本地网络IP地址
- **ItForce IP**：WAN隧道分配的IP地址

**界面特点：**
- 简洁的卡片式设计
- 实时数据更新
- 清晰的信息分类

### 设置界面

**应用设置：**
- **语言选择**：支持中文/英文切换，立即生效
- **开机自动启动**：系统启动时自动运行应用程序

**路由设置：**
- **全部路由模式**：所有网络流量通过WAN隧道
- **按网段路由模式**：仅指定网段流量通过WAN隧道

**网段配置：**
- 支持标准CIDR格式：`***********/24`
- 多个网段用逗号分隔：`***********/24, 10.0.0.0/8`
- 输入框具有500ms防抖机制，优化用户体验

**设置应用：**
- 修改路由设置后需点击"应用设置"按钮
- 语言和自动启动设置立即生效
- 已连接状态下路由变更无需重新连接

### 关于界面

**应用信息：**
- 应用名称：ItForce WAN
- 版本号：当前安装版本
- 设备ID：唯一设备标识符

**协议和联系：**
- **服务条款**：点击复制URL到剪贴板
  - 中文：`www.panabit.com/term_service_zh`
  - 英文：`www.panabit.com/term_service_en`
- **隐私政策**：点击复制URL到剪贴板
  - 中文：`www.panabit.com/privacy_policy_zh`
  - 英文：`www.panabit.com/privacy_policy_en`
- **官方网站**：显示并可点击访问 `https://www.panabit.com`

---

## 设置配置

### 路由配置详解

**全部路由模式：**
- 适用场景：需要完全匿名或访问受限网络
- 工作原理：所有网络流量都通过WAN隧道传输
- 优点：最大程度保护隐私和安全
- 缺点：可能影响本地网络访问速度

**按网段路由模式：**
- 适用场景：只需要访问特定网络资源
- 工作原理：仅指定网段流量通过WAN隧道
- 优点：提高访问速度，减少带宽消耗
- 缺点：需要手动配置网段

**网段格式要求：**
```
单个网段：***********/24
多个网段：***********/24, 10.0.0.0/8, 172.16.0.0/12
常用私有网段：
- 192.168.0.0/16  （192.168.x.x）
- 10.0.0.0/8      （10.x.x.x）
- 172.16.0.0/12   （172.16.x.x - 172.31.x.x）
```

### 自动启动配置

**启用方法：**
1. 进入设置界面
2. 找到"开机自动启动"选项
3. 切换开关至启用状态
4. 设置立即生效，无需重启

**注意事项：**
- 首次启用可能需要管理员权限确认
- 某些安全软件可能会询问是否允许自启动
- 设置保存在本地系统中，不会上传到服务器

### 语言设置

**切换方法：**
1. 进入设置界面
2. 在语言下拉框中选择目标语言
3. 设置立即生效，界面自动更新

**支持语言：**
- 中文（简体）
- English（英文）

**本地化特性：**
- 界面文字完全本地化
- 服务器名称根据语言显示
- 错误信息和通知消息本地化
- 确认对话框使用目标语言显示

---

## 故障排除

### 常见问题

**1. 无法启动应用程序**
- 确认是否以管理员权限运行
- 检查系统是否满足最低要求
- 查看Windows事件日志中的错误信息

**2. 登录失败**
- 检查用户名和密码是否正确
- 确认网络连接正常
- 联系管理员确认账户状态

**3. 无法连接服务器**
- 检查网络连接状态
- 尝试选择其他服务器
- 确认防火墙设置允许应用程序访问网络

**4. 连接频繁断开**
- 检查网络稳定性
- 尝试更换服务器
- 查看应用程序日志文件

**5. 路由设置不生效**
- 确认已点击"应用设置"按钮
- 检查网段格式是否正确
- 重新连接WAN以应用新设置

### 日志文件位置

**Windows系统：**
- 应用程序日志：`%APPDATA%\ItForce WAN\logs\`
- 后端服务日志：`logs\backend.log`
- 崩溃报告：`logs\panic_reports\`

**日志级别：**
- INFO：重要用户操作和状态变更
- DEBUG：详细的内部操作信息
- ERROR：错误和异常情况

### 网络诊断

**连接测试：**
1. 检查本地网络连接
2. 测试DNS解析功能
3. 验证防火墙设置
4. 检查代理服务器配置

**服务器延迟测试：**
- 应用程序自动进行延迟测试
- 测试间隔：25秒
- 超时时间：2秒
- 结果显示：颜色编码

### 重置和恢复

**重置应用设置：**
1. 完全退出应用程序
2. 删除配置文件目录
3. 重新启动应用程序
4. 重新进行初始配置

**重新安装：**
1. 卸载当前版本
2. 清理残留文件
3. 重新下载安装包
4. 以管理员权限安装

---

## 技术支持

### 联系方式

**官方网站：** https://www.panabit.com

**技术文档：**
- 用户服务条款：www.panabit.com/term_service_zh
- 隐私政策：www.panabit.com/privacy_policy_zh

### 反馈问题

**提交问题时请提供：**
1. 应用程序版本号
2. 操作系统版本
3. 详细的问题描述
4. 重现步骤
5. 相关日志文件

### 更新说明

**自动更新：**
- 应用程序会定期检查更新
- 重要安全更新会自动提示

**手动更新：**
- 从官方渠道下载最新版本
- 卸载旧版本后安装新版本
- 用户设置和数据会自动保留

---

## 高级功能

### 系统架构

ItForce WAN 采用分层架构设计，包含以下核心组件：

**1. 平台层**
- 抽象平台特定操作
- 提供统一的系统接口
- 支持跨平台部署

**2. 协议层**
- 实现 SDWAN ZZVPN 协议
- 处理数据包加密和解密
- 管理协议状态机

**3. 连接层**
- 管理 WAN 连接生命周期
- 智能服务器选择算法
- 连接状态监控和恢复

**4. 服务层**
- 提供 HTTP API 接口
- WebSocket 实时通信
- 事件通知机制

**5. 界面层**
- 跨平台用户界面
- 本地功能集成
- 用户交互处理

### 技术实现细节

**通信协议：**
- HTTP API 基础地址：`http://localhost:56543/api`
- WebSocket 地址：`ws://localhost:56544/ws`
- 统一的 JSON 响应格式
- 完整的错误码体系

**安全机制：**
- 企业级加密算法
- 凭据安全存储
- TUN 设备安全管理
- 网络流量加密传输

**性能优化：**
- 对象池管理
- 协程并发处理
- 内存使用优化
- 网络传输优化

### 配置文件说明

**主配置文件 (config.yaml)：**
```yaml
api:
  host: "localhost"
  port: 56543

logging:
  level: "info"
  file: "logs/backend.log"
  max_size: 10    # MB
  max_backups: 3
  max_age: 28     # 天

vpn:
  tunnel:
    encrypt: true           # 启用加密
    route_mode: 0           # 路由模式
    mtu: 1400               # 最大传输单元
  server_list_url: "http://example.com/servers.json"
```

**服务器列表格式：**
```json
[
  {
    "id": "server_001",
    "name": "服务器1",
    "name_en": "Server 1",
    "server_name": "vpn1.example.com",
    "server_port": 8000,
    "isauto": false
  }
]
```

---

## 附录

### 错误代码参考

**连接错误：**
- 1001：网络连接失败
- 1002：服务器不可达
- 1003：认证失败
- 1004：连接超时

**配置错误：**
- 2001：配置文件格式错误
- 2002：路由配置无效
- 2003：服务器列表获取失败
- 2004：权限不足

**系统错误：**
- 3001：TUN 设备创建失败
- 3002：网络接口配置失败
- 3003：路由表操作失败
- 3004：系统资源不足

### 网络端口说明

**本地端口：**
- 56543：HTTP API 服务端口
- 56544：WebSocket 通信端口

**远程端口：**
- 8000：默认 WAN 服务端口（可配置）
- 443：HTTPS 服务器列表获取

### 系统兼容性

**Windows 版本：**
- Windows 10 (1903+)
- Windows 11 (所有版本)
- Windows Server 2019+
- Windows Server 2022

**硬件要求：**
- x64 处理器架构
- 支持虚拟化技术
- 网络适配器支持

### 第三方组件

**开源组件：**
- WinTun：Windows TUN 设备驱动
- Flutter：跨平台 UI 框架
- Go：后端服务开发语言

**许可证信息：**
- 遵循相关开源许可证
- 详细许可证信息见安装目录

---

*本手册基于 ItForce WAN v1.0.0 编写，如有更新请以最新版本为准。*

*最后更新时间：2024年12月*
