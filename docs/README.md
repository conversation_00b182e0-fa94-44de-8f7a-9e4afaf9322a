# ITforce WAN 文档中心

## 📋 文档概述

欢迎来到 ITforce WAN 项目的文档中心。本项目是一个基于 SDWAN ZZVPN 协议的企业级 WAN 客户端，支持 Windows、macOS、iOS 和 Android 多平台。

**项目特点**：
- 🏗️ **模块化架构** - 分层设计，职责清晰
- 🌐 **跨平台支持** - 统一协议，平台适配
- 🔒 **企业级安全** - 完整的加密和认证机制
- 📱 **现代化界面** - Flutter 跨平台 UI
- ⚡ **高性能优化** - 对象池、协程管理、性能监控

## 🗂️ 文档分类

### 📖 核心设计文档

#### 系统架构
- **[系统架构设计](architecture_design.md)** - 完整的系统架构和组件设计
- **[API 接口规范](api_specification.md)** - HTTP API 和 WebSocket 接口文档
- **[协议实现文档](protocol_implementation.md)** - SDWAN ZZVPN 协议的详细实现

#### 用户界面
- **[Flutter UI 设计](flutter_ui_design.md)** - 前端界面设计和交互规范
- **[Flutter iOS/macOS 适配分析](flutter-ios-macos-adaptation-analysis.md)** - 跨平台适配分析
- **[Flutter iOS/macOS 接口分析](flutter-ios-macos-interface-analysis.md)** - Platform Channel 接口设计

### 🔧 平台实现文档

#### iOS/macOS 平台
- **[iOS/macOS 文档索引](ios-macos-docs-index.md)** - iOS/macOS 平台完整文档导航
- **[iOS/macOS 架构设计](ios-macos-architecture-design.md)** - 简化架构设计和实现指南
- **[iOS/macOS 项目结构](ios-macos-project-structure.md)** - 代码组织和目录结构
- **[iOS/macOS 设计指导原则](ios-macos-design-guidelines.md)** - 代码规范和质量标准
- **[iOS/macOS 简化架构实施指南](ios-macos-simplified-architecture-implementation-guide.md)** - 详细实施指南

#### Android 平台
- **[Android 文档索引](android/README.md)** - Android 平台完整文档导航
- **[Android 架构设计](android/android-architecture-design.md)** - Android 平台架构设计
- **[Android 目录结构](android/android-directory-structure.md)** - 项目结构和代码组织

### 🧩 模块设计文档

#### 核心模块
- **[基础设施层设计](modules/infrastructure-layer-design.md)** - 日志、错误处理、性能监控
- **[协议层设计](modules/protocol-layer-design.md)** - SDWAN 协议实现
- **[平台层设计](modules/platform-layer-design.md)** - 平台抽象和系统集成
- **[连接层设计](modules/connection-layer-design.md)** - 连接管理和服务器选择
- **[服务层设计](modules/service-layer-design.md)** - API 服务和业务逻辑

#### 集成模块
- **[Flutter 集成设计](modules/flutter-integration-design.md)** - Flutter 与原生平台集成
- **[Flutter 代码复用策略](modules/flutter-code-reuse-strategy.md)** - 跨平台代码复用

### 📚 技术专题文档

#### 网络和协议
- **[SDWAN 协议规范](sdwan.md)** - SDWAN ZZVPN 协议的完整规范
- **[IP 分片处理](ip_fragment_handling.md)** - 网络数据包分片处理技术
- **[VPN 授权指南](VPN_Authorization_Guide.md)** - VPN 权限申请和授权流程

#### 开发规范
- **[iOS VPN 代码审查规则](ios-vpn-code-review-rules.md)** - iOS VPN 开发代码规范和审查标准

#### 跨平台适配
- **[Flutter iOS/macOS 适配分析](flutter-ios-macos-adaptation-analysis.md)** - 跨平台适配完整分析
- **[Flutter iOS/macOS 接口分析](flutter-ios-macos-interface-analysis.md)** - Platform Channel 接口设计

### 🔄 UI 后端交互文档

#### 通信设计
- **[UI 后端交互概述](ui_backend_interaction/README.md)** - 前后端通信架构
- **[HTTP API 设计](ui_backend_interaction/http_api/design.md)** - HTTP 接口设计
- **[WebSocket 设计](ui_backend_interaction/websocket/design.md)** - 实时通信设计

#### 本地功能
- **[UI 前端本地功能](ui_frontend_local/README.md)** - 本地功能概述
- **[本地功能设计](ui_frontend_local/design.md)** - 本地功能详细设计

## 🎯 快速导航

### 对于新开发者
1. 📖 先阅读 [系统架构设计](architecture_design.md) 了解整体架构
2. 🔧 根据开发平台选择对应的平台文档：
   - iOS/macOS: [iOS/macOS 文档索引](ios-macos-docs-index.md)
   - Android: [Android 文档索引](android/README.md)
3. 📱 查看 [Flutter UI 设计](flutter_ui_design.md) 了解前端实现
4. 🔗 查看 [API 接口规范](api_specification.md) 了解接口设计

### 对于架构师
1. 🏗️ [系统架构设计](architecture_design.md) - 整体架构概览
2. 🧩 [模块设计文档](modules/) - 各层详细设计
3. 🔄 [协议实现文档](protocol_implementation.md) - 协议栈实现
4. 📊 [项目重构计划](project-restructure-plan.md) - 架构演进

### 对于产品经理
1. 📖 [用户手册](ItForce_WAN_用户手册.md) - 产品功能概览
2. 📱 [Flutter UI 设计](flutter_ui_design.md) - 界面设计规范
3. 🌐 [多语言支持](MULTILINGUAL_SUPPORT.md) - 国际化支持
4. 🔧 各平台文档了解技术实现

### 对于测试工程师
1. 🔗 [API 接口规范](api_specification.md) - 接口测试参考
2. 🧪 各平台架构文档了解测试要点
3. 📊 [协议实现文档](protocol_implementation.md) - 协议测试参考

## 📊 文档状态

### 完成度统计
- ✅ **核心设计文档**: 100% 完成
- ✅ **iOS/macOS 文档**: 100% 完成
- 🚧 **Android 文档**: 80% 完成（实施中）
- ✅ **模块设计文档**: 100% 完成
- ✅ **技术专题文档**: 100% 完成

### 最近更新
- **2025-07-14**: 文档中心创建，统一文档导航
- **2025-07-10**: iOS/macOS 架构简化文档更新
- **2025-06-26**: iOS/macOS 实现完成文档
- **2025-01-XX**: Android 架构设计文档

## 🤝 文档贡献

### 文档规范
- 使用 Markdown 格式
- 遵循统一的文档结构
- 包含完整的目录和导航
- 及时更新文档状态

### 更新流程
1. 修改相关文档
2. 更新文档索引
3. 检查链接有效性
4. 提交变更记录

## 📞 联系信息

如有文档相关问题或建议：
- 📧 技术文档：查阅本目录下的详细文档
- 🔗 项目仓库：ITforce WAN Repository
- 👥 开发团队：项目开发团队

---

*本文档中心为 ITforce WAN 项目提供完整的文档导航和技术参考。*

*创建时间：2025年7月14日*
*最后更新：2025年7月14日*
