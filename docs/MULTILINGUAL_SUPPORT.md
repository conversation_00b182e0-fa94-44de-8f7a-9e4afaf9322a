# ItForce WAN 多语言支持实现

## 概述

ItForce WAN 现在支持多语言功能，用户可以在安装过程中选择中文或英文，应用程序将根据选择的语言显示相应的界面文本。

## 实现特性

### 1. 安装器语言选择
- 在安装过程中提供语言选择页面
- 支持中文（简体）和英文
- 根据选择的语言显示相应的用户协议和隐私政策URL
- 将语言选择保存到配置文件

### 2. Flutter应用多语言支持
- 使用Flutter官方国际化框架
- 支持运行时语言切换
- 从安装器配置文件读取初始语言设置
- 支持中文（简体）和英文

### 3. 用户协议和隐私政策
- 根据选择的语言提供不同的协议URL
- 中文：
  - 服务条款：https://www.panabit.com/term_service_zh
  - 隐私政策：https://www.panabit.com/privacy_policy_zh
- 英文：
  - 服务条款：https://www.panabit.com/term_service_en
  - 隐私政策：https://www.panabit.com/privacy_policy_en

## 文件结构

```
ui/flutter/
├── l10n.yaml                          # 国际化配置文件
├── lib/
│   ├── l10n/                          # 本地化资源文件
│   │   ├── app_en.arb                 # 英文资源
│   │   └── app_zh.arb                 # 中文资源
│   ├── generated/l10n/                # 生成的本地化文件
│   │   ├── app_localizations.dart     # 主本地化类
│   │   ├── app_localizations_en.dart  # 英文实现
│   │   └── app_localizations_zh.dart  # 中文实现
│   ├── services/
│   │   └── language_service.dart      # 语言服务
│   └── screens/
│       └── language_demo_screen.dart  # 语言演示屏幕
└── pubspec.yaml                       # 依赖配置

installer/
└── setup.iss                          # 安装器脚本（包含语言选择功能）
```

## 使用方法

### 1. 安装过程中的语言选择
1. 运行安装器
2. 在语言选择页面选择中文或英文
3. 点击下一步后会显示相应语言的用户协议
4. 同意协议后继续安装

### 2. 应用程序中的语言切换
1. 打开设置页面
2. 在语言设置部分选择所需语言
3. 应用程序界面将立即切换到选择的语言

### 3. 开发者使用本地化文本
```dart
// 在Widget中使用本地化文本
final l10n = AppLocalizations.of(context)!;

Text(l10n.login)        // 显示"登录"或"Login"
Text(l10n.connect)      // 显示"连接"或"Connect"
Text(l10n.settings)     // 显示"设置"或"Settings"
```

## 配置文件

### 语言配置文件 (language.cfg)
安装器会在应用程序目录创建此文件：
```
language=zh  # 或 language=en
```

### 本地化资源文件 (app_zh.arb / app_en.arb)
包含所有界面文本的翻译：
```json
{
  "@@locale": "zh",
  "appTitle": "ItForce VPN",
  "login": "登录",
  "username": "用户名",
  "password": "密码",
  ...
}
```

## 支持的语言

目前支持以下语言：
- 中文（简体）- zh
- 英文 - en

## 扩展新语言

要添加新语言支持：

1. 在 `lib/l10n/` 目录添加新的 `.arb` 文件
2. 更新 `lib/services/language_service.dart` 中的支持语言列表
3. 在安装器脚本中添加新语言选项
4. 重新生成本地化文件

## 技术实现细节

### 1. 语言服务 (LanguageService)
- 管理当前语言设置
- 从安装器配置文件读取初始语言
- 提供语言切换功能
- 使用SharedPreferences持久化语言设置

### 2. 安装器集成
- 自定义安装页面用于语言选择
- 根据语言选择显示相应的协议URL
- 将语言配置写入文件供Flutter应用读取

### 3. 本地化文件生成
- 使用Flutter官方的gen-l10n工具
- 自动生成类型安全的本地化类
- 支持运行时语言切换

## 注意事项

1. 首次启动时，应用程序会优先读取安装器设置的语言
2. 用户可以在设置中随时更改语言
3. 语言更改会立即生效，无需重启应用
4. 所有新增的界面文本都应该添加到本地化资源文件中
