# Windows 系统服务化部署完整方案

## 概述

使用 Inno Setup + sc.exe 的方式将 Panabit Client 后端服务注册为 Windows 系统服务，消除每次启动时的 UAC 提示。采用优雅降级策略，确保在系统服务不可用时能够回退到原有的管理员权限启动方式。

## 核心思路

1. **Go 后端 SCM 适配**：添加 Windows 服务控制管理器（SCM）支持，使后端能够作为系统服务运行
2. **服务管理命令**：支持 install/remove/start/stop 等服务管理命令
3. **Inno Setup 安装器**：负责文件部署和服务注册，支持升级场景
4. **sc.exe 服务管理**：使用 Windows 内置工具注册和管理服务
5. **优雅降级策略**：UI 应用优先连接系统服务，失败时回退到管理员权限启动
6. **连接模式变更**：UI 退出时只断开连接，不关闭后端服务

## 技术方案

### 1. 服务注册方式

使用 sc.exe 命令注册服务：

```batch
sc create "PanabitService" ^
  binPath= "C:\Program Files\Panabit\panabit-service.exe --config \"C:\ProgramData\Panabit\config.yaml\"" ^
  DisplayName= "Panabit Client WAN Service" ^
  start= auto ^
  obj= LocalSystem
```

### 2. Inno Setup 安装脚本逻辑

**安装流程**：
1. **检查现有服务**：查询是否已存在 PanabitService
2. **停止旧服务**：如果服务存在且正在运行，先停止服务
3. **文件部署**：
   - 后端服务程序到 `C:\Program Files\Panabit\`
   - UI 应用程序到 `C:\Program Files\Panabit\`
   - 配置文件到 `C:\ProgramData\Panabit\`
4. **服务管理**：
   - 删除旧服务（如果存在）
   - 创建新服务并配置为自动启动
   - 设置服务描述信息
   - 启动新服务
5. **权限设置**：配置目录权限，确保服务和用户都能正常访问

**升级场景处理**：
- 安装前检查服务状态，如果正在运行则先停止
- 替换文件后重新启动服务
- 保留用户配置文件，避免配置丢失

**卸载流程**：
1. 停止 PanabitService 服务
2. 删除服务注册
3. 清理所有安装文件
4. 移除注册表项和快捷方式

### 3. UI 应用启动逻辑修改

**优雅降级策略**：
1. **健康检查优先**：UI 启动时首先检查 `http://127.0.0.1:8080/health` 端点
2. **服务可用场景**：如果健康检查成功，直接连接到系统服务的 API
3. **服务不可用场景**：
   - 发送 `/quit` 命令尝试优雅关闭可能存在的旧后端进程
   - 等待 2 秒确保进程退出
   - 回退到原有的 PowerShell + RunAs 启动方式
4. **连接建立**：无论哪种方式，最终都通过 HTTP API 与后端通信

**UI 退出逻辑变更**：
- **原逻辑**：发送 `/shutdown` 命令，后端服务完全退出
- **新逻辑**：发送 `/disconnect` 命令，仅断开当前 VPN 连接，后端服务继续运行
- **好处**：系统服务保持运行，下次启动 UI 时无需重新启动后端

### 4. Go 后端 SCM 适配

**Windows 服务接口实现**：
- 使用 `golang.org/x/sys/windows/svc` 包实现服务接口
- 实现 `svc.Handler` 接口的 `Execute()` 方法
- 响应 SCM 的启动、停止、关闭等命令
- 向 SCM 报告服务状态变化

**服务管理命令支持**：
- `panabit-service.exe install` - 安装为系统服务
- `panabit-service.exe remove` - 卸载系统服务
- `panabit-service.exe start` - 启动服务
- `panabit-service.exe stop` - 停止服务
- 无参数运行时自动检测是否在服务模式下运行

**信号处理改造**：
- 服务模式：通过 SCM 接收停止信号
- 保持现有的优雅关闭逻辑
- 确保所有组件正确清理

### 5. 后端 API 扩展

**新增健康检查端点**：
- **路径**：`GET /health`
- **返回**：JSON 格式的服务状态信息
- **用途**：UI 应用检测后端服务是否正常运行

**新增退出端点**：
- **路径**：`POST /quit`
- **功能**：优雅关闭后端进程（用于降级场景）
- **用途**：在启动新进程前清理可能存在的旧进程

**修改断开连接端点**：
- **路径**：`POST /disconnect`
- **功能**：断开当前 VPN 连接，但不退出后端服务
- **用途**：UI 退出时保持后端服务运行

## 配置文件管理

### 配置文件位置
- **系统配置**：`C:\ProgramData\Panabit\config.yaml`
- **日志文件**：`C:\ProgramData\Panabit\logs\`
- **程序文件**：`C:\Program Files\Panabit\`

### 权限设置
- **LocalSystem 账户**：对配置目录具有完全控制权限
- **普通用户**：对配置目录具有读取权限
- **安装器设置**：通过 icacls 命令配置目录权限

## 开发任务计划

### 阶段一：Go 后端 SCM 适配（2天）
1. **添加 Windows 服务支持**：
   - 引入 `golang.org/x/sys/windows/svc` 依赖
   - 实现 `svc.Handler` 接口
   - 添加服务状态管理逻辑
2. **实现服务管理命令**：
   - 添加命令行参数解析（install/remove/start/stop）
   - 实现服务安装和卸载逻辑
   - 添加服务启动和停止功能
3. **改造程序入口点**：
   - 修改 `main()` 函数支持服务模式检测
   - 保持现有 `realMain()` 逻辑不变
   - 添加 SCM 信号处理

### 阶段二：后端 API 扩展（1天）
1. **添加健康检查端点**：在 HTTP 服务器中添加 `/health` 路由
2. **添加退出端点**：实现 `/quit` 端点用于优雅关闭
3. **修改断开连接逻辑**：确保 `/disconnect` 只断开 VPN 不退出服务

### 阶段三：UI 应用逻辑改造（2天）
1. **实现健康检查逻辑**：UI 启动时优先检查服务状态
2. **实现降级启动逻辑**：服务不可用时回退到管理员权限启动
3. **修改退出逻辑**：UI 退出时只发送 disconnect 命令
4. **优化用户体验**：添加状态提示和错误处理

### 阶段四：Inno Setup 安装器开发（2天）
1. **编写安装脚本**：实现文件部署和服务注册逻辑
2. **处理升级场景**：检查并停止现有服务，替换文件后重启
3. **配置权限设置**：确保服务和用户都能正常访问配置文件
4. **实现卸载逻辑**：完整清理服务和文件

### 阶段五：测试和验证（2天）
1. **功能测试**：验证安装、运行、升级、卸载各个环节
2. **降级测试**：验证服务不可用时的回退逻辑
3. **重启测试**：验证系统重启后服务自动启动
4. **用户体验测试**：确保 UAC 提示消除，启动速度优化

### 阶段一：Inno Setup 安装器开发
1. **创建安装脚本**
   - 编写 .iss 安装脚本文件
   - 配置文件部署路径
   - 添加服务注册命令

2. **测试安装流程**
   - 验证文件正确部署
   - 确认服务成功注册
   - 检查服务自动启动

### 阶段二：UI 应用适配
1. **修改后端启动逻辑**
   - 通过移除 PowerShell 启动代码
   - 添加服务状态检测
   - 实现服务启动功能

2. **添加健康检查**
   - 实现 `/health` 端点检测
   - 添加连接重试机制
   - 优化错误提示信息

### 阶段三：测试和优化
1. **功能测试**
   - 安装/卸载流程测试
   - 服务启动/停止测试
   - UI 连接测试

2. **用户体验优化**
   - 启动速度优化
   - 错误处理完善
   - 状态提示改进

## 技术实现要点

### Go 后端 SCM 集成
- **服务接口**：实现 `svc.Handler` 接口响应 SCM 命令
- **自动检测**：程序启动时自动检测是否在服务模式运行
- **状态报告**：向 SCM 报告服务启动、运行、停止状态
- **优雅关闭**：响应 SCM 停止信号，执行现有的优雅关闭逻辑
- **命令行支持**：支持 install/remove/start/stop 服务管理命令

### 服务注册和管理
- **服务名称**：PanabitService
- **显示名称**：Panabit Client WAN Service
- **启动类型**：自动启动
- **服务账户**：LocalSystem（具备管理员权限）
- **服务描述**：Panabit Client WAN 网络服务

### 升级场景处理
1. **检查现有服务**：安装前查询服务是否已存在
2. **停止旧服务**：如果服务正在运行，先执行停止命令
3. **删除旧服务**：移除现有的服务注册
4. **替换文件**：部署新版本的程序文件
5. **重新注册**：创建新的服务注册
6. **启动新服务**：启动更新后的服务

### 降级策略实现
1. **健康检查**：UI 启动时首先尝试访问 `/health` 端点
2. **服务连接**：如果健康检查成功，直接使用系统服务
3. **优雅退出**：如果健康检查失败，发送 `/quit` 命令清理可能存在的进程
4. **权限启动**：回退到原有的 PowerShell + RunAs 启动方式
5. **状态提示**：向用户显示当前使用的启动方式

### 连接模式变更
- **启动阶段**：UI 通过健康检查确定连接方式
- **运行阶段**：UI 与后端通过 HTTP API 正常通信
- **退出阶段**：UI 发送 `/disconnect` 断开 VPN 连接，后端服务继续运行
- **好处**：下次启动时系统服务仍在运行，启动速度更快

## 优势分析

### 相比复杂服务化方案的优势
1. **实现简单**：Go 后端只需添加 SCM 支持和少量 API 端点
2. **维护容易**：使用标准 Windows 服务管理
3. **兼容性好**：复用现有 HTTP API 接口和业务逻辑
4. **风险较低**：改动最小，影响范围可控
5. **渐进式部署**：支持优雅降级，确保向后兼容
6. **标准实践**：遵循 Windows 服务开发最佳实践

### 解决的问题
1. **消除 UAC 提示**：系统服务以 LocalSystem 运行
2. **开机自启动**：服务配置为自动启动
3. **持久化运行**：服务独立于 UI 应用运行
4. **API 始终可用**：服务启动后 HTTP API 立即可用
5. **快速启动**：UI 启动时无需等待后端进程启动

## 注意事项

1. **安装器权限**：需要管理员权限执行 sc.exe 命令
2. **服务稳定性**：确保后端程序稳定运行，避免服务异常
3. **端口冲突**：确保 8080/8081 端口不被其他程序占用
4. **升级处理**：升级时需要先停止服务，替换文件后重启
5. **降级兼容**：确保在系统服务不可用时能正常回退

## 总结

这个优雅降级方案通过 Inno Setup + sc.exe 的方式，以最小的代码改动实现了系统服务化部署。在提供最佳用户体验（无 UAC 提示）的同时，保持了向后兼容性和系统稳定性。通过健康检查和降级机制，确保在任何情况下用户都能正常使用应用程序。
