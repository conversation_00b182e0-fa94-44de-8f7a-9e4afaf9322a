# iOS/macOS 模块设计指导规范

## 📋 文档概述

本文档基于 `docs/go-backend-optimization-plan.md` 中的成功经验和规范，为iOS/macOS平台模块设计制定统一的指导规范，确保设计质量和一致性。

## 🎯 设计目标与标准

### 主要目标

1. **注释规范化**: 确保所有注释完全符合Swift最佳实践和项目规范
2. **Clean Architecture (清晰架构)**:
   - 代码逻辑结构简单明了，实现逻辑正确无误
   - 类和函数职责单一，单文件职责单一
   - 避免过度复杂的嵌套和依赖关系
3. **DRY原则 (避免重复)**: 识别并消除重复代码，提取公共协议和扩展
4. **业务分层**: 业务逻辑和平台代码正确分离
5. **代码清理**: 删除未使用的参数、变量、函数和导入
6. **Swift生态优先**: 充分利用Swift语言特性，不必完全按照Go实现
7. **减少复杂度**: 移除不必要的抽象层，避免过度设计

### 格式要求

- 所有注释中的AUTHOR字段统一填写：`wei`
- 所有注释中的HISTORY日期统一填写：`23/06/2025`
- 确保注释格式严格遵循Swift文档注释标准
- 使用英文注释，符合国际化标准

### 质量原则

- **单一职责**: 每个类、协议、函数只做一件事，逻辑清晰
- **适度复杂度**: 不强制行数限制，以职责单一和逻辑清晰为准
- **代码可读性**: 优先考虑代码的可读性和可维护性
- **功能对等**: 确保与Go后端功能完全对等，但实现方式可以Swift化
- **Swift优先**: 优先使用Swift原生特性，而非直接移植Go模式
- **简化设计**: 避免不必要的抽象层，保持设计简洁有效

## 📝 Swift注释规范

### 文件头注释模板

```swift
/**
 * FILE: [文件名].swift
 *
 * DESCRIPTION:
 *     [文件功能描述]
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */
```

### 协议注释模板

```swift
/**
 * NAME: [协议名]
 *
 * DESCRIPTION:
 *     [协议功能描述]
 *
 * METHODS:
 *     [方法名] - [方法描述]
 *     [方法名] - [方法描述]
 */
public protocol ProtocolName {
    // 协议定义
}
```

### 结构体/类注释模板

```swift
/**
 * NAME: [结构体/类名]
 *
 * DESCRIPTION:
 *     [结构体/类功能描述]
 *
 * PROPERTIES:
 *     [属性名] - [属性描述]
 *     [属性名] - [属性描述]
 */
public struct/class StructName {
    // 实现
}
```

### 函数注释模板

```swift
/**
 * NAME: [函数名]
 *
 * DESCRIPTION:
 *     [函数功能描述]
 *
 * PARAMETERS:
 *     [参数名] - [参数描述]
 *     [参数名] - [参数描述]
 *
 * RETURNS:
 *     [返回值描述]
 *
 * THROWS:
 *     [异常描述] (如果适用)
 */
public func functionName() throws -> ReturnType {
    // 实现
}
```

## 🏗️ 架构设计原则

### 1. 模块分层原则

```
Service Layer (服务层)
    ↓ 依赖
Connection Layer (连接层)
    ↓ 依赖  
Protocol Layer (协议层)
    ↓ 依赖
Platform Layer (平台层)
    ↓ 依赖
Infrastructure Layer (基础设施层)
```

**依赖规则**:
- 上层可以依赖下层，下层不能依赖上层
- 同层模块之间避免循环依赖
- 通过协议抽象降低耦合度

### 2. 单一职责原则

**文件职责**:
- 每个Swift文件专注于一个主要功能
- 相关的协议、结构体、扩展可以在同一文件中
- 文件大小控制在500行以内，超过时考虑拆分

**类/结构体职责**:
- 每个类或结构体只负责一个明确的功能
- 避免"上帝对象"，将复杂功能拆分为多个协作对象
- 优先使用组合而非继承

**函数职责**:
- 每个函数只做一件事
- 函数名清晰表达其功能
- 参数数量控制在5个以内

### 3. 协议优先设计

```swift
// 优先定义协议
public protocol LoggerProtocol {
    func log(_ message: String, level: LogLevel)
}

// 然后提供具体实现
public struct OSLogLogger: LoggerProtocol {
    public func log(_ message: String, level: LogLevel) {
        // 实现
    }
}
```

## 🔧 技术规范

### 1. Swift语言特性使用

**推荐使用**:
- `async/await` 用于异步操作
- `Actor` 用于并发安全
- `@MainActor` 用于UI更新
- `Result<Success, Failure>` 用于错误处理
- `Codable` 用于序列化
- `@frozen` 用于性能关键的枚举

**谨慎使用**:
- `@objc` 和 `NSObject` 继承（仅在必要时使用）
- `UnsafePointer` 系列（仅在性能关键场景）
- `force unwrapping` (!)（仅在确保安全的情况下）

### 2. 内存管理规范

**NetworkExtension环境优化**:
```swift
// 使用weak引用避免循环引用
weak var delegate: SomeDelegate?

// 使用unowned用于确定的生命周期关系
unowned let parent: ParentObject

// 及时释放大对象
defer {
    largeDataBuffer = nil
}
```

**对象池模式**:
```swift
public protocol ObjectPool {
    associatedtype PooledObject
    func get() -> PooledObject
    func put(_ object: PooledObject)
}
```

### 3. 错误处理规范

**错误类型定义**:
```swift
public enum ModuleError: Error, LocalizedError {
    case configurationInvalid(String)
    case networkTimeout
    case authenticationFailed
    
    public var errorDescription: String? {
        switch self {
        case .configurationInvalid(let details):
            return "Configuration invalid: \(details)"
        case .networkTimeout:
            return "Network operation timed out"
        case .authenticationFailed:
            return "Authentication failed"
        }
    }
}
```

**错误传播**:
```swift
// 使用Result类型
public func performOperation() -> Result<Data, ModuleError> {
    // 实现
}

// 使用throws
public func performOperation() throws -> Data {
    // 实现
}
```

## 📊 与Go后端对应关系

### 1. 模块映射规则

| Go模块 | Swift模块 | 对应关系 | 实现策略 |
|--------|-----------|----------|----------|
| `internal/common/logger/` | `Infrastructure/Logging/` | 日志系统 | OSLog原生集成 |
| `internal/common/errors/` | `Infrastructure/ErrorHandling/` | 错误处理 | Swift Error机制 |
| `internal/common/pool/` | `Infrastructure/DataBufferPool/` | 数据缓冲 | 简化版，利用ARC |
| `internal/common/goroutine/` | ~~移除~~ | ~~并发管理~~ | 使用Swift原生并发 |
| `internal/protocol/` | `Protocol/` | 协议实现 | 保持兼容性 |
| `internal/platform/` | `Platform/` | 平台抽象 | NetworkExtension优化 |
| `internal/connection/` | `Connection/` | 连接管理 | Actor模式 |
| `internal/service/` | `Service/` | 服务层 | async/await |

### 2. Swift化设计原则

**功能对等，实现Swift化**:
- Swift实现必须提供与Go版本相同的功能
- 优先使用Swift原生特性（async/await, Actor, ARC等）
- 接口设计符合Swift API设计指南
- 性能指标达到相同水平，但实现方式可以不同

**数据结构适配**:
- 核心数据结构保持逻辑一致
- 字段名遵循Swift命名规范（camelCase）
- 序列化格式必须兼容
- 利用Swift的Codable进行序列化

**架构简化原则**:
- 移除Go特有的抽象层（如goroutine管理）
- 利用Swift系统级特性替代手动管理
- 减少不必要的中间层和包装类
- 保持代码简洁和可维护性

## 🔍 质量检查清单

### ✅ 注释格式规范化
- [ ] 文件头注释符合模板
- [ ] AUTHOR字段统一为：`wei`
- [ ] HISTORY字段统一为：`23/06/2025`
- [ ] 协议注释包含NAME、DESCRIPTION、METHODS
- [ ] 结构体/类注释包含NAME、DESCRIPTION、PROPERTIES
- [ ] 函数注释包含NAME、DESCRIPTION、PARAMETERS、RETURNS

### ✅ Clean Architecture原则
- [ ] 类/结构体职责单一
- [ ] 单文件职责明确
- [ ] 逻辑清晰简洁
- [ ] 避免过度复杂的嵌套和依赖关系

### ✅ DRY原则应用
- [ ] 识别并提取重复代码
- [ ] 创建公共协议和扩展
- [ ] 统一错误处理模式
- [ ] 统一日志记录格式

### ✅ 代码清理
- [ ] 删除未使用的导入
- [ ] 删除未使用的变量和常量
- [ ] 删除未使用的函数和方法
- [ ] 删除注释掉的代码

### ✅ 业务分层验证
- [ ] 业务逻辑与平台代码分离
- [ ] 协议抽象正确使用
- [ ] 依赖关系清晰合理

### ✅ Swift特性应用
- [ ] 正确使用async/await
- [ ] 适当使用Actor模式
- [ ] 合理的内存管理
- [ ] 符合Swift API设计指南

## 🚀 执行流程

### 阶段一: 设计规范确立 ✅ (已完成)
- ✅ 已分析Go后端优化经验
- ✅ 已制定Swift设计规范
- ✅ 已建立质量检查标准

### 阶段二: 逐模块设计执行
- **执行方式**: 从基础设施层开始，逐步推进到上层模块
- **确认机制**: 每完成一个模块后提供详细设计说明，等待review确认后再继续
- **质量保证**: 每次设计后进行规范验证，确保符合所有质量标准

### 阶段三: 设计一致性验证
- **接口验证**: 确保模块间接口设计一致
- **依赖验证**: 验证模块依赖关系合理
- **功能验证**: 确保与Go后端功能对等

---

**AUTHOR**: wei  
**HISTORY**: 23/06/2025 create design guidelines based on go-backend-optimization-plan.md
