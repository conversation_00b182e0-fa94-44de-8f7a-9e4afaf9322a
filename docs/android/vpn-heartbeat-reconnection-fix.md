# Android VPN心跳重连机制状态管理修复

## 问题描述

Android版本的VPN心跳重连机制存在状态管理问题，当心跳超时触发重连时，系统直接从`connected`状态跳转到`connecting`状态，导致"Another operation is in progress"错误。

### 错误日志分析
```
W/ConnectionManager(17646): [HeartbeatManager.kt:invokeSuspend:342] Heartbeat timeout detected, triggering reconnection [reason=Heartbeat timeout detected, current_state=connected]
I/ITforceWAN-ConnectionManager(17646): [ConnectionManager.kt:handleAutoReconnection:1611] VPN state changed [old_state=connected, new_state=connecting, timestamp=1752673671291]
E/ITforceWAN-ConnectionManager(17646): [ConnectionManager.kt:access$handleAutoReconnection:96] Auto-reconnection failed [reason=Heartbeat timeout: Heartbeat timeout detected, server_id=2]
E/ITforceWAN-ConnectionManager(17646): ConnectionFailed(errorMessage=Another operation is in progress, serverAddress=null, retryCount=0)
W/ConnectionManager(17646): [ConnectionManager.kt:handleAutoReconnection:1638] Invalid state transition blocked [from_state=connecting, to_state=disconnected]
```

## 根本原因

1. **状态转换冲突**：`handleAutoReconnection`方法先设置为`Reconnecting`状态，然后调用`disconnect()`，但`disconnect()`方法会尝试设置`Disconnecting`状态，与当前的`Reconnecting`状态产生冲突
2. **操作进行中检查**：`connect`方法检查`isOperationInProgress`时，`Reconnecting`状态返回`true`，导致"Another operation is in progress"错误
3. **状态转换规则缺失**：从`Reconnecting`到`Connecting`的状态转换规则未定义

## 修复方案

### 1. 修复重连逻辑流程 (`ConnectionManager.kt`)

**修改前的问题流程**：
```
connected → reconnecting → disconnect() → 状态冲突
```

**修复后的正确流程**：
```
connected → disconnecting → disconnected → reconnecting → connecting → connected
```

**具体修改**：
- 在`handleAutoReconnection`方法中，先执行完整的断开连接流程
- 确保状态正确转换到`disconnected`后，再设置为`reconnecting`状态
- 添加强制清理逻辑，防止断开连接失败时的资源泄漏

### 2. 允许从Reconnecting状态进行连接 (`ConnectionManager.kt`)

**修改前**：
```kotlin
if (currentState.isOperationInProgress) {
    return@withContext Result.failure(
        VPNServiceError.ConnectionFailed("Another operation is in progress")
    )
}
```

**修改后**：
```kotlin
// Allow connection from Reconnecting state (auto-reconnection scenario)
// Block connection if other operations are in progress (Connecting, Disconnecting)
if (currentState.isOperationInProgress && currentState !is VPNState.Reconnecting) {
    return@withContext Result.failure(
        VPNServiceError.ConnectionFailed("Another operation is in progress")
    )
}
```

### 3. 完善状态转换规则 (`VPNState.kt`)

**修改前**：
```kotlin
is Reconnecting -> {
    // From Reconnecting, can go to Connected, Error, or Disconnecting
    to is Connected || to is Error || to is Disconnecting
}
```

**修改后**：
```kotlin
is Reconnecting -> {
    // From Reconnecting, can go to Connecting, Connected, Error, or Disconnecting
    to is Connecting || to is Connected || to is Error || to is Disconnecting
}
```

## 修复效果

1. **正确的状态转换**：重连流程遵循正确的状态转换序列
2. **避免状态冲突**：消除"Another operation is in progress"错误
3. **保持一致性**：与手动断开重连逻辑保持一致
4. **资源清理**：确保在重连过程中正确清理资源

## 测试建议

1. **心跳超时测试**：模拟网络中断，验证心跳超时重连是否正常工作
2. **状态转换测试**：验证重连过程中的状态转换是否符合预期
3. **并发操作测试**：验证重连过程中不会与其他操作产生冲突
4. **资源清理测试**：验证重连失败时资源是否正确清理

## 相关文件

- `ui/flutter/android/app/src/main/kotlin/com/itforce/wan/connection/ConnectionManager.kt`
- `ui/flutter/android/app/src/main/kotlin/com/itforce/wan/connection/models/VPNState.kt`
