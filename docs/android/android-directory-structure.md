# Android项目目录结构

## 📋 概述

本文档描述了ITforce WAN Android项目的完整目录结构，基于架构设计文档创建的分层目录组织。

**创建时间**: 2025-01-10  
**基于文档**: `android-architecture-design.md`  
**目录根路径**: `ui/flutter/android/app/src/main/kotlin/com/itforce/wan/`

## 🏗️ 完整目录结构

```
ui/flutter/android/app/src/main/kotlin/com/itforce/wan/
├── MainActivity.kt                    # Flutter主活动
├── MainApplication.kt                 # 应用程序类
├── vpn/                              # VPN核心模块
│   ├── ITforceVPNService.kt          # Android VPN服务（继承VpnService）
│   ├── VPNServiceBinder.kt           # 服务绑定器
│   └── VPNServiceManager.kt          # VPN服务管理器
├── platform/                         # 平台集成模块
│   ├── PlatformChannelHandler.kt     # Platform Channel处理器
│   ├── VPNInterfaceManager.kt        # VPN接口统一管理
│   ├── VPNConfigBuilder.kt           # VPN配置构建器
│   ├── NetworkMonitor.kt             # 网络监听和自动重连
│   ├── PermissionManager.kt          # 权限和电池优化管理
│   └── SystemOptimizationManager.kt  # 系统优化管理
├── connection/                       # 连接管理模块
│   ├── ConnectionManager.kt          # 连接管理器（线程安全）
│   ├── ServerManager.kt              # 服务器管理器
│   ├── StateMachine.kt               # 统一状态管理
│   └── models/                       # 连接相关数据模型
│       ├── VPNState.kt               # VPN状态密封类
│       ├── ConnectionProgress.kt     # 连接进度枚举
│       └── ServerInfo.kt             # 服务器信息
├── protocol/                         # 协议实现模块
│   ├── SDWANProtocol.kt             # SDWAN协议实现
│   ├── EncryptionService.kt          # 加密服务
│   ├── AuthService.kt                # 认证服务
│   ├── PacketProcessor.kt            # 数据包处理器
│   └── models/                       # 协议数据模型
│       ├── ProtocolState.kt          # 协议状态枚举
│       └── PacketModels.kt           # 数据包模型
├── network/                          # 网络通信模块
│   ├── UDPConnection.kt              # UDP连接管理（DatagramSocket）
│   ├── NetworkInterface.kt           # 网络接口抽象
│   └── NetworkUtils.kt               # 网络工具类
├── infrastructure/                   # 基础设施模块
│   ├── logging/                      # 日志系统
│   │   ├── Logger.kt                 # 日志接口
│   │   └── AndroidLogger.kt          # Android日志实现
│   ├── error/                        # 错误处理
│   │   ├── VPNServiceError.kt        # VPN错误定义
│   │   └── ErrorHandler.kt           # 错误处理器
│   ├── performance/                  # 性能监控
│   │   ├── PerformanceManager.kt     # 性能监控管理器
│   │   ├── ObjectPoolManager.kt      # 对象池管理
│   │   └── BatteryOptimizationManager.kt # 电池优化
│   └── utils/                        # 工具类
│       ├── Extensions.kt             # Kotlin扩展函数
│       └── Constants.kt              # 常量定义
├── services/                         # 后台服务
│   ├── NetworkKeepAliveJobService.kt # 网络保活任务
│   └── HeartbeatReceiver.kt          # 心跳广播接收器
└── di/                               # 依赖注入模块
    ├── AppModule.kt                  # 应用模块
    ├── VPNModule.kt                  # VPN模块
    ├── NetworkModule.kt              # 网络模块
    └── PlatformModule.kt             # 平台模块
```

## 📊 架构层次映射

### Infrastructure Layer (基础设施层)
- `infrastructure/` - 日志、错误处理、性能监控、工具类

### Platform Layer (平台抽象层)  
- `platform/` - Android平台特定功能集成

### Protocol Layer (协议实现层)
- `protocol/` - SDWAN协议栈实现
- `network/` - UDP网络通信

### Core Logic Layer (核心逻辑层)
- `connection/` - 连接管理和状态管理

### Application Layer (应用服务层)
- `vpn/` - VPN服务实现
- `services/` - 后台服务

### Integration Layer (集成层)
- `MainActivity.kt`, `MainApplication.kt` - Flutter集成
- `di/` - 依赖注入配置

## ✅ 实施状态

### 已完成
- [x] 完整目录结构创建
- [x] 主要文件框架创建
- [x] 架构文档更新
- [x] 实施计划制定

### 待实施
- [ ] 各模块具体实现
- [ ] 单元测试编写
- [ ] 集成测试验证
- [ ] 性能优化调整

## 🎯 下一步计划

1. **Phase 1: Infrastructure Layer** - 实现基础设施组件
2. **Phase 2: Platform Layer** - 实现Android平台集成
3. **Phase 3: Protocol Layer** - 实现SDWAN协议栈
4. **Phase 4: Core Logic Layer** - 实现连接管理逻辑
5. **Phase 5: Application Layer** - 实现VPN服务
6. **Phase 6: Integration Layer** - 完成Flutter集成

---

**创建时间**: 2025-01-10  
**负责人**: wei  
**状态**: 目录结构已创建，待开始实施
