# Android移动数据接口检测测试指南

## 测试目标

验证Android版本在移动数据连接（4G/5G）时能正确获取网络接口名称与本地IP地址，解决统计界面显示"unknown"的问题。

## 测试环境准备

### 1. 测试设备要求

- **不同芯片平台**：
  - Qualcomm设备（小米、一加、索尼等）
  - MediaTek设备（部分小米、OPPO、vivo等）
  - 三星Exynos设备
  - 华为麒麟设备

- **不同Android版本**：
  - Android 6.0+ (API 23+)
  - 重点测试Android 10+ (API 29+)

### 2. 网络环境

- **移动数据连接**：
  - 4G LTE连接
  - 5G连接（如果设备支持）
  - 不同运营商（中国移动、联通、电信）

- **WiFi连接**：
  - 作为对比测试
  - 验证WiFi接口检测正常

## 测试用例

### 测试用例1：移动数据接口检测

**测试步骤**：
1. 关闭WiFi，仅使用移动数据
2. 启动应用并连接VPN
3. 查看统计界面的接口信息

**预期结果**：
- 接口名称：显示具体接口名（如`rmnet0`, `ccmni0`等）
- 本地IP：显示运营商分配的IP地址
- 不应显示"unknown"

**验证方法**：
```bash
# 通过adb查看实际接口
adb shell ip addr show
adb shell cat /proc/net/route
```

### 测试用例2：不同芯片平台验证

**Qualcomm设备预期接口**：
- `rmnet0`, `rmnet1`
- `r_rmnet0`, `r_rmnet1`

**MediaTek设备预期接口**：
- `ccmni0`, `ccmni1`

**其他设备可能的接口**：
- `pdp_ip0`, `pdp_ip1`
- `v4-rmnet0`

### 测试用例3：网络切换测试

**测试步骤**：
1. 在WiFi下连接VPN，记录接口信息
2. 切换到移动数据，观察接口信息变化
3. 再切换回WiFi，验证接口信息更新

**预期结果**：
- WiFi: `wlan0` + WiFi IP地址
- 移动数据: `rmnet0`/`ccmni0` + 移动数据IP
- 切换过程中应正确更新，不出现"unknown"

### 测试用例4：日志验证

**查看调试日志**：
```bash
adb logcat | grep -E "(NetworkUtils|ConnectivityManager|interface)"
```

**预期日志内容**：
```
Found interface via ConnectivityManager: interface_name=rmnet0, ip_address=10.x.x.x, network_type=Mobile
Interface info retrieved: physical_interface=rmnet0, physical_ip=10.x.x.x, tunnel_ip=172.16.x.x
```

## 测试工具和命令

### 1. 查看网络接口

```bash
# 查看所有网络接口
adb shell ip addr show

# 查看路由表
adb shell ip route show

# 查看默认路由
adb shell ip route show default
```

### 2. 查看ConnectivityManager状态

```bash
# 查看网络状态
adb shell dumpsys connectivity

# 查看活动网络
adb shell dumpsys connectivity | grep -A 10 "Active networks"
```

### 3. 应用日志过滤

```bash
# 过滤网络相关日志
adb logcat | grep -E "(NetworkUtils|getPhysicalInterface|ConnectivityManager)"

# 过滤接口信息日志
adb logcat | grep -E "(Interface info|physical_interface|tunnel_ip)"
```

## 问题排查

### 1. 仍显示"unknown"

**可能原因**：
- ConnectivityManager返回null
- LinkProperties中没有接口名称
- 权限问题

**排查步骤**：
1. 检查日志中的错误信息
2. 验证网络权限
3. 手动查看系统接口状态

### 2. 接口名称不匹配

**可能原因**：
- 新的接口命名模式
- 厂商自定义接口名称

**解决方案**：
1. 记录实际接口名称
2. 更新接口优先级列表
3. 添加新的接口模式

### 3. IP地址获取失败

**可能原因**：
- IPv6地址被过滤
- 链路本地地址被排除
- 地址格式问题

**排查方法**：
1. 检查LinkAddress列表
2. 验证地址过滤逻辑
3. 查看系统实际IP配置

## 性能测试

### 1. 接口检测耗时

**测试方法**：
```kotlin
val startTime = System.currentTimeMillis()
val result = NetworkUtils.getPhysicalInterfaceInfo(context)
val duration = System.currentTimeMillis() - startTime
logInfo("Interface detection took ${duration}ms")
```

**预期结果**：
- ConnectivityManager方法：< 50ms
- NetworkInterface枚举：< 100ms

### 2. 内存使用

**监控要点**：
- 无内存泄漏
- 对象及时释放
- GC压力正常

## 回归测试

### 1. 原有功能验证

- WiFi连接正常工作
- 以太网连接正常工作
- 向后兼容性保持

### 2. 错误处理验证

- 无网络时返回"unknown"
- 异常情况不崩溃
- 日志记录完整

## 测试报告模板

```
测试设备：[设备型号] [Android版本] [芯片平台]
测试网络：[运营商] [网络类型]
测试结果：
- 接口名称：[实际检测到的接口名]
- 本地IP：[实际IP地址]
- 检测方法：[ConnectivityManager/NetworkInterface]
- 检测耗时：[毫秒]
- 问题记录：[如有问题请详细描述]
```

## 自动化测试

建议创建自动化测试脚本：

```kotlin
@Test
fun testMobileDataInterfaceDetection() {
    // 模拟移动数据环境
    // 调用接口检测方法
    // 验证结果不为"unknown"
    // 验证接口名称格式正确
}
```

通过以上测试，可以全面验证移动数据接口检测功能的正确性和稳定性。
