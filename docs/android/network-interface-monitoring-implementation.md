# Android网络接口监控实现（简化版）

## 概述

本文档描述了Android版本中简化的网络接口监控功能，专注于检测物理网络接口变化并通知UI进行VPN重连。

## 核心逻辑

**简单原则**: 当物理接口与connect时获取的接口不一致时，通知UI进行重连。

### 1. NetworkMonitor创建和初始化

- **位置**: `ConnectionManager.kt` 第203-206行
- **实现**: 使用lazy初始化创建NetworkMonitor实例，并设置ConnectionManager为delegate

```kotlin
private val networkMonitor: NetworkMonitor by lazy {
    NetworkMonitor(context).apply {
        delegate = this@ConnectionManager
    }
}
```

### 2. 网络监听启动和停止

#### 启动监听
- **触发时机**: VPN连接成功后
- **位置**: `ConnectionManager.connect()` 方法中，第541行
- **功能**: 
  - 记录当前物理接口信息
  - 启动NetworkMonitor监听
  - 设置VPN连接状态为true

```kotlin
// Start network interface monitoring for reconnection detection
startNetworkInterfaceMonitoring()
```

#### 停止监听
- **触发时机**: VPN断开连接时
- **位置**: `ConnectionManager.disconnect()` 方法中，第634-639行
- **功能**:
  - 设置VPN连接状态为false
  - 停止NetworkMonitor监听

```kotlin
// Stop network interface monitoring
val networkStopStartTime = System.currentTimeMillis()
stopNetworkInterfaceMonitoring()
```

### 3. 物理接口信息记录

#### recordCurrentPhysicalInterface()
- **功能**: 在VPN连接时记录当前物理接口信息
- **位置**: `ConnectionManager.kt` 第3151-3176行
- **实现**: 调用getCurrentPhysicalInterface()获取接口名称和IP地址

#### getCurrentPhysicalInterface()
- **功能**: 获取当前物理网络接口信息，排除VPN接口
- **位置**: `ConnectionManager.kt` 第3178-3222行
- **过滤逻辑**: 排除tun、ppp、vpn开头的接口名称

### 4. 物理接口变化检测

#### NetworkMonitor.checkPhysicalInterfaceChange()
- **触发时机**: 网络链路属性变化时
- **位置**: `NetworkMonitor.kt` 第534-597行
- **检测逻辑**:
  1. 获取ConnectionManager记录的接口信息
  2. 获取当前物理接口信息
  3. 比较接口名称和IP地址
  4. 如果发生变化，通知delegate进行重连

#### getCurrentPhysicalInterfaceInfo()
- **功能**: 获取当前物理接口信息（NetworkMonitor版本）
- **位置**: `NetworkMonitor.kt` 第599-642行
- **实现**: 与ConnectionManager中的方法类似，排除VPN接口

### 5. UI重连通知

#### handleNetworkChange()
- **功能**: 处理网络变化并通知UI
- **位置**: `ConnectionManager.kt` 第772-820行
- **通知机制**: 使用VPNStateEventSender发送RECONNECT_REQUIRED事件

```kotlin
vpnStateEventSender.sendReconnectRequiredEvent(
    reason = VPNStateEventSender.RECONNECT_REASON_NETWORK_CHANGE,
    message = "Network interface configuration has changed, reconnection required: $reason",
    networkInfo = networkInfo
)
```

## 工作流程

### VPN连接时
1. 调用`recordCurrentPhysicalInterface()`记录当前接口信息
2. 调用`networkMonitor.startMonitoring()`开始监听
3. 调用`networkMonitor.setVPNConnected(true)`启用变化检测

### 网络变化时
1. NetworkMonitor检测到网络变化
2. 调用`checkPhysicalInterfaceChange()`比较接口信息
3. 如果检测到变化，调用`delegate.onNetworkChanged()`
4. ConnectionManager收到通知，调用`handleNetworkChange()`
5. 通过VPNStateEventSender通知Flutter UI进行重连

### VPN断开时
1. 调用`networkMonitor.setVPNConnected(false)`禁用变化检测
2. 调用`networkMonitor.stopMonitoring()`停止监听

## 检测的网络变化类型

1. **网络类型变化**: WiFi ↔ 移动网络
2. **网络ID变化**: 同类型网络间切换（如WiFi热点切换）
3. **接口名称变化**: 物理接口名称改变
4. **IP地址变化**: 接口IP地址改变

## 与iOS版本的一致性

- **功能对等**: 实现了与iOS版本相同的网络接口监控功能
- **通知机制**: 使用相同的重连通知逻辑
- **架构模式**: 采用delegate模式，保持架构一致性

## 测试

测试文件位于: `ui/flutter/android/app/src/test/kotlin/com/panabit/client/connection/NetworkInterfaceMonitoringTest.kt`

包含以下测试用例:
- NetworkMonitor初始化测试
- 监听启动/停止测试
- VPN连接状态影响测试
- 物理接口变化检测测试
- 网络类型变化测试
- 接口名称变化测试

## 注意事项

1. **VPN接口排除**: 确保不监控VPN自身的tun接口
2. **性能优化**: 只在VPN连接时启用监听，避免不必要的资源消耗
3. **错误处理**: 包含完整的异常处理和日志记录
4. **线程安全**: 使用原子变量和协程确保线程安全
