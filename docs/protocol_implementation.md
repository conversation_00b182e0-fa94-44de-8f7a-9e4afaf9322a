# SDWAN ZZVPN 协议实现文档

## 1. 协议概述

SDWAN ZZVPN 是一种专为企业级 VPN 应用设计的安全隧道协议。该协议支持多种加密方式，提供可靠的数据传输和连接管理功能，适用于各种网络环境和平台。

### 1.1 协议特性

- **多平台支持**：支持 Windows、Linux、macOS、Android、iOS 等平台
- **灵活加密**：支持 AES 和 XOR 等多种加密算法
- **可靠传输**：基于 UDP 的可靠数据传输机制
- **智能路由**：支持分片重组和段路由功能
- **状态管理**：完整的连接状态机和会话管理

### 1.2 实现状态

**多平台实现完成度**：
- ✅ **Go 后端** (`internal/protocol/`): 100% 完成，生产级实现
- ✅ **Swift iOS/macOS** (`Sources/Protocol/`): 100% 完成，Actor 模式实现
- ✅ **Kotlin Android** (`protocol/`): 100% 完成，协程模式实现

**核心功能实现**：
- ✅ **包头格式和签名验证**：8字节包头，MD5签名验证
- ✅ **TLV 属性编码解析**：完整的 Type-Length-Value 属性处理
- ✅ **用户认证和会话建立**：OPEN/OPENACK 握手流程
- ✅ **多种加密算法**：XOR、AES-ECB 加密支持
- ✅ **数据传输协议**：DATA 包的加密传输
- ✅ **心跳和连接维护**：ECHO 包的定期发送和响应
- ✅ **状态机管理**：严格的协议状态转换控制

## 2. 协议包格式

### 2.1 通用包头

每个协议包都包含标准的8字节包头结构（基于实际代码实现）：

```c
typedef struct sdwan_pkthdr {
    uint8_t type;      // 包类型（1字节）
    uint8_t encrypt;   // 加密标志（1字节）
    uint16_t sid;      // 会话ID（2字节，大端序）
    uint32_t token;    // 会话token（4字节，大端序）
} sdwan_pkthdr_t;
```

#### 字段说明
- **Type (1字节)**：包类型标识，基于 `internal/protocol/common/constants.go`
  - `0x13` (19) SDWAN_OPEN - 认证握手包
  - `0x12` (18) SDWAN_OPENACK - 认证成功响应
  - `0x14` (20) SDWAN_DATA - 普通数据包
  - `0x15` (21) SDWAN_ECHOREQ - 心跳请求
  - `0x16` (22) SDWAN_ECHORES - 心跳响应
  - `0x17` (23) SDWAN_CLOSE - 连接关闭
- **Encrypt (1字节)**：加密方法标识
  - `0x00` NONE - 无加密
  - `0x01` XOR - XOR 加密
  - `0x02` AES - AES-ECB 加密
- **Session ID (2字节)**：会话标识符，大端序网络字节序
- **Token (4字节)**：认证令牌，大端序网络字节序

#### 多平台实现一致性
- **Go 后端**：`internal/protocol/common/packet.go` - PacketHeader 结构体
- **Swift iOS/macOS**：`Sources/Protocol/Common/PacketHeader.swift` - PacketHeader 结构体
- **Kotlin Android**：`protocol/models/PacketModels.kt` - PacketHeader 数据类

### 2.2 包签名机制

所有控制类型的数据包都包含 MD5 签名：
- **签名位置**：紧跟在包头之后的 16 字节
- **签名内容**：`MD5(包头 + "mw")`
- **验证范围**：OPEN、OPENACK、ECHOREQ、ECHORES、CLOSE 等控制包

### 2.3 TLV 属性区

包头和签名之后是 TLV (Type-Length-Value) 属性区，基于实际代码实现：

```
+--------+--------+--------+--------+
| Type   | Length |    Value...     |
+--------+--------+--------+--------+
```

#### TLV 格式说明
- **Type (1字节)**：属性类型标识
- **Length (1字节)**：属性长度（包括 Type 和 Length 本身）
- **Value (N字节)**：属性值数据，长度为 Length-2

#### 属性类型定义
基于 `internal/protocol/common/constants.go` 和各平台实现：

- `0x01` SDWAN_USERNAME - 用户名（变长）
- `0x02` SDWAN_PASSWORD - 加密后的密码（16字节）
- `0x03` SDWAN_MTU - 最大传输单元（2字节）
- `0x04` SDWAN_IP - IPv4 地址（4字节）
- `0x05` SDWAN_DNS - DNS 服务器（4字节）
- `0x06` SDWAN_GATEWAY - 网关地址（4字节）
- `0x07` SDWAN_NETMASK - 网络掩码（4字节）
- `0x08` SDWAN_ENCRYPT - 加密类型（1字节）
- `0x0A` SDWAN_TIMESTAMP - 时间戳（8字节）
- `0x0B` SDWAN_DELAY - 延迟信息（4字节）
- `0x0C` SDWAN_SDRT - SDRT 标签（4字节）

#### 属性顺序要求
**OPEN 包中的属性必须按以下顺序排列**（服务器严格验证）：
1. **MTU** - 必须是第一个属性
2. **USERNAME** - 必须是第二个属性
3. **PASSWORD** - 必须是第三个属性
4. **ENCRYPT** - 可选，加密类型
5. 其他可选属性
- `0x08` ENCRYPT - 加密类型

## 3. 认证流程

### 3.1 OPEN 包处理

OPEN 包用于用户认证和参数协商：

#### 组包流程
1. **包头设置**：Type=OPEN, Encrypt=配置值, SID=0, Token=0
2. **签名计算**：计算并添加 MD5 签名
3. **TLV 属性**：按固定顺序添加属性
   - MTU 属性（必须第一个）
   - 用户名属性（必须第二个）
   - 加密密码属性（必须第三个）
   - 其他可选属性

#### 密码加密
- **加密密钥**：`MD5("mw" + 用户名)`
- **加密算法**：AES-ECB 模式
- **处理流程**：
  1. 密码填充到 32 字节
  2. AES-ECB 加密
  3. 取前 16 字节作为密码属性值

### 3.2 OPENACK 包处理

服务器认证成功后返回 OPENACK 包：

#### 处理流程
1. **状态验证**：确认当前处于认证状态
2. **签名验证**：验证包签名的正确性
3. **属性解析**：提取服务器配置参数
4. **状态切换**：切换到数据传输状态
5. **网络配置**：配置 TUN 设备和路由

#### 配置参数
- **IP 地址**：VPN 分配的 IP 地址
- **网关地址**：VPN 网关地址
- **DNS 服务器**：VPN DNS 服务器
- **MTU 设置**：隧道 MTU 大小

### 3.3 认证失败处理

收到 OPENREJ 包时的处理：
1. **状态验证**：确认当前处于认证状态
2. **错误记录**：记录认证失败原因
3. **状态重置**：重置连接状态
4. **用户通知**：通知用户认证失败

## 4. 数据传输

### 4.1 加密机制

#### 会话密钥生成
- **密钥算法**：`MD5(用户名 + 密码)`
- **密钥长度**：16 字节
- **用途**：数据包加密和解密

#### 数据加密
- **XOR 加密**：每 8 字节与会话密钥异或
- **剩余处理**：不足 8 字节的部分与密钥前 N 字节异或
- **AES 加密**：使用 AES-ECB 模式，无 PKCS#7 填充

### 4.2 数据包处理

#### 发送流程
1. **数据获取**：从 TUN 设备读取 IP 数据包
2. **包封装**：添加协议包头
3. **数据加密**：根据配置进行数据加密
4. **网络发送**：通过 UDP 连接发送

#### 接收流程
1. **网络接收**：从 UDP 连接接收数据
2. **包解析**：解析协议包头和属性
3. **数据解密**：解密数据包内容
4. **转发处理**：将数据写入 TUN 设备

### 4.3 心跳机制

#### 心跳包格式
```
+--------+--------+--------+--------+
|      包头 (8字节)              |
+--------+--------+--------+--------+
|      签名 (16字节)             |
+--------+--------+--------+--------+
|      时间戳 (8字节)            |
+--------+--------+--------+--------+
| 当前延迟 | 最小延迟 | 最大延迟 |
+--------+--------+--------+--------+
|      SDRT标签 (4字节)          |
+--------+--------+--------+--------+
```

#### 心跳处理
- **发送频率**：定期发送心跳包保持连接
- **延迟统计**：记录和更新网络延迟信息
- **连接检测**：检测连接状态和网络质量

## 5. 状态机管理

### 5.1 连接状态

- **FREE**：未初始化状态
- **INIT**：DNS 查询状态
- **INIT1**：DNS 解析完成，准备认证
- **AUTH**：用户认证进行中
- **DATA**：数据传输状态
- **CLOSED**：连接已关闭
- **AUTHFAIL**：认证失败状态

### 5.2 状态转换

#### 正常流程
```
FREE → INIT → INIT1 → AUTH → DATA
```

#### 异常处理
```
任何状态 → CLOSED (连接错误)
AUTH → AUTHFAIL (认证失败)
DATA → AUTH (重新认证)
```

### 5.3 状态驱动

- **定时器驱动**：每秒检查一次状态
- **事件驱动**：根据网络事件触发状态变化
- **超时处理**：处理各种超时情况
- **自动重连**：连接断开后的自动重连机制

## 6. 高级功能

### 6.1 分片处理

#### 分片包格式
- **IPFRAG**：IPv4 分片包
- **IPFRAG6**：IPv6 分片包

#### 重组机制
- **分片标识**：根据 ID 和偏移量重组数据
- **超时处理**：处理分片超时和丢失
- **内存管理**：合理管理分片缓存

### 6.2 段路由 (SR)

#### SR 包处理
- **SEGRT**：段路由包类型
- **路径信息**：解析和处理路径信息
- **转发逻辑**：根据段路由信息转发数据

### 6.3 错误处理

#### 错误类型
- **网络错误**：连接超时、网络不可达
- **协议错误**：包格式错误、签名验证失败
- **认证错误**：用户名密码错误、权限不足

#### 处理策略
- **错误记录**：详细记录错误信息和上下文
- **自动恢复**：尝试自动恢复连接
- **用户通知**：友好的错误提示和解决建议

## 7. 性能优化

### 7.1 网络优化

- **连接复用**：复用 UDP 连接减少开销
- **缓冲管理**：优化数据包缓冲和处理
- **并发处理**：使用协程实现并发数据处理

### 7.2 内存优化

- **对象池**：复用数据包对象减少 GC 压力
- **缓存策略**：合理的数据缓存和清理
- **资源管理**：及时释放不需要的资源

### 7.3 CPU 优化

- **加密优化**：优化加密算法的实现
- **数据拷贝**：减少不必要的数据拷贝
- **算法优化**：优化关键路径的算法实现

## 8. 安全考虑

### 8.1 加密安全

- **密钥管理**：安全的密钥生成和存储
- **加密强度**：使用足够强度的加密算法
- **密钥轮换**：定期更新会话密钥

### 8.2 协议安全

- **签名验证**：严格验证包签名防止篡改
- **重放攻击**：防止数据包重放攻击
- **会话安全**：安全的会话管理和超时处理

### 8.3 实现安全

- **输入验证**：严格验证所有输入数据
- **缓冲区保护**：防止缓冲区溢出攻击
- **错误处理**：安全的错误处理，避免信息泄露

## 9. 兼容性和扩展

### 9.1 版本兼容

- **协议版本**：支持协议版本协商
- **向后兼容**：保持与旧版本的兼容性
- **功能检测**：动态检测和适配服务器功能

### 9.2 平台适配

- **字节序处理**：正确处理不同平台的字节序
- **数据类型**：使用标准数据类型确保兼容性
- **系统调用**：抽象平台特定的系统调用

### 9.3 功能扩展

- **插件机制**：支持协议功能的插件化扩展
- **配置驱动**：通过配置启用或禁用特定功能
- **API 接口**：提供清晰的扩展 API 接口
