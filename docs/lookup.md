1. 概述
为了支持多客户、多租户环境下的灵活配置和服务发现，VPN 客户端需要支持一种基于客户域（Customer Domain）的 lookup 机制。该机制允许客户端根据用户输入的客户域名（如 research.CNG.itforce 或 CNG.hongkong），自动寻址对应的服务地址，并区分不同的服务分发方式，包括 serverlist 和控制器两种。

本文档定义了客户域结构、lookup 请求与响应格式、服务发现的规则逻辑以及两种分发方式的区别和扩展支持策略。

2. 客户域（Customer Domain）定义
2.1 域结构
客户域采用 DNS 式的反向层级结构，从右到左依次表示：
- 顶级域：服务商名或控制器名称
- 次顶级域：客户名
- 次次顶级域（可选）：客户下的子部门或分流策略

2.2 分发模式一：Serverlist
结构示例：research.CNG.itforce

- 顶级：itforce（服务商名）
- 次顶级：CNG（客户名）
- 次次顶级：research（分流名）

地址拼接规则：
服务商只需注册一次顶级域，客户端自动根据以下规则拼接 serverlist 路径：
{address}/serverlist/{次顶级}/{次次顶级}/serverlist.json
示例：
输入客户域：research.CNG.itforce
Lookup 返回地址：https://www.itforce-tech.com
最终拼接路径：https://www.itforce-tech.com/serverlist/CNG/research/serverlist.json
特点
- 服务商只需维护一份 serverlist 根地址，列表都由服务商维护。
- 子客户/分流由客户端拼接，无需每次手动注册。

2.3 分发模式二：Controller
结构示例：CNG.hongkong
- 顶级：hongkong（Controller 名称）
- 次顶级：CNG（客户名）

行为说明：
客户端收到顶级控制器地址后，可向其发起认证请求或加载客户配置，进行下一步连接。

示例：
客户域：CNG.hongkong
Lookup 返回：https://www.hongkongController.com
客户端与控制器平台进行交互
特点：
- 客户集中在某一平台（Controller）管理
- Lookup 只需记录控制器顶级地址

如果需要支持客户域的模糊匹配(省略Controller域)，那就需要在增加客户customer时，在lookup上添加客户域与Controller对应关系，交互相较于频繁。
例如 输入：CNG
推导目标客户域：CNG.hongkong

2.4 示例
暂时无法在飞书文档外展示此内容

3. Lookup 服务
3.1 客户端行为
客户端启动后，用户输入客户域，客户端向 lookup 服务器发起查询请求，获取对应服务地址与类型。
3.2 Lookup 请求格式
- 方法：GET
- 参数：domain
示例请求：
GET https://www.lookup.com/lookup?domain={客户域}
3.3 Lookup 响应格式（JSON）
{
  "type": "serverlist",
  "address": "https://www.itforce-tech.com"
}
或：
{
  "type": "Controller",
  "address": "https://www.hongkongController.com"
}
3.4 字段说明
暂时无法在飞书文档外展示此内容

4. 客户端处理流程
用户输入客户域 → 客户端向 Lookup 请求 → 获取分发方式及地址 →
如果为 serverlist：拼接路径后请求 serverlist.json →
如果为 Controller：与 Controller 地址进行认证交互

5. lookup配置
分为两个部分：
- domains：支持精确匹配的顶级域（服务商或 Controller），每个域配置其访问地址、类型、客户列表等。
- mappings：支持模糊匹配（简写客户域 → 完整客户域）

{
  "domains": {
    "itforce": {
      "type": "serverlist",
      "address": "https://www.itforce-tech.com"
    },
    "hongkong": {
      "type": "Controller",
      "address": "https://www.hongkongController.com"
    }
  },
  "customers": {
    "itforce": [
      "research.CNG",
      "ACME"
    ],
    "hongkong": [
      "CNG"，
      "CTG"
    ]
  }
}

// 从 customers 字段生成 mappings
for domain, clients := range cfg.Customers {
    for _, customer := range clients {
        mappings[customer] = customer + "." + domain
    }
}
字段说明：
暂时无法在飞书文档外展示此内容
6. FAQ
Q: 所有客户都要录入 Lookup 吗？
A: 不需要。serverlist 模式下只需录入顶级服务商即可，客户端自动拼接。

Q: 客户域拼写错误会怎样？
A: Lookup 返回错误，客户端可提示“客户域无效”或“不存在”。

Q: 一个客户是否可以同时存在于 serverlist 与 Controller？
A: 建议一个顶级域只对应一种类型，避免逻辑冲突。
