# Windows网络连接管理机制实现

## 概述

本文档描述了为Windows版本移动应用实现的网络连接管理机制，该机制解决了网络接口变更和heartbeat超时等问题，通过后端主动通知、UI日志记录和重连逻辑调整来提供更稳定的连接体验。

## 实现目标

### 背景问题
- 网络接口变更导致连接不稳定
- heartbeat keepalive超时
- Windows自动重连逻辑需要优化

### 解决方案
1. **后端主动通知机制**：当检测到网络接口变更或heartbeat超时时，后端立即向UI发送事件通知
2. **UI日志记录**：UI接收到后端事件通知后，记录详细日志，包括精确的时间戳和断开连接的具体原因
3. **重连逻辑调整**：移除后端的自动重连机制，改为由UI主动控制重连流程

## 技术实现

### 1. 后端事件通知机制

#### 新增API事件类型
```go
// 新增重连事件常量
const (
    EventReconnectRequired = "reconnect_required"
)

// 重连原因常量
const (
    ReconnectReasonNetworkInterfaceChange = "network_interface_change"
    ReconnectReasonHeartbeatTimeout       = "heartbeat_timeout"
    ReconnectReasonConnectionLost         = "connection_lost"
)

// 重连事件数据结构
type ReconnectRequiredData struct {
    Reason    string `json:"reason"`
    Message   string `json:"message"`
    Timestamp int64  `json:"timestamp"`
}
```

#### StatusNotifier接口扩展
```go
type StatusNotifier interface {
    NotifyStatusChange(status *Status)
    NotifyReconnectRequired(reason, message string) // 新增方法
}
```

#### Heartbeat超时处理修改
- 原来：直接执行`disconnectInternal()`
- 现在：通过`StatusNotifier.NotifyReconnectRequired()`发送重连通知

#### 网络接口变更处理修改
- 原来：执行自动重连流程
- 现在：发送重连通知给UI，由UI控制重连流程

### 2. UI日志记录功能

#### WebSocket事件处理
```dart
case WebSocketEvents.reconnectRequired:
  logService.info('WebSocket', 'Reconnect required event received');
  _handleReconnectRequiredEvent(eventData, logService);
  break;
```

#### 详细日志记录
```dart
void _handleReconnectRequiredEvent(dynamic eventData, dynamic logService) {
  final reason = eventData['reason'] as String? ?? 'unknown';
  final message = eventData['message'] as String? ?? 'No details provided';
  final timestamp = eventData['timestamp'] as int? ?? 0;
  
  // 转换时间戳为可读格式
  final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
  final formattedTime = dateTime.toIso8601String();

  // 记录详细的重连需求日志
  logService.info('WebSocket', '=== RECONNECT REQUIRED EVENT ===');
  logService.info('WebSocket', 'Timestamp: $formattedTime');
  logService.info('WebSocket', 'Reason: $reason');
  logService.info('WebSocket', 'Message: $message');
  logService.info('WebSocket', '================================');
}
```

### 3. 重连逻辑调整

#### 后端自动重连禁用
```go
// 在Manager初始化时禁用自动重连
autoReconnectEnabled: false, // 禁用自动重连 - UI将处理重连
```

#### UI重连服务实现
```dart
class ReconnectService {
  Future<bool> handleReconnectRequired(String reason, String message, {Server? serverInfo}) async {
    // 步骤1: 断开当前连接
    final disconnectSuccess = await _performDisconnect();
    
    // 步骤2: 等待网络稳定
    await _waitForNetworkStability();
    
    // 步骤3: 设置路由（如果需要）
    await _setupRouting();
    
    // 步骤4: 重新连接
    final reconnectSuccess = await _performReconnect(serverInfo);
    
    return reconnectSuccess;
  }
}
```

#### 复用现有连接管理逻辑
- 使用`ConnectionManager.disconnect()`执行断开
- 使用`ConnectionManager.connect()`执行重连
- 通过`AppState.selectServer()`设置目标服务器

## 事件流程

### 网络接口变更流程
1. **后端检测**：`server_network_monitor.go`检测到网络接口变更
2. **事件通知**：调用`StatusNotifier.NotifyReconnectRequired("network_interface_change", message)`
3. **WebSocket推送**：通过WebSocket向UI发送`reconnect_required`事件
4. **UI处理**：WebSocket服务接收事件，记录详细日志
5. **重连执行**：触发重连流程：disconnect → 等待 → connect

### Heartbeat超时流程
1. **超时检测**：`connection_heartbeat.go`检测到heartbeat超时
2. **事件通知**：调用`StatusNotifier.NotifyReconnectRequired("heartbeat_timeout", message)`
3. **WebSocket推送**：通过WebSocket向UI发送`reconnect_required`事件
4. **UI处理**：WebSocket服务接收事件，记录详细日志
5. **重连执行**：触发重连流程：disconnect → 等待 → connect

## 关键特性

### 1. 统一事件处理
- 所有重连触发原因都使用同一个`reconnect_required`事件
- 通过`reason`字段区分具体原因
- 简化了UI端的事件处理逻辑

### 2. 详细日志记录
- 精确的时间戳记录
- 具体的断开原因
- 结构化的日志格式
- 便于问题诊断和分析

### 3. 复用现有逻辑
- 重连服务复用`ConnectionManager`的连接方法
- 参考iOS/Android平台的重连模式
- 保持跨平台一致性

### 4. 防并发保护
- 重连状态标志防止并发重连
- 重连尝试计数和最大限制
- 优雅的错误处理和恢复

## 测试验证

### 单元测试覆盖
- 重连服务基本功能测试
- 错误处理测试
- 并发保护测试
- 状态管理测试

### 集成测试场景
- 网络接口变更模拟
- Heartbeat超时模拟
- WebSocket事件传递验证
- 端到端重连流程验证

## 配置说明

### 后端配置
- 自动重连默认禁用
- Heartbeat超时阈值：45秒（3倍心跳间隔）
- 网络监控间隔：10秒

### UI配置
- 重连延迟：2秒
- 最大重连尝试：3次
- 网络稳定等待：3秒

## 兼容性

### 平台支持
- ✅ Windows：完整支持新的重连机制
- ✅ iOS/macOS：保持现有重连逻辑不变
- ✅ Android：保持现有重连逻辑不变

### 向后兼容
- 保留原有的StatusNotifier接口
- 新增方法为可选实现
- 支持渐进式升级

## 总结

本实现成功解决了Windows版本的网络连接管理问题，通过以下关键改进：

1. **主动通知**：后端主动通知UI重连需求，而不是静默处理
2. **详细日志**：完整记录重连事件的时间、原因和处理过程
3. **UI控制**：将重连控制权转移到UI层，提供更好的用户体验
4. **统一流程**：标准化的disconnect → 路由设置 → connect流程

该机制提供了更稳定、可控和可观测的网络连接管理体验，为Windows用户提供了与iOS/Android平台一致的高质量VPN连接服务。
