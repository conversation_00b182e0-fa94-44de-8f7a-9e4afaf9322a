# ITforce WAN API 规范文档

## 1. 概述

本文档描述了 ITforce WAN 客户端的完整 API 规范，包括多种通信机制以适配不同平台的架构特点。前端 Flutter 界面通过以下方式与后端服务通信，实现 VPN 连接控制、状态监控和配置管理：

**通信机制**：
- **Windows/Linux**: HTTP RESTful API + WebSocket 实时通信（与 Go 后端）
- **iOS/macOS**: Platform Channel + Event Channel（与 Swift 后端）
- **Android**: Platform Channel + Event Channel（与 Kotlin 后端，开发中）

**功能对等性**：所有平台提供相同的功能接口，但实现方式根据平台特点进行优化。

## 2. 基础信息

### 2.1 服务地址

- **HTTP API 基础地址**：`http://localhost:56543/api`
- **WebSocket 地址**：`ws://localhost:56544/ws`

### 2.2 通用响应格式

所有 HTTP API 响应使用统一的 JSON 格式：

```json
{
  "success": true|false,
  "message": "响应消息",
  "data": {
    // 响应数据对象
  },
  "error": {
    "error_code": 1001,
    "error_type": "error_type",
    "message": "详细错误信息"
  }
}
```

### 2.3 错误处理

经过重构优化，错误处理系统现在提供更加详细和一致的错误信息：

- **success**: `false`
- **message**: 用户友好的错误描述
- **error**: 详细的错误对象，包含：
  - `code`: 标准化错误码
  - `type`: 错误类型分类
  - `message`: 详细的技术错误信息
  - `component`: 发生错误的组件
  - `operation`: 失败的操作

#### 错误响应示例
```json
{
  "success": false,
  "message": "认证失败",
  "error": {
    "code": 1002,
    "type": "auth_failed",
    "message": "用户名或密码错误",
    "component": "connection_manager",
    "operation": "authenticate"
  }
}
```

### 2.4 状态码

- **200 OK**: 请求成功处理
- **400 Bad Request**: 请求参数错误或格式不正确
- **401 Unauthorized**: 认证失败或权限不足
- **404 Not Found**: 请求的资源不存在
- **500 Internal Server Error**: 服务器内部错误

### 2.5 性能优化

API 层经过全面重构，具备以下性能特性：

- **统一错误处理**: 标准化的错误响应格式
- **代码重复消除**: 统一的依赖检查和模型转换
- **异步处理**: 耗时操作在后台执行，立即返回响应
- **并发安全**: 完善的锁机制保护共享资源
- **监控集成**: 内置性能监控和指标收集

## 3. HTTP API 接口

### 3.1 用户认证

#### 用户登录
- **接口**: `POST /api/login`
- **功能**: 验证用户凭据并建立 VPN 连接
- **实现位置**: `internal/service/http/handlers/auth.go`

**特性**:
- 用户名密码验证
- 自动服务器选择（基于延迟和负载）
- 异步连接建立
- 完整的状态事件通知
- 错误处理和重试机制

- **请求体**:
  ```json
  {
    "username": "用户名",
    "password": "用户密码"
  }
  ```

- **成功响应**:
  ```json
  {
    "success": true,
    "message": "登录成功，正在连接到最佳服务器",
    "data": {
      "best_server": {
        "id": "server_001",
        "name": "服务器名称",
        "name_en": "Server Name",
        "server_name": "server.example.com",
        "server_port": 8000,
        "ping": 50,
        "isauto": true,
        "status": "online",
        "is_default": false
      }
    }
  }
  ```

- **错误响应**:
  ```json
  {
    "success": false,
    "message": "认证失败",
    "error": {
      "code": 1002,
      "type": "auth_failed",
      "message": "用户名或密码错误"
    }
  }
  ```

- **处理流程**:
  1. 解析和验证请求参数
  2. 验证系统依赖项
  3. 获取最佳服务器
  4. 执行用户认证
  5. 立即返回成功响应（包含best_server信息）
  6. **注意**: 不再自动连接，保持与iOS/Android平台一致
  7. UI需要手动发起连接请求

### 3.2 服务器管理

#### 获取服务器列表
- **接口**: `GET /api/servers`
- **功能**: 获取所有可用服务器的列表和状态信息
- **实现位置**: `internal/service/http/handlers/server.go`

**特性**:
- 服务器状态实时查询
- 延迟信息包含（如果已测试）
- 服务器分组和排序
- 默认服务器标识

- **响应数据**:
  ```json
  {
    "success": true,
    "message": "获取服务器列表成功",
    "data": {
      "servers": [
        {
          "id": "server_001",
          "name": "服务器名称",
          "name_en": "Server Name",
          "server_name": "server.example.com",
          "server_port": 8000,
          "ping": 50,
          "isauto": true,
          "status": "online",
          "is_default": false
        }
      ]
    }
  }
  ```

#### 测试服务器延迟
- **接口**: `POST /api/servers/ping`
- **功能**: 启动服务器延迟测试，结果通过 WebSocket 事件通知
- **实现位置**: `internal/service/http/handlers/server.go`

**特性**:
- 并发延迟测试
- 实时结果推送（WebSocket）
- 超时控制和错误处理
- 测试结果缓存

- **成功响应**:
  ```json
  {
    "success": true,
    "message": "服务器延迟测试已启动"
  }
  ```

- **处理流程**:
  1. 验证系统依赖项
  2. 后台启动延迟测试
  3. 立即返回确认响应
  4. 通过 WebSocket 推送测试结果
  5. 发送测试完成事件

#### 设置服务器列表源
- **接口**: `POST /api/servers/provider`
- **功能**: 更新服务器列表的获取源地址
- **请求体**:
  ```json
  {
    "url": "https://api.example.com/servers"
  }
  ```

### 3.3 连接控制

#### 建立 VPN 连接
- **接口**: `POST /api/connect`
- **功能**: 连接到指定的 VPN 服务器
- **实现位置**: `internal/service/http/handlers/connection.go`

**请求体**:
```json
{
  "server_id": "server_001"
}
```

**特性**:
- 异步连接建立
- 自动状态机管理
- 连接超时控制
- 错误重试机制
- WebSocket 状态推送

#### 断开 VPN 连接
- **接口**: `POST /api/disconnect`
- **功能**: 断开当前的 VPN 连接
- **实现位置**: `internal/service/http/handlers/connection.go`

**特性**:
- 优雅断开连接
- 资源清理
- 状态重置
- 事件通知

#### 获取连接状态
- **接口**: `GET /api/status`
- **功能**: 获取当前 VPN 连接的详细状态信息
- **实现位置**: `internal/service/http/handlers/status.go`

**响应数据**:
```json
{
  "success": true,
  "data": {
    "status": "connected",
    "message": "已连接到服务器",
    "server": {
      "id": "server_001",
      "name": "服务器名称",
      "server_name": "server.example.com",
      "server_port": 8000
    },
    "connected_time": **********,
    "session_info": {
      "session_id": 12345,
      "token": 67890
    },
    "traffic_stats": {
      "bytes_sent": 1048576,
      "bytes_received": 2097152,
      "packets_sent": 1024,
      "packets_received": 2048
    }
  }
}
```

### 3.4 系统信息

#### 获取网络接口信息
- **接口**: `GET /api/interface`
- **功能**: 获取当前网络接口的配置信息
- **响应数据**:
  ```json
  {
    "interface_name": "ItForce WAN",
    "local_ip": "*************",
    "tun_ip": "********",
    "mtu": 1400
  }
  ```

#### 健康检查
- **接口**: `GET /api/health`
- **功能**: 检查服务运行状态和基本信息
- **响应数据**:
  ```json
  {
    "status": "ok",
    "version": "1.0.0",
    "vpn_status": "connected",
    "uptime": 3600
  }
  ```

### 3.5 配置管理

#### 获取路由设置
- **接口**: `GET /api/settings/routing`
- **功能**: 获取当前的路由模式配置
- **响应数据**:
  ```json
  {
    "mode": "all",
    "custom_routes": ""
  }
  ```

#### 更新路由设置
- **接口**: `POST /api/settings/routing`
- **功能**: 更新路由模式和自定义路由规则
- **请求体**:
  ```json
  {
    "mode": "custom",
    "custom_routes": "***********/16,10.0.0.0/8"
  }
  ```

## 4. WebSocket 实时通信

### 4.1 连接建立

前端应在启动时建立 WebSocket 连接以接收实时事件：

```javascript
const ws = new WebSocket('ws://localhost:56544/ws');
```

#### 重构改进
- **并发安全**: 修复所有锁机制，确保线程安全
- **连接管理**: 完善的客户端注册、维护和清理
- **事件广播**: 统一的事件广播机制
- **心跳机制**: 定期心跳保持连接活跃
- **错误处理**: 完整的错误捕获和恢复

### 4.2 消息格式

所有 WebSocket 消息使用统一的 JSON 格式：

```json
{
  "event": "事件类型",
  "data": {
    // 事件相关数据
  },
  "timestamp": **********
}
```

### 4.3 连接生命周期

#### 连接建立
1. 客户端发起 WebSocket 连接
2. 服务器接受连接并注册客户端
3. 发送初始状态信息
4. 启动心跳机制

#### 连接维护
- 定期发送心跳事件保持连接活跃
- 监控连接状态，自动清理无效连接
- 并发安全的客户端管理

#### 连接断开
1. 客户端主动断开或网络异常
2. 服务器检测到连接断开
3. 清理客户端注册信息
4. 释放相关资源

### 4.3 事件类型

#### 连接状态事件
- **事件**: `status`
- **触发**: VPN 连接状态发生变化时
- **数据**: 包含新的连接状态、消息和服务器信息

#### 服务器列表更新事件
- **事件**: `servers`
- **触发**: 服务器列表更新或服务器状态变化时
- **数据**: 完整的服务器列表数组

#### 延迟测试事件
- **事件**: `ping_start` / `ping_complete` / `ping_results`
- **触发**: 服务器延迟测试的不同阶段
- **数据**: 测试状态或结果信息

#### 错误事件
- **事件**: `error`
- **触发**: 系统发生错误或异常时
- **数据**: 错误码、类型和详细描述

#### 网络接口事件
- **事件**: `interface_info`
- **触发**: 网络接口配置变化时
- **数据**: 接口名称、IP 地址和配置信息

#### 心跳事件
- **事件**: `heartbeat`
- **触发**: 定期发送以保持连接活跃
- **数据**: 时间戳信息

### 4.4 事件处理

前端应注册相应的事件处理器：

```javascript
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  switch (message.event) {
    case 'status':
      // 处理连接状态变化
      break;
    case 'servers':
      // 处理服务器列表更新
      break;
    case 'error':
      // 处理错误通知
      break;
    // 其他事件处理...
  }
};
```

## 5. 数据模型

### 5.1 服务器信息模型

```json
{
  "id": "唯一标识符",
  "name": "显示名称",
  "name_en": "英文名称",
  "server_name": "服务器地址",
  "server_port": 8000,
  "ping": 50,
  "isauto": true,
  "status": "online|offline|testing",
  "isdefault": false
}
```

### 5.2 连接状态模型

```json
{
  "status": "disconnected|connecting|connected|error",
  "message": "状态描述",
  "server": "服务器信息对象",
  "connected_time": **********,
  "session_info": {
    "session_id": 12345,
    "token": 67890
  }
}
```

### 5.3 错误信息模型

```json
{
  "error_code": 1001,
  "error_type": "network_error",
  "message": "网络连接失败",
  "details": "详细的技术错误信息"
}
```

## 6. 使用示例

### 6.1 完整连接流程

1. **用户登录**: `POST /api/login`
2. **获取服务器列表**: `GET /api/servers`
3. **建立 WebSocket 连接**: 监听状态事件
4. **发起连接**: `POST /api/connect`
5. **监听状态变化**: 通过 WebSocket 接收状态更新
6. **查询连接状态**: `GET /api/status`

### 6.2 服务器管理流程

1. **获取当前服务器列表**: `GET /api/servers`
2. **启动延迟测试**: `POST /api/servers/ping`
3. **监听测试结果**: WebSocket `ping_results` 事件
4. **选择最佳服务器**: 基于延迟结果选择服务器

### 6.3 错误处理流程

1. **监听错误事件**: WebSocket `error` 事件
2. **检查 API 响应**: 检查 `success` 字段和 `error` 对象
3. **用户友好提示**: 显示 `message` 字段内容
4. **技术日志记录**: 记录详细的错误信息用于调试

## 7. 安全考虑

### 7.1 认证安全

- 用户密码在传输前应进行适当的处理
- 会话令牌具有合理的过期时间
- API 访问需要有效的认证状态

### 7.2 通信安全

- 本地通信使用 localhost 接口
- WebSocket 连接包含心跳机制防止超时
- 敏感信息在日志中进行脱敏处理

### 7.3 数据保护

- 用户凭据在本地进行加密存储
- 配置信息包含适当的访问控制
- 网络传输数据经过协议层加密

## 8. Platform Channel 接口规范 (iOS/macOS/Android)

### 8.1 Method Channel 接口

**Channel 名称**: `com.itforce.wan/vpn_service`

#### 8.1.1 后端初始化
```dart
// 方法名: initializeBackend
// 参数: 无
// 返回: Map<String, dynamic>
await platform.invokeMethod('initializeBackend');

// 响应格式:
{
  "success": true,
  "message": "Backend initialized successfully",
  "data": {
    "version": "1.0.0",
    "platform": "iOS" // 或 "macOS", "Android"
  }
}
```

#### 8.1.2 用户登录
```dart
// 方法名: login
// 参数: Map<String, dynamic>
await platform.invokeMethod('login', {
  'username': '<EMAIL>',
  'password': 'password123'
});

// 响应格式:
{
  "success": true,
  "message": "Login successful",
  "data": {
    "token": "auth_token_here",
    "user": {
      "username": "<EMAIL>",
      "displayName": "User Name"
    }
  }
}
```

#### 8.1.3 VPN 连接控制
```dart
// 连接 VPN
await platform.invokeMethod('connect', {
  'serverId': 'server_001'
});

// 断开 VPN
await platform.invokeMethod('disconnect');

// 获取连接状态
await platform.invokeMethod('getConnectionStatus');
```

#### 8.1.4 服务器管理
```dart
// 获取服务器列表
await platform.invokeMethod('getServerList');

// 测试服务器延迟
await platform.invokeMethod('pingServers', {
  'serverIds': ['server_001', 'server_002']
});
```

### 8.2 Event Channel 接口

**Channel 名称**: `com.itforce.wan/vpn_events`

#### 8.2.1 连接状态事件
```dart
// 监听连接状态变化
eventChannel.receiveBroadcastStream().listen((event) {
  switch (event['type']) {
    case 'connection_status_changed':
      // 处理连接状态变化
      break;
    case 'traffic_stats_updated':
      // 处理流量统计更新
      break;
    case 'server_list_updated':
      // 处理服务器列表更新
      break;
  }
});
```

#### 8.2.2 事件数据格式
```json
// 连接状态变化事件
{
  "type": "connection_status_changed",
  "timestamp": 1625097600000,
  "data": {
    "status": "connected", // disconnected, connecting, connected, disconnecting
    "server": {
      "id": "server_001",
      "name": "Server 1",
      "location": "Hong Kong"
    },
    "duration": 3600, // 连接时长（秒）
    "ip": "********" // 分配的 VPN IP
  }
}

// 流量统计更新事件
{
  "type": "traffic_stats_updated",
  "timestamp": 1625097600000,
  "data": {
    "bytesReceived": 1048576,
    "bytesSent": 524288,
    "packetsReceived": 1024,
    "packetsSent": 512
  }
}
```

### 8.3 错误处理

Platform Channel 错误使用 Flutter 的 PlatformException：

```dart
try {
  await platform.invokeMethod('connect', {'serverId': 'invalid'});
} on PlatformException catch (e) {
  // e.code: 错误代码
  // e.message: 错误消息
  // e.details: 详细错误信息
}
```

**常见错误代码**：
- `INVALID_ARGUMENTS`: 参数无效
- `VPN_PERMISSION_DENIED`: VPN 权限被拒绝
- `CONNECTION_FAILED`: 连接失败
- `SERVER_NOT_FOUND`: 服务器不存在
- `AUTHENTICATION_FAILED`: 认证失败

### 8.4 平台差异

#### iOS 特有功能
- NetworkExtension 权限申请
- App Store 审核相关限制

#### macOS 特有功能
- 系统扩展权限管理
- 网络配置权限

#### Android 特有功能（开发中）
- VpnService 权限申请
- 后台运行权限
- 电池优化白名单
