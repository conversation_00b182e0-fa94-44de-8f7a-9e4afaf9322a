# ITforce WAN 当前项目架构深度分析

## 📋 文档概述

**创建日期**: 2025-01-18
**分析目的**: 为项目重构提供详细的现状分析基础
**涵盖范围**: Go后端、Flutter前端、四平台特性、构建系统、资源管理

## 🏗️ 整体架构概览

### 1.1 项目根目录结构

```
mobile/
├── cmd/                    # Go应用程序入口
│   └── vpn-service/        # VPN服务主程序
├── internal/               # Go内部实现包
│   ├── common/             # 通用基础组件
│   ├── interfaces/         # 核心接口定义
│   ├── platform/           # 平台抽象层
│   ├── protocol/           # 协议实现层
│   ├── connection/         # 连接管理层
│   └── service/            # 服务接口层
├── ui/                     # 用户界面
│   └── flutter/            # Flutter跨平台应用
├── configs/                # 配置文件模板
├── docs/                   # 设计文档
├── scripts/                # 构建和部署脚本
├── build/                  # 编译产物
├── wintun/                 # Windows TUN驱动
├── go.mod                  # Go模块定义
├── go.sum                  # Go依赖锁定
├── Makefile                # 根级构建配置
└── VERSION                 # 版本管理文件
```

### 1.2 技术栈组合

- **后端**: Go 1.24.2 + 自定义SDWAN协议
- **前端**: Flutter 3.0+ + Dart
- **平台**: Windows、iOS、macOS、Android
- **构建**: Make + 平台特定脚本
- **依赖**: Go Modules + Flutter Pub

## 🔧 Go 后端架构分析

### 2.1 模块依赖关系

```
cmd/vpn-service (入口)
    ↓
internal/service (服务层)
    ├── http (HTTP API)
    ├── websocket (实时通信)
    └── config (配置管理)
    ↓
internal/connection (连接层)
    ├── manager (连接管理)
    └── server (服务器管理)
    ↓
internal/protocol (协议层)
    ├── tunnel (SDWAN协议)
    ├── encryption (加密服务)
    └── packet (数据包处理)
    ↓
internal/platform (平台层)
    └── tun (TUN设备管理)
    ↓
internal/common (基础层)
    ├── logger (日志系统)
    ├── errors (错误处理)
    ├── monitor (性能监控)
    └── pool (对象池)
```

### 2.2 核心组件分析

**启动流程** (`cmd/vpn-service/main.go`):
1. 配置加载和验证
2. 日志系统初始化
3. 服务器管理器初始化
4. TUN设备初始化
5. 连接管理器启动
6. HTTP/WebSocket服务启动

**配置管理** (`internal/service/config/`):
- 支持YAML格式配置
- 热重载机制
- 配置验证和类型安全
- 环境变量覆盖

**日志系统** (`internal/common/logger/`):
- 基于zap的结构化日志
- 多输出支持(文件+控制台)
- 动态日志级别调整
- 性能追踪和错误跟踪

### 2.3 关键配置项

**模块标识符**: `mobile`
**主要依赖**:
- `github.com/gorilla/mux` - HTTP路由
- `github.com/gorilla/websocket` - WebSocket通信
- `go.uber.org/zap` - 结构化日志
- `golang.zx2c4.com/wireguard/windows` - Windows TUN

## 📱 Flutter 前端架构分析

### 3.1 项目配置 (`ui/flutter/pubspec.yaml`)

**基本信息**:
- 包名: `itforce`
- 描述: "ItForce WAN Client with Flutter UI and Go backend"
- 版本: `1.0.0+1`
- Flutter SDK: `>=3.0.0 <4.0.0`

**核心依赖**:
- `provider` - 状态管理
- `get_it` - 依赖注入
- `http` + `web_socket_channel` - 网络通信
- `shared_preferences` - 本地存储
- `window_manager` - 窗口管理
- `device_info_plus` - 设备信息

### 3.2 资源管理分析

**图标资源**:
- `assets/icons/app_icon.png` - 主应用图标
- `assets/icons/app_icon.webp` - WebP格式图标
- `assets/icons/app_icon.ico` - Windows ICO图标

**SVG图像**:
- `assets/images/ITFORCE_Logo.svg` - 主品牌Logo
- `assets/images/connected.svg` - 连接状态图标
- `assets/images/disconnected.svg` - 断开状态图标

**字体资源**:
- `Roboto` 系列 - 英文字体
- `SourceHanSansCN` 系列 - 中文字体

### 3.3 平台特定配置

**Windows配置** (`msix_config`):
- 显示名称: "ItForce"
- 发布者: "ItForce Corporation"
- 标识符: "ItForce.ItForce"
- 权限: `internetClient`, `runFullTrust`

## 🎯 四平台特性分析

### 4.1 iOS 平台 (`ui/flutter/ios/`)

**Bundle配置**:
- Bundle ID: `com.itforce.itForceClient`
- 显示名称: "ITforce WAN"
- VPN Extension ID: `com.itforce.itForceClient.Extension`

**关键文件**:
- `Runner.xcodeproj/` - Xcode项目文件
- `ItForceVPNExtension/` - NetworkExtension实现
- `Info.plist` - 应用配置
- `Podfile` - CocoaPods依赖

**权限需求**:
- NetworkExtension权限
- VPN配置权限
- 应用组共享权限

### 4.2 Android 平台 (`ui/flutter/android/`)

**包配置**:
- 包名: `com.itforce.wan`
- 应用名称: "ITforce WAN"
- 最低SDK: 24 (Android 7.0+)
- 目标SDK: Flutter默认

**关键文件**:
- `app/build.gradle.kts` - 构建配置
- `AndroidManifest.xml` - 应用清单
- Kotlin源码实现

**权限需求**:
- `BIND_VPN_SERVICE` - VPN服务绑定
- `FOREGROUND_SERVICE` - 前台服务
- `INTERNET` + `ACCESS_NETWORK_STATE` - 网络访问

### 4.3 macOS 平台 (`ui/flutter/macos/`)

**Bundle配置**:
- Bundle ID: `com.itforce.itForceClient`
- 显示名称: "ITforce WAN"
- VPN Extension ID: `com.itforce.itForceClient.Extension`

**关键文件**:
- `Runner.xcodeproj/` - Xcode项目文件
- `ItForceVPNExtension/` - NetworkExtension实现
- `Configs/AppInfo.xcconfig` - 应用配置

### 4.4 Windows 平台 (`ui/flutter/windows/`)

**项目配置**:
- 项目名称: `itforce`
- 可执行文件: `itforce.exe`
- CMake最低版本: 3.14

**关键文件**:
- `CMakeLists.txt` - CMake构建配置
- `runner/` - C++运行时代码

## 🔨 构建系统分析

### 5.1 根级Makefile

**主要目标**:
- `all` - 构建所有组件
- `backend` - 构建Go后端
- `frontend` - 构建Flutter前端
- `installer` - 构建安装包

**版本管理**:
- 从`VERSION`文件读取版本号
- Git提交信息集成
- 时间戳生成

### 5.2 构建脚本分析

**后端构建** (`scripts/build_backend.sh`):
- 跨平台编译支持
- WinTun驱动集成
- 配置文件复制
- 版本信息嵌入

**前端构建** (`scripts/build_frontend.sh`):
- Flutter依赖获取
- 应用图标生成
- Windows应用构建
- 资源验证检查

## 🎨 品牌和资源管理现状

### 6.1 硬编码品牌信息

**应用名称**:
- iOS/macOS: "ITforce WAN"
- Android: "ITforce WAN"
- Windows: "ItForce"

**公司信息**:
- 发布者: "ItForce Corporation"
- 版权: "Copyright © 2025 com.itforce"

**标识符**:
- iOS/macOS: `com.itforce.itForceClient`
- Android: `com.itforce.wan`
- Windows: `ItForce.ItForce`

### 6.2 资源分布

**图标资源**: 集中在`ui/flutter/assets/icons/`
**品牌图像**: 集中在`ui/flutter/assets/images/`
**字体文件**: 集中在`ui/flutter/assets/fonts/`

### 6.3 配置分散问题

- 品牌信息分散在各平台配置文件中
- 无统一的品牌配置管理
- OEM定制需要手动修改多个文件

## 📊 重构需求总结

### 7.1 目录结构问题

1. **混合架构**: Go和Flutter代码混合在根目录
2. **深层嵌套**: 平台代码深埋在`ui/flutter/`子目录
3. **职责不清**: 缺乏清晰的平台分离

### 7.2 品牌管理问题

1. **硬编码分散**: 品牌信息分散在各配置文件
2. **无统一管理**: 缺乏品牌配置系统
3. **OEM困难**: 定制需要手动修改多处

### 7.3 构建系统问题

1. **路径硬编码**: 构建脚本中路径依赖固化
2. **缺乏模块化**: 无平台独立构建能力
3. **版本管理**: 版本信息分散管理

## 🎨 OEM定制需求详细分析

### 8.1 各平台需要替换的资源文件

**iOS平台** (`ui/flutter/ios/`):
- 应用图标: `Runner/Assets.xcassets/AppIcon.appiconset/`
- 启动图: `Runner/Assets.xcassets/LaunchImage.imageset/`
- Info.plist中的显示名称: `CFBundleDisplayName`
- Bundle标识符: `PRODUCT_BUNDLE_IDENTIFIER`
- VPN Extension图标和标识符

**Android平台** (`ui/flutter/android/`):
- 应用图标: `app/src/main/res/mipmap-*/ic_launcher.png`
- 启动图: `app/src/main/res/drawable/launch_background.xml`
- 应用名称: `AndroidManifest.xml`中的`android:label`
- 包名: `build.gradle.kts`中的`applicationId`

**macOS平台** (`ui/flutter/macos/`):
- 应用图标: `Runner/Assets.xcassets/AppIcon.appiconset/`
- Info.plist配置: `CFBundleDisplayName`, Bundle ID
- VPN Extension配置

**Windows平台** (`ui/flutter/windows/`):
- 应用图标: CMakeLists.txt中的图标配置
- MSIX配置: `pubspec.yaml`中的`msix_config`
- 可执行文件名: `BINARY_NAME`

**Flutter共享资源** (`ui/flutter/assets/`):
- 应用图标: `icons/app_icon.*`
- 品牌Logo: `images/ITFORCE_Logo.svg`
- 状态图标: `images/connected.svg`, `images/disconnected.svg`

### 8.2 需要修改的代码内容

**应用标识符**:
- iOS: `com.itforce.itForceClient` → `com.{brand}.{product}`
- Android: `com.itforce.wan` → `com.{brand}.{product}`
- Windows: `ItForce.ItForce` → `{Brand}.{Product}`

**API端点配置**:
- 服务器列表URL
- 认证API端点
- 更新检查URL

**功能开关**:
- 分析统计开关
- 崩溃报告开关
- 自动更新开关
- Beta功能开关

**品牌文本**:
- 应用名称和描述
- 公司名称和版权信息
- 用户协议和隐私政策链接
- 客服联系方式

### 8.3 OEM定制技术实现方案

**品牌配置文件格式** (`brand-config.yaml`):
```yaml
metadata:
  version: "1.0"
  brand_id: "custom-brand"

brand:
  name: "Custom VPN"
  display_name: "Custom VPN Client"
  company: "Custom Corporation"

identifiers:
  ios_bundle_id: "com.custom.vpn"
  android_package: "com.custom.vpn"
  windows_identity: "Custom.VPN"

assets:
  app_icon: "icons/custom_icon.png"
  logo: "images/custom_logo.svg"

colors:
  primary: "#1976D2"
  accent: "#FF5722"

api:
  base_url: "https://api.custom.com"
  server_list_url: "https://api.custom.com/servers"
```

**自动化构建脚本** (`scripts/oem/build-oem.sh`):
1. 验证品牌配置文件
2. 生成平台特定配置文件
3. 复制和替换品牌资源
4. 更新代码中的硬编码值
5. 执行平台特定构建
6. 生成OEM安装包

**资源管理机制**:
- 品牌资源模板系统
- 自动图标尺寸生成
- 配置文件模板替换
- 代码注入和替换

## 🎯 重构优先级建议

**P0 (高优先级)**:
1. 目录结构重组 - 创建`platforms/`分层架构
2. 构建系统重构 - 实现模块化构建

**P1 (中优先级)**:
3. 平台代码分离 - 独立平台目录
4. 品牌配置系统 - 统一品牌管理

**P2 (低优先级)**:
5. CI/CD优化 - 平台独立流水线
6. 文档更新 - 反映新架构
