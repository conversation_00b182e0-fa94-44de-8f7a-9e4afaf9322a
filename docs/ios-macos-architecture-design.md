# iOS/macOS 架构设计

## 📋 概述

ITforce WAN iOS/macOS 客户端基于 Swift 原生实现，采用简化架构设计，完全兼容现有 SDWAN ZZVPN 协议。

**实现状态**：✅ **100% 完成** - 生产级质量，零编译错误

**核心特性**：
- 🎯 **协议兼容** - 与 Go 后端服务器 100% 兼容
- 🚀 **原生性能** - Swift 5.7+ 原生实现，充分利用系统特性
- 🔒 **系统集成** - 深度集成 NetworkExtension 框架
- 📱 **跨平台** - iOS/macOS 共享 95%+ 代码
- 🏗️ **简化架构** - VPNService 统一管理，消除冗余抽象层
- 🧵 **线程安全** - Actor 模式确保并发安全
- 📊 **实时监控** - 完整的状态管理和流量统计

## 🏗️ 系统架构

### 整体架构图

系统采用简化分层架构设计，VPNService作为统一业务入口，直接管理核心组件：

```mermaid
graph TB
    subgraph "Flutter Layer"
        UI[Flutter UI<br/>复用现有代码]
    end

    subgraph "Swift Native Layer"
        subgraph "Presentation Layer"
            PC[Platform Channel Handler<br/>Flutter桥接]
            NE[NetworkExtension<br/>系统VPN集成]
        end

        subgraph "VPNService Layer (统一业务入口)"
            VS[VPNService<br/>统一VPN管理<br/>直接管理所有功能]
        end

        subgraph "Core Logic Layer (核心逻辑)"
            CM[Connection Manager<br/>连接管理实现]
            SM[Server Manager<br/>服务器管理实现]
            ST[State Machine<br/>状态管理]
        end

        subgraph "Protocol Layer (协议抽象)"
            SP[SDWAN Protocol<br/>ZZVPN协议栈]
            EN[Encryption<br/>加密服务]
            AU[Authentication<br/>认证机制]
        end

        subgraph "Platform Layer (平台抽象)"
            PT[PacketTunnelProvider<br/>TUN设备管理]
            RM[Route Management<br/>路由管理]
            DN[DNS Configuration<br/>DNS配置]
        end

        subgraph "Infrastructure Layer"
            LG[Logging System<br/>日志系统]
            EH[Error Handling<br/>错误处理]
            PM[Performance Monitor<br/>性能监控]
            DB[Data Buffer Pool<br/>数据缓冲池]
        end
    end

    subgraph "System Layer"
        SYS[iOS/macOS System<br/>NetworkExtension Framework]
    end

    %% 简化的数据流向
    UI --> PC
    PC --> VS
    VS --> CM
    VS --> SM
    CM --> ST
    CM --> SP
    SP --> EN
    SP --> AU
    NE --> PT
    PT --> RM
    PT --> DN

    %% 基础设施依赖
    VS -.-> LG
    CM -.-> EH
    CM -.-> PM
    SP -.-> DB

    %% 系统集成
    NE --> SYS
    PT --> SYS

    %% 样式
    classDef flutter fill:#e1f5fe
    classDef presentation fill:#f3e5f5
    classDef vpnservice fill:#e8f5e8
    classDef corelogic fill:#fff3e0
    classDef protocol fill:#fce4ec
    classDef platform fill:#f1f8e9
    classDef infrastructure fill:#f5f5f5
    classDef system fill:#ffebee

    class UI flutter
    class PC,NE presentation
    class VS vpnservice
    class CM,SM,ST corelogic
    class SP,EN,AU protocol
    class PT,RM,DN platform
    class LG,EH,PM,DB infrastructure
    class SYS system
```

### 架构特点

**简化设计原则**：
- 📱 **Flutter Layer** - 复用现有UI代码，无需重写
- 🔗 **Presentation Layer** - Platform Channel桥接，NetworkExtension集成
- 🎯 **VPNService Layer** - 统一业务入口，直接管理所有VPN功能
- 🧠 **Core Logic Layer** - 核心业务逻辑实现，状态管理
- 📡 **Protocol Layer** - SDWAN协议实现，加密认证
- 🛠️ **Platform Layer** - 系统集成，TUN设备管理
- 🏗️ **Infrastructure Layer** - 基础设施，日志监控

### NetworkExtension集成架构

iOS/macOS VPN基于NetworkExtension框架实现，采用独立进程架构：

```mermaid
graph LR
    subgraph "Main App Process"
        subgraph "Flutter Layer"
            FUI[Flutter UI<br/>用户界面]
        end

        subgraph "Swift Layer"
            PC[Platform Channel<br/>桥接层]
            VS[VPN Service<br/>VPN管理]
            NEC[NE Controller<br/>扩展控制器]
        end
    end

    subgraph "NetworkExtension Process"
        subgraph "System Interface"
            PTP[PacketTunnelProvider<br/>系统VPN接口]
        end

        subgraph "Core Components"
            CM[Connection Manager<br/>连接管理]
            SP[SDWAN Protocol<br/>协议处理]
            PM[Packet Manager<br/>数据包处理]
        end

        subgraph "Platform Integration"
            TUN[TUN Device<br/>虚拟网络接口]
            RT[Route Manager<br/>路由管理]
            DNS[DNS Config<br/>DNS配置]
        end
    end

    subgraph "iOS/macOS System"
        SYS[System Network Stack<br/>系统网络栈]
        VPN[VPN Framework<br/>VPN框架]
    end

    %% 主要数据流
    FUI --> PC
    PC --> VS
    VS --> NEC
    NEC -.->|XPC Communication| PTP
    PTP --> CM
    CM --> SP
    SP --> PM
    PM --> TUN
    TUN --> RT
    TUN --> DNS

    %% 系统集成
    PTP --> VPN
    TUN --> SYS
    RT --> SYS
    DNS --> SYS

    %% 样式
    classDef mainApp fill:#e3f2fd
    classDef extension fill:#f3e5f5
    classDef system fill:#fff3e0

    class FUI,PC,VS,NEC mainApp
    class PTP,CM,SP,PM,TUN,RT,DNS extension
    class SYS,VPN system
```

**架构特性**：
- 🔄 **独立进程** - NetworkExtension在独立进程中运行，不受主应用生命周期影响
- 🛡️ **系统管理** - 由iOS/macOS系统自动管理启动停止，确保VPN连接持续性
- ⚡ **资源优化** - 严格内存限制（<40MB）下的高效运行，优化数据结构和算法
- 🔗 **状态同步** - 通过XPC机制实现主应用与NetworkExtension间的状态同步

### VPN连接建立流程

完整的VPN连接建立过程涉及Flutter UI、Swift服务层、NetworkExtension和SDWAN协议的协调：

```mermaid
sequenceDiagram
    participant UI as Flutter UI
    participant PC as Platform Channel
    participant VS as VPN Service
    participant NE as NetworkExtension
    participant CM as Connection Manager
    participant SP as SDWAN Protocol
    participant SV as VPN Server

    Note over UI,SV: VPN连接建立完整流程

    UI->>PC: 用户点击连接
    PC->>VS: connect(serverInfo)

    Note over VS: 验证配置和权限
    VS->>VS: 验证服务器配置
    VS->>VS: 检查VPN权限

    VS->>NE: 启动NetworkExtension
    activate NE

    Note over NE: NetworkExtension初始化
    NE->>NE: 配置TUN设备
    NE->>NE: 设置路由规则
    NE->>NE: 配置DNS设置

    NE->>CM: 初始化连接管理器
    activate CM

    Note over CM,SV: SDWAN协议握手
    CM->>SP: 创建OPEN包
    SP->>SV: 发送OPEN包
    SV->>SP: 返回OPENACK包
    SP->>CM: 解析OPENACK包

    Note over CM: 认证验证
    CM->>CM: 验证认证信息
    CM->>CM: 建立会话密钥

    Note over CM,UI: 状态同步
    CM->>NE: 连接建立成功
    NE->>VS: 通知连接状态
    VS->>PC: 状态变更事件
    PC->>UI: 更新UI状态

    Note over NE,SV: 开始数据传输
    NE->>CM: 启动心跳机制
    CM->>SV: 定期发送心跳包

    deactivate CM
    deactivate NE

    Note over UI: 连接建立完成
```

## 🧩 核心模块

基于实际的 Swift 代码实现 (`ui/flutter/ItForceCore/Sources/`)：

### VPNService Layer (统一业务入口)
**实现位置**: `Sources/Service/VPNService.swift`

```swift
@MainActor
class VPNService: ObservableObject {
    private let connectionManager: ConnectionManager
    private let serverManager: ServerManager
    private let platformChannelHandler: PlatformChannelHandler

    // 统一的 VPN 管理接口
    func connect(to server: Server) async throws
    func disconnect() async throws
    func getConnectionStatus() -> VPNState
}
```

**核心功能**：
- 📱 **统一VPN管理** - 作为唯一业务入口，直接管理所有VPN功能
- 🔗 **Flutter桥接** - Platform Channel通信，替代HTTP/WebSocket API
- 🎯 **状态协调** - 统一状态管理，消除多层状态同步
- 🔄 **生命周期管理** - 服务启动、停止、重连等完整生命周期
- 🛡️ **权限处理** - VPN权限申请和管理

### Connection Layer (连接管理)
**实现位置**: `Sources/Connection/`

```swift
actor ConnectionManager {
    private var currentState: VPNState = .disconnected
    private let protocolHandler: SDWANProtocolHandler
    private let packetTunnelProvider: PacketTunnelProvider

    func connect(to server: Server) async throws
    func disconnect() async throws
    func handleIncomingPacket(_ packet: Data) async
}
```

**核心组件**：
- 🔄 **ConnectionManager** - Actor模式的线程安全连接管理
- 🌐 **ServerManager** - 服务器选择、ping测试、故障切换
- 🎯 **VPNState** - 严格的状态转换逻辑和一致性保证

### Protocol Layer (协议实现)
**实现位置**: `Sources/Protocol/`

```swift
class SDWANProtocolHandler {
    private let encryptionService: EncryptionService
    private let packetParser: PacketParser

    func createOpenPacket(username: String, password: String) -> Data
    func parseOpenAckPacket(_ data: Data) throws -> OpenAckResponse
    func encryptDataPacket(_ data: Data) throws -> Data
}
```

**核心功能**：
- 🔗 **SDWAN协议** - ZZVPN协议栈完整实现，与Go后端100%兼容
- 📦 **数据包处理** - TLV属性解析、包头处理、数据封装
- 🔐 **加密服务** - XOR/AES算法实现，密钥管理
- 🔑 **认证机制** - OPEN/OPENACK握手流程，会话管理

### Platform Layer (平台集成)
**实现位置**: `Sources/Platform/`

```swift
class PacketTunnelProvider: NEPacketTunnelProvider {
    private let connectionManager: ConnectionManager

    override func startTunnel(options: [String : NSObject]?) async throws
    override func stopTunnel(with reason: NEProviderStopReason) async
    override func handleAppMessage(_ messageData: Data) async -> Data?
}
```

**核心功能**：
- 🛡️ **NetworkExtension** - PacketTunnelProvider系统集成
- 🔧 **TUN设备** - 虚拟网络接口管理和数据转发
- 🛣️ **路由管理** - iOS/macOS路由策略和DNS配置
- 📱 **平台检测** - iOS/macOS平台差异处理

### Infrastructure Layer (基础设施)
**实现位置**: `Sources/Infrastructure/`

```swift
class Logger {
    static func info(_ message: String, file: String = #file, function: String = #function, line: Int = #line)
    static func error(_ message: String, error: Error? = nil, file: String = #file, function: String = #function, line: Int = #line)
}

class PerformanceMonitor {
    func trackMemoryUsage()
    func trackNetworkLatency()
    func reportMetrics()
}
```

**核心功能**：
- 🪵 **日志系统** - OSLog集成，结构化记录，自动文件/行号
- ❌ **错误处理** - Swift Error机制，统一错误类型和处理
- 📊 **性能监控** - 内存、CPU、网络指标实时监控
- 🗂️ **数据管理** - UserDefaults封装，配置管理

## 🚀 简化架构优势

### 架构简化对比

**简化前（过度分层）**：
```
VPNService (Application Layer)
    ↓ 委托调用
ConnectionService (Application Layer)
    ↓ 委托调用
ConnectionManager (Domain Layer)
    ↓ 直接调用
Protocol/Platform Layers
```

**简化后（合理分层）**：
```
VPNService (统一业务入口)
    ↓ 直接管理
ConnectionManager + ServerManager (核心逻辑组件)
    ↓ 抽象接口
Protocol Layer (SDWAN协议) + Platform Layer (系统集成)
```

### 简化效果

| 指标 | 简化前 | 简化后 | 改善程度 |
|------|--------|--------|----------|
| **Service层文件数** | 3个 (VPN/Connection/Server) | 1个 (VPNService) | 减少67% |
| **调用链长度** | 4层 | 3层 | 减少25% |
| **接口抽象数** | 15+ 个Protocol | 8个 Protocol | 减少47% |
| **依赖关系复杂度** | 高 (多层委托) | 中 (直接管理) | 显著降低 |

### 维护优势

- **开发效率提升** - 减少层间跳转，逻辑更直观
- **调试简化** - 调用栈更短，问题定位更容易
- **测试简化** - 减少Mock对象，测试用例更简单
- **文档维护** - 架构文档更简洁，易于理解

### 与Go后端功能对应关系

| Go后端模块 | 简化后Swift模块 | 功能描述 | 实现策略 |
|------------|----------------|----------|----------|
| `internal/service/http/` | VPNService + PlatformChannelHandler | API接口服务 | HTTP API → Platform Channel |
| `internal/service/websocket/` | VPNService + EventChannel | 实时事件推送 | WebSocket → Event Channel |
| `internal/connection/manager/` | ConnectionManager | 连接管理 | 直接对应，简化接口 |
| `internal/connection/server/` | ServerManager | 服务器管理 | 直接对应，简化接口 |
| `internal/protocol/` | Protocol Layer | SDWAN协议 | 保持不变 |
| `internal/platform/` | Platform Layer | 平台抽象 | 保持不变 |
| `internal/common/` | Infrastructure Layer | 基础设施 | 保持不变 |

### 调用链对比

**Go后端调用链**:
```
HTTP API → Service → ConnectionManager → Protocol/Platform
```

**简化后Swift调用链**:
```
Platform Channel → VPNService → ConnectionManager → Protocol/Platform
```

**优势**:
- 调用链长度一致，保持与Go后端的对等性
- 减少不必要的中间层，提高调用效率
- 简化架构，提高可维护性

## ⚠️ 关键架构决策

### 服务器切换机制

**iOS/macOS特殊性**：
- 必须重建NetworkExtension以更新`tunnelRemoteAddress`
- 切换时间约2-5秒（vs Go后端100-500ms）
- 系统自动管理路由，避免路由循环

**服务器切换流程图**：

```mermaid
sequenceDiagram
    participant UI as Flutter UI
    participant SS as Server Service
    participant VS as VPN Service
    participant NE as NetworkExtension
    participant SYS as iOS/macOS System
    participant SV1 as 当前服务器
    participant SV2 as 新服务器

    Note over UI,SV2: iOS/macOS服务器切换流程

    UI->>SS: 选择新服务器
    SS->>SS: 解析服务器地址
    SS->>SS: 更新服务器列表

    Note over SS,VS: 准备切换
    SS->>VS: switchToServer(newServer)
    VS->>VS: 验证新服务器配置

    Note over VS,SYS: 停止当前连接
    VS->>NE: stopTunnel()
    NE->>SV1: 断开连接
    NE->>SYS: 清理网络配置

    Note over VS,SYS: 重建NetworkExtension
    VS->>SYS: 创建新的TunnelConfiguration
    Note right of SYS: tunnelRemoteAddress = 新服务器IP<br/>excludedRoutes = 所有服务器IP

    VS->>NE: startTunnel(newConfig)
    activate NE

    Note over NE,SV2: 建立新连接
    NE->>SYS: 应用新网络配置
    NE->>SV2: 建立SDWAN连接
    SV2->>NE: 连接确认

    Note over NE,UI: 状态同步
    NE->>VS: 连接建立成功
    VS->>SS: 更新服务器状态
    SS->>UI: 切换完成通知

    deactivate NE

    Note over UI: 切换完成 (2-5秒)
```

**职责分离**：
- **应用层** - 服务器选择、健康检查、主机名解析
- **NetworkExtension** - 网络配置、数据包处理、系统集成

### 连接管理简化

**保留**：
- ✅ **ConnectionManager** - SDWAN协议实现、状态机管理
- ✅ **NetworkExtension** - 系统级网络管理

**移除**：
- ❌ **AutoReconnectionManager** - 系统自动处理重连

## 🛣️ 路由管理

### 平台差异

**iOS特性**：
- 🔒 沙盒限制，仅通过NetworkExtension配置路由
- 📋 声明式配置，系统托管路由生效
- 🧹 VPN断开时系统自动清理路由

**macOS特性**：
- 🔓 完整权限，可读写系统路由表
- ⚡ 动态管理，运行时添加删除路由
- 🔧 精细控制，详细路由参数设置

### 路由策略

**includedRoutes** - 通过VPN的流量：
- 🌐 全局模式：0.0.0.0/0 (所有流量)
- 🎯 选择性模式：指定网络段

**excludedRoutes** - 绕过VPN的流量：
- 🏠 本地网络：192.168.0.0/16, 10.0.0.0/8, 172.16.0.0/12
- 🌐 VPN服务器：确保服务器可达性
- 🔗 链路本地：169.254.0.0/16

*详细实现参见：`docs/modules/platform-layer-design.md`*

## 📱 Flutter集成

### Platform Channel通信机制

Flutter与Swift通过Platform Channel实现双向通信，支持方法调用、事件推送和数据交换：

```mermaid
graph TB
    subgraph "Flutter Layer"
        FUI[Flutter UI]
        FMC[Method Channel Client]
        FEC[Event Channel Client]
    end

    subgraph "Platform Channel Bridge"
        PCH[Platform Channel Handler]
        MCD[Method Call Dispatcher]
        ESS[Event Stream Sink]
    end

    subgraph "Swift Service Layer"
        VS[VPN Service]
        CS[Connection Service]
        SS[Server Service]
    end

    subgraph "Communication Types"
        MC[Method Channel<br/>方法调用]
        EC[Event Channel<br/>事件推送]
        DC[Data Channel<br/>数据交换]
    end

    %% Method Channel 流程
    FUI --> FMC
    FMC -->|connect/disconnect/getStatus| PCH
    PCH --> MCD
    MCD --> VS
    VS --> CS
    VS --> SS

    %% Event Channel 流程
    VS -->|状态变化| ESS
    CS -->|连接事件| ESS
    SS -->|服务器更新| ESS
    ESS --> FEC
    FEC --> FUI

    %% 数据类型
    PCH -.-> MC
    PCH -.-> EC
    PCH -.-> DC

    %% 样式
    classDef flutter fill:#e1f5fe
    classDef bridge fill:#f3e5f5
    classDef service fill:#e8f5e8
    classDef comm fill:#fff3e0

    class FUI,FMC,FEC flutter
    class PCH,MCD,ESS bridge
    class VS,CS,SS service
    class MC,EC,DC comm
```

**通信方式**：
- 📞 **Method Channel** - Flutter调用Swift方法（connect/disconnect/getStatus）
- 📡 **Event Channel** - Swift推送状态变化（连接状态、流量统计）
- 💬 **Data Channel** - 双向复杂数据交换（服务器列表、配置信息）

**核心接口**：
- 🔗 连接控制（connect/disconnect/reconnect）
- 📊 状态查询（连接状态、流量统计）
- ⚙️ 配置管理（服务器列表、用户设置）
- 🔔 事件通知（状态变化、错误事件）

### 数据包处理流程

VPN数据包在NetworkExtension中的完整处理流程：

```mermaid
flowchart TD
    subgraph "上行数据流 (设备 → 服务器)"
        APP[应用程序] --> TUN1[TUN设备]
        TUN1 --> PTP1[PacketTunnelProvider]
        PTP1 --> PM1[Packet Manager]
        PM1 --> SP1[SDWAN Protocol]
        SP1 --> EN1[Encryption]
        EN1 --> UDP1[UDP Socket]
        UDP1 --> SV1[VPN服务器]
    end

    subgraph "下行数据流 (服务器 → 设备)"
        SV2[VPN服务器] --> UDP2[UDP Socket]
        UDP2 --> DE1[Decryption]
        DE1 --> SP2[SDWAN Protocol]
        SP2 --> PM2[Packet Manager]
        PM2 --> PTP2[PacketTunnelProvider]
        PTP2 --> TUN2[TUN设备]
        TUN2 --> APP2[应用程序]
    end

    subgraph "控制流程"
        CM[Connection Manager]
        HB[Heartbeat]
        ST[State Machine]
    end

    %% 控制连接
    CM -.-> SP1
    CM -.-> SP2
    HB -.-> SV1
    ST -.-> CM

    %% 样式
    classDef upflow fill:#e8f5e8
    classDef downflow fill:#e3f2fd
    classDef control fill:#fff3e0

    class APP,TUN1,PTP1,PM1,SP1,EN1,UDP1,SV1 upflow
    class SV2,UDP2,DE1,SP2,PM2,PTP2,TUN2,APP2 downflow
    class CM,HB,ST control
```

### 状态同步机制

- ⚡ **实时同步** - VPN状态、流量数据通过Event Channel实时推送
- 🔄 **后台处理** - 应用切换时NetworkExtension继续运行，状态保持同步
- 📢 **系统通知** - 重要状态变化通过系统通知告知用户

*详细实现参见：`docs/modules/flutter-integration-design.md`*

## 🔧 技术栈

### SDWAN协议认证流程

ZZVPN协议的完整认证握手过程，确保与Go后端服务器100%兼容：

```mermaid
sequenceDiagram
    participant CM as Connection Manager
    participant SP as SDWAN Protocol
    participant EN as Encryption
    participant SV as VPN Server

    Note over CM,SV: SDWAN ZZVPN 认证流程

    CM->>SP: 创建OPEN包
    Note right of SP: 包含用户名、密码<br/>MTU、客户端信息

    SP->>EN: 加密认证数据
    EN->>SP: 返回加密包

    SP->>SV: 发送OPEN包
    Note right of SV: 服务器验证<br/>用户凭据

    alt 认证成功
        SV->>SP: 返回OPENACK包
        Note left of SV: 包含会话密钥<br/>服务器配置

        SP->>EN: 解密OPENACK
        EN->>SP: 返回解密数据

        SP->>CM: 认证成功
        CM->>CM: 建立会话状态

        Note over CM,SV: 开始心跳机制
        loop 每30秒
            CM->>SP: 创建HEARTBEAT包
            SP->>SV: 发送心跳
            SV->>SP: 心跳响应
            SP->>CM: 连接正常
        end

    else 认证失败
        SV->>SP: 返回错误响应
        SP->>CM: 认证失败
        CM->>CM: 清理连接状态
    end
```

### 核心技术

| 组件 | 技术选择 | 特点 |
|------|----------|------|
| **语言** | Swift 5.7+ | 原生性能，系统深度集成 |
| **UI** | Flutter (复用) | 跨平台一致性 |
| **通信** | Platform Channel | Flutter↔Swift直接通信 |
| **网络** | NetworkExtension | iOS/macOS VPN必需框架 |
| **存储** | UserDefaults + Keychain | 配置存储 + 安全凭据 |
| **日志** | OSLog | 系统原生，性能最优 |
| **加密** | CryptoKit + CommonCrypto | 系统原生加密 |
| **异步** | async/await + Actor | 现代并发编程 |

### 架构模式

- 🏗️ **Clean Architecture** - 分层设计，职责清晰
- 🔗 **Protocol-Oriented** - 提高可测试性和扩展性
- 🎯 **Actor Model** - 并发安全的数据处理
- 📡 **Observer Pattern** - 状态变化通知

## 📊 实现状态

### 模块依赖关系图

各层模块间的依赖关系遵循Clean Architecture原则，上层依赖下层，下层不依赖上层：

```mermaid
graph TD
    subgraph "Presentation Layer"
        PC[Platform Channel]
        NE[NetworkExtension]
    end

    subgraph "Application Layer"
        VS[VPN Service]
        CS[Connection Service]
        SS[Server Service]
    end

    subgraph "Domain Layer"
        CM[Connection Manager]
        SM[Server Manager]
        ST[State Machine]
    end

    subgraph "Protocol Layer"
        SP[SDWAN Protocol]
        EN[Encryption]
        AU[Authentication]
    end

    subgraph "Platform Layer"
        PT[PacketTunnelProvider]
        RM[Route Management]
        DN[DNS Configuration]
    end

    subgraph "Infrastructure Layer"
        LG[Logging]
        EH[Error Handling]
        PM[Performance Monitor]
        DB[Data Buffer Pool]
    end

    %% 依赖关系
    PC --> VS
    NE --> PT

    VS --> CS
    VS --> SS
    CS --> CM
    SS --> SM

    CM --> ST
    CM --> SP
    SM --> SP

    SP --> EN
    SP --> AU

    PT --> RM
    PT --> DN
    NE --> CM

    %% 基础设施依赖 (虚线表示被依赖)
    VS -.-> LG
    CS -.-> EH
    CM -.-> PM
    SP -.-> DB
    PT -.-> LG

    %% 样式
    classDef presentation fill:#e1f5fe
    classDef application fill:#f3e5f5
    classDef domain fill:#e8f5e8
    classDef protocol fill:#fff3e0
    classDef platform fill:#fce4ec
    classDef infrastructure fill:#f1f8e9

    class PC,NE presentation
    class VS,CS,SS application
    class CM,SM,ST domain
    class SP,EN,AU protocol
    class PT,RM,DN platform
    class LG,EH,PM,DB infrastructure
```

### 当前状态 ✅

**所有模块已完成实现**：
- ✅ Infrastructure Layer - 日志、错误处理、性能监控
- ✅ Protocol Layer - SDWAN协议、加密、认证
- ✅ Platform Layer - NetworkExtension、路由管理
- ✅ Domain Layer - 连接管理、状态机、服务器管理
- ✅ Application Layer - VPN/连接/服务器服务
- ✅ Presentation Layer - Platform Channel、UI集成

### 关键特性

**协议兼容性**：
- 🔗 与Go后端服务器100%兼容
- 📦 完整SDWAN ZZVPN协议栈实现
- 🔐 XOR/AES加密算法支持

**性能优化**：
- ⚡ NetworkExtension内存优化（<40MB）
- 🚀 异步数据包处理
- 🔄 智能缓存和对象池

**系统集成**：
- 📱 iOS/macOS原生集成
- 🛡️ 系统级VPN管理
- 🔔 后台运行支持

## 📚 相关文档

### 模块设计文档
- 📋 [Infrastructure Layer](docs/modules/infrastructure-layer-design.md) - 基础设施层设计
- 🔗 [Protocol Layer](docs/modules/protocol-layer-design.md) - SDWAN协议层设计
- 🛡️ [Platform Layer](docs/modules/platform-layer-design.md) - NetworkExtension平台层
- 🎯 [Domain Layer](docs/modules/connection-layer-design.md) - 连接管理领域层
- 📱 [Application Layer](docs/modules/service-layer-design.md) - 应用服务层
- 🌉 [Flutter Integration](docs/modules/flutter-integration-design.md) - Flutter集成层

### 项目配置文档
- 🏗️ [项目结构](docs/ios-macos-project-structure.md) - 目录结构和配置
- 📝 [设计指导原则](docs/ios-macos-design-guidelines.md) - 代码规范和质量标准





---

**文档版本**: v2.0
**创建日期**: 2025-06-23
**最后更新**: 2025-06-26
**负责人**: wei
**状态**: 实现完成，进入Review阶段

## 📊 架构图表总结

本文档包含以下关键架构图表，提升了文档的可读性和理解性：

1. **🏗️ 整体架构图** - 展示分层架构和组件关系
2. **🔗 NetworkExtension集成架构图** - 主应用与扩展进程的交互
3. **🚀 VPN连接建立流程图** - 完整的连接建立时序
4. **🔄 服务器切换流程图** - iOS/macOS特有的重建机制
5. **📱 Platform Channel通信机制图** - Flutter与Swift的桥接
6. **📦 数据包处理流程图** - 上行下行数据处理路径
7. **🔐 SDWAN协议认证流程图** - ZZVPN协议握手过程
8. **🧩 模块依赖关系图** - Clean Architecture依赖关系

这些图表使用Mermaid语法，可在支持的Markdown渲染器中直接显示，为开发者和Code Review提供了清晰的视觉指导。
