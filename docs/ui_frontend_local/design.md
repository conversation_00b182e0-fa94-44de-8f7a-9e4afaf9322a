# UI前端本地功能设计文档

本文档定义了UI前端需要在本地处理的功能，不需要与后端交互。设计参考了Android实现，并保持了一致的功能逻辑。

## 1. 用户信息管理

### 1.1 数据模型

用户信息模型包含以下字段：

```json
{
  "name": "用户姓名",
  "title": "职位",
  "department": "部门",
  "is_accept_license": true
}
```

### 1.2 存储方式

用户信息应使用本地存储机制保存，例如：
- Flutter中可使用`shared_preferences`或`sqflite`
- 参考Android实现中的Room数据库

### 1.3 功能需求

#### 1.3.1 获取用户信息

UI应提供方法从本地存储中获取用户信息：

```dart
UserInfo getUserInfo();
```

#### 1.3.2 更新用户信息

UI应提供方法更新本地存储中的用户信息：

```dart
Future<void> updateUserInfo(UserInfo userInfo);
```

#### 1.3.3 用户信息显示

用户信息应在以下位置显示：
- 设置界面中显示和编辑用户信息（姓名、部门、职位）
- 侧边栏或导航抽屉中显示用户信息
- 其他适当的UI元素中

## 2. 用户协议和隐私政策

### 2.1 协议URL

用户协议和隐私政策使用固定URL，无需从后端获取：

- 服务条款（中文）：`https://www.panabit.com/term_service_zh`
- 服务条款（英文）：`https://www.panabit.com/term_service_en`
- 隐私政策（中文）：`https://www.panabit.com/privacy_policy_zh`
- 隐私政策（英文）：`https://www.panabit.com/privacy_policy_en`

### 2.2 协议状态管理

#### 2.2.1 数据模型

协议接受状态应存储在用户信息模型中的`is_accept_license`字段。

#### 2.2.2 存储方式

协议接受状态应与用户信息一起存储在本地。

#### 2.2.3 功能需求

##### 接受协议

UI应提供方法接受用户协议：

```dart
Future<void> acceptLicense();
```

##### 撤销协议

UI应提供方法撤销用户协议：

```dart
Future<void> revokeLicense();
```

##### 检查协议状态

UI应提供方法检查用户是否已接受协议：

```dart
bool isLicenseAccepted();
```

### 2.3 协议显示

#### 2.3.1 首次启动

当用户首次启动应用或尚未接受协议时，应显示用户协议和隐私政策对话框，用户必须接受才能继续使用应用。

#### 2.3.2 设置界面

在设置界面中应提供查看用户协议和隐私政策的选项，以及撤销协议的功能。

#### 2.3.3 WebView实现

协议内容应使用WebView加载，参考Android实现中的`LicenseActivity`。

## 3. 日志管理

### 3.1 数据模型

日志模型包含以下字段：

```json
{
  "timestamp": **********,
  "level": "info",
  "message": "日志消息",
  "module": "ui"
}
```

### 3.2 存储方式

日志可以存储在内存中或本地文件中，建议实现循环日志机制，避免日志过大。

### 3.3 功能需求

#### 3.3.1 记录日志

UI应提供方法记录不同级别的日志：

```dart
void logDebug(String module, String message);
void logInfo(String module, String message);
void logWarning(String module, String message);
void logError(String module, String message, [dynamic error]);
```

#### 3.3.2 获取日志

UI应提供方法获取日志：

```dart
List<LogEntry> getLogs({String level, int limit});
```

#### 3.3.3 清除日志

UI应提供方法清除日志：

```dart
void clearLogs();
```

### 3.4 日志显示

UI应提供日志查看界面，可以：
- 按级别筛选日志
- 按时间排序
- 搜索日志内容
- 清除日志
- 复制日志

## 4. 语言和本地化

### 4.1 支持的语言

应用应至少支持以下语言：
- 中文（简体）
- 英文

### 4.2 本地化实现

使用Flutter的本地化机制实现多语言支持：
- 使用`flutter_localizations`包
- 创建语言资源文件
- 实现语言切换功能

### 4.3 语言选择

用户应能在设置界面中选择应用语言，选择后立即生效。

### 4.4 自动语言检测

应用启动时应检测系统语言，并自动选择相应的语言。

## 5. 外观设计

### 5.1 设计风格

应用外观应遵循以下原则：
- 与Android实现保持一致的设计风格
- 遵循Material Design设计规范
- 保持简洁、清晰的界面布局

### 5.2 颜色方案

应用应使用与Android实现一致的颜色方案，包括：
- 主色调
- 强调色
- 背景色
- 文字颜色

### 5.3 图标和资源

应用应使用与Android实现一致的图标和资源，确保视觉体验的一致性。

## 6. 服务器列表管理

### 6.1 数据模型

服务器模型与HTTP API中定义的一致：

```json
{
  "id": "服务器ID",
  "name": "服务器名称（中文）",
  "name_en": "服务器名称（英文）",
  "server_name": "服务器域名或IP",
  "server_port": 8000,
  "ping": 50,
  "isauto": false,
  "status": "connected|disconnected|connecting",
  "isdefault": true|false
}
```

### 6.2 存储方式

服务器列表应从后端获取，但可以在本地缓存以提高性能。

### 6.3 功能需求

#### 6.3.1 缓存服务器列表

UI应提供方法缓存服务器列表：

```dart
Future<void> cacheServerList(List<Server> servers);
```

#### 6.3.2 获取缓存的服务器列表

UI应提供方法获取缓存的服务器列表：

```dart
List<Server> getCachedServerList();
```

#### 6.3.3 清除缓存

UI应提供方法清除缓存的服务器列表：

```dart
void clearServerCache();
```

### 6.4 服务器列表显示

服务器列表应显示以下信息：
- 服务器名称（根据当前语言显示中文或英文）
- 延迟（ping值）
- 连接状态（已连接、未连接）
- 默认服务器标识

## 7. 连接状态管理

### 7.1 数据模型

连接状态模型与WebSocket事件中定义的一致：

```json
{
  "status": "connected|disconnected|connecting|disconnecting|error",
  "message": "状态消息",
  "server": {
    // 服务器信息
  },
  "connected_time": **********
}
```

### 7.2 状态管理

UI应使用状态管理机制（如Provider、Bloc等）管理连接状态，并在状态变化时更新UI。

### 7.3 功能需求

#### 7.3.1 更新连接状态

UI应提供方法更新连接状态：

```dart
void updateConnectionStatus(ConnectionStatus status);
```

#### 7.3.2 获取连接状态

UI应提供方法获取当前连接状态：

```dart
ConnectionStatus getConnectionStatus();
```

#### 7.3.3 计算连接时长

UI应提供方法计算并格式化连接时长：

```dart
String getFormattedConnectionTime();
```

### 7.4 连接状态显示

连接状态应在以下位置显示：
- 主界面上显示当前连接状态
- 显示已连接的服务器信息
- 显示连接时长
- 提供连接/断开按钮

## 8. 错误处理

### 8.1 错误类型

应用应处理以下类型的错误：
- 网络错误
- 认证错误
- 服务器错误
- 本地存储错误
- 未知错误

### 8.2 错误显示

错误应以适当的方式显示给用户：
- 轻微错误使用Toast或Snackbar
- 严重错误使用对话框
- 致命错误使用全屏错误页面

### 8.3 错误恢复

应用应提供错误恢复机制：
- 网络错误时自动重试
- 认证错误时引导用户重新登录
- 提供手动重试选项

## 9. 应用生命周期管理

### 9.1 启动流程

应用启动时应执行以下操作：
1. 检查用户协议接受状态
2. 如果未接受，显示用户协议对话框
3. 如果已接受，检查登录状态
4. 如果已登录，进入主界面
5. 如果未登录，进入登录界面

### 9.2 后台运行

当应用进入后台时：
- 保持VPN连接
- 停止不必要的UI更新
- 保存当前状态

### 9.3 恢复前台

当应用恢复前台时：
- 恢复UI更新
- 检查VPN连接状态
- 如果连接已断开，显示适当的提示

### 9.4 退出处理

当用户退出应用时：
- 提示用户是否断开VPN连接
- 保存所有未保存的设置
- 清理临时资源
