# IP分片处理设计文档

## 1. 概述

本文档描述了VPN客户端中IP分片处理功能的设计和实现。IP分片处理是VPN隧道中的重要功能，用于处理大于MTU的IP数据包，确保数据能够完整传输。

## 2. 背景

在VPN通信中，当IP数据包大小超过网络MTU时，数据包会被分片传输。SDWAN协议使用特定的分片包类型（IPFRAG/0x22和IPFRAG6/0x24）来传输这些分片。客户端需要接收这些分片，并将它们重新组装成完整的IP数据包，然后再写入TUN设备。

## 3. 设计目标

- 实现与原始C/Java代码功能一致的IP分片处理
- 支持IPv4和IPv6分片
- 高效管理分片队列，避免内存泄漏
- 实现超时机制，清理过期分片
- 提供详细的日志记录，便于调试

## 4. 数据结构

### 4.1 分片结构

```go
// IPFragment 表示一个IP分片
type IPFragment struct {
    ID         uint32    // 分片标识符
    EOP        uint8     // 结束标志(End of Packet)
    Offset     uint16    // 分片偏移
    Length     uint16    // 分片长度
    Data       []byte    // 分片数据
    InsertTime time.Time // 插入时间
}
```

### 4.2 分片队列

```go
// FragmentQueue 分片队列
type FragmentQueue struct {
    Fragments [FragQueueCapacity][]*IPFragment // 分片列表数组
    mu        sync.Mutex                       // 互斥锁
    logger    logger.Logger                    // 日志记录器
}
```

### 4.3 常量定义

```go
// 分片处理常量
const (
    FragQueueCapacity = 8    // 分片队列容量
    FragTimeout       = 100  // 分片超时时间(毫秒)
    MaxFragDataSize   = 2048 // 最大分片数据大小
)
```

## 5. 功能实现

### 5.1 分片解析

分片解析函数从SDWAN数据包中提取分片信息：

```go
// ParseFragment 解析分片数据
func ParseFragment(data []byte) (*IPFragment, error) {
    // 解析分片头
    id := binary.BigEndian.Uint32(data[0:4])
    fragInfo := binary.BigEndian.Uint32(data[4:8])

    // 提取分片信息
    eop := uint8((fragInfo >> 31) & 0x01)
    offset := uint16((fragInfo >> 18) & 0x1FFF)
    length := uint16((fragInfo >> 7) & 0x7FF)
    
    // 创建分片对象并复制数据
    ...
}
```

### 5.2 分片添加与重组

```go
// AddFragment 添加分片并尝试重组
func (q *FragmentQueue) AddFragment(fragment *IPFragment) ([]byte, bool) {
    // 查找匹配的分片组
    // 添加新分片
    // 尝试重组
    // 处理超时
    ...
}

// reassembleFragments 重组分片
func (q *FragmentQueue) reassembleFragments(index int) ([]byte, bool) {
    // 计算总长度
    // 创建缓冲区
    // 按顺序复制数据
    // 清空分片组
    ...
}
```

### 5.3 分片处理流程

1. 接收IPFRAG/IPFRAG6数据包
2. 解析分片头部信息
3. 将分片添加到对应的分片组
4. 检查分片组是否完整
5. 如果完整，重组分片并写入TUN设备
6. 如果不完整，等待更多分片
7. 定期清理超时的分片组

## 6. 处理函数

### 6.1 分片处理

```go
// handleFragmentPacket 处理分片数据包
func (m *Manager) handleFragmentPacket(packet *tunnel.Packet) {
    // 解析分片
    // 添加分片并尝试重组
    // 如果重组完成，写入TUN设备
    ...
}
```

## 7. 错误处理

- 检查分片数据长度是否足够
- 验证客户端状态是否为数据传输状态
- 处理分片解析错误
- 记录详细的日志信息

## 8. 性能考虑

- 使用固定大小的分片队列，避免无限增长
- 实现超时机制，自动清理过期分片
- 使用互斥锁保护分片队列，确保线程安全
- 预分配分片数据缓冲区，减少内存分配

## 9. 与原始实现的对比

本实现与原始Java实现保持了一致性，包括：
- 分片队列容量（8个分片组）
- 分片超时时间（100毫秒）
- 分片解析逻辑（ID、EOP、偏移量、长度）
- 分片重组逻辑（按顺序组合分片数据）

## 10. 测试策略

### 10.1 单元测试

- 测试分片解析函数
- 测试分片添加与重组函数
- 测试超时处理逻辑

### 10.2 集成测试

- 测试完整的分片处理流程
- 测试与TUN设备的交互
- 测试多个分片的并发处理

### 10.3 性能测试

- 测试大量分片的处理性能
- 测试内存使用情况
- 测试高并发场景下的稳定性

## 11. 未来改进

- 实现更高效的分片存储结构
- 优化分片重组算法
- 添加更多的统计信息
- 实现自适应超时机制

## 12. 结论

IP分片处理功能是VPN客户端的重要组成部分，本设计文档详细描述了VPN客户端中IP分片处理功能的实现。通过合理的数据结构设计和处理流程，确保了分片处理的正确性和效率。
