# ITforce WAN 架构设计文档

## 1. 系统概述

ITforce WAN 是一个基于 SDWAN ZZVPN 协议的企业级 WAN 客户端，采用现代化的分层架构设计。系统支持多平台部署，包括 Windows、macOS、iOS 和 Android，通过统一的 Flutter 前端和平台特定的后端实现提供一致的用户体验。

**多平台架构特点**：
- **Windows/Linux**: Go 后端 + HTTP API/WebSocket + Flutter 前端
- **iOS/macOS**: Swift 原生后端 + Platform Channel + Flutter 前端
- **Android**: Kotlin 原生后端 + Platform Channel + Flutter 前端（开发中）

### 1.1 设计目标

- **高可用性**：稳定的连接管理和自动故障恢复
- **高性能**：优化的数据传输和资源利用
- **可扩展性**：模块化设计，支持功能扩展
- **跨平台性**：统一的架构，支持多平台部署
- **安全性**：企业级安全设计和数据保护

### 1.2 核心特性

- 完整的 SDWAN ZZVPN 协议实现
- 智能服务器选择和负载均衡
- 实时状态监控和事件通知
- 跨平台 TUN 设备管理
- 企业级安全和凭据管理

## 2. 系统架构

### 2.1 整体架构

系统采用分层架构模式，从底层到顶层分为五个主要层次：

```
┌─────────────────────────────────────────┐
│              界面层 (UI Layer)            │
│  Flutter 跨平台应用 + 本地功能集成        │
├─────────────────────────────────────────┤
│             服务层 (Service Layer)       │
│  HTTP API + WebSocket + 配置管理         │
├─────────────────────────────────────────┤
│            连接层 (Connection Layer)     │
│  连接管理 + 服务器选择 + 状态机          │
├─────────────────────────────────────────┤
│            协议层 (Protocol Layer)       │
│  SDWAN ZZVPN + 加密服务 + 数据包处理     │
├─────────────────────────────────────────┤
│            平台层 (Platform Layer)       │
│  TUN 设备管理 + 网络配置 + 系统抽象      │
└─────────────────────────────────────────┘
```

### 2.2 层次职责

#### 平台层 (Platform Layer)
- **TUN 设备管理**：跨平台 TUN 设备创建、配置和数据传输
- **网络配置**：路由表管理、DNS 设置和网络接口配置
- **系统抽象**：操作系统特定功能的统一接口

#### 协议层 (Protocol Layer)
- **SDWAN ZZVPN 协议**：完整的协议栈实现，包括认证、握手和数据传输
- **加密服务**：多种加密算法支持，包括 AES 和 XOR 加密
- **数据包处理**：IP 数据包解析、封装和转发

#### 连接层 (Connection Layer)
- **连接管理**：VPN 连接的生命周期管理和状态跟踪
- **服务器管理**：服务器列表维护、延迟测试和智能选择
- **状态机**：连接状态转换和事件处理

#### 服务层 (Service Layer)
**多平台实现策略**：
- **Windows/Linux**: HTTP API + WebSocket 服务（Go 后端）
- **iOS/macOS**: VPNService 统一管理 + Platform Channel（Swift 后端）
- **Android**: VPNService 统一管理 + Platform Channel（Kotlin 后端，开发中）

**核心功能**：
- **连接控制**：VPN 连接建立、断开、状态查询
- **实时通信**：状态更新和事件推送（WebSocket/Event Channel）
- **配置管理**：应用配置和用户凭据的安全存储
- **服务器管理**：服务器列表获取、延迟测试、智能选择

#### 界面层 (UI Layer)
- **Flutter 应用**：统一的跨平台用户界面和交互逻辑
- **平台适配**：通过 Platform Channel 或 HTTP API 与后端通信
- **状态管理**：Provider 模式的全局状态管理
- **本地功能**：系统集成、通知、生命周期管理和平台特定功能

## 3. 核心组件设计

### 3.1 基础设施组件

#### 3.1.1 统一错误处理系统 (`internal/common/errors/`)
- **错误码体系**：标准化的错误码定义和分类
- **详细错误信息**：包含上下文、组件、操作等详细信息
- **错误链追踪**：支持错误包装和原因链追踪
- **严重性级别**：Info、Warning、Error、Critical、Fatal 五个级别
- **国际化支持**：多语言错误消息支持

#### 3.1.2 结构化日志系统 (`internal/common/logger/`)
- **模块化日志**：每个组件独立的日志记录器
- **结构化输出**：JSON 格式的结构化日志
- **多级别支持**：Debug、Info、Warn、Error、Fatal 级别
- **字段化记录**：支持键值对形式的上下文信息

#### 3.1.3 对象池管理系统 (`internal/common/pool/`)
- **内存优化**：减少频繁的内存分配和回收
- **多种池类型**：字节缓冲池、数据包缓冲池等
- **自动管理**：自动回收和重用对象
- **性能监控**：池使用统计和命中率监控

#### 3.1.4 协程管理系统 (`internal/common/goroutine/`)
- **生命周期管理**：统一管理所有后台协程
- **优雅停止**：支持超时控制的优雅停止机制
- **错误恢复**：自动捕获和处理 panic
- **监控指标**：协程状态、运行时间、错误统计

#### 3.1.5 性能监控系统 (`internal/common/monitor/`)
- **实时指标**：CPU、内存、网络、GC 等系统指标
- **自定义指标**：支持业务指标的收集和监控
- **请求追踪**：请求级别的性能分析
- **告警机制**：基于阈值的性能告警

#### 3.1.6 网络连接池 (`internal/common/netpool/`)
- **连接复用**：减少网络连接建立开销
- **健康检查**：自动检测和清理无效连接
- **负载均衡**：在多个连接间分配请求
- **超时管理**：连接超时和自动清理

### 3.2 连接管理器 (Connection Manager)

连接管理器是系统的核心组件，负责 VPN 连接的完整生命周期管理。

#### 多平台实现
- **Go 后端** (`internal/connection/manager/`): 完整的连接管理实现
- **Swift 后端** (`Sources/Connection/ConnectionManager/`): Actor 模式的线程安全实现
- **Kotlin 后端** (开发中): 协程模式的异步实现

#### 主要职责
- **连接建立**：UDP 连接建立、SDWAN 认证、握手流程
- **状态管理**：连接状态机、会话信息、状态转换
- **数据传输**：TUN 设备与 UDP 连接的数据包转发
- **心跳机制**：定期心跳包发送、连接活性检测
- **错误处理**：连接异常检测、自动重连、故障恢复
- **流量统计**：实时流量监控、速度计算、统计上报

#### 核心特性
- **线程安全**：Go 的 mutex 锁机制、Swift 的 Actor 模式
- **状态机管理**：严格的状态转换控制和验证
- **自动重连**：网络变化检测、智能重连策略
- **性能优化**：对象池复用、协程管理、内存优化
- **监控集成**：实时性能指标、流量统计、错误追踪

#### 状态机设计
连接管理器使用状态机模式管理连接状态，确保状态转换的一致性：

**Go 后端状态**：
- **StateClosed (0)**：连接关闭状态
- **StateInit (1)**：初始化状态，系统启动和资源准备
- **StateInit1 (2)**：服务器解析状态，解析服务器地址
- **StateAuth (3)**：认证状态，用户认证和服务器握手
- **StateData (4)**：数据传输状态，正常 VPN 数据传输
- **StateAuthFail (5)**：认证失败状态

**Swift 后端状态**：
- **disconnected**：断开连接状态
- **connecting**：连接建立中
- **connected**：已连接状态
- **disconnecting**：断开连接中
- **error**：错误状态

**状态转换规则**：严格的状态转换验证，防止无效状态切换

### 3.3 服务器管理器 (Server Manager)

服务器管理器负责服务器列表的维护和最佳服务器的选择。

#### 核心功能
- **多源数据**：支持从 HTTP API 和本地文件获取服务器列表
- **实时监控**：定期测试服务器延迟和可用性
- **智能选择**：基于延迟、负载和健康状态选择最佳服务器
- **故障转移**：服务器故障时的自动切换机制
- **回调机制**：支持 Ping 结果和服务器更新回调

#### 选择算法
- **延迟优先**：选择延迟最低的可用服务器
- **负载均衡**：在延迟相近的服务器间分配负载
- **健康检查**：排除不可用或响应异常的服务器
- **自动服务器**：智能选择最佳的自动服务器

### 3.4 HTTP 服务层 (Windows/Linux)

HTTP 服务层为 Windows/Linux 平台提供 RESTful API 服务，与 Flutter 前端通信。

#### 服务配置
- **监听地址**：`localhost:56543` (HTTP API)
- **WebSocket 地址**：`localhost:56544` (实时事件)
- **中间件**：日志记录、错误恢复、CORS 支持

#### 核心 API 端点
基于 `internal/service/http/server.go` 的实际实现：

- **POST /api/login**：用户登录认证
- **GET /api/servers**：获取服务器列表
- **POST /api/servers/ping**：测试服务器延迟
- **POST /api/connect**：建立 VPN 连接
- **POST /api/disconnect**：断开 VPN 连接
- **GET /api/status**：获取连接状态
- **GET /api/interface**：获取网络接口信息
- **GET /api/settings/routing**：获取路由设置
- **POST /api/settings/routing**：设置路由配置
- **POST /api/servers/provider**：设置服务器提供商

#### 响应格式
统一的 JSON 响应格式 (`internal/service/api/api.go`)：
```json
{
  "success": true|false,
  "message": "响应消息",
  "data": { /* 响应数据 */ },
  "error": { /* 错误信息 */ }
}
```

### 3.5 WebSocket 服务

WebSocket 服务提供实时的状态更新和事件推送。

#### 重构改进
- **并发安全修复**：恢复所有锁机制，确保线程安全
- **事件管理优化**：统一的事件广播和客户端管理
- **连接生命周期**：完善的连接建立、维护和清理机制
- **心跳机制**：定期心跳保持连接活跃

#### 事件类型
- **状态事件**：连接状态变化通知
- **服务器事件**：服务器列表更新和 Ping 结果
- **流量统计**：实时流量数据推送
- **接口信息**：网络接口状态更新
- **心跳事件**：保持连接活跃的心跳消息

### 3.6 协议处理器 (Protocol Handler)

协议处理器实现完整的 SDWAN ZZVPN 协议栈。

#### 协议特性
- **包头格式**：标准的 SDWAN 包头结构和签名验证
- **TLV 属性**：灵活的属性编码和解析机制
- **认证流程**：安全的用户认证和会话建立
- **加密传输**：多种加密模式和密钥管理
- **分片处理**：大数据包的分片和重组机制

#### 数据流处理
- **上行数据**：TUN 设备 → 加密 → 网络发送
- **下行数据**：网络接收 → 解密 → TUN 设备
- **控制消息**：心跳、状态更新和协议控制
- **错误处理**：协议错误的检测和恢复

### 3.7 TUN 设备管理

TUN 设备管理器负责跨平台的 TUN 设备操作。

#### 重构改进
- **并发安全**：恢复完整的锁机制保护
- **错误处理统一**：使用标准错误码和详细错误信息
- **代码清理**：移除冗余代码和注释
- **状态管理**：清晰的设备生命周期管理

#### 核心功能
- **设备创建**：跨平台的 TUN 设备创建
- **网络配置**：IP 地址、路由、DNS 配置
- **数据传输**：高效的数据包读写
- **资源管理**：设备资源的自动清理

## 4. 数据流设计

### 4.1 连接建立流程

1. **用户认证**：前端发送登录请求到后端 API
2. **服务器选择**：服务器管理器选择最佳服务器
3. **连接建立**：连接管理器建立 UDP 连接
4. **协议握手**：发送 OPEN 包并等待 OPENACK 响应
5. **TUN 配置**：根据服务器响应配置 TUN 设备
6. **数据传输**：启动数据包转发和心跳机制

### 4.2 数据传输流程

#### 上行数据流
```
应用程序 → TUN设备 → 连接管理器 → 协议处理器 → 加密 → UDP发送
```

#### 下行数据流
```
UDP接收 → 协议处理器 → 解密 → 连接管理器 → TUN设备 → 应用程序
```

### 4.3 状态同步流程

1. **状态变化**：连接管理器检测到状态变化
2. **事件生成**：生成相应的状态事件
3. **WebSocket 推送**：通过 WebSocket 推送到前端
4. **界面更新**：前端接收事件并更新界面状态

## 5. 安全设计

### 5.1 数据加密

- **传输加密**：SDWAN ZZVPN 协议内置的数据加密
- **凭据加密**：用户凭据的本地加密存储
- **通信安全**：API 通信的安全机制

### 5.2 认证机制

- **用户认证**：基于用户名密码的认证流程
- **会话管理**：安全的会话令牌和超时机制
- **权限控制**：API 访问的权限验证

### 5.3 安全存储

- **凭据保护**：使用机器绑定的密钥加密用户凭据
- **配置安全**：敏感配置信息的安全存储
- **日志安全**：日志信息的脱敏和保护

## 6. 性能优化

### 6.1 内存管理优化

#### 对象池系统
- **全局对象池**：减少 70% 的内存分配
- **多种池类型**：小、中、大、数据包缓冲池
- **自动管理**：自动回收和重用对象
- **命中率监控**：实时监控池使用效率

#### 内存使用优化
- **缓冲区优化**：增加通道缓冲区大小（10240）
- **原子操作**：使用原子计数器避免锁竞争
- **GC 优化**：减少垃圾回收压力

### 6.2 并发性能优化

#### 协程管理
- **统一管理**：所有后台协程的生命周期管理
- **优雅停止**：支持超时控制的停止机制
- **错误恢复**：自动捕获和处理 panic
- **资源监控**：协程状态和性能指标

#### 锁优化
- **读写锁**：合理使用读写锁减少竞争
- **锁粒度**：最小化锁持有时间
- **原子操作**：使用原子操作替代锁保护的简单操作

### 6.3 网络性能优化

#### 连接池管理
- **连接复用**：减少网络连接建立开销
- **健康检查**：自动检测和清理无效连接
- **负载均衡**：在多个连接间分配请求
- **超时管理**：连接超时和自动清理

#### 数据传输优化
- **缓冲优化**：优化数据包缓冲和处理
- **异步处理**：使用协程实现异步数据处理
- **流量统计**：实时流量监控和速度计算

### 6.4 监控和分析

#### 性能监控系统
- **实时指标**：CPU、内存、网络、GC 等系统指标
- **自定义指标**：业务指标的收集和监控
- **请求追踪**：请求级别的性能分析
- **告警机制**：基于阈值的性能告警

#### 性能分析
- **热点分析**：识别性能瓶颈
- **资源使用**：监控资源使用情况
- **趋势分析**：性能趋势和预测

### 6.5 用户体验优化

- **快速连接**：优化连接建立时间
- **实时反馈**：实时的状态更新和错误提示
- **智能重连**：网络异常时的自动重连机制
- **流畅交互**：减少界面卡顿和延迟

## 7. 扩展性设计

### 7.1 平台扩展

- **接口抽象**：统一的平台抽象接口
- **插件机制**：支持平台特定功能的插件化实现
- **配置驱动**：通过配置支持不同平台的差异化需求

### 7.2 协议扩展

- **协议版本**：支持协议版本协商和兼容性处理
- **功能扩展**：预留协议扩展点支持新功能
- **向后兼容**：保持与旧版本协议的兼容性

### 7.3 功能扩展

- **模块化设计**：清晰的模块边界和接口定义
- **插件架构**：支持功能插件的动态加载
- **配置化**：通过配置启用或禁用特定功能

## 8. 部署架构

### 8.1 单机部署

- **一体化应用**：后端服务和前端界面打包为单一应用
- **本地通信**：通过本地网络接口进行前后端通信
- **资源共享**：共享系统资源和配置文件

### 8.2 分布式部署

- **服务分离**：后端服务可独立部署和运行
- **远程访问**：前端可连接到远程后端服务
- **负载均衡**：支持多个后端实例的负载均衡

### 8.3 容器化部署

- **Docker 支持**：提供 Docker 镜像和部署脚本
- **编排支持**：支持 Kubernetes 等容器编排平台
- **配置管理**：通过环境变量和配置文件管理部署参数
