# SDWAN ZZVPN 协议与实现文档

## 目录

1. [协议概述](#1-协议概述)
2. [协议包格式](#2-协议包格式)
   - [2.1 通用包头](#21-通用包头)
   - [2.2 包签名](#22-包签名)
   - [2.3 TLV属性区](#23-tlv属性区)
3. [OPEN包详解](#3-open包详解)
   - [3.1 组包流程](#31-组包流程)
   - [3.2 密码加密方式](#32-密码加密方式)
   - [3.3 OPENACK包处理](#33-openack包处理)
   - [3.4 OPENREJ包处理](#34-openrej包处理)
4. [加密与密钥生成](#4-加密与密钥生成)
   - [4.1 会话密钥生成](#41-会话密钥生成)
   - [4.2 数据加解密](#42-数据加解密)
5. [收发包流程](#5-收发包流程)
   - [5.1 发送流程](#51-发送流程)
   - [5.2 接收流程](#52-接收流程)
   - [5.3 包类型分发](#53-包类型分发)
   - [5.4 各类型包的详细处理逻辑](#54-各类型包的详细处理逻辑)
6. [状态机维护](#6-状态机维护)
   - [6.1 状态定义](#61-状态定义)
   - [6.2 状态流转](#62-状态流转)
   - [6.3 状态切换触发点](#63-状态切换触发点)
7. [Go实现分析](#7-go实现分析)
   - [7.1 协议一致性分析](#71-协议一致性分析)
   - [7.2 包接收主流程一致性分析](#72-包接收主流程一致性分析)
   - [7.3 差异总结](#73-差异总结)
8. [开发要点与注意事项](#8-开发要点与注意事项)
9. [典型包格式示例](#9-典型包格式示例)
   - [9.1 OPEN包二进制结构](#91-open包二进制结构)
   - [9.2 OPENACK包结构](#92-openack包结构)
   - [9.3 ECHO包结构](#93-echo包结构)

## 1. 协议概述

SDWAN ZZVPN是一种用于安全远程接入的VPN协议，支持多种平台，包括Windows、Linux、Android、iOS等。本文档详细说明ZZVPN SD-WAN客户端核心协议、包格式、加密方式、收发包流程、状态机维护及开发要点，便于协议实现、调试和维护。

## 2. 协议包格式

### 2.1 通用包头

每个自定义协议包都以包头开头，定义如下：

```c
typedef struct sdwan_pkthdr {
    uint8_t type;      // 包类型（如 SDWAN_OPEN、SDWAN_DATA 等）
    uint8_t encrypt;   // 加密标志
    uint16_t sid;      // 会话ID
    uint32_t token;    // 会话token
} sdwan_pkthdr_t;
```

- type 取值包括：
  - `SDWAN_OPEN` (0x13)：认证/握手包
  - `SDWAN_OPENACK` (0x12)：认证成功回复
  - `SDWAN_DATA` (0x14)：数据包
  - `SDWAN_ECHOREQ` (0x15)：心跳包
  - `SDWAN_ECHORES` (0x16)：心跳回复
  - `SDWAN_CLOSE` (0x17)：关闭连接
  - 其它类型详见头文件

### 2.2 包签名

- 包头后紧跟16字节MD5签名，用于包完整性校验
- 签名内容为：`MD5(包头 + "mw")`
- **重要**：所有控制类型的数据包（如OPEN、OPENACK、ECHOREQ等）都必须包含签名，数据包（DATA、DATAENC等）不需要签名

### 2.3 TLV属性区

- 包头+签名后是若干TLV（Type-Length-Value）属性
- 每个属性格式：`[Type][Length][Value...]`
  - Type: 1字节
  - Length: 1字节（包括Type和Length本身）
  - Value: 长度为 Length-2 的数据
- 常用Type定义：
  - `SDWAN_USERNAME` (0x01)：用户名
  - `SDWAN_PASSWORD` (0x02)：密码（加密）
  - `SDWAN_MTU` (0x03)：MTU
  - `SDWAN_IP` (0x04)：分配IP
  - `SDWAN_DNS` (0x05)：DNS
  - `SDWAN_GATEWAY` (0x06)：网关
  - `SDWAN_ENCRYPT` (0x08)：加密类型
  - `SDWAN_IP6` (0x0b)：IPv6地址
  - `SDWAN_DNS6` (0x0c)：IPv6 DNS
  - `SDWAN_GATEWAY6` (0x0d)：IPv6网关

## 3. OPEN包详解

### 3.1 组包流程

OPEN包用于认证和协商参数，组包步骤如下：

1. 包头赋值：type=SDWAN_OPEN，encrypt=配置，sid=0，token=0
2. 计算MD5签名，写入包头后16字节
3. 依次写入TLV属性（**属性顺序非常重要**）：
   - MTU（4字节）**必须是第一个属性**
   - 用户名（变长）**必须是第二个属性**
   - 密码（16字节，AES加密，密钥见下）**必须是第三个属性**
   - ENCRYPT（可选，3字节）
   - SR Header（可选，链路信息）
4. 返回包长度

> **注意**：TLV属性的顺序对服务器解析至关重要。服务器期望MTU属性在第一位，用户名属性在第二位，密码属性在第三位。如果顺序不正确，服务器可能无法正确解析数据包，导致认证失败。

### 3.2 密码加密方式

- 密码加密密钥为 `MD5("mw" + 用户名)`
- 使用AES-ECB模式加密密码，具体步骤：
  1. 将密码填充到32字节（不足部分填0）
  2. 使用AES-ECB模式加密填充后的密码
  3. 只取加密后的前16字节写入OPEN包
- 这与Android实现中的`HandShakeHelper.encrypt()`方法一致

### 3.3 OPENACK包处理

收到OPENACK包后，客户端：

1. 校验状态和签名
2. 解析TLV属性，写入客户端结构体（如MTU、IP、GATEWAY、DNS等）
3. 切换状态为DATA，记录会话ID、token
4. 配置虚拟网卡IP、路由、DNS等

### 3.4 OPENREJ包处理

- 校验状态和签名
- 记录认证失败原因
- 状态切换为AUTHFAIL
- 重置会话

## 4. 加密与密钥生成

### 4.1 会话密钥生成

- 方式：`MD5(用户名 + 密码)`，取前16字节作为会话密钥
- 用于数据包加解密

### 4.2 数据加解密

- 数据包加密采用异或：每8字节与会话密钥做异或，剩余不足8字节部分与密钥前N字节异或
- 加密/解密函数本质上是同一个函数
- 对于AES加密，必须使用AES-ECB模式，不使用PKCS#7填充，与Android实现保持一致

## 5. 收发包流程

### 5.1 发送流程

- 主要通过UDP发送包
- 发送OPEN包：认证时发送
- 发送ECHO包：保持连接活跃
- 发送数据包：TUN设备读取数据后组包发送

### 5.2 接收流程

1. UDP socket接收数据
   - 读取UDP包到缓冲区，获得数据长度
2. 状态判断
   - 如果当前状态为INIT（DNS查询），则进入DNS解析流程
   - 否则，进入核心包处理流程
3. 包签名验证
   - 对所有非数据包类型的数据包进行签名验证
   - 数据包（DATA、DATAENC等）不需要验证签名

### 5.3 包类型分发

核心分发逻辑如下：

```c
switch (pkt->type) {
    case SDWAN_DATA:
    case SDWAN_DATAENC:
    case SDWAN_DATA6:
        // 数据包
        sdwan_onDATA(clnt, buf, len, pkt);
        break;
    case SDWAN_OPENACK:
        // 认证成功
        sdwan_onOPENACK(clnt, buf, len, pkt);
        break;
    case SDWAN_OPENREJ:
        // 认证失败
        sdwan_onOPENREJ(clnt, buf, len, pkt);
        break;
    case SDWAN_ECHORES:
        // 心跳响应
        sdwan_onECHORESP(clnt, buf, len, pkt);
        break;
    case SDWAN_CLOSE:
        // 关闭连接
        sdwan_onCLOSE(clnt, buf, len, pkt);
        break;
    case SDWAN_IPFRAG:
    case SDWAN_IPFRAG6:
        // 分片包
        sdwan_onIPFRAG(clnt, buf, len, pkt);
        break;
    case SDWAN_SEGRT:
        // SR路由包
        sdwan_onSEGRT(clnt, buf, len);
        break;
    default:
        // 未知类型
        clnt->si_stats.panic_pkttype++;
        break;
}
```

### 5.4 各类型包的详细处理逻辑

#### 5.4.1 数据包（SDWAN_DATA / SDWAN_DATAENC / SDWAN_DATA6）

- 流程：
  1. 若为加密包（DATAENC），先解密数据体
  2. 将数据写入TUN虚拟网卡
  3. 统计数据包数量

#### 5.4.2 认证成功包（SDWAN_OPENACK）

- 流程：
  1. 校验当前状态为AUTH，并校验包签名
  2. 解析TLV属性，提取MTU、IP、GATEWAY、DNS等
  3. 切换状态为DATA，记录会话ID、token
  4. 配置本地虚拟网卡IP、路由、DNS等

#### 5.4.3 认证失败包（SDWAN_OPENREJ）

- 流程：
  1. 校验当前状态为AUTH，并校验包签名
  2. 记录认证失败原因
  3. 切换状态为AUTHFAIL，重置会话

#### 5.4.4 心跳请求包（SDWAN_ECHOREQ）

- 流程：
  1. 校验当前状态为DATA，并校验包签名
  2. 更新最后活动时间
  3. 发送心跳响应包（ECHORES）

#### 5.4.5 心跳响应包（SDWAN_ECHORES）

- 流程：
  1. 校验当前状态为DATA，并校验包签名
  2. 解析ECHO体，更新延迟统计
  3. 若包中带有路由信息，则更新路由表

#### 5.4.6 关闭包（SDWAN_CLOSE）

- 流程：
  1. 校验当前状态为DATA，并校验包签名
  2. 切换状态为CLOSED，记录关闭次数

#### 5.4.7 分片包（SDWAN_IPFRAG / SDWAN_IPFRAG6）

- 流程：
  1. 解析分片头，按ID和偏移重组数据
  2. 分片重组完成后，写入TUN虚拟网卡

#### 5.4.8 SR路由包（SDWAN_SEGRT）

- 流程：
  1. 解析SR头，提取链路信息
  2. 剩余数据作为数据包处理

## 6. 状态机维护

### 6.1 状态定义

- `SDWANCLNT_FREE`：未初始化
- `SDWANCLNT_INIT`：DNS查询
- `SDWANCLNT_INIT1`：DNS解析成功，准备认证
- `SDWANCLNT_AUTH`：认证中
- `SDWANCLNT_DATA`：数据通道建立
- `SDWANCLNT_CLOSED`：连接关闭
- `SDWANCLNT_AUTHFAIL`：认证失败

### 6.2 状态流转

- 由定时器驱动，1秒一次
- INIT：发DNS包，收到响应进入INIT1
- INIT1：进入AUTH，发OPEN包
- AUTH：等待OPENACK，超时重发或失败
- DATA：正常收发数据，定时发ECHO包
- CLOSE：收到CLOSE包或超时，重置状态
- AUTHFAIL：认证失败

### 6.3 状态切换触发点

- 包处理函数会根据协议内容切换状态
- 超时/异常也会切换状态
- CLOSED → INIT/INIT1：定时器驱动，自动重连
- 其它异常切换：包校验失败、token/sid不匹配、socket异常等

## 7. Go实现分析

### 7.1 协议一致性分析

#### 7.1.1 通用包头

✅ **一致**
- Go实现在 `tunnel/packet.go` 中定义了 `PacketHeader` 结构体，包含 Type、Encrypt、SID 和 Token 字段，与协议规范一致。

#### 7.1.2 包签名

✅ **一致**
- Go实现在 `tunnel/auth.go` 中实现了包签名计算，使用 `MD5(包头 + "mw")`
- 在 `connection/manager.go` 的 `receivePackets` 方法中对所有非数据包类型的数据包进行签名验证
- 数据包（DATA、DATAENC等）不需要验证签名

#### 7.1.3 TLV属性区

✅ **一致**
- Go实现在 `tunnel/auth.go` 中实现了 TLV 属性的添加和解析
- 属性类型定义在 `tunnel/constants.go` 中，与协议规范一致

#### 7.1.4 OPEN包详解

✅ **一致**
- Go实现在 `tunnel/auth.go` 的 `CreateOpenPacket` 方法中实现了 OPEN 包的组包流程
- 包头赋值、计算签名、添加 TLV 属性的步骤与协议规范一致
- 密码加密方式使用 `MD5("mw" + 用户名)` 作为密钥，使用 AES-ECB 加密密码

#### 7.1.5 加密与密钥生成

✅ **一致**
- Go实现在 `encryption/encryption.go` 中使用 `MD5(用户名 + 密码)` 生成会话密钥
- 实现了 XOR 加密，每 8 字节与会话密钥做异或，剩余部分与密钥前 N 字节异或
- 实现了 AES-ECB 加密，不使用 PKCS#7 填充，与 Android 实现一致

#### 7.1.6 心跳包实现

✅ **一致**
- Go实现在 `tunnel/auth.go` 的 `CreateEchoRequestPacket` 方法中实现了心跳包的组包流程
- 包头赋值、计算签名、添加时间戳和延迟信息的步骤与协议规范一致
- 心跳包格式包含 MD5 签名、时间戳、延迟信息和 SDRT 标签

### 7.2 包接收主流程一致性分析

#### 7.2.1 UDP socket接收数据

✅ **一致**
- Go实现在 `connection/manager.go` 的 `receivePackets` 方法中实现了UDP数据接收
- 使用 `conn.Read(buffer)` 读取UDP包到缓冲区

#### 7.2.2 状态判断

⚠️ **部分一致**
- Go实现没有专门处理 DNS 查询状态，因为使用 Go 的标准库进行 DNS 解析
- 但在 `authenticate` 方法中实现了类似的逻辑，先解析服务器地址，再进入认证流程

#### 7.2.3 包类型分发

✅ **一致**
- Go实现在 `connection/manager.go` 的 `receivePackets` 方法中实现了根据包类型分发的逻辑
- 分发逻辑与协议规范基本一致

#### 7.2.4 各类型包的详细处理逻辑

✅ **大部分一致**
- 数据包处理：一致
- 认证成功包处理：一致
- 认证失败包处理：一致
- 心跳请求包处理：一致
- 心跳响应包处理：一致（已实现解析 ECHO 体和更新延迟统计）
- 关闭包处理：一致
- 分片包处理：部分一致（没有实现分片重组）
- SR路由包处理：不一致（没有实现）

#### 7.2.5 处理流程关键点

✅ **一致**
- 包头校验：对所有非数据包类型的数据包进行签名验证
- 会话校验：一致
- 状态校验：一致
- 异常处理：一致

### 7.3 差异总结

Go实现与SDWAN ZZVPN协议规范的主要差异：

1. **DNS查询处理**：使用Go标准库进行DNS解析，而不是在包处理流程中处理DNS查询
2. **分片包处理**：没有实现分片重组的功能
3. **SR路由包处理**：没有实现SR路由包的处理

## 8. 开发要点与注意事项

- 所有协议包均需严格按TLV格式组装，属性顺序必须与服务端保持一致
- 密钥生成、加密算法需与服务端完全一致，否则无法互通
- 状态机切换需严格遵循协议流程，异常/超时需及时重置状态
- 日志建议详细记录每一步操作，便于调试
- 所有非数据包类型的数据包都必须包含MD5签名，并在接收时进行验证
- AES加密必须使用ECB模式，不使用PKCS#7填充，与Android实现保持一致
- 心跳包必须包含正确的时间戳、延迟信息和SDRT标签

## 9. 典型包格式示例

### 9.1 OPEN包二进制结构

```
[包头: sdwan_pkthdr_t][签名:16字节MD5][MTU TLV][用户名TLV][加密密码TLV][可选TLV...]
```

### 9.2 OPENACK包结构

```
[包头][签名][MTU TLV][IP TLV][GATEWAY TLV][DNS TLV][ENCRYPT TLV][可选TLV...]
```

### 9.3 ECHO包结构

```
[包头: sdwan_pkthdr_t][签名:16字节MD5][时间戳:8字节][当前延迟:4字节][最小延迟:4字节][最大延迟:4字节][SDRT标签:4字节]
```
