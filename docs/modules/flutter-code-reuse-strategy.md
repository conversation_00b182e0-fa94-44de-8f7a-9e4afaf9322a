# Flutter 代码复用策略设计

## 📋 文档概述

本文档提供iOS/macOS平台Flutter代码复用的详细策略，**最大化保持现有Flutter代码不变**，仅通过最小化修改实现平台适配。

**核心原则**：
- **零UI修改**：所有现有UI组件、屏幕、Widget保持完全不变
- **接口兼容**：保持现有服务接口签名完全一致
- **依赖注入替换**：仅在依赖注入层进行平台检测和服务替换
- **渐进式迁移**：支持逐步从HTTP切换到Platform Channel

## 🎯 代码复用分析

### 1. 现有代码结构分析

```
ui/flutter/lib/
├── screens/                      # ✅ 完全保持不变
│   ├── redesigned_main_screen.dart
│   ├── server_list_screen.dart
│   ├── settings_screen.dart
│   └── 其他屏幕...
├── widgets/                      # ✅ 完全保持不变
│   ├── enhanced_connection_button.dart
│   ├── server_list_card.dart
│   └── 其他组件...
├── models/                       # ✅ 完全保持不变
│   ├── server.dart
│   ├── connection_status.dart
│   └── 其他模型...
├── services/                     # 🔄 仅替换实现，接口不变
│   ├── api_service.dart         # 保持接口，替换HTTP为Platform Channel
│   ├── websocket_service.dart   # 保持接口，替换WebSocket为Event Channel
│   ├── connection_manager.dart  # ✅ 完全保持不变
│   └── 其他服务...
└── core/                        # 🔄 仅修改依赖注入
    └── dependency_injection.dart # 添加平台检测逻辑
```

### 2. 最小化修改策略

**修改范围**：
- ✅ **UI层**：0个文件需要修改
- ✅ **业务逻辑层**：0个文件需要修改  
- 🔄 **服务层**：2个文件需要适配（api_service.dart, websocket_service.dart）
- 🔄 **依赖注入**：1个文件需要修改（dependency_injection.dart）
- ➕ **新增文件**：3-4个平台适配文件

## 🔧 具体实现策略

### 1. 依赖注入层修改

```dart
// 在 core/dependency_injection.dart 中添加平台检测
void setupDependencies() {
  final logService = LogService();
  
  // 平台检测和服务创建
  ApiService apiService;
  WebSocketService webSocketService;
  
  if (Platform.isIOS || Platform.isMacOS) {
    // iOS/macOS平台使用Platform Channel
    apiService = PlatformChannelApiService(logService: logService);
    webSocketService = EventChannelWebSocketService(logService: logService);
  } else {
    // 其他平台使用HTTP
    apiService = ApiService(baseUrl: BackendConfig.baseUrl)..logService = logService;
    webSocketService = WebSocketService(apiService);
  }
  
  // 注册服务（其他代码保持不变）
  GetIt.instance.registerSingleton<ApiService>(apiService);
  GetIt.instance.registerSingleton<WebSocketService>(webSocketService);
  
  // 其他服务注册保持完全不变...
}
```

### 2. ApiService适配策略

```dart
// 新建 services/platform_channel_api_service.dart
class PlatformChannelApiService extends ApiService {
  static const MethodChannel _channel = MethodChannel('itforce_vpn/methods');
  
  @override
  final LogService logService;
  
  // 继承现有ApiService，保持接口完全一致
  PlatformChannelApiService({required this.logService}) : super(baseUrl: '');
  
  // 重写所有方法，保持返回类型和参数完全一致
  @override
  Future<Map<String, dynamic>> login(String username, String password) async {
    try {
      final result = await _channel.invokeMethod('login', {
        'username': username,
        'password': password,
      });
      return Map<String, dynamic>.from(result);
    } catch (e) {
      if (e is PlatformException) {
        throw ApiException(e.message ?? 'Login failed', 1, e.code);
      }
      rethrow;
    }
  }
  
  @override
  Future<List<Server>> getServers() async {
    try {
      final result = await _channel.invokeMethod('getServers');
      if (result is List) {
        return result.map((server) => Server.fromJson(server)).toList();
      }
      return [];
    } catch (e) {
      if (e is PlatformException) {
        throw ApiException(e.message ?? 'Get servers failed', 1, e.code);
      }
      rethrow;
    }
  }
  
  // 其他方法保持相同模式...
}
```

### 3. WebSocketService适配策略

```dart
// 新建 services/event_channel_websocket_service.dart
class EventChannelWebSocketService extends WebSocketService {
  static const EventChannel _eventChannel = EventChannel('itforce_vpn/events');
  
  final StreamController<Map<String, dynamic>> _eventController = 
      StreamController<Map<String, dynamic>>.broadcast();
  
  StreamSubscription? _eventSubscription;
  
  // 继承现有WebSocketService，保持接口完全一致
  EventChannelWebSocketService({required LogService logService}) 
      : super(ApiService(baseUrl: '')) {
    // 设置Event Channel监听
    _setupEventChannel();
  }
  
  void _setupEventChannel() {
    _eventSubscription = _eventChannel.receiveBroadcastStream().listen(
      (event) {
        if (event is Map<String, dynamic>) {
          _eventController.add(event);
        }
      },
      onError: (error) {
        // 错误处理
      },
    );
  }
  
  @override
  Stream<Map<String, dynamic>> get eventStream => _eventController.stream;
  
  @override
  Future<bool> connect(String url) async {
    // Platform Channel不需要显式连接，直接返回成功
    return true;
  }
  
  @override
  Future<void> disconnect() async {
    // Platform Channel不需要显式断开
  }
  
  // 其他方法保持兼容...
}
```

## 📊 修改影响评估

### 1. 文件修改统计

| 类别 | 现有文件数 | 需要修改 | 修改比例 |
|------|-----------|----------|----------|
| **UI屏幕** | ~10个 | 0个 | 0% |
| **UI组件** | ~15个 | 0个 | 0% |
| **数据模型** | ~8个 | 0个 | 0% |
| **业务服务** | ~15个 | 0个 | 0% |
| **核心服务** | 2个 | 2个 | 100% |
| **依赖注入** | 1个 | 1个 | 100% |
| **新增文件** | - | 3个 | - |

**总体修改比例**：3/50+ = **<6%**

### 2. 风险评估

**低风险**：
- ✅ UI层完全不变，用户体验保持一致
- ✅ 业务逻辑完全不变，功能保持一致
- ✅ 数据模型完全不变，数据流保持一致

**中等风险**：
- 🔄 Platform Channel通信可能存在性能差异
- 🔄 事件处理时序可能略有不同

**缓解措施**：
- 完整的单元测试覆盖
- 渐进式迁移，支持回退
- 详细的兼容性测试

## 🚀 实施计划

### 阶段一：准备阶段 (0.5周)
- [ ] 分析现有代码依赖关系
- [ ] 设计Platform Channel接口规范
- [ ] 准备测试环境

### 阶段二：适配层实现 (1周)
- [ ] 实现PlatformChannelApiService
- [ ] 实现EventChannelWebSocketService
- [ ] 修改dependency_injection.dart

### 阶段三：测试验证 (0.5周)
- [ ] 单元测试验证接口兼容性
- [ ] 集成测试验证功能完整性
- [ ] UI测试验证用户体验一致性

### 阶段四：部署上线 (0.5周)
- [ ] 代码审查和优化
- [ ] 文档更新
- [ ] 发布和监控

**总计**：2.5周完成完整适配

## ✅ 验收标准

### 功能兼容性
- [ ] 所有现有功能100%保持
- [ ] 所有UI界面完全一致
- [ ] 所有用户操作流程不变

### 性能标准
- [ ] 应用启动时间变化 < 10%
- [ ] API调用响应时间 < 200ms
- [ ] 内存使用增长 < 5MB

### 代码质量
- [ ] 现有代码零修改（除了3个指定文件）
- [ ] 新增代码符合现有代码规范
- [ ] 单元测试覆盖率 > 90%

---

**AUTHOR**: wei
**HISTORY**: 23/06/2025 create flutter code reuse strategy
