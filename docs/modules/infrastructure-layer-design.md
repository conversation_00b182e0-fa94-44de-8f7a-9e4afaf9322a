# Infrastructure Layer 详细设计文档

## 📋 文档概述

**模块名称**: Infrastructure Layer (基础设施层)
**对应Go模块**: `internal/common/`
**创建日期**: 2025-06-23
**最后更新**: 2025-07-15
**版本**: v2.0
**实施状态**: ✅ **100% 完成** - 生产级实现
**设计规范**: 遵循 `docs/ios-macos-design-guidelines.md` 和 `docs/go-backend-optimization-plan.md` 规范

## 1. 模块概述

### 1.1 模块职责和目标

Infrastructure Layer 是整个iOS/macOS VPN应用的基础设施层，为上层模块提供核心的基础服务。主要职责包括：

- **日志系统**: 基于OSLog的结构化、高性能日志记录服务
- **错误处理**: 统一的错误类型定义和Swift错误处理机制
- **性能监控**: 应用性能指标收集和NetworkExtension内存监控
- **数据缓冲池**: 简化的数据包缓冲复用，优化高频内存分配（可选组件）

**Swift特性优化**:
- 利用Swift原生`async/await`、`Actor`并发机制，无需手动协程管理
- 基于ARC自动内存管理，简化内存池设计
- 针对NetworkExtension环境优化，重点关注内存限制和性能监控

### 1.2 与Go后端对应关系

| Swift模块 | Go模块 | 功能对应 | 实施状态 |
|-----------|--------|----------|----------|
| `Logger` | `internal/common/logger/` | 结构化日志记录 | ✅ **完成** |
| `ErrorTypes` | `internal/common/errors/` | 错误处理和类型定义 | ✅ **完成** |
| `PerformanceMonitor` | `internal/common/monitor/` | 性能指标监控 | ✅ **完成** |
| `DataManager` | `internal/common/storage/` | 数据存储和配置管理 | ✅ **完成** |
| `PlatformDetector` | `internal/platform/` | 平台检测和适配 | ✅ **完成** |
| ~~`DataBufferPool`~~ | ~~`internal/common/pool/`~~ | ~~数据缓冲池~~ | ❌ **简化移除** |
| ~~`GoroutineManager`~~ | ~~`internal/common/goroutine/`~~ | ~~并发任务管理~~ | ❌ **Swift原生替代** |

**实际实现位置**: `ui/flutter/ItForceCore/Sources/Infrastructure/`

**设计调整说明**:
- **GoroutineManager移除**: Swift使用`async/await`、`Task`、`Actor`等原生并发机制，无需手动协程管理
- **MemoryManager简化**: 重命名为`DataBufferPool`，专注于数据包缓冲复用，移除复杂的池管理逻辑
- **ResourceMonitor移入**: 从Service Layer移入，更符合基础设施层的职责定位

### 1.3 在整体架构中的位置

Infrastructure Layer 位于架构的最底层，被所有上层模块依赖：

```
Service Layer ────────┐
Connection Layer ─────┤
Protocol Layer ───────┼──→ Infrastructure Layer
Platform Layer ───────┤
Flutter Integration ──┘
```

## 2. 接口设计

### 2.1 日志系统接口

```swift
/**
 * FILE: LoggingSystem.swift
 *
 * DESCRIPTION:
 *     Logging system interfaces and types for structured logging
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

/**
 * NAME: LogLevel
 *
 * DESCRIPTION:
 *     Enumeration defining different log levels with priority ordering
 *
 * CASES:
 *     debug - Detailed information for debugging
 *     info - General information messages
 *     warning - Warning messages for potential issues
 *     error - Error messages for failures
 *     fatal - Critical errors that may cause application termination
 */
public enum LogLevel: Int, CaseIterable {
    case debug = 0
    case info = 1
    case warning = 2
    case error = 3
    case fatal = 4
}

/**
 * NAME: LogField
 *
 * DESCRIPTION:
 *     Protocol for structured log field data
 *
 * PROPERTIES:
 *     key - Field identifier
 *     value - Field value
 */
public protocol LogField {
    var key: String { get }
    var value: Any { get }
}

/**
 * NAME: Logger
 *
 * DESCRIPTION:
 *     Main logging interface providing structured logging capabilities
 *
 * METHODS:
 *     debug - Log debug level messages
 *     info - Log informational messages
 *     warning - Log warning messages
 *     error - Log error messages
 *     fatal - Log fatal error messages
 *     withModule - Create module-specific logger
 *     withRequestID - Create request-specific logger
 *     withContext - Create context-aware logger
 *     traceMethod - Trace method execution time
 *     traceError - Trace error with context
 *     sync - Synchronize log output
 *     close - Close logger and cleanup resources
 */
public protocol Logger {
    // Basic logging methods
    func debug(_ message: String, fields: [LogField])
    func info(_ message: String, fields: [LogField])
    func warning(_ message: String, fields: [LogField])
    func error(_ message: String, fields: [LogField])
    func fatal(_ message: String, fields: [LogField])

    // Context-aware methods
    func withModule(_ module: String) -> Logger
    func withRequestID(_ requestID: String) -> Logger
    func withContext(_ context: [String: Any]) -> Logger

    // Performance tracking methods
    func traceMethod(_ methodName: String) -> (() -> Void)
    func traceError(_ error: Error, message: String, fields: [LogField])

    // Synchronization and cleanup
    func sync() async throws
    func close() async throws
}
```

### 2.2 错误处理接口

```swift
/**
 * NAME: ErrorCode
 *
 * DESCRIPTION:
 *     Standardized error codes organized by category for consistent error handling
 *
 * CASES:
 *     networkTimeout - Network operation timeout (1001)
 *     networkUnreachable - Network unreachable (1002)
 *     connectionFailed - Connection establishment failed (1003)
 *     authenticationFailed - Authentication process failed (2001)
 *     invalidCredentials - Invalid user credentials (2002)
 *     tokenExpired - Authentication token expired (2003)
 *     configurationInvalid - Invalid configuration data (3001)
 *     configurationMissing - Missing required configuration (3002)
 *     systemResourceUnavailable - System resource unavailable (4001)
 *     permissionDenied - Permission denied for operation (4002)
 *     memoryLimitExceeded - Memory limit exceeded (4003)
 */
public enum ErrorCode: Int, CaseIterable {
    // Network related errors (1000-1999)
    case networkTimeout = 1001
    case networkUnreachable = 1002
    case connectionFailed = 1003

    // Authentication related errors (2000-2999)
    case authenticationFailed = 2001
    case invalidCredentials = 2002
    case tokenExpired = 2003

    // Configuration related errors (3000-3999)
    case configurationInvalid = 3001
    case configurationMissing = 3002

    // System related errors (4000-4999)
    case systemResourceUnavailable = 4001
    case permissionDenied = 4002
    case memoryLimitExceeded = 4003
}

/**
 * NAME: ErrorSeverity
 *
 * DESCRIPTION:
 *     Error severity levels for categorizing error impact
 *
 * CASES:
 *     info - Informational, no action required
 *     warning - Warning, attention recommended
 *     error - Error, action required
 *     critical - Critical error, immediate action required
 *     fatal - Fatal error, system may be unstable
 */
public enum ErrorSeverity: Int {
    case info = 0
    case warning = 1
    case error = 2
    case critical = 3
    case fatal = 4
}

/**
 * NAME: DetailedError
 *
 * DESCRIPTION:
 *     Enhanced error protocol providing detailed error information and context
 *
 * PROPERTIES:
 *     code - Standardized error code
 *     message - Human-readable error message
 *     details - Additional error details
 *     severity - Error severity level
 *     context - Additional context information
 *     underlyingError - Original underlying error
 *     timestamp - Error occurrence timestamp
 *
 * METHODS:
 *     withContext - Add context information
 *     withComponent - Add component information
 *     withOperation - Add operation information
 */
public protocol DetailedError: Error {
    var code: ErrorCode { get }
    var message: String { get }
    var details: String? { get }
    var severity: ErrorSeverity { get }
    var context: [String: Any] { get }
    var underlyingError: Error? { get }
    var timestamp: Date { get }

    func withContext(_ key: String, value: Any) -> DetailedError
    func withComponent(_ component: String) -> DetailedError
    func withOperation(_ operation: String) -> DetailedError
}
```

### 2.3 性能监控接口

```swift
// MARK: - 性能指标结构
public struct PerformanceMetrics {
    let memoryUsed: UInt64
    let memoryAvailable: UInt64
    let cpuUsage: Double
    let networkBytesIn: UInt64
    let networkBytesOut: UInt64
    let activeConnections: Int
    let timestamp: Date
    let custom: [String: Any]
}

// MARK: - 指标收集器协议
public protocol MetricCollector {
    var name: String { get }
    func collect() -> [String: Any]
}

// MARK: - 性能监控接口
public protocol PerformanceMonitor {
    func start() async throws
    func stop() async
    func getCurrentMetrics() -> PerformanceMetrics
    func registerCollector(_ collector: MetricCollector)
    func removeCollector(name: String)
    
    // 事件回调
    var onMetricsUpdated: ((PerformanceMetrics) -> Void)? { get set }
    var onThresholdExceeded: ((String, Any) -> Void)? { get set }
}
```

### 2.4 数据缓冲池接口（简化版）

```swift
/**
 * NAME: DataBufferPool
 *
 * DESCRIPTION:
 *     Simplified data buffer pool for high-frequency packet processing
 *     Optimized for NetworkExtension memory constraints
 *
 * METHODS:
 *     getBuffer - Get reusable data buffer
 *     returnBuffer - Return buffer to pool
 *     clear - Clear all buffers
 *     getStatistics - Get pool usage statistics
 */
public protocol DataBufferPool {
    func getBuffer(size: Int) -> Data
    func returnBuffer(_ buffer: Data)
    func clear()
    func getStatistics() -> BufferPoolStatistics
}

/**
 * NAME: BufferPoolStatistics
 *
 * DESCRIPTION:
 *     Statistics for buffer pool usage monitoring
 *
 * PROPERTIES:
 *     totalAllocated - Total buffers allocated
 *     totalReused - Total buffers reused
 *     currentPoolSize - Current pool size
 *     hitRate - Cache hit rate percentage
 *     memoryUsage - Current memory usage in bytes
 */
public struct BufferPoolStatistics {
    let totalAllocated: Int
    let totalReused: Int
    let currentPoolSize: Int
    let hitRate: Double
    let memoryUsage: UInt64
}

/**
 * NAME: MemoryPressureMonitor
 *
 * DESCRIPTION:
 *     Monitor memory pressure in NetworkExtension environment
 *
 * METHODS:
 *     getCurrentMemoryUsage - Get current memory usage
 *     checkMemoryPressure - Check if memory pressure exists
 *     setMemoryWarningHandler - Set memory warning callback
 */
public protocol MemoryPressureMonitor {
    func getCurrentMemoryUsage() -> UInt64
    func checkMemoryPressure() -> MemoryPressureLevel
    func setMemoryWarningHandler(_ handler: @escaping (MemoryPressureLevel) -> Void)
}

/**
 * NAME: MemoryPressureLevel
 *
 * DESCRIPTION:
 *     Memory pressure levels for NetworkExtension
 *
 * CASES:
 *     normal - Normal memory usage
 *     warning - Approaching memory limit
 *     critical - Critical memory usage, cleanup required
 */
public enum MemoryPressureLevel {
    case normal
    case warning
    case critical
}
```

## 3. 数据结构设计

### 3.1 日志相关数据结构

```swift
// MARK: - 日志配置
public struct LoggingConfiguration {
    let level: LogLevel
    let format: LogFormat
    let outputs: [LogOutput]
    let includeCaller: Bool
    let includeTimestamp: Bool
    let maxFileSize: UInt64
    let maxBackups: Int
    let compressBackups: Bool
}

public enum LogFormat {
    case json
    case text
    case osLog
}

public enum LogOutput {
    case console
    case file(path: String)
    case osLog(subsystem: String, category: String)
    case memory(maxEntries: Int)
}

// MARK: - 日志字段实现
public struct LogFieldImpl: LogField {
    public let key: String
    public let value: Any
    
    public init(key: String, value: Any) {
        self.key = key
        self.value = value
    }
}

// 便利构造方法
extension LogFieldImpl {
    static func string(_ key: String, _ value: String) -> LogField {
        return LogFieldImpl(key: key, value: value)
    }
    
    static func int(_ key: String, _ value: Int) -> LogField {
        return LogFieldImpl(key: key, value: value)
    }
    
    static func double(_ key: String, _ value: Double) -> LogField {
        return LogFieldImpl(key: key, value: value)
    }
    
    static func bool(_ key: String, _ value: Bool) -> LogField {
        return LogFieldImpl(key: key, value: value)
    }
    
    static func error(_ key: String, _ value: Error) -> LogField {
        return LogFieldImpl(key: key, value: value.localizedDescription)
    }
}
```

### 3.2 错误处理数据结构

```swift
// MARK: - 详细错误实现
public struct DetailedErrorImpl: DetailedError {
    public let code: ErrorCode
    public let message: String
    public let details: String?
    public let severity: ErrorSeverity
    public let context: [String: Any]
    public let underlyingError: Error?
    public let timestamp: Date
    
    public init(
        code: ErrorCode,
        message: String,
        details: String? = nil,
        severity: ErrorSeverity = .error,
        context: [String: Any] = [:],
        underlyingError: Error? = nil
    ) {
        self.code = code
        self.message = message
        self.details = details
        self.severity = severity
        self.context = context
        self.underlyingError = underlyingError
        self.timestamp = Date()
    }
    
    public func withContext(_ key: String, value: Any) -> DetailedError {
        var newContext = self.context
        newContext[key] = value
        return DetailedErrorImpl(
            code: code,
            message: message,
            details: details,
            severity: severity,
            context: newContext,
            underlyingError: underlyingError
        )
    }
    
    public func withComponent(_ component: String) -> DetailedError {
        return withContext("component", value: component)
    }
    
    public func withOperation(_ operation: String) -> DetailedError {
        return withContext("operation", value: operation)
    }
}

// MARK: - 错误工厂
public struct ErrorFactory {
    public static func create(
        code: ErrorCode,
        message: String,
        details: String? = nil,
        severity: ErrorSeverity = .error
    ) -> DetailedError {
        return DetailedErrorImpl(
            code: code,
            message: message,
            details: details,
            severity: severity
        )
    }
    
    public static func wrap(
        _ error: Error,
        code: ErrorCode,
        message: String
    ) -> DetailedError {
        return DetailedErrorImpl(
            code: code,
            message: message,
            details: error.localizedDescription,
            underlyingError: error
        )
    }
}
```

### 3.3 性能监控数据结构

```swift
// MARK: - 内存使用信息
public struct MemoryInfo {
    let used: UInt64
    let available: UInt64
    let total: UInt64
    let pressure: MemoryPressure
}

public enum MemoryPressure {
    case normal
    case warning
    case critical
}

// MARK: - CPU使用信息
public struct CPUInfo {
    let usage: Double
    let userTime: Double
    let systemTime: Double
    let idleTime: Double
}

// MARK: - 网络统计信息
public struct NetworkStats {
    let bytesIn: UInt64
    let bytesOut: UInt64
    let packetsIn: UInt64
    let packetsOut: UInt64
    let errors: UInt64
    let drops: UInt64
}
```

### 3.4 数据缓冲相关数据结构

```swift
/**
 * NAME: BufferPoolConfiguration
 *
 * DESCRIPTION:
 *     Configuration for data buffer pool
 *
 * PROPERTIES:
 *     initialSize - Initial pool size
 *     maxSize - Maximum pool size
 *     bufferSize - Standard buffer size
 *     memoryLimit - Memory limit in bytes
 */
public struct BufferPoolConfiguration {
    let initialSize: Int
    let maxSize: Int
    let bufferSize: Int
    let memoryLimit: UInt64

    public static let `default` = BufferPoolConfiguration(
        initialSize: 10,
        maxSize: 50,
        bufferSize: 2048,
        memoryLimit: 5 * 1024 * 1024 // 5MB
    )
}

/**
 * NAME: MemoryUsageInfo
 *
 * DESCRIPTION:
 *     Current memory usage information
 *
 * PROPERTIES:
 *     used - Currently used memory in bytes
 *     available - Available memory in bytes
 *     pressure - Current memory pressure level
 *     timestamp - Measurement timestamp
 */
public struct MemoryUsageInfo {
    let used: UInt64
    let available: UInt64
    let pressure: MemoryPressureLevel
    let timestamp: Date

    public init(used: UInt64, available: UInt64, pressure: MemoryPressureLevel) {
        self.used = used
        self.available = available
        self.pressure = pressure
        self.timestamp = Date()
    }
}
```

## 4. 核心功能设计

### 4.1 日志系统核心功能

**主要功能模块**:

1. **OSLogLogger**: 基于OSLog的高性能日志实现
   - 利用系统原生日志框架
   - 支持结构化日志记录
   - 自动分类和子系统管理

2. **FileLogger**: 文件日志记录器
   - 支持日志轮转和压缩
   - 异步写入避免阻塞
   - 文件大小和数量限制

3. **CompositeLogger**: 组合日志记录器
   - 支持多个输出目标
   - 统一的接口管理
   - 性能优化的批量写入

**业务逻辑流程**:
```
日志请求 → 级别过滤 → 字段序列化 → 输出路由 → 异步写入 → 性能统计
```

### 4.2 错误处理核心功能

**主要功能模块**:

1. **ErrorCodeManager**: 错误码管理
   - 错误码注册和查询
   - 国际化消息模板
   - 错误分类和严重性管理

2. **ErrorReporter**: 错误报告器
   - 错误信息收集和格式化
   - 堆栈跟踪捕获
   - 错误上报和持久化

3. **PanicHandler**: Panic处理器
   - 全局异常捕获
   - 崩溃报告生成
   - 优雅降级处理

**业务逻辑流程**:
```
错误发生 → 错误包装 → 上下文添加 → 严重性评估 → 日志记录 → 错误传播
```

### 4.3 性能监控核心功能

**主要功能模块**:

1. **SystemMetricsCollector**: 系统指标收集器
   - 内存使用监控
   - CPU使用率统计
   - 网络流量统计

2. **ApplicationMetricsCollector**: 应用指标收集器
   - 连接状态监控
   - 数据包处理统计
   - 错误率统计

3. **ThresholdMonitor**: 阈值监控器
   - 性能阈值设置
   - 异常检测和告警
   - 自动优化建议

**业务逻辑流程**:
```
定时收集 → 指标计算 → 阈值检查 → 事件通知 → 数据存储 → 趋势分析
```

### 4.4 数据缓冲和内存监控核心功能

**主要功能模块**:

1. **SimpleDataBufferPool**: 简化数据缓冲池
   - 固定大小数据缓冲区复用
   - 基于ARC的自动内存管理
   - NetworkExtension内存限制优化

2. **MemoryPressureMonitor**: 内存压力监控
   - 实时内存使用监控
   - 内存压力等级检测
   - 自动内存清理触发

3. **SwiftConcurrencyManager**: Swift并发管理（替代GoroutineManager）
   - 基于`async/await`的任务管理
   - `Actor`并发安全保证
   - `Task`生命周期监控

**业务逻辑流程**:
```
缓冲请求 → 池查找 → 缓冲分配/复用 → 内存监控 → 缓冲归还 → 压力检查
```

**Swift并发模式**:
```
任务创建 → Task管理 → Actor安全 → 自动清理
```

## 5. 依赖关系

### 5.1 对其他模块的依赖

Infrastructure Layer 作为基础层，**不依赖任何上层模块**，只依赖系统框架：

- **Foundation**: 基础数据类型和工具
- **OSLog**: 系统日志框架
- **Network**: 网络状态监控
- **Darwin**: 系统调用和性能指标

### 5.2 外部框架依赖

```swift
import Foundation
import OSLog
import Network
import Darwin.Mach
import Darwin.sys.sysctl
```

### 5.3 依赖注入设计

```swift
// MARK: - 依赖注入容器
public protocol DependencyContainer {
    func register<T>(_ type: T.Type, factory: @escaping () -> T)
    func resolve<T>(_ type: T.Type) -> T?
}

// MARK: - Infrastructure依赖注册
public struct InfrastructureDependencies {
    public static func register(container: DependencyContainer) {
        // 注册日志系统
        container.register(Logger.self) {
            return OSLogLogger(
                subsystem: "com.itforce.vpn",
                category: "infrastructure"
            )
        }

        // 注册性能监控
        container.register(PerformanceMonitor.self) {
            return SystemPerformanceMonitor(
                interval: 5.0,
                logger: container.resolve(Logger.self)!
            )
        }

        // 注册数据缓冲池
        container.register(DataBufferPool.self) {
            return SimpleDataBufferPool(
                configuration: .default,
                logger: container.resolve(Logger.self)!
            )
        }

        // 注册内存压力监控
        container.register(MemoryPressureMonitor.self) {
            return SystemMemoryPressureMonitor(
                logger: container.resolve(Logger.self)!
            )
        }
    }
}
```

## 6. 错误处理

### 6.1 错误类型定义

```swift
// MARK: - Infrastructure特定错误
public enum InfrastructureError: Error {
    case loggerInitializationFailed(String)
    case performanceMonitorStartFailed(String)
    case memoryPoolCreationFailed(String)
    case configurationInvalid(String)
    case resourceUnavailable(String)
}

// MARK: - 错误处理策略
public struct ErrorHandlingStrategy {
    public static func handle(_ error: Error, logger: Logger) {
        switch error {
        case let infraError as InfrastructureError:
            handleInfrastructureError(infraError, logger: logger)
        case let detailedError as DetailedError:
            handleDetailedError(detailedError, logger: logger)
        default:
            handleGenericError(error, logger: logger)
        }
    }

    private static func handleInfrastructureError(
        _ error: InfrastructureError,
        logger: Logger
    ) {
        logger.error("Infrastructure error occurred", fields: [
            .string("error_type", String(describing: error)),
            .string("error_description", error.localizedDescription)
        ])
    }
}
```

### 6.2 异常恢复机制

```swift
// MARK: - 恢复策略
public enum RecoveryStrategy {
    case retry(maxAttempts: Int, delay: TimeInterval)
    case fallback(alternative: () -> Void)
    case gracefulDegradation
    case fail
}

// MARK: - 异常恢复管理器
public class RecoveryManager {
    public static func executeWithRecovery<T>(
        operation: () throws -> T,
        strategy: RecoveryStrategy,
        logger: Logger
    ) -> Result<T, Error> {
        switch strategy {
        case .retry(let maxAttempts, let delay):
            return executeWithRetry(
                operation: operation,
                maxAttempts: maxAttempts,
                delay: delay,
                logger: logger
            )
        case .fallback(let alternative):
            return executeWithFallback(
                operation: operation,
                fallback: alternative,
                logger: logger
            )
        case .gracefulDegradation:
            return executeWithDegradation(operation: operation, logger: logger)
        case .fail:
            return executeWithFail(operation: operation, logger: logger)
        }
    }
}
```

## 7. 性能考虑

### 7.1 性能优化点

**日志系统优化**:
- **异步写入**: 使用后台队列进行日志写入，避免阻塞主线程
- **批量处理**: 批量写入多条日志，减少I/O操作
- **内存缓冲**: 使用内存缓冲区，定期刷新到磁盘
- **级别过滤**: 在序列化前进行级别过滤，减少不必要的处理

**错误处理优化**:
- **错误池化**: 复用错误对象，减少内存分配
- **延迟堆栈捕获**: 只在需要时捕获详细堆栈信息
- **上下文缓存**: 缓存常用的上下文信息

**性能监控优化**:
- **采样策略**: 使用智能采样减少监控开销
- **增量计算**: 只计算变化的指标，避免重复计算
- **阈值预检**: 快速阈值检查，减少复杂计算

### 7.2 内存管理策略

**NetworkExtension内存限制应对**:
```swift
// MARK: - 内存压力监控
public class MemoryPressureMonitor {
    private let threshold: UInt64 = 40 * 1024 * 1024 // 40MB
    private let criticalThreshold: UInt64 = 45 * 1024 * 1024 // 45MB

    public func checkMemoryPressure() -> MemoryPressure {
        let usage = getCurrentMemoryUsage()

        if usage > criticalThreshold {
            return .critical
        } else if usage > threshold {
            return .warning
        } else {
            return .normal
        }
    }

    public func handleMemoryPressure(_ pressure: MemoryPressure) {
        switch pressure {
        case .warning:
            // 清理缓存，减少池大小
            MemoryManager.shared.shrinkPools()
        case .critical:
            // 激进清理，只保留必要对象
            MemoryManager.shared.emergencyCleanup()
        case .normal:
            break
        }
    }
}
```

**对象池优化策略**:
- **动态调整**: 根据使用情况动态调整池大小
- **预分配**: 启动时预分配常用对象
- **生命周期管理**: 及时释放不再使用的对象
- **内存对齐**: 优化内存布局，提高缓存命中率

### 7.3 并发处理设计

**Actor模式应用**:
```swift
// MARK: - 线程安全的性能监控器
@available(iOS 13.0, macOS 10.15, *)
public actor PerformanceMonitorActor {
    private var metrics: PerformanceMetrics
    private var collectors: [MetricCollector] = []

    public func updateMetrics(_ newMetrics: PerformanceMetrics) {
        self.metrics = newMetrics
    }

    public func addCollector(_ collector: MetricCollector) {
        collectors.append(collector)
    }

    public func getCurrentMetrics() -> PerformanceMetrics {
        return metrics
    }
}
```

**并发队列设计**:
- **串行队列**: 用于状态修改操作
- **并发队列**: 用于只读操作和独立任务
- **优先级队列**: 根据任务重要性分配优先级
- **背压控制**: 防止队列过载

## 8. 测试策略

### 8.1 单元测试设计

**日志系统测试**:
```swift
class LoggerTests: XCTestCase {
    func testLogLevelFiltering() {
        let logger = createTestLogger(level: .warning)
        let mockOutput = MockLogOutput()

        logger.debug("Debug message")
        logger.warning("Warning message")

        XCTAssertEqual(mockOutput.messages.count, 1)
        XCTAssertTrue(mockOutput.messages[0].contains("Warning message"))
    }

    func testStructuredLogging() {
        let logger = createTestLogger()
        let mockOutput = MockLogOutput()

        logger.info("Test message", fields: [
            .string("key1", "value1"),
            .int("key2", 42)
        ])

        let logData = mockOutput.lastMessage
        XCTAssertTrue(logData.contains("key1"))
        XCTAssertTrue(logData.contains("value1"))
    }
}
```

**错误处理测试**:
```swift
class ErrorHandlingTests: XCTestCase {
    func testErrorCodeMapping() {
        let error = ErrorFactory.create(
            code: .networkTimeout,
            message: "Connection timed out"
        )

        XCTAssertEqual(error.code, .networkTimeout)
        XCTAssertEqual(error.severity, .error)
    }

    func testErrorWrapping() {
        let originalError = NSError(domain: "test", code: 1, userInfo: nil)
        let wrappedError = ErrorFactory.wrap(
            originalError,
            code: .networkTimeout,
            message: "Network operation failed"
        )

        XCTAssertEqual(wrappedError.underlyingError as? NSError, originalError)
    }
}
```

### 8.2 集成测试要点

**性能监控集成测试**:
- 验证指标收集的准确性
- 测试阈值监控和告警机制
- 验证内存压力下的行为

**内存管理集成测试**:
- 测试对象池的创建和销毁
- 验证内存泄漏检测
- 测试极限内存使用情况

### 8.3 Mock对象设计

```swift
// MARK: - Mock日志输出
public class MockLogOutput: LogOutput {
    public var messages: [String] = []
    public var lastMessage: String { messages.last ?? "" }

    public func write(_ message: String) {
        messages.append(message)
    }

    public func clear() {
        messages.removeAll()
    }
}

// MARK: - Mock性能收集器
public class MockMetricCollector: MetricCollector {
    public let name: String = "mock"
    public var mockMetrics: [String: Any] = [:]

    public func collect() -> [String: Any] {
        return mockMetrics
    }
}
```

## 9. 实施计划

### 9.1 开发优先级

1. **第一阶段**: 日志系统和错误处理 (1周)
   - 实现基于OSLog的日志接口
   - 完成Swift错误类型定义和处理机制

2. **第二阶段**: 性能监控系统 (1周)
   - 实现系统指标收集
   - 完成NetworkExtension内存监控和告警

3. **第三阶段**: 数据缓冲和并发管理 (1周)
   - 实现简化的数据缓冲池（可选）
   - 完成内存压力监控
   - 验证Swift原生并发机制集成

**组件优先级**:
- 🔴 **必需组件**: LoggingSystem, ErrorHandling, PerformanceMonitor
- 🟡 **可选组件**: DataBufferPool（根据性能测试结果决定）
- ❌ **移除组件**: GoroutineManager（使用Swift原生并发）

### 9.2 验收标准

**功能验收**:
- ✅ 所有接口定义完整且符合Swift最佳实践
- ✅ 与Go后端功能对等，接口兼容
- ✅ 性能测试通过，满足NetworkExtension内存限制

**质量验收**:
- ✅ 单元测试覆盖率达到90%以上
- ✅ 所有注释符合 `docs/ios-macos-design-guidelines.md` 规范
- ✅ AUTHOR字段统一为 `wei`，HISTORY字段为 `23/06/2025`
- ✅ 遵循Clean Architecture原则，职责单一
- ✅ 应用DRY原则，消除重复代码
- ✅ 代码清理完成，无未使用的导入和变量

**规范验收**:
- ✅ 文件头注释符合模板格式
- ✅ 协议注释包含NAME、DESCRIPTION、METHODS
- ✅ 结构体/类注释包含NAME、DESCRIPTION、PROPERTIES
- ✅ 函数注释包含NAME、DESCRIPTION、PARAMETERS、RETURNS
- ✅ 英文注释，符合国际化标准

## 10. 规范遵循声明

本设计文档严格遵循以下规范：

1. **`docs/go-backend-optimization-plan.md`** - 借鉴Go后端优化的成功经验
2. **`docs/ios-macos-design-guidelines.md`** - 遵循iOS/macOS设计指导规范
3. **Swift API Design Guidelines** - 符合Apple官方Swift设计指南
4. **Clean Architecture原则** - 确保代码结构清晰、职责单一
5. **DRY原则** - 避免重复代码，提取公共组件

---

**文档状态**: ✅ 完成 (v1.2 - Swift特性优化)
**AUTHOR**: wei
**HISTORY**: 23/06/2025 create, 23/06/2025 update with design guidelines compliance, 23/06/2025 optimize for Swift concurrency model
**下一步**: 等待Review确认，然后开始Protocol Layer设计

## 📋 v1.2 更新摘要

**主要变更**:
- ❌ **移除GoroutineManager**: 使用Swift原生`async/await`、`Task`、`Actor`并发机制
- 🔄 **简化MemoryManager**: 重构为`DataBufferPool`和`MemoryPressureMonitor`，专注NetworkExtension需求
- ✅ **保留核心组件**: LoggingSystem, ErrorHandling, PerformanceMonitor
- 🎯 **Swift特性优化**: 充分利用ARC、Actor模式、async/await等Swift特性

**设计理念**:
- 遵循Swift最佳实践，避免过度设计
- 专注NetworkExtension实际需求
- 利用系统提供的并发和内存管理机制
- 保持与Go后端功能对等的前提下，优化Swift实现

## 📊 ResourceMonitor 设计 (从Service Layer移入)

### ResourceMonitor接口设计

```swift
/**
 * NAME: ResourceMonitor
 *
 * DESCRIPTION:
 *     System resource monitoring for NetworkExtension environment
 *     Moved from Service Layer to Infrastructure Layer for better architecture
 *
 * METHODS:
 *     startMonitoring - Start resource monitoring
 *     stopMonitoring - Stop resource monitoring
 *     getCurrentMemoryUsage - Get current memory usage
 *     getCPUUsage - Get current CPU usage
 *     checkResourceLimits - Check if approaching resource limits
 *     setThresholds - Set resource warning thresholds
 */
public protocol ResourceMonitor {
    func startMonitoring() async throws
    func stopMonitoring() async
    func getCurrentMemoryUsage() async -> UInt64
    func getCPUUsage() async -> Double
    func checkResourceLimits() async -> ResourceStatus
    func setThresholds(_ thresholds: ResourceThresholds) async

    // Event callbacks
    var onResourceWarning: ((ResourceWarning) -> Void)? { get set }
    var onResourceCritical: ((ResourceCritical) -> Void)? { get set }
}

/**
 * NAME: ResourceStatus
 *
 * DESCRIPTION:
 *     Current resource usage status
 */
public struct ResourceStatus {
    let memoryUsage: UInt64
    let memoryLimit: UInt64
    let cpuUsage: Double
    let isMemoryWarning: Bool
    let isCPUWarning: Bool
    let timestamp: Date
}

/**
 * NAME: ResourceThresholds
 *
 * DESCRIPTION:
 *     Resource warning thresholds for NetworkExtension
 */
public struct ResourceThresholds {
    let memoryWarningThreshold: UInt64  // 30MB
    let memoryCriticalThreshold: UInt64 // 40MB
    let cpuWarningThreshold: Double     // 80%
    let cpuCriticalThreshold: Double    // 95%
}

/**
 * NAME: ResourceWarning
 *
 * DESCRIPTION:
 *     Resource warning event data
 */
public struct ResourceWarning {
    let type: ResourceType
    let currentValue: Double
    let threshold: Double
    let timestamp: Date
}

/**
 * NAME: ResourceCritical
 *
 * DESCRIPTION:
 *     Critical resource event data
 */
public struct ResourceCritical {
    let type: ResourceType
    let currentValue: Double
    let threshold: Double
    let recommendedAction: String
    let timestamp: Date
}

public enum ResourceType {
    case memory
    case cpu
    case networkBandwidth
}
```

### 简化任务管理接口

```swift
/**
 * NAME: SimpleTaskManager
 *
 * DESCRIPTION:
 *     Simplified task management using Swift native features
 *     Replaces complex background task scheduler from Service Layer
 */
public protocol SimpleTaskManager {
    func schedulePeriodicTask(
        _ task: @escaping () async -> Void,
        interval: TimeInterval,
        identifier: String
    ) async
    func cancelTask(identifier: String) async
    func cancelAllTasks() async
    func getActiveTasks() async -> [String]
}

/**
 * NAME: SimpleTaskManagerImpl
 *
 * DESCRIPTION:
 *     Implementation using Timer and Task
 */
public final class SimpleTaskManagerImpl: SimpleTaskManager {
    private var activeTasks: [String: Timer] = [:]
    private let queue = DispatchQueue(label: "task_manager", qos: .utility)

    public func schedulePeriodicTask(
        _ task: @escaping () async -> Void,
        interval: TimeInterval,
        identifier: String
    ) async {
        await cancelTask(identifier: identifier)

        let timer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { _ in
            Task {
                await task()
            }
        }

        activeTasks[identifier] = timer
    }

    public func cancelTask(identifier: String) async {
        activeTasks[identifier]?.invalidate()
        activeTasks.removeValue(forKey: identifier)
    }

    public func cancelAllTasks() async {
        for timer in activeTasks.values {
            timer.invalidate()
        }
        activeTasks.removeAll()
    }

    public func getActiveTasks() async -> [String] {
        return Array(activeTasks.keys)
    }
}
```

**移入理由**:
- 资源监控属于基础设施功能，应该在Infrastructure Layer
- 简化的任务管理更适合作为基础工具
- 减少Service Layer的复杂度，专注于Platform Channel桥接
