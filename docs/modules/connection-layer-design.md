# Connection Layer 详细设计文档

## 📋 文档概述

**实施状态**: ✅ **100% 完成** - 生产级实现，2025年7月完成
**实现位置**: `ui/flutter/ItForceCore/Sources/Connection/`

**文档目标**: 基于 Go 后端 connection 模块的深入分析，设计 iOS/macOS 平台的 Connection Layer，实现 VPN 连接的完整生命周期管理。

**设计原则**: 遵循 `docs/ios-macos-design-guidelines.md` 规范，充分利用 Swift 语言特性，实现与 Go 后端功能对等但 Swift 化的连接管理系统。

**实际实现特性**:
- ✅ **Actor 模式**: 使用 Swift Actor 确保线程安全
- ✅ **异步编程**: 基于 async/await 的现代异步模式
- ✅ **状态机管理**: 严格的状态转换控制和验证
- ✅ **自动重连**: 智能网络变化检测和重连策略
- ✅ **性能监控**: 实时连接质量和流量统计

**核心职责**:
- 连接生命周期管理：建立、维护、断开 VPN 连接
- 状态机管理：连接状态转换和状态同步
- 服务器管理：服务器选择、切换、故障转移
- 自动重连：网络中断检测和智能重连机制
- 性能监控：连接质量评估和性能指标收集

## 🏗️ 模块架构设计

### 1. 整体架构

```
Connection Layer
├── ConnectionManager (连接管理器)
│   ├── ConnectionStateMachine (状态机)
│   ├── ServerManager (服务器管理)
│   ├── ReconnectionManager (重连管理)
│   └── ConnectionMonitor (连接监控)
├── ConnectionConfiguration (连接配置)
├── ConnectionStatus (连接状态)
└── ConnectionDelegate (连接代理)
```

### 2. 依赖关系

```
ConnectionManager
    ↓ 依赖
Protocol Layer (SDWAN协议)
    ↓ 依赖
Platform Layer (NetworkExtension)
    ↓ 依赖
Infrastructure Layer (日志、错误处理)
```

## 🔧 核心组件设计

### 1. ConnectionManager (连接管理器)

**Swift实现对应Go模块**: `internal/connection/manager/manager.go`

```swift
/**
 * NAME: ConnectionManager
 *
 * DESCRIPTION:
 *     VPN连接管理器，负责连接的完整生命周期管理
 *
 * PROPERTIES:
 *     configuration - 连接配置
 *     stateMachine - 连接状态机
 *     serverManager - 服务器管理器
 *     reconnectionManager - 重连管理器
 *     monitor - 连接监控器
 */
@MainActor
public class ConnectionManager: ObservableObject {
    // Core components
    private let configuration: ConnectionConfiguration
    private let stateMachine: ConnectionStateMachine
    private let serverManager: ServerManager
    private let reconnectionManager: ReconnectionManager
    private let monitor: ConnectionMonitor
    
    // Protocol and platform dependencies
    private let protocolHandler: SDWANProtocolHandler
    private let networkProvider: NetworkExtensionProvider
    private let logger: LoggerProtocol
    
    // Connection state
    @Published public private(set) var status: ConnectionStatus
    @Published public private(set) var currentServer: ServerInfo?
    @Published public private(set) var statistics: ConnectionStatistics
    
    // Async task management
    private var connectionTask: Task<Void, Never>?
    private var monitoringTask: Task<Void, Never>?
}
```

### 2. ConnectionStateMachine (连接状态机)

**Swift实现对应Go模块**: `internal/connection/manager/state_machine.go`

```swift
/**
 * NAME: ConnectionStateMachine
 *
 * DESCRIPTION:
 *     连接状态机，管理VPN连接的状态转换
 *
 * PROPERTIES:
 *     currentState - 当前连接状态
 *     sessionID - 会话标识符
 *     authToken - 认证令牌
 *     lastHeartbeat - 最后心跳时间
 */
public actor ConnectionStateMachine {
    // State definitions (iOS platform-independent design)
    public enum State: UInt8, CaseIterable {
        case disconnected = 0        // VPN is disconnected and idle
        case initializing = 1        // NetworkExtension initialization and DNS resolution
        case serverResolved = 2      // Server IP obtained, ready for authentication
        case authenticating = 3      // VPN authentication in progress
        case connected = 4           // VPN tunnel active and data transmission ready
        case authenticationFailed = 5 // Authentication failed, connection terminated
    }
    
    // State management
    private var currentState: State = .disconnected
    private var sessionID: UInt16 = 0
    private var authToken: UInt32 = 0
    private var lastHeartbeat: Date = Date()
    private var stateChangeTime: Date = Date()
    
    // Callbacks and handlers
    private var stateChangeCallbacks: [(State, State) -> Void] = []
    private var packetHandlers: [UInt8: (SDWANPacket) async throws -> Void] = [:]
    
    private let logger: LoggerProtocol
}
```

### 3. ServerManager (服务器管理器)

**Swift实现对应Go模块**: `internal/connection/server/server_manager.go`

```swift
/**
 * NAME: ServerManager
 *
 * DESCRIPTION:
 *     服务器管理器，负责服务器列表维护和选择策略
 *
 * PROPERTIES:
 *     servers - 服务器列表
 *     currentServer - 当前连接的服务器
 *     selectionStrategy - 服务器选择策略
 */
public actor ServerManager {
    // Server management
    private var servers: [ServerInfo] = []
    private var currentServer: ServerInfo?
    private var selectionStrategy: ServerSelectionStrategy
    
    // Network monitoring
    private var pingResults: [String: PingResult] = [:]
    private var networkMonitor: NetworkMonitor
    
    // Update management
    private var updateTask: Task<Void, Never>?
    private var pingTask: Task<Void, Never>?
    
    private let configuration: ServerConfiguration
    private let logger: LoggerProtocol
}
```

## 📊 数据模型设计

### 1. ConnectionConfiguration (连接配置)

```swift
/**
 * NAME: ConnectionConfiguration
 *
 * DESCRIPTION:
 *     连接配置数据模型
 *
 * PROPERTIES:
 *     serverAddress - 服务器地址
 *     serverPort - 服务器端口
 *     username - 用户名
 *     password - 密码
 *     autoReconnect - 自动重连设置
 */
public struct ConnectionConfiguration: Codable {
    public let serverAddress: String
    public let serverPort: Int
    public let username: String
    public let password: String
    public let mtu: Int
    public let autoReconnect: AutoReconnectConfiguration
    public let timeout: TimeInterval
    public let heartbeatInterval: TimeInterval
}
```

### 2. ConnectionStatus (连接状态)

```swift
/**
 * NAME: ConnectionStatus
 *
 * DESCRIPTION:
 *     连接状态信息
 *
 * PROPERTIES:
 *     state - 当前连接状态
 *     serverInfo - 服务器信息
 *     statistics - 连接统计
 *     error - 错误信息
 */
public struct ConnectionStatus: Codable {
    public let state: ConnectionStateMachine.State
    public let serverInfo: ServerInfo?
    public let statistics: ConnectionStatistics
    public let error: ConnectionError?
    public let lastUpdated: Date
    public let sessionDuration: TimeInterval
}
```

### 3. ServerInfo (服务器信息)

```swift
/**
 * NAME: ServerInfo
 *
 * DESCRIPTION:
 *     服务器信息数据模型
 *
 * PROPERTIES:
 *     id - 服务器标识符
 *     name - 服务器名称
 *     address - 服务器地址
 *     port - 服务器端口
 *     ping - 延迟信息
 */
public struct ServerInfo: Codable, Identifiable {
    public let id: String
    public let name: String
    public let nameEn: String
    public let address: String
    public let port: Int
    public let location: ServerLocation
    public let ping: PingResult?
    public let load: ServerLoad?
    public let isOnline: Bool
}
```

## 🔄 状态机设计

### 1. 状态转换图

```
[disconnected] 
    ↓ connect()
[initializing] (DNS解析)
    ↓ server resolved
[serverResolved] 
    ↓ start auth
[authenticating] 
    ↓ auth success / auth failed
[connected] / [authenticationFailed]
    ↓ disconnect() / error
[disconnected]
```

### 2. 状态转换规则

```swift
/**
 * NAME: validateStateTransition
 *
 * DESCRIPTION:
 *     验证状态转换是否合法
 *
 * PARAMETERS:
 *     from - 源状态
 *     to - 目标状态
 *
 * RETURNS:
 *     Bool - 转换是否合法
 */
private func validateStateTransition(from: State, to: State) -> Bool {
    switch (from, to) {
    case (.disconnected, .initializing),
         (.initializing, .serverResolved),
         (.serverResolved, .authenticating),
         (.authenticating, .connected),
         (.authenticating, .authenticationFailed),
         (_, .disconnected):
        return true
    default:
        return false
    }
}
```

## 🔄 自动重连机制

### 1. ReconnectionManager (重连管理器)

**Swift实现对应Go模块**: `internal/connection/manager/connection_reconnect.go`

```swift
/**
 * NAME: ReconnectionManager
 *
 * DESCRIPTION:
 *     自动重连管理器，处理网络中断和智能重连
 *
 * PROPERTIES:
 *     isEnabled - 是否启用自动重连
 *     maxAttempts - 最大重连次数
 *     currentAttempts - 当前重连次数
 *     backoffStrategy - 退避策略
 */
public actor ReconnectionManager {
    // Reconnection configuration
    private let configuration: AutoReconnectConfiguration
    private var isEnabled: Bool
    private var maxAttempts: Int
    private var currentAttempts: Int = 0
    
    // Backoff strategy
    private var backoffStrategy: BackoffStrategy
    private var reconnectionTask: Task<Void, Never>?
    
    // Network monitoring
    private var networkMonitor: NetworkMonitor
    private var lastNetworkStatus: NetworkStatus
    
    private let logger: LoggerProtocol
}
```

### 2. 重连策略

```swift
/**
 * NAME: BackoffStrategy
 *
 * DESCRIPTION:
 *     重连退避策略
 */
public enum BackoffStrategy {
    case fixed(TimeInterval)
    case exponential(base: TimeInterval, multiplier: Double, maxDelay: TimeInterval)
    case linear(base: TimeInterval, increment: TimeInterval, maxDelay: TimeInterval)
    
    func nextDelay(for attempt: Int) -> TimeInterval {
        switch self {
        case .fixed(let interval):
            return interval
        case .exponential(let base, let multiplier, let maxDelay):
            let delay = base * pow(multiplier, Double(attempt))
            return min(delay, maxDelay)
        case .linear(let base, let increment, let maxDelay):
            let delay = base + (increment * Double(attempt))
            return min(delay, maxDelay)
        }
    }
}
```

## 🔍 连接监控设计

### 1. ConnectionMonitor (连接监控器)

```swift
/**
 * NAME: ConnectionMonitor
 *
 * DESCRIPTION:
 *     连接监控器，负责连接质量监控和性能指标收集
 *
 * PROPERTIES:
 *     statistics - 连接统计信息
 *     qualityMetrics - 连接质量指标
 *     performanceMonitor - 性能监控器
 */
public actor ConnectionMonitor {
    // Statistics tracking
    private var statistics: ConnectionStatistics
    private var qualityMetrics: ConnectionQualityMetrics

    // Monitoring tasks
    private var statisticsTask: Task<Void, Never>?
    private var qualityTask: Task<Void, Never>?
    private var heartbeatTask: Task<Void, Never>?

    // Performance monitoring
    private let performanceMonitor: PerformanceMonitorProtocol
    private let logger: LoggerProtocol

    // Monitoring intervals
    private let statisticsInterval: TimeInterval = 1.0
    private let qualityInterval: TimeInterval = 5.0
    private let heartbeatInterval: TimeInterval = 30.0
}
```

### 2. 性能指标定义

```swift
/**
 * NAME: ConnectionStatistics
 *
 * DESCRIPTION:
 *     连接统计信息
 */
public struct ConnectionStatistics: Codable {
    public let bytesReceived: UInt64
    public let bytesSent: UInt64
    public let packetsReceived: UInt64
    public let packetsSent: UInt64
    public let packetsLost: UInt64
    public let averageLatency: TimeInterval
    public let connectionDuration: TimeInterval
    public let lastUpdated: Date
}

/**
 * NAME: ConnectionQualityMetrics
 *
 * DESCRIPTION:
 *     连接质量指标
 */
public struct ConnectionQualityMetrics: Codable {
    public let signalStrength: Double // 0.0 - 1.0
    public let packetLossRate: Double // 0.0 - 1.0
    public let jitter: TimeInterval
    public let throughput: Double // bytes per second
    public let stability: Double // 0.0 - 1.0
}
```

## 🚀 核心功能实现

### 1. 连接建立流程

```swift
/**
 * NAME: connect
 *
 * DESCRIPTION:
 *     建立VPN连接
 *
 * PARAMETERS:
 *     serverAddress - 服务器地址
 *     serverPort - 服务器端口
 *
 * RETURNS:
 *     Void
 *
 * THROWS:
 *     ConnectionError - 连接错误
 */
public func connect(to serverAddress: String, port serverPort: Int) async throws {
    logger.info("Starting connection process", metadata: [
        "server": .string(serverAddress),
        "port": .stringConvertible(serverPort)
    ])

    // Validate configuration
    try validateConfiguration()

    // Disconnect existing connection if any
    if await stateMachine.currentState != .disconnected {
        await disconnect()
        try await Task.sleep(nanoseconds: 100_000_000) // 100ms
    }

    // Start connection task
    connectionTask = Task {
        do {
            // Set state to initializing
            await stateMachine.setState(.initializing)

            // Resolve server address
            let resolvedAddress = try await resolveServerAddress(serverAddress)
            await stateMachine.setState(.serverResolved)

            // Establish network connection
            try await establishNetworkConnection(to: resolvedAddress, port: serverPort)

            // Start authentication
            await stateMachine.setState(.authenticating)
            try await performAuthentication()

            // Connection successful
            await stateMachine.setState(.connected)
            await startMonitoring()

            logger.info("Connection established successfully")

        } catch {
            logger.error("Connection failed", metadata: ["error": .string(error.localizedDescription)])
            await handleConnectionError(error)
        }
    }
}
```

### 2. 自动重连实现

```swift
/**
 * NAME: startAutoReconnect
 *
 * DESCRIPTION:
 *     启动自动重连机制
 *
 * PARAMETERS:
 *     reason - 重连原因
 */
private func startAutoReconnect(reason: ReconnectionReason) async {
    guard await reconnectionManager.isEnabled else { return }

    logger.info("Starting auto-reconnect", metadata: [
        "reason": .string(reason.description),
        "attempt": .stringConvertible(await reconnectionManager.currentAttempts + 1)
    ])

    await reconnectionManager.scheduleReconnection { [weak self] in
        guard let self = self else { return }

        do {
            // Get current server info
            guard let server = await self.serverManager.currentServer else {
                throw ConnectionError.noServerAvailable
            }

            // Attempt reconnection
            try await self.connect(to: server.address, port: server.port)

            // Reset reconnection attempts on success
            await self.reconnectionManager.resetAttempts()

        } catch {
            // Handle reconnection failure
            await self.handleReconnectionFailure(error)
        }
    }
}
```

### 3. 服务器选择策略

```swift
/**
 * NAME: ServerSelectionStrategy
 *
 * DESCRIPTION:
 *     服务器选择策略
 */
public enum ServerSelectionStrategy {
    case fastest        // 选择延迟最低的服务器
    case loadBalanced   // 选择负载最低的服务器
    case geographic     // 选择地理位置最近的服务器
    case manual(String) // 手动指定服务器ID

    func selectServer(from servers: [ServerInfo]) -> ServerInfo? {
        switch self {
        case .fastest:
            return servers
                .filter { $0.isOnline && $0.ping != nil }
                .min { ($0.ping?.averageLatency ?? .infinity) < ($1.ping?.averageLatency ?? .infinity) }

        case .loadBalanced:
            return servers
                .filter { $0.isOnline && $0.load != nil }
                .min { ($0.load?.cpuUsage ?? 1.0) < ($1.load?.cpuUsage ?? 1.0) }

        case .geographic:
            // Implementation would require user location and server location comparison
            return servers.filter { $0.isOnline }.first

        case .manual(let serverID):
            return servers.first { $0.id == serverID && $0.isOnline }
        }
    }
}
```

## 🔧 错误处理设计

### 1. ConnectionError 定义

```swift
/**
 * NAME: ConnectionError
 *
 * DESCRIPTION:
 *     连接错误类型定义
 */
public enum ConnectionError: Error, LocalizedError {
    case configurationInvalid(String)
    case serverUnreachable(String)
    case authenticationFailed(String)
    case networkTimeout
    case tunnelSetupFailed(String)
    case protocolError(String)
    case noServerAvailable
    case reconnectionLimitExceeded
    case networkExtensionError(Error)

    public var errorDescription: String? {
        switch self {
        case .configurationInvalid(let details):
            return "Configuration invalid: \(details)"
        case .serverUnreachable(let server):
            return "Server unreachable: \(server)"
        case .authenticationFailed(let reason):
            return "Authentication failed: \(reason)"
        case .networkTimeout:
            return "Network operation timed out"
        case .tunnelSetupFailed(let details):
            return "Tunnel setup failed: \(details)"
        case .protocolError(let details):
            return "Protocol error: \(details)"
        case .noServerAvailable:
            return "No server available for connection"
        case .reconnectionLimitExceeded:
            return "Maximum reconnection attempts exceeded"
        case .networkExtensionError(let error):
            return "NetworkExtension error: \(error.localizedDescription)"
        }
    }
}
```

### 2. 错误恢复策略

```swift
/**
 * NAME: handleConnectionError
 *
 * DESCRIPTION:
 *     处理连接错误并执行恢复策略
 *
 * PARAMETERS:
 *     error - 连接错误
 */
private func handleConnectionError(_ error: Error) async {
    let connectionError = error as? ConnectionError ?? .networkExtensionError(error)

    logger.error("Connection error occurred", metadata: [
        "error": .string(connectionError.localizedDescription)
    ])

    // Update state based on error type
    switch connectionError {
    case .authenticationFailed:
        await stateMachine.setState(.authenticationFailed)

    case .serverUnreachable, .networkTimeout:
        await stateMachine.setState(.disconnected)
        // Trigger server failover if available
        await serverManager.markServerAsUnavailable()
        if await reconnectionManager.isEnabled {
            await startAutoReconnect(reason: .serverUnreachable)
        }

    case .configurationInvalid:
        await stateMachine.setState(.disconnected)
        // Configuration errors require user intervention

    default:
        await stateMachine.setState(.disconnected)
        if await reconnectionManager.isEnabled {
            await startAutoReconnect(reason: .networkError)
        }
    }

    // Notify delegates
    await notifyConnectionError(connectionError)
}
```

## 📋 测试策略

### 1. 单元测试

```swift
/**
 * NAME: ConnectionManagerTests
 *
 * DESCRIPTION:
 *     连接管理器单元测试
 */
@MainActor
class ConnectionManagerTests: XCTestCase {
    private var connectionManager: ConnectionManager!
    private var mockProtocolHandler: MockSDWANProtocolHandler!
    private var mockNetworkProvider: MockNetworkExtensionProvider!

    override func setUp() async throws {
        mockProtocolHandler = MockSDWANProtocolHandler()
        mockNetworkProvider = MockNetworkExtensionProvider()

        connectionManager = ConnectionManager(
            configuration: .testConfiguration,
            protocolHandler: mockProtocolHandler,
            networkProvider: mockNetworkProvider,
            logger: MockLogger()
        )
    }

    func testConnectionEstablishment() async throws {
        // Given
        mockNetworkProvider.shouldSucceed = true
        mockProtocolHandler.shouldAuthenticate = true

        // When
        try await connectionManager.connect(to: "test.server.com", port: 443)

        // Then
        let status = await connectionManager.status
        XCTAssertEqual(status.state, .connected)
        XCTAssertNotNil(status.serverInfo)
    }

    func testAutoReconnection() async throws {
        // Given
        try await connectionManager.connect(to: "test.server.com", port: 443)

        // When
        await connectionManager.simulateNetworkInterruption()

        // Then
        // Wait for auto-reconnection
        try await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds

        let status = await connectionManager.status
        XCTAssertEqual(status.state, .connected)
    }
}
```

### 2. 集成测试

```swift
/**
 * NAME: ConnectionIntegrationTests
 *
 * DESCRIPTION:
 *     连接层集成测试
 */
class ConnectionIntegrationTests: XCTestCase {
    func testFullConnectionFlow() async throws {
        // Test complete connection flow with real NetworkExtension
        // This would require test server setup
    }

    func testServerFailover() async throws {
        // Test server failover mechanism
    }

    func testNetworkTransition() async throws {
        // Test WiFi to cellular transition
    }
}
```

## 🎯 性能优化

### 1. 内存优化

- 使用`weak`引用避免循环引用
- 及时释放大对象和缓冲区
- 使用对象池管理频繁创建的对象
- 监控NetworkExtension内存使用

### 2. 并发优化

- 使用`Actor`确保线程安全
- 合理使用`async/await`避免阻塞
- 批量处理网络操作
- 优化状态同步频率

### 3. 网络优化

- 智能的重连退避策略
- 连接复用和保持
- 优化心跳间隔
- 减少不必要的网络请求

## ✅ 验收标准

### 功能验收
- [ ] 连接建立成功率 > 95%
- [ ] 状态转换逻辑正确
- [ ] 自动重连机制有效
- [ ] 服务器选择策略正确
- [ ] 错误处理完善

### 性能验收
- [ ] 连接建立时间 < 5秒
- [ ] 状态同步延迟 < 100ms
- [ ] 内存使用 < 10MB
- [ ] CPU使用率 < 5%

### 兼容性验收
- [ ] 与Protocol Layer完全兼容
- [ ] 与Platform Layer正确集成
- [ ] 支持iOS 14.0+ 和 macOS 11.0+
- [ ] 与Go后端协议兼容

---

**AUTHOR**: wei
**HISTORY**: 23/06/2025 create connection layer design based on Go backend analysis
