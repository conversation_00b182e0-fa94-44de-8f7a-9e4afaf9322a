# Platform Layer 详细设计文档

## 📋 模块概述

**模块名称**: Platform Layer (平台层)  
**对应Go模块**: `internal/platform/`  
**设计版本**: v1.0  
**AUTHOR**: wei  
**HISTORY**: 23/06/2025 create platform layer design for iOS/macOS NetworkExtension integration

### 主要职责

Platform Layer是iOS/macOS平台的核心抽象层，负责与系统NetworkExtension框架的深度集成，提供跨平台的网络管理能力。

**核心功能**:
- **NetworkExtension Provider**: PacketTunnelProvider实现，处理系统VPN请求
- **TUN设备管理**: 虚拟网络接口配置和数据包收发
- **路由管理**: 多平台路由策略实现，支持iOS/macOS差异化处理  
- **DNS配置**: DNS服务器设置和域名解析管理
- **网络状态监控**: 实时监控网络连接状态和质量

### 设计目标

1. **平台抽象**: 为上层提供统一的平台接口，屏蔽iOS/macOS差异
2. **NetworkExtension集成**: 深度集成系统VPN框架，确保稳定运行
3. **性能优化**: 高效的数据包处理和路由管理
4. **资源管理**: 优化NetworkExtension环境下的内存和CPU使用
5. **错误恢复**: 完善的错误检测和自动恢复机制

## 🏗️ 架构设计

### 模块依赖关系

```
Platform Layer
    ↓ 依赖
Infrastructure Layer (日志、错误处理、性能监控)
    ↓ 依赖  
Protocol Layer (数据包处理、加密解密)
```

### 核心组件架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Platform Layer                          │
├─────────────────────────────────────────────────────────────┤
│  NetworkExtension Provider                                 │
│  ├── PacketTunnelProvider                                  │
│  ├── VPN Configuration Manager                             │
│  ├── System Integration                                    │
│  └── Background Task Handler                               │
├─────────────────────────────────────────────────────────────┤
│  TUN Device Management                                     │
│  ├── Virtual Interface Manager                             │
│  ├── Packet Reader/Writer                                  │
│  ├── MTU Configuration                                     │
│  └── Interface State Monitor                               │
├─────────────────────────────────────────────────────────────┤
│  Route Management                                          │
│  ├── iOS Route Manager                                     │
│  ├── macOS Route Manager                                   │
│  ├── Route Conflict Resolver                               │
│  └── Route State Monitor                                   │
├─────────────────────────────────────────────────────────────┤
│  DNS Configuration                                         │
│  ├── DNS Settings Manager                                  │
│  ├── DNS Server Configuration                              │
│  ├── Domain Resolution                                     │
│  └── DNS Fallback Handler                                  │
├─────────────────────────────────────────────────────────────┤
│  Network State Monitor                                     │
│  ├── Connection Quality Monitor                            │
│  ├── Network Change Detector                               │
│  ├── Performance Metrics                                   │
│  └── Health Check Service                                  │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 核心组件设计

### 1. NetworkExtension Provider

#### 1.1 PacketTunnelProvider实现

**文件**: `Platform/NetworkExtension/PacketTunnelProvider.swift`

```swift
/**
 * FILE: PacketTunnelProvider.swift
 *
 * DESCRIPTION:
 *     Core NetworkExtension provider implementation for iOS/macOS VPN functionality.
 *     Handles system VPN requests and manages tunnel lifecycle.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import NetworkExtension
import Infrastructure
import Protocol

/**
 * NAME: VPNPacketTunnelProvider
 *
 * DESCRIPTION:
 *     Main PacketTunnelProvider implementation that integrates with iOS/macOS system VPN.
 *     Manages tunnel establishment, packet processing, and system integration.
 *
 * PROPERTIES:
 *     tunnelManager - TUN device management interface
 *     routeManager - Platform-specific route management
 *     dnsManager - DNS configuration management
 *     protocolHandler - SDWAN protocol processing
 */
public class VPNPacketTunnelProvider: NEPacketTunnelProvider {
    
    private let tunnelManager: TunnelManagerProtocol
    private let routeManager: RouteManagerProtocol  
    private let dnsManager: DNSManagerProtocol
    private let protocolHandler: ProtocolHandlerProtocol
    private let logger: LoggerProtocol
    
    /**
     * NAME: startTunnel
     *
     * DESCRIPTION:
     *     Starts the VPN tunnel with provided configuration options.
     *     Configures network settings and establishes connection.
     *
     * PARAMETERS:
     *     options - Tunnel configuration options from system
     *     completionHandler - Completion callback with result
     *
     * RETURNS:
     *     None (async completion)
     */
    public override func startTunnel(options: [String : NSObject]?, 
                                   completionHandler: @escaping (Error?) -> Void) {
        Task {
            do {
                try await initializeTunnel(options: options)
                completionHandler(nil)
            } catch {
                logger.log("Failed to start tunnel: \(error)", level: .error)
                completionHandler(error)
            }
        }
    }
    
    /**
     * NAME: stopTunnel
     *
     * DESCRIPTION:
     *     Stops the VPN tunnel and cleans up resources.
     *     Restores original network configuration.
     *
     * PARAMETERS:
     *     reason - Reason for tunnel stop
     *     completionHandler - Completion callback
     *
     * RETURNS:
     *     None (async completion)
     */
    public override func stopTunnel(with reason: NEProviderStopReason, 
                                  completionHandler: @escaping () -> Void) {
        Task {
            await cleanupTunnel(reason: reason)
            completionHandler()
        }
    }
}
```

#### 1.2 VPN Configuration Manager

**协议定义**:

```swift
/**
 * NAME: VPNConfigurationManagerProtocol
 *
 * DESCRIPTION:
 *     Manages VPN configuration and system integration settings.
 *     Handles NetworkExtension configuration and validation.
 *
 * METHODS:
 *     configureNetworkSettings - Configure tunnel network settings
 *     validateConfiguration - Validate VPN configuration
 *     updateConfiguration - Update runtime configuration
 */
public protocol VPNConfigurationManagerProtocol {
    func configureNetworkSettings() async throws -> NEPacketTunnelNetworkSettings
    func validateConfiguration(_ config: VPNConfiguration) throws
    func updateConfiguration(_ config: VPNConfiguration) async throws
}
```

### 2. TUN Device Management

#### 2.1 Virtual Interface Manager

**文件**: `Platform/TunnelDevice/VirtualInterfaceManager.swift`

```swift
/**
 * NAME: VirtualInterfaceManager
 *
 * DESCRIPTION:
 *     Manages virtual network interface configuration and state.
 *     Handles TUN device setup, MTU configuration, and interface monitoring.
 *     Based on Go backend Device interface design.
 *
 * PROPERTIES:
 *     interfaceSettings - Current interface configuration
 *     packetFlow - NetworkExtension packet flow
 *     mtuSize - Maximum transmission unit size
 *     isActive - Interface active state
 *     addresses - Assigned IP addresses
 */
public actor VirtualInterfaceManager: VirtualInterfaceManagerProtocol {

    private var interfaceSettings: InterfaceSettings?
    private var packetFlow: NEPacketTunnelFlow?
    private var mtuSize: Int = 1400
    private var isActive: Bool = false
    private var addresses: [IPNetwork] = []

    /**
     * NAME: configureInterface
     *
     * DESCRIPTION:
     *     Configures the virtual network interface with specified settings.
     *     Sets up IP address, subnet mask, and MTU parameters.
     *     Equivalent to Go Device.Configure() method.
     *
     * PARAMETERS:
     *     settings - Interface configuration settings
     *     packetFlow - NetworkExtension packet flow
     *
     * RETURNS:
     *     Configuration result
     *
     * THROWS:
     *     InterfaceError - Configuration validation or setup errors
     */
    public func configureInterface(settings: InterfaceSettings,
                                 packetFlow: NEPacketTunnelFlow) throws {
        // Store packet flow reference
        self.packetFlow = packetFlow
        self.interfaceSettings = settings
        self.mtuSize = settings.mtu
        self.addresses = settings.addresses

        // Validate configuration
        try validateConfiguration(settings)

        // Configure interface parameters
        try applyInterfaceConfiguration(settings)
    }

    /**
     * NAME: startInterface
     *
     * DESCRIPTION:
     *     Starts the virtual interface and begins packet processing.
     *     Equivalent to Go Device.Start() method.
     *
     * PARAMETERS:
     *     None
     *
     * RETURNS:
     *     None
     *
     * THROWS:
     *     InterfaceError - Interface start errors
     */
    public func startInterface() throws {
        guard let settings = interfaceSettings else {
            throw InterfaceError.notConfigured
        }

        // Start packet processing
        try startPacketProcessing()

        // Mark interface as active
        isActive = true
    }

    /**
     * NAME: stopInterface
     *
     * DESCRIPTION:
     *     Stops the virtual interface and cleans up resources.
     *     Equivalent to Go Device.Stop() method.
     *
     * PARAMETERS:
     *     None
     *
     * RETURNS:
     *     None
     */
    public func stopInterface() {
        // Stop packet processing
        stopPacketProcessing()

        // Clean up resources
        packetFlow = nil
        isActive = false
    }

    /**
     * NAME: flushConfiguration
     *
     * DESCRIPTION:
     *     Flushes all interface configuration (IP, routes, DNS).
     *     Equivalent to Go Device.FlushConfiguration() method.
     *     Uses bulk operations for efficiency.
     *
     * PARAMETERS:
     *     None
     *
     * RETURNS:
     *     None
     *
     * THROWS:
     *     InterfaceError - Configuration flush errors
     */
    public func flushConfiguration() throws {
        // Flush IP addresses
        try flushIPAddresses()

        // Flush routes
        try flushRoutes()

        // Flush DNS settings
        try flushDNSSettings()
    }
}
```

#### 2.2 Network Interface Abstraction

**文件**: `Platform/NetworkInterface/NetworkInterfaceManager.swift`

```swift
/**
 * NAME: NetworkInterfaceManager
 *
 * DESCRIPTION:
 *     Platform-agnostic network interface management.
 *     Provides unified API for iOS/macOS network configuration.
 *     Based on Go backend NetworkInterface design.
 *
 * PROPERTIES:
 *     platform - Current platform (iOS/macOS)
 *     logger - Logger for operation tracking
 *     packetFlow - NetworkExtension packet flow reference
 */
public actor NetworkInterfaceManager: NetworkInterfaceProtocol {

    private let platform: Platform
    private let logger: LoggerProtocol
    private weak var packetFlow: NEPacketTunnelFlow?

    /**
     * NAME: flushIPAddresses
     *
     * DESCRIPTION:
     *     Bulk flush of all IP addresses from interface.
     *     Equivalent to Go NetworkInterface.FlushIPAddresses().
     *
     * PARAMETERS:
     *     None
     *
     * RETURNS:
     *     None
     *
     * THROWS:
     *     NetworkError - IP address flush errors
     */
    public func flushIPAddresses() throws {
        switch platform {
        case .iOS:
            try flushIPAddressesiOS()
        case .macOS:
            try flushIPAddressesmacOS()
        }
    }

    /**
     * NAME: flushRoutes
     *
     * DESCRIPTION:
     *     Bulk flush of all routes from interface.
     *     Equivalent to Go NetworkInterface.FlushRoutes().
     *
     * PARAMETERS:
     *     None
     *
     * RETURNS:
     *     None
     *
     * THROWS:
     *     NetworkError - Route flush errors
     */
    public func flushRoutes() throws {
        switch platform {
        case .iOS:
            try flushRoutesiOS()
        case .macOS:
            try flushRoutesmacOS()
        }
    }

    /**
     * NAME: flushDNS
     *
     * DESCRIPTION:
     *     Bulk flush of DNS configuration.
     *     Equivalent to Go NetworkInterface.FlushDNS().
     *
     * PARAMETERS:
     *     None
     *
     * RETURNS:
     *     None
     *
     * THROWS:
     *     NetworkError - DNS flush errors
     */
    public func flushDNS() throws {
        switch platform {
        case .iOS:
            try flushDNSiOS()
        case .macOS:
            try flushDNSmacOS()
        }
    }
}
```

#### 2.3 Packet Reader/Writer

```swift
/**
 * NAME: PacketProcessor
 *
 * DESCRIPTION:
 *     Handles packet reading from and writing to TUN device.
 *     Manages packet queues and processing optimization.
 *     Based on Go backend WinTun session management design.
 *
 * PROPERTIES:
 *     readQueue - Packet reading queue
 *     writeQueue - Packet writing queue
 *     bufferPool - Reusable packet buffers
 *     sessionActive - Packet processing session state
 *     processingMetrics - Performance metrics collection
 */
public actor PacketProcessor: PacketProcessorProtocol {

    private let readQueue = DispatchQueue(label: "packet.read", qos: .userInitiated)
    private let writeQueue = DispatchQueue(label: "packet.write", qos: .userInitiated)
    private let bufferPool: DataBufferPool
    private var sessionActive: Bool = false
    private var processingMetrics = PacketProcessingMetrics()

    /**
     * NAME: startPacketProcessing
     *
     * DESCRIPTION:
     *     Starts continuous packet processing from TUN device.
     *     Reads packets and forwards to protocol handler.
     *     Equivalent to Go WinTun session management.
     *
     * PARAMETERS:
     *     packetFlow - NetworkExtension packet flow
     *     handler - Protocol packet handler
     *
     * RETURNS:
     *     None (continuous processing)
     */
    public func startPacketProcessing(packetFlow: NEPacketTunnelFlow,
                                    handler: ProtocolHandlerProtocol) async {
        sessionActive = true

        // Start read loop
        Task {
            await startReadLoop(packetFlow: packetFlow, handler: handler)
        }

        // Start write loop
        Task {
            await startWriteLoop(packetFlow: packetFlow)
        }
    }

    /**
     * NAME: stopPacketProcessing
     *
     * DESCRIPTION:
     *     Stops packet processing and cleans up session.
     *     Equivalent to Go WinTun session cleanup.
     *
     * PARAMETERS:
     *     None
     *
     * RETURNS:
     *     None
     */
    public func stopPacketProcessing() {
        sessionActive = false

        // Clean up buffers
        bufferPool.flush()

        // Reset metrics
        processingMetrics.reset()
    }
}
```

### 3. Route Management

#### 3.1 Platform-Specific Route Managers

**iOS Route Manager**:

```swift
/**
 * NAME: iOSRouteManager
 *
 * DESCRIPTION:
 *     iOS-specific route management implementation.
 *     Uses NEPacketTunnelNetworkSettings for route configuration.
 *
 * PROPERTIES:
 *     currentSettings - Active network settings
 *     routeRules - Configured route rules
 */
public class iOSRouteManager: RouteManagerProtocol {
    
    /**
     * NAME: configureRoutes
     *
     * DESCRIPTION:
     *     Configures routes using iOS NetworkExtension framework.
     *     Sets up included and excluded routes through system settings.
     *
     * PARAMETERS:
     *     settings - Route configuration settings
     *
     * RETURNS:
     *     Configuration result
     *
     * THROWS:
     *     RouteError - Route configuration errors
     */
    public func configureRoutes(settings: RouteSettings) throws -> RouteConfigurationResult {
        // iOS-specific implementation
    }
}
```

**macOS Route Manager**:

```swift
/**
 * NAME: macOSRouteManager
 *
 * DESCRIPTION:
 *     macOS-specific route management with enhanced capabilities.
 *     Supports dynamic route management and system integration.
 *
 * PROPERTIES:
 *     systemRoutes - System route table access
 *     dynamicRoutes - Runtime route modifications
 */
public class macOSRouteManager: RouteManagerProtocol {
    
    /**
     * NAME: configureRoutes
     *
     * DESCRIPTION:
     *     Configures routes with macOS-specific capabilities.
     *     Supports dynamic route updates and system monitoring.
     *
     * PARAMETERS:
     *     settings - Route configuration settings
     *
     * RETURNS:
     *     Configuration result
     *
     * THROWS:
     *     RouteError - Route configuration errors
     */
    public func configureRoutes(settings: RouteSettings) throws -> RouteConfigurationResult {
        // macOS-specific implementation
    }
}
```

#### 3.2 Route Conflict Resolver

```swift
/**
 * NAME: RouteConflictResolver
 *
 * DESCRIPTION:
 *     Detects and resolves route conflicts automatically.
 *     Handles overlapping routes and priority management.
 *
 * PROPERTIES:
 *     conflictDetector - Route conflict detection engine
 *     resolutionStrategy - Conflict resolution strategy
 */
public struct RouteConflictResolver: RouteConflictResolverProtocol {
    
    /**
     * NAME: detectConflicts
     *
     * DESCRIPTION:
     *     Scans for route conflicts in current configuration.
     *     Identifies overlapping routes and priority issues.
     *
     * PARAMETERS:
     *     routes - Route configuration to analyze
     *
     * RETURNS:
     *     List of detected conflicts
     */
    public func detectConflicts(routes: [RouteRule]) -> [RouteConflict] {
        // Implementation
    }
}
```

### 4. DNS Configuration

#### 4.1 DNS Settings Manager

```swift
/**
 * NAME: DNSSettingsManager
 *
 * DESCRIPTION:
 *     Manages DNS server configuration and domain resolution.
 *     Handles DNS settings for VPN tunnel.
 *
 * PROPERTIES:
 *     dnsServers - Configured DNS servers
 *     searchDomains - DNS search domains
 *     dnsProtocol - DNS protocol settings
 */
public actor DNSSettingsManager: DNSManagerProtocol {
    
    /**
     * NAME: configureDNS
     *
     * DESCRIPTION:
     *     Configures DNS settings for VPN tunnel.
     *     Sets up DNS servers and search domains.
     *
     * PARAMETERS:
     *     settings - DNS configuration settings
     *
     * RETURNS:
     *     DNS configuration result
     *
     * THROWS:
     *     DNSError - DNS configuration errors
     */
    public func configureDNS(settings: DNSSettings) throws -> DNSConfigurationResult {
        // Implementation
    }
}
```

### 5. Network State Monitor

#### 5.1 Connection Quality Monitor

```swift
/**
 * NAME: ConnectionQualityMonitor
 *
 * DESCRIPTION:
 *     Monitors VPN connection quality and performance metrics.
 *     Tracks latency, throughput, and connection stability.
 *
 * PROPERTIES:
 *     qualityMetrics - Current quality measurements
 *     monitoringInterval - Monitoring frequency
 */
public actor ConnectionQualityMonitor: ConnectionQualityMonitorProtocol {
    
    /**
     * NAME: startMonitoring
     *
     * DESCRIPTION:
     *     Starts continuous connection quality monitoring.
     *     Measures and reports connection performance metrics.
     *
     * PARAMETERS:
     *     interval - Monitoring interval in seconds
     *
     * RETURNS:
     *     None (continuous monitoring)
     */
    public func startMonitoring(interval: TimeInterval) async {
        // Implementation
    }
}
```

## 🔗 模块接口定义

### 核心协议

```swift
// Platform Layer主要协议
public protocol PlatformManagerProtocol {
    func initializePlatform() async throws
    func startVPNTunnel(config: VPNConfiguration) async throws
    func stopVPNTunnel() async throws
    func getNetworkStatus() async -> NetworkStatus
}

// TUN设备管理协议 (基于Go Device接口设计)
public protocol TunnelManagerProtocol {
    func configureTunnel(settings: TunnelSettings) async throws
    func startInterface() async throws
    func stopInterface() async
    func flushConfiguration() async throws
    func setMTU(_ mtu: Int) async throws
    func setAddresses(_ addresses: [IPNetwork]) async throws
    func isInterfaceUp() async -> Bool
}

// 网络接口管理协议 (基于Go NetworkInterface设计)
public protocol NetworkInterfaceProtocol {
    func flushIPAddresses() throws
    func flushRoutes() throws
    func flushDNS() throws
    func addIPAddress(_ address: IPNetwork) throws
    func deleteIPAddress(_ address: IPNetwork) throws
    func addRoute(destination: IPNetwork, gateway: IPAddress, metric: Int) throws
    func deleteRoute(destination: IPNetwork, gateway: IPAddress) throws
}

// 路由管理协议 (增强版，支持批量操作)
public protocol RouteManagerProtocol {
    func configureRoutes(settings: RouteSettings) throws -> RouteConfigurationResult
    func addRoute(destination: IPNetwork, gateway: IPAddress) throws
    func removeRoute(destination: IPNetwork) throws
    func getActiveRoutes() -> [Route]
    func flushAllRoutes() throws  // 批量清理
    func validateRouteConflicts(_ routes: [RouteRule]) -> [RouteConflict]
}

// DNS管理协议 (增强版，支持批量操作)
public protocol DNSManagerProtocol {
    func configureDNS(settings: DNSSettings) throws -> DNSConfigurationResult
    func updateDNSServers(_ servers: [String]) throws
    func getDNSConfiguration() -> DNSSettings
    func flushDNSConfiguration() throws  // 批量清理
    func validateDNSSettings(_ settings: DNSSettings) throws
}
```

## 📊 数据模型定义

### 配置数据结构

```swift
/**
 * NAME: VPNConfiguration
 *
 * DESCRIPTION:
 *     VPN tunnel configuration data structure.
 *     Contains all settings needed for tunnel establishment.
 *
 * PROPERTIES:
 *     serverAddress - VPN server address
 *     tunnelSettings - TUN device settings
 *     routeSettings - Route configuration
 *     dnsSettings - DNS configuration
 */
public struct VPNConfiguration: Codable {
    public let serverAddress: String
    public let tunnelSettings: TunnelSettings
    public let routeSettings: RouteSettings
    public let dnsSettings: DNSSettings
}

/**
 * NAME: TunnelSettings
 *
 * DESCRIPTION:
 *     TUN device configuration settings.
 *
 * PROPERTIES:
 *     localAddress - Local tunnel IP address
 *     subnetMask - Subnet mask
 *     mtu - Maximum transmission unit
 */
public struct TunnelSettings: Codable {
    public let localAddress: String
    public let subnetMask: String
    public let mtu: Int
}

/**
 * NAME: RouteSettings
 *
 * DESCRIPTION:
 *     Route configuration settings.
 *
 * PROPERTIES:
 *     includedRoutes - Routes to include in VPN
 *     excludedRoutes - Routes to exclude from VPN
 *     defaultRoute - Default route configuration
 */
public struct RouteSettings: Codable {
    public let includedRoutes: [RouteRule]
    public let excludedRoutes: [RouteRule]
    public let defaultRoute: Bool
}
```

## 🔄 平台差异化处理

### iOS平台特性

**系统限制**:
- 严格的沙盒环境，无法直接操作系统路由表
- 只能通过NetworkExtension框架进行网络配置
- 内存使用限制严格（通常<50MB）
- 后台运行时间限制

**iOS优化策略**:
```swift
/**
 * NAME: iOSPlatformOptimizer
 *
 * DESCRIPTION:
 *     iOS平台特定的优化策略实现。
 *     处理iOS系统限制和性能优化。
 *
 * PROPERTIES:
 *     memoryMonitor - 内存使用监控
 *     backgroundTaskManager - 后台任务管理
 */
public class iOSPlatformOptimizer: PlatformOptimizerProtocol {

    /**
     * NAME: optimizeForBackground
     *
     * DESCRIPTION:
     *     优化应用进入后台时的资源使用。
     *     减少内存占用和CPU使用率。
     *
     * PARAMETERS:
     *     None
     *
     * RETURNS:
     *     None
     */
    public func optimizeForBackground() {
        // 减少缓存大小
        // 暂停非关键任务
        // 优化数据包处理频率
    }
}
```

### macOS平台特性

**系统能力**:
- 完整的系统路由表访问权限
- 更灵活的网络配置能力
- 更大的内存和CPU资源
- 完整的系统集成能力

**macOS增强功能**:
```swift
/**
 * NAME: macOSPlatformEnhancer
 *
 * DESCRIPTION:
 *     macOS平台特定的增强功能实现。
 *     利用macOS的高级网络能力。
 *
 * PROPERTIES:
 *     systemRouteMonitor - 系统路由监控
 *     networkChangeNotifier - 网络变化通知
 */
public class macOSPlatformEnhancer: PlatformEnhancerProtocol {

    /**
     * NAME: enableAdvancedRouting
     *
     * DESCRIPTION:
     *     启用macOS高级路由功能。
     *     支持动态路由更新和精细控制。
     *
     * PARAMETERS:
     *     None
     *
     * RETURNS:
     *     None
     */
    public func enableAdvancedRouting() {
        // 启用动态路由监控
        // 配置高级路由策略
        // 集成系统网络偏好设置
    }
}
```

## ⚡ 性能优化策略

### 内存管理优化

```swift
/**
 * NAME: PlatformMemoryManager
 *
 * DESCRIPTION:
 *     Platform Layer专用内存管理器。
 *     优化NetworkExtension环境下的内存使用。
 *
 * PROPERTIES:
 *     packetBufferPool - 数据包缓冲池
 *     configurationCache - 配置缓存
 *     memoryPressureHandler - 内存压力处理器
 */
public actor PlatformMemoryManager {

    private let packetBufferPool: DataBufferPool
    private var configurationCache: [String: Any] = [:]
    private let memoryPressureHandler: MemoryPressureHandler

    /**
     * NAME: handleMemoryPressure
     *
     * DESCRIPTION:
     *     处理系统内存压力事件。
     *     释放非关键缓存和资源。
     *
     * PARAMETERS:
     *     level - 内存压力级别
     *
     * RETURNS:
     *     None
     */
    public func handleMemoryPressure(level: MemoryPressureLevel) {
        switch level {
        case .normal:
            break
        case .warning:
            clearNonEssentialCache()
        case .critical:
            clearAllCache()
            compactBufferPool()
        }
    }
}
```

### 数据包处理优化

```swift
/**
 * NAME: OptimizedPacketProcessor
 *
 * DESCRIPTION:
 *     优化的数据包处理器。
 *     提供高性能的数据包读写能力。
 *
 * PROPERTIES:
 *     processingQueue - 数据包处理队列
 *     batchSize - 批处理大小
 *     processingMetrics - 处理性能指标
 */
public actor OptimizedPacketProcessor: PacketProcessorProtocol {

    private let processingQueue = DispatchQueue(label: "packet.processing", qos: .userInitiated)
    private let batchSize: Int = 32
    private var processingMetrics = ProcessingMetrics()

    /**
     * NAME: processBatchPackets
     *
     * DESCRIPTION:
     *     批量处理数据包以提高性能。
     *     减少系统调用开销。
     *
     * PARAMETERS:
     *     packets - 待处理的数据包数组
     *
     * RETURNS:
     *     处理结果
     */
    public func processBatchPackets(_ packets: [Data]) async -> ProcessingResult {
        // 批量处理逻辑
        // 性能指标收集
        // 错误处理和恢复
    }
}
```

## 🔧 错误处理和恢复

### 平台错误定义

```swift
/**
 * NAME: PlatformError
 *
 * DESCRIPTION:
 *     Platform Layer特定错误类型定义。
 *     涵盖NetworkExtension和系统集成相关错误。
 */
public enum PlatformError: Error, LocalizedError {
    case networkExtensionNotAvailable
    case tunnelConfigurationFailed(String)
    case routeConfigurationFailed(String)
    case dnsConfigurationFailed(String)
    case systemPermissionDenied
    case memoryLimitExceeded
    case backgroundTaskExpired

    public var errorDescription: String? {
        switch self {
        case .networkExtensionNotAvailable:
            return "NetworkExtension framework not available"
        case .tunnelConfigurationFailed(let details):
            return "Tunnel configuration failed: \(details)"
        case .routeConfigurationFailed(let details):
            return "Route configuration failed: \(details)"
        case .dnsConfigurationFailed(let details):
            return "DNS configuration failed: \(details)"
        case .systemPermissionDenied:
            return "System permission denied"
        case .memoryLimitExceeded:
            return "Memory limit exceeded"
        case .backgroundTaskExpired:
            return "Background task expired"
        }
    }
}
```

### 自动恢复机制

```swift
/**
 * NAME: PlatformRecoveryManager
 *
 * DESCRIPTION:
 *     Platform Layer自动恢复管理器。
 *     处理系统级错误和自动恢复。
 *
 * PROPERTIES:
 *     recoveryStrategies - 恢复策略映射
 *     maxRetryAttempts - 最大重试次数
 *     recoveryMetrics - 恢复性能指标
 */
public actor PlatformRecoveryManager {

    /**
     * NAME: handlePlatformError
     *
     * DESCRIPTION:
     *     处理平台错误并尝试自动恢复。
     *     根据错误类型选择合适的恢复策略。
     *
     * PARAMETERS:
     *     error - 平台错误
     *     context - 错误上下文信息
     *
     * RETURNS:
     *     恢复结果
     */
    public func handlePlatformError(_ error: PlatformError,
                                  context: ErrorContext) async -> RecoveryResult {
        switch error {
        case .tunnelConfigurationFailed:
            return await recoverTunnelConfiguration(context: context)
        case .routeConfigurationFailed:
            return await recoverRouteConfiguration(context: context)
        case .memoryLimitExceeded:
            return await recoverFromMemoryPressure(context: context)
        default:
            return .failed("No recovery strategy available")
        }
    }
}
```

## 🧪 测试策略

### 单元测试

```swift
/**
 * NAME: PlatformLayerTests
 *
 * DESCRIPTION:
 *     Platform Layer单元测试套件。
 *     验证核心功能和错误处理。
 */
class PlatformLayerTests: XCTestCase {

    /**
     * NAME: testTunnelConfiguration
     *
     * DESCRIPTION:
     *     测试TUN设备配置功能。
     *     验证配置参数和状态管理。
     */
    func testTunnelConfiguration() async throws {
        let manager = VirtualInterfaceManager()
        let settings = TunnelSettings(
            localAddress: "********",
            subnetMask: "*************",
            mtu: 1400
        )

        try await manager.configureInterface(settings: settings, packetFlow: mockPacketFlow)

        let status = await manager.getInterfaceStatus()
        XCTAssertTrue(status.isConfigured)
        XCTAssertEqual(status.mtu, 1400)
    }

    /**
     * NAME: testRouteConflictDetection
     *
     * DESCRIPTION:
     *     测试路由冲突检测功能。
     *     验证冲突识别和解决机制。
     */
    func testRouteConflictDetection() {
        let resolver = RouteConflictResolver()
        let routes = [
            RouteRule(destination: "***********/24", gateway: "********"),
            RouteRule(destination: "***********/24", gateway: "********") // 冲突
        ]

        let conflicts = resolver.detectConflicts(routes: routes)
        XCTAssertEqual(conflicts.count, 1)
        XCTAssertEqual(conflicts[0].type, .overlappingDestination)
    }
}
```

### 集成测试

```swift
/**
 * NAME: PlatformIntegrationTests
 *
 * DESCRIPTION:
 *     Platform Layer集成测试。
 *     验证与其他层的集成和系统交互。
 */
class PlatformIntegrationTests: XCTestCase {

    /**
     * NAME: testNetworkExtensionIntegration
     *
     * DESCRIPTION:
     *     测试NetworkExtension集成。
     *     验证系统VPN功能正常工作。
     */
    func testNetworkExtensionIntegration() async throws {
        let provider = VPNPacketTunnelProvider()
        let config = VPNConfiguration.testConfiguration()

        try await provider.startTunnel(options: config.options) { error in
            XCTAssertNil(error)
        }

        // 验证隧道状态
        let status = await provider.getTunnelStatus()
        XCTAssertEqual(status, .connected)
    }
}
```

## 📋 实施计划

### 开发阶段

**阶段1: 核心框架搭建 (1周)**
- [ ] 创建基础协议定义
- [ ] 实现PacketTunnelProvider框架
- [ ] 搭建测试环境

**阶段2: TUN设备管理 (1周)**
- [ ] 实现VirtualInterfaceManager
- [ ] 实现PacketProcessor
- [ ] 添加MTU配置和优化

**阶段3: 路由管理实现 (1.5周)**
- [ ] 实现iOS/macOS路由管理器
- [ ] 实现路由冲突检测和解决
- [ ] 添加路由状态监控

**阶段4: DNS和网络监控 (0.5周)**
- [ ] 实现DNS配置管理
- [ ] 实现网络状态监控
- [ ] 集成性能指标收集

### 验收标准

- ✅ 所有核心协议定义完整
- ✅ NetworkExtension集成正常工作
- ✅ iOS和macOS平台差异正确处理
- ✅ 路由配置和冲突解决功能正常
- ✅ DNS配置生效
- ✅ 内存使用控制在限制范围内
- ✅ 错误处理和恢复机制完善
- ✅ 单元测试覆盖率>85%
- ✅ 集成测试通过

## 🔄 设计优化总结

### 基于Go后端分析的优化

通过深入分析Go后端Platform Layer实现（`internal/platform/`），本设计进行了以下关键优化：

**1. 功能对等性保证**：
- ✅ 实现Go Device接口的所有核心方法（Configure、Start、Stop、FlushConfiguration）
- ✅ 支持Go NetworkInterface的批量操作（FlushIPAddresses、FlushRoutes、FlushDNS）
- ✅ 保持与Go后端相同的错误处理和恢复机制

**2. 平台适配简化**：
- ✅ 移除不必要的接口标识符概念，专注于NEPacketTunnelFlow的功能实现
- ✅ 基于iOS/macOS NetworkExtension特性进行优化，而非直接移植Windows LUID概念
- ✅ 简化网络接口抽象，专注于实际功能而非平台特定标识

**3. 架构设计改进**：
- ✅ 增强TUN设备管理接口，添加批量配置清理功能
- ✅ 完善数据包处理流程，对应Go WinTun会话管理模式
- ✅ 加强错误处理和恢复机制，参考Go实现的robust设计

**4. 性能优化策略**：
- ✅ 针对NetworkExtension环境的内存限制进行专门优化
- ✅ 实现高效的批量操作，减少系统调用开销
- ✅ 优化数据包处理队列，提高吞吐量

### 与Go后端的关键差异

**保持的核心功能**：
- 完整的设备生命周期管理
- 批量配置操作支持
- 错误检测和自动恢复

**平台特定优化**：
- 使用NEPacketTunnelFlow替代Windows WinTun会话
- 基于iOS/macOS系统特性的路由管理
- NetworkExtension环境的资源管理优化

---

**文档版本**: v1.1
**创建日期**: 2025-06-23
**最后更新**: 2025-06-23
**AUTHOR**: wei
**HISTORY**: 23/06/2025 create platform layer design, optimized based on Go backend analysis
