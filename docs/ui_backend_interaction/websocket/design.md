# UI和后端WebSocket通信设计文档

本文档定义了UI（Flutter）和后端（Go）之间的WebSocket通信接口，用于后端主动向UI推送实时状态更新。设计参考了Android实现，并保持了一致的交互模式。

## WebSocket和HTTP API的关系

WebSocket和HTTP API是两种互补的通信机制，它们同时存在于系统中：

- **WebSocket**：由后端主动推送状态更新，用于实时通知UI状态变化。
- **HTTP API**：由UI主动发起请求，用于触发操作或获取数据。

WebSocket在建立连接后保持长连接，允许后端随时向UI推送消息。这与HTTP的请求-响应模式不同，后者需要UI主动发起请求才能获取数据。

例如，当用户通过HTTP API触发连接操作后，连接状态的变化（如`connecting`、`connected`等）会通过WebSocket实时推送给UI，而不需要UI反复轮询状态。

这种设计提供了更好的用户体验和更高的实时性，特别适合VPN客户端这种需要实时状态更新的应用。

## 基本信息

### WebSocket URL
WebSocket连接URL：
```
ws://localhost:8081/ws
```

**注意**：WebSocket服务器使用的端口（8081）与HTTP服务器（8080）不同。这是因为它们是两个独立的服务器，各自监听不同的端口。

### 消息格式
所有WebSocket消息都遵循统一的JSON格式：
```json
{
  "event": "事件类型",
  "data": {
    // 事件数据，根据不同事件类型有不同的结构
  }
}
```

## 事件类型

### 1. 状态更新事件

当VPN连接状态发生变化时，后端会发送状态更新事件。

**事件类型**：`status`

**数据格式**：
```json
{
  "event": "status",
  "data": {
    "status": "connected",
    "message": "已连接到服务器",
    "server": {
      "id": "server_id",
      "name": "服务器名称",
      "name_en": "Server Name",
      "server_name": "server.example.com",
      "server_port": 8000
    },
    "connected_time": 1234567890
  }
}
```

**状态值**：
- `disconnected`: 未连接到任何VPN服务器
- `connecting`: 正在连接
- `connected`: 已成功连接到VPN服务器
- `disconnecting`: 正在断开连接
- `error`: 错误状态

### 2. 错误事件

当发生错误时，后端会发送错误事件。

**事件类型**：`error`

**数据格式**：
```json
{
  "event": "error",
  "data": {
    "code": 1001,
    "message": "错误消息",
    "type": "error_type"
  }
}
```

### 3. 服务器列表更新事件

当服务器列表发生变化时，后端会发送服务器列表更新事件。

**事件类型**：`servers`

**数据格式**：
```json
{
  "event": "servers",
  "data": [
    {
      "id": "server_id_1",
      "name": "服务器1",
      "name_en": "Server 1",
      "server_name": "server1.example.com",
      "server_port": 8000,
      "ping": 50,
      "isauto": false,
      "status": "connected"
    },
    {
      "id": "server_id_2",
      "name": "服务器2",
      "name_en": "Server 2",
      "server_name": "server2.example.com",
      "server_port": 8000,
      "ping": 100,
      "isauto": false,
      "status": "disconnected"
    }
  ]
}
```

### 4. 服务器测试事件

当服务器测试开始或完成时，后端会发送服务器测试事件。

#### 4.1 测试开始事件

**事件类型**：`ping_start`

**数据格式**：
```json
{
  "event": "ping_start",
  "data": null
}
```

#### 4.2 测试完成事件

**事件类型**：`ping_complete`

**数据格式**：
```json
{
  "event": "ping_complete",
  "data": null
}
```

测试完成后，通常会紧接着发送一个`servers`事件，包含更新后的服务器列表和延迟信息。

## 连接管理

### 连接建立
当UI启动时，应立即建立WebSocket连接。连接建立后，后端会立即发送当前的状态。

### 登录后的状态更新
当用户登录成功后，后端会在后台自动连接到最佳服务器。连接过程中的状态变化将通过WebSocket实时推送给UI，包括：

1. **连接中状态**：当后端开始连接过程时，发送`status`事件，状态为`connecting`

2. **认证过程**：当进行服务器认证时，状态仍然为`connecting`，但可能会更新消息字段

3. **TUN接口配置**：当认证成功并配置TUN接口时，状态仍然为`connecting`

4. **连接成功**：当全部过程完成时，发送`status`事件，状态变为`connected`，并包含连接时间和服务器信息

5. **连接失败**：如果连接过程中出现错误，发送`status`事件，状态变为`error`，并发送`error`事件包含错误详情

### 连接断开和重连
当WebSocket连接断开时，UI应该实现自动重连机制，使用指数退避策略（例如，初始等待3秒，然后6秒，12秒等）。

### 心跳机制
为了保持WebSocket连接活跃，后端会定期（例如每30秒）发送心跳消息。

**事件类型**：`heartbeat`

**数据格式**：
```json
{
  "event": "heartbeat",
  "data": {
    "timestamp": 1234567890
  }
}
```

UI收到心跳消息后，可以选择回复心跳响应，但这不是必需的。

## 实现注意事项

### 1. 状态同步
UI应该使用WebSocket事件来保持其状态与后端同步。当收到状态更新事件时，UI应该更新其内部状态并相应地更新界面。

### 2. 错误处理
UI应该妥善处理WebSocket连接错误，并在连接断开时实现重连机制。

### 3. 消息缓冲
后端应该实现消息缓冲机制，以防止在高负载情况下消息丢失。

### 4. 并发控制
后端应该实现适当的并发控制，以确保WebSocket消息的顺序性和一致性。

## 数据模型

### 状态模型
```json
{
  "status": "连接状态",
  "message": "状态消息",
  "server": {
    // 服务器信息
  },
  "connected_time": 1234567890
}
```

### 错误模型
```json
{
  "code": 1001,
  "message": "错误消息",
  "type": "error_type"
}
```

### 服务器模型
```json
{
  "id": "服务器ID",
  "name": "服务器名称（中文）",
  "name_en": "服务器名称（英文）",
  "server_name": "服务器域名或IP",
  "server_port": 8000,
  "ping": 50,
  "isauto": false,
  "status": "connected|disconnected|connecting"
}
```