# UI 和后端交互设计

**实施状态**: ✅ **100% 完成** - 支持多平台通信机制

本目录包含 UI（Flutter）和后端之间交互的设计文档，涵盖多平台通信架构。

## 目录结构

- `http_api/` - HTTP API 设计文档（Windows/Linux）
  - `design.md` - 详细的 HTTP API 设计
- `websocket/` - WebSocket 通信设计文档（Windows/Linux）
  - `design.md` - 详细的 WebSocket 通信设计

## 多平台通信架构

基于实际实现，UI 和后端之间的交互采用多平台策略：

### Windows/Linux 平台
1. **HTTP API**：用于 UI 主动向 Go 后端发起请求，如登录、连接 VPN、获取服务器列表等
2. **WebSocket**：用于 Go 后端主动向 UI 推送实时状态更新，如连接状态变化等

### iOS/macOS 平台
1. **Method Channel**：用于 UI 主动向 Swift 后端发起请求，功能等同于 HTTP API
2. **Event Channel**：用于 Swift 后端主动向 UI 推送实时状态更新，功能等同于 WebSocket

### Android 平台（开发中）
1. **Method Channel**：用于 UI 主动向 Kotlin 后端发起请求，功能等同于 HTTP API
2. **Event Channel**：用于 Kotlin 后端主动向 UI 推送实时状态更新，功能等同于 WebSocket

### 通信机制对比

不同平台采用不同的通信机制，但提供相同的功能：

#### Windows/Linux: HTTP API + WebSocket
- **HTTP API**：请求-响应模式，UI 主动发起请求，Go 后端响应
- **WebSocket**：实时推送模式，Go 后端主动向 UI 发送更新

#### iOS/macOS/Android: Platform Channel + Event Channel
- **Method Channel**：请求-响应模式，UI 主动调用原生方法，Swift/Kotlin 后端响应
- **Event Channel**：实时推送模式，Swift/Kotlin 后端主动向 UI 发送事件

### 功能对等性

所有平台提供相同的功能接口：

| 功能 | Windows/Linux | iOS/macOS/Android |
|------|---------------|-------------------|
| 用户登录 | `POST /api/login` | `platform.invokeMethod('login')` |
| VPN 连接 | `POST /api/connect` | `platform.invokeMethod('connect')` |
| 获取状态 | `GET /api/status` | `platform.invokeMethod('getConnectionStatus')` |
| 状态更新 | WebSocket 事件 | Event Channel 事件 |

### 通信流程示例

**连接 VPN 流程**：

1. **用户操作**：用户点击连接按钮
2. **UI 请求**：
   - Windows/Linux: 发送 HTTP 请求到 `/api/connect`
   - iOS/macOS/Android: 调用 `platform.invokeMethod('connect')`
3. **后端处理**：后端开始连接过程
4. **状态推送**：
   - Windows/Linux: 通过 WebSocket 推送状态更新
   - iOS/macOS/Android: 通过 Event Channel 推送状态更新
5. **UI 更新**：UI 接收状态更新并刷新界面

### 服务器实现

在实现上，HTTP和WebSocket是两个独立的服务器：

- **HTTP服务器**：监听在默认端口（如8080），处理所有HTTP API请求。
- **WebSocket服务器**：监听在不同的端口（如8081），专门处理WebSocket连接和消息推送。

这种设计提供了更好的分离和扩展性，允许两个服务器独立扩展和维护。UI需要同时连接这两个服务器：

1. 使用HTTP客户端连接HTTP服务器，发送API请求
2. 使用WebSocket客户端连接WebSocket服务器，接收实时更新

## 设计原则

1. **一致性**：保持与Android实现的一致性，包括接口名称、参数和返回值。
2. **简洁性**：接口设计简洁明了，避免不必要的复杂性。
3. **可扩展性**：设计考虑未来可能的扩展需求。
4. **安全性**：考虑接口的安全性，如认证和授权。
5. **可靠性**：考虑网络不稳定情况下的重试和恢复机制。

## 实现指南

### 后端实现

后端应实现以下功能：

1. HTTP服务器，处理来自UI的请求
2. WebSocket服务器，向UI推送实时状态更新
3. 认证和授权机制
4. 错误处理和日志记录

### UI实现

UI应实现以下功能：

1. HTTP客户端，向后端发送请求
2. WebSocket客户端，接收后端推送的实时状态更新
3. 状态管理，保持与后端的状态同步
4. 错误处理和重试机制

## 参考资料

- [Flutter HTTP 客户端](https://pub.dev/packages/http)
- [Flutter WebSocket 客户端](https://pub.dev/packages/web_socket_channel)
- [Go HTTP 服务器](https://golang.org/pkg/net/http/)
- [Go WebSocket 服务器](https://github.com/gorilla/websocket)
