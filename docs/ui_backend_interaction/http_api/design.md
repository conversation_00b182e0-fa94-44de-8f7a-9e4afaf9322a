# UI和后端HTTP交互设计文档

本文档定义了UI（Flutter）和后端（Go）之间的HTTP交互接口。设计参考了Android实现，并保持了一致的交互模式。

## HTTP API和WebSocket的关系

HTTP API和WebSocket是两种互补的通信机制，它们同时存在于系统中：

- **HTTP API**：由UI主动发起请求，用于触发操作或获取数据。
- **WebSocket**：由后端主动推送状态更新，用于实时通知UI状态变化。

例如，当用户点击连接按钮时，UI会发送HTTP请求到`/connect`端点。这个请求会立即返回，表示连接过程已启动。随后，后端会通过WebSocket实时推送连接状态的变化（如`connecting`、`connected`等）。

这种设计允许UI在触发长时间运行的操作后立即响应用户，同时通过WebSocket获取实时状态更新。

## 基本信息

### 基础URL
所有API端点都相对于基础URL：
```
http://localhost:8080/api
```

### 响应格式
所有API响应都遵循统一的JSON格式：
```json
{
  "success": true|false,
  "message": "响应消息",
  "data": {
    // 响应数据，根据不同接口有不同的结构
  }
}
```

### 错误处理
当发生错误时，响应将包含错误信息：
```json
{
  "success": false,
  "message": "错误消息",
  "error_code": 1001,
  "error_type": "error_type"
}
```

## API端点

### 1. 用户认证

#### 1.1 登录

**请求**：
```
POST /login
```

**请求体**：
```json
{
  "username": "用户名",
  "password": "密码"
}
```

**响应**：
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "best_server": {
      "id": "server_id",
      "name": "服务器名称",
      "name_en": "Server Name",
      "server_name": "server.example.com",
      "server_port": 8000,
      "ping": 50,
      "isauto": true,
      "status": "disconnected"
    }
  }
}
```

**处理流程**：

1. 验证用户名和密码
2. 服务器获取最佳服务器信息
3. 进行认证验证：
   - 使用用户提供的凭据进行认证
   - 如果认证失败，立即返回错误
   - 如果认证成功，返回成功响应（包含best_server信息）
4. **注意**：登录成功后不再自动连接VPN，保持与iOS/Android平台一致
5. UI收到登录成功响应后，会自动选择best_server作为默认服务器
6. 用户需要手动点击连接按钮来建立VPN连接
7. 连接过程中的状态变化通过WebSocket实时推送给UI

**注意**：登录成功后不返回token，UI和后端之间的通信不需要认证。

### 2. 服务器管理

#### 2.1 获取服务器列表

**请求**：
```
GET /servers
```

**响应**：
```json
{
  "success": true,
  "data": [
    {
      "id": "server_id_1",
      "name": "服务器1",
      "name_en": "Server 1",
      "server_name": "server1.example.com",
      "server_port": 8000,
      "ping": 50,
      "isauto": false,
      "status": "connected",
      "isdefault": true
    },
    {
      "id": "server_id_2",
      "name": "服务器2",
      "name_en": "Server 2",
      "server_name": "server2.example.com",
      "server_port": 8000,
      "ping": 100,
      "isauto": false,
      "status": "disconnected",
      "isdefault": false
    },
    {
      "id": "auto",
      "name": "自动选择",
      "name_en": "Auto Select",
      "server_name": "",
      "server_port": 0,
      "ping": 0,
      "isauto": true,
      "status": "disconnected",
      "isdefault": false
    }
  ]
}
```

#### 2.2 测试服务器延迟

**请求**：
```
POST /servers/ping
```

**响应**：
```json
{
  "success": true,
  "message": "服务器延迟测试已完成"
}
```

### 3. 连接管理

#### 3.1 连接到VPN

**请求**：
```
POST /connect
```

**请求体**：
```json
{
  "server_id": "server_id"
}
```

**响应**：
```json
{
  "success": true,
  "message": "VPN连接已启动"
}
```

**处理流程**：

1. 验证请求参数，确保服务器ID有效
2. 在后台启动连接过程：
   - 使用存储在连接管理器中的用户凭据
   - 连接到指定的服务器
   - 创建TUN接口并进行配置
3. 立即返回成功响应，不等待连接完成
4. 连接状态变化通过WebSocket实时推送给UI

**注意**：这个接口仅启动连接过程，不等待连接完成。连接的实际状态需要通过WebSocket或`/status`接口获取。

#### 3.2 断开VPN连接

**请求**：
```
POST /disconnect
```

**响应**：
```json
{
  "success": true,
  "message": "VPN连接已断开"
}
```

**处理流程**：

1. 在后台启动断开连接过程：
   - 关闭TUN接口
   - 断开与服务器的连接
   - 清理相关资源
2. 立即返回成功响应，不等待断开连接完成
3. 连接状态变化通过WebSocket实时推送给UI

**注意**：这个接口仅启动断开连接过程，不等待过程完成。连接的实际状态需要通过WebSocket或`/status`接口获取。

#### 3.3 获取连接状态

**请求**：
```
GET /status
```

**响应**：
```json
{
  "success": true,
  "data": {
    "status": "connected",
    "server": {
      "id": "server_id",
      "name": "服务器名称",
      "name_en": "Server Name",
      "server_name": "server.example.com",
      "server_port": 8000
    },
    "connected_time": **********
  }
}
```

**处理流程**：

1. 从连接管理器获取当前的连接状态
2. 返回状态信息，包括：
   - 连接状态（connected、disconnected、connecting等）
   - 当前连接的服务器信息（如果已连接）
   - 连接时间（如果已连接）

**注意**：这个接口提供了连接的实时状态，可以用于在UI启动时同步状态，或在WebSocket连接不可用时获取状态。

### 4. 系统管理

#### 4.1 健康检查

**请求**：
```
GET /health
```

**响应**：
```json
{
  "success": true,
  "data": {
    "status": "ok",
    "version": "1.0.0",
    "vpn_status": "disconnected"
  }
}
```

#### 4.2 关闭服务

**请求**：
```
POST /shutdown
```

**响应**：
```json
{
  "success": true,
  "message": "服务正在关闭"
}
```

## 数据模型

### 服务器模型
```json
{
  "id": "服务器ID",
  "name": "服务器名称（中文）",
  "name_en": "服务器名称（英文）",
  "server_name": "服务器域名或IP",
  "server_port": 8000,
  "ping": 50,
  "isauto": false,
  "status": "connected|disconnected|connecting"
}
```

### 状态模型
```json
{
  "status": "连接状态",
  "message": "状态消息",
  "server": {
    // 服务器信息
  },
  "connected_time": **********
}
```

## 状态码

- `disconnected`: 未连接到任何VPN服务器
- `connecting`: 正在连接
- `connected`: 已成功连接到VPN服务器
- `disconnecting`: 正在断开连接
- `error`: 错误状态

## 登录和连接流程

### 登录流程

1. **用户登录**
   - UI发送用户名和密码到后端
   - 后端验证用户凭据

2. **认证过程**
   - 后端先进行认证，不建立连接
   - 如果认证失败，立即返回错误
   - 如果认证成功，返回最佳服务器信息

3. **自动连接**
   - 后端在返回响应后，在后台启动连接过程
   - 使用用户提供的凭据连接到最佳服务器

4. **状态更新**
   - 整个过程中的状态变化通过WebSocket实时推送给UI
   - UI根据状态更新界面显示
   - 包括认证中、认证成功、认证失败、连接中、连接成功、连接失败等状态

### VPN连接过程

1. **连接过程**
   - 创建OPEN数据包，包含用户名、密码和MTU等信息
   - 发送数据包到服务器
   - 等待服务器响应
   - 服务器返回OpenAck数据包，包含IP地址、子网掩码、DNS等信息

2. **TUN接口创建和配置**
   - 创建TUN设备
   - 设置MTU
   - 配置IP地址和子网掩码
   - 设置DNS服务器
   - 添加默认路由（0.0.0.0/0）指向TUN设备

3. **数据传输**
   - 启动数据包接收协程
   - 启动心跳协程
   - 启动TUN设备读取协程

### 服务器管理和连接流程

1. **服务器选择**
   - 系统会自动选择`isauto`标记的服务器中延迟最低的作为最佳服务器
   - 登录成功后，系统会自动连接到最佳服务器
   - 用户也可以手动选择其他服务器进行连接

2. **手动连接**
   - UI可以调用`/connect`接口连接到指定的服务器
   - 这个操作会启动连接过程，包括认证、TUN接口创建等
   - 连接状态变化通过WebSocket实时推送给UI

3. **断开连接**
   - UI可以调用`/disconnect`接口断开当前连接
   - 这个操作会关闭TUN接口并断开与服务器的连接
   - 连接状态变化通过WebSocket实时推送给UI

4. **状态查询**
   - UI可以调用`/status`接口获取当前连接状态
   - 这个操作返回当前的连接状态、服务器信息和连接时间
   - 用于UI启动时同步状态或WebSocket连接不可用时获取状态
