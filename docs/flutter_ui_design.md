# Flutter UI 设计文档

## 1. 概述

本文档描述了 ITforce WAN 客户端 Flutter UI 的设计和实现。该 UI 已完成开发并支持多平台部署，包括 Windows、macOS、iOS 和 Android（开发中）。通过统一的 Flutter 代码库和平台特定的通信机制，在不同平台上提供一致的用户体验。

**当前实现状态**：
- ✅ **Windows**: 完整实现，通过 HTTP API + WebSocket 与 Go 后端通信
- ✅ **macOS**: 完整实现，通过 Platform Channel 与 Swift 后端通信
- ✅ **iOS**: 完整实现，通过 Platform Channel 与 Swift 后端通信
- 🚧 **Android**: 开发中，将通过 Platform Channel 与 Kotlin 后端通信

### 1.1 设计目标

- ✅ **跨平台一致性**：在 Windows、macOS、iOS、Android 上提供统一的用户体验
- ✅ **现代化设计**：Material Design 3 风格，响应式布局适配不同屏幕
- ✅ **功能完整性**：包含连接管理、服务器选择、流量统计、设置配置等完整功能
- ✅ **实时更新**：通过 WebSocket/Event Channel 实现状态实时同步
- ✅ **多语言支持**：完整的中英文本地化支持
- ✅ **平台适配**：针对桌面和移动平台的界面优化

### 1.2 技术特性

- **状态管理**：Provider 模式，统一的状态管理和数据流
- **通信机制**：支持 HTTP API + WebSocket（Windows）和 Platform Channel（iOS/macOS/Android）
- **响应式设计**：自适应布局，支持不同屏幕尺寸和方向
- **主题系统**：统一的设计系统，支持深色/浅色主题
- **本地化**：基于 Flutter Intl 的完整国际化支持

## 2. 项目结构

基于实际的Flutter项目实现，目录结构如下：

```
ui/flutter/
├── lib/
│   ├── main.dart                     # 应用入口，Provider配置
│   ├── core/                         # 核心功能
│   │   ├── app_state.dart           # 全局状态管理（Provider）
│   │   ├── dependency_injection.dart # 依赖注入配置
│   │   └── error_handler.dart       # 全局错误处理
│   ├── screens/                      # UI界面层
│   │   ├── login_screen.dart        # 登录界面
│   │   ├── main_screen.dart         # 主界面容器（导航）
│   │   ├── connection_screen.dart   # 连接管理界面
│   │   ├── user_screen.dart         # 用户信息界面
│   │   ├── statistics_screen.dart   # 流量统计界面
│   │   ├── settings_screen.dart     # 设置界面
│   │   ├── logs_screen.dart         # 日志查看界面
│   │   ├── about_screen.dart        # 关于界面
│   │   └── server_list_screen.dart  # 服务器选择界面
│   ├── services/                     # 服务层
│   │   ├── platform/                # 平台特定服务
│   │   │   ├── cross_platform_api_service.dart      # 跨平台API抽象
│   │   │   ├── http_api_service.dart                # HTTP API实现（Windows/Linux）
│   │   │   ├── platform_channel_api_service.dart    # Platform Channel实现（iOS/macOS）
│   │   │   ├── cross_platform_storage_service.dart  # 跨平台存储抽象
│   │   │   ├── platform_service_factory.dart        # 平台服务工厂
│   │   │   └── platform_channel_backend_service.dart # Platform Channel后端服务
│   │   ├── backend_service.dart      # 后端服务管理
│   │   ├── connection_manager.dart   # 连接管理服务
│   │   ├── auth_service.dart         # 认证服务
│   │   ├── websocket_service.dart    # WebSocket服务（Windows/Linux）
│   │   ├── log_service.dart          # 日志服务
│   │   ├── notification_service.dart # 通知服务
│   │   ├── app_initialization_service.dart # 应用初始化
│   │   └── routing_settings_service.dart   # 路由设置服务
│   ├── models/                       # 数据模型
│   │   ├── connection_status.dart    # 连接状态模型
│   │   ├── server.dart              # 服务器信息模型
│   │   ├── user_info.dart           # 用户信息模型
│   │   ├── traffic_stats.dart       # 流量统计模型
│   │   ├── interface_info.dart      # 网络接口信息
│   │   ├── routing_settings.dart    # 路由设置模型
│   │   └── log_entry.dart           # 日志条目模型
│   ├── widgets/                      # 通用UI组件
│   │   ├── app_builder.dart         # 应用构建器
│   │   ├── notification_manager.dart # 通知管理器
│   │   ├── responsive_layout.dart   # 响应式布局
│   │   ├── enhanced_connection_button.dart # 增强连接按钮
│   │   ├── routing_mode_selector.dart      # 路由模式选择器
│   │   └── adaptive_sidebar.dart    # 自适应侧边栏
│   ├── utils/                        # 工具类
│   │   ├── constants.dart           # 常量定义
│   │   ├── formatters.dart          # 格式化工具
│   │   ├── api_exception.dart       # API异常处理
│   │   ├── design_system.dart       # 设计系统
│   │   ├── device_info.dart         # 设备信息
│   │   └── simple_encryptor.dart    # 简单加密工具
│   └── l10n/                         # 国际化
│       ├── app_en.arb              # 英文资源
│       └── app_zh.arb              # 中文资源
├── assets/                           # 静态资源
│   ├── images/                      # 图片资源
│   └── icons/                       # 图标资源
├── pubspec.yaml                      # Flutter依赖配置
└── ItForceCore/                      # iOS/macOS Swift包（本地引用）
    └── Sources/                     # Swift源码
```

### 实际实现特点

1. **跨平台架构**：通过 `platform/` 目录实现平台特定服务的抽象
2. **状态管理**：使用 Provider 模式进行全局状态管理
3. **依赖注入**：使用 GetIt 进行服务定位和依赖管理
4. **多语言支持**：完整的 Flutter Intl 国际化实现
5. **响应式设计**：适配桌面和移动平台的不同屏幕尺寸

## 3. 通信架构

### 3.1 多平台通信策略

Flutter UI 通过抽象工厂模式实现跨平台通信，根据运行平台自动选择合适的通信方式：

```dart
// 平台服务工厂 - platform_service_factory.dart
class PlatformServiceFactory {
  static CrossPlatformApiService createApiService() {
    if (Platform.isWindows || Platform.isLinux) {
      return HttpApiService(); // HTTP API + WebSocket
    } else {
      return PlatformChannelApiService(); // Platform Channel
    }
  }
}
```

### 3.2 Windows/Linux 通信架构

**HTTP API + WebSocket 模式**：

```dart
// HTTP API 服务 - http_api_service.dart
class HttpApiService extends CrossPlatformApiService {
  static const String baseUrl = 'http://localhost:56543';
  static const String wsUrl = 'ws://localhost:56544/ws';

  // HTTP API 调用
  Future<ApiResponse> login(String username, String password) async {
    final response = await http.post('$baseUrl/api/login', body: {
      'username': username,
      'password': password,
    });
    return ApiResponse.fromJson(response.body);
  }

  // WebSocket 事件监听
  void startWebSocketConnection() {
    _channel = WebSocketChannel.connect(Uri.parse(wsUrl));
    _channel.stream.listen((message) {
      final event = json.decode(message);
      _handleWebSocketEvent(event);
    });
  }
}
```

**WebSocket 事件类型**：
- `connection_status_changed` - 连接状态变化
- `traffic_stats_updated` - 流量统计更新
- `server_list_updated` - 服务器列表更新

### 3.3 iOS/macOS/Android 通信架构

**Platform Channel + Event Channel 模式**：

```dart
// Platform Channel API 服务 - platform_channel_api_service.dart
class PlatformChannelApiService extends CrossPlatformApiService {
  static const MethodChannel _methodChannel =
      MethodChannel('com.itforce.wan/vpn_service');
  static const EventChannel _eventChannel =
      EventChannel('com.itforce.wan/vpn_events');

  // Method Channel 调用
  Future<ApiResponse> login(String username, String password) async {
    try {
      final result = await _methodChannel.invokeMethod('login', {
        'username': username,
        'password': password,
      });
      return ApiResponse.fromMap(result);
    } on PlatformException catch (e) {
      throw ApiException(e.code, e.message);
    }
  }

  // Event Channel 监听
  void startEventChannelListening() {
    _eventChannel.receiveBroadcastStream().listen((event) {
      _handlePlatformEvent(event);
    });
  }
}
```

**Platform Channel 方法**：
- `initializeBackend` - 初始化后端服务
- `login` - 用户登录
- `connect` - 建立 VPN 连接
- `disconnect` - 断开 VPN 连接
- `getServerList` - 获取服务器列表
- `getConnectionStatus` - 获取连接状态

### 3.4 统一接口抽象

```dart
// 跨平台 API 服务抽象 - cross_platform_api_service.dart
abstract class CrossPlatformApiService {
  Future<ApiResponse> initializeBackend();
  Future<ApiResponse> login(String username, String password);
  Future<ApiResponse> connect(String serverId);
  Future<ApiResponse> disconnect();
  Future<ApiResponse> getServerList();
  Future<ApiResponse> getConnectionStatus();

  // 事件流
  Stream<ConnectionStatusEvent> get connectionStatusStream;
  Stream<TrafficStatsEvent> get trafficStatsStream;
  Stream<ServerListEvent> get serverListStream;
}
```

### 3.5 状态同步机制

**Provider 状态管理**：

```dart
// 全局状态管理 - app_state.dart
class AppState extends ChangeNotifier {
  ConnectionStatus _connectionStatus = ConnectionStatus.disconnected;
  List<Server> _servers = [];
  TrafficStats _trafficStats = TrafficStats.empty();

  // 状态更新方法
  void updateConnectionStatus(ConnectionStatus status) {
    _connectionStatus = status;
    notifyListeners();
  }

  void updateTrafficStats(TrafficStats stats) {
    _trafficStats = stats;
    notifyListeners();
  }
}
```

**事件处理流程**：
1. 后端服务发送事件（WebSocket/Event Channel）
2. 平台特定服务接收事件
3. 转换为统一的事件格式
4. 更新全局状态（AppState）
5. UI 组件自动重建（Provider.Consumer）

## 3. 设计规范

### 3.1 颜色方案

UI将使用与Android版本一致的颜色方案：

- **主色调**：#FF7A00（橙色）
- **背景色**：#FFFFFF（白色）
- **文本颜色**：
  - 主要文本：#333333（深灰色）
  - 次要文本：#666666（中灰色）
  - 提示文本：#999999（浅灰色）
- **状态颜色**：
  - 已连接：#33DB87（绿色）
  - 连接中：#FF9500（橙色）
  - 未连接：#999999（灰色）
  - 错误：#FF3B30（红色）
- **边框和分割线**：#E5E5E5（浅灰色）

### 3.2 字体

UI将使用Roboto字体系列：

- Roboto-Regular：常规文本
- Roboto-Bold：标题和强调文本
- Roboto-Light：次要文本

### 3.3 图标和资源

UI将使用与Android版本一致的图标和资源，确保视觉体验的一致性。所有图标将以SVG格式存储，以确保在不同分辨率下的清晰度。

### 3.4 布局原则

- 使用响应式布局，适应不同屏幕尺寸
- 保持与Android版本一致的组件大小和间距
- 使用Material Design的卡片、阴影和高度来创建层次感
- 确保所有交互元素有足够的点击区域（至少48x48像素）

## 4. 屏幕设计

### 4.1 登录屏幕

登录屏幕是用户首次进入应用时看到的界面，用于用户认证。

#### 4.1.1 布局设计

![登录屏幕布局](placeholder_for_login_screen_image)

- **顶部**：应用logo
- **中部**：
  - 用户名输入框（带用户图标）
  - 密码输入框（带密码图标和显示/隐藏切换）
- **底部**：
  - 登录按钮（橙色，#FF7A00）
  - 用户协议和隐私政策复选框和链接

#### 4.1.2 交互设计

- **用户名输入**：点击输入框，显示键盘，输入用户名
- **密码输入**：点击输入框，显示键盘，输入密码
  - 点击显示/隐藏图标，切换密码可见性
- **登录按钮**：
  - 点击后显示加载动画
  - 登录成功后跳转到主屏幕
  - 登录失败显示错误提示
- **用户协议**：
  - 点击复选框，接受/撤销协议
  - 点击链接文本，打开协议详情页面

#### 4.1.3 状态管理

- **初始状态**：空白输入框，禁用登录按钮
- **输入状态**：填写用户名和密码后，启用登录按钮
- **加载状态**：登录按钮显示加载动画，禁用所有输入
- **错误状态**：显示错误消息，重新启用输入
- **协议状态**：未接受协议时禁用登录按钮

### 4.2 主屏幕

主屏幕是用户登录后的主界面，显示连接状态和提供主要功能入口。

#### 4.2.1 布局设计

![主屏幕布局](placeholder_for_main_screen_image)

- **顶部**：
  - 导航栏（带应用标题）
  - 菜单按钮（打开侧边栏）
- **中部**：
  - 连接状态显示（已连接/未连接/连接中）
  - 当前连接的服务器信息
  - 连接时间显示
  - 流量统计显示（上传/下载速度）
- **底部**：
  - 连接/断开按钮
  - 服务器列表展开/折叠按钮

#### 4.2.2 侧边栏菜单

- 用户信息显示（姓名、部门、职位）
- 菜单项：
  - 主页
  - 服务器列表
  - 设置
  - 日志
  - 关于
  - 退出登录

#### 4.2.3 交互设计

- **连接/断开按钮**：
  - 未连接状态：点击连接到选定服务器
  - 已连接状态：点击断开连接
  - 连接中状态：显示进度，不可点击
- **服务器列表**：
  - 点击展开/折叠按钮，显示/隐藏服务器列表
  - 点击服务器项，选择该服务器
- **侧边栏**：
  - 点击菜单按钮，打开侧边栏
  - 点击菜单项，导航到相应页面
  - 点击退出登录，返回登录页面

#### 4.2.4 状态管理

- **未连接状态**：
  - 状态显示为"未连接"（灰色）
  - 按钮显示"连接"
  - 不显示连接时间和服务器信息
- **连接中状态**：
  - 状态显示为"连接中"（橙色）
  - 按钮显示加载动画
  - 显示正在连接的服务器信息
- **已连接状态**：
  - 状态显示为"已连接"（绿色）
  - 按钮显示"断开"
  - 显示已连接的服务器信息和连接时间
  - 显示实时流量统计

### 4.3 服务器列表屏幕

服务器列表屏幕显示所有可用的服务器，并允许用户选择和连接。

#### 4.3.1 布局设计

![服务器列表屏幕布局](placeholder_for_server_list_screen_image)

- **顶部**：
  - 导航栏（带"服务器列表"标题）
  - 返回按钮
  - 搜索按钮
- **搜索栏**：
  - 搜索输入框
  - 排序选项（按名称、延迟）
- **服务器列表**：
  - 服务器卡片（每个显示名称、地址、延迟）
  - 选中状态指示
  - 连接按钮

#### 4.3.2 交互设计

- **搜索**：
  - 点击搜索按钮，显示/隐藏搜索栏
  - 输入关键词，实时筛选服务器列表
- **排序**：
  - 点击排序选项，切换排序方式
  - 支持按名称和延迟排序
- **服务器选择**：
  - 点击服务器卡片，选择该服务器
  - 选中的服务器卡片高亮显示
- **连接**：
  - 点击连接按钮，连接到选中的服务器
  - 连接成功后返回主屏幕

#### 4.3.3 服务器卡片设计

每个服务器卡片包含：
- 服务器名称（主要文本）
- 服务器地址和端口（次要文本）
- 延迟显示（带颜色编码：绿色=良好，橙色=中等，红色=较差）
- 状态指示器（已选择/已连接）
- 连接按钮

### 4.4 设置屏幕

设置屏幕允许用户配置应用程序和个人信息。

#### 4.4.1 布局设计

![设置屏幕布局](placeholder_for_settings_screen_image)

- **顶部**：
  - 导航栏（带"设置"标题）
  - 返回按钮
- **用户信息部分**：
  - 姓名输入框
  - 部门输入框
  - 职位输入框
- **应用设置部分**：
  - 语言选择（中文/英文）
  - 主题选择（浅色/深色/系统）
- **协议部分**：
  - 查看用户协议
  - 查看隐私政策
  - 撤销协议同意
- **其他操作**：
  - 退出登录按钮

#### 4.4.2 交互设计

- **用户信息**：
  - 点击输入框，编辑相应信息
  - 失去焦点时自动保存
- **语言选择**：
  - 点击语言选项，切换应用语言
  - 切换后立即生效
- **主题选择**：
  - 点击主题选项，切换应用主题
  - 切换后立即生效
- **协议操作**：
  - 点击查看链接，打开相应协议页面
  - 点击撤销按钮，撤销协议同意并返回登录页面
- **退出操作**：
  - 点击退出登录按钮，清除用户凭证并返回登录页面

### 4.5 日志屏幕

日志屏幕显示应用程序的日志记录，用于调试和问题排查。

#### 4.5.1 布局设计

![日志屏幕布局](placeholder_for_logs_screen_image)

- **顶部**：
  - 导航栏（带"日志"标题）
  - 返回按钮
  - 筛选按钮
  - 清除按钮
- **筛选栏**：
  - 日志级别选择（全部、调试、信息、警告、错误）
  - 搜索输入框
- **日志列表**：
  - 日志条目（时间戳、级别、消息）
  - 颜色编码（调试=灰色，信息=蓝色，警告=橙色，错误=红色）

#### 4.5.2 交互设计

- **筛选**：
  - 点击筛选按钮，显示/隐藏筛选栏
  - 选择日志级别，筛选显示的日志
  - 输入关键词，实时筛选日志内容
- **清除**：
  - 点击清除按钮，清空日志列表
  - 显示确认对话框
- **日志查看**：
  - 支持滚动查看所有日志
  - 点击日志条目，显示完整内容
  - 支持复制日志内容

### 4.6 关于屏幕

关于屏幕显示应用程序的信息和版本。

#### 4.6.1 布局设计

![关于屏幕布局](placeholder_for_about_screen_image)

- **顶部**：
  - 导航栏（带"关于"标题）
  - 返回按钮
- **应用信息**：
  - 应用logo
  - 应用名称
  - 版本号
  - 版权信息
- **链接部分**：
  - 官方网站
  - 技术支持
  - 反馈问题

#### 4.6.2 交互设计

- **链接**：
  - 点击链接，打开相应网页
  - 使用系统浏览器打开

## 5. 组件设计

### 5.1 连接状态组件

连接状态组件显示当前WAN连接的状态。

#### 5.1.1 设计规范

- **尺寸**：根据内容自适应，最小高度80dp
- **边距**：内边距16dp，外边距16dp
- **边框**：1dp，颜色根据状态变化
- **圆角**：8dp

#### 5.1.2 状态变化

- **未连接状态**：
  - 图标：云断开图标
  - 文本："未连接"
  - 颜色：灰色（#999999）
- **连接中状态**：
  - 图标：云同步图标（带动画）
  - 文本："连接中..."
  - 颜色：橙色（#FF9500）
- **已连接状态**：
  - 图标：云连接图标
  - 文本："已连接"
  - 颜色：绿色（#33DB87）
  - 附加信息：服务器名称、连接时间

### 5.2 服务器列表项组件

服务器列表项组件显示单个服务器的信息。

#### 5.2.1 设计规范

- **尺寸**：高度72dp，宽度填充父容器
- **边距**：内边距16dp，外边距（上下4dp，左右16dp）
- **边框**：选中时2dp，未选中时0dp
- **圆角**：8dp
- **阴影**：选中时4dp，未选中时1dp

#### 5.2.2 内容布局

- **左侧**：
  - 服务器名称（主要文本，16sp）
  - 服务器地址和端口（次要文本，14sp）
- **右侧**：
  - 延迟显示（带颜色编码）
  - 连接按钮（36dp高）

#### 5.2.3 状态变化

- **普通状态**：
  - 背景色：白色
  - 边框：无
  - 阴影：1dp
- **选中状态**：
  - 背景色：白色
  - 边框：主色调（#FF7A00）
  - 阴影：4dp
- **已连接状态**：
  - 连接按钮文本："已连接"
  - 连接按钮禁用

### 5.3 流量统计组件

流量统计组件显示当前的网络流量情况。

#### 5.3.1 设计规范

- **尺寸**：高度120dp，宽度填充父容器
- **边距**：内边距16dp，外边距16dp
- **边框**：无
- **圆角**：8dp
- **背景**：白色，阴影2dp

#### 5.3.2 内容布局

- **上传部分**：
  - 上传图标
  - "上传"文本
  - 上传速度（带单位）
- **下载部分**：
  - 下载图标
  - "下载"文本
  - 下载速度（带单位）
- **图表部分**：
  - 流量历史图表（线图）
  - 时间轴（最近5分钟）

### 5.4 日志查看组件

日志查看组件显示应用程序的日志记录。

#### 5.4.1 设计规范

- **尺寸**：高度填充可用空间，宽度填充父容器
- **边距**：内边距0dp，外边距16dp
- **边框**：无
- **背景**：白色

#### 5.4.2 内容布局

- **日志条目**：
  - 时间戳（格式：HH:MM:SS）
  - 日志级别（图标或文本）
  - 日志消息
- **颜色编码**：
  - 调试：灰色（#999999）
  - 信息：蓝色（#007AFF）
  - 警告：橙色（#FF9500）
  - 错误：红色（#FF3B30）

### 5.5 用户信息表单组件

用户信息表单组件用于编辑用户的个人信息。

#### 5.5.1 设计规范

- **尺寸**：高度根据内容自适应，宽度填充父容器
- **边距**：内边距16dp，外边距16dp
- **边框**：无
- **背景**：白色

#### 5.5.2 内容布局

- **姓名字段**：
  - 标签："姓名"
  - 输入框：单行文本
- **部门字段**：
  - 标签："部门"
  - 输入框：单行文本
- **职位字段**：
  - 标签："职位"
  - 输入框：单行文本
- **保存按钮**：
  - 文本："保存"
  - 颜色：主色调（#FF7A00）

## 6. 交互设计

### 6.1 导航模式

应用采用侧边栏导航模式，提供以下导航路径：

- 登录屏幕 → 主屏幕
- 主屏幕 → 服务器列表屏幕
- 主屏幕 → 设置屏幕
- 主屏幕 → 日志屏幕
- 主屏幕 → 关于屏幕
- 任何屏幕 → 登录屏幕（退出登录）

### 6.2 手势支持

- **滑动**：
  - 从左向右滑动，打开侧边栏
  - 上下滑动，滚动内容
- **点击**：
  - 点击按钮，触发相应操作
  - 点击列表项，选择项目
- **长按**：
  - 长按日志条目，显示复制选项

### 6.3 动画效果

- **过渡动画**：
  - 页面切换使用淡入淡出效果
  - 侧边栏使用滑入滑出效果
- **状态动画**：
  - 连接状态变化使用淡入淡出效果
  - 连接中状态使用旋转动画
- **交互反馈**：
  - 按钮点击使用波纹效果
  - 列表项选择使用高亮效果

### 6.4 错误处理

- **网络错误**：
  - 显示错误提示，提供重试选项
  - 自动重连机制
- **认证错误**：
  - 显示错误消息，返回登录页面
- **服务器错误**：
  - 显示错误详情，提供反馈选项
- **输入错误**：
  - 实时验证，显示错误提示
  - 禁用提交按钮直到错误修复

## 7. 响应式设计

### 7.1 屏幕适配

UI将适应不同的屏幕尺寸和方向：

- **手机**：
  - 竖屏：单列布局
  - 横屏：双列布局（导航+内容）
- **平板**：
  - 竖屏：双列布局（导航+内容）
  - 横屏：三列布局（导航+内容+详情）
- **桌面**：
  - 固定宽度窗口，最小尺寸800x600
  - 可调整大小，最大尺寸1200x800

### 7.2 布局调整

- **弹性布局**：
  - 使用Flex布局适应不同屏幕尺寸
  - 关键元素保持固定大小
- **内容优先级**：
  - 小屏幕优先显示关键信息
  - 大屏幕显示更多详细信息
- **导航调整**：
  - 小屏幕使用底部导航栏
  - 大屏幕使用侧边栏导航

## 8. 本地化

### 8.1 支持的语言

- 中文（简体）
- 英文

### 8.2 本地化资源

- **文本**：
  - 所有UI文本存储在语言资源文件中
  - 支持动态切换语言
- **日期和时间**：
  - 根据语言环境格式化
  - 支持12/24小时制
- **数字和单位**：
  - 根据语言环境格式化
  - 支持不同的度量单位

### 8.3 自动检测

- 应用启动时检测系统语言
- 自动选择相应的语言资源
- 允许用户手动覆盖语言选择

### 8.4 应用关闭行为

当用户关闭应用程序时，将显示确认对话框。

**功能特性：**

- 关闭确认对话框
- 最小化到系统托盘选项
- 退出应用选项
- 取消关闭选项

**交互逻辑：**

1. 用户点击关闭按钮
2. 显示确认对话框，提供“取消”、“最小化”和“退出”选项
3. 用户选择“最小化”，应用程序最小化到系统托盘
4. 用户选择“退出”，应用程序执行以下操作：
   - 如果VPN已连接，先断开VPN连接，并显示“断开连接中...”状态
   - 通过HTTP shutdown接口优雅地停止后端服务
   - 关闭应用程序
5. 用户选择“取消”，关闭对话框并继续使用应用

**实现细节：**

- 使用WindowManager的setPreventClose方法拦截窗口关闭事件
- 实现WindowCloseHandler类处理关闭事件
- 使用HTTP接口停止后端服务，而不是直接杀死进程
- 在关闭前添加适当的延时，确保资源正确释放

## 9. 实现计划

### 9.1 阶段划分

1. **第一阶段：项目设置和核心服务**
   - 设置项目结构和依赖
   - 实现API和WebSocket服务
   - 创建数据模型

2. **第二阶段：基本UI实现**
   - 实现登录屏幕
   - 创建主屏幕结构
   - 实现服务器列表屏幕

3. **第三阶段：完成UI实现**
   - 实现设置屏幕
   - 创建日志屏幕
   - 实现关于屏幕

4. **第四阶段：完善和测试**
   - 添加动画和过渡效果
   - 实现国际化
   - 测试和修复问题
   - 优化UI

### 9.2 优先级

1. **高优先级**：
   - 登录功能
   - 连接/断开功能
   - 服务器列表和选择
   - 连接状态显示

2. **中优先级**：
   - 用户信息管理
   - 设置功能
   - 流量统计显示
   - 本地化支持

3. **低优先级**：
   - 日志查看
   - 动画效果
   - 主题切换
   - 关于页面

### 9.3 时间线

- **第1周**：项目设置和核心服务
- **第2周**：基本UI实现
- **第3周**：完成UI实现
- **第4周**：完善和测试

## 10. 测试计划

### 10.1 单元测试

- 测试API服务
- 测试WebSocket服务
- 测试数据模型

### 10.2 组件测试

- 测试UI组件的渲染
- 测试组件的交互
- 测试组件的状态管理

### 10.3 集成测试

- 测试屏幕之间的导航
- 测试与后端的通信
- 测试完整的用户流程

### 10.4 兼容性测试

- 测试不同屏幕尺寸
- 测试不同操作系统
- 测试不同语言环境

## 11. 当前实现进度

以下是当前实现的进度和状态：

### 11.1 已完成的部分

1. **项目结构设置**
   - 创建了完整的项目目录结构
   - 配置了必要的依赖项
   - 设置了分析选项和构建配置

2. **核心服务实现**
   - API服务（与后端通信）
   - WebSocket服务（实时状态更新）
   - 认证服务（用户登录和凭证管理）
   - 日志服务（日志记录和管理）
   - 后端服务（管理后端进程）
   - 通知服务（在UI上显示通知）
   - 主题服务（管理应用主题）
   - 语言服务（管理应用语言）
   - 窗口关闭处理服务（管理应用关闭行为）

3. **数据模型实现**
   - 服务器模型
   - 连接状态模型
   - 流量统计模型
   - 用户信息模型
   - 日志条目模型

4. **工具类实现**
   - 常量定义
   - API异常处理
   - 格式化工具
   - 主题配置
   - 多线程处理工具

5. **UI组件实现**
   - 登录屏幕实现
   - 主屏幕实现
   - 服务器列表屏幕实现
   - 设置屏幕实现
   - 日志屏幕实现
   - 关于屏幕实现
   - 连接状态组件实现
   - 流量统计组件实现
   - 服务器列表组件实现
   - 用户信息表单组件实现
   - 日志查看组件实现
   - 通知组件实现

6. **特性实现**
   - 用户认证和登录
   - VPN连接和断开
   - 服务器列表管理
   - 用户信息管理
   - 主题切换
   - 语言切换
   - 应用关闭行为
   - 后端服务管理

### 11.2 进行中的部分

1. **界面优化**
   - 界面元素对齐和间距调整
   - 界面动画和过渡效果添加
   - 界面响应式调整

2. **性能优化**
   - 内存使用优化
   - 界面渲染性能优化
   - 网络请求缓存优化

3. **兼容性测试**
   - 不同屏幕尺寸测试
   - 不同系统版本测试
   - 不同语言环境测试

### 11.3 待实现的部分

1. **高级功能**
   - 自动重连机制
   - 网络识别与切换
   - 流量统计导出

2. **其他平台支持**
   - iOS平台支持
   - macOS平台支持
   - Android平台支持

3. **安全增强**
   - 数据加密存储
   - 安全断开机制
   - 异常检测与恢复

## 12. 结论

本设计文档详细描述了ItForce客户端Flutter UI的设计和实现计划。通过参考Android版本的实现，确保在不同平台上提供一致的用户体验。UI将使用Flutter框架实现，支持跨平台部署，初始阶段将优先支持Windows平台。

设计遵循Material Design原则，同时保持与Android版本一致的视觉风格和交互模式。实现计划分为四个阶段，从核心服务到完整UI，最后进行完善和测试。

目前，我们已经完成了大部分功能的实现，包括所有核心服务、数据模型、UI组件和主要功能。特别是，我们实现了与后端的无缝集成，确保了前后端交互的正确性和效率。我们还增强了应用的关闭行为，提供了更好的用户体验。

接下来，我们将继续优化界面、提高性能并进行兼容性测试。同时，我们还将实现一些高级功能，如自动重连机制、网络识别与切换等，并计划支持更多平台。

通过这个设计和实现，我们已经创建了一个功能完善、用户友好的ItForce客户端UI，为用户提供流畅、直观的使用体验。
