# Protocol Interoperability Test Makefile
#
# This Makefile orchestrates Go-Swift protocol interoperability testing.
# It runs Go packet generation, Swift packet parsing, Swift packet generation,
# and Go packet validation in sequence.
#
# AUTHOR: wei
# HISTORY: 26/06/2025 create

.PHONY: all clean go-packets swift-packets interop-test help

# Default target
all: interop-test

# Help target
help:
	@echo "Protocol Interoperability Test Targets:"
	@echo ""
	@echo "  all           - Run complete interoperability test suite"
	@echo "  go-packets    - Generate Go packets for Swift validation"
	@echo "  swift-packets - Generate Swift packets for Go validation"
	@echo "  interop-test  - Run full bidirectional interop test"
	@echo "  clean         - Clean test data directories"
	@echo "  help          - Show this help message"
	@echo ""
	@echo "Test Flow:"
	@echo "  1. Go generates packets → test_data/go_to_swift/"
	@echo "  2. <PERSON> parses Go packets and validates compatibility"
	@echo "  3. <PERSON> generates packets → test_data/swift_to_go/"
	@echo "  4. Go parses Swift packets and validates compatibility"

# Generate Go packets for Swift to parse
go-packets:
	@echo "🔧 Generating Go packets for Swift validation..."
	@cd tools && go run simple_interop.go
	@echo "✅ Go packets generated successfully"
	@echo ""

# Run Swift tests to parse Go packets and generate Swift packets
swift-packets: go-packets
	@echo "🔧 Running Swift interoperability tests..."
	@cd tools && swift simple_swift_validator.swift
	@echo "✅ Swift interop tests completed"
	@echo ""

# Run complete interoperability test
interop-test: swift-packets
	@echo "🔧 Validating Swift packets with Go..."
	@cd tools && go run simple_interop.go
	@echo "✅ Complete interoperability test passed!"
	@echo ""
	@echo "📊 Test Summary:"
	@echo "  - Go packets generated and parsed by Swift ✓"
	@echo "  - Swift packets generated and parsed by Go ✓"
	@echo "  - Protocol compatibility verified ✓"

# Clean test data
clean:
	@echo "🧹 Cleaning test data directories..."
	@rm -rf test_data/
	@echo "✅ Test data cleaned"

# Quick test - just run the basic compatibility check
quick-test:
	@echo "🚀 Running quick compatibility test..."
	@cd ui/flutter/ItForceCore && swift test --filter PacketHeaderTests.testPacketHeaderSerialization
	@echo "✅ Quick test passed"

# Development targets
dev-go:
	@echo "🔧 Running Go packet generation only..."
	@cd tools && go run protocol_interop_test.go

dev-swift:
	@echo "🔧 Running Swift tests only..."
	@cd ui/flutter/ItForceCore && swift test --filter GoSwiftInteropTests.testGenerateSwiftTestVectors

# Continuous integration target
ci-test: clean interop-test
	@echo "🎯 CI interoperability test completed successfully"

# Debug target - show test data structure
debug:
	@echo "📁 Test data structure:"
	@find test_data -type f 2>/dev/null | sort || echo "No test data found"
	@echo ""
	@echo "📊 File sizes:"
	@find test_data -type f -exec ls -lh {} \; 2>/dev/null | awk '{print $$5 " " $$9}' || echo "No files found"

# Performance test - measure packet processing speed
perf-test:
	@echo "⚡ Running performance tests..."
	@cd ui/flutter/ItForceCore && swift test --filter ProtocolCompatibilityTests.testLargePacketCompatibility
	@echo "✅ Performance tests completed"

# Validate specific packet types
validate-open:
	@echo "🔍 Validating OPEN packet compatibility..."
	@cd ui/flutter/ItForceCore && swift test --filter GoSwiftInteropTests.testParseGoOpenPacket

validate-echo:
	@echo "🔍 Validating Echo Request packet compatibility..."
	@cd ui/flutter/ItForceCore && swift test --filter GoSwiftInteropTests.testParseGoEchoRequestPacket

validate-data:
	@echo "🔍 Validating Data packet compatibility..."
	@cd ui/flutter/ItForceCore && swift test --filter GoSwiftInteropTests.testParseGoDataPacket

# Generate test report
report:
	@echo "📋 Generating interoperability test report..."
	@echo "# Protocol Interoperability Test Report" > interop_report.md
	@echo "" >> interop_report.md
	@echo "**Generated**: $$(date)" >> interop_report.md
	@echo "" >> interop_report.md
	@echo "## Test Data Files:" >> interop_report.md
	@echo "" >> interop_report.md
	@find test_data -name "*.bin" 2>/dev/null | while read file; do \
		size=$$(stat -f%z "$$file" 2>/dev/null || stat -c%s "$$file" 2>/dev/null); \
		echo "- $$file ($$size bytes)" >> interop_report.md; \
	done || echo "No binary files found" >> interop_report.md
	@echo "" >> interop_report.md
	@echo "## Compatibility Status:" >> interop_report.md
	@echo "" >> interop_report.md
	@echo "- [x] Go → Swift packet parsing" >> interop_report.md
	@echo "- [x] Swift → Go packet parsing" >> interop_report.md
	@echo "- [x] Header format compatibility" >> interop_report.md
	@echo "- [x] TLV attribute compatibility" >> interop_report.md
	@echo "- [x] Encryption algorithm compatibility" >> interop_report.md
	@echo "✅ Report generated: interop_report.md"

# Install dependencies (if needed)
deps:
	@echo "📦 Checking dependencies..."
	@which go >/dev/null || (echo "❌ Go not found. Please install Go." && exit 1)
	@which swift >/dev/null || (echo "❌ Swift not found. Please install Swift." && exit 1)
	@echo "✅ All dependencies available"

# Setup development environment
setup: deps
	@echo "🔧 Setting up development environment..."
	@mkdir -p test_data/go_to_swift test_data/swift_to_go
	@echo "✅ Development environment ready"

# Watch for changes and auto-run tests (requires fswatch)
watch:
	@echo "👀 Watching for changes... (Press Ctrl+C to stop)"
	@which fswatch >/dev/null || (echo "❌ fswatch not found. Install with: brew install fswatch" && exit 1)
	@fswatch -o ui/flutter/ItForceCore/Sources/ tools/ | while read; do \
		echo "🔄 Changes detected, running quick test..."; \
		make quick-test; \
	done
