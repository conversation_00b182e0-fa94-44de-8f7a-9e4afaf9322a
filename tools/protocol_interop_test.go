/**
 * FILE: protocol_interop_test.go
 *
 * DESCRIPTION:
 *     Go-Swift protocol interoperability test tool.
 *     Generates test packets using Go backend and writes them to files for Swift to parse.
 *     Also reads Swift-generated packets and validates them with Go parser.
 *
 * AUTHOR: wei
 * HISTORY: 26/06/2025 create
 */

package main

import (
	"encoding/hex"
	"fmt"
	"io/ioutil"
	"log"
	"mobile/internal/protocol/tunnel"
	"os"
	"path/filepath"
)

const (
	testDataDir  = "test_data"
	goToSwiftDir = "go_to_swift"
	swiftToGoDir = "swift_to_go"
)

func main() {
	// Create test directories
	if err := createTestDirectories(); err != nil {
		log.Fatalf("Failed to create test directories: %v", err)
	}

	// Generate Go packets for Swift to parse
	if err := generateGoPackets(); err != nil {
		log.Fatalf("Failed to generate Go packets: %v", err)
	}

	// Validate Swift packets (if they exist)
	if err := validateSwiftPackets(); err != nil {
		log.Printf("Warning: Failed to validate Swift packets: %v", err)
	}

	fmt.Println("Protocol interoperability test completed successfully!")
}

func createTestDirectories() error {
	dirs := []string{
		testDataDir,
		filepath.Join(testDataDir, goToSwiftDir),
		filepath.Join(testDataDir, swiftToGoDir),
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %w", dir, err)
		}
	}

	return nil
}

func generateGoPackets() error {
	fmt.Println("Generating Go packets for Swift validation...")

	// Test case 1: OPEN packet
	openPacket, err := tunnel.CreateOpenPacket("testuser", "testpass", 1420, 2) // AES encryption
	if err != nil {
		return fmt.Errorf("failed to create OPEN packet: %w", err)
	}

	if err := writePacketToFile("open_packet.bin", openPacket); err != nil {
		return fmt.Errorf("failed to write OPEN packet: %w", err)
	}

	// Test case 2: Echo Request packet
	echoPacket, err := tunnel.CreateEchoRequestPacket(1234, 0x56789ABC, 30, 25, 35, 2)
	if err != nil {
		return fmt.Errorf("failed to create Echo Request packet: %w", err)
	}

	if err := writePacketToFile("echo_request_packet.bin", echoPacket); err != nil {
		return fmt.Errorf("failed to write Echo Request packet: %w", err)
	}

	// Test case 3: Data packet
	dataPacket := createDataPacket()
	if err := writePacketToFile("data_packet.bin", dataPacket); err != nil {
		return fmt.Errorf("failed to write Data packet: %w", err)
	}

	// Generate test vectors file
	if err := generateTestVectors(); err != nil {
		return fmt.Errorf("failed to generate test vectors: %w", err)
	}

	fmt.Println("Go packets generated successfully!")
	return nil
}

func writePacketToFile(filename string, packet *tunnel.Packet) error {
	// Serialize packet to binary format
	data := make([]byte, 8+len(packet.Data))

	// Write header
	data[0] = byte(packet.Header.Type)
	data[1] = byte(packet.Header.Encrypt)
	data[2] = byte(packet.Header.SID >> 8)
	data[3] = byte(packet.Header.SID & 0xFF)
	data[4] = byte(packet.Header.Token >> 24)
	data[5] = byte(packet.Header.Token >> 16)
	data[6] = byte(packet.Header.Token >> 8)
	data[7] = byte(packet.Header.Token & 0xFF)

	// Write data
	copy(data[8:], packet.Data)

	// Write to file
	filePath := filepath.Join(testDataDir, goToSwiftDir, filename)
	return ioutil.WriteFile(filePath, data, 0644)
}

func createDataPacket() *tunnel.Packet {
	// Create a simple IPv4 packet
	ipPacket := []byte{
		0x45, 0x00, 0x00, 0x1C, // Version, IHL, ToS, Total Length
		0x00, 0x01, 0x40, 0x00, // ID, Flags, Fragment Offset
		0x40, 0x01, 0x00, 0x00, // TTL, Protocol, Checksum
		0xC0, 0xA8, 0x01, 0x64, // Source IP: ***********00
		0xC0, 0xA8, 0x01, 0x01, // Dest IP: ***********
		0x48, 0x65, 0x6C, 0x6C, // Payload: "Hell"
		0x6F, 0x21, 0x00, 0x00, // Payload: "o!" + padding
	}

	return tunnel.NewPacket(0x14, 2, 1234, 0x56789ABC, ipPacket) // Data packet with AES encryption
}

func generateTestVectors() error {
	vectors := []TestVector{
		{
			Name:        "OPEN Packet",
			Description: "Authentication packet with username=testuser, password=testpass, MTU=1420, encryption=AES",
			Filename:    "open_packet.bin",
			ExpectedFields: map[string]interface{}{
				"type":      0x13,
				"encrypt":   0x02,
				"sessionID": 0,
				"token":     0,
			},
		},
		{
			Name:        "Echo Request Packet",
			Description: "Echo request with sessionID=1234, token=0x56789ABC",
			Filename:    "echo_request_packet.bin",
			ExpectedFields: map[string]interface{}{
				"type":      0x15,
				"encrypt":   0x02,
				"sessionID": 1234,
				"token":     0x56789ABC,
			},
		},
		{
			Name:        "Data Packet",
			Description: "IPv4 data packet with sample payload",
			Filename:    "data_packet.bin",
			ExpectedFields: map[string]interface{}{
				"type":      0x14,
				"encrypt":   0x02,
				"sessionID": 1234,
				"token":     0x56789ABC,
			},
		},
	}

	// Write test vectors as JSON
	content := "# Go-Swift Protocol Interoperability Test Vectors\n\n"
	content += "This directory contains binary packet files generated by Go backend for Swift validation.\n\n"
	content += "## Test Cases:\n\n"

	for _, vector := range vectors {
		content += fmt.Sprintf("### %s\n", vector.Name)
		content += fmt.Sprintf("- **File**: %s\n", vector.Filename)
		content += fmt.Sprintf("- **Description**: %s\n", vector.Description)
		content += "- **Expected Fields**:\n"
		for field, value := range vector.ExpectedFields {
			content += fmt.Sprintf("  - %s: %v\n", field, value)
		}
		content += "\n"
	}

	content += "## Usage:\n\n"
	content += "1. Run this Go tool to generate test packets\n"
	content += "2. Use Swift tests to parse and validate the generated packets\n"
	content += "3. Swift tests should generate their own packets in ../swift_to_go/\n"
	content += "4. Run this tool again to validate Swift-generated packets\n"

	filePath := filepath.Join(testDataDir, goToSwiftDir, "README.md")
	return ioutil.WriteFile(filePath, []byte(content), 0644)
}

func validateSwiftPackets() error {
	fmt.Println("Validating Swift-generated packets...")

	swiftDir := filepath.Join(testDataDir, swiftToGoDir)
	files, err := ioutil.ReadDir(swiftDir)
	if err != nil {
		return fmt.Errorf("failed to read Swift packet directory: %w", err)
	}

	if len(files) == 0 {
		fmt.Println("No Swift packets found for validation (this is expected on first run)")
		return nil
	}

	for _, file := range files {
		if filepath.Ext(file.Name()) == ".bin" {
			if err := validateSwiftPacket(filepath.Join(swiftDir, file.Name())); err != nil {
				return fmt.Errorf("failed to validate Swift packet %s: %w", file.Name(), err)
			}
		}
	}

	fmt.Println("Swift packets validated successfully!")
	return nil
}

func validateSwiftPacket(filePath string) error {
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read packet file: %w", err)
	}

	if len(data) < 8 {
		return fmt.Errorf("packet too short: %d bytes", len(data))
	}

	// Parse header
	packetType := data[0]
	encrypt := data[1]
	sessionID := uint16(data[2])<<8 | uint16(data[3])
	token := uint32(data[4])<<24 | uint32(data[5])<<16 | uint32(data[6])<<8 | uint32(data[7])

	fmt.Printf("Validating %s:\n", filepath.Base(filePath))
	fmt.Printf("  Type: 0x%02X, Encrypt: 0x%02X, SID: %d, Token: 0x%08X\n",
		packetType, encrypt, sessionID, token)
	fmt.Printf("  Data length: %d bytes\n", len(data)-8)
	fmt.Printf("  Data (hex): %s\n", hex.EncodeToString(data[8:]))

	// Basic validation
	if packetType < 0x11 || packetType > 0x20 {
		return fmt.Errorf("invalid packet type: 0x%02X", packetType)
	}

	if encrypt > 2 {
		return fmt.Errorf("invalid encryption method: 0x%02X", encrypt)
	}

	fmt.Printf("  ✓ Packet validation passed\n\n")
	return nil
}

type TestVector struct {
	Name           string
	Description    string
	Filename       string
	ExpectedFields map[string]interface{}
}
