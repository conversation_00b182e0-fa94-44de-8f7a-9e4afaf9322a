/**
 * FILE: cross_platform_protocol_validator.go
 *
 * DESCRIPTION:
 *     Cross-platform protocol validation tool.
 *     Generates various packet types for Android parsing and validates Android-generated packets.
 *     Ensures protocol compatibility between Go backend and Android implementation.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create cross-platform protocol validation
 */

package main

import (
	"crypto/md5"
	"fmt"
	"os"
	"path/filepath"
	"time"
	"unsafe"
)

// PacketType represents different packet types in the protocol
type PacketType uint8

const (
	PacketTypeOpenReject    PacketType = 0x11
	PacketTypeOpenAck       PacketType = 0x12
	PacketTypeOpen          PacketType = 0x13
	PacketTypeData          PacketType = 0x14
	PacketTypeEchoRequest   PacketType = 0x15
	PacketTypeEchoResponse  PacketType = 0x16
	PacketTypeClose         PacketType = 0x17
	PacketTypeDataEncrypted PacketType = 0x18
	PacketTypeDataDup       PacketType = 0x19
	PacketTypeDataEncDup    PacketType = 0x20
)

// EncryptionMethod represents encryption methods
type EncryptionMethod uint8

const (
	EncryptionNone EncryptionMethod = 0x00
	EncryptionXOR  EncryptionMethod = 0x01
	EncryptionAES  EncryptionMethod = 0x02
)

// TLVAttributeType represents TLV attribute types
type TLVAttributeType uint8

const (
	TLVUsername TLVAttributeType = 0x01
	TLVPassword TLVAttributeType = 0x02
	TLVMTU      TLVAttributeType = 0x03
	TLVIP       TLVAttributeType = 0x04
	TLVDNS      TLVAttributeType = 0x05
	TLVGateway  TLVAttributeType = 0x06
	TLVNetmask  TLVAttributeType = 0x07
	TLVEncrypt  TLVAttributeType = 0x08
)

// PacketHeader represents the 8-byte packet header
type PacketHeader struct {
	Type      PacketType
	Encrypt   EncryptionMethod
	SessionID uint16
	Token     uint32
}

// TLVAttribute represents a TLV attribute
type TLVAttribute struct {
	Type   TLVAttributeType
	Length uint8
	Value  []byte
}

// TestPacket represents a complete test packet
type TestPacket struct {
	Name        string
	Description string
	Header      PacketHeader
	Attributes  []TLVAttribute
	RawData     []byte
}

// PacketGenerator generates test packets
type PacketGenerator struct {
	testDataDir string
}

// NewPacketGenerator creates a new packet generator
func NewPacketGenerator() *PacketGenerator {
	return &PacketGenerator{
		testDataDir: "test_data",
	}
}

// Initialize creates test directories
func (pg *PacketGenerator) Initialize() error {
	dirs := []string{
		pg.testDataDir,
		filepath.Join(pg.testDataDir, "go_to_android"),
		filepath.Join(pg.testDataDir, "android_to_go"),
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %v", dir, err)
		}
	}

	return nil
}

// GenerateTestPackets generates various test packets for Android validation
func (pg *PacketGenerator) GenerateTestPackets() error {
	fmt.Println("🔧 Generating test packets for Android validation...")

	testPackets := []TestPacket{
		pg.generateOpenPacket(),
		pg.generateOpenAckPacket(),
		pg.generateOpenRejectPacket(),
		pg.generateEchoRequestPacket(),
		pg.generateEchoResponsePacket(),
		pg.generateClosePacket(),
		pg.generateDataPacket(),
		pg.generateEncryptedDataPacket(),
	}

	for i, packet := range testPackets {
		filename := fmt.Sprintf("go_packet_%02d_%s.bin", i+1, packet.Name)
		filepath := filepath.Join(pg.testDataDir, "go_to_android", filename)

		if err := os.WriteFile(filepath, packet.RawData, 0644); err != nil {
			return fmt.Errorf("failed to write packet %s: %v", filename, err)
		}

		fmt.Printf("✅ Generated: %s (%d bytes) - %s\n", filename, len(packet.RawData), packet.Description)

		// Also generate metadata file
		metadataFile := filepath + ".meta"
		metadata := fmt.Sprintf("Name: %s\nDescription: %s\nType: 0x%02X\nEncrypt: 0x%02X\nSessionID: %d\nToken: %d\nSize: %d bytes\n",
			packet.Name, packet.Description, packet.Header.Type, packet.Header.Encrypt,
			packet.Header.SessionID, packet.Header.Token, len(packet.RawData))

		if err := os.WriteFile(metadataFile, []byte(metadata), 0644); err != nil {
			return fmt.Errorf("failed to write metadata %s: %v", metadataFile, err)
		}
	}

	return nil
}

// generateOpenPacket creates an OPEN packet
func (pg *PacketGenerator) generateOpenPacket() TestPacket {
	header := PacketHeader{
		Type:      PacketTypeOpen,
		Encrypt:   EncryptionXOR,
		SessionID: 0,
		Token:     0,
	}

	// Create TLV attributes in required order
	username := "testuser"
	password := "testpass"
	mtu := uint16(1500)

	// Encrypt password
	encryptedPassword := pg.encryptPassword(password, username)

	attributes := []TLVAttribute{
		{Type: TLVMTU, Length: 2, Value: []byte{byte(mtu >> 8), byte(mtu & 0xFF)}},
		{Type: TLVUsername, Length: uint8(len(username)), Value: []byte(username)},
		{Type: TLVPassword, Length: uint8(len(encryptedPassword)), Value: encryptedPassword},
	}

	rawData := pg.buildPacket(header, attributes)

	return TestPacket{
		Name:        "open",
		Description: "Client connection request with authentication",
		Header:      header,
		Attributes:  attributes,
		RawData:     rawData,
	}
}

// generateOpenAckPacket creates an OPEN_ACK packet
func (pg *PacketGenerator) generateOpenAckPacket() TestPacket {
	header := PacketHeader{
		Type:      PacketTypeOpenAck,
		Encrypt:   EncryptionNone,
		SessionID: 12345,
		Token:     0xABCDEF01,
	}

	// Server response with network configuration
	attributes := []TLVAttribute{
		{Type: TLVIP, Length: 4, Value: []byte{192, 168, 1, 100}},
		{Type: TLVGateway, Length: 4, Value: []byte{192, 168, 1, 1}},
		{Type: TLVNetmask, Length: 4, Value: []byte{255, 255, 255, 0}},
		{Type: TLVDNS, Length: 4, Value: []byte{8, 8, 8, 8}},
	}

	rawData := pg.buildPacket(header, attributes)

	return TestPacket{
		Name:        "open_ack",
		Description: "Server acknowledges connection with network config",
		Header:      header,
		Attributes:  attributes,
		RawData:     rawData,
	}
}

// generateOpenRejectPacket creates an OPEN_REJECT packet
func (pg *PacketGenerator) generateOpenRejectPacket() TestPacket {
	header := PacketHeader{
		Type:      PacketTypeOpenReject,
		Encrypt:   EncryptionNone,
		SessionID: 0,
		Token:     0,
	}

	// No attributes for reject packet
	rawData := pg.buildPacket(header, nil)

	return TestPacket{
		Name:        "open_reject",
		Description: "Server rejects connection request",
		Header:      header,
		Attributes:  nil,
		RawData:     rawData,
	}
}

// generateEchoRequestPacket creates an ECHO_REQUEST packet
func (pg *PacketGenerator) generateEchoRequestPacket() TestPacket {
	header := PacketHeader{
		Type:      PacketTypeEchoRequest,
		Encrypt:   EncryptionNone,
		SessionID: 12345,
		Token:     0xABCDEF01,
	}

	// Echo request with timestamp
	timestamp := time.Now().Unix()
	timestampBytes := []byte{
		byte(timestamp >> 56), byte(timestamp >> 48), byte(timestamp >> 40), byte(timestamp >> 32),
		byte(timestamp >> 24), byte(timestamp >> 16), byte(timestamp >> 8), byte(timestamp),
	}

	// Build packet with signature + timestamp
	headerData := pg.headerToBytes(header)
	signature := pg.calculateSignature(header)
	rawData := append(headerData, signature...)
	rawData = append(rawData, timestampBytes...)

	return TestPacket{
		Name:        "echo_request",
		Description: "Heartbeat request with timestamp",
		Header:      header,
		Attributes:  nil,
		RawData:     rawData,
	}
}

// generateEchoResponsePacket creates an ECHO_RESPONSE packet
func (pg *PacketGenerator) generateEchoResponsePacket() TestPacket {
	header := PacketHeader{
		Type:      PacketTypeEchoResponse,
		Encrypt:   EncryptionNone,
		SessionID: 12345,
		Token:     0xABCDEF01,
	}

	// Echo response with timestamp and delay info
	timestamp := time.Now().Unix()
	timestampBytes := []byte{
		byte(timestamp >> 56), byte(timestamp >> 48), byte(timestamp >> 40), byte(timestamp >> 32),
		byte(timestamp >> 24), byte(timestamp >> 16), byte(timestamp >> 8), byte(timestamp),
	}

	delayInfo := []byte{0x00, 0x10} // 16ms delay
	sdrtTag := []byte("SDRT")

	// Build packet with signature + timestamp + delay + SDRT
	headerData := pg.headerToBytes(header)
	signature := pg.calculateSignature(header)
	rawData := append(headerData, signature...)
	rawData = append(rawData, timestampBytes...)
	rawData = append(rawData, delayInfo...)
	rawData = append(rawData, sdrtTag...)

	return TestPacket{
		Name:        "echo_response",
		Description: "Heartbeat response with timing info",
		Header:      header,
		Attributes:  nil,
		RawData:     rawData,
	}
}

// generateClosePacket creates a CLOSE packet
func (pg *PacketGenerator) generateClosePacket() TestPacket {
	header := PacketHeader{
		Type:      PacketTypeClose,
		Encrypt:   EncryptionNone,
		SessionID: 12345,
		Token:     0xABCDEF01,
	}

	rawData := pg.buildPacket(header, nil)

	return TestPacket{
		Name:        "close",
		Description: "Connection close request",
		Header:      header,
		Attributes:  nil,
		RawData:     rawData,
	}
}

// generateDataPacket creates a DATA packet
func (pg *PacketGenerator) generateDataPacket() TestPacket {
	header := PacketHeader{
		Type:      PacketTypeData,
		Encrypt:   EncryptionNone,
		SessionID: 12345,
		Token:     0xABCDEF01,
	}

	// Sample IPv4 packet data
	ipv4Data := []byte{
		0x45, 0x00, 0x00, 0x3C, // Version, IHL, ToS, Total Length
		0x1C, 0x46, 0x40, 0x00, // Identification, Flags, Fragment Offset
		0x40, 0x06, 0x00, 0x00, // TTL, Protocol, Header Checksum
		0xC0, 0xA8, 0x01, 0x64, // Source IP: ***********00
		0xC0, 0xA8, 0x01, 0x01, // Destination IP: ***********
	}

	// Data packets don't have signature, just header + data
	headerData := pg.headerToBytes(header)
	rawData := append(headerData, ipv4Data...)

	return TestPacket{
		Name:        "data",
		Description: "IPv4 data packet",
		Header:      header,
		Attributes:  nil,
		RawData:     rawData,
	}
}

// generateEncryptedDataPacket creates an encrypted DATA packet
func (pg *PacketGenerator) generateEncryptedDataPacket() TestPacket {
	header := PacketHeader{
		Type:      PacketTypeDataEncrypted,
		Encrypt:   EncryptionXOR,
		SessionID: 12345,
		Token:     0xABCDEF01,
	}

	// Sample IPv4 packet data
	ipv4Data := []byte{
		0x45, 0x00, 0x00, 0x3C,
		0x1C, 0x46, 0x40, 0x00,
		0x40, 0x06, 0x00, 0x00,
		0xC0, 0xA8, 0x01, 0x64,
		0xC0, 0xA8, 0x01, 0x01,
	}

	// Encrypt the data using XOR (simplified)
	sessionKey := md5.Sum([]byte("testuser" + "testpass"))
	encryptedData := pg.xorEncrypt(ipv4Data, sessionKey[:])

	// Data packets don't have signature, just header + encrypted data
	headerData := pg.headerToBytes(header)
	rawData := append(headerData, encryptedData...)

	return TestPacket{
		Name:        "data_encrypted",
		Description: "XOR encrypted IPv4 data packet",
		Header:      header,
		Attributes:  nil,
		RawData:     rawData,
	}
}

// Helper functions

// encryptPassword encrypts password using AES-ECB with MD5("mw" + username) key
func (pg *PacketGenerator) encryptPassword(password, username string) []byte {
	// This is a simplified version - in real implementation use proper AES-ECB
	key := md5.Sum([]byte("mw" + username))

	// Pad password to 32 bytes
	paddedPassword := make([]byte, 32)
	copy(paddedPassword, []byte(password))

	// Simplified encryption (XOR with key for demo)
	encrypted := make([]byte, 16)
	for i := 0; i < 16; i++ {
		encrypted[i] = paddedPassword[i] ^ key[i%16]
	}

	return encrypted
}

// xorEncrypt encrypts data using XOR with session key (matches Go backend implementation)
func (pg *PacketGenerator) xorEncrypt(data, key []byte) []byte {
	if len(data) == 0 {
		return data
	}

	result := make([]byte, len(data))
	copy(result, data)

	// Process 8 bytes at a time using uint32 operations (consistent with Go backend)
	blockCount := len(result) / 8

	for i := 0; i < blockCount; i++ {
		// Treat data and key as uint32 arrays
		data32 := (*[2]uint32)(unsafe.Pointer(&result[i*8]))
		key32 := (*[2]uint32)(unsafe.Pointer(&key[0]))

		// XOR operation
		data32[0] ^= key32[0]
		data32[1] ^= key32[1]
	}

	// Process remaining bytes
	remain := len(result) % 8
	if remain > 0 {
		for i := 0; i < remain; i++ {
			result[len(result)-remain+i] ^= key[i]
		}
	}

	return result
}

// buildPacket builds a complete packet with header, signature, and TLV attributes
func (pg *PacketGenerator) buildPacket(header PacketHeader, attributes []TLVAttribute) []byte {
	// Build header
	headerData := pg.headerToBytes(header)

	// Calculate signature
	signature := pg.calculateSignature(header)

	// Build TLV data
	var tlvData []byte
	for _, attr := range attributes {
		tlvData = append(tlvData, byte(attr.Type))
		tlvData = append(tlvData, attr.Length)
		tlvData = append(tlvData, attr.Value...)
	}

	// Combine: header + signature + TLV data
	result := append(headerData, signature...)
	result = append(result, tlvData...)

	return result
}

// headerToBytes converts PacketHeader to byte array
func (pg *PacketGenerator) headerToBytes(header PacketHeader) []byte {
	return []byte{
		byte(header.Type),
		byte(header.Encrypt),
		byte(header.SessionID >> 8), byte(header.SessionID & 0xFF),
		byte(header.Token >> 24), byte(header.Token >> 16), byte(header.Token >> 8), byte(header.Token & 0xFF),
	}
}

// calculateSignature calculates MD5(header + "mw") signature
func (pg *PacketGenerator) calculateSignature(header PacketHeader) []byte {
	headerData := pg.headerToBytes(header)
	salt := []byte{109, 119} // ASCII values for 'm' and 'w'
	signatureData := append(headerData, salt...)
	signature := md5.Sum(signatureData)
	return signature[:]
}

// ValidateAndroidPackets validates packets generated by Android
func (pg *PacketGenerator) ValidateAndroidPackets() error {
	fmt.Println("🔍 Validating Android-generated packets...")

	androidDir := filepath.Join(pg.testDataDir, "android_to_go")
	files, err := os.ReadDir(androidDir)
	if err != nil {
		fmt.Printf("⚠️  No Android packets found in %s\n", androidDir)
		return nil
	}

	validCount := 0
	totalCount := 0

	for _, file := range files {
		if filepath.Ext(file.Name()) == ".bin" {
			totalCount++
			filePath := filepath.Join(androidDir, file.Name())

			if err := pg.validatePacketFile(filePath); err != nil {
				fmt.Printf("❌ %s: %v\n", file.Name(), err)
			} else {
				fmt.Printf("✅ %s: Valid\n", file.Name())
				validCount++
			}
		}
	}

	fmt.Printf("\n📊 Validation Summary: %d/%d packets valid\n", validCount, totalCount)

	if validCount == totalCount && totalCount > 0 {
		fmt.Println("🎉 All Android packets are valid!")
		return nil
	} else if totalCount == 0 {
		fmt.Println("ℹ️  No Android packets to validate")
		return nil
	} else {
		return fmt.Errorf("validation failed: %d/%d packets invalid", totalCount-validCount, totalCount)
	}
}

// validatePacketFile validates a single packet file
func (pg *PacketGenerator) validatePacketFile(filePath string) error {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read file: %v", err)
	}

	if len(data) < 8 {
		return fmt.Errorf("packet too short: %d bytes", len(data))
	}

	// Parse header
	header := PacketHeader{
		Type:      PacketType(data[0]),
		Encrypt:   EncryptionMethod(data[1]),
		SessionID: uint16(data[2])<<8 | uint16(data[3]),
		Token:     uint32(data[4])<<24 | uint32(data[5])<<16 | uint32(data[6])<<8 | uint32(data[7]),
	}

	// Validate packet type
	validTypes := []PacketType{
		PacketTypeOpenReject, PacketTypeOpenAck, PacketTypeOpen, PacketTypeData,
		PacketTypeEchoRequest, PacketTypeEchoResponse, PacketTypeClose,
		PacketTypeDataEncrypted, PacketTypeDataDup, PacketTypeDataEncDup,
	}

	validType := false
	for _, vt := range validTypes {
		if header.Type == vt {
			validType = true
			break
		}
	}

	if !validType {
		return fmt.Errorf("invalid packet type: 0x%02X", header.Type)
	}

	// For control packets (non-data), validate signature
	if header.Type != PacketTypeData && header.Type != PacketTypeDataEncrypted &&
		header.Type != PacketTypeDataDup && header.Type != PacketTypeDataEncDup {
		if len(data) < 24 { // header + signature
			return fmt.Errorf("control packet too short for signature: %d bytes", len(data))
		}

		expectedSignature := pg.calculateSignature(header)
		actualSignature := data[8:24]

		for i := 0; i < 16; i++ {
			if expectedSignature[i] != actualSignature[i] {
				return fmt.Errorf("signature mismatch at byte %d: expected 0x%02X, got 0x%02X",
					i, expectedSignature[i], actualSignature[i])
			}
		}
	}

	return nil
}

func main() {
	fmt.Println("=== Cross-Platform Protocol Validation Tool ===")
	fmt.Println()

	generator := NewPacketGenerator()

	// Initialize test directories
	if err := generator.Initialize(); err != nil {
		fmt.Printf("❌ Failed to initialize: %v\n", err)
		os.Exit(1)
	}

	// Generate test packets for Android
	if err := generator.GenerateTestPackets(); err != nil {
		fmt.Printf("❌ Failed to generate test packets: %v\n", err)
		os.Exit(1)
	}

	fmt.Println()

	// Validate Android-generated packets
	if err := generator.ValidateAndroidPackets(); err != nil {
		fmt.Printf("❌ Validation failed: %v\n", err)
		os.Exit(1)
	}

	fmt.Println()
	fmt.Println("=== Instructions for Android Testing ===")
	fmt.Println("1. Run this tool to generate Go packets in test_data/go_to_android/")
	fmt.Println("2. Create Android test that reads these packets and validates parsing")
	fmt.Println("3. Create Android test that generates packets and saves to test_data/android_to_go/")
	fmt.Println("4. Run this tool again to validate Android-generated packets")
	fmt.Println("5. Ensure all packets pass validation for full compatibility")
	fmt.Println()
	fmt.Println("✅ Cross-platform protocol validation complete!")
}
