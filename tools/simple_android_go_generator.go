/**
 * FILE: simple_android_go_generator.go
 *
 * DESCRIPTION:
 *     Simplified Android-Go protocol interoperability test tool.
 *     Generates test packets using Go backend for Android validation.
 *
 * AUTHOR: wei
 * HISTORY: 16/07/2025 create simplified Android-Go interop test
 */

package main

import (
	"crypto/md5"
	"encoding/binary"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"time"
)

const (
	testDataDir    = "../ui/flutter/android/app/src/test/resources"
	goToAndroidDir = "go_to_android"
	androidToGoDir = "android_to_go"
)

// Packet types
const (
	PacketTypeOpen        = 0x13
	PacketTypeOpenAck     = 0x12
	PacketTypeData        = 0x14
	PacketTypeDataEncrypt = 0x17
	PacketTypeEchoRequest = 0x15
)

// Encryption methods
const (
	EncryptNone = 0x00
	EncryptXOR  = 0x01
	EncryptAES  = 0x02
)

type PacketHeader struct {
	Type      uint8
	Encrypt   uint8
	SessionID uint16
	Token     uint32
}

func main() {
	fmt.Println("=== Simple Android-Go Protocol Test ===")
	fmt.Println()

	// Initialize test directories
	if err := initializeDirectories(); err != nil {
		fmt.Printf("❌ Failed to initialize directories: %v\n", err)
		os.Exit(1)
	}

	// Generate Go packets for Android validation
	if err := generateGoPackets(); err != nil {
		fmt.Printf("❌ Failed to generate Go packets: %v\n", err)
		os.Exit(1)
	}

	fmt.Println()
	fmt.Println("✅ Test packet generation completed successfully!")
	fmt.Println("📁 Generated packets in:", filepath.Join(testDataDir, goToAndroidDir))
}

func initializeDirectories() error {
	dirs := []string{
		filepath.Join(testDataDir, goToAndroidDir),
		filepath.Join(testDataDir, androidToGoDir),
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %w", dir, err)
		}
	}

	return nil
}

func generateGoPackets() error {
	fmt.Println("🔧 Generating Go packets for Android validation...")

	// Test 1: OPEN packet with XOR encryption
	openPacket := createOpenPacket("testuser", "testpass", 1420, EncryptXOR)
	if err := writePacket("go_open_xor.bin", openPacket); err != nil {
		return err
	}
	fmt.Println("✅ Generated: go_open_xor.bin - OPEN packet with XOR encryption")

	// Test 2: OPEN_ACK packet with session info
	openAckPacket := createOpenAckPacket(0x1234, 0xDEADBEEF, EncryptXOR)
	if err := writePacket("go_openack_xor.bin", openAckPacket); err != nil {
		return err
	}
	fmt.Println("✅ Generated: go_openack_xor.bin - OPEN_ACK packet with session info")

	// Test 3: Encrypted data packet
	dataEncPacket := createEncryptedDataPacket(0x1234, 0xDEADBEEF, EncryptXOR)
	if err := writePacket("go_data_encrypted.bin", dataEncPacket); err != nil {
		return err
	}
	fmt.Println("✅ Generated: go_data_encrypted.bin - Encrypted data packet")

	// Test 4: Plain data packet
	dataPacket := createDataPacket(0x5678, 0x12345678, EncryptNone)
	if err := writePacket("go_data_plain.bin", dataPacket); err != nil {
		return err
	}
	fmt.Println("✅ Generated: go_data_plain.bin - Plain data packet")

	// Test 5: Echo request packet
	echoPacket := createEchoRequestPacket(0x1234, 0xDEADBEEF, EncryptXOR)
	if err := writePacket("go_echo_request.bin", echoPacket); err != nil {
		return err
	}
	fmt.Println("✅ Generated: go_echo_request.bin - Echo request packet")

	return nil
}

func writePacket(filename string, data []byte) error {
	filepath := filepath.Join(testDataDir, goToAndroidDir, filename)
	return ioutil.WriteFile(filepath, data, 0644)
}

func createOpenPacket(username, password string, mtu uint16, encryptMethod uint8) []byte {
	header := PacketHeader{
		Type:      PacketTypeOpen,
		Encrypt:   encryptMethod,
		SessionID: 0,
		Token:     0,
	}

	headerBytes := serializeHeader(header)
	signature := calculateSignature(headerBytes)
	attributes := createOpenAttributes(username, password, mtu, encryptMethod)

	// Combine: header + signature + attributes
	packet := make([]byte, 0, 8+16+len(attributes))
	packet = append(packet, headerBytes...)
	packet = append(packet, signature...)
	packet = append(packet, attributes...)

	return packet
}

func createOpenAckPacket(sessionID uint16, token uint32, encryptMethod uint8) []byte {
	header := PacketHeader{
		Type:      PacketTypeOpenAck,
		Encrypt:   encryptMethod,
		SessionID: sessionID,
		Token:     token,
	}

	headerBytes := serializeHeader(header)
	signature := calculateSignature(headerBytes)

	// Simple server config attributes
	attributes := []byte{
		0x10, 0x04, 192, 168, 1, 1, // Server IP
		0x11, 0x04, 8, 8, 8, 8, // DNS server
	}

	packet := make([]byte, 0, 8+16+len(attributes))
	packet = append(packet, headerBytes...)
	packet = append(packet, signature...)
	packet = append(packet, attributes...)

	return packet
}

func createEncryptedDataPacket(sessionID uint16, token uint32, encryptMethod uint8) []byte {
	header := PacketHeader{
		Type:      PacketTypeDataEncrypt,
		Encrypt:   encryptMethod,
		SessionID: sessionID,
		Token:     token,
	}

	headerBytes := serializeHeader(header)

	// Sample IP packet (would be encrypted in real implementation)
	ipPacket := []byte{
		0x45, 0x00, 0x00, 0x3C, // IP header
		0x1C, 0x46, 0x40, 0x00,
		0x40, 0x06, 0x00, 0x00,
		0xC0, 0xA8, 0x01, 0x64, // Source IP
		0xC0, 0xA8, 0x01, 0x01, // Dest IP
		// Payload: "Hello from Go!"
		0x48, 0x65, 0x6C, 0x6C, 0x6F, 0x20, 0x66, 0x72,
		0x6F, 0x6D, 0x20, 0x47, 0x6F, 0x21, 0x00, 0x00,
	}

	packet := make([]byte, 0, 8+len(ipPacket))
	packet = append(packet, headerBytes...)
	packet = append(packet, ipPacket...)

	return packet
}

func createDataPacket(sessionID uint16, token uint32, encryptMethod uint8) []byte {
	header := PacketHeader{
		Type:      PacketTypeData,
		Encrypt:   encryptMethod,
		SessionID: sessionID,
		Token:     token,
	}

	headerBytes := serializeHeader(header)

	// Sample IP packet
	ipPacket := []byte{
		0x45, 0x00, 0x00, 0x28, // IP header
		0x1C, 0x47, 0x40, 0x00,
		0x40, 0x01, 0x00, 0x00,
		0xC0, 0xA8, 0x01, 0x01, // Source IP
		0xC0, 0xA8, 0x01, 0x64, // Dest IP
		// ICMP payload
		0x08, 0x00, 0xF7, 0xFC, 0x00, 0x00, 0x00, 0x00,
	}

	packet := make([]byte, 0, 8+len(ipPacket))
	packet = append(packet, headerBytes...)
	packet = append(packet, ipPacket...)

	return packet
}

func createEchoRequestPacket(sessionID uint16, token uint32, encryptMethod uint8) []byte {
	header := PacketHeader{
		Type:      PacketTypeEchoRequest,
		Encrypt:   encryptMethod,
		SessionID: sessionID,
		Token:     token,
	}

	headerBytes := serializeHeader(header)
	signature := calculateSignature(headerBytes)

	// Create timestamp (8 bytes, microseconds)
	timestamp := uint64(time.Now().UnixNano() / 1000)
	timestampBytes := make([]byte, 8)
	binary.BigEndian.PutUint64(timestampBytes, timestamp)

	packet := make([]byte, 0, 8+16+8)
	packet = append(packet, headerBytes...)
	packet = append(packet, signature...)
	packet = append(packet, timestampBytes...)

	return packet
}

func serializeHeader(header PacketHeader) []byte {
	bytes := make([]byte, 8)
	bytes[0] = header.Type
	bytes[1] = header.Encrypt
	binary.BigEndian.PutUint16(bytes[2:4], header.SessionID)
	binary.BigEndian.PutUint32(bytes[4:8], header.Token)
	return bytes
}

func calculateSignature(headerBytes []byte) []byte {
	hasher := md5.New()
	hasher.Write(headerBytes)
	hasher.Write([]byte{109, 119}) // ASCII 'm', 'w'
	return hasher.Sum(nil)
}

func createOpenAttributes(username, password string, mtu uint16, encryptMethod uint8) []byte {
	var attributes []byte

	// MTU attribute (must be first)
	mtuBytes := make([]byte, 4)
	binary.BigEndian.PutUint16(mtuBytes[0:2], mtu)
	attributes = append(attributes, createTLVAttribute(0x03, mtuBytes)...)

	// Username attribute (must be second)
	attributes = append(attributes, createTLVAttribute(0x01, []byte(username))...)

	// Password attribute (must be third) - simplified encryption
	encryptedPassword := encryptPassword(password, username)
	attributes = append(attributes, createTLVAttribute(0x02, encryptedPassword)...)

	// Encryption method attribute
	attributes = append(attributes, createTLVAttribute(0x04, []byte{encryptMethod})...)

	return attributes
}

func createTLVAttribute(attrType uint8, value []byte) []byte {
	attr := make([]byte, 2+len(value))
	attr[0] = attrType
	attr[1] = uint8(len(value))
	copy(attr[2:], value)
	return attr
}

func encryptPassword(password, username string) []byte {
	// Simplified password encryption for testing
	key := md5.Sum([]byte("mw" + username))
	encrypted := make([]byte, 16) // Take first 16 bytes

	passwordBytes := []byte(password)
	for i := 0; i < len(encrypted) && i < len(passwordBytes); i++ {
		encrypted[i] = passwordBytes[i] ^ key[i%16]
	}

	return encrypted
}
