# Go-Swift Protocol Interoperability Test Vectors

This directory contains binary packet files generated by Go backend for Swift validation.

## Test Cases:

### OPEN Packet (open_packet.bin)
- **Type**: 0x13 (OPEN)
- **Encryption**: 0x02 (AES)
- **SessionID**: 0
- **Token**: 0
- **Contains**: MTU, Username, Password, Encrypt attributes

### Echo Request Packet (echo_request_packet.bin)
- **Type**: 0x15 (ECHO_REQUEST)
- **Encryption**: 0x02 (AES)
- **SessionID**: 1234
- **Token**: 0x56789ABC
- **Contains**: 8-byte timestamp

### Data Packet (data_packet.bin)
- **Type**: 0x14 (DATA)
- **Encryption**: 0x02 (AES)
- **SessionID**: 1234
- **Token**: 0x56789ABC
- **Contains**: Sample IPv4 packet

## Usage:

1. Run this Go tool to generate test packets
2. Use Swift tests to parse and validate the generated packets
3. Swift tests should generate their own packets in ../swift_to_go/
4. Run this tool again to validate Swift-generated packets

## Validation Points:

- Header format (8 bytes, Big Endian)
- Packet type constants match
- TLV attribute encoding
- MD5 signature calculation
- Encryption method constants
