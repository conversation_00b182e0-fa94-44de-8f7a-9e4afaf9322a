/**
 * FILE: xor_encryption_validator.go
 *
 * DESCRIPTION:
 *     Cross-platform XOR encryption verification tool.
 *     Generates test vectors for iOS and Android platforms to verify compatibility.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create cross-platform XOR encryption test
 */

package main

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"os"
	"unsafe"
)

// XOREncryption implements the same XOR algorithm as Go backend
type XOREncryption struct {
	key []byte
}

// NewXOREncryption creates XOR encryption with session key from username+password
func NewXOREncryption(username, password string) *XOREncryption {
	// Generate session key using MD5(username + password)
	combined := username + password
	key := md5.Sum([]byte(combined))

	return &XOREncryption{
		key: key[:],
	}
}

// Encrypt encrypts data using XOR algorithm (matches Go backend implementation)
func (e *XOREncryption) Encrypt(data []byte) []byte {
	if len(data) == 0 {
		return data
	}

	// Create result array
	result := make([]byte, len(data))
	copy(result, data)

	// Use same XOR encryption method as pa_mobile
	// Process 8 bytes at a time for XOR operation
	for i := 0; i < len(result)/8; i++ {
		// Treat data and key as uint32 arrays
		data32 := (*[2]uint32)(unsafe.Pointer(&result[i*8]))
		key32 := (*[2]uint32)(unsafe.Pointer(&e.key[0]))

		// XOR operation
		data32[0] ^= key32[0]
		data32[1] ^= key32[1]
	}

	// Process remaining bytes
	remain := len(result) % 8
	if remain > 0 {
		for i := 0; i < remain; i++ {
			result[len(result)-remain+i] ^= e.key[i]
		}
	}

	return result
}

// Decrypt decrypts data (XOR is symmetric)
func (e *XOREncryption) Decrypt(data []byte) []byte {
	return e.Encrypt(data)
}

// TestVector represents a test case for cross-platform verification
type TestVector struct {
	Name        string
	Username    string
	Password    string
	PlainText   []byte
	Description string
}

func main() {
	fmt.Println("=== Cross-Platform XOR Encryption Test Vector Generator ===")
	fmt.Println()

	// Define test vectors
	testVectors := []TestVector{
		{
			Name:        "Basic 8-byte aligned",
			Username:    "testuser",
			Password:    "testpass",
			PlainText:   []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08},
			Description: "8-byte aligned data for UInt32 operation verification",
		},
		{
			Name:        "Non-aligned 10 bytes",
			Username:    "testuser",
			Password:    "testpass",
			PlainText:   []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A},
			Description: "10-byte data to test remainder handling",
		},
		{
			Name:        "Hello World",
			Username:    "tmptest",
			Password:    "itforce",
			PlainText:   []byte("Hello World"),
			Description: "Real-world text data",
		},
		{
			Name:        "Small 3-byte data",
			Username:    "user",
			Password:    "pass",
			PlainText:   []byte{0xFF, 0xAA, 0x55},
			Description: "Small data less than 8 bytes",
		},
		{
			Name:        "16-byte aligned",
			Username:    "admin",
			Password:    "secret",
			PlainText:   []byte{0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD, 0xEE, 0xFF},
			Description: "16-byte data (exactly 2 blocks)",
		},
	}

	// Generate test vectors
	for i, vector := range testVectors {
		fmt.Printf("Test Vector %d: %s\n", i+1, vector.Name)
		fmt.Printf("Description: %s\n", vector.Description)
		fmt.Printf("Username: %s\n", vector.Username)
		fmt.Printf("Password: %s\n", vector.Password)

		// Generate session key
		encryption := NewXOREncryption(vector.Username, vector.Password)
		fmt.Printf("Session Key: %s\n", hex.EncodeToString(encryption.key))

		// Show plaintext
		fmt.Printf("PlainText:   %s\n", hex.EncodeToString(vector.PlainText))

		// Encrypt
		encrypted := encryption.Encrypt(vector.PlainText)
		fmt.Printf("Encrypted:   %s\n", hex.EncodeToString(encrypted))

		// Verify decryption
		decrypted := encryption.Decrypt(encrypted)
		fmt.Printf("Decrypted:   %s\n", hex.EncodeToString(decrypted))

		// Verify round-trip
		if string(decrypted) == string(vector.PlainText) {
			fmt.Printf("✅ Round-trip: PASS\n")
		} else {
			fmt.Printf("❌ Round-trip: FAIL\n")
		}

		fmt.Println()
	}

	// Generate test files for mobile platforms
	fmt.Println("=== Generating Test Files for Mobile Platforms ===")

	// Create test data file for iOS/Android
	testData := map[string]interface{}{
		"test_vectors": make([]map[string]interface{}, len(testVectors)),
	}

	for i, vector := range testVectors {
		encryption := NewXOREncryption(vector.Username, vector.Password)
		encrypted := encryption.Encrypt(vector.PlainText)

		testData["test_vectors"].([]map[string]interface{})[i] = map[string]interface{}{
			"name":        vector.Name,
			"username":    vector.Username,
			"password":    vector.Password,
			"session_key": hex.EncodeToString(encryption.key),
			"plaintext":   hex.EncodeToString(vector.PlainText),
			"encrypted":   hex.EncodeToString(encrypted),
			"description": vector.Description,
		}
	}

	// Write binary test files
	for i, vector := range testVectors {
		encryption := NewXOREncryption(vector.Username, vector.Password)
		encrypted := encryption.Encrypt(vector.PlainText)

		filename := fmt.Sprintf("test_vector_%d.bin", i+1)
		err := os.WriteFile(filename, encrypted, 0644)
		if err != nil {
			fmt.Printf("Error writing %s: %v\n", filename, err)
		} else {
			fmt.Printf("Generated: %s (%d bytes)\n", filename, len(encrypted))
		}
	}

	fmt.Println()
	fmt.Println("=== Instructions for Mobile Platform Testing ===")
	fmt.Println("1. Copy the test vectors above to your mobile platform tests")
	fmt.Println("2. Implement the same test cases in iOS (Swift) and Android (Kotlin)")
	fmt.Println("3. Verify that your platform produces identical encrypted results")
	fmt.Println("4. Verify round-trip encryption/decryption works correctly")
	fmt.Println("5. Pay special attention to UInt32 vs byte-level operations")
	fmt.Println()
	fmt.Println("Expected behavior:")
	fmt.Println("- iOS should match Go results exactly (both use UInt32 operations)")
	fmt.Println("- Android should now match Go results (after UInt32 fix)")
	fmt.Println("- All platforms should have identical encrypted outputs for same inputs")
}
