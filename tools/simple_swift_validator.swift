#!/usr/bin/env swift

/**
 * FILE: simple_swift_validator.swift
 *
 * DESCRIPTION:
 *     Simple Swift script to validate Go-generated packets.
 *     Demonstrates basic Go-Swift protocol interoperability.
 *
 * AUTHOR: wei
 * HISTORY: 26/06/2025 create
 */

import Foundation

// MARK: - Main Function

func main() {
    print("🔄 Simple Swift-Go Protocol Validator")
    print("=====================================")
    
    validateGoPackets()
    generateSimpleSwiftPackets()
    
    print("\n✅ Validation completed!")
}

// MARK: - Go Packet Validation

func validateGoPackets() {
    print("\n📥 Validating Go-generated packets...")
    
    let goPackets = ["open_packet.bin", "echo_request_packet.bin", "data_packet.bin"]
    
    for packetFile in goPackets {
        do {
            let url = URL(fileURLWithPath: "test_data/go_to_swift/\(packetFile)")
            let data = try Data(contentsOf: url)
            
            print("  ✓ \(packetFile): \(data.count) bytes")
            
            if data.count >= 8 {
                let type = data[0]
                let encrypt = data[1]
                let sessionID = UInt16(data[2]) << 8 | UInt16(data[3])
                let token = UInt32(data[4]) << 24 | UInt32(data[5]) << 16 | 
                           UInt32(data[6]) << 8 | UInt32(data[7])
                
                print("    - Type: 0x\(String(format: "%02X", type))")
                print("    - Encrypt: 0x\(String(format: "%02X", encrypt))")
                print("    - SessionID: \(sessionID)")
                print("    - Token: 0x\(String(format: "%08X", token))")
                
                // Validate packet type
                if type >= 0x11 && type <= 0x20 {
                    print("    - ✅ Valid packet type")
                } else {
                    print("    - ❌ Invalid packet type")
                }
                
                // Validate encryption method
                if encrypt <= 0x02 {
                    print("    - ✅ Valid encryption method")
                } else {
                    print("    - ❌ Invalid encryption method")
                }
                
                // Type-specific validation
                switch type {
                case 0x13: // OPEN
                    if sessionID == 0 && token == 0 {
                        print("    - ✅ OPEN packet format correct")
                    } else {
                        print("    - ❌ OPEN packet should have SessionID=0, Token=0")
                    }
                    
                    if data.count >= 24 {
                        print("    - ✅ Has signature and attributes")
                    } else {
                        print("    - ❌ Missing signature or attributes")
                    }
                    
                case 0x15: // ECHO_REQUEST
                    if data.count >= 32 {
                        print("    - ✅ Has signature and timestamp")
                    } else {
                        print("    - ❌ Missing signature or timestamp")
                    }
                    
                case 0x14: // DATA
                    let payloadSize = data.count - 8
                    print("    - Payload: \(payloadSize) bytes")
                    if payloadSize > 0 && data[8] == 0x45 {
                        print("    - ✅ IPv4 packet detected")
                    }
                    
                default:
                    print("    - Unknown packet type")
                }
            } else {
                print("    - ❌ Packet too short")
            }
            
        } catch {
            print("  ⚠ \(packetFile) not found or error: \(error)")
        }
    }
}

// MARK: - Swift Packet Generation

func generateSimpleSwiftPackets() {
    print("\n📤 Generating simple Swift packets...")
    
    // Create output directory
    let outputDir = URL(fileURLWithPath: "test_data/swift_to_go")
    try? FileManager.default.createDirectory(at: outputDir, withIntermediateDirectories: true)
    
    // Generate OPEN packet
    do {
        var openPacket = Data()
        
        // Header: Type(1) + Encrypt(1) + SessionID(2) + Token(4)
        openPacket.append(0x13) // OPEN
        openPacket.append(0x02) // AES
        openPacket.append(0x00) // SessionID = 0
        openPacket.append(0x00)
        openPacket.append(0x00) // Token = 0
        openPacket.append(0x00)
        openPacket.append(0x00)
        openPacket.append(0x00)
        
        // Simple 16-byte signature (dummy)
        let signature = Data(repeating: 0xAB, count: 16)
        openPacket.append(signature)
        
        // Simple MTU attribute
        openPacket.append(0x03) // MTU type
        openPacket.append(0x04) // Length = 4
        openPacket.append(0x05) // MTU = 1420 (0x058C)
        openPacket.append(0x8C)
        
        let openURL = outputDir.appendingPathComponent("swift_open_packet.bin")
        try openPacket.write(to: openURL)
        print("  ✓ Generated OPEN packet (\(openPacket.count) bytes)")
        
    } catch {
        print("  ❌ Error generating OPEN packet: \(error)")
    }
    
    // Generate Echo Request packet
    do {
        var echoPacket = Data()
        
        // Header
        echoPacket.append(0x15) // ECHO_REQUEST
        echoPacket.append(0x01) // XOR
        echoPacket.append(0x16) // SessionID = 5678 (0x162E)
        echoPacket.append(0x2E)
        echoPacket.append(0xDE) // Token = 0xDEADBEEF
        echoPacket.append(0xAD)
        echoPacket.append(0xBE)
        echoPacket.append(0xEF)
        
        // Simple signature
        let signature = Data(repeating: 0xCD, count: 16)
        echoPacket.append(signature)
        
        // 8-byte timestamp
        let timestamp = UInt64(Date().timeIntervalSince1970)
        var timestampData = Data(count: 8)
        timestampData.withUnsafeMutableBytes { bytes in
            bytes.storeBytes(of: timestamp.bigEndian, as: UInt64.self)
        }
        echoPacket.append(timestampData)
        
        let echoURL = outputDir.appendingPathComponent("swift_echo_packet.bin")
        try echoPacket.write(to: echoURL)
        print("  ✓ Generated Echo Request packet (\(echoPacket.count) bytes)")
        
    } catch {
        print("  ❌ Error generating Echo packet: \(error)")
    }
    
    // Generate Data packet
    do {
        var dataPacket = Data()
        
        // Header
        dataPacket.append(0x14) // DATA
        dataPacket.append(0x00) // No encryption
        dataPacket.append(0x22) // SessionID = 8888 (0x22B8)
        dataPacket.append(0xB8)
        dataPacket.append(0x12) // Token = 0x12345678
        dataPacket.append(0x34)
        dataPacket.append(0x56)
        dataPacket.append(0x78)
        
        // Simple payload
        let payload = "Hello from Swift!".data(using: .utf8)!
        dataPacket.append(payload)
        
        let dataURL = outputDir.appendingPathComponent("swift_data_packet.bin")
        try dataPacket.write(to: dataURL)
        print("  ✓ Generated Data packet (\(dataPacket.count) bytes)")
        
    } catch {
        print("  ❌ Error generating Data packet: \(error)")
    }
}

// MARK: - Entry Point

main()
