package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"time"
)

// ServerInfo 表示VPN服务器信息
type ServerInfo struct {
	ID         int    `json:"id"`
	IsAuto     bool   `json:"isauto"`
	Name       string `json:"name"`
	NameEn     string `json:"name_en"`
	ServerName string `json:"serverName"`
	ServerPort int    `json:"serverPort"`
}

var (
	port       = flag.Int("port", 8090, "服务器监听端口")
	format     = flag.String("format", "json", "响应格式 (json 或 text)")
	delay      = flag.Int("delay", 200, "响应延迟(毫秒)")
	delayRange = flag.Int("delay-range", 100, "随机延迟范围(毫秒)")
	version    = flag.String("version", "20241227", "服务器列表版本号")
)

// 模拟服务器列表
var servers = []ServerInfo{
	{
		ID:         1,
		IsAuto:     true,
		Name:       "leo线路",
		NameEn:     "leo",
		ServerName: "xzdpops.secwan.com.cn",
		ServerPort: 8000,
	},
	{
		ID:         2,
		IsAuto:     false,
		Name:       "迪拜服务器线路2",
		NameEn:     "dubai",
		ServerName: "itdubai2.com",
		ServerPort: 9000,
	},
	{
		ID:         3,
		IsAuto:     false,
		Name:       "北京服务器线路1",
		NameEn:     "china",
		ServerName: "20.209.5.19",
		ServerPort: 8000,
	},
	{
		ID:         4,
		IsAuto:     false,
		Name:       "北京服务器线路2",
		NameEn:     "china",
		ServerName: "20.209.5.20",
		ServerPort: 8000,
	},
	{
		ID:         5,
		IsAuto:     false,
		Name:       "北京服务器线路3",
		NameEn:     "china",
		ServerName: "9.209.5.20",
		ServerPort: 8000,
	},
	{
		ID:         6,
		IsAuto:     false,
		Name:       "北京服务器线路4",
		NameEn:     "china",
		ServerName: "9.209.5.21",
		ServerPort: 8000,
	},
	{
		ID:         7,
		IsAuto:     false,
		Name:       "香港服务器线路1",
		NameEn:     "hongkong",
		ServerName: "hk.vpnserver.example.com",
		ServerPort: 8000,
	},
	{
		ID:         8,
		IsAuto:     false,
		Name:       "香港服务器线路2",
		NameEn:     "hongkong",
		ServerName: "hk2.vpnserver.example.com",
		ServerPort: 8000,
	},
	{
		ID:         11,
		IsAuto:     false,
		Name:       "新加坡服务器线路1",
		NameEn:     "singapore",
		ServerName: "sg.vpnserver.example.com",
		ServerPort: 8000,
	},
	{
		ID:         12,
		IsAuto:     false,
		Name:       "新加坡服务器线路2",
		NameEn:     "singapore",
		ServerName: "sg2.vpnserver.example.com",
		ServerPort: 8000,
	},
	{
		ID:         15,
		IsAuto:     false,
		Name:       "日本服务器线路1",
		NameEn:     "japan",
		ServerName: "jp.vpnserver.example.com",
		ServerPort: 8000,
	},
	{
		ID:         16,
		IsAuto:     false,
		Name:       "日本服务器线路2",
		NameEn:     "japan",
		ServerName: "jp2.vpnserver.example.com",
		ServerPort: 8000,
	},
}

func main() {
	flag.Parse()
	rand.Seed(time.Now().UnixNano())

	http.HandleFunc("/servers", handleServers)
	http.HandleFunc("/", handleHome)

	addr := fmt.Sprintf(":%d", *port)
	log.Printf("启动模拟服务器列表服务器在 http://localhost%s", addr)
	log.Printf("响应格式: %s", *format)
	log.Printf("响应延迟: %d±%d ms", *delay, *delayRange)
	log.Printf("访问 http://localhost%s/servers 获取服务器列表", addr)
	log.Fatal(http.ListenAndServe(addr, nil))
}

func handleHome(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/html")
	html := `
<!DOCTYPE html>
<html>
<head>
    <title>VPN服务器列表模拟器</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #333; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .endpoint { background-color: #f0f0f0; padding: 10px; border-radius: 4px; font-family: monospace; }
        .auto { color: green; font-weight: bold; }
        .not-auto { color: #666; }
    </style>
</head>
<body>
    <h1>VPN服务器列表模拟器</h1>
    <p>这是一个模拟VPN服务器列表的服务器。</p>

    <h2>端点</h2>
    <div class="endpoint">GET /servers</div>

    <h2>当前配置</h2>
    <ul>
        <li>端口: %d</li>
        <li>响应格式: %s</li>
        <li>响应延迟: %d±%d ms</li>
        <li>版本号: %s</li>
    </ul>

    <h2>可用服务器</h2>
    <table>
        <tr>
            <th>ID</th>
            <th>名称</th>
            <th>英文名称</th>
            <th>服务器地址</th>
            <th>端口</th>
            <th>自动分流</th>
        </tr>
`
	for _, server := range servers {
		autoClass := "not-auto"
		autoText := "否"
		if server.IsAuto {
			autoClass = "auto"
			autoText = "是"
		}
		html += fmt.Sprintf(`
        <tr>
            <td>%d</td>
            <td>%s</td>
            <td>%s</td>
            <td>%s</td>
            <td>%d</td>
            <td class="%s">%s</td>
        </tr>
`, server.ID, server.Name, server.NameEn, server.ServerName, server.ServerPort, autoClass, autoText)
	}

	html += `
    </table>

    <h2>使用方法</h2>
    <p>访问 <a href="/servers">/servers</a> 获取服务器列表。</p>
    <p>您可以通过命令行参数修改服务器行为：</p>
    <pre>
    --port=8090       # 服务器监听端口
    --format=json     # 响应格式 (json 或 text)
    --delay=200       # 响应延迟(毫秒)
    --delay-range=100 # 随机延迟范围(毫秒)
    --version=20241227 # 服务器列表版本号
    </pre>

    <h2>自动分流线路说明</h2>
    <p>根据客户端的实现，自动分流线路（ID为4、8、12、16的线路）将在以下情况下被自动选择：</p>
    <ul>
        <li>首次安装或更新服务器列表时</li>
        <li>当服务器列表版本更新时</li>
    </ul>
    <p>系统会对这些线路进行 ping 测试，并选择响应最快的线路作为默认连接。</p>
</body>
</html>
`
	fmt.Fprintf(w, html, *port, *format, *delay, *delayRange, *version)
}

func handleServers(w http.ResponseWriter, r *http.Request) {
	// 添加随机延迟
	randomDelay := *delay
	if *delayRange > 0 {
		randomDelay += rand.Intn(*delayRange*2) - *delayRange
		if randomDelay < 0 {
			randomDelay = 0
		}
	}
	time.Sleep(time.Duration(randomDelay) * time.Millisecond)

	// 设置CORS头，允许所有来源
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	// 处理OPTIONS请求
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// 根据格式返回响应
	switch *format {
	case "json":
		serveJSON(w, r)
	case "text":
		serveText(w, r)
	default:
		serveJSON(w, r)
	}
}

func serveJSON(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	// 创建响应结构
	response := struct {
		Version    string       `json:"version"`
		ServerList []ServerInfo `json:"serverlist"`
	}{
		Version:    *version,
		ServerList: servers,
	}

	// 编码为JSON
	if err := json.NewEncoder(w).Encode(response); err != nil {
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}
}

func serveText(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain")

	// 以文本格式返回服务器列表
	// 格式: id,name,serverName:serverPort,isauto
	for _, server := range servers {
		fmt.Fprintf(w, "%d,%s,%s:%d,%t\n",
			server.ID,
			server.Name,
			server.ServerName,
			server.ServerPort,
			server.IsAuto)
	}
}
