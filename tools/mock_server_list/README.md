# VPN服务器列表模拟器

这是一个简单的HTTP服务器，用于模拟VPN服务器列表API。它可以返回JSON或纯文本格式的服务器列表，以便测试VPN客户端应用程序。

## 功能

- 提供模拟的VPN服务器列表
- 支持JSON和纯文本响应格式
- 可配置的响应延迟，模拟真实网络条件
- 简单的Web界面，显示当前配置和可用服务器

## 使用方法

### 启动服务器

```bash
go run main.go [options]
```

### 命令行选项

```
--port=8090       # 服务器监听端口
--format=json     # 响应格式 (json 或 text)
--delay=200       # 响应延迟(毫秒)
--delay-range=100 # 随机延迟范围(毫秒)
```

### 示例

启动服务器，使用默认配置：

```bash
go run main.go
```

启动服务器，使用文本格式响应：

```bash
go run main.go --format=text
```

启动服务器，使用自定义端口和延迟：

```bash
go run main.go --port=9000 --delay=500 --delay-range=200
```

## API端点

### GET /servers

返回VPN服务器列表。

#### JSON响应格式

```json
{
  "success": true,
  "message": "Server list retrieved successfully",
  "servers": [
    {
      "id": 1,
      "name": "美国服务器",
      "address": "us.vpnserver.example.com",
      "port": 443,
      "country": "US",
      "city": "New York",
      "ping": 120
    },
    ...
  ],
  "version": "1.0.0"
}
```

#### 文本响应格式

```
us.vpnserver.example.com:443,美国服务器,US,New York
jp.vpnserver.example.com:443,日本服务器,JP,Tokyo
...
```

## 与VPN客户端集成

要将此模拟服务器与您的VPN客户端集成，请将客户端中的服务器列表URL更改为：

```
http://localhost:8090/servers
```

或者，如果您在不同的端口上运行服务器：

```
http://localhost:<port>/servers
```

## 构建可执行文件

要构建一个独立的可执行文件，请运行：

```bash
go build -o server_list_mock
```

然后可以直接运行生成的可执行文件：

```bash
./server_list_mock [options]
```
