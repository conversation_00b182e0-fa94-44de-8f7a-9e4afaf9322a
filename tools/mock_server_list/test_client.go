package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"strings"
	"time"
)

// ServerInfo 表示VPN服务器信息
type ServerInfo struct {
	ID         int    `json:"id"`
	IsAuto     bool   `json:"isauto"`
	Name       string `json:"name"`
	NameEn     string `json:"name_en"`
	ServerName string `json:"serverName"`
	ServerPort int    `json:"serverPort"`
}

// ServerResponse 表示JSON响应
type ServerResponse struct {
	Version    string      `json:"version"`
	ServerList []ServerInfo `json:"serverlist"`
}

var (
	serverURL = flag.String("url", "http://localhost:8090/servers", "服务器列表URL")
	format    = flag.String("format", "json", "响应格式 (json 或 text)")
)

func main() {
	flag.Parse()

	fmt.Printf("测试VPN服务器列表客户端\n")
	fmt.Printf("服务器URL: %s\n", *serverURL)
	fmt.Printf("请求格式: %s\n\n", *format)

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// 设置请求头
	req, err := http.NewRequest("GET", *serverURL, nil)
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		os.Exit(1)
	}

	if *format == "json" {
		req.Header.Set("Accept", "application/json")
	} else {
		req.Header.Set("Accept", "text/plain")
	}

	// 发送请求
	fmt.Printf("发送请求...\n")
	startTime := time.Now()
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		os.Exit(1)
	}
	defer resp.Body.Close()
	duration := time.Since(startTime)

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("请求完成，耗时: %v\n", duration)
	fmt.Printf("状态码: %d\n", resp.StatusCode)
	fmt.Printf("响应内容类型: %s\n\n", resp.Header.Get("Content-Type"))

	// 解析响应
	if *format == "json" {
		parseJSONResponse(body)
	} else {
		parseTextResponse(body)
	}
}

func parseJSONResponse(body []byte) {
	var response ServerResponse
	if err := json.Unmarshal(body, &response); err != nil {
		fmt.Printf("解析JSON响应失败: %v\n", err)
		fmt.Printf("原始响应: %s\n", string(body))
		return
	}

	fmt.Printf("版本: %s\n", response.Version)
	fmt.Printf("服务器数量: %d\n\n", len(response.ServerList))

	fmt.Printf("服务器列表:\n")
	fmt.Printf("%-5s %-20s %-15s %-30s %-6s %-10s\n", "ID", "名称", "英文名称", "服务器地址", "端口", "自动分流")
	fmt.Printf("%s\n", strings.Repeat("-", 100))

	for _, server := range response.ServerList {
		autoText := "否"
		if server.IsAuto {
			autoText = "是"
		}
		fmt.Printf("%-5d %-20s %-15s %-30s %-6d %-10s\n",
			server.ID, server.Name, server.NameEn, server.ServerName, server.ServerPort, autoText)
	}
}

func parseTextResponse(body []byte) {
	lines := strings.Split(string(body), "\n")
	var servers []ServerInfo

	for i, line := range lines {
		if line == "" {
			continue
		}

		parts := strings.Split(line, ",")
		if len(parts) < 3 {
			fmt.Printf("警告: 第%d行格式不正确: %s\n", i+1, line)
			continue
		}

		// 解析ID
		id := 0
		fmt.Sscanf(parts[0], "%d", &id)

		// 解析名称
		name := parts[1]

		// 解析服务器地址和端口
		serverParts := strings.Split(parts[2], ":")
		serverName := serverParts[0]
		serverPort := 8000
		if len(serverParts) > 1 {
			fmt.Sscanf(serverParts[1], "%d", &serverPort)
		}

		// 解析自动分流
		isAuto := false
		if len(parts) > 3 {
			isAuto = (parts[3] == "true")
		}

		// 创建服务器信息
		server := ServerInfo{
			ID:         id,
			Name:       name,
			ServerName: serverName,
			ServerPort: serverPort,
			IsAuto:     isAuto,
			NameEn:     "unknown",
		}

		servers = append(servers, server)
	}

	fmt.Printf("服务器数量: %d\n\n", len(servers))

	fmt.Printf("服务器列表:\n")
	fmt.Printf("%-5s %-20s %-30s %-6s %-10s\n", "ID", "名称", "服务器地址", "端口", "自动分流")
	fmt.Printf("%s\n", strings.Repeat("-", 90))

	for _, server := range servers {
		autoText := "否"
		if server.IsAuto {
			autoText = "是"
		}
		fmt.Printf("%-5d %-20s %-30s %-6d %-10s\n",
			server.ID, server.Name, server.ServerName, server.ServerPort, autoText)
	}
}
