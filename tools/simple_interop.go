/**
 * FILE: simple_interop_test.go
 *
 * DESCRIPTION:
 *     Simplified Go-Swift protocol interoperability test tool.
 *     Generates basic test packets and validates Swift-generated packets.
 *
 * AUTHOR: wei
 * HISTORY: 26/06/2025 create
 */

package main

import (
	"crypto/md5"
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"time"
)

const (
	testDataDir  = "test_data"
	goToSwiftDir = "go_to_swift"
	swiftToGoDir = "swift_to_go"
)

// Packet types (matching Go backend constants)
const (
	PacketTypeOpenReject  = 0x11
	PacketTypeOpenAck     = 0x12
	PacketTypeOpen        = 0x13
	PacketTypeData        = 0x14
	PacketTypeEchoRequest = 0x15
	PacketTypeEchoResponse = 0x16
)

// Encryption methods
const (
	EncryptNone = 0x00
	EncryptXOR  = 0x01
	EncryptAES  = 0x02
)

// TLV attribute types
const (
	TLVUsername = 0x01
	TLVPassword = 0x02
	TLVMTU      = 0x03
	TLVIP       = 0x04
	TLVDNS      = 0x05
	TLVGateway  = 0x06
	TLVNetmask  = 0x07
	TLVEncrypt  = 0x08
)

type PacketHeader struct {
	Type      uint8
	Encrypt   uint8
	SessionID uint16
	Token     uint32
}

func main() {
	// Create test directories
	if err := createTestDirectories(); err != nil {
		log.Fatalf("Failed to create test directories: %v", err)
	}

	// Generate Go packets for Swift to parse
	if err := generateGoPackets(); err != nil {
		log.Fatalf("Failed to generate Go packets: %v", err)
	}

	// Validate Swift packets (if they exist)
	if err := validateSwiftPackets(); err != nil {
		log.Printf("Warning: Failed to validate Swift packets: %v", err)
	}

	fmt.Println("Protocol interoperability test completed successfully!")
}

func createTestDirectories() error {
	dirs := []string{
		testDataDir,
		filepath.Join(testDataDir, goToSwiftDir),
		filepath.Join(testDataDir, swiftToGoDir),
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %w", dir, err)
		}
	}

	return nil
}

func generateGoPackets() error {
	fmt.Println("Generating Go packets for Swift validation...")

	// Test case 1: OPEN packet
	openPacket := createOpenPacket()
	if err := writePacketToFile("open_packet.bin", openPacket); err != nil {
		return fmt.Errorf("failed to write OPEN packet: %w", err)
	}

	// Test case 2: Echo Request packet
	echoPacket := createEchoRequestPacket()
	if err := writePacketToFile("echo_request_packet.bin", echoPacket); err != nil {
		return fmt.Errorf("failed to write Echo Request packet: %w", err)
	}

	// Test case 3: Data packet
	dataPacket := createDataPacket()
	if err := writePacketToFile("data_packet.bin", dataPacket); err != nil {
		return fmt.Errorf("failed to write Data packet: %w", err)
	}

	// Generate test vectors file
	if err := generateTestVectors(); err != nil {
		return fmt.Errorf("failed to generate test vectors: %w", err)
	}

	fmt.Println("Go packets generated successfully!")
	return nil
}

func createOpenPacket() []byte {
	header := PacketHeader{
		Type:      PacketTypeOpen,
		Encrypt:   EncryptAES,
		SessionID: 0,
		Token:     0,
	}

	// Create header bytes
	headerBytes := serializeHeader(header)

	// Calculate MD5 signature
	signature := calculateSignature(headerBytes)

	// Create TLV attributes
	attributes := createOpenAttributes()

	// Combine: header + signature + attributes
	packet := make([]byte, 0, 8+16+len(attributes))
	packet = append(packet, headerBytes...)
	packet = append(packet, signature...)
	packet = append(packet, attributes...)

	return packet
}

func createEchoRequestPacket() []byte {
	header := PacketHeader{
		Type:      PacketTypeEchoRequest,
		Encrypt:   EncryptAES,
		SessionID: 1234,
		Token:     0x56789ABC,
	}

	headerBytes := serializeHeader(header)
	signature := calculateSignature(headerBytes)

	// Add 8-byte timestamp
	timestamp := make([]byte, 8)
	binary.BigEndian.PutUint64(timestamp, uint64(time.Now().Unix()))

	packet := make([]byte, 0, 8+16+8)
	packet = append(packet, headerBytes...)
	packet = append(packet, signature...)
	packet = append(packet, timestamp...)

	return packet
}

func createDataPacket() []byte {
	header := PacketHeader{
		Type:      PacketTypeData,
		Encrypt:   EncryptAES,
		SessionID: 1234,
		Token:     0x56789ABC,
	}

	headerBytes := serializeHeader(header)

	// Create sample IPv4 packet
	ipPacket := []byte{
		0x45, 0x00, 0x00, 0x1C, // Version, IHL, ToS, Total Length
		0x00, 0x01, 0x40, 0x00, // ID, Flags, Fragment Offset
		0x40, 0x01, 0x00, 0x00, // TTL, Protocol, Checksum
		0xC0, 0xA8, 0x01, 0x64, // Source IP: ***********00
		0xC0, 0xA8, 0x01, 0x01, // Dest IP: ***********
		0x48, 0x65, 0x6C, 0x6C, // Payload: "Hell"
		0x6F, 0x21, 0x00, 0x00, // Payload: "o!" + padding
	}

	// Data packets: header + payload (no signature)
	packet := make([]byte, 0, 8+len(ipPacket))
	packet = append(packet, headerBytes...)
	packet = append(packet, ipPacket...)

	return packet
}

func serializeHeader(header PacketHeader) []byte {
	bytes := make([]byte, 8)
	bytes[0] = header.Type
	bytes[1] = header.Encrypt
	binary.BigEndian.PutUint16(bytes[2:4], header.SessionID)
	binary.BigEndian.PutUint32(bytes[4:8], header.Token)
	return bytes
}

func calculateSignature(headerBytes []byte) []byte {
	// MD5(header + "mw")
	hasher := md5.New()
	hasher.Write(headerBytes)
	hasher.Write([]byte{109, 119}) // ASCII 'm', 'w'
	return hasher.Sum(nil)
}

func createOpenAttributes() []byte {
	var attributes []byte

	// MTU attribute (1420)
	mtuAttr := createTLVAttribute(TLVMTU, []byte{0x05, 0x8C}) // 1420 in big endian
	attributes = append(attributes, mtuAttr...)

	// Username attribute
	username := "testuser"
	usernameAttr := createTLVAttribute(TLVUsername, []byte(username))
	attributes = append(attributes, usernameAttr...)

	// Password attribute (encrypted)
	encryptedPassword := make([]byte, 16) // Simplified - just zeros for now
	passwordAttr := createTLVAttribute(TLVPassword, encryptedPassword)
	attributes = append(attributes, passwordAttr...)

	// Encrypt attribute
	encryptAttr := createTLVAttribute(TLVEncrypt, []byte{EncryptAES})
	attributes = append(attributes, encryptAttr...)

	return attributes
}

func createTLVAttribute(attrType uint8, value []byte) []byte {
	length := uint8(len(value) + 2) // +2 for type and length bytes
	attr := make([]byte, 0, int(length))
	attr = append(attr, attrType)
	attr = append(attr, length)
	attr = append(attr, value...)
	return attr
}

func writePacketToFile(filename string, packet []byte) error {
	filePath := filepath.Join(testDataDir, goToSwiftDir, filename)
	return ioutil.WriteFile(filePath, packet, 0644)
}

func generateTestVectors() error {
	content := `# Go-Swift Protocol Interoperability Test Vectors

This directory contains binary packet files generated by Go backend for Swift validation.

## Test Cases:

### OPEN Packet (open_packet.bin)
- **Type**: 0x13 (OPEN)
- **Encryption**: 0x02 (AES)
- **SessionID**: 0
- **Token**: 0
- **Contains**: MTU, Username, Password, Encrypt attributes

### Echo Request Packet (echo_request_packet.bin)
- **Type**: 0x15 (ECHO_REQUEST)
- **Encryption**: 0x02 (AES)
- **SessionID**: 1234
- **Token**: 0x56789ABC
- **Contains**: 8-byte timestamp

### Data Packet (data_packet.bin)
- **Type**: 0x14 (DATA)
- **Encryption**: 0x02 (AES)
- **SessionID**: 1234
- **Token**: 0x56789ABC
- **Contains**: Sample IPv4 packet

## Usage:

1. Run this Go tool to generate test packets
2. Use Swift tests to parse and validate the generated packets
3. Swift tests should generate their own packets in ../swift_to_go/
4. Run this tool again to validate Swift-generated packets

## Validation Points:

- Header format (8 bytes, Big Endian)
- Packet type constants match
- TLV attribute encoding
- MD5 signature calculation
- Encryption method constants
`

	filePath := filepath.Join(testDataDir, goToSwiftDir, "README.md")
	return ioutil.WriteFile(filePath, []byte(content), 0644)
}

func validateSwiftPackets() error {
	fmt.Println("Validating Swift-generated packets...")

	swiftDir := filepath.Join(testDataDir, swiftToGoDir)
	files, err := ioutil.ReadDir(swiftDir)
	if err != nil {
		return fmt.Errorf("failed to read Swift packet directory: %w", err)
	}

	if len(files) == 0 {
		fmt.Println("No Swift packets found for validation (this is expected on first run)")
		return nil
	}

	for _, file := range files {
		if filepath.Ext(file.Name()) == ".bin" {
			if err := validateSwiftPacket(filepath.Join(swiftDir, file.Name())); err != nil {
				return fmt.Errorf("failed to validate Swift packet %s: %w", file.Name(), err)
			}
		}
	}

	fmt.Println("Swift packets validated successfully!")
	return nil
}

func validateSwiftPacket(filePath string) error {
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read packet file: %w", err)
	}

	if len(data) < 8 {
		return fmt.Errorf("packet too short: %d bytes", len(data))
	}

	// Parse header
	packetType := data[0]
	encrypt := data[1]
	sessionID := binary.BigEndian.Uint16(data[2:4])
	token := binary.BigEndian.Uint32(data[4:8])

	fmt.Printf("Validating %s:\n", filepath.Base(filePath))
	fmt.Printf("  Type: 0x%02X, Encrypt: 0x%02X, SID: %d, Token: 0x%08X\n", 
		packetType, encrypt, sessionID, token)
	fmt.Printf("  Data length: %d bytes\n", len(data)-8)
	fmt.Printf("  Data (hex): %s\n", hex.EncodeToString(data[8:]))

	// Basic validation
	if packetType < 0x11 || packetType > 0x20 {
		return fmt.Errorf("invalid packet type: 0x%02X", packetType)
	}

	if encrypt > 2 {
		return fmt.Errorf("invalid encryption method: 0x%02X", encrypt)
	}

	fmt.Printf("  ✓ Packet validation passed\n\n")
	return nil
}
