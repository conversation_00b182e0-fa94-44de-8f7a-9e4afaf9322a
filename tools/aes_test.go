package main

import (
	"crypto/aes"
	"crypto/md5"
	"fmt"
)

func main() {
	// Test with the same parameters as the packet capture
	username := "tmptest"
	password := "test"
	
	fmt.Printf("=== AES Password Encryption Test ===\n")
	fmt.Printf("Username: '%s'\n", username)
	fmt.Printf("Password: '%s'\n", password)
	
	// Step 1: Generate key using MD5("mw" + username)
	keyInput := "mw" + username
	fmt.Printf("Key input: '%s'\n", keyInput)
	
	keyHash := md5.Sum([]byte(keyInput))
	fmt.Printf("MD5 key: %x\n", keyHash[:])
	
	// Step 2: Pad password to 32 bytes
	passwordBytes := []byte(password)
	paddedPassword := make([]byte, 32)
	copy(paddedPassword, passwordBytes)
	
	fmt.Printf("Password bytes: %x\n", passwordBytes)
	fmt.Printf("Padded password (32 bytes): %x\n", paddedPassword)
	
	// Step 3: AES-ECB encryption
	block, err := aes.NewCipher(keyHash[:])
	if err != nil {
		panic(err)
	}
	
	encryptedPassword := make([]byte, 32)
	
	// Encrypt in 16-byte blocks
	for i := 0; i < len(paddedPassword); i += 16 {
		block.Encrypt(encryptedPassword[i:i+16], paddedPassword[i:i+16])
	}
	
	fmt.Printf("Full encrypted (32 bytes): %x\n", encryptedPassword)
	fmt.Printf("First 16 bytes: %x\n", encryptedPassword[:16])
	
	// Expected Windows result
	fmt.Printf("Expected Windows: cb7d3435273173aa7a120628c9bccaaa\n")
	
	// Format for comparison
	fmt.Printf("\nFormatted comparison:\n")
	fmt.Printf("Go result:       %s\n", formatHex(encryptedPassword[:16]))
	fmt.Printf("Windows result:  cb 7d 34 35 27 31 73 aa 7a 12 06 28 c9 bc ca aa\n")
}

func formatHex(data []byte) string {
	result := ""
	for i, b := range data {
		if i > 0 {
			result += " "
		}
		result += fmt.Sprintf("%02x", b)
	}
	return result
}
