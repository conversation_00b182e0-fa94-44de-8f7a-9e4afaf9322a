-- SDWAN Protocol Dissector for Wireshark
-- 用于解析UDP端口8000的VPN协议数据包

-- 导入bit库，用于位操作
-- 在不同版本的Wireshark中，bit库的导入方式可能不同
local bit
if not pcall(function() bit = require("bit") end) then
    -- 如果require("bit")失败，尝试使用Wireshark内置的bit32库
    bit = bit32 or _G.bit32 or _G.bit

    if not bit then
        -- 如果bit32也不可用，创建一个简单的位操作函数集
        bit = {}

        -- 右移位操作
        function bit.rshift(value, shift)
            return math.floor(value / 2^shift)
        end

        -- 按位与操作
        function bit.band(a, b)
            local result = 0
            local bitval = 1
            for i = 0, 31 do
                if (a % 2 == 1) and (b % 2 == 1) then
                    result = result + bitval
                end
                bitval = bitval * 2
                a = math.floor(a / 2)
                b = math.floor(b / 2)
                if a == 0 and b == 0 then break end
            end
            return result
        end
    end
end

-- 创建新的协议
local sdwan_proto = Proto("SDWAN", "SDWAN Protocol")

-- 定义协议字段
local fields = sdwan_proto.fields
fields.type = ProtoField.uint8("sdwan.type", "Type", base.HEX, {
    [0x11] = "OPENREJ (Server rejects client connection)",
    [0x12] = "OPENACK (Server acknowledges client connection)",
    [0x13] = "OPEN (Client requests connection)",
    [0x14] = "DATA (IPv4 data packet)",
    [0x15] = "ECHOREQ (Echo request/keepalive)",
    [0x16] = "ECHORES (Echo response/keepalive)",
    [0x17] = "CLOSE (Connection close)",
    [0x18] = "DATAENC (Encrypted IPv4 data packet)",
    [0x19] = "DATADUP (Duplicate IPv4 data packet)",
    [0x20] = "DATAENCDUP (Encrypted duplicate IPv4 data packet)",
    [0x22] = "IPFRAG (IPv4 fragment packet)",
    [0x23] = "DATA6 (IPv6 data packet)",
    [0x24] = "IPFRAG6 (IPv6 fragment packet)",
    [0x28] = "SEGRT (Segment routing packet)",
    [0x29] = "PINGREQ (Ping request)",
    [0x2A] = "PINGRES (Ping response)"
})

fields.encrypt = ProtoField.uint8("sdwan.encrypt", "Encryption Method", base.HEX, {
    [0x00] = "None",
    [0x01] = "XOR",
    [0x02] = "AES"
})

fields.sid = ProtoField.uint16("sdwan.sid", "Session ID", base.HEX)
fields.token = ProtoField.uint32("sdwan.token", "Token", base.HEX)
fields.md5 = ProtoField.bytes("sdwan.md5", "MD5 Signature")

-- TLV属性字段
fields.tlv_type = ProtoField.uint8("sdwan.tlv.type", "TLV Type", base.HEX, {
    [0x01] = "Username",
    [0x02] = "Password",
    [0x03] = "MTU",
    [0x04] = "IP",
    [0x05] = "DNS",
    [0x06] = "Gateway",
    [0x07] = "Netmask",
    [0x08] = "Encrypt",
    [0x09] = "DupPkt",
    [0x0A] = "Link",
    [0x0B] = "IP6",
    [0x0C] = "DNS6",
    [0x0D] = "Gateway6"
})

fields.tlv_length = ProtoField.uint8("sdwan.tlv.length", "TLV Length")
fields.tlv_value = ProtoField.bytes("sdwan.tlv.value", "TLV Value")

-- 特定TLV值字段
fields.tlv_mtu = ProtoField.uint16("sdwan.tlv.mtu", "MTU Value", base.DEC)
fields.tlv_ip = ProtoField.ipv4("sdwan.tlv.ip", "IP Address")
fields.tlv_dns = ProtoField.ipv4("sdwan.tlv.dns", "DNS Server")
fields.tlv_dns2 = ProtoField.ipv4("sdwan.tlv.dns2", "DNS Server 2")
fields.tlv_gateway = ProtoField.ipv4("sdwan.tlv.gateway", "Gateway")
fields.tlv_netmask = ProtoField.ipv4("sdwan.tlv.netmask", "Netmask")
fields.tlv_username = ProtoField.string("sdwan.tlv.username", "Username")
fields.tlv_encrypt_method = ProtoField.uint8("sdwan.tlv.encrypt_method", "Encryption Method", base.HEX, {
    [0x00] = "None",
    [0x01] = "XOR",
    [0x02] = "AES"
})

-- Echo包字段
fields.echo_timestamp = ProtoField.uint64("sdwan.echo.timestamp", "Timestamp", base.DEC)
fields.echo_cur_delay = ProtoField.uint32("sdwan.echo.cur_delay", "Current Delay", base.DEC)
fields.echo_min_delay = ProtoField.uint32("sdwan.echo.min_delay", "Minimum Delay", base.DEC)
fields.echo_max_delay = ProtoField.uint32("sdwan.echo.max_delay", "Maximum Delay", base.DEC)
fields.echo_sdrt = ProtoField.string("sdwan.echo.sdrt", "SDRT Tag")

-- IP分片包字段
fields.frag_id = ProtoField.uint32("sdwan.frag.id", "Fragment ID", base.HEX)
fields.frag_info = ProtoField.uint32("sdwan.frag.info", "Fragment Info", base.HEX)
fields.frag_eop = ProtoField.uint8("sdwan.frag.eop", "End of Packet", base.DEC, {[0] = "No", [1] = "Yes"})
fields.frag_offset = ProtoField.uint16("sdwan.frag.offset", "Fragment Offset", base.DEC)
fields.frag_length = ProtoField.uint16("sdwan.frag.length", "Fragment Length", base.DEC)
fields.frag_data = ProtoField.bytes("sdwan.frag.data", "Fragment Data")

-- 解析TLV属性
local function parse_tlv(buffer, offset, tree, pinfo)
    local pos = offset
    local buffer_len = buffer:len()

    while pos < buffer_len do
        -- 确保至少有类型和长度字段
        if pos + 2 > buffer_len then
            break
        end

        local attr_type = buffer(pos, 1):uint()
        local attr_len = buffer(pos + 1, 1):uint()

        -- 检查长度是否合理
        if attr_len < 2 then
            pos = pos + 1
            goto continue
        end

        -- 检查是否超出缓冲区
        if pos + attr_len > buffer_len then
            -- 尝试使用预期长度
            local expected_len = 0

            if attr_type == 0x01 then  -- Username
                expected_len = 2 + buffer(pos + 2, buffer_len - pos - 2):len()
            elseif attr_type == 0x02 then  -- Password
                expected_len = 18  -- 2 + 16 (MD5)
            elseif attr_type == 0x03 then  -- MTU
                expected_len = 4   -- 2 + 2
            elseif attr_type == 0x04 or attr_type == 0x06 or attr_type == 0x07 then  -- IP, Gateway, Netmask
                expected_len = 6   -- 2 + 4
            elseif attr_type == 0x05 then  -- DNS
                expected_len = 10  -- 2 + 4 + 4
            elseif attr_type == 0x08 or attr_type == 0x09 then  -- Encrypt, DupPkt
                expected_len = 3   -- 2 + 1
            elseif attr_type == 0x0B or attr_type == 0x0C or attr_type == 0x0D then  -- IP6, DNS6, Gateway6
                expected_len = 18  -- 2 + 16
            end

            if expected_len > 0 and pos + expected_len <= buffer_len then
                attr_len = expected_len
            else
                break
            end
        end

        -- 创建TLV子树
        local tlv_item = tree:add(sdwan_proto, buffer(pos, attr_len), string.format("TLV: %s (0x%02X)",
            get_tlv_type_name(attr_type), attr_type))

        tlv_item:add(fields.tlv_type, buffer(pos, 1))
        tlv_item:add(fields.tlv_length, buffer(pos + 1, 1))

        -- 解析特定类型的TLV值
        if attr_type == 0x01 then  -- Username
            local username_len = attr_len - 2
            if username_len > 0 then
                local username = buffer(pos + 2, username_len):string()
                tlv_item:add(fields.tlv_username, buffer(pos + 2, username_len))
                tlv_item:append_text(string.format(": %s", username))
            end
        elseif attr_type == 0x02 then  -- Password (加密的)
            tlv_item:add(fields.tlv_value, buffer(pos + 2, attr_len - 2))
        elseif attr_type == 0x03 then  -- MTU
            if attr_len >= 4 then
                local mtu = buffer(pos + 2, 2):uint()
                tlv_item:add(fields.tlv_mtu, buffer(pos + 2, 2))
                tlv_item:append_text(string.format(": %d", mtu))
            end
        elseif attr_type == 0x04 then  -- IP
            if attr_len >= 6 then
                tlv_item:add(fields.tlv_ip, buffer(pos + 2, 4))
                tlv_item:append_text(string.format(": %s", format_ipv4(buffer(pos + 2, 4):bytes())))
            end
        elseif attr_type == 0x05 then  -- DNS
            if attr_len >= 6 then
                tlv_item:add(fields.tlv_dns, buffer(pos + 2, 4))
                tlv_item:append_text(string.format(": %s", format_ipv4(buffer(pos + 2, 4):bytes())))

                -- 检查是否有第二个DNS
                if attr_len >= 10 then
                    tlv_item:add(fields.tlv_dns2, buffer(pos + 6, 4))
                    tlv_item:append_text(string.format(", %s", format_ipv4(buffer(pos + 6, 4):bytes())))
                end
            end
        elseif attr_type == 0x06 then  -- Gateway
            if attr_len >= 6 then
                tlv_item:add(fields.tlv_gateway, buffer(pos + 2, 4))
                tlv_item:append_text(string.format(": %s", format_ipv4(buffer(pos + 2, 4):bytes())))
            end
        elseif attr_type == 0x07 then  -- Netmask
            if attr_len >= 6 then
                tlv_item:add(fields.tlv_netmask, buffer(pos + 2, 4))
                tlv_item:append_text(string.format(": %s", format_ipv4(buffer(pos + 2, 4):bytes())))
            end
        elseif attr_type == 0x08 then  -- Encrypt
            if attr_len >= 3 then
                local encrypt_method = buffer(pos + 2, 1):uint()
                tlv_item:add(fields.tlv_encrypt_method, buffer(pos + 2, 1))
                tlv_item:append_text(string.format(": %s", get_encrypt_method_name(encrypt_method)))
            end
        else
            -- 其他类型的TLV，显示原始值
            if attr_len > 2 then
                tlv_item:add(fields.tlv_value, buffer(pos + 2, attr_len - 2))
            end
        end

        pos = pos + attr_len
        ::continue::
    end

    return pos
end

-- 解析Echo包
local function parse_echo_packet(buffer, offset, tree)
    if buffer:len() < offset + 36 then
        return
    end

    -- 跳过MD5签名
    local pos = offset + 16

    -- 解析时间戳
    tree:add(fields.echo_timestamp, buffer(pos, 8))
    pos = pos + 8

    -- 解析延迟信息
    tree:add(fields.echo_cur_delay, buffer(pos, 4))
    pos = pos + 4

    tree:add(fields.echo_min_delay, buffer(pos, 4))
    pos = pos + 4

    tree:add(fields.echo_max_delay, buffer(pos, 4))
    pos = pos + 4

    -- 解析SDRT标签
    if buffer:len() >= pos + 4 then
        local sdrt = buffer(pos, 4):string()
        tree:add(fields.echo_sdrt, buffer(pos, 4))
    end
end

-- 解析IP分片包
local function parse_fragment_packet(buffer, offset, tree, pinfo)
    if buffer:len() < offset + 8 then
        return
    end

    -- 解析分片头
    local id = buffer(offset, 4):uint()
    local frag_info = buffer(offset + 4, 4):uint()

    -- 提取分片信息
    -- 注意: 这里的位操作与Java实现保持一致
    local eop = bit.band(bit.rshift(frag_info, 31), 0x01)
    local offset_value = bit.band(bit.rshift(frag_info, 18), 0x1FFF)
    local length = bit.band(bit.rshift(frag_info, 7), 0x7FF)

    -- 创建分片子树
    local frag_tree = tree:add(sdwan_proto, buffer(offset, buffer:len() - offset), "Fragment Header")

    -- 添加分片字段
    frag_tree:add(fields.frag_id, buffer(offset, 4)):append_text(string.format(" (0x%08X)", id))
    frag_tree:add(fields.frag_info, buffer(offset + 4, 4)):append_text(string.format(" (0x%08X)", frag_info))
    frag_tree:add(fields.frag_eop, eop)
    frag_tree:add(fields.frag_offset, offset_value)
    frag_tree:add(fields.frag_length, length)

    -- 更新Info列
    pinfo.cols.info = string.format("%s, ID=0x%08X, EOP=%d, Offset=%d, Length=%d",
                                    pinfo.cols.info, id, eop, offset_value, length)

    -- 如果有分片数据
    if buffer:len() > offset + 8 then
        local actual_data_length = buffer:len() - (offset + 8)
        local data_length = length

        -- 检查实际数据长度是否足够
        if actual_data_length < length then
            data_length = actual_data_length
            frag_tree:add(buffer(offset + 8, data_length), string.format("Fragment Data (Truncated: %d of %d bytes)", data_length, length))

            -- 添加警告信息
            frag_tree:add(buffer(offset + 4, 4), string.format("[WARNING: Declared length (%d) exceeds actual data length (%d)]", length, actual_data_length)):set_generated(true):set_text_color(0x7F0000) -- 深红色
        else
            frag_tree:add(fields.frag_data, buffer(offset + 8, data_length))
        end

        -- 如果有数据，尝试解析IP头
        if data_length > 0 then
            -- 检查是否可能是IP包
            if data_length >= 1 then
                local ip_version_byte = buffer(offset + 8, 1):uint()
                local ip_version = bit.rshift(ip_version_byte, 4)

                if ip_version == 4 and data_length >= 20 then
                    -- IPv4包
                    local ip_tvb = buffer(offset + 8, data_length):tvb("Encapsulated IPv4 Fragment")
                    Dissector.get("ip"):call(ip_tvb, pinfo, tree)
                elseif ip_version == 6 and data_length >= 40 then
                    -- IPv6包
                    local ipv6_tvb = buffer(offset + 8, data_length):tvb("Encapsulated IPv6 Fragment")
                    Dissector.get("ipv6"):call(ipv6_tvb, pinfo, tree)
                else
                    -- 不是标准IP包或数据不足
                    frag_tree:add(buffer(offset + 8, 1), string.format("[IP Version: %d (0x%02X)]", ip_version, ip_version_byte))
                    if ip_version == 4 and data_length < 20 then
                        frag_tree:add(buffer(offset + 8, data_length), "[WARNING: Insufficient data for IPv4 header (need 20 bytes)]"):set_generated(true):set_text_color(0x7F0000)
                    elseif ip_version == 6 and data_length < 40 then
                        frag_tree:add(buffer(offset + 8, data_length), "[WARNING: Insufficient data for IPv6 header (need 40 bytes)]"):set_generated(true):set_text_color(0x7F0000)
                    end
                end
            end
        end
    else
        -- 没有数据
        frag_tree:add(buffer(offset + 8, 0), "[No fragment data]"):set_generated(true)
    end
end

-- 辅助函数：获取TLV类型名称
function get_tlv_type_name(type_value)
    local type_names = {
        [0x01] = "Username",
        [0x02] = "Password",
        [0x03] = "MTU",
        [0x04] = "IP",
        [0x05] = "DNS",
        [0x06] = "Gateway",
        [0x07] = "Netmask",
        [0x08] = "Encrypt",
        [0x09] = "DupPkt",
        [0x0A] = "Link",
        [0x0B] = "IP6",
        [0x0C] = "DNS6",
        [0x0D] = "Gateway6"
    }

    return type_names[type_value] or string.format("Unknown (0x%02X)", type_value)
end

-- 辅助函数：获取加密方法名称
function get_encrypt_method_name(method_value)
    local method_names = {
        [0x00] = "None",
        [0x01] = "XOR",
        [0x02] = "AES"
    }

    return method_names[method_value] or string.format("Unknown (0x%02X)", method_value)
end

-- 辅助函数：格式化IPv4地址
function format_ipv4(bytes)
    return string.format("%d.%d.%d.%d", bytes:get_index(0), bytes:get_index(1), bytes:get_index(2), bytes:get_index(3))
end

-- 协议解析函数
function sdwan_proto.dissector(buffer, pinfo, tree)
    -- 检查数据包长度
    local length = buffer:len()
    if length < 8 then return false end

    -- 设置协议列
    pinfo.cols.protocol = sdwan_proto.name

    -- 创建协议树
    local subtree = tree:add(sdwan_proto, buffer(), "SDWAN ZZVPN Protocol")

    -- 解析包头
    local packet_type = buffer(0, 1):uint()
    local encrypt_method = buffer(1, 1):uint()
    local sid = buffer(2, 2):uint()
    local token = buffer(4, 4):uint()

    subtree:add(fields.type, buffer(0, 1))
    subtree:add(fields.encrypt, buffer(1, 1))
    subtree:add(fields.sid, buffer(2, 2))
    subtree:add(fields.token, buffer(4, 4))

    -- 设置Info列
    local packet_type_name = ""
    if packet_type == 0x11 then
        packet_type_name = "OPENREJ"
    elseif packet_type == 0x12 then
        packet_type_name = "OPENACK"
    elseif packet_type == 0x13 then
        packet_type_name = "OPEN"
    elseif packet_type == 0x14 then
        packet_type_name = "DATA"
    elseif packet_type == 0x15 then
        packet_type_name = "ECHOREQ"
    elseif packet_type == 0x16 then
        packet_type_name = "ECHORES"
    elseif packet_type == 0x17 then
        packet_type_name = "CLOSE"
    elseif packet_type == 0x18 then
        packet_type_name = "DATAENC"
    elseif packet_type == 0x19 then
        packet_type_name = "DATADUP"
    elseif packet_type == 0x20 then
        packet_type_name = "DATAENCDUP"
    elseif packet_type == 0x22 then
        packet_type_name = "IPFRAG"
    elseif packet_type == 0x23 then
        packet_type_name = "DATA6"
    elseif packet_type == 0x24 then
        packet_type_name = "IPFRAG6"
    elseif packet_type == 0x28 then
        packet_type_name = "SEGRT"
    elseif packet_type == 0x29 then
        packet_type_name = "PINGREQ"
    elseif packet_type == 0x2A then
        packet_type_name = "PINGRES"
    else
        packet_type_name = string.format("Unknown (0x%02X)", packet_type)
    end

    pinfo.cols.info = string.format("%s, SID=0x%04X, Token=0x%08X", packet_type_name, sid, token)

    -- 如果有数据部分
    if length > 8 then
        -- 检查是否需要解析MD5签名
        if packet_type ~= 0x14 and packet_type ~= 0x18 and packet_type ~= 0x19 and packet_type ~= 0x20 and
           packet_type ~= 0x22 and packet_type ~= 0x23 and packet_type ~= 0x24 then
            -- 控制包需要验证签名
            if length >= 24 then  -- 至少包含8字节头部和16字节MD5
                subtree:add(fields.md5, buffer(8, 16)):append_text(" (MD5 Signature)")

                -- 根据包类型解析剩余数据
                if packet_type == 0x13 then  -- OPEN
                    -- 解析TLV属性
                    parse_tlv(buffer, 24, subtree, pinfo)
                elseif packet_type == 0x12 then  -- OPENACK
                    -- 解析TLV属性
                    parse_tlv(buffer, 24, subtree, pinfo)
                elseif packet_type == 0x11 then  -- OPENREJ
                    -- OPENREJ包可能包含一个拒绝原因代码
                    if length > 24 then
                        local reason = buffer(24, 1):uint()
                        local reason_str = "Unknown"

                        if reason == 0 then
                            reason_str = "Unknown"
                        elseif reason == 1 then
                            reason_str = "Invalid username"
                        elseif reason == 2 then
                            reason_str = "Invalid password"
                        elseif reason == 3 then
                            reason_str = "Server is full"
                        elseif reason == 4 then
                            reason_str = "Server error"
                        elseif reason == 5 then
                            reason_str = "Unsupported feature"
                        elseif reason == 6 then
                            reason_str = "Account expired"
                        elseif reason == 7 then
                            reason_str = "Account disabled"
                        elseif reason == 8 then
                            reason_str = "Maximum sessions reached"
                        elseif reason == 9 then
                        elseif reason == 9 then
                            reason_str = "Invalid token"
                        end

                        subtree:add(buffer(24, 1), string.format("Reject Reason: %s (%d)", reason_str, reason))
                        pinfo.cols.info = string.format("%s, Reason: %s", pinfo.cols.info, reason_str)
                    end
                elseif packet_type == 0x15 or packet_type == 0x16 then  -- ECHOREQ or ECHORES
                    -- 解析Echo包
                    parse_echo_packet(buffer, 8, subtree)
                end
            end
        else
            -- 数据包，直接显示数据
            subtree:add(buffer(8), "Data")

            -- 如果是IPv4或IPv6数据包，尝试解析
            if packet_type == 0x14 or packet_type == 0x19 then  -- DATA or DATADUP
                -- 尝试调用IP解析器
                local ip_tvb = buffer(8):tvb("Encapsulated IP Packet")
                Dissector.get("ip"):call(ip_tvb, pinfo, tree)
            elseif packet_type == 0x23 then  -- DATA6
                -- 尝试调用IPv6解析器
                local ipv6_tvb = buffer(8):tvb("Encapsulated IPv6 Packet")
                Dissector.get("ipv6"):call(ipv6_tvb, pinfo, tree)
            elseif packet_type == 0x22 or packet_type == 0x24 then  -- IPFRAG or IPFRAG6
                -- 解析IP分片包
                parse_fragment_packet(buffer, 8, subtree, pinfo)
            elseif packet_type == 0x18 or packet_type == 0x20 then  -- DATAENC or DATAENCDUP
                subtree:add(buffer(8), "Encrypted Data (cannot be decoded)")
            end
        end
    end

    return true
end

-- 注册解析器
local udp_port = DissectorTable.get("udp.port")
udp_port:add(8000, sdwan_proto)

-- 注册解析器的首选项
sdwan_proto.prefs.port = Pref.uint("UDP Port", 8000, "UDP port for SDWAN Protocol")

-- 当首选项更改时更新端口
function sdwan_proto.prefs_changed()
    -- 移除旧端口
    if default_settings.port ~= sdwan_proto.prefs.port then
        udp_port:remove(default_settings.port, sdwan_proto)
        -- 添加新端口
        udp_port:add(sdwan_proto.prefs.port, sdwan_proto)
        default_settings.port = sdwan_proto.prefs.port
    end
end

-- 存储默认设置
local default_settings = {
    port = sdwan_proto.prefs.port
}

return sdwan_proto
