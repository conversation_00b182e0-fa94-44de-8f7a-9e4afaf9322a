#!/usr/bin/env swift

import Foundation
import CryptoKit
import CommonCrypto

// Replicate the exact Swift AES implementation from ItForceCore
func encryptPassword(_ password: String, username: String) throws -> Data {
    // Generate password encryption key using MD5("mw" + username)
    let combined = "mw" + username
    guard let keyData = combined.data(using: .utf8) else {
        throw NSError(domain: "EncryptionError", code: 1, userInfo: nil)
    }
    
    let key = Insecure.MD5.hash(data: keyData)
    
    // Pad password to 32 bytes (consistent with Go backend)
    guard let passwordData = password.data(using: .utf8) else {
        throw NSError(domain: "EncryptionError", code: 2, userInfo: nil)
    }
    
    var paddedPassword = Data(count: 32)
    let copyLength = min(passwordData.count, 32)
    paddedPassword.replaceSubrange(0..<copyLength, with: passwordData.prefix(copyLength))
    
    // Debug output
    print("=== Swift AES Password Encryption Test ===")
    print("Username: '\(username)'")
    print("Password: '\(password)'")
    print("Key input: '\(combined)'")
    print("MD5 key: \(Data(key).map { String(format: "%02x", $0) }.joined(separator: " "))")
    print("Password bytes: \(passwordData.map { String(format: "%02x", $0) }.joined(separator: " "))")
    print("Padded password (32 bytes): \(paddedPassword.map { String(format: "%02x", $0) }.joined(separator: " "))")
    
    // Encrypt using AES-ECB mode
    let encryptedPassword = try encryptAESECB(data: paddedPassword, key: Data(key))
    
    // Return only first 16 bytes (consistent with Go backend)
    let result = encryptedPassword.prefix(16)
    
    print("Encrypted result: \(result.map { String(format: "%02x", $0) }.joined(separator: " "))")
    print("Expected Go result: cb 7d 34 35 27 31 73 aa 7a 12 06 28 c9 bc ca aa")
    
    let expected = "cb7d3435273173aa7a120628c9bccaaa"
    let actual = result.map { String(format: "%02x", $0) }.joined()
    if actual == expected {
        print("✅ PERFECT MATCH with Go backend!")
    } else {
        print("❌ Mismatch with Go backend")
        print("Expected: \(expected)")
        print("Actual:   \(actual)")
    }
    
    return Data(result)
}

func encryptAESECB(data: Data, key: Data) throws -> Data {
    // Use CommonCrypto for AES-ECB encryption (no padding)
    let keyBytes = key.withUnsafeBytes { $0.bindMemory(to: UInt8.self) }
    let dataBytes = data.withUnsafeBytes { $0.bindMemory(to: UInt8.self) }
    
    var encryptedData = Data(count: data.count)
    var numBytesEncrypted: size_t = 0
    
    let status = encryptedData.withUnsafeMutableBytes { encryptedBytes in
        CCCrypt(
            CCOperation(kCCEncrypt),
            CCAlgorithm(kCCAlgorithmAES),
            CCOptions(kCCOptionECBMode),  // ECB mode, no padding
            keyBytes.baseAddress,
            key.count,
            nil,  // No IV for ECB
            dataBytes.baseAddress,
            data.count,
            encryptedBytes.bindMemory(to: UInt8.self).baseAddress,
            data.count,
            &numBytesEncrypted
        )
    }
    
    guard status == kCCSuccess else {
        throw NSError(domain: "AESError", code: Int(status), userInfo: nil)
    }
    
    return encryptedData
}

// Test with the correct credentials
do {
    let _ = try encryptPassword("itforce", username: "tmptest")
} catch {
    print("Error: \(error)")
}
