/**
 * FILE: android_go_interop_test.go
 *
 * DESCRIPTION:
 *     Android-Go protocol interoperability test tool.
 *     Generates test packets using Go backend for Android validation.
 *     Validates Android-generated packets with Go parser.
 *     Tests bidirectional compatibility and data integrity.
 *
 * AUTHOR: wei
 * HISTORY: 16/07/2025 create Android-Go interop test
 */

package main

import (
	"crypto/md5"
	"encoding/binary"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"time"
)

const (
	testDataDir    = "../ui/flutter/android/app/src/test/resources"
	goToAndroidDir = "go_to_android"
	androidToGoDir = "android_to_go"
)

// Packet types (matching Go backend constants)
const (
	PacketTypeOpen         uint8 = 0x13
	PacketTypeOpenAck      uint8 = 0x12
	PacketTypeOpenReject   uint8 = 0x11
	PacketTypeData         uint8 = 0x14
	PacketTypeDataEncrypt  uint8 = 0x17
	PacketTypeEchoRequest  uint8 = 0x15
	PacketTypeEchoResponse uint8 = 0x16
)

// Encryption methods
const (
	EncryptNone uint8 = 0x00
	EncryptXOR  uint8 = 0x01
	EncryptAES  uint8 = 0x02
)

// TLV attribute types
const (
	AttributeUsername = 0x01
	AttributePassword = 0x02
	AttributeMTU      = 0x03
	AttributeEncrypt  = 0x04
)

type PacketHeader struct {
	Type      uint8
	Encrypt   uint8
	SessionID uint16
	Token     uint32
}

type TestPacket struct {
	Name        string
	Description string
	Header      PacketHeader
	RawData     []byte
}

func main() {
	fmt.Println("=== Android-Go Protocol Interoperability Test ===")
	fmt.Println()

	// Initialize test directories
	if err := initializeDirectories(); err != nil {
		fmt.Printf("❌ Failed to initialize directories: %v\n", err)
		os.Exit(1)
	}

	// Generate Go packets for Android validation
	if err := generateGoPackets(); err != nil {
		fmt.Printf("❌ Failed to generate Go packets: %v\n", err)
		os.Exit(1)
	}

	fmt.Println()

	// Validate Android-generated packets (if any exist)
	if err := validateAndroidPackets(); err != nil {
		fmt.Printf("❌ Failed to validate Android packets: %v\n", err)
		os.Exit(1)
	}

	fmt.Println()
	fmt.Println("✅ Android-Go interoperability test completed successfully!")
}

func initializeDirectories() error {
	dirs := []string{
		filepath.Join(testDataDir, goToAndroidDir),
		filepath.Join(testDataDir, androidToGoDir),
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %w", dir, err)
		}
	}

	return nil
}

func generateGoPackets() error {
	fmt.Println("🔧 Generating Go packets for Android validation...")

	testPackets := []TestPacket{
		// Test 1: OPEN packet with XOR encryption
		{
			Name:        "open_xor",
			Description: "OPEN packet with XOR encryption for authentication test",
			Header: PacketHeader{
				Type:      PacketTypeOpen,
				Encrypt:   EncryptXOR,
				SessionID: 0,
				Token:     0,
			},
			RawData: createOpenPacket("testuser", "testpass", 1420, EncryptXOR),
		},

		// Test 2: OPEN_ACK packet with session info
		{
			Name:        "openack_xor",
			Description: "OPEN_ACK packet with session information",
			Header: PacketHeader{
				Type:      PacketTypeOpenAck,
				Encrypt:   EncryptXOR,
				SessionID: 0x1234,
				Token:     0xDEADBEEF,
			},
			RawData: createOpenAckPacket(0x1234, 0xDEADBEEF, EncryptXOR),
		},

		// Test 3: Encrypted data packet
		{
			Name:        "data_encrypted_xor",
			Description: "Encrypted data packet with XOR encryption",
			Header: PacketHeader{
				Type:      PacketTypeDataEncrypt,
				Encrypt:   EncryptXOR,
				SessionID: 0x1234,
				Token:     0xDEADBEEF,
			},
			RawData: createEncryptedDataPacket(0x1234, 0xDEADBEEF, EncryptXOR),
		},

		// Test 4: Unencrypted data packet
		{
			Name:        "data_plain",
			Description: "Plain data packet without encryption",
			Header: PacketHeader{
				Type:      PacketTypeData,
				Encrypt:   EncryptNone,
				SessionID: 0x5678,
				Token:     0x12345678,
			},
			RawData: createDataPacket(0x5678, 0x12345678, EncryptNone),
		},

		// Test 5: Echo request packet
		{
			Name:        "echo_request_xor",
			Description: "Echo request packet for heartbeat test",
			Header: PacketHeader{
				Type:      PacketTypeEchoRequest,
				Encrypt:   EncryptXOR,
				SessionID: 0x1234,
				Token:     0xDEADBEEF,
			},
			RawData: createEchoRequestPacket(0x1234, 0xDEADBEEF, EncryptXOR),
		},
	}

	for i, packet := range testPackets {
		filename := fmt.Sprintf("go_packet_%02d_%s.bin", i+1, packet.Name)
		filepath := filepath.Join(testDataDir, goToAndroidDir, filename)

		if err := ioutil.WriteFile(filepath, packet.RawData, 0644); err != nil {
			return fmt.Errorf("failed to write packet %s: %w", filename, err)
		}

		fmt.Printf("✅ Generated: %s (%d bytes) - %s\n", filename, len(packet.RawData), packet.Description)

		// Generate metadata file for Android tests
		metadataFile := filepath + ".meta"
		metadata := fmt.Sprintf("Name: %s\nDescription: %s\nType: 0x%02X\nEncrypt: 0x%02X\nSessionID: %d\nToken: %d\nSize: %d bytes\n",
			packet.Name, packet.Description, packet.Header.Type, packet.Header.Encrypt,
			packet.Header.SessionID, packet.Header.Token, len(packet.RawData))

		if err := ioutil.WriteFile(metadataFile, []byte(metadata), 0644); err != nil {
			return fmt.Errorf("failed to write metadata %s: %w", metadataFile, err)
		}
	}

	// Generate README for Android developers
	if err := generateReadme(); err != nil {
		return fmt.Errorf("failed to generate README: %w", err)
	}

	return nil
}

func createOpenPacket(username, password string, mtu uint16, encryptMethod uint8) []byte {
	header := PacketHeader{
		Type:      PacketTypeOpen,
		Encrypt:   encryptMethod,
		SessionID: 0,
		Token:     0,
	}

	headerBytes := serializeHeader(header)
	signature := calculateSignature(headerBytes)
	attributes := createOpenAttributes(username, password, mtu, encryptMethod)

	// Combine: header + signature + attributes
	packet := make([]byte, 0, 8+16+len(attributes))
	packet = append(packet, headerBytes...)
	packet = append(packet, signature...)
	packet = append(packet, attributes...)

	return packet
}

func createOpenAckPacket(sessionID uint16, token uint32, encryptMethod uint8) []byte {
	header := PacketHeader{
		Type:      PacketTypeOpenAck,
		Encrypt:   encryptMethod,
		SessionID: sessionID,
		Token:     token,
	}

	headerBytes := serializeHeader(header)
	signature := calculateSignature(headerBytes)

	// Create server configuration attributes
	attributes := createServerConfigAttributes()

	// Combine: header + signature + attributes
	packet := make([]byte, 0, 8+16+len(attributes))
	packet = append(packet, headerBytes...)
	packet = append(packet, signature...)
	packet = append(packet, attributes...)

	return packet
}

func createEncryptedDataPacket(sessionID uint16, token uint32, encryptMethod uint8) []byte {
	header := PacketHeader{
		Type:      PacketTypeDataEncrypt,
		Encrypt:   encryptMethod,
		SessionID: sessionID,
		Token:     token,
	}

	headerBytes := serializeHeader(header)

	// Create sample IP packet
	ipPacket := []byte{
		0x45, 0x00, 0x00, 0x3C, // IP header
		0x1C, 0x46, 0x40, 0x00,
		0x40, 0x06, 0x00, 0x00,
		0xC0, 0xA8, 0x01, 0x64, // Source IP
		0xC0, 0xA8, 0x01, 0x01, // Dest IP
		// Payload: "Hello from Go!"
		0x48, 0x65, 0x6C, 0x6C, 0x6F, 0x20, 0x66, 0x72,
		0x6F, 0x6D, 0x20, 0x47, 0x6F, 0x21, 0x00, 0x00,
	}

	// For testing purposes, we'll use the raw IP packet as "encrypted" data
	// In real implementation, this would be encrypted with session key
	packet := make([]byte, 0, 8+len(ipPacket))
	packet = append(packet, headerBytes...)
	packet = append(packet, ipPacket...)

	return packet
}

func createDataPacket(sessionID uint16, token uint32, encryptMethod uint8) []byte {
	header := PacketHeader{
		Type:      PacketTypeData,
		Encrypt:   encryptMethod,
		SessionID: sessionID,
		Token:     token,
	}

	headerBytes := serializeHeader(header)

	// Create sample IP packet
	ipPacket := []byte{
		0x45, 0x00, 0x00, 0x28, // IP header
		0x1C, 0x47, 0x40, 0x00,
		0x40, 0x01, 0x00, 0x00,
		0xC0, 0xA8, 0x01, 0x01, // Source IP
		0xC0, 0xA8, 0x01, 0x64, // Dest IP
		// ICMP payload
		0x08, 0x00, 0xF7, 0xFC, 0x00, 0x00, 0x00, 0x00,
	}

	packet := make([]byte, 0, 8+len(ipPacket))
	packet = append(packet, headerBytes...)
	packet = append(packet, ipPacket...)

	return packet
}

func createEchoRequestPacket(sessionID uint16, token uint32, encryptMethod uint8) []byte {
	header := PacketHeader{
		Type:      PacketTypeEchoRequest,
		Encrypt:   encryptMethod,
		SessionID: sessionID,
		Token:     token,
	}

	headerBytes := serializeHeader(header)
	signature := calculateSignature(headerBytes)

	// Create timestamp (8 bytes, microseconds)
	timestamp := uint64(time.Now().UnixNano() / 1000)
	timestampBytes := make([]byte, 8)
	binary.BigEndian.PutUint64(timestampBytes, timestamp)

	packet := make([]byte, 0, 8+16+8)
	packet = append(packet, headerBytes...)
	packet = append(packet, signature...)
	packet = append(packet, timestampBytes...)

	return packet
}

func serializeHeader(header PacketHeader) []byte {
	bytes := make([]byte, 8)
	bytes[0] = header.Type
	bytes[1] = header.Encrypt
	binary.BigEndian.PutUint16(bytes[2:4], header.SessionID)
	binary.BigEndian.PutUint32(bytes[4:8], header.Token)
	return bytes
}

func calculateSignature(headerBytes []byte) []byte {
	hasher := md5.New()
	hasher.Write(headerBytes)
	hasher.Write([]byte{109, 119}) // ASCII 'm', 'w'
	return hasher.Sum(nil)
}

func createOpenAttributes(username, password string, mtu uint16, encryptMethod uint8) []byte {
	var attributes []byte

	// MTU attribute (must be first)
	mtuBytes := make([]byte, 4)
	binary.BigEndian.PutUint16(mtuBytes[0:2], mtu)
	attributes = append(attributes, createTLVAttribute(AttributeMTU, mtuBytes)...)

	// Username attribute (must be second)
	attributes = append(attributes, createTLVAttribute(AttributeUsername, []byte(username))...)

	// Password attribute (must be third) - encrypted with AES
	encryptedPassword := encryptPassword(password, username)
	attributes = append(attributes, createTLVAttribute(AttributePassword, encryptedPassword)...)

	// Encryption method attribute
	attributes = append(attributes, createTLVAttribute(AttributeEncrypt, []byte{encryptMethod})...)

	return attributes
}

func createServerConfigAttributes() []byte {
	var attributes []byte

	// Server IP attribute (example)
	serverIP := []byte{192, 168, 1, 1}
	attributes = append(attributes, createTLVAttribute(0x10, serverIP)...)

	// DNS server attribute (example)
	dnsServer := []byte{8, 8, 8, 8}
	attributes = append(attributes, createTLVAttribute(0x11, dnsServer)...)

	return attributes
}

func createTLVAttribute(attrType uint8, value []byte) []byte {
	attr := make([]byte, 2+len(value))
	attr[0] = attrType
	attr[1] = uint8(len(value))
	copy(attr[2:], value)
	return attr
}

func encryptPassword(password, username string) []byte {
	// Simplified password encryption for testing
	// In real implementation, this would use AES-ECB with MD5("mw" + username) key
	key := md5.Sum([]byte("mw" + username))
	encrypted := make([]byte, 16) // Take first 16 bytes

	passwordBytes := []byte(password)
	for i := 0; i < len(encrypted) && i < len(passwordBytes); i++ {
		encrypted[i] = passwordBytes[i] ^ key[i%16]
	}

	return encrypted
}

func validateAndroidPackets() error {
	fmt.Println("🔍 Validating Android-generated packets...")

	androidDir := filepath.Join(testDataDir, androidToGoDir)
	files, err := ioutil.ReadDir(androidDir)
	if err != nil {
		fmt.Printf("⚠️  No Android packets found in %s\n", androidDir)
		return nil
	}

	validCount := 0
	totalCount := 0

	for _, file := range files {
		if filepath.Ext(file.Name()) == ".bin" {
			totalCount++
			filePath := filepath.Join(androidDir, file.Name())

			if err := validatePacketFile(filePath); err != nil {
				fmt.Printf("❌ %s: %v\n", file.Name(), err)
			} else {
				fmt.Printf("✅ %s: Valid\n", file.Name())
				validCount++
			}
		}
	}

	if totalCount == 0 {
		fmt.Println("⚠️  No Android packets found for validation")
		fmt.Println("   Run Android tests first to generate packets")
	} else {
		fmt.Printf("📊 Validation Summary: %d/%d packets valid\n", validCount, totalCount)
	}

	return nil
}

func validatePacketFile(filePath string) error {
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read file: %w", err)
	}

	if len(data) < 8 {
		return fmt.Errorf("packet too short: %d bytes", len(data))
	}

	// Parse header
	header := PacketHeader{
		Type:      data[0],
		Encrypt:   data[1],
		SessionID: binary.BigEndian.Uint16(data[2:4]),
		Token:     binary.BigEndian.Uint32(data[4:8]),
	}

	// Validate packet type
	validTypes := []uint8{
		PacketTypeOpen, PacketTypeOpenAck, PacketTypeOpenReject,
		PacketTypeData, PacketTypeDataEncrypt,
		PacketTypeEchoRequest, PacketTypeEchoResponse,
	}

	typeValid := false
	for _, validType := range validTypes {
		if header.Type == validType {
			typeValid = true
			break
		}
	}

	if !typeValid {
		return fmt.Errorf("invalid packet type: 0x%02X", header.Type)
	}

	// Validate encryption method
	if header.Encrypt > EncryptAES {
		return fmt.Errorf("invalid encryption method: 0x%02X", header.Encrypt)
	}

	// For control packets, verify signature if present
	if header.Type != PacketTypeData && header.Type != PacketTypeDataEncrypt && len(data) >= 24 {
		headerBytes := data[:8]
		signature := data[8:24]
		expectedSignature := calculateSignature(headerBytes)

		if !bytesEqual(signature, expectedSignature) {
			return fmt.Errorf("invalid signature")
		}
	}

	return nil
}

func bytesEqual(a, b []byte) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

func generateReadme() error {
	content := `# Android-Go Protocol Interoperability Test Data

This directory contains binary packet files generated by Go backend for Android validation.

## Test Cases:

### go_packet_01_open_xor.bin
- **Type**: 0x13 (OPEN)
- **Encryption**: 0x01 (XOR)
- **SessionID**: 0
- **Token**: 0
- **Contains**: MTU, Username, Password, Encrypt attributes
- **Purpose**: Test Android OPEN packet parsing with XOR encryption

### go_packet_02_openack_xor.bin
- **Type**: 0x12 (OPEN_ACK)
- **Encryption**: 0x01 (XOR)
- **SessionID**: 0x1234
- **Token**: 0xDEADBEEF
- **Contains**: Server configuration attributes
- **Purpose**: Test Android session establishment

### go_packet_03_data_encrypted_xor.bin
- **Type**: 0x17 (DATA_ENCRYPTED)
- **Encryption**: 0x01 (XOR)
- **SessionID**: 0x1234
- **Token**: 0xDEADBEEF
- **Contains**: Encrypted IP packet
- **Purpose**: Test Android encrypted data packet processing

### go_packet_04_data_plain.bin
- **Type**: 0x14 (DATA)
- **Encryption**: 0x00 (NONE)
- **SessionID**: 0x5678
- **Token**: 0x12345678
- **Contains**: Plain IP packet
- **Purpose**: Test Android unencrypted data packet processing

### go_packet_05_echo_request_xor.bin
- **Type**: 0x15 (ECHO_REQUEST)
- **Encryption**: 0x01 (XOR)
- **SessionID**: 0x1234
- **Token**: 0xDEADBEEF
- **Contains**: 8-byte timestamp
- **Purpose**: Test Android heartbeat packet processing

## Usage:

1. Run this Go tool to generate test packets
2. Use Android tests to parse and validate the generated packets
3. Android tests should generate their own packets in ../android_to_go/
4. Run this tool again to validate Android-generated packets

## Validation Points:

- Header format (8 bytes, Big Endian)
- Packet type constants match
- TLV attribute encoding
- MD5 signature calculation
- Encryption method constants
- Session ID and token handling
- Data packet structure
`

	filePath := filepath.Join(testDataDir, goToAndroidDir, "README.md")
	return ioutil.WriteFile(filePath, []byte(content), 0644)
}
