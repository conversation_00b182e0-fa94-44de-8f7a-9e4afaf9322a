#!/usr/bin/env swift

/**
 * FILE: swift_interop_validator.swift
 *
 * DESCRIPTION:
 *     Standalone Swift script to validate Go-generated packets and generate Swift packets.
 *     This script demonstrates Go-Swift protocol interoperability without complex dependencies.
 *
 * AUTHOR: wei
 * HISTORY: 26/06/2025 create
 */

import Foundation
import CryptoKit

// MARK: - Constants

let testDataDir = "test_data"
let goToSwiftDir = "go_to_swift"
let swiftToGoDir = "swift_to_go"

// Packet types
let PacketTypeOpen: UInt8 = 0x13
let PacketTypeData: UInt8 = 0x14
let PacketTypeEchoRequest: UInt8 = 0x15

// Encryption methods
let EncryptNone: UInt8 = 0x00
let EncryptXOR: UInt8 = 0x01
let EncryptAES: UInt8 = 0x02

// MARK: - Main Function

func main() {
    print("🔄 Swift-Go Protocol Interoperability Validator")
    print("=" * 50)
    
    // Create directories
    createDirectories()
    
    // Validate Go packets
    validateGoPackets()
    
    // Generate Swift packets
    generateSwiftPackets()
    
    print("\n✅ Swift-Go interoperability validation completed!")
}

// MARK: - Directory Management

func createDirectories() {
    let fileManager = FileManager.default
    let baseURL = URL(fileURLWithPath: testDataDir)
    
    let directories = [
        baseURL,
        baseURL.appendingPathComponent(goToSwiftDir),
        baseURL.appendingPathComponent(swiftToGoDir)
    ]
    
    for directory in directories {
        try? fileManager.createDirectory(at: directory, withIntermediateDirectories: true)
    }
}

// MARK: - Go Packet Validation

func validateGoPackets() {
    print("\n📥 Validating Go-generated packets...")
    
    let goPackets = ["open_packet.bin", "echo_request_packet.bin", "data_packet.bin"]
    
    for packetFile in goPackets {
        do {
            let packetData = try loadGoPacket(packetFile)
            try validatePacketHeader(packetData, filename: packetFile)
            
            // Specific validation based on packet type
            switch packetData[0] {
            case PacketTypeOpen:
                try validateOpenPacket(packetData)
            case PacketTypeEchoRequest:
                try validateEchoRequestPacket(packetData)
            case PacketTypeData:
                try validateDataPacket(packetData)
            default:
                print("  ⚠ Unknown packet type: 0x\(String(format: "%02X", packetData[0]))")
            }
            
        } catch {
            if (error as NSError).code == NSFileReadNoSuchFileError {
                print("  ⚠ \(packetFile) not found - run Go interop tool first")
            } else {
                print("  ❌ Error validating \(packetFile): \(error)")
            }
        }
    }
}

func loadGoPacket(_ filename: String) throws -> Data {
    let url = URL(fileURLWithPath: testDataDir)
        .appendingPathComponent(goToSwiftDir)
        .appendingPathComponent(filename)
    
    return try Data(contentsOf: url)
}

func validatePacketHeader(_ data: Data, filename: String) throws {
    guard data.count >= 8 else {
        throw ValidationError.packetTooShort(filename, data.count)
    }
    
    let type = data[0]
    let encrypt = data[1]
    let sessionID = UInt16(data[2]) << 8 | UInt16(data[3])
    let token = UInt32(data[4]) << 24 | UInt32(data[5]) << 16 | 
               UInt32(data[6]) << 8 | UInt32(data[7])
    
    print("  ✓ \(filename):")
    print("    - Type: 0x\(String(format: "%02X", type))")
    print("    - Encrypt: 0x\(String(format: "%02X", encrypt))")
    print("    - SessionID: \(sessionID)")
    print("    - Token: 0x\(String(format: "%08X", token))")
    print("    - Size: \(data.count) bytes")
    
    // Basic validation
    guard type >= 0x11 && type <= 0x20 else {
        throw ValidationError.invalidPacketType(type)
    }
    
    guard encrypt <= 0x02 else {
        throw ValidationError.invalidEncryptionMethod(encrypt)
    }
}

func validateOpenPacket(_ data: Data) throws {
    // OPEN packet: Header(8) + Signature(16) + Attributes(n)
    guard data.count >= 24 else {
        throw ValidationError.invalidOpenPacket("Too short for OPEN packet")
    }
    
    // Verify header values
    guard data[0] == PacketTypeOpen else {
        throw ValidationError.invalidOpenPacket("Wrong packet type")
    }
    
    guard data[2] == 0 && data[3] == 0 else {
        throw ValidationError.invalidOpenPacket("OPEN packet should have SessionID = 0")
    }
    
    guard data[4] == 0 && data[5] == 0 && data[6] == 0 && data[7] == 0 else {
        throw ValidationError.invalidOpenPacket("OPEN packet should have Token = 0")
    }
    
    // Verify signature is present
    let signature = data.subdata(in: 8..<24)
    print("    - Signature: \(signature.map { String(format: "%02x", $0) }.joined())")
    
    // Verify attributes
    let attributesData = data.dropFirst(24)
    print("    - Attributes: \(attributesData.count) bytes")
    
    if attributesData.count > 0 {
        try parseAndValidateAttributes(attributesData)
    }
}

func validateEchoRequestPacket(_ data: Data) throws {
    // Echo Request: Header(8) + Signature(16) + Timestamp(8)
    guard data.count >= 32 else {
        throw ValidationError.invalidEchoPacket("Too short for Echo Request packet")
    }
    
    guard data[0] == PacketTypeEchoRequest else {
        throw ValidationError.invalidEchoPacket("Wrong packet type")
    }
    
    let signature = data.subdata(in: 8..<24)
    let timestamp = data.subdata(in: 24..<32)
    
    print("    - Signature: \(signature.prefix(8).map { String(format: "%02x", $0) }.joined())...")
    print("    - Timestamp: \(timestamp.map { String(format: "%02x", $0) }.joined())")
}

func validateDataPacket(_ data: Data) throws {
    // Data packet: Header(8) + Payload(n)
    guard data[0] == PacketTypeData else {
        throw ValidationError.invalidDataPacket("Wrong packet type")
    }
    
    let payload = data.dropFirst(8)
    print("    - Payload: \(payload.count) bytes")
    
    if payload.count > 0 {
        // Check if it looks like an IPv4 packet
        if payload[0] == 0x45 {
            print("    - IPv4 packet detected ✓")
        }
    }
}

func parseAndValidateAttributes(_ data: Data) throws {
    var offset = 0
    var attributeCount = 0
    
    while offset < data.count - 1 {
        let type = data[offset]
        let length = Int(data[offset + 1])
        
        guard offset + length <= data.count else {
            throw ValidationError.invalidAttribute("Attribute length exceeds data")
        }
        
        let value = data.subdata(in: (offset + 2)..<(offset + length))
        
        print("      - Attr 0x\(String(format: "%02X", type)): \(length - 2) bytes")
        
        attributeCount += 1
        offset += length
    }
    
    print("    - Total attributes: \(attributeCount)")
}

// MARK: - Swift Packet Generation

func generateSwiftPackets() {
    print("\n📤 Generating Swift packets for Go validation...")
    
    do {
        try generateSwiftOpenPacket()
        try generateSwiftEchoPacket()
        try generateSwiftDataPacket()
        
        print("  ✅ All Swift packets generated successfully")
        
    } catch {
        print("  ❌ Error generating Swift packets: \(error)")
    }
}

func generateSwiftOpenPacket() throws {
    var packet = Data()
    
    // Header: Type(1) + Encrypt(1) + SessionID(2) + Token(4)
    packet.append(PacketTypeOpen)
    packet.append(EncryptAES)
    packet.append(0x00) // SessionID = 0
    packet.append(0x00)
    packet.append(0x00) // Token = 0
    packet.append(0x00)
    packet.append(0x00)
    packet.append(0x00)
    
    // Calculate MD5 signature
    let signature = calculateMD5Signature(for: packet)
    packet.append(signature)
    
    // Add TLV attributes
    // MTU attribute
    packet.append(0x03) // MTU type
    packet.append(0x04) // Length = 4
    packet.append(0x05) // MTU = 1420 (0x058C)
    packet.append(0x8C)
    
    // Username attribute
    let username = "swiftuser"
    let usernameData = username.data(using: .utf8)!
    packet.append(0x01) // Username type
    packet.append(UInt8(usernameData.count + 2)) // Length
    packet.append(usernameData)
    
    try saveSwiftPacket("swift_open_packet.bin", data: packet)
    print("  ✓ Generated OPEN packet (\(packet.count) bytes)")
}

func generateSwiftEchoPacket() throws {
    var packet = Data()
    
    // Header
    packet.append(PacketTypeEchoRequest)
    packet.append(EncryptXOR)
    packet.append(0x16) // SessionID = 5678 (0x162E)
    packet.append(0x2E)
    packet.append(0xDE) // Token = 0xDEADBEEF
    packet.append(0xAD)
    packet.append(0xBE)
    packet.append(0xEF)
    
    // Calculate signature
    let signature = calculateMD5Signature(for: packet)
    packet.append(signature)
    
    // Add 8-byte timestamp
    let timestamp = UInt64(Date().timeIntervalSince1970)
    var timestampBytes = Data(count: 8)
    timestampBytes.withUnsafeMutableBytes { bytes in
        bytes.storeBytes(of: timestamp.bigEndian, as: UInt64.self)
    }
    packet.append(timestampBytes)
    
    try saveSwiftPacket("swift_echo_packet.bin", data: packet)
    print("  ✓ Generated Echo Request packet (\(packet.count) bytes)")
}

func generateSwiftDataPacket() throws {
    var packet = Data()
    
    // Header
    packet.append(PacketTypeData)
    packet.append(EncryptNone)
    packet.append(0x22) // SessionID = 8888 (0x22B8)
    packet.append(0xB8)
    packet.append(0x12) // Token = 0x12345678
    packet.append(0x34)
    packet.append(0x56)
    packet.append(0x78)
    
    // Add sample payload
    let payload = "Hello from Swift!".data(using: .utf8)!
    packet.append(payload)
    
    try saveSwiftPacket("swift_data_packet.bin", data: packet)
    print("  ✓ Generated Data packet (\(packet.count) bytes)")
}

// MARK: - Helper Functions

func calculateMD5Signature(for headerData: Data) -> Data {
    // MD5(header + "mw")
    var combined = headerData
    combined.append(Data([109, 119])) // ASCII 'm', 'w'
    
    let digest = Insecure.MD5.hash(data: combined)
    return Data(digest)
}

func saveSwiftPacket(_ filename: String, data: Data) throws {
    let url = URL(fileURLWithPath: testDataDir)
        .appendingPathComponent(swiftToGoDir)
        .appendingPathComponent(filename)
    
    try data.write(to: url)
}

// MARK: - Error Types

enum ValidationError: Error, CustomStringConvertible {
    case packetTooShort(String, Int)
    case invalidPacketType(UInt8)
    case invalidEncryptionMethod(UInt8)
    case invalidOpenPacket(String)
    case invalidEchoPacket(String)
    case invalidDataPacket(String)
    case invalidAttribute(String)
    
    var description: String {
        switch self {
        case .packetTooShort(let filename, let size):
            return "Packet \(filename) too short: \(size) bytes"
        case .invalidPacketType(let type):
            return "Invalid packet type: 0x\(String(format: "%02X", type))"
        case .invalidEncryptionMethod(let method):
            return "Invalid encryption method: 0x\(String(format: "%02X", method))"
        case .invalidOpenPacket(let reason):
            return "Invalid OPEN packet: \(reason)"
        case .invalidEchoPacket(let reason):
            return "Invalid Echo packet: \(reason)"
        case .invalidDataPacket(let reason):
            return "Invalid Data packet: \(reason)"
        case .invalidAttribute(let reason):
            return "Invalid attribute: \(reason)"
        }
    }
}

// MARK: - String Extension

extension String {
    static func *(lhs: String, rhs: Int) -> String {
        return String(repeating: lhs, count: rhs)
    }
}

// MARK: - Entry Point

main()
