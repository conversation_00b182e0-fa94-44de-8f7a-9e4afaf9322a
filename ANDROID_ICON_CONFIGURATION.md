# Android 图标配置指南

## 概述
为 Panabit Client Android 版本配置应用图标，使其与 iOS 版本保持一致。

## 当前状况

### ✅ 已完成的配置
1. **源图标文件**：`ui/flutter/assets/icons/panabit_icon.png` 已存在
2. **Flutter 配置**：`pubspec.yaml` 中已正确配置 `flutter_launcher_icons` 插件
3. **Android 项目结构**：所有必要的 mipmap 目录已存在
4. **AndroidManifest.xml**：图标引用配置正确 (`@mipmap/ic_launcher`)

### 📋 当前配置详情

#### pubspec.yaml 配置
```yaml
flutter_launcher_icons:
  android: "ic_launcher"
  ios: false
  image_path: "assets/icons/panabit_icon.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/icons/panabit_icon.png"
```

#### AndroidManifest.xml 配置
```xml
<application
    android:label="Panabit iWAN"
    android:icon="@mipmap/ic_launcher"
    ...>
```

## 实施步骤

### 第一步：生成 Android 图标

在 `ui/flutter` 目录下执行以下命令：

```bash
cd ui/flutter
flutter pub get
dart run flutter_launcher_icons:main
```

或者使用提供的脚本：
```bash
cd ui/flutter
chmod +x generate_android_icons.sh
./generate_android_icons.sh
```

### 第二步：验证生成的图标

生成完成后，检查以下文件是否存在：

#### 标准图标 (mipmap)
- `android/app/src/main/res/mipmap-mdpi/ic_launcher.png` (48x48)
- `android/app/src/main/res/mipmap-hdpi/ic_launcher.png` (72x72)
- `android/app/src/main/res/mipmap-xhdpi/ic_launcher.png` (96x96)
- `android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png` (144x144)
- `android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png` (192x192)

#### Adaptive Icon 组件 (drawable)
- `android/app/src/main/res/drawable-mdpi/ic_launcher_foreground.png` (108x108)
- `android/app/src/main/res/drawable-hdpi/ic_launcher_foreground.png` (162x162)
- `android/app/src/main/res/drawable-xhdpi/ic_launcher_foreground.png` (216x216)
- `android/app/src/main/res/drawable-xxhdpi/ic_launcher_foreground.png` (324x324)
- `android/app/src/main/res/drawable-xxxhdpi/ic_launcher_foreground.png` (432x432)

#### Adaptive Icon 配置
- `android/app/src/main/res/mipmap-anydpi-v26/ic_launcher.xml`
- `android/app/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml`

### 第三步：测试图标显示

1. **编译 Android 应用**：
   ```bash
   cd ui/flutter
   flutter build apk --debug
   ```

2. **安装到设备测试**：
   ```bash
   flutter install
   ```

3. **检查图标显示**：
   - 应用启动器中的图标
   - 最近任务中的图标
   - 通知栏中的图标

## 技术细节

### Android 图标规格

| 密度 | 尺寸 | 用途 |
|------|------|------|
| mdpi | 48x48 | 标准密度屏幕 |
| hdpi | 72x72 | 高密度屏幕 |
| xhdpi | 96x96 | 超高密度屏幕 |
| xxhdpi | 144x144 | 超超高密度屏幕 |
| xxxhdpi | 192x192 | 超超超高密度屏幕 |

### Adaptive Icon 说明

Android 8.0 (API 26) 引入了 Adaptive Icon，支持：
- **背景层**：纯色背景 (#FFFFFF)
- **前景层**：图标主体内容
- **动态形状**：系统可以应用不同的蒙版形状

## 与 iOS 版本的一致性

### 设计一致性
- ✅ 使用相同的源图标文件 (`panabit_icon.png`)
- ✅ 保持相同的品牌视觉识别
- ✅ 白色背景与 iOS 版本一致

### 功能一致性
- ✅ 应用启动器图标
- ✅ 系统通知图标
- ✅ 任务切换器图标

## 故障排除

### 常见问题

1. **图标生成失败**
   - 检查 `flutter_launcher_icons` 插件是否正确安装
   - 确认源图标文件路径正确
   - 运行 `flutter clean` 后重新生成

2. **图标显示异常**
   - 清除应用数据后重新安装
   - 检查 Adaptive Icon 配置是否正确
   - 验证图标文件尺寸是否符合规范

3. **编译错误**
   - 检查 Android 资源文件是否有语法错误
   - 确认所有必要的图标文件都已生成

## 完成检查清单

- [ ] 执行图标生成命令
- [ ] 验证所有尺寸的图标文件已生成
- [ ] 检查 Adaptive Icon 配置
- [ ] 编译 Android 应用成功
- [ ] 在设备上测试图标显示
- [ ] 确认与 iOS 版本视觉一致性
