@echo off
echo Building Panabit Client...

REM 设置日志文件
REM 使用PowerShell获取标准格式的日期时间 (年月日时分秒)
for /f "delims=" %%a in ('powershell -Command "Get-Date -Format 'yyyyMMddHHmmss'"') do set TIMESTAMP=%%a
set LOGFILE=build_%TIMESTAMP%.log
echo Build started at %date% %time% > %LOGFILE%

REM 1. 构建后端
echo Building backend...
echo Building backend... >> %LOGFILE%
call scripts\build_backend.bat > temp.log 2>&1
type temp.log >> %LOGFILE%
type temp.log
del temp.log
if %ERRORLEVEL% neq 0 (
    echo Error: Backend build failed. See %LOGFILE% for details.
    exit /b 1
)
echo Backend build completed successfully.

REM 2. 构建前端
echo Building frontend...
echo Building frontend... >> %LOGFILE%
call scripts\build_frontend.bat > temp.log 2>&1
type temp.log >> %LOGFILE%
type temp.log
del temp.log
if %ERRORLEVEL% neq 0 (
    echo Error: Frontend build failed. See %LOGFILE% for details.
    exit /b 1
)
echo Frontend build completed successfully.

REM 3. 准备安装程序文件
echo Preparing installer files...
echo Preparing installer files... >> %LOGFILE%

REM 创建安装程序构建目录
if not exist installer\build mkdir installer\build

REM 复制后端文件
echo Copying backend files to installer directory...
xcopy /E /I /Y build\backend\* installer\build\

REM 复制前端文件
echo Copying frontend files to installer directory...
xcopy /E /I /Y build\frontend\* installer\build\

REM 确保安装程序图标文件可用
echo Ensuring installer icon files are available...
if not exist installer\assets mkdir installer\assets
if exist ui\flutter\assets\icons\panabit_icon.ico (
    copy ui\flutter\assets\icons\panabit_icon.ico installer\assets\icon.ico
    echo Copied Panabit icon to installer assets
) else if exist ui\flutter\assets\icons\app_icon.ico (
    copy ui\flutter\assets\icons\app_icon.ico installer\assets\icon.ico
    echo Copied app icon to installer assets
) else if exist docs\logo.ico (
    copy docs\logo.ico installer\assets\icon.ico
    echo Copied logo to installer assets
) else (
    echo Warning: No icon file found for installer
)

REM 4. 构建安装程序
echo Building installer...
echo Building installer... >> %LOGFILE%
cd installer
call build_installer.bat > ..\temp.log 2>&1
type ..\temp.log >> ..\%LOGFILE%
type ..\temp.log
del ..\temp.log
if %ERRORLEVEL% neq 0 (
    echo Error: Installer build failed. See %LOGFILE% for details.
    exit /b 1
)
cd ..
echo Installer build completed successfully.

REM 5. 复制安装程序到发布目录
if not exist release mkdir release
copy installer\output\Panabit-Client-Setup.exe release\Panabit-Client-Setup_%TIMESTAMP%.exe

echo Build process completed successfully!
echo Installer is available at: release\Panabit-Client-Setup_%TIMESTAMP%.exe
echo Full build log is available at: %LOGFILE%
