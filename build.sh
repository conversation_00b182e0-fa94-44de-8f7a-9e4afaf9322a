#!/bin/bash

# 退出时出错
set -e

# 设置变量
TIMESTAMP=$(date +%Y%m%d%H%M%S)
LOGFILE="build_$TIMESTAMP.log"

# 记录构建开始
echo "Build started at $(date)" > $LOGFILE

# 创建输出目录
mkdir -p build/backend
mkdir -p build/frontend

# 1. 构建后端
echo "Building backend..."
echo "Building backend..." >> $LOGFILE
bash scripts/build_backend.sh >> $LOGFILE 2>&1
if [ $? -ne 0 ]; then
    echo "Error: Backend build failed. See $LOGFILE for details."
    exit 1
fi
echo "Backend build completed successfully."

# 2. 构建前端
echo "Building frontend..."
echo "Building frontend..." >> $LOGFILE
bash scripts/build_frontend.sh >> $LOGFILE 2>&1
if [ $? -ne 0 ]; then
    echo "Error: Frontend build failed. See $LOGFILE for details."
    exit 1
fi
echo "Frontend build completed successfully."

# 3. 准备安装程序文件
echo "Preparing installer files..."
echo "Preparing installer files..." >> $LOGFILE

# 创建安装程序构建目录
mkdir -p installer/build

# 复制后端文件
echo "Copying backend files to installer directory..."
cp -R build/backend/* installer/build/

# 复制前端文件
echo "Copying frontend files to installer directory..."
cp -R build/frontend/* installer/build/

# 确保安装程序图标文件可用
echo "Ensuring installer icon files are available..."
mkdir -p installer/assets
if [ -f ui/flutter/assets/icons/app_icon.ico ]; then
    cp ui/flutter/assets/icons/app_icon.ico installer/assets/icon.ico
    echo "Copied app icon to installer assets"
elif [ -f docs/logo.ico ]; then
    cp docs/logo.ico installer/assets/icon.ico
    echo "Copied logo to installer assets"
else
    echo "Warning: No icon file found for installer"
fi

# 4. 构建安装程序
echo "Building installer..."
echo "Building installer..." >> $LOGFILE
cd installer
bash build_installer.sh >> ../$LOGFILE 2>&1
if [ $? -ne 0 ]; then
    echo "Error: Installer build failed. See $LOGFILE for details."
    exit 1
fi
cd ..
echo "Installer build completed successfully."

# 5. 复制安装程序到发布目录
mkdir -p release
if [ -f installer/output/Panabit-Client-1.0.3-Setup.exe ]; then
    cp installer/output/Panabit-Client-1.0.3-Setup.exe release/Panabit-Client-Setup_$TIMESTAMP.exe
elif [ -f installer/output/Panabit-Client-Setup.exe ]; then
    cp installer/output/Panabit-Client-Setup.exe release/Panabit-Client-Setup_$TIMESTAMP.exe
else
    echo "Warning: Installer output file not found"
fi

echo "Build process completed successfully!"
echo "Installer is available at: release/ItForce-WAN-Setup_$TIMESTAMP.exe"
echo "Full build log is available at: $LOGFILE"

# 记录构建结束
echo "Build completed at $(date)" >> $LOGFILE
