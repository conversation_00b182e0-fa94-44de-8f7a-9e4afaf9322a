# 配置文件说明

本目录包含VPN客户端的配置文件。

## 配置文件

### config.yaml

主配置文件，包含以下配置项：

- **API服务器设置**：HTTP API服务器的主机和端口
- **日志设置**：日志级别、文件路径和轮转策略
- **VPN设置**：
  - **隧道设置**：加密、路由模式、MTU等
  - **服务器列表设置**：服务器列表URL或本地文件路径（二选一）
- **用户凭据**：加密存储的用户名和密码

### servers.json

服务器列表示例文件，当配置使用本地文件作为服务器列表源时使用。

## 使用方法

1. 将 `config.yaml` 复制到程序运行目录
2. 根据需要修改配置项
3. 如果使用本地服务器列表，将 `servers.json` 复制到指定路径，并在 `config.yaml` 中设置 `server_list_file` 路径

## 配置优先级

1. 命令行参数（如果支持）
2. 环境变量（如果支持）
3. 配置文件
4. 默认值

## 服务器列表配置说明

服务器列表配置支持两种方式：URL和本地JSON文件。这两种方式是互斥的，只能选择其中一种。

### 优先级

当同时配置了URL和文件路径时，客户端将按照以下规则选择：

1. 如果`server_list_file`不为空且文件存在，优先使用本地文件
2. 如果`server_list_file`为空或文件不存在，但`server_list_url`不为空，则使用URL
3. 如果两者都为空，则使用默认的URL
