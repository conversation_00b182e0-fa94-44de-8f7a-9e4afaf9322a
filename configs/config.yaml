# API服务器设置
api:
  host: "localhost"
  port: 56543

# 日志设置
logging:
  level: "info"
  file: "logs/backend.log"
  max_size: 10    # MB
  max_backups: 3
  max_age: 28     # 天

# VPN设置
vpn:
  # 隧道设置
  tunnel:
    encrypt: true           # 是否启用加密
    route_mode: 0           # 路由模式（0: 本地配置, 1: 服务器配置, 2: 全部默认）
    route_force: false      # 强制路由（如果启用，清除默认路由）
    duplicate_packets: 0    # 数据包重复（0: 禁用, 1: 启用）
    mtu: 1400               # 最大传输单元

  # 服务器列表设置（二选一）
  server_list_url: ""                                 # 服务器列表URL（由UI设置）
  server_list_file: ""                                # 服务器列表本地文件路径

  # TLS设置（用于HTTPS服务器列表URL）
  tls:
    skip_verify: true                                 # 跳过TLS证书验证（默认：true）
