# ========================================================================
# VPN Client Makefile - 统一版本管理
# ========================================================================

# 检测操作系统
ifeq ($(OS),Windows_NT)
	SHELL := cmd.exe
	RM := del /q
	MKDIR := mkdir
	EXECUTABLE := vpn-service.exe
	BUILD_SCRIPT := scripts\build_backend.bat
	VERSION_SCRIPT := scripts\version_manager.bat
else
	SHELL := /bin/bash
	RM := rm -f
	MKDIR := mkdir -p
	EXECUTABLE := vpn-service
	BUILD_SCRIPT := scripts/build_backend.sh
	VERSION_SCRIPT := scripts/version_manager.sh
endif

# Version Management
.PHONY: version
version:
ifeq ($(OS),Windows_NT)
	@type VERSION
else
	@cat VERSION
endif

.PHONY: version-update
version-update:
	@echo Updating all component versions...
ifeq ($(OS),Windows_NT)
	scripts\version_manager.bat update-all
else
	./$(VERSION_SCRIPT) update-all
endif

# 默认目标
.PHONY: all
all: clean-log version-update build

# 清理日志文件
.PHONY: clean-log
clean-log:
ifeq ($(OS),Windows_NT)
	if exist build.log del build.log
	echo Build started at %DATE% %TIME% > build.log
else
	rm -f build.log
	echo "Build started at $$(date)" > build.log
endif

# 构建所有组件
.PHONY: build
build: backend frontend installer

# 构建后端
.PHONY: backend
backend:
	echo "Building backend..."
ifeq ($(OS),Windows_NT)
	$(BUILD_SCRIPT) $(ARGS) > temp.log 2>&1
	type temp.log >> build.log
	type temp.log
	del temp.log
else
	bash $(BUILD_SCRIPT) $(ARGS) | tee -a build.log
endif

# 构建前端
.PHONY: frontend
frontend:
	echo "Building frontend..."
ifeq ($(OS),Windows_NT)
	scripts\build_frontend.bat > temp.log 2>&1
	type temp.log >> build.log
	type temp.log
	del temp.log
else
	bash scripts/build_frontend.sh | tee -a build.log
endif

# 构建安装程序
.PHONY: installer
installer: backend frontend
	echo "Building installer..."
ifeq ($(OS),Windows_NT)
	if not exist installer\build mkdir installer\build
	if exist build\backend xcopy /E /I /Y build\backend\* installer\build\
	if exist build\frontend xcopy /E /I /Y build\frontend\* installer\build\
	echo Ensuring installer icon files are available...
	if not exist installer\assets mkdir installer\assets
	if exist ui\flutter\assets\icons\panabit_icon.ico (copy ui\flutter\assets\icons\panabit_icon.ico installer\assets\icon.ico)
	if exist docs\logo.ico (copy docs\logo.ico installer\assets\icon.ico)
	cd installer && build_installer.bat > ../temp.log 2>&1 && cd ..
	type temp.log >> build.log
	type temp.log
	del temp.log
else
	mkdir -p installer/build
	cp -R build/backend/* installer/build/
	cp -R build/frontend/* installer/build/
	echo "Ensuring installer icon files are available..."
	mkdir -p installer/assets
	if [ -f ui/flutter/assets/icons/app_icon.ico ]; then cp ui/flutter/assets/icons/app_icon.ico installer/assets/icon.ico; fi
	if [ -f docs/logo.ico ]; then cp docs/logo.ico installer/assets/icon.ico; fi
	cd installer && bash build_installer.sh | tee -a ../build.log && cd ..
endif

# 构建后端（调试模式）
.PHONY: debug
debug: clean-log
	echo "Building backend in debug mode..."
ifeq ($(OS),Windows_NT)
	$(BUILD_SCRIPT) --debug > temp.log 2>&1
	type temp.log >> build.log
	type temp.log
	del temp.log
else
	bash $(BUILD_SCRIPT) --debug | tee -a build.log
endif

# 运行后端（仅用于开发）
.PHONY: run
run: debug
	echo "Running backend in debug mode..."
ifeq ($(OS),Windows_NT)
	cd build\backend && vpn-service.exe --config configs\config.yaml
else
	cd build/backend && ./$(EXECUTABLE) --config configs/config.yaml
endif

# 清理构建文件
.PHONY: clean
clean:
	echo "Cleaning build files..."
ifeq ($(OS),Windows_NT)
	if exist build rmdir /s /q build
	if exist installer\build rmdir /s /q installer\build
	if exist installer\output rmdir /s /q installer\output
	if exist release rmdir /s /q release
	if exist build.log del build.log
else
	rm -rf build
	rm -rf installer/build
	rm -rf installer/output
	rm -rf release
	rm -f build.log
endif
	echo "Clean completed."

# Help Information
.PHONY: help
help:
	@echo ItForce WAN Client Build System
	@echo ==============================
	@echo Build Targets:
	@echo "  all        : Build all components (includes version update)"
	@echo "  build      : Build all components"
	@echo "  backend    : Build backend only"
	@echo "  frontend   : Build frontend only"
	@echo "  installer  : Build installer"
	@echo "  debug      : Build backend in debug mode"
	@echo "  run        : Run backend in debug mode"
	@echo "  clean      : Clean build files"
	@echo "  version    : Show current version"
	@echo "  help       : Show this help"
